<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Farms - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
<style>
    .farms-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .farms-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 6s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .farms-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 25px;
        margin: 30px 0;
    }

    .farm-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .farm-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }

    .farm-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2);
    }

    .farm-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .farm-name {
        font-size: 1.5em;
        font-weight: bold;
        color: #2d3436;
        margin: 0;
    }

    .farm-status {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
        background: linear-gradient(45deg, #00b894, #00cec9);
        color: white;
    }

    .farm-details {
        margin: 20px 0;
    }

    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .detail-row:last-child {
        border-bottom: none;
    }

    .detail-label {
        color: #636e72;
        font-weight: 500;
    }

    .detail-value {
        color: #2d3436;
        font-weight: bold;
    }

    .farm-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin: 20px 0;
    }

    .stat-item {
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 12px;
        border: 1px solid #e2e8f0;
    }

    .stat-number {
        font-size: 1.8em;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 0.8em;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .farm-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .action-btn {
        flex: 1;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 15px;
        padding: 10px 15px;
        color: white;
        font-weight: bold;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        font-size: 0.9em;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .action-btn.secondary {
        background: linear-gradient(45deg, #74b9ff, #0984e3);
    }

    .action-btn.secondary:hover {
        box-shadow: 0 8px 20px rgba(116, 185, 255, 0.4);
    }

    .create-farm-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: 2px dashed rgba(255,255,255,0.3);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        min-height: 300px;
        transition: all 0.3s ease;
    }

    .create-farm-card:hover {
        transform: translateY(-5px);
        border-color: rgba(255,255,255,0.6);
        color: white;
        text-decoration: none;
    }

    .create-farm-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.8;
    }

    .create-farm-text {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .create-farm-subtitle {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .search-controls {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .view-toggle {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        justify-content: center;
    }

    .toggle-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        color: white;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .toggle-btn:hover,
    .toggle-btn.active {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }
    </style>
</head>
<body>
<div class="container py-4">
    <!-- Header -->
    <div class="farms-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">🏢 Farm Management</h1>
                <p class="mb-0" style="opacity: 0.9;">Manage your shrimp farms and monitor pond operations</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'ponds:farm_create_with_map' %}" class="btn btn-light">
                    <i class="fas fa-plus-circle me-2"></i> Create Farm
                </a>
                <a href="{% url 'ponds:farm_dashboard' %}" class="btn btn-outline-light">
                    <i class="fas fa-chart-bar me-2"></i> Dashboard
                </a>
            </div>
        </div>
    </div>

    <!-- Search Controls -->
    <div class="search-controls">
        <form method="get" class="row g-3 align-items-center">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" name="search" class="form-control" placeholder="Search farms by name or location..." value="{{ search_query }}">
                    <button type="submit" class="btn btn-primary">Search</button>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                {% if search_query %}
                    <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> Clear Search
                    </a>
                {% endif %}
            </div>
        </form>
    </div>

    <!-- View Toggle -->
    <div class="view-toggle">
        <a href="{% url 'ponds:farm_list' %}" class="toggle-btn active">
            <i class="fas fa-th-large"></i> Card View
        </a>
        <a href="{% url 'ponds:farm_dashboard' %}" class="toggle-btn">
            <i class="fas fa-table"></i> Table View
        </a>
    </div>

    <!-- Farms Grid -->
    <div class="farms-grid">
        <!-- Create New Farm Card -->
        <a href="{% url 'ponds:farm_create_with_map' %}" class="farm-card create-farm-card">
            <div class="create-farm-icon">
                <i class="fas fa-plus-circle"></i>
            </div>
            <div class="create-farm-text">Create New Farm</div>
            <div class="create-farm-subtitle">Start managing a new shrimp farming facility</div>
        </a>

        {% if farms %}
            {% for farm in farms %}
            <div class="farm-card" onclick="window.location.href='{% url 'ponds:farm_detail' farm.pk %}'">
                <div class="farm-card-header">
                    <h3 class="farm-name">🏢 {{ farm.name }}</h3>
                    <span class="farm-status">Active</span>
                </div>

                <div class="farm-details">
                    <div class="detail-row">
                        <span class="detail-label">📍 Location</span>
                        <span class="detail-value">{{ farm.location|default:"Not specified" }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">📏 Farm Size</span>
                        <span class="detail-value">{{ farm.size }} hectares</span>
                    </div>
                    {% if farm.contact_person %}
                    <div class="detail-row">
                        <span class="detail-label">👤 Manager</span>
                        <span class="detail-value">{{ farm.contact_person }}</span>
                    </div>
                    {% endif %}
                </div>

                <div class="farm-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ farm.total_ponds|default:0 }}</div>
                        <div class="stat-label">Total Ponds</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ farm.active_ponds|default:0 }}</div>
                        <div class="stat-label">Active Ponds</div>
                    </div>
                </div>

                <div class="farm-actions">
                    <a href="{% url 'ponds:farm_detail' farm.pk %}" class="action-btn" onclick="event.stopPropagation();">
                        <i class="fas fa-eye"></i> View Details
                    </a>
                    <a href="{% url 'ponds:pond_create' %}?farm={{ farm.pk }}" class="action-btn secondary" onclick="event.stopPropagation();">
                        <i class="fas fa-plus"></i> Add Pond
                    </a>
                </div>
            </div>
            {% endfor %}
        {% endif %}
    </div>

    {% if not farms and not search_query %}
    <div class="text-center py-5">
        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">🏢</div>
        <h3>No Farms Yet</h3>
        <p class="text-muted mb-4">Get started by creating your first shrimp farm to begin managing your shrimp farming operations.</p>
        <a href="{% url 'ponds:farm_create_with_map' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus-circle me-2"></i> Create Your First Farm
        </a>
    </div>
    {% elif not farms and search_query %}
    <div class="text-center py-5">
        <div style="font-size: 4rem; margin-bottom: 20px; opacity: 0.5;">🔍</div>
        <h3>No Farms Found</h3>
        <p class="text-muted mb-4">No farms match your search criteria "{{ search_query }}". Try adjusting your search terms.</p>
        <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i> View All Farms
        </a>
    </div>
    {% endif %}
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Add any JavaScript functionality here
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🏢 Farm List page loaded without header and sidebar');

        // Add click handlers for farm cards
        document.querySelectorAll('.farm-card:not(.create-farm-card)').forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.action-btn')) {
                    const url = this.getAttribute('onclick');
                    if (url) {
                        eval(url);
                    }
                }
            });
        });
    });
</script>

</body>
</html>