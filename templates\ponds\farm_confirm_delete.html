{% extends "base.html" %}
{% load static %}

{% block title %}Delete Farm - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Delete Farm</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-triangle text-danger fa-4x mb-3"></i>
                        <h4>Are you sure you want to delete this farm?</h4>
                        <p class="text-muted">This action cannot be undone. All ponds associated with this farm will also be deleted.</p>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">Farm Details:</h6>
                        <p class="mb-1"><strong>Name:</strong> {{ farm.name }}</p>
                        <p class="mb-1"><strong>Location:</strong> {{ farm.location|default:"Not specified" }}</p>
                        <p class="mb-1"><strong>Size:</strong> {{ farm.size }} hectares</p>
                        <p class="mb-0"><strong>Ponds:</strong> {{ farm.total_ponds }} ({{ farm.active_ponds }} active)</p>
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-grid gap-2">
                            <a href="{% url 'ponds:farm_detail' farm.pk %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash-alt me-2"></i> Delete Farm
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}