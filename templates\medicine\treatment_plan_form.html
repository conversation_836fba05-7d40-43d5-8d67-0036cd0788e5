<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if form.instance.id %}Edit{% else %}Create{% endif %} Treatment Plan - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .medicine-icon {
        font-size: 2.5rem;
        opacity: 0.9;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-action i {
        font-size: 1.1rem;
    }
    
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        margin-bottom: 30px;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 15px 15px 0 0 !important;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    
    .preview-section {
        background: rgba(102, 126, 234, 0.1);
        border-radius: 15px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .preview-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 5px 0;
    }
    
    .preview-label {
        font-weight: 600;
        color: #495057;
    }
    
    .preview-value {
        color: #667eea;
        font-weight: 600;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-clipboard-check medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Treatment Plan</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">{% if form.instance.id %}Edit{% else %}Create{% endif %} Treatment Plan</h2>
                    <p class="mb-0 opacity-75">Design comprehensive treatment protocols</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Status</small>
                                <strong>{% if form.instance.id %}Editing{% else %}New{% endif %}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="/medicine/treatment-plans/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list"></i> All Plans
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">Loading...</span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/treatment-plans/" class="quick-action">
                <i class="fas fa-list"></i>
                View All Plans
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                Medicines
            </a>
            <a href="/medicine/dashboard/" class="quick-action">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
        </div>

        <!-- Form -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clipboard-check me-2"></i>
                            {% if form.instance.id %}Edit Treatment Plan{% else %}Create New Treatment Plan{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" id="treatment-plan-form">
                            {% csrf_token %}
                            
                            <!-- Basic Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-muted mb-3">Basic Information</h6>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">
                                        {{ form.name.label }}
                                        {% if form.name.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small mt-1">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.pond.id_for_label }}" class="form-label">
                                        {{ form.pond.label }}
                                        {% if form.pond.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.pond }}
                                    {% if form.pond.errors %}
                                        <div class="text-danger small mt-1">{{ form.pond.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-12 mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        {{ form.description.label }}
                                    </label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                        <div class="text-danger small mt-1">{{ form.description.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Schedule -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-muted mb-3">Schedule</h6>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label">
                                        {{ form.start_date.label }}
                                        {% if form.start_date.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger small mt-1">{{ form.start_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">
                                        {{ form.end_date.label }}
                                        {% if form.end_date.field.required %}<span class="text-danger">*</span>{% endif %}
                                    </label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger small mt-1">{{ form.end_date.errors.0 }}</div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">
                                        {{ form.status.label }}
                                    </label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger small mt-1">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Duration Preview -->
                            <div class="preview-section" id="duration-preview" style="display: none;">
                                <h6 class="mb-3">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Duration Preview
                                </h6>
                                <div class="preview-item">
                                    <span class="preview-label">Start Date:</span>
                                    <span class="preview-value" id="preview-start">-</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">End Date:</span>
                                    <span class="preview-value" id="preview-end">-</span>
                                </div>
                                <div class="preview-item">
                                    <span class="preview-label">Duration:</span>
                                    <span class="preview-value" id="preview-duration">-</span>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <a href="/medicine/treatment-plans/" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>
                                    {% if form.instance.id %}Update{% else %}Create{% endif %} Treatment Plan
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Duration preview functionality
        function updateDurationPreview() {
            const startDate = document.getElementById('{{ form.start_date.id_for_label }}').value;
            const endDate = document.getElementById('{{ form.end_date.id_for_label }}').value;
            
            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                
                if (end >= start) {
                    const duration = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
                    
                    document.getElementById('preview-start').textContent = start.toLocaleDateString();
                    document.getElementById('preview-end').textContent = end.toLocaleDateString();
                    document.getElementById('preview-duration').textContent = duration + ' days';
                    document.getElementById('duration-preview').style.display = 'block';
                } else {
                    document.getElementById('duration-preview').style.display = 'none';
                }
            } else {
                document.getElementById('duration-preview').style.display = 'none';
            }
        }
        
        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Update time immediately and then every minute
            updateTime();
            setInterval(updateTime, 60000);
            
            // Add event listeners for date fields
            const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
            const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
            
            if (startDateField && endDateField) {
                startDateField.addEventListener('change', updateDurationPreview);
                endDateField.addEventListener('change', updateDurationPreview);
                
                // Initial preview update
                updateDurationPreview();
            }
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8f9fa;
        margin-top: 1rem;
    }
    
    .pond-preview-title {
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .pond-preview-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
    }
    
    .pond-preview-label {
        color: #6c757d;
    }
    
    .pond-preview-value {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-clipboard-check text-primary me-2" style="font-size: 1.5rem;"></i>
            <h1 class="h3 mb-0">{% if form.instance.id %}Edit{% else %}Create{% endif %} Treatment Plan</h1>
        </div>
        <div>
            <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Plans
            </a>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card form-card">
                <div class="card-body">
                    <form method="post" action="{% if form.instance.id %}{% url 'medicine:treatment_plan_update' form.instance.id %}{% else %}{% url 'medicine:treatment_plan_create' %}{% endif %}">
                        {% csrf_token %}
                        
                        <div class="form-section">
                            <h5 class="form-section-title">Basic Information</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.pond|as_crispy_field }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.name|as_crispy_field }}
                                </div>
                            </div>
                            <div class="mb-3">
                                {{ form.description|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h5 class="form-section-title">Schedule</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    {{ form.start_date|as_crispy_field }}
                                </div>
                                <div class="col-md-6 mb-3">
                                    {{ form.end_date|as_crispy_field }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-section">
                            <h5 class="form-section-title">Status</h5>
                            <div class="mb-3">
                                {{ form.status|as_crispy_field }}
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-outline-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                {% if form.instance.id %}Update{% else %}Create{% endif %} Treatment Plan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card form-card">
                <div class="card-body">
                    <h5 class="card-title">Treatment Duration</h5>
                    <p class="card-text text-muted">Select start and end dates to see the treatment duration</p>
                    
                    <div class="date-range-preview" id="date-range-preview">
                        <div class="date-range-preview-title">Treatment Duration</div>
                        <div class="date-range-preview-item">
                            <span class="date-range-preview-label">Start Date:</span>
                            <span class="date-range-preview-value" id="preview-start-date">--</span>
                        </div>
                        <div class="date-range-preview-item">
                            <span class="date-range-preview-label">End Date:</span>
                            <span class="date-range-preview-value" id="preview-end-date">--</span>
                        </div>
                        <div class="date-range-preview-item">
                            <span class="date-range-preview-label">Duration:</span>
                            <span class="date-range-preview-value" id="preview-duration">--</span>
                        </div>
                    </div>
                    
                    <h5 class="card-title mt-4">Pond Information</h5>
                    <p class="card-text text-muted">Select a pond to see details</p>
                    
                    <div class="pond-preview" id="pond-preview" style="display: none;">
                        <div class="pond-preview-title" id="pond-name">Pond Name</div>
                        <div class="pond-preview-item">
                            <span class="pond-preview-label">Size:</span>
                            <span class="pond-preview-value" id="pond-size">--</span>
                        </div>
                        <div class="pond-preview-item">
                            <span class="pond-preview-label">Species:</span>
                            <span class="pond-preview-value" id="pond-species">--</span>
                        </div>
                        <div class="pond-preview-item">
                            <span class="pond-preview-label">Status:</span>
                            <span class="pond-preview-value" id="pond-status">--</span>
                        </div>
                        <div class="pond-preview-item">
                            <span class="pond-preview-label">Active Treatments:</span>
                            <span class="pond-preview-value" id="pond-treatments">--</span>
                        </div>
                    </div>
                    
                    <h5 class="card-title mt-4">Next Steps</h5>
                    <p class="card-text">After creating the treatment plan, you'll be able to add treatment steps with specific medicines and dosages.</p>
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item">1. Create the treatment plan</li>
                        <li class="list-group-item">2. Add treatment steps with medicines</li>
                        <li class="list-group-item">3. Schedule applications</li>
                        <li class="list-group-item">4. Track progress and effectiveness</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startDateInput = document.getElementById('id_start_date');
        const endDateInput = document.getElementById('id_end_date');
        const pondSelect = document.getElementById('id_pond');
        
        // Update duration preview
        function updateDurationPreview() {
            const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput.value ? new Date(endDateInput.value) : null;
            
            document.getElementById('preview-start-date').textContent = startDate ? startDate.toLocaleDateString() : '--';
            document.getElementById('preview-end-date').textContent = endDate ? endDate.toLocaleDateString() : '--';
            
            if (startDate && endDate) {
                const diffTime = Math.abs(endDate - startDate);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
                document.getElementById('preview-duration').textContent = diffDays + ' days';
            } else {
                document.getElementById('preview-duration').textContent = '--';
            }
        }
        
        // Update pond preview
        function updatePondPreview() {
            const pondId = pondSelect.value;
            
            if (pondId) {
                // In a real application, you would fetch pond details from the server
                // For now, we'll just show a placeholder
                document.getElementById('pond-preview').style.display = 'block';
                document.getElementById('pond-name').textContent = pondSelect.options[pondSelect.selectedIndex].text;
                document.getElementById('pond-size').textContent = '5000 m²';
                document.getElementById('pond-species').textContent = 'White Shrimp (L. vannamei)';
                document.getElementById('pond-status').textContent = 'Active';
                document.getElementById('pond-treatments').textContent = '0';
            } else {
                document.getElementById('pond-preview').style.display = 'none';
            }
        }
        
        // Add event listeners
        if (startDateInput) {
            startDateInput.addEventListener('change', updateDurationPreview);
        }
        
        if (endDateInput) {
            endDateInput.addEventListener('change', updateDurationPreview);
        }
        
        if (pondSelect) {
            pondSelect.addEventListener('change', updatePondPreview);
        }
        
        // Initialize previews
        updateDurationPreview();
        updatePondPreview();
    });
</script>
{% endblock %}
