{% extends "base.html" %}
{% load static %}

{% block title %}Pond Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<style>
    .stats-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
    }
    
    .stats-value {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .map-container {
        height: 400px;
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
    
    .filter-card {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.25rem;
        margin-bottom: 1.5rem;
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 1rem;
    }
    
    .status-active {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .status-maintenance {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .status-empty {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
    }
    
    .status-harvested {
        background-color: rgba(13, 110, 253, 0.1);
        color: #0d6efd;
    }
    
    .quality-good {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .quality-average {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .quality-poor {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .pond-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s;
        height: 100%;
        border: 1px solid rgba(0,0,0,0.125);
    }
    
    .pond-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .pond-card-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.125);
        background-color: #f8f9fa;
    }
    
    .pond-card-body {
        padding: 1rem;
    }
    
    .pond-card-footer {
        padding: 1rem;
        border-top: 1px solid rgba(0,0,0,0.125);
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Pond Dashboard</h1>
        <div class="btn-group">
            <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i> All Ponds
            </a>
            <a href="{% url 'ponds:pond_create' %}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i> Add Pond
            </a>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-water"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_ponds }}</div>
                        <div class="stats-label">Total Ponds</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ active_ponds }}</div>
                        <div class="stats-label">Active Ponds</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_area|floatformat:2 }}</div>
                        <div class="stats-label">Total Area (hectares)</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-fish"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_farms }}</div>
                        <div class="stats-label">Total Farms</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card filter-card shadow-sm mb-4">
        <form method="get" action="{% url 'ponds:pond_dashboard' %}">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="farm" class="form-label">Farm</label>
                    <select name="farm" id="farm" class="form-select">
                        <option value="">All Farms</option>
                        {% for farm in farms %}
                            <option value="{{ farm.id }}" {% if selected_farm == farm.id %}selected{% endif %}>{{ farm.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="active" {% if selected_status == 'active' %}selected{% endif %}>Active</option>
                        <option value="maintenance" {% if selected_status == 'maintenance' %}selected{% endif %}>Maintenance</option>
                        <option value="empty" {% if selected_status == 'empty' %}selected{% endif %}>Empty</option>
                        <option value="harvested" {% if selected_status == 'harvested' %}selected{% endif %}>Harvested</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="water_quality" class="form-label">Water Quality</label>
                    <select name="water_quality" id="water_quality" class="form-select">
                        <option value="">All Qualities</option>
                        <option value="Good" {% if selected_water_quality == 'Good' %}selected{% endif %}>Good</option>
                        <option value="Average" {% if selected_water_quality == 'Average' %}selected{% endif %}>Average</option>
                        <option value="Poor" {% if selected_water_quality == 'Poor' %}selected{% endif %}>Poor</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i> Apply Filters
                    </button>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Pond Map with Weather Integration -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Pond Locations with Weather</h5>
            <div class="btn-group btn-group-sm">
                <a href="{% url 'weather:unified_weather_map' %}" class="btn btn-outline-primary">
                    <i class="fas fa-globe me-1"></i> Unified Map
                </a>
                <button class="btn btn-outline-secondary" onclick="toggleWeatherLayer()">
                    <i class="fas fa-cloud me-1"></i> Weather Layer
                </button>
            </div>
        </div>
        <div class="card-body p-0">
            <div id="pondMap" class="map-container">
                <!-- Map will be loaded here -->
                <div class="d-flex justify-content-center align-items-center h-100 bg-light">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="visually-hidden">Loading map...</span>
                        </div>
                        <p class="mb-0">Loading pond locations and weather data...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Pond List -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Ponds</h5>
                    <span class="badge bg-primary">{{ ponds.count }} ponds</span>
                </div>
                <div class="card-body p-0">
                    {% if ponds %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Farm</th>
                                        <th>Size</th>
                                        <th>Status</th>
                                        <th>Water Quality</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for pond in ponds %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'ponds:pond_detail' pond.pk %}" class="text-decoration-none fw-medium">
                                                    {{ pond.name }}
                                                </a>
                                            </td>
                                            <td>
                                                {% if pond.farm %}
                                                    <a href="{% url 'ponds:farm_detail' pond.farm.pk %}" class="text-decoration-none">
                                                        {{ pond.farm.name }}
                                                    </a>
                                                {% else %}
                                                    --
                                                {% endif %}
                                            </td>
                                            <td>{{ pond.size|floatformat:2 }} hectares</td>
                                            <td>
                                                <span class="status-badge status-{{ pond.status }}">
                                                    {{ pond.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if pond.water_quality %}
                                                    <span class="status-badge quality-{{ pond.water_quality|lower }}">
                                                        {{ pond.water_quality }}
                                                    </span>
                                                {% else %}
                                                    --
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'ponds:pond_detail' pond.pk %}" class="btn btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'ponds:enhanced_pond_detail' pond.pk %}" class="btn btn-outline-info">
                                                        <i class="fas fa-chart-line"></i>
                                                    </a>
                                                    <a href="{% url 'ponds:pond_update' pond.pk %}" class="btn btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No ponds found matching your criteria.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Pond Size Distribution Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Pond Size Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="pondSizeChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Pond Status Distribution -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Pond Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="pondStatusChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Water Quality Overview -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Water Quality Overview</h5>
                </div>
                <div class="card-body">
                    <div id="waterQualityChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'ponds:pond_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Pond
                        </a>
                        <a href="{% url 'ponds:farm_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Farm
                        </a>                        <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i> View All Ponds
                        </a>
                        <a href="{% url 'ponds:cumulative_map_dashboard' %}" class="btn btn-outline-primary">
                            <i class="fas fa-globe-americas me-2"></i> Cumulative Map
                        </a>
                        <a href="{% url 'ponds:farm_dashboard' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-tachometer-alt me-2"></i> Farm Dashboard
                        </a>
                        <a href="{% url 'weather:unified_weather_map' %}" class="btn btn-outline-info">
                            <i class="fas fa-map me-2"></i> Weather Map
                        </a>
                        <a href="{% url 'weather:pond_weather_dashboard' %}" class="btn btn-outline-success">
                            <i class="fas fa-cloud-sun me-2"></i> Weather Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize pond map with weather integration
        const pondMap = L.map('pondMap').setView([13.7563, 100.5018], 8);
        let weatherLayer = null;
        let weatherLayerVisible = false;

        // Add satellite tile layer
        const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
        }).addTo(pondMap);

        // Add street map layer
        const streetLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        });

        // Layer control
        const baseMaps = {
            "Satellite": satelliteLayer,
            "Street Map": streetLayer
        };

        L.control.layers(baseMaps).addTo(pondMap);
        
        // Add enhanced pond markers with weather integration
        const pondMarkers = [];
        {% for pond in ponds %}
            {% if pond.latitude and pond.longitude %}
                // Create custom marker based on pond status
                const statusColors = {
                    'active': '#28a745',
                    'maintenance': '#ffc107',
                    'empty': '#6c757d',
                    'harvested': '#007bff'
                };

                const pondMarker{{ pond.id }} = L.circleMarker([{{ pond.latitude }}, {{ pond.longitude }}], {
                    radius: 10,
                    fillColor: statusColors['{{ pond.status }}'] || '#667eea',
                    color: 'white',
                    weight: 3,
                    opacity: 1,
                    fillOpacity: 0.8
                }).addTo(pondMap);

                // Enhanced popup with weather integration
                const popupContent = `
                    <div style="min-width: 250px;">
                        <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 10px; margin: -10px -10px 10px -10px; border-radius: 5px;">
                            <h5 style="margin: 0; text-align: center;">🐟 {{ pond.name }}</h5>
                        </div>
                        <p><strong>Farm:</strong> {% if pond.farm %}{{ pond.farm.name }}{% else %}Not assigned{% endif %}</p>
                        <p><strong>Size:</strong> {{ pond.size|floatformat:2 }} hectares</p>
                        <p><strong>Status:</strong> <span style="background: ${statusColors['{{ pond.status }}'] || '#667eea'}; color: white; padding: 2px 8px; border-radius: 10px; font-size: 0.8em;">{{ pond.get_status_display }}</span></p>
                        {% if pond.water_quality %}<p><strong>Water Quality:</strong> {{ pond.water_quality }}</p>{% endif %}
                        <p><strong>Location:</strong> {{ pond.latitude|floatformat:4 }}°N, {{ pond.longitude|floatformat:4 }}°E</p>
                        <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 10px; border-radius: 5px; text-align: center; margin: 10px 0;">
                            <div style="font-size: 1.2em; font-weight: bold;">🌤️ Weather Enabled</div>
                            <div style="font-size: 0.9em; opacity: 0.9;">Real-time monitoring available</div>
                        </div>
                        <div style="text-align: center; margin-top: 15px;">
                            <a href="{% url "ponds:pond_detail" pond.pk %}" class="btn btn-sm btn-primary me-1">View Details</a>
                            <a href="{% url "weather:pond_weather_map" pond.pk %}" class="btn btn-sm btn-info">Weather Map</a>
                        </div>
                    </div>
                `;

                pondMarker{{ pond.id }}.bindPopup(popupContent, {
                    maxWidth: 300
                });

                // Add click handler for enhanced interaction
                pondMarker{{ pond.id }}.on('click', function() {
                    // Center map on clicked pond
                    pondMap.setView([{{ pond.latitude }}, {{ pond.longitude }}], Math.max(pondMap.getZoom(), 15), {
                        animate: true,
                        duration: 1
                    });
                });

                pondMarkers.push(pondMarker{{ pond.id }});

                // If this is the first pond with coordinates, center the map on it
                {% if forloop.first %}
                    pondMap.setView([{{ pond.latitude }}, {{ pond.longitude }}], 10);
                {% endif %}
            {% endif %}
        {% endfor %}

        // Fit map to show all ponds if multiple ponds exist
        if (pondMarkers.length > 1) {
            const group = new L.featureGroup(pondMarkers);
            pondMap.fitBounds(group.getBounds().pad(0.1));
        }

        // Toggle weather layer function
        window.toggleWeatherLayer = function() {
            if (weatherLayerVisible) {
                if (weatherLayer) {
                    pondMap.removeLayer(weatherLayer);
                    weatherLayer = null;
                }
                weatherLayerVisible = false;
            } else {
                // Add weather layer (temperature overlay)
                weatherLayer = L.tileLayer('https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=9de243494c0b295cca9337e1e96b00e2', {
                    opacity: 0.6,
                    attribution: '© OpenWeatherMap'
                }).addTo(pondMap);
                weatherLayerVisible = true;
            }
        };
        
        // Initialize pond size distribution chart
        const pondSizeCtx = document.getElementById('pondSizeChart').getContext('2d');
        const pondSizeChart = new Chart(pondSizeCtx, {
            type: 'bar',
            data: {
                labels: [{% for pond in ponds %}{% if forloop.counter <= 10 %}'{{ pond.name }}'{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}],
                datasets: [{
                    label: 'Pond Size (hectares)',
                    data: [{% for pond in ponds %}{% if forloop.counter <= 10 %}{{ pond.size }}{% if not forloop.last %}, {% endif %}{% endif %}{% endfor %}],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Size (hectares)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Pond'
                        }
                    }
                }
            }
        });
        
        // Initialize pond status distribution chart
        const pondStatusCtx = document.getElementById('pondStatusChart').getContext('2d');
        const pondStatusChart = new Chart(pondStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Maintenance', 'Empty', 'Harvested'],
                datasets: [{
                    data: [
                        {{ active_ponds }}, 
                        {{ maintenance_ponds|default:0 }}, 
                        {{ empty_ponds|default:0 }}, 
                        {{ harvested_ponds|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(13, 110, 253, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(108, 117, 125, 1)',
                        'rgba(13, 110, 253, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Initialize water quality chart
        const waterQualityCtx = document.getElementById('waterQualityChart').getContext('2d');
        const waterQualityChart = new Chart(waterQualityCtx, {
            type: 'pie',
            data: {
                labels: ['Good', 'Average', 'Poor'],
                datasets: [{
                    data: [
                        {{ good_water_quality|default:0 }}, 
                        {{ average_water_quality|default:0 }}, 
                        {{ poor_water_quality|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}