{% extends 'base.html' %}
{% load static %}

{% block title %}{{ pond.name }} - Enhanced Analytics{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .enhanced-header {
        background: linear-gradient(135deg, #2196F3 0%, #21CBF3 50%, #2196F3 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .enhanced-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 8s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .analytics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
        margin: 30px 0;
    }

    .analytics-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
    }

    .analytics-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        border-color: #2196F3;
    }

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
    }

    .card-icon {
        font-size: 1.8em;
        margin-right: 15px;
        background: linear-gradient(45deg, #2196F3, #21CBF3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-title {
        font-size: 1.3em;
        font-weight: 600;
        margin: 0;
        color: #2c3e50;
    }

    .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin: 20px 0;
    }

    .metric-card {
        text-align: center;
        padding: 15px;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .metric-card:hover {
        transform: scale(1.05);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .metric-value {
        font-size: 2em;
        font-weight: bold;
        color: #2196F3;
        margin-bottom: 5px;
    }

    .metric-label {
        font-size: 0.9em;
        color: #6c757d;
        font-weight: 500;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 1px;
    }

    .status-active { background: linear-gradient(45deg, #28a745, #20c997); color: white; }
    .status-maintenance { background: linear-gradient(45deg, #ffc107, #fd7e14); color: white; }
    .status-inactive { background: linear-gradient(45deg, #6c757d, #495057); color: white; }
    .status-harvesting { background: linear-gradient(45deg, #17a2b8, #007bff); color: white; }

    .chart-container {
        position: relative;
        height: 300px;
        margin: 20px 0;
    }

    .progress-circle {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: white;
        font-size: 1.2em;
        margin: 0 auto 10px;
    }

    .timeline-item {
        display: flex;
        align-items: center;
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #2196F3;
    }

    .timeline-icon {
        font-size: 1.5em;
        color: #2196F3;
        margin-right: 15px;
    }

    .timeline-content h6 {
        margin: 0 0 5px 0;
        color: #2c3e50;
    }

    .timeline-content p {
        margin: 0;
        color: #6c757d;
        font-size: 0.9em;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .action-btn {
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .action-btn-primary {
        background: linear-gradient(45deg, #2196F3, #21CBF3);
        color: white;
    }

    .action-btn-secondary {
        background: linear-gradient(45deg, #6c757d, #495057);
        color: white;
    }

    .action-btn-success {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.15);
    }

    .environmental-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .env-metric {
        text-align: center;
        padding: 20px;
        background: linear-gradient(135deg, #ffffff, #f8f9fa);
        border-radius: 15px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .env-metric:hover {
        border-color: #2196F3;
        transform: translateY(-5px);
    }

    .env-icon {
        font-size: 2.5em;
        margin-bottom: 10px;
        background: linear-gradient(45deg, #2196F3, #21CBF3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 20px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'ponds:pond_list' %}">Ponds</a></li>
            <li class="breadcrumb-item"><a href="{% url 'ponds:pond_detail' pond.pk %}">{{ pond.name }}</a></li>
            <li class="breadcrumb-item active">Enhanced Analytics</li>
        </ol>
    </nav>

    <!-- Enhanced Header -->
    <div class="enhanced-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-3">
                    <i class="fas fa-chart-line me-3"></i>
                    {{ pond.name }} Analytics
                </h1>
                <p class="lead mb-0">Advanced monitoring and performance insights</p>
                <p class="mb-0 mt-2">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {% if pond.farm %}{{ pond.farm.name }}{% else %}No Farm Assigned{% endif %}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <div class="status-badge status-{{ pond.status }}">
                    {{ pond.get_status_display }}
                </div>
                <div class="mt-3">
                    <small class="text-white-50">Last Updated: {{ pond.updated_at|date:"M d, Y H:i" }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="analytics-card">
        <div class="card-header">
            <i class="fas fa-tachometer-alt card-icon"></i>
            <h3 class="card-title">Key Performance Metrics</h3>
        </div>
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">{{ pond.size|floatformat:1 }}</div>
                <div class="metric-label">Size (hectares)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{ pond.occupancy|default:"0" }}%</div>
                <div class="metric-label">Occupancy</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{ pond.aerators.count }}</div>
                <div class="metric-label">Aerators</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">
                    {% if pond.days_since_stocking %}
                        {{ pond.days_since_stocking }}
                    {% else %}
                        N/A
                    {% endif %}
                </div>
                <div class="metric-label">Days Since Stocking</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">
                    {% if pond.days_until_harvest %}
                        {{ pond.days_until_harvest }}
                    {% else %}
                        N/A
                    {% endif %}
                </div>
                <div class="metric-label">Days Until Harvest</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{{ pond.water_quality|default:"N/A" }}</div>
                <div class="metric-label">Water Quality</div>
            </div>
        </div>
    </div>

    <!-- Analytics Grid -->
    <div class="analytics-grid">
        <!-- Production Timeline -->
        <div class="analytics-card">
            <div class="card-header">
                <i class="fas fa-timeline card-icon"></i>
                <h3 class="card-title">Production Timeline</h3>
            </div>
            
            {% if pond.stocked_date %}
            <div class="timeline-item">
                <i class="fas fa-seedling timeline-icon"></i>
                <div class="timeline-content">
                    <h6>Stocking Date</h6>
                    <p>{{ pond.stocked_date|date:"M d, Y" }} ({{ pond.days_since_stocking }} days ago)</p>
                </div>
            </div>
            {% endif %}

            {% if pond.harvest_date %}
            <div class="timeline-item">
                <i class="fas fa-calendar-check timeline-icon"></i>
                <div class="timeline-content">
                    <h6>Planned Harvest</h6>
                    <p>{{ pond.harvest_date|date:"M d, Y" }} 
                        {% if pond.days_until_harvest > 0 %}
                            (in {{ pond.days_until_harvest }} days)
                        {% else %}
                            (overdue)
                        {% endif %}
                    </p>
                </div>
            </div>
            {% endif %}

            <div class="timeline-item">
                <i class="fas fa-water timeline-icon"></i>
                <div class="timeline-content">
                    <h6>Current Status</h6>
                    <p>{{ pond.get_status_display }} - {{ pond.species|default:"Species not specified" }}</p>
                </div>
            </div>
        </div>

        <!-- Environmental Conditions -->
        <div class="analytics-card">
            <div class="card-header">
                <i class="fas fa-thermometer-half card-icon"></i>
                <h3 class="card-title">Environmental Monitoring</h3>
            </div>
            
            <div class="environmental-grid">
                <div class="env-metric">
                    <div class="env-icon">🌡️</div>
                    <div class="metric-value">{{ pond.temperature|default:"--" }}°C</div>
                    <div class="metric-label">Temperature</div>
                </div>
                <div class="env-metric">
                    <div class="env-icon">💧</div>
                    <div class="metric-value">{{ pond.ph_level|default:"--" }}</div>
                    <div class="metric-label">pH Level</div>
                </div>
                <div class="env-metric">
                    <div class="env-icon">🌊</div>
                    <div class="metric-value">{{ pond.oxygen_level|default:"--" }}</div>
                    <div class="metric-label">Oxygen (mg/L)</div>
                </div>
                <div class="env-metric">
                    <div class="env-icon">🧂</div>
                    <div class="metric-value">{{ pond.salinity|default:"--" }}</div>
                    <div class="metric-label">Salinity (ppt)</div>
                </div>
            </div>

            {% if pond.latitude and pond.longitude %}
            <div class="mt-3 text-center">
                <small class="text-muted">
                    <i class="fas fa-map-marker-alt"></i>
                    {{ pond.latitude|floatformat:4 }}°N, {{ pond.longitude|floatformat:4 }}°E
                </small>
            </div>
            {% endif %}
        </div>

        <!-- Equipment Status -->
        <div class="analytics-card">
            <div class="card-header">
                <i class="fas fa-cogs card-icon"></i>
                <h3 class="card-title">Equipment Status</h3>
            </div>
            
            <div class="row">
                <div class="col-6 text-center">
                    <div class="progress-circle" style="background: conic-gradient(#28a745 0deg {% widthratio pond.aerators.count pond.aerators.count 360 %}deg, #e9ecef 0deg);">
                        {{ pond.aerators.count }}
                    </div>
                    <div class="metric-label">Total Aerators</div>
                </div>
                <div class="col-6 text-center">
                    <div class="progress-circle" style="background: conic-gradient(#2196F3 0deg 270deg, #e9ecef 0deg);">
                        75%
                    </div>
                    <div class="metric-label">Operational</div>
                </div>
            </div>

            {% if pond.aerators.all %}
            <div class="mt-3">
                <h6>Aerator Details:</h6>
                {% for aerator in pond.aerators.all %}
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <span>{{ aerator.name }}</span>
                    <span class="badge bg-{% if aerator.status == 'active' %}success{% else %}secondary{% endif %}">
                        {{ aerator.get_status_display }}
                    </span>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <!-- Performance Chart -->
        <div class="analytics-card" style="grid-column: 1 / -1;">
            <div class="card-header">
                <i class="fas fa-chart-area card-icon"></i>
                <h3 class="card-title">Performance Trends</h3>
            </div>
            <div class="chart-container">
                <canvas id="performanceChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="analytics-card">
        <div class="card-header">
            <i class="fas fa-tools card-icon"></i>
            <h3 class="card-title">Quick Actions</h3>
        </div>
        <div class="action-buttons">
            <a href="{% url 'ponds:pond_detail' pond.pk %}" class="action-btn action-btn-primary">
                <i class="fas fa-eye"></i> Basic Details
            </a>
            <a href="{% url 'ponds:pond_update' pond.pk %}" class="action-btn action-btn-secondary">
                <i class="fas fa-edit"></i> Edit Pond
            </a>
            {% if pond.latitude and pond.longitude %}
            <a href="{% url 'ponds:aerator_map' pond.pk %}" class="action-btn action-btn-success">
                <i class="fas fa-map"></i> View on Map
            </a>
            {% endif %}
            <a href="{% url 'ponds:pond_list' %}" class="action-btn action-btn-secondary">
                <i class="fas fa-list"></i> All Ponds
            </a>
        </div>
    </div>
</div>

<script>
// Performance Chart
const ctx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6'],
        datasets: [{
            label: 'Growth Rate (%)',
            data: [12, 19, 15, 25, 22, 30],
            borderColor: '#2196F3',
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Survival Rate (%)',
            data: [98, 97, 96, 95, 94, 93],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Feed Conversion Ratio',
            data: [1.2, 1.3, 1.25, 1.4, 1.35, 1.3],
            borderColor: '#ffc107',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: true,
                text: 'Pond Performance Over Time'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0,0,0,0.1)'
                }
            },
            x: {
                grid: {
                    color: 'rgba(0,0,0,0.1)'
                }
            }
        }
    }
});
</script>
{% endblock %}
