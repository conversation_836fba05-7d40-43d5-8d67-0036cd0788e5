<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Record Medicine Application - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    
    .form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        border: none;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-syringe medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Application Form</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Record Medicine Application</h2>
                    <p class="mb-0 opacity-75">Apply medicine to pond and track usage</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Date</small>
                                <strong>{{ current_date|default:"Today" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/application/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to Applications
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-list"></i>
                Applications List
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                Medicines
            </a>
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-tags"></i>
                Categories
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-10">
                    <div class="card form-card">
                        <div class="card-body">
                            <form method="post">
                                {% csrf_token %}
                                
                                {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {% for error in form.non_field_errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="{{ form.pond.id_for_label }}" class="form-label">
                                            <i class="fas fa-water me-2"></i>Pond
                                        </label>
                                        {% if form.pond.errors %}
                                        <div class="text-danger small">{{ form.pond.errors.0 }}</div>
                                        {% endif %}
                                        <select name="{{ form.pond.name }}" id="{{ form.pond.id_for_label }}" class="form-select {% if form.pond.errors %}is-invalid{% endif %}" required>
                                            <option value="">Select a pond</option>
                                            {% for value, text in form.pond.field.choices %}
                                            {% if value %}
                                            <option value="{{ value }}" {% if form.pond.value|stringformat:"i" == value|stringformat:"i" %}selected{% endif %}>{{ text }}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="{{ form.medicine.id_for_label }}" class="form-label">
                                            <i class="fas fa-pills me-2"></i>Medicine
                                        </label>
                                        {% if form.medicine.errors %}
                                        <div class="text-danger small">{{ form.medicine.errors.0 }}</div>
                                        {% endif %}
                                        <select name="{{ form.medicine.name }}" id="{{ form.medicine.id_for_label }}" class="form-select {% if form.medicine.errors %}is-invalid{% endif %}" required>
                                            <option value="">Select a medicine</option>
                                            {% for value, text in form.medicine.field.choices %}
                                            {% if value %}
                                            <option value="{{ value }}" {% if form.medicine.value|stringformat:"i" == value|stringformat:"i" %}selected{% endif %}>{{ text }}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="{{ form.application_date.id_for_label }}" class="form-label">
                                            <i class="fas fa-calendar me-2"></i>Application Date
                                        </label>
                                        {% if form.application_date.errors %}
                                        <div class="text-danger small">{{ form.application_date.errors.0 }}</div>
                                        {% endif %}
                                        <input type="date" name="{{ form.application_date.name }}" id="{{ form.application_date.id_for_label }}" class="form-control {% if form.application_date.errors %}is-invalid{% endif %}" value="{{ form.application_date.value|date:'Y-m-d'|default:current_date }}" required>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.quantity_used.id_for_label }}" class="form-label">
                                            <i class="fas fa-weight me-2"></i>Quantity Used
                                        </label>
                                        {% if form.quantity_used.errors %}
                                        <div class="text-danger small">{{ form.quantity_used.errors.0 }}</div>
                                        {% endif %}
                                        <input type="number" step="0.01" name="{{ form.quantity_used.name }}" id="{{ form.quantity_used.id_for_label }}" class="form-control {% if form.quantity_used.errors %}is-invalid{% endif %}" value="{{ form.quantity_used.value|default:'' }}" placeholder="0.00" required>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.reason.id_for_label }}" class="form-label">
                                            <i class="fas fa-info-circle me-2"></i>Reason
                                        </label>
                                        {% if form.reason.errors %}
                                        <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                                        {% endif %}
                                        <select name="{{ form.reason.name }}" id="{{ form.reason.id_for_label }}" class="form-select {% if form.reason.errors %}is-invalid{% endif %}" required>
                                            <option value="">Select reason</option>
                                            {% for value, text in form.reason.field.choices %}
                                            {% if value %}
                                            <option value="{{ value }}" {% if form.reason.value == value %}selected{% endif %}>{{ text }}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.applied_by.id_for_label }}" class="form-label">
                                        <i class="fas fa-user me-2"></i>Applied By
                                    </label>
                                    {% if form.applied_by.errors %}
                                    <div class="text-danger small">{{ form.applied_by.errors.0 }}</div>
                                    {% endif %}
                                    <select name="{{ form.applied_by.name }}" id="{{ form.applied_by.id_for_label }}" class="form-select {% if form.applied_by.errors %}is-invalid{% endif %}" required>
                                        <option value="">Select staff member</option>
                                        {% for staff in staff_members %}
                                        <option value="{{ staff.username }}" {% if form.applied_by.value == staff.username %}selected{% endif %}>
                                            {{ staff.get_full_name|default:staff.username }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                                        <i class="fas fa-sticky-note me-2"></i>Notes
                                    </label>
                                    {% if form.notes.errors %}
                                    <div class="text-danger small">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                    <textarea name="{{ form.notes.name }}" id="{{ form.notes.id_for_label }}" class="form-control {% if form.notes.errors %}is-invalid{% endif %}" rows="3" placeholder="Optional notes about the application...">{{ form.notes.value|default:'' }}</textarea>
                                </div>
                                
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="/medicine/application/" class="btn btn-outline-secondary me-md-2">
                                        <i class="fas fa-times me-1"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i> Record Application
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
