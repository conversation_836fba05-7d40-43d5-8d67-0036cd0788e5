{% extends 'website/base.html' %}

{% block content %}
<!-- Support Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Support Center
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Get the help you need to maximize your aquaculture operations
                </p>
                <div data-aos="fade-up" data-aos-delay="200">
                    <a href="#contact-support" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-headset me-2"></i>Contact Support
                    </a>
                    <a href="#knowledge-base" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-book me-2"></i>Knowledge Base
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Help Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">
                    How can we help you?
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Choose the support option that best fits your needs
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="feature-card text-center h-100" data-aos="fade-up">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Emergency Support</h5>
                    <p class="text-muted mb-4">
                        Critical issues affecting your farm operations. Available 24/7 for urgent matters.
                    </p>
                    <div class="mt-auto">
                        <div class="alert alert-danger mb-3">
                            <strong>Emergency Hotline:</strong><br>
                            +1 (555) 123-HELP
                        </div>
                        <p class="text-muted small">
                            Response time: &lt; 1 hour
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card text-center h-100" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Live Chat</h5>
                    <p class="text-muted mb-4">
                        Real-time assistance for technical questions and general inquiries.
                    </p>
                    <div class="mt-auto">
                        <button class="btn btn-gradient btn-lg mb-3" onclick="openLiveChat()">
                            <i class="fas fa-comment me-2"></i>Start Chat
                        </button>
                        <p class="text-muted small">
                            Available: Mon-Fri 8AM-8PM EST
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card text-center h-100" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h5 class="fw-bold mb-3">Email Support</h5>
                    <p class="text-muted mb-4">
                        Detailed technical support and documentation requests.
                    </p>
                    <div class="mt-auto">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-gradient btn-lg mb-3">
                            <i class="fas fa-envelope me-2"></i>Send Email
                        </a>
                        <p class="text-muted small">
                            Response time: &lt; 24 hours
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Knowledge Base Section -->
<section id="knowledge-base" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">
                    Knowledge Base
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Find answers to frequently asked questions and browse our documentation
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            {% for category in kb_categories %}
            <div class="col-lg-6">
                <div class="feature-card" data-aos="fade-up" data-aos-delay="{% if forloop.counter0 == 0 %}0{% elif forloop.counter0 == 1 %}100{% elif forloop.counter0 == 2 %}200{% else %}300{% endif %}">
                    <div class="d-flex align-items-start">
                        <div class="feature-icon me-3" style="background: linear-gradient(135deg, #667eea, #764ba2); width: 60px; height: 60px;">
                            <i class="fas fa-{{ category.icon }}"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="fw-bold mb-2">{{ category.title }}</h5>
                            <p class="text-muted mb-3">{{ category.description }}</p>
                            
                            <div class="list-group list-group-flush">
                                {% for article in category.articles %}
                                <a href="#" class="list-group-item list-group-item-action border-0 px-0 py-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-muted me-2"></i>
                                        <span class="text-dark">{{ article.title }}</span>
                                        <small class="text-muted ms-auto">{{ article.views }} views</small>
                                    </div>
                                </a>
                                {% endfor %}
                            </div>
                            
                            <div class="mt-3">
                                <a href="#" class="text-primary fw-semibold">
                                    View all {{ category.count }} articles <i class="fas fa-arrow-right ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-5 fw-bold" data-aos="fade-up">
                    Frequently Asked Questions
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Quick answers to common questions
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="faqAccordion">
                    {% for faq in faqs %}
                    <div class="accordion-item border-0 mb-3" data-aos="fade-up" data-aos-delay="{% if forloop.counter0 == 0 %}0{% elif forloop.counter0 == 1 %}100{% elif forloop.counter0 == 2 %}200{% elif forloop.counter0 == 3 %}300{% elif forloop.counter0 == 4 %}400{% elif forloop.counter0 == 5 %}500{% elif forloop.counter0 == 6 %}600{% else %}700{% endif %}">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed glass" type="button" 
                                    data-bs-toggle="collapse" data-bs-target="#faq{{ forloop.counter }}" 
                                    aria-expanded="false" aria-controls="faq{{ forloop.counter }}">
                                <strong>{{ faq.question }}</strong>
                            </button>
                        </h2>
                        <div id="faq{{ forloop.counter }}" class="accordion-collapse collapse" 
                             data-bs-parent="#faqAccordion">
                            <div class="accordion-body glass">
                                {{ faq.answer|linebreaks }}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Support Section -->
<section id="contact-support" class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <div data-aos="fade-right">
                    <h2 class="display-5 fw-bold mb-4">
                        Still need help?
                    </h2>
                    <p class="lead mb-4">
                        Our expert support team is here to help you succeed. Get personalized 
                        assistance tailored to your specific needs.
                    </p>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-clock me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <div class="fw-bold">Response Time</div>
                                    <div class="opacity-75">Within 24 hours</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users me-3" style="font-size: 1.5rem;"></i>
                                <div>
                                    <div class="fw-bold">Expert Team</div>
                                    <div class="opacity-75">Aquaculture specialists</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div data-aos="fade-left">
                    <form class="glass p-4" style="background: rgba(255,255,255,0.1); border-radius: 15px;">
                        <div class="mb-3">
                            <label for="supportName" class="form-label text-white">Name</label>
                            <input type="text" class="form-control" id="supportName" 
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                        </div>
                        <div class="mb-3">
                            <label for="supportEmail" class="form-label text-white">Email</label>
                            <input type="email" class="form-control" id="supportEmail" 
                                   style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                        </div>
                        <div class="mb-3">
                            <label for="supportPriority" class="form-label text-white">Priority</label>
                            <select class="form-control" id="supportPriority" 
                                    style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                                <option value="low">Low - General inquiry</option>
                                <option value="normal">Normal - Technical support</option>
                                <option value="high">High - System issue</option>
                                <option value="urgent">Urgent - Critical problem</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="supportMessage" class="form-label text-white">Message</label>
                            <textarea class="form-control" id="supportMessage" rows="4" 
                                      style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;" 
                                      placeholder="Describe your issue or question..."></textarea>
                        </div>
                        <button type="submit" class="btn btn-light text-primary fw-bold w-100">
                            <i class="fas fa-paper-plane me-2"></i>Submit Support Request
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Additional Resources -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center">
                <h2 class="display-5 fw-bold mb-4" data-aos="fade-up">
                    Additional Resources
                </h2>
                
                <div class="row g-4 justify-content-center">
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up">
                            <a href="{% url 'website:docs' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-book me-2"></i>Documentation
                            </a>
                            <small class="text-muted d-block">API docs and guides</small>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up" data-aos-delay="100">
                            <a href="{% url 'website:blog' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-newspaper me-2"></i>Blog
                            </a>
                            <small class="text-muted d-block">Latest insights and updates</small>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up" data-aos-delay="200">
                            <a href="{% url 'website:demo' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-play me-2"></i>Live Demo
                            </a>
                            <small class="text-muted d-block">Try our platform</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function openLiveChat() {
    // Placeholder for live chat integration
    alert('Live chat would open here. In production, this would integrate with your chat service.');
}
</script>
{% endblock %}
