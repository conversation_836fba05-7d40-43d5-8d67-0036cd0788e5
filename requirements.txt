# Django and web framework
Django>=4.2
django-crispy-forms==2.0
crispy-bootstrap5==0.7
Pillow==10.4.0
python-dateutil==2.8.2
django-environ==0.11.2
djangorestframework==3.14.0
django-filter==23.3
django-cors-headers==4.3.0
pytz==2023.3
requests==2.31.0
gunicorn==21.2.0
whitenoise==6.5.0
python-dotenv==1.0.0
Jinja2==3.1.2
Flask==2.3.3
APScheduler==3.10.1

# Production dependencies
psycopg2-binary==2.9.6  # PostgreSQL adapter
dj-database-url==2.0.0  # Database URL configuration
django-redis==5.3.0     # Redis cache backend
redis==4.5.5            # Redis client
uvicorn==0.22.0         # ASGI server
django-storages==1.13.2 # Storage backends for static/media files
boto3==1.28.15          # AWS S3 integration
sentry-sdk==1.28.1      # Error tracking

# Security and monitoring
django-ratelimit==4.1.0  # Rate limiting
django-security==0.17.0  # Security middleware
prometheus-client==0.17.1  # Metrics collection
django-health-check==3.17.0  # Health checks

# Data science and ML
numpy>=1.20.0
scipy>=1.7.0
matplotlib>=3.4.0
pandas>=1.3.0
scikit-learn>=1.0.0

# Image processing and computer vision
opencv-python>=4.5.0
# albumentations>=1.0.0

# Audio processing for bioacoustics
# librosa>=0.8.0
# soundfile>=0.10.0

# IoT and hardware integration
# pyserial>=3.5
paho-mqtt>=1.6.0

# Geospatial
# geopandas>=0.10.0
folium>=0.12.0
# shapely>=1.8.0

# Visualization
seaborn>=0.11.0
plotly>=5.0.0

# Business Intelligence and Reporting
reportlab>=4.0.0
openpyxl>=3.1.0

# Database
influxdb-client>=1.24.0

# Environment variables (should be in .env file, not requirements.txt)
# INFLUXDB_URL=http://localhost:8086
# INFLUXDB_TOKEN=your-token
# INFLUXDB_ORG=your-org
# INFLUXDB_BUCKET=shrimp_metrics
