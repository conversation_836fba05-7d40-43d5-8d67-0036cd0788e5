{% extends "base.html" %}
{% load static %}

{% block title %}Unified Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .dashboard-container {
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .dashboard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 8s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    /* Zoom Notification Animations */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2);
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #636e72;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
    }

    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .sidebar-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .widget {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .widget-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f3f4;
    }

    .widget-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .widget-actions {
        display: flex;
        gap: 10px;
    }

    .widget-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 8px 16px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .widget-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    .map-widget {
        height: 600px;
        border-radius: 15px;
        overflow: hidden;
        position: relative;
    }

    /* Enhanced GPS System Styles */
    .gps-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .gps-stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
    }

    .gps-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .gps-stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .gps-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .gps-stat-label {
        color: #636e72;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.8rem;
    }

    .gps-control-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .gps-panel-title {
        font-size: 1rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .gps-layer-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }

    .gps-layer-btn {
        background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-layer-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .gps-layer-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .gps-layer-btn.gps-ponds.active {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
    }

    .gps-layer-btn.gps-workers.active {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
    }

    .gps-layer-btn.gps-aerators.active {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
    }

    .gps-layer-btn.gps-geofences.active {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    }

    .gps-map-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .gps-map-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-map-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    .gps-status-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }

    .gps-status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .gps-status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .gps-status-working { background: #00b894; }
    .gps-status-available { background: #0984e3; }
    .gps-status-break { background: #fdcb6e; }
    .gps-status-traveling { background: #6c5ce7; }
    .gps-status-offline { background: #636e72; }

    .recent-activities {
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 2px;
    }

    .activity-time {
        font-size: 0.85rem;
        color: #636e72;
    }

    .weather-widget {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }

    .weather-current {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
    }

    .weather-icon {
        font-size: 3rem;
    }

    .weather-temp {
        font-size: 2.5rem;
        font-weight: 700;
    }

    .weather-desc {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .weather-details {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .weather-detail {
        background: rgba(255,255,255,0.2);
        padding: 10px;
        border-radius: 10px;
        text-align: center;
    }

    .alerts-widget {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
    }

    .alert-item {
        background: rgba(255,255,255,0.2);
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .alert-item:last-child {
        margin-bottom: 0;
    }

    .quick-actions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .quick-action {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .quick-action:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 184, 148, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-action-icon {
        font-size: 2rem;
    }

    .quick-action-text {
        font-weight: 600;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .dashboard-stats {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 480px) {
        .dashboard-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">🌊 Unified Dashboard</h1>
                <p class="mb-0" style="opacity: 0.9;">Complete overview of your shrimp farming operations</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-2"></i> Refresh
                </button>
                <button class="btn btn-outline-light" onclick="exportReport()">
                    <i class="fas fa-download me-2"></i> Export
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="dashboard-stats">
        <div class="stat-card">
            <div class="stat-icon">🏢</div>
            <div class="stat-number">{{ total_farms|default:0 }}</div>
            <div class="stat-label">Total Farms</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🦐</div>
            <div class="stat-number">{{ total_ponds|default:0 }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">⚡</div>
            <div class="stat-number">{{ total_aerators|default:0 }}</div>
            <div class="stat-label">Active Aerators</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">👥</div>
            <div class="stat-number">{{ total_workers|default:0 }}</div>
            <div class="stat-label">Workers</div>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Main Content -->
        <div class="main-content">
            <!-- Enhanced GPS Statistics -->
            <div class="gps-stats-grid">
                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="gps-stat-value" id="farms-count">{{ total_farms|default:0 }}</div>
                    <div class="gps-stat-label">Active Farms</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <div class="gps-stat-value" id="ponds-count">{{ total_ponds|default:0 }}</div>
                    <div class="gps-stat-label">Monitored Ponds</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="gps-stat-value" id="workers-count">{{ total_workers|default:0 }}</div>
                    <div class="gps-stat-label">GPS Workers</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-fan"></i>
                    </div>
                    <div class="gps-stat-value" id="aerators-count">{{ total_aerators|default:0 }}</div>
                    <div class="gps-stat-label">Active Aerators</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="gps-stat-value" id="security-zones">0</div>
                    <div class="gps-stat-label">Security Zones</div>
                </div>
            </div>

            <!-- Enhanced GPS Control Panel -->
            <div class="gps-control-panel">
                <div class="gps-panel-title">
                    <i class="fas fa-circle"></i>
                    WORKER STATUS LEGEND
                </div>
                <div class="gps-status-legend">
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-working"></div>
                        <span>Working</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-available"></div>
                        <span>Available</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-break"></div>
                        <span>On Break</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-traveling"></div>
                        <span>Traveling</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-offline"></div>
                        <span>Offline</span>
                    </div>
                </div>

                <!-- Layer Controls -->
                <div class="gps-panel-title">
                    <i class="fas fa-layer-group"></i>
                    LAYER CONTROLS
                </div>
                <div class="gps-layer-controls">
                    <button class="gps-layer-btn active" id="farms-layer">
                        <i class="fas fa-warehouse"></i>
                        FARMS
                    </button>
                    <button class="gps-layer-btn gps-ponds active" id="ponds-layer">
                        <i class="fas fa-water"></i>
                        PONDS
                    </button>
                    <button class="gps-layer-btn gps-workers active" id="workers-layer">
                        <i class="fas fa-users"></i>
                        WORKERS
                    </button>
                    <button class="gps-layer-btn gps-aerators active" id="aerators-layer">
                        <i class="fas fa-fan"></i>
                        AERATORS
                    </button>
                    <button class="gps-layer-btn gps-geofences" id="geofences-layer">
                        <i class="fas fa-draw-polygon"></i>
                        GEOFENCES
                    </button>
                </div>

                <!-- Map Controls -->
                <div class="gps-panel-title">
                    <i class="fas fa-cog"></i>
                    MAP CONTROLS
                </div>
                <div class="gps-map-controls">
                    <button class="gps-map-btn" id="center-map">
                        <i class="fas fa-crosshairs"></i>
                        CENTER MAP
                    </button>
                    <button class="gps-map-btn gps-refresh" id="refresh-data">
                        <i class="fas fa-sync-alt"></i>
                        REFRESH
                    </button>
                    <button class="gps-map-btn gps-fit" id="fit-all">
                        <i class="fas fa-expand-arrows-alt"></i>
                        FIT ALL
                    </button>
                    <button class="gps-map-btn" onclick="fullscreenMap()">
                        <i class="fas fa-expand"></i>
                        FULLSCREEN
                    </button>
                </div>
            </div>

            <!-- Enhanced GPS Map Container -->
            <div class="widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-map-marked-alt"></i>
                        Enhanced GPS Tracking System
                    </h3>
                    <div class="widget-actions">
                        <button class="widget-btn" onclick="toggleWeatherLayer()">
                            <i class="fas fa-cloud-sun"></i> Weather
                        </button>
                        <button class="widget-btn" onclick="toggleMapView()">
                            <i class="fas fa-layer-group"></i> Layers
                        </button>
                    </div>
                </div>
                <div id="dashboard-map" class="map-widget" style="height: 400px; background: #f0f0f0; border-radius: 8px; position: relative;">
                    <div id="map-loading" style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                        <div style="text-align: center;">
                            <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 10px;"></i>
                            <div>Loading Enhanced GPS Map...</div>
                            <div id="map-status" style="font-size: 0.8rem; margin-top: 5px; opacity: 0.7;">Initializing...</div>
                        </div>
                    </div>

                    <!-- Map Controls -->
                    <div id="map-controls" style="
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        z-index: 1000;
                        display: none;
                    ">
                        <div style="background: rgba(255, 255, 255, 0.9); border-radius: 8px; padding: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                            <button onclick="centerMap()" style="background: #667eea; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-size: 12px;" title="Center Map">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                            <button onclick="fitAllMarkers()" style="background: #20c997; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-size: 12px;" title="Fit All Markers">
                                <i class="fas fa-expand-arrows-alt"></i>
                            </button>
                            <button onclick="refreshMapData()" style="background: #fd7e14; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-size: 12px;" title="Refresh Data">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                            <button onclick="getCurrentLocation()" style="background: #e74c3c; color: white; border: none; padding: 8px 12px; border-radius: 5px; margin: 2px; cursor: pointer; font-size: 12px;" title="My Location">
                                <i class="fas fa-location-arrow"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Map Legend -->
                    <div id="map-legend" style="
                        position: absolute;
                        bottom: 10px;
                        left: 10px;
                        z-index: 1000;
                        background: rgba(255, 255, 255, 0.9);
                        border-radius: 8px;
                        padding: 10px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        font-size: 12px;
                        display: none;
                    ">
                        <div style="font-weight: 600; margin-bottom: 5px;">Map Legend</div>
                        <div style="display: flex; flex-direction: column; gap: 3px;">
                            <div><span style="color: green; font-size: 14px;">●</span> Farms (<span id="farms-count">0</span>)</div>
                            <div><span style="color: blue; font-size: 14px;">●</span> Ponds (<span id="ponds-count">0</span>)</div>
                            <div><span style="color: orange; font-size: 14px;">●</span> Workers (<span id="workers-count">0</span>)</div>
                            <div><span style="color: red; font-size: 14px;">●</span> Aerators (<span id="aerators-count">0</span>)</div>
                            <div><span style="color: #007bff; font-size: 14px;">●</span> Weather (<span id="weather-count">0</span>)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-clock"></i>
                        Recent Activities
                    </h3>
                    <div class="widget-actions">
                        <button class="widget-btn" onclick="viewAllActivities()">
                            <i class="fas fa-list"></i> View All
                        </button>
                    </div>
                </div>
                <div class="recent-activities">
                    {% for activity in recent_activities %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas {{ activity.icon }}"></i>
                        </div>
                        <div class="activity-content">
                            <div class="activity-title">{{ activity.title }}</div>
                            <div class="activity-time">{{ activity.timestamp|timesince }} ago</div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-history" style="font-size: 2rem; color: #dee2e6; margin-bottom: 10px;"></i>
                        <p class="text-muted">No recent activities</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content">
            <!-- Weather Widget -->
            <div class="widget weather-widget">
                <div class="widget-header" style="border-color: rgba(255,255,255,0.3);">
                    <h3 class="widget-title" style="color: white;">
                        <i class="fas fa-cloud-sun"></i>
                        Weather Status
                    </h3>
                </div>
                <div class="weather-current">
                    <div class="weather-icon">☀️</div>
                    <div>
                        <div class="weather-temp">{{ current_weather.temperature|default:"--" }}°C</div>
                        <div class="weather-desc">{{ current_weather.description|default:"No data" }}</div>
                    </div>
                </div>
                <div class="weather-details">
                    <div class="weather-detail">
                        <div>💧 Humidity</div>
                        <div>{{ current_weather.humidity|default:"--" }}%</div>
                    </div>
                    <div class="weather-detail">
                        <div>💨 Wind</div>
                        <div>{{ current_weather.wind_speed|default:"--" }} km/h</div>
                    </div>
                </div>
            </div>

            <!-- Alerts Widget -->
            <div class="widget alerts-widget">
                <div class="widget-header" style="border-color: rgba(255,255,255,0.3);">
                    <h3 class="widget-title" style="color: white;">
                        <i class="fas fa-exclamation-triangle"></i>
                        System Alerts
                    </h3>
                </div>
                {% for alert in system_alerts %}
                <div class="alert-item">
                    <i class="fas {{ alert.icon }}"></i>
                    <div>
                        <div style="font-weight: 600;">{{ alert.title }}</div>
                        <div style="font-size: 0.85rem; opacity: 0.9;">{{ alert.message }}</div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-3">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <p style="margin: 0; opacity: 0.9;">All systems normal</p>
                </div>
                {% endfor %}
            </div>

            <!-- Quick Actions -->
            <div class="widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="quick-actions">
                    <a href="{% url 'ponds:farm_create_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">🏢</div>
                        <div class="quick-action-text">Farm Creation Wizard</div>
                    </a>
                    <a href="{% url 'ponds:create_pond_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">�‍♂️</div>
                        <div class="quick-action-text">Pond Creation Wizard</div>
                    </a>
                    <a href="{% url 'ponds:create_pond_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">�🦐</div>
                        <div class="quick-action-text">3-Step Pond Wizard</div>
                    </a>
                    <a href="{% url 'ponds:farm_create_with_map' %}" class="quick-action">
                        <div class="quick-action-icon">🗺️</div>
                        <div class="quick-action-text">Create Farm</div>
                    </a>
                    <a href="{% url 'ponds:create_pond_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">🌊</div>
                        <div class="quick-action-text">Create Pond</div>
                    </a>
                    <a href="{% url 'ponds:farm_list' %}" class="quick-action">
                        <div class="quick-action-icon">📊</div>
                        <div class="quick-action-text">View Farms</div>
                    </a>
                    <a href="{% url 'ponds:pond_list' %}" class="quick-action">
                        <div class="quick-action-icon">📋</div>
                        <div class="quick-action-text">View Ponds</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Google Maps API will be loaded at the end -->

<!-- Define map data and callback function BEFORE loading Google Maps API -->
<script>
    // Map data initialization - MUST be defined before initMap
    const mapData = {
        farms: {{ farms_data|safe }},
        ponds: {{ ponds_data|safe }},
        workers: {{ workers_data|safe }},
        aerators: {{ aerators_data|safe }},
        weather: {{ weather_data|safe }},
        geofences: {{ geofences_data|safe }},
        centerLat: {{ center_lat|default:"13.0827" }},
        centerLng: {{ center_lng|default:"80.2707" }}
    };

    // Debug: Log the data to console
    console.log('DEBUG: Map data loaded:', mapData);
    console.log('DEBUG: Farms count:', mapData.farms.length);
    console.log('DEBUG: Ponds count:', mapData.ponds.length);
    console.log('DEBUG: Workers count:', mapData.workers.length);
    console.log('DEBUG: Aerators count:', mapData.aerators.length);
    console.log('DEBUG: Weather stations count:', mapData.weather.length);

    console.log('📊 Map data initialized:', {
        farms: mapData.farms.length,
        ponds: mapData.ponds.length,
        workers: mapData.workers.length,
        aerators: mapData.aerators.length,
        weather: mapData.weather.length,
        geofences: mapData.geofences.length,
        center: [mapData.centerLat, mapData.centerLng]
    });

    // Helper function to update map status - MUST be defined before initMap
    function updateMapStatus(message) {
        const statusElement = document.getElementById('map-status');
        if (statusElement) {
            statusElement.textContent = message;
        }
        console.log('📍 Map Status:', message);
    }

    // Define the callback function globally BEFORE loading the API
    window.initMap = function() {
        console.log('🗺️ Google Maps API callback triggered!');
        updateMapStatus('Google Maps API loaded, creating map...');

        const mapElement = document.getElementById('dashboard-map');
        const loadingElement = document.getElementById('map-loading');

        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }

        try {
            console.log('📍 Creating map with center:', mapData.centerLat, mapData.centerLng);
            console.log('📊 Data available for markers:', {
                farms: mapData.farms.length,
                ponds: mapData.ponds.length,
                workers: mapData.workers.length,
                aerators: mapData.aerators.length
            });

            // Create a simple map
            const map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: mapData.centerLat, lng: mapData.centerLng },
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            console.log('✅ Map created successfully');

            // Add markers for farms with click functionality
            if (mapData.farms && mapData.farms.length > 0) {
                console.log('🏭 Adding', mapData.farms.length, 'farm markers');
                mapData.farms.forEach((farm, index) => {
                    console.log('Adding farm marker:', farm);
                    const marker = new google.maps.Marker({
                        position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
                        map: map,
                        title: farm.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="green"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>'),
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    // Add info window for farm
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>🏭 ${farm.name}</h4>
                                <p><strong>Location:</strong> ${farm.latitude}, ${farm.longitude}</p>
                                <p><strong>Ponds:</strong> ${farm.pond_count || 'N/A'}</p>
                                <p><strong>Area:</strong> ${farm.area || 'N/A'} hectares</p>
                            </div>
                        `
                    });

                    // Add click listener for zoom functionality
                    marker.addListener('click', () => {
                        map.panTo(marker.getPosition());
                        map.setZoom(16);
                        infoWindow.open(map, marker);
                    });
                });
            } else {
                console.log('⚠️ No farm data available');
            }

            // Add markers for ponds with click functionality
            if (mapData.ponds && mapData.ponds.length > 0) {
                console.log('🏊 Adding', mapData.ponds.length, 'pond markers');
                mapData.ponds.forEach((pond, index) => {
                    console.log('Adding pond marker:', pond);
                    const marker = new google.maps.Marker({
                        position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
                        map: map,
                        title: pond.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="blue"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>'),
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    // Add info window for pond
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>🏊 ${pond.name}</h4>
                                <p><strong>Farm:</strong> ${pond.farm_name || 'N/A'}</p>
                                <p><strong>Location:</strong> ${pond.latitude}, ${pond.longitude}</p>
                                <p><strong>Area:</strong> ${pond.area || 'N/A'} m²</p>
                                <p><strong>Depth:</strong> ${pond.depth || 'N/A'} m</p>
                            </div>
                        `
                    });

                    // Add click listener for zoom functionality
                    marker.addListener('click', () => {
                        map.panTo(marker.getPosition());
                        map.setZoom(18);
                        infoWindow.open(map, marker);
                    });
                });
            } else {
                console.log('⚠️ No pond data available');
            }

            // Add markers for workers with click functionality
            if (mapData.workers && mapData.workers.length > 0) {
                console.log('👷 Adding', mapData.workers.length, 'worker markers');
                mapData.workers.forEach((worker, index) => {
                    console.log('Adding worker marker:', worker);
                    const marker = new google.maps.Marker({
                        position: { lat: parseFloat(worker.latitude), lng: parseFloat(worker.longitude) },
                        map: map,
                        title: worker.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="orange"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>'),
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    // Add info window for worker
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>👷 ${worker.name}</h4>
                                <p><strong>Employee ID:</strong> ${worker.employee_id || 'N/A'}</p>
                                <p><strong>Status:</strong> ${worker.status || 'N/A'}</p>
                                <p><strong>Department:</strong> ${worker.department || 'N/A'}</p>
                                <p><strong>Phone:</strong> ${worker.phone || 'N/A'}</p>
                                <p><strong>Location:</strong> ${worker.latitude}, ${worker.longitude}</p>
                            </div>
                        `
                    });

                    // Add click listener for zoom functionality
                    marker.addListener('click', () => {
                        map.panTo(marker.getPosition());
                        map.setZoom(17);
                        infoWindow.open(map, marker);
                    });
                });
            } else {
                console.log('⚠️ No worker data available');
            }

            // Add markers for aerators with click functionality
            if (mapData.aerators && mapData.aerators.length > 0) {
                console.log('⚙️ Adding', mapData.aerators.length, 'aerator markers');
                mapData.aerators.forEach((aerator, index) => {
                    console.log('Adding aerator marker:', aerator);
                    const marker = new google.maps.Marker({
                        position: { lat: parseFloat(aerator.latitude), lng: parseFloat(aerator.longitude) },
                        map: map,
                        title: aerator.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="red"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>'),
                            scaledSize: new google.maps.Size(24, 24)
                        }
                    });

                    // Add info window for aerator
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>⚙️ ${aerator.name}</h4>
                                <p><strong>Pond:</strong> ${aerator.pond_name || 'N/A'}</p>
                                <p><strong>Status:</strong> ${aerator.status || 'N/A'}</p>
                                <p><strong>Location:</strong> ${aerator.latitude}, ${aerator.longitude}</p>
                            </div>
                        `
                    });

                    // Add click listener for zoom functionality
                    marker.addListener('click', () => {
                        map.panTo(marker.getPosition());
                        map.setZoom(20);
                        infoWindow.open(map, marker);
                    });
                });
            } else {
                console.log('⚠️ No aerator data available');
            }

            // Add markers for weather stations with enhanced display
            if (mapData.weather && mapData.weather.length > 0) {
                console.log('🌤️ Adding', mapData.weather.length, 'weather station markers');
                mapData.weather.forEach((weather, index) => {
                    console.log('Adding weather marker:', weather);

                    // Create weather icon based on condition
                    let weatherIcon = '🌤️'; // default
                    if (weather.condition) {
                        const condition = weather.condition.toLowerCase();
                        if (condition.includes('rain') || condition.includes('shower')) {
                            weatherIcon = '🌧️';
                        } else if (condition.includes('cloud')) {
                            weatherIcon = '☁️';
                        } else if (condition.includes('sun') || condition.includes('clear')) {
                            weatherIcon = '☀️';
                        } else if (condition.includes('storm')) {
                            weatherIcon = '⛈️';
                        } else if (condition.includes('snow')) {
                            weatherIcon = '❄️';
                        } else if (condition.includes('fog') || condition.includes('mist')) {
                            weatherIcon = '🌫️';
                        }
                    }

                    const marker = new google.maps.Marker({
                        position: { lat: parseFloat(weather.latitude), lng: parseFloat(weather.longitude) },
                        map: map,
                        title: `${weather.station_name || weather.name || 'Weather Station'} - ${weather.temperature || '--'}°C`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                                    <circle cx="16" cy="16" r="15" fill="white" stroke="#007bff" stroke-width="2"/>
                                    <text x="16" y="20" text-anchor="middle" font-size="16">${weatherIcon}</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(32, 32)
                        }
                    });

                    // Add info window for weather data
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 15px; min-width: 250px; background: rgba(255, 255, 255, 0.95); border-radius: 10px;">
                                <h6 style="margin: 0 0 15px 0; color: #007bff; font-size: 16px; font-weight: 600;">
                                    ${weatherIcon} ${weather.station_name || weather.name || 'Weather Station'}
                                </h6>
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
                                    <div style="font-weight: 600;">Temperature:</div>
                                    <div style="color: #e74c3c;">${weather.temperature || '--'}°C</div>

                                    <div style="font-weight: 600;">Humidity:</div>
                                    <div style="color: #3498db;">${weather.humidity || '--'}%</div>

                                    <div style="font-weight: 600;">Pressure:</div>
                                    <div style="color: #9b59b6;">${weather.pressure || '--'} hPa</div>

                                    <div style="font-weight: 600;">Wind Speed:</div>
                                    <div style="color: #1abc9c;">${weather.wind_speed || '--'} m/s</div>

                                    <div style="font-weight: 600;">Wind Direction:</div>
                                    <div style="color: #1abc9c;">${weather.wind_direction || '--'}</div>

                                    <div style="font-weight: 600;">Condition:</div>
                                    <div style="color: #f39c12;">${weather.condition || 'Unknown'}</div>

                                    <div style="font-weight: 600;">Precipitation:</div>
                                    <div style="color: #3498db;">${weather.precipitation || '--'} mm</div>
                                </div>
                                <div style="margin-top: 12px; padding-top: 10px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
                                    <strong>Last Updated:</strong> ${weather.timestamp ? new Date(weather.timestamp).toLocaleString() : 'Unknown'}
                                </div>
                            </div>
                        `
                    });

                    // Add click listener for zoom functionality
                    marker.addListener('click', () => {
                        map.panTo(marker.getPosition());
                        map.setZoom(15);
                        infoWindow.open(map, marker);
                    });
                });
            } else {
                console.log('⚠️ No weather data available');
            }

            console.log('✅ Map created successfully!');
            updateMapStatus('Map created successfully!');

            // Hide loading indicator and show controls
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            // Show map controls and legend
            const mapControls = document.getElementById('map-controls');
            const mapLegend = document.getElementById('map-legend');
            if (mapControls) mapControls.style.display = 'block';
            if (mapLegend) mapLegend.style.display = 'block';

            // Store map reference globally
            window.dashboardMap = map;

            // Update legend counts
            updateLegendCounts();

            // Show data summary
            showDataSummary();

            // Load comprehensive data after a short delay
            setTimeout(() => {
                if (typeof loadComprehensiveMapData === 'function') {
                    loadComprehensiveMapData();
                }
                if (typeof setupMapEventListeners === 'function') {
                    setupMapEventListeners();
                }
            }, 1000);

        } catch (error) {
            console.error('❌ Error creating map:', error);
            updateMapStatus('Error: ' + error.message);
            if (loadingElement) {
                loadingElement.innerHTML = '<div style="color: red;">Error: ' + error.message + '</div>';
            }
        }
    };

    console.log('✅ initMap function defined globally');

    // Continue with the rest of the JavaScript in the same script block
    console.log('🚀 Enhanced Unified Dashboard with GPS System Starting...');

    // Layer visibility state
    const layerState = {
        farms: true,
        ponds: true,
        workers: true,
        aerators: true,
        geofences: false,
        weather: true,
        weatherAnimations: false,
        weatherNumbers: false
    };

    // Layer groups for map
    const layerGroups = {
        farms: null,
        ponds: null,
        workers: null,
        geofences: null
    };

    // Weather overlays storage
    let weatherOverlays = [];
    let weatherAnimations = [];
    let weatherNumbers = [];

    // Map data already initialized above - accessible in same scope
    console.log('📊 Using existing map data:', {
        farms: mapData.farms.length,
        ponds: mapData.ponds.length,
        workers: mapData.workers.length,
        aerators: mapData.aerators.length,
        weather: mapData.weather.length,
        geofences: mapData.geofences.length,
        center: [mapData.centerLat, mapData.centerLng]
    });

    let dashboardMap;
    let mapMarkers = [];
    let isFullscreen = false;

    // Make sure dashboardMap is also available globally
    window.dashboardMap = null;

    // Handle map loading errors and provide fallback
    function handleMapError() {
        console.error('Failed to load Google Maps API, using fallback map');
        const mapContainer = document.getElementById('dashboard-map');
        if (mapContainer) {
            // Create a beautiful fallback map with farm/pond locations
            mapContainer.innerHTML = `
                <div style="position: relative; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; overflow: hidden;">
                    <!-- Map Header -->
                    <div style="position: absolute; top: 0; left: 0; right: 0; background: rgba(255,255,255,0.95); padding: 15px; z-index: 10;">
                        <div style="display: flex; align-items: center; justify-content: between;">
                            <h4 style="margin: 0; color: #2d3436; display: flex; align-items: center;">
                                <i class="fas fa-map-marked-alt" style="margin-right: 10px; color: #667eea;"></i>
                                Farm & Pond Locations
                            </h4>
                            <button onclick="switchToInteractiveMap()" style="background: #667eea; color: white; border: none; padding: 8px 15px; border-radius: 20px; cursor: pointer; font-size: 0.85rem;">
                                <i class="fas fa-external-link-alt"></i> Interactive Map
                            </button>
                        </div>
                    </div>

                    <!-- Map Content -->
                    <div style="position: absolute; top: 70px; left: 0; right: 0; bottom: 0; padding: 20px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; height: 100%; overflow-y: auto;">
                            <!-- Farms Section -->
                            <div style="background: rgba(255,255,255,0.95); border-radius: 10px; padding: 20px;">
                                <h5 style="color: #667eea; margin-bottom: 15px; display: flex; align-items: center;">
                                    <i class="fas fa-building" style="margin-right: 8px;"></i>
                                    Farms ({{ total_farms|default:0 }})
                                </h5>
                                <div id="farms-list" style="space-y: 10px;">
                                    <!-- Farms will be populated here -->
                                </div>
                            </div>

                            <!-- Ponds Section -->
                            <div style="background: rgba(255,255,255,0.95); border-radius: 10px; padding: 20px;">
                                <h5 style="color: #20c997; margin-bottom: 15px; display: flex; align-items: center;">
                                    <i class="fas fa-water" style="margin-right: 8px;"></i>
                                    Ponds ({{ total_ponds|default:0 }})
                                </h5>
                                <div id="ponds-list" style="space-y: 10px;">
                                    <!-- Ponds will be populated here -->
                                </div>
                            </div>

                            <!-- Statistics Section -->
                            <div style="background: rgba(255,255,255,0.95); border-radius: 10px; padding: 20px;">
                                <h5 style="color: #fd7e14; margin-bottom: 15px; display: flex; align-items: center;">
                                    <i class="fas fa-chart-bar" style="margin-right: 8px;"></i>
                                    Quick Stats
                                </h5>
                                <div style="space-y: 10px;">
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                                        <span>Total Farms:</span>
                                        <strong>{{ total_farms|default:0 }}</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                                        <span>Total Ponds:</span>
                                        <strong>{{ total_ponds|default:0 }}</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee;">
                                        <span>Total Aerators:</span>
                                        <strong>{{ total_aerators|default:0 }}</strong>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; padding: 8px 0;">
                                        <span>Active Workers:</span>
                                        <strong>{{ total_workers|default:0 }}</strong>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div style="position: absolute; bottom: 20px; right: 20px; opacity: 0.1;">
                        <i class="fas fa-map" style="font-size: 4rem; color: white;"></i>
                    </div>
                </div>
            `;

            // Populate farms and ponds data
            populateFallbackMapData();
        }
    }

    // Populate fallback map with data
    function populateFallbackMapData() {
        // Populate farms
        const farmsList = document.getElementById('farms-list');
        if (farmsList) {
            {% if farms_data %}
            const farms = {{ farms_data|safe }};
            farmsList.innerHTML = farms.map(farm => `
                <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #667eea;">
                    <div style="font-weight: 600; color: #2d3436; margin-bottom: 4px;">${farm.name}</div>
                    <div style="font-size: 0.85rem; color: #636e72;">
                        📍 ${farm.location || 'Location not specified'}<br>
                        🦐 ${farm.pond_count || 0} ponds
                    </div>
                </div>
            `).join('');
            {% else %}
            farmsList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">No farms available</div>';
            {% endif %}
        }

        // Populate ponds
        const pondsList = document.getElementById('ponds-list');
        if (pondsList) {
            {% if ponds_data %}
            const ponds = {{ ponds_data|safe }};
            pondsList.innerHTML = ponds.map(pond => `
                <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; margin-bottom: 8px; border-left: 4px solid #20c997;">
                    <div style="font-weight: 600; color: #2d3436; margin-bottom: 4px;">${pond.name}</div>
                    <div style="font-size: 0.85rem; color: #636e72;">
                        🏢 ${pond.farm_name || 'No farm assigned'}<br>
                        📏 ${pond.size || 'Size not specified'}
                    </div>
                </div>
            `).join('');
            {% else %}
            pondsList.innerHTML = '<div style="text-align: center; color: #6c757d; padding: 20px;">No ponds available</div>';
            {% endif %}
        }
    }

    // Switch to interactive map
    function switchToInteractiveMap() {
        window.open('/ponds/multi-aerator-map/', '_blank');
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        updateStats();
        loadRealtimeData();
        startAutoRefresh();

        console.log('🚀 Document ready, checking Google Maps API...');
        updateMapStatus('Checking Google Maps API...');

        // Check if Google Maps API is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            console.log('✅ Google Maps API already loaded, initializing map...');
            updateMapStatus('Google Maps API ready, initializing...');
            window.initMap();
        } else {
            console.log('⏳ Google Maps API not loaded yet, waiting for callback...');
            updateMapStatus('Waiting for Google Maps API...');

            // Set a timeout to show error if Google Maps doesn't load
            setTimeout(() => {
                if (typeof google === 'undefined' || !google.maps) {
                    console.error('❌ Google Maps API failed to load within timeout');
                    updateMapStatus('Failed to load Google Maps API');
                    const loadingElement = document.getElementById('map-loading');
                    if (loadingElement) {
                        loadingElement.innerHTML = '<div style="color: red; text-align: center;"><i class="fas fa-exclamation-triangle"></i><br>Google Maps failed to load.<br>Please check your internet connection and refresh the page.</div>';
                    }
                }
            }, 15000); // 15 second timeout
        }
    });





    // Load comprehensive map data with all layers
    function loadComprehensiveMapData() {
        // Add farm markers
        mapData.farms.forEach((farm, index) => {
            if (farm.latitude && farm.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: farm.latitude, lng: farm.longitude },
                    map: dashboardMap,
                    title: farm.name || `Farm ${index + 1}`,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="#28a745" stroke="white" stroke-width="2"/>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">F</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h4>${farm.name || 'Farm'}</h4>
                            <p><strong>Location:</strong> ${farm.latitude}, ${farm.longitude}</p>
                            <p><strong>Ponds:</strong> ${farm.pond_count || 'N/A'}</p>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    // Zoom to farm location with smooth animation
                    dashboardMap.panTo(marker.getPosition());
                    dashboardMap.setZoom(18);

                    // Add bounce animation
                    marker.setAnimation(google.maps.Animation.BOUNCE);
                    setTimeout(() => {
                        marker.setAnimation(null);
                    }, 2000);

                    infoWindow.open(dashboardMap, marker);

                    // Show notification
                    showZoomNotification(`Zoomed to ${farm.name || 'Farm'}`, 'farm');
                });

                mapMarkers.push(marker);
            }
        });

        // Add pond markers with enhanced info
        mapData.ponds.forEach((pond, index) => {
            if (pond.latitude && pond.longitude) {
                // Color based on status
                const statusColors = {
                    'active': '#007bff',
                    'inactive': '#6c757d',
                    'maintenance': '#ffc107',
                    'harvesting': '#28a745'
                };

                const color = statusColors[pond.status] || '#007bff';

                const marker = new google.maps.Marker({
                    position: { lat: pond.latitude, lng: pond.longitude },
                    map: dashboardMap,
                    title: pond.name || `Pond ${index + 1}`,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                <!-- Background circle -->
                                <circle cx="20" cy="20" r="18" fill="white" stroke="${color}" stroke-width="3" opacity="0.95"/>

                                <!-- Green landscape base -->
                                <ellipse cx="20" cy="32" rx="15" ry="6" fill="#27ae60"/>
                                <ellipse cx="20" cy="30" rx="12" ry="4" fill="#2ecc71"/>

                                <!-- Water waves -->
                                <path d="M8 28 Q12 26, 16 28 Q20 30, 24 28 Q28 26, 32 28" stroke="#3498db" stroke-width="1.5" fill="none"/>
                                <path d="M8 30 Q12 28, 16 30 Q20 32, 24 30 Q28 28, 32 30" stroke="#5dade2" stroke-width="1" fill="none"/>

                                <!-- Main shrimp body (curved) -->
                                <path d="M8 20 Q10 15, 15 16 Q20 17, 25 18 Q30 19, 32 20 Q30 21, 25 22 Q20 23, 15 24 Q10 25, 8 20 Z" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>

                                <!-- Shrimp segments -->
                                <path d="M12 18 Q16 17, 20 18" stroke="#c0392b" stroke-width="0.8" fill="none"/>
                                <path d="M13 20 Q17 19, 21 20" stroke="#c0392b" stroke-width="0.8" fill="none"/>
                                <path d="M12 22 Q16 23, 20 22" stroke="#c0392b" stroke-width="0.8" fill="none"/>

                                <!-- Shrimp head -->
                                <ellipse cx="8.5" cy="20" rx="2.5" ry="3.5" fill="#e67e22" stroke="#d35400" stroke-width="0.8"/>

                                <!-- Eye -->
                                <circle cx="7.5" cy="18.5" r="1" fill="#2c3e50"/>
                                <circle cx="7.3" cy="18.2" r="0.3" fill="white"/>

                                <!-- Antennae -->
                                <path d="M6.5 17 Q4 15, 3 13" stroke="#d35400" stroke-width="1" fill="none"/>
                                <path d="M7 17.5 Q5 15.5, 4 13.5" stroke="#d35400" stroke-width="1" fill="none"/>

                                <!-- Tail -->
                                <path d="M32 20 Q34 18, 36 19 Q35 20, 36 21 Q34 22, 32 20" fill="#e67e22" stroke="#d35400" stroke-width="0.8"/>

                                <!-- Gear (technology symbol) -->
                                <circle cx="12" cy="12" r="3" fill="#f39c12" stroke="#e67e22" stroke-width="0.8"/>
                                <circle cx="12" cy="12" r="1.5" fill="none" stroke="#e67e22" stroke-width="0.6"/>
                                <rect x="10.5" y="9" width="3" height="1" fill="#e67e22"/>
                                <rect x="10.5" y="14" width="3" height="1" fill="#e67e22"/>
                                <rect x="9" y="11.5" width="1" height="1" fill="#e67e22"/>
                                <rect x="14" y="11.5" width="1" height="1" fill="#e67e22"/>

                                <!-- Circuit pattern -->
                                <path d="M4 8 L6 8 L6 6 L8 6" stroke="#f39c12" stroke-width="0.8" fill="none"/>
                                <circle cx="4" cy="8" r="0.5" fill="#f39c12"/>
                                <circle cx="6" cy="6" r="0.5" fill="#f39c12"/>
                                <circle cx="8" cy="6" r="0.5" fill="#f39c12"/>

                                <!-- Wind turbine -->
                                <line x1="28" y1="12" x2="28" y2="8" stroke="#ecf0f1" stroke-width="1"/>
                                <path d="M28 8 L26 6 L28 8 L30 6" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="0.5"/>
                                <circle cx="28" cy="8" r="0.8" fill="#95a5a6"/>

                                <!-- Tech circuit lines -->
                                <path d="M32 6 L34 6 L34 8 L36 8" stroke="#f39c12" stroke-width="0.6" fill="none"/>
                                <circle cx="32" cy="6" r="0.3" fill="#f39c12"/>
                                <circle cx="36" cy="8" r="0.3" fill="#f39c12"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(40, 40)
                    }
                });

                // Add combined info window with weather
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div id="pond-info-${index}" style="
                            padding: 15px;
                            background: rgba(255, 255, 255, 0.15);
                            border-radius: 15px;
                            backdrop-filter: blur(20px);
                            border: 1px solid rgba(255,255,255,0.2);
                            color: rgba(255, 255, 255, 0.95);
                            text-shadow: 0 1px 3px rgba(0,0,0,0.5);
                            font-size: 0.85rem;
                            line-height: 1.3;
                            min-width: 250px;
                            max-width: 280px;
                        ">
                            <!-- Pond Information Section -->
                            <div style="margin-bottom: 12px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 10px;">
                                <h4 style="margin: 0 0 8px 0; font-size: 1.1rem; font-weight: 600; color: rgba(255, 255, 255, 0.98); display: flex; align-items: center;">
                                    🦐 ${pond.name || 'Pond'}
                                </h4>
                                <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                    <span style="opacity: 0.8;">Farm:</span>
                                    <span style="font-weight: 600;">${pond.farm_name}</span>
                                </div>
                                <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                    <span style="opacity: 0.8;">Status:</span>
                                    <span style="font-weight: 600; color: ${pond.status === 'active' ? '#2ecc71' : pond.status === 'maintenance' ? '#f39c12' : '#e74c3c'};">${pond.status}</span>
                                </div>
                                <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                    <span style="opacity: 0.8;">Size:</span>
                                    <span style="font-weight: 600;">${pond.size} m²</span>
                                </div>
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    // Zoom to pond location with smooth animation
                    dashboardMap.panTo(marker.getPosition());
                    dashboardMap.setZoom(19); // Closer zoom for ponds

                    // Add bounce animation
                    marker.setAnimation(google.maps.Animation.BOUNCE);
                    setTimeout(() => {
                        marker.setAnimation(null);
                    }, 2000);

                    infoWindow.open(dashboardMap, marker);

                    // Show notification
                    showZoomNotification(`Zoomed to ${pond.name || 'Pond'}`, 'pond');
                });

                mapMarkers.push(marker);
            }
        });

        // Add worker GPS markers
        addWorkerGPSMarkers();

        // Add aerator markers
        addAeratorMarkers();

        // Add geofence markers
        addGeofenceMarkers();
    }

    // Add worker GPS markers
    function addWorkerGPSMarkers() {
        // Use real worker GPS data from backend
        const workers = mapData.workers;

        workers.forEach(worker => {
            const statusColors = {
                'working': '#00b894',
                'available': '#0984e3',
                'break': '#fdcb6e',
                'traveling': '#6c5ce7',
                'offline': '#636e72'
            };

            const marker = new google.maps.Marker({
                position: { lat: worker.latitude, lng: worker.longitude },
                map: dashboardMap,
                title: worker.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="12.5" cy="12.5" r="10" fill="${statusColors[worker.status] || '#636e72'}" stroke="white" stroke-width="2"/>
                            <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">👤</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(25, 25)
                }
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="padding: 10px;">
                        <h4>👤 ${worker.name}</h4>
                        <p><strong>Employee ID:</strong> ${worker.employee_id || 'N/A'}</p>
                        <p><strong>Status:</strong> ${worker.status}</p>
                        <p><strong>Department:</strong> ${worker.department || 'N/A'}</p>
                        <p><strong>Phone:</strong> ${worker.phone || 'N/A'}</p>
                        <p><strong>Location:</strong> ${worker.latitude.toFixed(4)}, ${worker.longitude.toFixed(4)}</p>
                        <p><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</p>
                    </div>
                `
            });

            marker.addListener('click', () => {
                // Zoom to worker location with smooth animation
                dashboardMap.panTo(marker.getPosition());
                dashboardMap.setZoom(17); // Medium zoom for workers

                // Add bounce animation
                marker.setAnimation(google.maps.Animation.BOUNCE);
                setTimeout(() => {
                    marker.setAnimation(null);
                }, 2000);

                infoWindow.open(dashboardMap, marker);

                // Show notification
                showZoomNotification(`Zoomed to ${worker.name || 'Worker'}`, 'worker');
            });

            mapMarkers.push(marker);
        });
    }

    // Add aerator markers
    function addAeratorMarkers() {
        // Use real aerator data from backend
        const aerators = mapData.aerators;

        aerators.forEach(aerator => {
            const statusColors = {
                'active': '#06b6d4',
                'maintenance': '#f59e0b',
                'offline': '#ef4444'
            };

            const marker = new google.maps.Marker({
                position: { lat: aerator.latitude, lng: aerator.longitude },
                map: dashboardMap,
                title: aerator.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="10" cy="10" r="8" fill="${statusColors[aerator.status] || '#06b6d4'}" stroke="white" stroke-width="2"/>
                            <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">⚡</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(20, 20)
                }
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="padding: 10px;">
                        <h4>⚡ ${aerator.name}</h4>
                        <p><strong>Pond:</strong> ${aerator.pond_name || 'N/A'}</p>
                        <p><strong>Type:</strong> ${aerator.aerator_type || 'N/A'}</p>
                        <p><strong>Status:</strong> ${aerator.status}</p>
                        <p><strong>Power Rating:</strong> ${aerator.power_rating || 'N/A'} W</p>
                        <p><strong>Efficiency:</strong> ${aerator.efficiency || 'N/A'}%</p>
                        <p><strong>Location:</strong> ${aerator.latitude.toFixed(4)}, ${aerator.longitude.toFixed(4)}</p>
                        <p><strong>Installed:</strong> ${aerator.installation_date ? new Date(aerator.installation_date).toLocaleDateString() : 'N/A'}</p>
                    </div>
                `
            });

            marker.addListener('click', () => {
                // Zoom to aerator location with smooth animation
                dashboardMap.panTo(marker.getPosition());
                dashboardMap.setZoom(20); // Very close zoom for aerators

                // Add bounce animation
                marker.setAnimation(google.maps.Animation.BOUNCE);
                setTimeout(() => {
                    marker.setAnimation(null);
                }, 2000);

                infoWindow.open(dashboardMap, marker);

                // Show notification
                showZoomNotification(`Zoomed to ${aerator.name || 'Aerator'}`, 'aerator');
            });

            mapMarkers.push(marker);
        });
    }

    // Add geofence markers
    function addGeofenceMarkers() {
        // Use real geofence data from backend
        const geofences = mapData.geofences;

        geofences.forEach(geofence => {
            if (geofence.center_latitude && geofence.center_longitude) {
                // Create geofence circle or polygon
                if (geofence.shape_type === 'circle' && geofence.radius) {
                    const circle = new google.maps.Circle({
                        strokeColor: '#FF0000',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: '#FF0000',
                        fillOpacity: 0.15,
                        map: dashboardMap,
                        center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                        radius: geofence.radius
                    });

                    // Add info window for geofence
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>🚧 ${geofence.name}</h4>
                                <p><strong>Type:</strong> ${geofence.geofence_type || 'General'}</p>
                                <p><strong>Shape:</strong> Circle</p>
                                <p><strong>Radius:</strong> ${geofence.radius} meters</p>
                                <p><strong>Center:</strong> ${geofence.center_latitude.toFixed(4)}, ${geofence.center_longitude.toFixed(4)}</p>
                                <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                            </div>
                        `
                    });

                    circle.addListener('click', () => {
                        infoWindow.setPosition({ lat: geofence.center_latitude, lng: geofence.center_longitude });
                        infoWindow.open(dashboardMap);
                    });

                } else if (geofence.shape_type === 'polygon' && geofence.boundary) {
                    // Handle polygon geofences
                    try {
                        const coordinates = geofence.boundary.map(coord => ({
                            lat: coord.lat || coord.latitude,
                            lng: coord.lng || coord.longitude
                        }));

                        const polygon = new google.maps.Polygon({
                            paths: coordinates,
                            strokeColor: '#FF0000',
                            strokeOpacity: 0.8,
                            strokeWeight: 2,
                            fillColor: '#FF0000',
                            fillOpacity: 0.15,
                            map: dashboardMap
                        });

                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 10px;">
                                    <h4>🚧 ${geofence.name}</h4>
                                    <p><strong>Type:</strong> ${geofence.geofence_type || 'General'}</p>
                                    <p><strong>Shape:</strong> Polygon</p>
                                    <p><strong>Points:</strong> ${coordinates.length}</p>
                                    <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                                </div>
                            `
                        });

                        polygon.addListener('click', (event) => {
                            infoWindow.setPosition(event.latLng);
                            infoWindow.open(dashboardMap);
                        });

                    } catch (error) {
                        console.error('Error creating polygon geofence:', error);
                    }
                }
            }
        });
    }

    // Setup map event listeners
    function setupMapEventListeners() {
        // Layer control buttons
        document.getElementById('farms-layer').addEventListener('click', function() {
            toggleLayer('farms', this);
        });

        document.getElementById('ponds-layer').addEventListener('click', function() {
            toggleLayer('ponds', this);
        });

        document.getElementById('workers-layer').addEventListener('click', function() {
            toggleLayer('workers', this);
        });

        document.getElementById('aerators-layer').addEventListener('click', function() {
            toggleLayer('aerators', this);
        });

        document.getElementById('geofences-layer').addEventListener('click', function() {
            toggleLayer('geofences', this);
        });

        // Map control buttons
        document.getElementById('center-map').addEventListener('click', centerMap);
        document.getElementById('refresh-data').addEventListener('click', refreshData);
        document.getElementById('fit-all').addEventListener('click', fitAllMarkers);
    }

    // Toggle layer visibility
    function toggleLayer(layerName, button) {
        layerState[layerName] = !layerState[layerName];

        if (layerState[layerName]) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        // Filter markers based on layer state
        mapMarkers.forEach(marker => {
            const title = marker.getTitle().toLowerCase();
            let shouldShow = true;

            if (title.includes('farm') && !layerState.farms) shouldShow = false;
            if (title.includes('pond') && !layerState.ponds) shouldShow = false;
            if (title.includes('worker') && !layerState.workers) shouldShow = false;
            if (title.includes('aerator') && !layerState.aerators) shouldShow = false;

            marker.setVisible(shouldShow);
        });

        console.log(`🔄 Layer ${layerName} ${layerState[layerName] ? 'enabled' : 'disabled'}`);
    }

    // Center map
    function centerMap() {
        if (window.dashboardMap) {
            window.dashboardMap.setCenter({ lat: mapData.centerLat, lng: mapData.centerLng });
            window.dashboardMap.setZoom(12);
            showZoomNotification('Map centered', 'info');
        }
    }

    // Update legend counts
    function updateLegendCounts() {
        document.getElementById('farms-count').textContent = mapData.farms ? mapData.farms.length : 0;
        document.getElementById('ponds-count').textContent = mapData.ponds ? mapData.ponds.length : 0;
        document.getElementById('workers-count').textContent = mapData.workers ? mapData.workers.length : 0;
        document.getElementById('aerators-count').textContent = mapData.aerators ? mapData.aerators.length : 0;
        document.getElementById('weather-count').textContent = mapData.weather ? mapData.weather.length : 0;
    }

    // Show data summary
    function showDataSummary() {
        console.log('📊 DATA SUMMARY:');
        console.log('================');
        console.log(`🏭 Farms: ${mapData.farms ? mapData.farms.length : 0}`);
        console.log(`🏊 Ponds: ${mapData.ponds ? mapData.ponds.length : 0}`);
        console.log(`👷 Workers: ${mapData.workers ? mapData.workers.length : 0}`);
        console.log(`⚙️ Aerators: ${mapData.aerators ? mapData.aerators.length : 0}`);
        console.log(`🌤️ Weather Stations: ${mapData.weather ? mapData.weather.length : 0}`);
        console.log(`🚧 Geofences: ${mapData.geofences ? mapData.geofences.length : 0}`);
        console.log('================');

        if (mapData.farms && mapData.farms.length > 0) {
            console.log('Sample farm:', mapData.farms[0]);
        }
        if (mapData.ponds && mapData.ponds.length > 0) {
            console.log('Sample pond:', mapData.ponds[0]);
        }
        if (mapData.workers && mapData.workers.length > 0) {
            console.log('Sample worker:', mapData.workers[0]);
        }
    }

    // Refresh map data
    function refreshMapData() {
        console.log('🔄 Refreshing map data...');
        showZoomNotification('Refreshing map data...', 'info');

        // Reload the page to get fresh data
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }

    // Refresh data
    function refreshData() {
        console.log('🔄 Refreshing map data...');
        loadRealtimeData();

        // Show refresh animation
        const refreshBtn = document.getElementById('refresh-data');
        const icon = refreshBtn.querySelector('i');
        icon.classList.add('fa-spin');
        setTimeout(() => {
            icon.classList.remove('fa-spin');
        }, 1000);
    }

    // Fit all markers
    function fitAllMarkers() {
        if (window.dashboardMap) {
            const bounds = new google.maps.LatLngBounds();
            let hasMarkers = false;

            // Add all data points to bounds
            if (mapData.farms) {
                mapData.farms.forEach(farm => {
                    bounds.extend(new google.maps.LatLng(farm.latitude, farm.longitude));
                    hasMarkers = true;
                });
            }
            if (mapData.ponds) {
                mapData.ponds.forEach(pond => {
                    bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
                    hasMarkers = true;
                });
            }
            if (mapData.workers) {
                mapData.workers.forEach(worker => {
                    bounds.extend(new google.maps.LatLng(worker.latitude, worker.longitude));
                    hasMarkers = true;
                });
            }
            if (mapData.aerators) {
                mapData.aerators.forEach(aerator => {
                    bounds.extend(new google.maps.LatLng(aerator.latitude, aerator.longitude));
                    hasMarkers = true;
                });
            }
            if (mapData.weather) {
                mapData.weather.forEach(weather => {
                    bounds.extend(new google.maps.LatLng(weather.latitude, weather.longitude));
                    hasMarkers = true;
                });
            }

            if (hasMarkers) {
                window.dashboardMap.fitBounds(bounds);
                showZoomNotification('Fitted all markers to view', 'info');
            } else {
                showZoomNotification('No markers to fit', 'info');
            }
        }
    }

    // Toggle weather layer
    function toggleWeatherLayer() {
        layerState.weather = !layerState.weather;
        console.log(`🌤️ Weather layer ${layerState.weather ? 'enabled' : 'disabled'}`);
        // Weather layer implementation would go here
    }

    // Update statistics
    function updateStats() {
        document.getElementById('farms-count').textContent = mapData.farms.length;
        document.getElementById('ponds-count').textContent = mapData.ponds.length;
        document.getElementById('workers-count').textContent = mapData.workers.length;
        document.getElementById('aerators-count').textContent = mapData.aerators.length;
    }

    // Load real-time data
    function loadRealtimeData() {
        // Simulate real-time data for now
        const mockData = {
            statistics: {
                total_farms: {{ total_farms|default:0 }},
                total_ponds: {{ total_ponds|default:0 }},
                total_aerators: {{ total_aerators|default:0 }},
                total_workers: {{ total_workers|default:0 }}
            },
            activities: [
                {
                    icon: 'fa-plus-circle',
                    title: 'New pond created in Farm A',
                    time_ago: '2 minutes'
                },
                {
                    icon: 'fa-thermometer-half',
                    title: 'Water temperature alert in Pond 3',
                    time_ago: '15 minutes'
                },
                {
                    icon: 'fa-user-plus',
                    title: 'New worker assigned to Farm B',
                    time_ago: '1 hour'
                }
            ],
            weather: {
                temperature: 28,
                description: 'Partly cloudy',
                humidity: 75,
                wind_speed: 12
            },
            alerts: []
        };

        updateStatistics(mockData.statistics);
        updateActivities(mockData.activities);
        updateWeather(mockData.weather);
        updateAlerts(mockData.alerts);
    }

    // Update functions
    function updateStatistics(stats) {
        if (stats) {
            document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = stats.total_farms || 0;
            document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = stats.total_ponds || 0;
            document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = stats.total_aerators || 0;
            document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = stats.total_workers || 0;
        }
    }

    function updateActivities(activities) {
        const container = document.querySelector('.recent-activities');
        if (activities && activities.length > 0) {
            container.innerHTML = activities.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="fas ${activity.icon}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-time">${activity.time_ago} ago</div>
                    </div>
                </div>
            `).join('');
        }
    }

    function updateWeather(weather) {
        if (weather) {
            document.querySelector('.weather-temp').textContent = `${weather.temperature || '--'}°C`;
            document.querySelector('.weather-desc').textContent = weather.description || 'No data';
        }
    }

    function updateAlerts(alerts) {
        // Alerts are handled in template for now
    }

    // Auto refresh
    function startAutoRefresh() {
        setInterval(loadRealtimeData, 30000); // Refresh every 30 seconds
    }

    // Dashboard functions
    function refreshDashboard() {
        loadRealtimeData();

        // Show refresh animation
        const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"] i');
        refreshBtn.classList.add('fa-spin');
        setTimeout(() => {
            refreshBtn.classList.remove('fa-spin');
        }, 1000);
    }

    function exportReport() {
        // Create a simple report export
        const reportData = {
            timestamp: new Date().toISOString(),
            farms: {{ total_farms|default:0 }},
            ponds: {{ total_ponds|default:0 }},
            aerators: {{ total_aerators|default:0 }},
            workers: {{ total_workers|default:0 }}
        };

        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(reportData, null, 2));
        const downloadAnchorNode = document.createElement('a');
        downloadAnchorNode.setAttribute("href", dataStr);
        downloadAnchorNode.setAttribute("download", "dashboard_report.json");
        document.body.appendChild(downloadAnchorNode);
        downloadAnchorNode.click();
        downloadAnchorNode.remove();
    }

    function toggleMapView() {
        if (dashboardMap) {
            const currentZoom = dashboardMap.getZoom();
            if (currentZoom < 12) {
                dashboardMap.setZoom(15);
            } else {
                dashboardMap.setZoom(10);
            }
        }
    }

    function fullscreenMap() {
        const mapContainer = document.getElementById('dashboard-map');
        if (!mapContainer || !dashboardMap) return;

        if (!isFullscreen) {
            mapContainer.style.position = 'fixed';
            mapContainer.style.top = '0';
            mapContainer.style.left = '0';
            mapContainer.style.width = '100vw';
            mapContainer.style.height = '100vh';
            mapContainer.style.zIndex = '9999';
            mapContainer.style.background = 'white';
            isFullscreen = true;

            // Add close button
            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '<i class="fas fa-times"></i> Close Fullscreen';
            closeBtn.style.position = 'absolute';
            closeBtn.style.top = '20px';
            closeBtn.style.right = '20px';
            closeBtn.style.zIndex = '10000';
            closeBtn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            closeBtn.style.color = 'white';
            closeBtn.style.border = 'none';
            closeBtn.style.padding = '12px 24px';
            closeBtn.style.borderRadius = '25px';
            closeBtn.style.cursor = 'pointer';
            closeBtn.style.fontWeight = '600';
            closeBtn.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.4)';
            closeBtn.onclick = fullscreenMap;
            mapContainer.appendChild(closeBtn);

            google.maps.event.trigger(dashboardMap, 'resize');
        } else {
            mapContainer.style.position = 'relative';
            mapContainer.style.top = 'auto';
            mapContainer.style.left = 'auto';
            mapContainer.style.width = '100%';
            mapContainer.style.height = '600px';
            mapContainer.style.zIndex = 'auto';
            isFullscreen = false;

            // Remove close button
            const closeBtn = mapContainer.querySelector('button');
            if (closeBtn) {
                closeBtn.remove();
            }

            google.maps.event.trigger(dashboardMap, 'resize');
        }
    }

    function viewAllActivities() {
        window.location.href = '/ponds/pond_list/';
    }

    // Additional utility functions for enhanced GPS system
    function getCurrentLocation() {
        if (navigator.geolocation) {
            showZoomNotification('Getting your location...', 'info');

            navigator.geolocation.getCurrentPosition(function(position) {
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };

                if (window.dashboardMap) {
                    window.dashboardMap.setCenter(userLocation);
                    window.dashboardMap.setZoom(15);

                    // Remove previous user location marker if exists
                    if (window.userLocationMarker) {
                        window.userLocationMarker.setMap(null);
                    }

                    // Add user location marker
                    window.userLocationMarker = new google.maps.Marker({
                        position: userLocation,
                        map: window.dashboardMap,
                        title: 'Your Current Location',
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12.5" cy="12.5" r="10" fill="#e74c3c" stroke="white" stroke-width="2"/>
                                    <circle cx="12.5" cy="12.5" r="4" fill="white"/>
                                    <circle cx="12.5" cy="12.5" r="2" fill="#e74c3c"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });

                    // Add info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>📍 Your Location</h4>
                                <p><strong>Coordinates:</strong> ${userLocation.lat.toFixed(6)}, ${userLocation.lng.toFixed(6)}</p>
                                <p><strong>Accuracy:</strong> ±${position.coords.accuracy.toFixed(0)} meters</p>
                            </div>
                        `
                    });

                    window.userLocationMarker.addListener('click', () => {
                        infoWindow.open(window.dashboardMap, window.userLocationMarker);
                    });

                    showZoomNotification('Location found and centered', 'info');
                }
            }, function(error) {
                let errorMessage = 'Unable to get your location';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage = 'Location access denied by user';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage = 'Location information unavailable';
                        break;
                    case error.TIMEOUT:
                        errorMessage = 'Location request timed out';
                        break;
                }
                showZoomNotification(errorMessage, 'info');
                console.error('Geolocation error:', error);
            });
        } else {
            showZoomNotification('Geolocation not supported by browser', 'info');
        }
    }

    // Initialize enhanced features
    function initializeEnhancedFeatures() {
        // Add current location button to map controls
        const locationBtn = document.createElement('button');
        locationBtn.innerHTML = '<i class="fas fa-location-arrow"></i> MY LOCATION';
        locationBtn.className = 'gps-map-btn';
        locationBtn.onclick = getCurrentLocation;

        const mapControls = document.querySelector('.gps-map-controls');
        if (mapControls) {
            mapControls.appendChild(locationBtn);
        }
    }

    // Show zoom notification function
    function showZoomNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `zoom-notification zoom-notification-${type}`;

        // Set icon based on type
        const icons = {
            'farm': 'fas fa-warehouse',
            'pond': 'fas fa-water',
            'worker': 'fas fa-user',
            'aerator': 'fas fa-fan',
            'info': 'fas fa-info-circle'
        };

        notification.innerHTML = `
            <i class="${icons[type] || icons.info}"></i>
            ${message}
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            font-size: 0.9rem;
            animation: slideInRight 0.4s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.4s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 400);
        }, 3000);
    }

    // Call enhanced features after map initialization
    setTimeout(() => {
        if (dashboardMap) {
            initializeEnhancedFeatures();
        }
    }, 3000);
</script>

<!-- Load Google Maps API after all JavaScript is defined -->
<script async defer src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&libraries=places&callback=initMap&v=weekly"></script>
{% endblock %}
