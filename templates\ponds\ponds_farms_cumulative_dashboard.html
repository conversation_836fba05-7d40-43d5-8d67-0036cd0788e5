{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Shrimp Farm GPS System{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .dashboard-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .system-title {
        text-align: center;
        color: white;
        font-size: 2.5rem;
        font-weight: 300;
        margin-bottom: 30px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .system-title i {
        margin-right: 15px;
        opacity: 0.9;
    }

    /* Stats Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px 25px;
        text-align: center;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.3);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        opacity: 0.9;
    }

    .stat-value {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .stat-label {
        font-size: 1.1rem;
        font-weight: 500;
        opacity: 0.9;
    }

    /* Control Panels */
    .control-panel {
        background: rgba(45, 55, 72, 0.95);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .panel-title {
        color: #a0aec0;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .panel-title i {
        font-size: 1rem;
    }

    /* Worker Status Legend */
    .status-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 25px;
    }

    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #e2e8f0;
        font-size: 0.9rem;
    }

    .status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
    }

    .status-working { background-color: #ef4444; }
    .status-available { background-color: #10b981; }
    .status-break { background-color: #f59e0b; }
    .status-traveling { background-color: #8b5cf6; }
    .status-offline { background-color: #6b7280; }

    /* Layer Controls */
    .layer-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 25px;
    }

    .layer-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .layer-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .layer-btn.active {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .layer-btn.ponds { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    .layer-btn.workers { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
    .layer-btn.geofences { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }

    /* Map Controls */
    .map-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .map-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .map-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .map-btn.refresh { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
    .map-btn.fit { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }

    /* Map Container */
    .map-container {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        position: relative;
    }

    .map-header {
        background: rgba(45, 55, 72, 0.95);
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .map-tabs {
        display: flex;
        gap: 10px;
    }

    .map-tab {
        background: rgba(255,255,255,0.1);
        color: #e2e8f0;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .map-tab.active {
        background: white;
        color: #2d3748;
    }

    .fullscreen-btn {
        background: rgba(255,255,255,0.1);
        color: #e2e8f0;
        border: none;
        border-radius: 8px;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .fullscreen-btn:hover {
        background: rgba(255,255,255,0.2);
    }

    #map {
        width: 100%;
        height: 500px;
    }

    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #666;
        height: 500px;
    }

    .loading::before {
        content: "🗺️ ";
        animation: spin 2s linear infinite;
        margin-right: 10px;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .layer-controls,
        .map-controls {
            justify-content: center;
        }

        .system-title {
            font-size: 2rem;
        }

        .stat-card {
            padding: 20px 15px;
        }

        .stat-value {
            font-size: 2.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- System Title -->
    <h1 class="system-title">
        <i class="fas fa-map-marked-alt"></i>
        Enhanced Shrimp Farm GPS System
    </h1>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="stat-value" id="farms-count">0</div>
            <div class="stat-label">Active Farms</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-water"></i>
            </div>
            <div class="stat-value" id="ponds-count">0</div>
            <div class="stat-label">Monitored Ponds</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-value" id="workers-count">0</div>
            <div class="stat-label">GPS Workers</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="stat-value" id="security-zones">0</div>
            <div class="stat-label">Security Zones</div>
        </div>
    </div>

    <!-- Worker Status Legend -->
    <div class="control-panel">
        <div class="panel-title">
            <i class="fas fa-circle"></i>
            WORKER STATUS LEGEND
        </div>
        <div class="status-legend">
            <div class="status-item">
                <div class="status-dot status-working"></div>
                <span>Working</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-available"></div>
                <span>Available</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-break"></div>
                <span>On Break</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-traveling"></div>
                <span>Traveling</span>
            </div>
            <div class="status-item">
                <div class="status-dot status-offline"></div>
                <span>Offline</span>
            </div>
        </div>

        <!-- Layer Controls -->
        <div class="panel-title">
            <i class="fas fa-layer-group"></i>
            LAYER CONTROLS
        </div>
        <div class="layer-controls">
            <button class="layer-btn active" id="farms-layer">
                <i class="fas fa-warehouse"></i>
                FARMS
            </button>
            <button class="layer-btn ponds active" id="ponds-layer">
                <i class="fas fa-water"></i>
                PONDS
            </button>
            <button class="layer-btn workers active" id="workers-layer">
                <i class="fas fa-users"></i>
                WORKERS
            </button>
            <button class="layer-btn geofences" id="geofences-layer">
                <i class="fas fa-draw-polygon"></i>
                GEOFENCES
            </button>
        </div>

        <!-- Map Controls -->
        <div class="panel-title">
            <i class="fas fa-cog"></i>
            MAP CONTROLS
        </div>
        <div class="map-controls">
            <button class="map-btn" id="center-map">
                <i class="fas fa-crosshairs"></i>
                CENTER MAP
            </button>
            <button class="map-btn refresh" id="refresh-data">
                <i class="fas fa-sync-alt"></i>
                REFRESH
            </button>
            <button class="map-btn fit" id="fit-all">
                <i class="fas fa-expand-arrows-alt"></i>
                FIT ALL
            </button>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div class="map-header">
            <div class="map-tabs">
                <button class="map-tab active">Map</button>
                <button class="map-tab">Satellite</button>
            </div>
            <button class="fullscreen-btn">
                <i class="fas fa-expand"></i>
            </button>
        </div>
        <div id="map" class="loading">Loading interactive map...</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log('🚀 Enhanced Shrimp Farm GPS System Starting...');

    // Layer visibility state
    const layerState = {
        farms: true,
        ponds: true,
        workers: true,
        geofences: false
    };

    // Layer groups for map
    const layerGroups = {
        farms: null,
        ponds: null,
        workers: null,
        geofences: null
    };

    // Step 1: Initialize all data first
    const mapData = {
        farms: {{ farms_data|default:"[]"|safe }},
        ponds: {{ ponds_data|default:"[]"|safe }},
        workers: {{ workers_data|default:"[]"|safe }},
        weather: {{ weather_data|default:"[]"|safe }},
        centerLat: {{ center_lat|default:"13.0827" }},
        centerLng: {{ center_lng|default:"80.2707" }},
        apiKey: "{{ google_maps_api_key|default:"" }}"
    };
      console.log('📊 Map Data Loaded:', {
        farms: mapData.farms.length,
        ponds: mapData.ponds.length,
        workers: mapData.workers.length,
        weather: mapData.weather.length,
        center: [mapData.centerLat, mapData.centerLng]
    });
    
    // Debug: Log the actual data
    console.log('🔍 DEBUG - Farms Data:', mapData.farms);
    console.log('🔍 DEBUG - Ponds Data:', mapData.ponds);
    console.log('🔍 DEBUG - Workers Data:', mapData.workers);
    
    if (mapData.farms.length === 0) {
        console.warn('⚠️ No farms data found!');
    }
    if (mapData.ponds.length === 0) {
        console.warn('⚠️ No ponds data found!');
    }
      // Step 2: Update info display
    document.addEventListener('DOMContentLoaded', function() {
        updateStats();
    });
    
    // Step 3: Define global variables for Google Maps
    let map;
    let markers = [];
    
    // Step 4: Define the global initMap function
    function initMap() {
        console.log('🗺️ Google Maps API loaded! Initializing ponds & farms map...');
        
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }
          try {
            // Calculate optimal map center and bounds based on pond and farm locations
            let mapCenter = { lat: mapData.centerLat, lng: mapData.centerLng };
            let shouldFitBounds = false;
            const bounds = new google.maps.LatLngBounds();
            
            // If we have pond data, prioritize pond locations for centering
            if (mapData.ponds.length > 0) {
                console.log('🎯 Including pond locations in map bounds...');
                mapData.ponds.forEach(pond => {
                    if (pond.latitude && pond.longitude) {
                        bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
                        shouldFitBounds = true;
                    }
                });
            }
            
            // Also include farm locations
            if (mapData.farms.length > 0) {
                console.log('🎯 Including farm locations in map bounds...');
                mapData.farms.forEach(farm => {
                    if (farm.latitude && farm.longitude) {
                        bounds.extend(new google.maps.LatLng(farm.latitude, farm.longitude));
                        shouldFitBounds = true;
                    }
                });
            }
            
            // Calculate center of all locations
            const allLocations = [...mapData.ponds, ...mapData.farms].filter(item => item.latitude && item.longitude);
            if (allLocations.length > 0) {
                const avgLat = allLocations.reduce((sum, item) => sum + item.latitude, 0) / allLocations.length;
                const avgLng = allLocations.reduce((sum, item) => sum + item.longitude, 0) / allLocations.length;
                mapCenter = { lat: avgLat, lng: avgLng };
                
                console.log(`📍 Combined center: ${avgLat}, ${avgLng} with ${allLocations.length} locations`);
            }
            
            // Create the map with combined center
            map = new google.maps.Map(mapElement, {
                zoom: shouldFitBounds ? 10 : 12, // Will be adjusted by fitBounds if needed
                center: mapCenter,
                mapTypeId: 'roadmap'
            });
            
            console.log('✅ Map created successfully with ponds & farms center!');
            mapElement.classList.remove('loading');
            
            // Add farm markers
            mapData.farms.forEach((farm, index) => {
                if (farm.latitude && farm.longitude) {
                    const marker = new google.maps.Marker({
                        position: { lat: farm.latitude, lng: farm.longitude },
                        map: map,
                        title: farm.name || `Farm ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#28a745" stroke="white" stroke-width="2"/>
                                    <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">F</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });
                    
                    // Add info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${farm.name || 'Farm'}</h4>
                                <p><strong>Location:</strong> ${farm.latitude}, ${farm.longitude}</p>
                                <p><strong>Ponds:</strong> ${farm.pond_count || 'N/A'}</p>
                            </div>
                        `
                    });
                    
                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                    
                    markers.push(marker);
                }
            });
              // Add pond markers
            mapData.ponds.forEach((pond, index) => {
                if (pond.latitude && pond.longitude) {
                    // Color based on status
                    const statusColors = {
                        'active': '#007bff',
                        'inactive': '#6c757d',
                        'maintenance': '#ffc107',
                        'harvesting': '#28a745'
                    };

                    const marker = new google.maps.Marker({
                        position: { lat: pond.latitude, lng: pond.longitude },
                        map: map,
                        title: pond.name || `Pond ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12.5" cy="12.5" r="10" fill="${statusColors[pond.status] || '#007bff'}" stroke="white" stroke-width="2"/>
                                    <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">P</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });

                    // Add info window for pond
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${pond.name || 'Pond'}</h4>
                                <p><strong>Location:</strong> ${pond.latitude}, ${pond.longitude}</p>
                                <p><strong>Farm:</strong> ${pond.farm_name || 'Unknown'}</p>
                                <p><strong>Status:</strong> <span style="color: ${statusColors[pond.status] || '#007bff'};">${pond.status}</span></p>
                                <p><strong>Size:</strong> ${pond.size || 'N/A'} m²</p>
                                <p><strong>Water Quality:</strong> ${pond.water_quality || 'N/A'}</p>
                                <p><strong>Species:</strong> ${pond.species || 'N/A'}</p>
                                <p><strong>Occupancy:</strong> ${pond.occupancy || 0}%</p>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });

            // Add worker markers
            mapData.workers.forEach((worker, index) => {
                if (worker.latitude && worker.longitude) {
                    // Color based on status
                    const statusColors = {
                        'available': '#28a745',
                        'working': '#007bff',
                        'break': '#ffc107',
                        'offline': '#6c757d'
                    };

                    const marker = new google.maps.Marker({
                        position: { lat: worker.latitude, lng: worker.longitude },
                        map: map,
                        title: worker.name || `Worker ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="10" cy="10" r="8" fill="${statusColors[worker.status] || '#28a745'}" stroke="white" stroke-width="2"/>
                                    <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">W</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(20, 20)
                        }
                    });

                    // Add info window for worker
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${worker.name || 'Worker'}</h4>
                                <p><strong>Location:</strong> ${worker.latitude}, ${worker.longitude}</p>
                                <p><strong>Status:</strong> <span style="color: ${statusColors[worker.status] || '#28a745'};">${worker.status}</span></p>
                                <p><strong>Team:</strong> ${worker.team || 'No Team'}</p>
                                <p><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</p>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });
            
            console.log(`✅ Added ${markers.length} markers to the map`);

            // Initialize layer groups
            layerGroups.farms = new google.maps.LatLngBounds();
            layerGroups.ponds = new google.maps.LatLngBounds();
            layerGroups.workers = new google.maps.LatLngBounds();

            // Auto-fit map to show all locations
            if (shouldFitBounds && bounds && !bounds.isEmpty()) {
                console.log('🔍 Auto-fitting map bounds to show all pond & farm locations...');
                map.fitBounds(bounds);

                // Add some padding to the bounds
                setTimeout(() => {
                    const currentZoom = map.getZoom();
                    if (currentZoom > 15) {
                        map.setZoom(15); // Don't zoom in too much
                    } else if (currentZoom < 10) {
                        map.setZoom(10); // Don't zoom out too much
                    }
                    console.log(`🎯 Final map zoom level: ${map.getZoom()}`);
                }, 100);
            }

            // Initialize interactive controls
            initializeControls();
            
        } catch (error) {
            console.error('❌ Error creating map:', error);
            handleMapError();
        }
    }

    // Step 5: Initialize interactive controls
    function initializeControls() {
        console.log('🎮 Initializing interactive controls...');

        // Layer toggle buttons
        document.getElementById('farms-layer').addEventListener('click', function() {
            toggleLayer('farms', this);
        });

        document.getElementById('ponds-layer').addEventListener('click', function() {
            toggleLayer('ponds', this);
        });

        document.getElementById('workers-layer').addEventListener('click', function() {
            toggleLayer('workers', this);
        });

        document.getElementById('geofences-layer').addEventListener('click', function() {
            toggleLayer('geofences', this);
        });

        // Map control buttons
        document.getElementById('center-map').addEventListener('click', centerMap);
        document.getElementById('refresh-data').addEventListener('click', refreshData);
        document.getElementById('fit-all').addEventListener('click', fitAllMarkers);

        // Map type toggle
        const mapTabs = document.querySelectorAll('.map-tab');
        mapTabs.forEach((tab, index) => {
            tab.addEventListener('click', function() {
                mapTabs.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                if (index === 0) {
                    map.setMapTypeId('roadmap');
                } else {
                    map.setMapTypeId('satellite');
                }
            });
        });

        // Fullscreen toggle
        document.querySelector('.fullscreen-btn').addEventListener('click', toggleFullscreen);

        console.log('✅ Interactive controls initialized');
    }

    // Layer toggle function
    function toggleLayer(layerType, button) {
        layerState[layerType] = !layerState[layerType];

        if (layerState[layerType]) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        // Filter markers based on layer state
        markers.forEach(marker => {
            const title = marker.getTitle().toLowerCase();
            let shouldShow = false;

            if (layerState.farms && title.includes('farm')) shouldShow = true;
            if (layerState.ponds && title.includes('pond')) shouldShow = true;
            if (layerState.workers && title.includes('worker')) shouldShow = true;

            marker.setVisible(shouldShow);
        });

        console.log(`🔄 Toggled ${layerType} layer: ${layerState[layerType] ? 'ON' : 'OFF'}`);
    }

    // Map control functions
    function centerMap() {
        if (map && mapData.center) {
            map.setCenter(mapData.center);
            map.setZoom(12);
            console.log('🎯 Map centered');
        }
    }

    function refreshData() {
        console.log('🔄 Refreshing data...');
        const refreshBtn = document.getElementById('refresh-data');
        const icon = refreshBtn.querySelector('i');

        icon.style.animation = 'spin 1s linear infinite';

        setTimeout(() => {
            // Update stats
            updateStats();
            icon.style.animation = '';
            console.log('✅ Data refreshed');
        }, 1000);
    }

    function fitAllMarkers() {
        if (map && bounds && !bounds.isEmpty()) {
            map.fitBounds(bounds);
            console.log('📏 Fitted all markers');
        }
    }

    function toggleFullscreen() {
        const mapContainer = document.querySelector('.map-container');

        if (!document.fullscreenElement) {
            mapContainer.requestFullscreen().catch(err => {
                console.log('Fullscreen not supported');
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Update stats function
    function updateStats() {
        document.getElementById('farms-count').textContent = mapData.farms.length;
        document.getElementById('ponds-count').textContent = mapData.ponds.length;
        document.getElementById('workers-count').textContent = mapData.workers.length;
        document.getElementById('security-zones').textContent = '0'; // Placeholder
    }

    // Step 6: Define error handler
    function handleMapError() {
        console.error('❌ Failed to load Google Maps');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.classList.remove('loading');
            mapElement.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i><br>
                    Failed to load Ponds & Farms Google Maps<br>
                    <small>Please check your internet connection and refresh the page</small>
                </div>
            `;
        }
    }
    
    // Step 6: Make functions globally accessible
    window.initMap = initMap;
    window.handleMapError = handleMapError;
    
    console.log('✅ Ponds & Farms Dashboard setup complete');
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
