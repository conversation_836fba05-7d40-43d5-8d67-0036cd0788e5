/**
 * Task Detail Screen
 * Shows detailed information about a specific task with actions
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Image
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import moment from 'moment';
import TaskManager, { TaskStatus, TaskPriority, TaskType } from '../services/TaskManager';

const TaskDetailScreen = ({ route, navigation }) => {
  const { taskId } = route.params;
  const [task, setTask] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTask();
  }, [taskId]);

  const loadTask = () => {
    try {
      const taskData = TaskManager.getTaskById(taskId);
      if (taskData) {
        setTask(taskData);
      } else {
        Alert.alert('Error', 'Task not found', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error loading task:', error);
      Alert.alert('Error', 'Failed to load task details');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case TaskStatus.PENDING: return '#FFA500';
      case TaskStatus.IN_PROGRESS: return '#2196F3';
      case TaskStatus.COMPLETED: return '#4CAF50';
      case TaskStatus.OVERDUE: return '#F44336';
      case TaskStatus.CANCELLED: return '#9E9E9E';
      default: return '#757575';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case TaskPriority.URGENT: return '#D32F2F';
      case TaskPriority.HIGH: return '#F57C00';
      case TaskPriority.MEDIUM: return '#1976D2';
      case TaskPriority.LOW: return '#388E3C';
      default: return '#757575';
    }
  };

  const getTaskTypeIcon = (type) => {
    switch (type) {
      case TaskType.WATER_QUALITY_CHECK: return 'water-drop';
      case TaskType.FEEDING: return 'restaurant';
      case TaskType.POND_CLEANING: return 'cleaning-services';
      case TaskType.EQUIPMENT_MAINTENANCE: return 'build';
      case TaskType.HARVEST_PREPARATION: return 'agriculture';
      case TaskType.DISEASE_INSPECTION: return 'medical-services';
      case TaskType.INVENTORY_CHECK: return 'inventory';
      case TaskType.REPORT_SUBMISSION: return 'description';
      default: return 'task';
    }
  };

  const handleStartTask = async () => {
    try {
      await TaskManager.startTask(taskId);
      loadTask();
      Alert.alert('Success', 'Task started successfully');
    } catch (error) {
      console.error('Error starting task:', error);
      Alert.alert('Error', 'Failed to start task');
    }
  };

  const handleCompleteTask = () => {
    navigation.navigate('TaskCompletion', { taskId });
  };

  const handleCancelTask = () => {
    Alert.alert(
      'Cancel Task',
      'Are you sure you want to cancel this task?',
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes', 
          style: 'destructive',
          onPress: async () => {
            try {
              await TaskManager.cancelTask(taskId, 'Cancelled by user');
              loadTask();
              Alert.alert('Success', 'Task cancelled successfully');
            } catch (error) {
              console.error('Error cancelling task:', error);
              Alert.alert('Error', 'Failed to cancel task');
            }
          }
        }
      ]
    );
  };

  const renderActionButtons = () => {
    if (!task) return null;

    const canStart = task.status === TaskStatus.PENDING || task.status === TaskStatus.OVERDUE;
    const canComplete = task.status === TaskStatus.IN_PROGRESS;
    const canCancel = task.status === TaskStatus.PENDING || 
                     task.status === TaskStatus.IN_PROGRESS || 
                     task.status === TaskStatus.OVERDUE;

    return (
      <View style={styles.actionButtons}>
        {canStart && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.startButton]}
            onPress={handleStartTask}
          >
            <Icon name="play-arrow" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Start Task</Text>
          </TouchableOpacity>
        )}
        
        {canComplete && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.completeButton]}
            onPress={handleCompleteTask}
          >
            <Icon name="check" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Complete</Text>
          </TouchableOpacity>
        )}
        
        {canCancel && (
          <TouchableOpacity 
            style={[styles.actionButton, styles.cancelButton]}
            onPress={handleCancelTask}
          >
            <Icon name="close" size={24} color="#fff" />
            <Text style={styles.actionButtonText}>Cancel</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderTaskInfo = () => {
    if (!task) return null;

    const isOverdue = moment(task.dueDate).isBefore(moment()) && 
                     task.status !== TaskStatus.COMPLETED && 
                     task.status !== TaskStatus.CANCELLED;

    return (
      <View style={styles.taskInfo}>
        {/* Header */}
        <View style={styles.taskHeader}>
          <View style={styles.taskTitleRow}>
            <Icon 
              name={getTaskTypeIcon(task.type)} 
              size={32} 
              color="#333" 
              style={styles.taskIcon}
            />
            <View style={styles.taskTitleContainer}>
              <Text style={styles.taskTitle}>{task.title}</Text>
              <View style={styles.badgeRow}>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(task.status) }
                ]}>
                  <Text style={styles.badgeText}>
                    {task.status.replace('_', ' ').toUpperCase()}
                  </Text>
                </View>
                <View style={[
                  styles.priorityBadge,
                  { backgroundColor: getPriorityColor(task.priority) }
                ]}>
                  <Text style={styles.badgeText}>
                    {task.priority.toUpperCase()}
                  </Text>
                </View>
              </View>
            </View>
          </View>
          
          {task.localChanges && (
            <View style={styles.syncStatus}>
              <Icon name="sync-problem" size={20} color="#FF9800" />
              <Text style={styles.syncStatusText}>Pending Sync</Text>
            </View>
          )}
        </View>

        {/* Description */}
        {task.description && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>{task.description}</Text>
          </View>
        )}

        {/* Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Details</Text>
          
          <View style={styles.detailRow}>
            <Icon name="schedule" size={20} color="#666" />
            <Text style={styles.detailLabel}>Due Date:</Text>
            <Text style={[
              styles.detailValue,
              isOverdue && styles.overdueText
            ]}>
              {moment(task.dueDate).format('MMM DD, YYYY HH:mm')}
              {isOverdue && ' (Overdue)'}
            </Text>
          </View>

          {task.pondName && (
            <View style={styles.detailRow}>
              <Icon name="water" size={20} color="#666" />
              <Text style={styles.detailLabel}>Pond:</Text>
              <Text style={styles.detailValue}>{task.pondName}</Text>
            </View>
          )}

          {task.estimatedDuration && (
            <View style={styles.detailRow}>
              <Icon name="timer" size={20} color="#666" />
              <Text style={styles.detailLabel}>Estimated Duration:</Text>
              <Text style={styles.detailValue}>{task.estimatedDuration} minutes</Text>
            </View>
          )}

          {task.assignedBy && (
            <View style={styles.detailRow}>
              <Icon name="person" size={20} color="#666" />
              <Text style={styles.detailLabel}>Assigned By:</Text>
              <Text style={styles.detailValue}>{task.assignedBy}</Text>
            </View>
          )}

          <View style={styles.detailRow}>
            <Icon name="add-circle" size={20} color="#666" />
            <Text style={styles.detailLabel}>Created:</Text>
            <Text style={styles.detailValue}>
              {moment(task.createdAt).format('MMM DD, YYYY HH:mm')}
            </Text>
          </View>

          {task.startedAt && (
            <View style={styles.detailRow}>
              <Icon name="play-arrow" size={20} color="#666" />
              <Text style={styles.detailLabel}>Started:</Text>
              <Text style={styles.detailValue}>
                {moment(task.startedAt).format('MMM DD, YYYY HH:mm')}
              </Text>
            </View>
          )}

          {task.completedAt && (
            <View style={styles.detailRow}>
              <Icon name="check-circle" size={20} color="#666" />
              <Text style={styles.detailLabel}>Completed:</Text>
              <Text style={styles.detailValue}>
                {moment(task.completedAt).format('MMM DD, YYYY HH:mm')}
              </Text>
            </View>
          )}
        </View>

        {/* Instructions */}
        {task.instructions && task.instructions.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Instructions</Text>
            {task.instructions.map((instruction, index) => (
              <View key={index} style={styles.instructionItem}>
                <Text style={styles.instructionNumber}>{index + 1}.</Text>
                <Text style={styles.instructionText}>{instruction}</Text>
              </View>
            ))}
          </View>
        )}

        {/* Completion Data */}
        {task.completionData && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Completion Details</Text>
            
            {task.completionData.notes && (
              <View style={styles.completionItem}>
                <Text style={styles.completionLabel}>Notes:</Text>
                <Text style={styles.completionValue}>{task.completionData.notes}</Text>
              </View>
            )}

            {task.completionData.photos && task.completionData.photos.length > 0 && (
              <View style={styles.completionItem}>
                <Text style={styles.completionLabel}>Photos:</Text>
                <View style={styles.photoGrid}>
                  {task.completionData.photos.map((photo, index) => (
                    <Image key={index} source={{ uri: photo }} style={styles.photo} />
                  ))}
                </View>
              </View>
            )}

            {task.completionData.measurements && (
              <View style={styles.completionItem}>
                <Text style={styles.completionLabel}>Measurements:</Text>
                {Object.entries(task.completionData.measurements).map(([key, value]) => (
                  <Text key={key} style={styles.measurementText}>
                    {key}: {value}
                  </Text>
                ))}
              </View>
            )}
          </View>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="hourglass-empty" size={48} color="#ccc" />
        <Text style={styles.loadingText}>Loading task details...</Text>
      </View>
    );
  }

  if (!task) {
    return (
      <View style={styles.errorContainer}>
        <Icon name="error" size={48} color="#F44336" />
        <Text style={styles.errorText}>Task not found</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderTaskInfo()}
      </ScrollView>
      {renderActionButtons()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    marginTop: 16,
  },
  taskInfo: {
    padding: 16,
  },
  taskHeader: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  taskTitleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  taskIcon: {
    marginRight: 12,
    marginTop: 4,
  },
  taskTitleContainer: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    lineHeight: 28,
    marginBottom: 8,
  },
  badgeRow: {
    flexDirection: 'row',
    gap: 8,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  priorityBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '700',
    color: '#fff',
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  syncStatusText: {
    fontSize: 12,
    color: '#FF9800',
    fontWeight: '600',
    marginLeft: 4,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    marginRight: 8,
    minWidth: 100,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  overdueText: {
    color: '#F44336',
    fontWeight: '600',
  },
  instructionItem: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  instructionNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2196F3',
    marginRight: 8,
    minWidth: 20,
  },
  instructionText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
    lineHeight: 20,
  },
  completionItem: {
    marginBottom: 16,
  },
  completionLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  completionValue: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  photo: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  measurementText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    gap: 8,
  },
  startButton: {
    backgroundColor: '#2196F3',
  },
  completeButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
});

export default TaskDetailScreen;
