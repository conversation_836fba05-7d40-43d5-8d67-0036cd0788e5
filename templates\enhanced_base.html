{% load static %}
<!DOCTYPE html>
<html lang="en" data-theme="dark" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="color-scheme" content="dark light">
    <title>{% block title %}Enhanced Dashboard - Shrimp Farm Guardian{% endblock %}</title><!-- Dark theme first before Bootstrap to enforce dark theme priority -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-dark-5@1.1.3/dist/css/bootstrap-dark.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS (loaded after dark theme to ensure dark theme takes precedence) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" media="print">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        
          html, body {
            height: auto;
            min-height: 100vh;
            width: 100%;
            overflow-x: hidden;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;
            background-color: #0f172a !important;
            color: #ffffff !important;
            color-scheme: dark !important;
        }
        
        /* Maximum priority dark theme enforcement */
        body:before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            z-index: -1;
            pointer-events: none;
        }
        
        /* Dark theme styles for enhanced dashboard */
        .card, .panel, .widget, .box, .container, .section {
            background-color: rgba(30, 41, 59, 0.7) !important;
            border-color: rgba(74, 85, 104, 0.3) !important;
            color: #e2e8f0 !important;
        }
        
        .card-header, .panel-header, .widget-header, .box-header {
            background-color: rgba(15, 23, 42, 0.6) !important;
            border-color: rgba(74, 85, 104, 0.3) !important;
        }
        
        .card-footer, .panel-footer, .widget-footer, .box-footer {
            background-color: rgba(15, 23, 42, 0.6) !important;
            border-color: rgba(74, 85, 104, 0.3) !important;
        }
        
        .bg-light, .bg-white {
            background-color: rgba(30, 41, 59, 0.7) !important;
        }
        
        /* Fix contrast for text */
        .text-dark, .text-body, .text-black {
            color: #e2e8f0 !important;
        }
        
        /* Ensure input fields are visible */
        input, select, textarea, .form-control {
            background-color: rgba(15, 23, 42, 0.7) !important;
            border-color: rgba(74, 85, 104, 0.5) !important;
            color: #e2e8f0 !important;
        }
        
        /* Fix buttons */
        .btn-light, .btn-white {
            background-color: #334155 !important;
            border-color: #475569 !important;
            color: white !important;
        }
        
        /* Dashboard specific elements */
        .dashboard-container,
        #dashboard-content,
        .enhanced-dashboard,
        .dashboard-wrapper,
        .dashboard-section {
            background: transparent !important;
            background-color: transparent !important;
            color: #e2e8f0 !important;
        }
        
        /* Final catch-all for body */
        @media (prefers-color-scheme: dark) {
            html, body {
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;
                background-color: #0f172a !important;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% block content %}{% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
