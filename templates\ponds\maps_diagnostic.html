<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Diagnostic</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .diagnostic-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #007bff;
            border-radius: 10px;
            margin: 20px 0;
        }
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .test-value {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 4px;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .refresh-btn {
            background: #28a745;
        }
        .refresh-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <h1>🔧 Google Maps Diagnostic Tool</h1>
    
    <div class="diagnostic-card">
        <h2>📊 System Status</h2>
        <div id="overall-status" class="status info">Running diagnostics...</div>
        
        <div class="test-results">
            <div class="test-item">
                <h4>🔑 API Key Status</h4>
                <div class="test-value" id="api-key-status">Checking...</div>
            </div>
            
            <div class="test-item">
                <h4>🌐 Google Maps API</h4>
                <div class="test-value" id="google-api-status">Checking...</div>
            </div>
            
            <div class="test-item">
                <h4>📍 Geolocation Support</h4>
                <div class="test-value" id="geolocation-status">Checking...</div>
            </div>
            
            <div class="test-item">
                <h4>🗺️ Map Initialization</h4>
                <div class="test-value" id="map-init-status">Waiting...</div>
            </div>
            
            <div class="test-item">
                <h4>📡 Network Status</h4>
                <div class="test-value" id="network-status">Checking...</div>
            </div>
            
            <div class="test-item">
                <h4>🔧 Browser Support</h4>
                <div class="test-value" id="browser-status">Checking...</div>
            </div>
        </div>
        
        <div>
            <button onclick="runDiagnostics()">🔄 Run Diagnostics</button>
            <button onclick="testMapCreation()" class="refresh-btn">🗺️ Test Map Creation</button>
            <button onclick="location.reload()">🔄 Refresh Page</button>
        </div>
    </div>
    
    <div class="diagnostic-card">
        <h2>🗺️ Map Test</h2>
        <div id="map-status" class="status info">Map will load here after API is ready</div>
        <div id="map"></div>
    </div>
    
    <div class="diagnostic-card">
        <h2>📋 Console Logs</h2>
        <div id="console-logs" style="background: #2d3748; color: #e2e8f0; padding: 15px; border-radius: 8px; font-family: monospace; max-height: 300px; overflow-y: auto;">
            <div>🔄 Diagnostic tool initialized</div>
        </div>
    </div>

    <script>
        let map;
        let diagnosticResults = {};
        
        // Custom console logging
        function logToConsole(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('console-logs');
            const logEntry = document.createElement('div');
            logEntry.style.margin = '2px 0';
            logEntry.style.color = type === 'error' ? '#fc8181' : type === 'success' ? '#68d391' : '#e2e8f0';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        // Global initMap function for Google Maps callback
        window.initMap = function() {
            logToConsole('🗺️ initMap called by Google Maps API', 'success');
            updateStatus('map-init-status', '✅ Callback received', 'success');
            
            try {
                testMapCreation();
            } catch (error) {
                logToConsole('❌ Error in initMap: ' + error.message, 'error');
                updateStatus('map-init-status', '❌ Error: ' + error.message, 'error');
            }
        };
        
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                element.className = `test-value ${type}`;
            }
        }
        
        function runDiagnostics() {
            logToConsole('🔄 Starting comprehensive diagnostics...');
            
            // Check API Key
            const apiKey = '{{ google_maps_api_key }}';
            if (apiKey && apiKey !== '') {
                updateStatus('api-key-status', '✅ API Key present', 'success');
                logToConsole('✅ API Key is configured');
            } else {
                updateStatus('api-key-status', '❌ API Key missing', 'error');
                logToConsole('❌ API Key is not configured', 'error');
            }
            
            // Check Google Maps API
            if (typeof google !== 'undefined' && google.maps) {
                updateStatus('google-api-status', '✅ Google Maps API loaded', 'success');
                logToConsole('✅ Google Maps API is available');
            } else {
                updateStatus('google-api-status', '❌ Google Maps API not loaded', 'error');
                logToConsole('❌ Google Maps API is not available', 'error');
            }
            
            // Check Geolocation
            if (navigator.geolocation) {
                updateStatus('geolocation-status', '✅ Geolocation supported', 'success');
                logToConsole('✅ Geolocation is supported');
            } else {
                updateStatus('geolocation-status', '❌ Geolocation not supported', 'error');
                logToConsole('❌ Geolocation is not supported', 'error');
            }
            
            // Check Network
            if (navigator.onLine) {
                updateStatus('network-status', '✅ Online', 'success');
                logToConsole('✅ Network connection is available');
            } else {
                updateStatus('network-status', '❌ Offline', 'error');
                logToConsole('❌ Network connection is not available', 'error');
            }
            
            // Check Browser Support
            const browserInfo = `${navigator.userAgent.split(' ')[0]} - ${navigator.language}`;
            updateStatus('browser-status', `✅ ${browserInfo}`, 'success');
            logToConsole(`✅ Browser: ${browserInfo}`);
            
            // Update overall status
            const hasErrors = document.querySelectorAll('.test-value.error').length > 0;
            const overallStatus = document.getElementById('overall-status');
            if (hasErrors) {
                overallStatus.textContent = '❌ Issues detected - check individual tests';
                overallStatus.className = 'status error';
            } else {
                overallStatus.textContent = '✅ All systems operational';
                overallStatus.className = 'status success';
            }
        }
        
        function testMapCreation() {
            logToConsole('🗺️ Testing map creation...');
            const mapStatus = document.getElementById('map-status');
            
            if (typeof google === 'undefined' || !google.maps) {
                mapStatus.textContent = '❌ Google Maps API not available';
                mapStatus.className = 'status error';
                logToConsole('❌ Cannot create map - Google Maps API not loaded', 'error');
                return;
            }
            
            try {
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 13.0827, lng: 80.2707 },
                    mapTypeId: 'roadmap'
                });
                
                // Add a test marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai, India'
                });
                
                mapStatus.textContent = '✅ Map created successfully with test marker';
                mapStatus.className = 'status success';
                updateStatus('map-init-status', '✅ Map created successfully', 'success');
                logToConsole('✅ Map created successfully with test marker', 'success');
                
            } catch (error) {
                mapStatus.textContent = '❌ Error creating map: ' + error.message;
                mapStatus.className = 'status error';
                updateStatus('map-init-status', '❌ Error: ' + error.message, 'error');
                logToConsole('❌ Error creating map: ' + error.message, 'error');
            }
        }
        
        function handleMapError() {
            logToConsole('❌ Google Maps API failed to load', 'error');
            updateStatus('google-api-status', '❌ Failed to load', 'error');
            const mapStatus = document.getElementById('map-status');
            mapStatus.textContent = '❌ Failed to load Google Maps API';
            mapStatus.className = 'status error';
        }
        
        // Run initial diagnostics
        document.addEventListener('DOMContentLoaded', function() {
            logToConsole('🔄 DOM loaded, running initial diagnostics...');
            setTimeout(runDiagnostics, 1000);
        });
        
        // Check for Google Maps API loading
        setTimeout(() => {
            if (typeof google === 'undefined') {
                logToConsole('⚠️ Google Maps API did not load within 10 seconds', 'error');
                updateStatus('google-api-status', '⚠️ Timeout - API not loaded', 'error');
            }
        }, 10000);
    </script>

    <!-- Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry&loading=async"
        onerror="handleMapError()">
    </script>
</body>
</html>
