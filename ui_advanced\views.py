"""
Advanced UI/UX Views
Design system showcase and component library
"""

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json


def component_showcase(request):
    """
    Design system component showcase
    Displays all available UI components and design tokens
    """
    context = {
        'page_title': 'Design System - Component Showcase',
        'components': [
            {
                'name': 'Buttons',
                'description': 'Interactive button components with multiple variants and sizes',
                'variants': ['primary', 'secondary', 'success', 'warning', 'danger'],
                'sizes': ['xs', 'sm', 'default', 'lg', 'xl']
            },
            {
                'name': 'Form Controls',
                'description': 'Input fields, selects, and form validation components',
                'types': ['text', 'email', 'password', 'textarea', 'select', 'checkbox', 'radio']
            },
            {
                'name': 'Cards',
                'description': 'Flexible content containers with headers, bodies, and footers',
                'layouts': ['basic', 'simple', 'image', 'action']
            },
            {
                'name': 'Alerts & Badges',
                'description': 'Status indicators and notification components',
                'types': ['primary', 'success', 'warning', 'danger', 'info']
            },
            {
                'name': 'Typography',
                'description': 'Text styles, headings, and font system',
                'scales': ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl']
            },
            {
                'name': 'Animations',
                'description': 'Motion design and interactive animations',
                'types': ['fade', 'slide', 'bounce', 'pulse', 'spin', 'hover-effects']
            }
        ],
        'design_tokens': {
            'colors': {
                'primary': ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
                'secondary': ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
                'success': ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
                'warning': ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900'],
                'danger': ['50', '100', '200', '300', '400', '500', '600', '700', '800', '900']
            },
            'spacing': ['0', '1', '2', '3', '4', '5', '6', '8', '10', '12', '16', '20', '24'],
            'typography': {
                'sizes': ['xs', 'sm', 'base', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl'],
                'weights': ['light', 'normal', 'medium', 'semibold', 'bold', 'extrabold']
            },
            'border_radius': ['none', 'sm', 'base', 'md', 'lg', 'xl', '2xl', '3xl', 'full'],
            'shadows': ['sm', 'base', 'md', 'lg', 'xl', '2xl', 'inner']
        }
    }
    
    return render(request, 'ui_advanced/component_showcase.html', context)


def design_tokens(request):
    """
    Design tokens API endpoint
    Returns all design system tokens as JSON
    """
    tokens = {
        'colors': {
            'primary': {
                '50': '#f0f9ff',
                '100': '#e0f2fe',
                '200': '#bae6fd',
                '300': '#7dd3fc',
                '400': '#38bdf8',
                '500': '#0ea5e9',
                '600': '#0284c7',
                '700': '#0369a1',
                '800': '#075985',
                '900': '#0c4a6e'
            },
            'secondary': {
                '50': '#f8fafc',
                '100': '#f1f5f9',
                '200': '#e2e8f0',
                '300': '#cbd5e1',
                '400': '#94a3b8',
                '500': '#64748b',
                '600': '#475569',
                '700': '#334155',
                '800': '#1e293b',
                '900': '#0f172a'
            },
            'success': {
                '50': '#f0fdf4',
                '100': '#dcfce7',
                '200': '#bbf7d0',
                '300': '#86efac',
                '400': '#4ade80',
                '500': '#22c55e',
                '600': '#16a34a',
                '700': '#15803d',
                '800': '#166534',
                '900': '#14532d'
            },
            'warning': {
                '50': '#fffbeb',
                '100': '#fef3c7',
                '200': '#fde68a',
                '300': '#fcd34d',
                '400': '#fbbf24',
                '500': '#f59e0b',
                '600': '#d97706',
                '700': '#b45309',
                '800': '#92400e',
                '900': '#78350f'
            },
            'danger': {
                '50': '#fef2f2',
                '100': '#fee2e2',
                '200': '#fecaca',
                '300': '#fca5a5',
                '400': '#f87171',
                '500': '#ef4444',
                '600': '#dc2626',
                '700': '#b91c1c',
                '800': '#991b1b',
                '900': '#7f1d1d'
            }
        },
        'spacing': {
            '0': '0',
            '1': '0.25rem',
            '2': '0.5rem',
            '3': '0.75rem',
            '4': '1rem',
            '5': '1.25rem',
            '6': '1.5rem',
            '8': '2rem',
            '10': '2.5rem',
            '12': '3rem',
            '16': '4rem',
            '20': '5rem',
            '24': '6rem'
        },
        'typography': {
            'font_sizes': {
                'xs': '0.75rem',
                'sm': '0.875rem',
                'base': '1rem',
                'lg': '1.125rem',
                'xl': '1.25rem',
                '2xl': '1.5rem',
                '3xl': '1.875rem',
                '4xl': '2.25rem',
                '5xl': '3rem'
            },
            'font_weights': {
                'light': '300',
                'normal': '400',
                'medium': '500',
                'semibold': '600',
                'bold': '700',
                'extrabold': '800'
            },
            'line_heights': {
                'tight': '1.25',
                'snug': '1.375',
                'normal': '1.5',
                'relaxed': '1.625',
                'loose': '2'
            }
        },
        'border_radius': {
            'none': '0',
            'sm': '0.125rem',
            'base': '0.25rem',
            'md': '0.375rem',
            'lg': '0.5rem',
            'xl': '0.75rem',
            '2xl': '1rem',
            '3xl': '1.5rem',
            'full': '9999px'
        },
        'shadows': {
            'sm': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
            'base': '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
            'md': '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
            'lg': '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
            'xl': '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
            '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
            'inner': 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)'
        },
        'transitions': {
            'fast': '150ms cubic-bezier(0.4, 0, 0.2, 1)',
            'base': '250ms cubic-bezier(0.4, 0, 0.2, 1)',
            'slow': '350ms cubic-bezier(0.4, 0, 0.2, 1)'
        }
    }
    
    return JsonResponse(tokens)


@login_required
def accessibility_demo(request):
    """
    Accessibility features demonstration
    Shows WCAG compliance and accessibility enhancements
    """
    context = {
        'page_title': 'Accessibility Features Demo',
        'accessibility_features': [
            {
                'name': 'Keyboard Navigation',
                'description': 'Full keyboard support with visible focus indicators',
                'wcag_level': 'AA',
                'demo_available': True
            },
            {
                'name': 'Screen Reader Support',
                'description': 'ARIA labels, live regions, and semantic markup',
                'wcag_level': 'AA',
                'demo_available': True
            },
            {
                'name': 'Color Contrast',
                'description': 'WCAG AA compliant color combinations',
                'wcag_level': 'AA',
                'demo_available': True
            },
            {
                'name': 'Reduced Motion',
                'description': 'Respects user preference for reduced motion',
                'wcag_level': 'AAA',
                'demo_available': True
            },
            {
                'name': 'Focus Management',
                'description': 'Proper focus trapping and restoration',
                'wcag_level': 'AA',
                'demo_available': True
            },
            {
                'name': 'Alternative Text',
                'description': 'Descriptive alt text for images and icons',
                'wcag_level': 'A',
                'demo_available': True
            }
        ]
    }
    
    return render(request, 'ui_advanced/accessibility_demo.html', context)


def dark_mode_demo(request):
    """
    Dark mode and theming demonstration
    Shows theme switching and customization options
    """
    context = {
        'page_title': 'Dark Mode & Theming Demo',
        'themes': [
            {
                'name': 'Light',
                'description': 'Clean and bright interface for daytime use',
                'preview_class': 'theme-light'
            },
            {
                'name': 'Dark',
                'description': 'Easy on the eyes for low-light environments',
                'preview_class': 'theme-dark'
            },
            {
                'name': 'Auto',
                'description': 'Automatically follows system preference',
                'preview_class': 'theme-auto'
            }
        ],
        'customization_options': [
            'Primary color selection',
            'Font size adjustment',
            'Animation preferences',
            'Contrast settings',
            'Layout density'
        ]
    }
    
    return render(request, 'ui_advanced/dark_mode_demo.html', context)


@require_http_methods(["POST"])
def save_theme_preference(request):
    """
    Save user theme preference
    """
    try:
        data = json.loads(request.body)
        theme = data.get('theme', 'light')
        
        if theme not in ['light', 'dark', 'auto']:
            return JsonResponse({'error': 'Invalid theme'}, status=400)
        
        # Save to user profile or session
        request.session['theme_preference'] = theme
        
        return JsonResponse({
            'success': True,
            'theme': theme,
            'message': f'Theme preference saved: {theme}'
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def responsive_demo(request):
    """
    Responsive design demonstration
    Shows breakpoint behavior and mobile optimization
    """
    context = {
        'page_title': 'Responsive Design Demo',
        'breakpoints': [
            {
                'name': 'Mobile',
                'size': '< 640px',
                'description': 'Optimized for mobile devices',
                'features': ['Touch-friendly buttons', 'Simplified navigation', 'Stacked layouts']
            },
            {
                'name': 'Tablet',
                'size': '640px - 1024px',
                'description': 'Balanced layout for tablets',
                'features': ['Flexible grids', 'Adaptive typography', 'Touch and mouse support']
            },
            {
                'name': 'Desktop',
                'size': '> 1024px',
                'description': 'Full-featured desktop experience',
                'features': ['Multi-column layouts', 'Hover effects', 'Keyboard shortcuts']
            }
        ],
        'responsive_features': [
            'Fluid typography scaling',
            'Flexible grid systems',
            'Adaptive image loading',
            'Touch gesture support',
            'Viewport-aware animations'
        ]
    }
    
    return render(request, 'ui_advanced/responsive_demo.html', context)


def animation_playground(request):
    """
    Animation and interaction playground
    Interactive demo of animation system
    """
    context = {
        'page_title': 'Animation Playground',
        'animations': [
            {
                'name': 'Fade In',
                'class': 'animate-fade-in',
                'description': 'Smooth opacity transition'
            },
            {
                'name': 'Slide In Up',
                'class': 'animate-fade-in-up',
                'description': 'Fade in with upward motion'
            },
            {
                'name': 'Slide In Down',
                'class': 'animate-fade-in-down',
                'description': 'Fade in with downward motion'
            },
            {
                'name': 'Slide In Left',
                'class': 'animate-slide-in-left',
                'description': 'Slide in from the left'
            },
            {
                'name': 'Slide In Right',
                'class': 'animate-slide-in-right',
                'description': 'Slide in from the right'
            },
            {
                'name': 'Pulse',
                'class': 'animate-pulse',
                'description': 'Continuous pulsing effect'
            },
            {
                'name': 'Bounce',
                'class': 'animate-bounce',
                'description': 'Bouncing animation'
            },
            {
                'name': 'Spin',
                'class': 'animate-spin',
                'description': 'Continuous rotation'
            }
        ],
        'interaction_types': [
            'Hover effects',
            'Click animations',
            'Scroll-triggered animations',
            'Parallax effects',
            'Micro-interactions'
        ]
    }
    
    return render(request, 'ui_advanced/animation_playground.html', context)
