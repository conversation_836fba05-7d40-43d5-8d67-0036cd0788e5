"""
Weather utilities for the shrimp farm monitoring system.
Contains functions for weather API integration, alert analysis, and notifications.
"""

import requests
import logging
from datetime import datetime, timedelta
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.html import strip_tags

logger = logging.getLogger(__name__)


def get_weather_data(latitude, longitude):
    """
    Fetch current weather data from OpenWeatherMap API.
    
    Args:
        latitude (float): Latitude coordinate
        longitude (float): Longitude coordinate
    
    Returns:
        dict: Weather data or None if failed
    """
    api_key = getattr(settings, 'OPENWEATHER_API_KEY', None)
    if not api_key:
        logger.error("OpenWeatherMap API key not configured")
        return None
    
    try:
        url = f"https://api.openweathermap.org/data/2.5/weather"
        params = {
            'lat': latitude,
            'lon': longitude,
            'appid': api_key,
            'units': 'metric'  # Use Celsius
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        # Extract relevant weather information
        weather_data = {
            'temperature': data['main']['temp'],
            'humidity': data['main']['humidity'],
            'pressure': data['main']['pressure'],
            'wind_speed': data['wind'].get('speed', 0),
            'wind_direction': data['wind'].get('deg', 0),
            'weather_condition': data['weather'][0]['main'].lower(),
            'description': data['weather'][0]['description'],
            'visibility': data.get('visibility', 0) / 1000,  # Convert to km
            'timestamp': datetime.now(),
            'location': f"{latitude}, {longitude}"
        }
        
        # Add rainfall if available
        if 'rain' in data:
            weather_data['rainfall'] = data['rain'].get('1h', 0)
        else:
            weather_data['rainfall'] = 0
        
        return weather_data
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching weather data: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in weather API: {e}")
        return None


def get_weather_forecast(latitude, longitude, days=5):
    """
    Fetch weather forecast from OpenWeatherMap API.
    
    Args:
        latitude (float): Latitude coordinate
        longitude (float): Longitude coordinate
        days (int): Number of days to forecast
    
    Returns:
        list: List of forecast data or None if failed
    """
    api_key = getattr(settings, 'OPENWEATHER_API_KEY', None)
    if not api_key:
        logger.error("OpenWeatherMap API key not configured")
        return None
    
    try:
        url = f"https://api.openweathermap.org/data/2.5/forecast"
        params = {
            'lat': latitude,
            'lon': longitude,
            'appid': api_key,
            'units': 'metric',
            'cnt': days * 8  # 8 forecasts per day (3-hour intervals)
        }
        
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        
        forecast_list = []
        for item in data['list']:
            forecast_data = {
                'datetime': datetime.fromtimestamp(item['dt']),
                'temperature': item['main']['temp'],
                'humidity': item['main']['humidity'],
                'pressure': item['main']['pressure'],
                'wind_speed': item['wind'].get('speed', 0),
                'weather_condition': item['weather'][0]['main'].lower(),
                'description': item['weather'][0]['description'],
                'rainfall': item.get('rain', {}).get('3h', 0)
            }
            forecast_list.append(forecast_data)
        
        return forecast_list
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching weather forecast: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error in weather forecast API: {e}")
        return None


def analyze_weather_for_alerts(pond, weather_data):
    """
    Analyze weather data and generate alerts for critical conditions.
    
    Args:
        pond: Pond model instance
        weather_data (dict): Weather data from API
    
    Returns:
        list: List of alert dictionaries
    """
    alerts = []
    
    # Define thresholds (these could be configurable per pond)
    thresholds = {
        'high_temperature': 35,  # Celsius
        'low_temperature': 18,   # Celsius
        'high_humidity': 85,     # Percentage
        'low_humidity': 40,      # Percentage
        'high_wind_speed': 10,   # m/s
        'heavy_rain': 10,        # mm/h
        'low_pressure': 1005,    # hPa
        'temperature_change': 5, # Celsius change threshold
    }
    
    temperature = weather_data.get('temperature', 0)
    humidity = weather_data.get('humidity', 0)
    wind_speed = weather_data.get('wind_speed', 0)
    rainfall = weather_data.get('rainfall', 0)
    pressure = weather_data.get('pressure', 1013)
    weather_condition = weather_data.get('weather_condition', '').lower()
    
    # High temperature alert
    if temperature > thresholds['high_temperature']:
        alerts.append({
            'type': 'HIGH_TEMPERATURE',
            'severity': 'HIGH',
            'message': f"High temperature alert: {temperature}°C in {pond.name}. Consider increasing aeration.",
            'value': temperature,
            'threshold': thresholds['high_temperature']
        })
    
    # Low temperature alert
    if temperature < thresholds['low_temperature']:
        alerts.append({
            'type': 'LOW_TEMPERATURE',
            'severity': 'MEDIUM',
            'message': f"Low temperature alert: {temperature}°C in {pond.name}. Monitor shrimp behavior.",
            'value': temperature,
            'threshold': thresholds['low_temperature']
        })
    
    # High humidity alert
    if humidity > thresholds['high_humidity']:
        alerts.append({
            'type': 'HIGH_HUMIDITY',
            'severity': 'MEDIUM',
            'message': f"High humidity alert: {humidity}% in {pond.name}. Increased disease risk.",
            'value': humidity,
            'threshold': thresholds['high_humidity']
        })
    
    # High wind speed alert
    if wind_speed > thresholds['high_wind_speed']:
        alerts.append({
            'type': 'HIGH_WIND',
            'severity': 'HIGH',
            'message': f"Strong wind alert: {wind_speed} m/s in {pond.name}. Secure equipment.",
            'value': wind_speed,
            'threshold': thresholds['high_wind_speed']
        })
    
    # Heavy rain alert
    if rainfall > thresholds['heavy_rain']:
        alerts.append({
            'type': 'HEAVY_RAIN',
            'severity': 'HIGH',
            'message': f"Heavy rainfall alert: {rainfall} mm/h in {pond.name}. Monitor water levels.",
            'value': rainfall,
            'threshold': thresholds['heavy_rain']
        })
    
    # Severe weather conditions
    severe_conditions = ['thunderstorm', 'tornado', 'hurricane', 'hail']
    if any(condition in weather_condition for condition in severe_conditions):
        alerts.append({
            'type': 'SEVERE_WEATHER',
            'severity': 'CRITICAL',
            'message': f"Severe weather alert: {weather_condition} in {pond.name}. Take immediate action.",
            'value': weather_condition,
            'threshold': 'any_severe'
        })
    
    # Low pressure (storm warning)
    if pressure < thresholds['low_pressure']:
        alerts.append({
            'type': 'LOW_PRESSURE',
            'severity': 'MEDIUM',
            'message': f"Low pressure alert: {pressure} hPa in {pond.name}. Possible storm approaching.",
            'value': pressure,
            'threshold': thresholds['low_pressure']
        })
    
    return alerts


def send_weather_notification(user, pond, alert_type, message, severity='MEDIUM'):
    """
    Send weather notification to user via email and SMS.
    
    Args:
        user: User model instance
        pond: Pond model instance
        alert_type (str): Type of alert
        message (str): Alert message
        severity (str): Alert severity level
    
    Returns:
        bool: Success status
    """
    try:
        # Create notification record
        from notifications.models import Notification
        notification = Notification.objects.create(
            user=user,
            title=f"Weather Alert - {pond.name}",
            message=message,
            notification_type='weather_alert',
            priority=severity.lower(),
            data={
                'pond_id': pond.id,
                'pond_name': pond.name,
                'alert_type': alert_type,
                'severity': severity
            }
        )
        
        # Send email notification
        success = send_email_notification(user, pond, alert_type, message, severity)
        
        # Send SMS notification for high/critical alerts
        if severity in ['HIGH', 'CRITICAL']:
            send_sms_notification(user, pond, alert_type, message, severity)
        
        return success
        
    except Exception as e:
        logger.error(f"Error sending weather notification: {e}")
        return False


def send_email_notification(user, pond, alert_type, message, severity):
    """
    Send email notification for weather alert.
    
    Args:
        user: User model instance
        pond: Pond model instance
        alert_type (str): Type of alert
        message (str): Alert message
        severity (str): Alert severity level
    
    Returns:
        bool: Success status
    """
    try:
        subject = f"🌤️ Weather Alert: {pond.name} - {alert_type}"
        
        # Create HTML email content
        html_message = render_to_string('alerts/email/weather_alert.html', {
            'user': user,
            'pond': pond,
            'alert_type': alert_type,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now()
        })
        
        # Create plain text version
        plain_message = strip_tags(html_message)
        
        # Send email
        send_mail(
            subject=subject,
            message=plain_message,
            html_message=html_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            recipient_list=[user.email],
            fail_silently=False
        )
        
        logger.info(f"Weather alert email sent to {user.email} for pond {pond.name}")
        return True
        
    except Exception as e:
        logger.error(f"Error sending weather alert email: {e}")
        return False


def send_sms_notification(user, pond, alert_type, message, severity):
    """
    Send SMS notification for weather alert.
    
    Args:
        user: User model instance
        pond: Pond model instance
        alert_type (str): Type of alert
        message (str): Alert message
        severity (str): Alert severity level
    
    Returns:
        bool: Success status
    """
    try:
        # Get user's phone number (from user profile or worker profile)
        phone_number = None
        if hasattr(user, 'worker_profile') and user.worker_profile:
            phone_number = user.worker_profile.phone
        elif hasattr(user, 'profile') and user.profile:
            phone_number = getattr(user.profile, 'phone', None)
        
        if not phone_number:
            logger.warning(f"No phone number found for user {user.email}")
            return False
        
        # SMS message
        sms_message = f"ALERT: {alert_type} at {pond.name}. {message[:100]}..."
        
        # TODO: Integrate with actual SMS provider (Twilio, AWS SNS, etc.)
        # This is a placeholder implementation
        sms_api_key = getattr(settings, 'SMS_API_KEY', None)
        if sms_api_key:
            # Example for Twilio integration:
            # from twilio.rest import Client
            # client = Client(account_sid, auth_token)
            # message = client.messages.create(
            #     body=sms_message,
            #     from_='+**********',
            #     to=phone_number
            # )
            pass
        
        logger.info(f"SMS notification sent to {phone_number} for pond {pond.name}")
        logger.info(f"SMS content: {sms_message}")
        
        # For now, just log the SMS (replace with actual SMS service)
        return True
        
    except Exception as e:
        logger.error(f"Error sending SMS notification: {e}")
        return False


def calculate_weather_impact_score(weather_data):
    """
    Calculate a weather impact score for shrimp farming operations.
    
    Args:
        weather_data (dict): Weather data
    
    Returns:
        tuple: (impact_score, impact_level, recommendations)
    """
    score = 0
    recommendations = []
    
    temperature = weather_data.get('temperature', 25)
    humidity = weather_data.get('humidity', 70)
    wind_speed = weather_data.get('wind_speed', 0)
    rainfall = weather_data.get('rainfall', 0)
    weather_condition = weather_data.get('weather_condition', '').lower()
    
    # Temperature impact
    if temperature < 20 or temperature > 32:
        score += 3
        recommendations.append("Monitor shrimp activity and adjust feeding schedule")
    elif temperature < 22 or temperature > 30:
        score += 1
        recommendations.append("Optimal temperature range slightly exceeded")
    
    # Humidity impact
    if humidity > 90:
        score += 2
        recommendations.append("High humidity increases disease risk - monitor water quality")
    elif humidity < 50:
        score += 1
        recommendations.append("Low humidity may affect water evaporation")
    
    # Wind impact
    if wind_speed > 8:
        score += 2
        recommendations.append("Strong winds may affect pond equipment and aeration")
    
    # Rainfall impact
    if rainfall > 5:
        score += 3
        recommendations.append("Heavy rain may dilute water and affect salinity")
    elif rainfall > 2:
        score += 1
        recommendations.append("Monitor water levels and quality")
    
    # Weather condition impact
    severe_conditions = ['thunderstorm', 'tornado', 'hurricane', 'hail']
    if any(condition in weather_condition for condition in severe_conditions):
        score += 5
        recommendations.append("CRITICAL: Severe weather - take immediate protective measures")
    elif weather_condition in ['rain', 'drizzle']:
        score += 1
        recommendations.append("Monitor pond water levels and quality")
    
    # Determine impact level
    if score >= 7:
        impact_level = 'CRITICAL'
    elif score >= 4:
        impact_level = 'HIGH'
    elif score >= 2:
        impact_level = 'MEDIUM'
    else:
        impact_level = 'LOW'
    
    return score, impact_level, recommendations


def get_weather_trends(pond, days=7):
    """
    Get weather trends for a pond over the specified number of days.
    
    Args:
        pond: Pond model instance
        days (int): Number of days to analyze
    
    Returns:
        dict: Weather trends and statistics
    """
    try:
        from weather.models import WeatherData
        
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        weather_data = WeatherData.objects.filter(
            station__pond=pond,
            timestamp__gte=start_date,
            timestamp__lte=end_date
        ).order_by('timestamp')
        
        if not weather_data.exists():
            return None
        
        # Calculate trends
        temperatures = [w.temperature for w in weather_data if w.temperature is not None]
        humidities = [w.humidity for w in weather_data if w.humidity is not None]
        
        trends = {
            'temperature': {
                'min': min(temperatures) if temperatures else None,
                'max': max(temperatures) if temperatures else None,
                'avg': sum(temperatures) / len(temperatures) if temperatures else None,
                'trend': calculate_trend(temperatures) if len(temperatures) > 1 else 'stable'
            },
            'humidity': {
                'min': min(humidities) if humidities else None,
                'max': max(humidities) if humidities else None,
                'avg': sum(humidities) / len(humidities) if humidities else None,
                'trend': calculate_trend(humidities) if len(humidities) > 1 else 'stable'
            },
            'total_rainfall': sum(w.rainfall or 0 for w in weather_data),
            'data_points': weather_data.count(),
            'period_days': days
        }
        
        return trends
        
    except Exception as e:
        logger.error(f"Error calculating weather trends: {e}")
        return None


def calculate_trend(values):
    """
    Calculate trend direction from a list of values.
    
    Args:
        values (list): List of numeric values
    
    Returns:
        str: 'increasing', 'decreasing', or 'stable'
    """
    if len(values) < 2:
        return 'stable'
    
    # Simple linear regression to determine trend
    n = len(values)
    x = list(range(n))
    
    # Calculate slope
    x_mean = sum(x) / n
    y_mean = sum(values) / n
    
    numerator = sum((x[i] - x_mean) * (values[i] - y_mean) for i in range(n))
    denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
    
    if denominator == 0:
        return 'stable'
    
    slope = numerator / denominator
    
    # Determine trend based on slope
    if slope > 0.1:
        return 'increasing'
    elif slope < -0.1:
        return 'decreasing'
    else:
        return 'stable'
