<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
        .status {
            padding: 20px;
            margin: 20px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="status" class="status">Loading...</div>
    <div id="map"></div>

    <script>
        function initMap() {
            console.log('Google Maps API loaded successfully!');
            document.getElementById('status').innerHTML = 'Google Maps API loaded successfully!';
            document.getElementById('status').className = 'status success';

            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 }
            });

            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Marker'
            });
        }

        function handleMapError() {
            console.error('Google Maps API failed to load');
            document.getElementById('status').innerHTML = 'Google Maps API failed to load - Invalid API key or network issue';
            document.getElementById('status').className = 'status error';
        }

        // Timeout check
        setTimeout(function() {
            if (typeof google === 'undefined') {
                console.error('Google Maps API timeout');
                document.getElementById('status').innerHTML = 'Google Maps API timeout - API key may be invalid';
                document.getElementById('status').className = 'status error';
            }
        }, 10000);
    </script>

    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&onerror=handleMapError">
    </script>
</body>
</html>
