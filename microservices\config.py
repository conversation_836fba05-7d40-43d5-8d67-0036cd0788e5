"""
Microservices Configuration and Service Discovery
Manages microservices architecture for Shrimp Farm Guardian
"""

import os
import json
import consul
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import requests
import time
from django.conf import settings

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    STARTING = "starting"
    STOPPING = "stopping"
    UNKNOWN = "unknown"

@dataclass
class ServiceConfig:
    """Configuration for a microservice"""
    name: str
    host: str
    port: int
    protocol: str = "http"
    health_check_path: str = "/health/"
    version: str = "1.0.0"
    tags: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def url(self) -> str:
        return f"{self.protocol}://{self.host}:{self.port}"
    
    @property
    def health_url(self) -> str:
        return f"{self.url}{self.health_check_path}"

class ServiceRegistry:
    """
    Service registry for microservices discovery and health monitoring
    """
    
    def __init__(self, consul_host: str = "localhost", consul_port: int = 8500):
        self.consul_client = consul.Consul(host=consul_host, port=consul_port)
        self.services: Dict[str, ServiceConfig] = {}
        self.service_health: Dict[str, ServiceStatus] = {}
        
        # Load service configurations
        self._load_service_configs()
    
    def _load_service_configs(self):
        """Load microservice configurations"""
        # Core services configuration
        core_services = {
            "user-service": ServiceConfig(
                name="user-service",
                host=os.getenv("USER_SERVICE_HOST", "localhost"),
                port=int(os.getenv("USER_SERVICE_PORT", "8001")),
                tags=["authentication", "user-management"],
                metadata={"database": "users_db", "cache": "redis"}
            ),
            "pond-service": ServiceConfig(
                name="pond-service",
                host=os.getenv("POND_SERVICE_HOST", "localhost"),
                port=int(os.getenv("POND_SERVICE_PORT", "8002")),
                tags=["pond-management", "core"],
                metadata={"database": "ponds_db", "cache": "redis"}
            ),
            "water-quality-service": ServiceConfig(
                name="water-quality-service",
                host=os.getenv("WATER_QUALITY_SERVICE_HOST", "localhost"),
                port=int(os.getenv("WATER_QUALITY_SERVICE_PORT", "8003")),
                tags=["monitoring", "sensors", "iot"],
                metadata={"database": "water_quality_db", "timeseries": "influxdb"}
            ),
            "ai-ml-service": ServiceConfig(
                name="ai-ml-service",
                host=os.getenv("AI_ML_SERVICE_HOST", "localhost"),
                port=int(os.getenv("AI_ML_SERVICE_PORT", "8004")),
                tags=["machine-learning", "predictions", "analytics"],
                metadata={"model_storage": "s3", "training": "gpu"}
            ),
            "notification-service": ServiceConfig(
                name="notification-service",
                host=os.getenv("NOTIFICATION_SERVICE_HOST", "localhost"),
                port=int(os.getenv("NOTIFICATION_SERVICE_PORT", "8005")),
                tags=["notifications", "alerts", "messaging"],
                metadata={"queue": "rabbitmq", "email": "smtp"}
            ),
            "analytics-service": ServiceConfig(
                name="analytics-service",
                host=os.getenv("ANALYTICS_SERVICE_HOST", "localhost"),
                port=int(os.getenv("ANALYTICS_SERVICE_PORT", "8006")),
                tags=["analytics", "reporting", "business-intelligence"],
                metadata={"warehouse": "clickhouse", "cache": "redis"}
            ),
            "gateway-service": ServiceConfig(
                name="gateway-service",
                host=os.getenv("GATEWAY_SERVICE_HOST", "localhost"),
                port=int(os.getenv("GATEWAY_SERVICE_PORT", "8000")),
                tags=["api-gateway", "routing", "load-balancer"],
                metadata={"rate_limiting": "redis", "auth": "jwt"}
            )
        }
        
        self.services.update(core_services)
        logger.info(f"Loaded {len(core_services)} service configurations")
    
    def register_service(self, service: ServiceConfig) -> bool:
        """Register a service with Consul"""
        try:
            # Register with Consul
            self.consul_client.agent.service.register(
                name=service.name,
                service_id=f"{service.name}-{service.host}-{service.port}",
                address=service.host,
                port=service.port,
                tags=service.tags,
                check=consul.Check.http(
                    service.health_url,
                    interval="10s",
                    timeout="5s"
                ),
                meta=service.metadata
            )
            
            self.services[service.name] = service
            self.service_health[service.name] = ServiceStatus.STARTING
            
            logger.info(f"Registered service: {service.name} at {service.url}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to register service {service.name}: {e}")
            return False
    
    def deregister_service(self, service_name: str) -> bool:
        """Deregister a service from Consul"""
        try:
            if service_name in self.services:
                service = self.services[service_name]
                service_id = f"{service.name}-{service.host}-{service.port}"
                
                self.consul_client.agent.service.deregister(service_id)
                
                del self.services[service_name]
                if service_name in self.service_health:
                    del self.service_health[service_name]
                
                logger.info(f"Deregistered service: {service_name}")
                return True
            else:
                logger.warning(f"Service {service_name} not found for deregistration")
                return False
                
        except Exception as e:
            logger.error(f"Failed to deregister service {service_name}: {e}")
            return False
    
    def discover_service(self, service_name: str) -> Optional[ServiceConfig]:
        """Discover a service by name"""
        try:
            # Try local registry first
            if service_name in self.services:
                return self.services[service_name]
            
            # Query Consul for service
            _, services = self.consul_client.health.service(service_name, passing=True)
            
            if services:
                service_data = services[0]['Service']
                return ServiceConfig(
                    name=service_data['Service'],
                    host=service_data['Address'],
                    port=service_data['Port'],
                    tags=service_data.get('Tags', []),
                    metadata=service_data.get('Meta', {})
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to discover service {service_name}: {e}")
            return None
    
    def get_healthy_services(self) -> List[ServiceConfig]:
        """Get all healthy services"""
        healthy_services = []
        
        for service_name, service in self.services.items():
            if self.check_service_health(service_name):
                healthy_services.append(service)
        
        return healthy_services
    
    def check_service_health(self, service_name: str) -> bool:
        """Check health of a specific service"""
        if service_name not in self.services:
            return False
        
        service = self.services[service_name]
        
        try:
            response = requests.get(
                service.health_url,
                timeout=5,
                headers={'User-Agent': 'ServiceRegistry/1.0'}
            )
            
            is_healthy = response.status_code == 200
            self.service_health[service_name] = ServiceStatus.HEALTHY if is_healthy else ServiceStatus.UNHEALTHY
            
            return is_healthy
            
        except Exception as e:
            logger.warning(f"Health check failed for {service_name}: {e}")
            self.service_health[service_name] = ServiceStatus.UNHEALTHY
            return False
    
    def get_service_status(self, service_name: str) -> ServiceStatus:
        """Get current status of a service"""
        return self.service_health.get(service_name, ServiceStatus.UNKNOWN)
    
    def get_services_by_tag(self, tag: str) -> List[ServiceConfig]:
        """Get services by tag"""
        return [
            service for service in self.services.values()
            if tag in service.tags
        ]
    
    def get_load_balanced_service(self, service_name: str) -> Optional[ServiceConfig]:
        """Get a service instance using round-robin load balancing"""
        try:
            # Query Consul for all healthy instances
            _, services = self.consul_client.health.service(service_name, passing=True)
            
            if not services:
                return self.discover_service(service_name)
            
            # Simple round-robin (in production, use more sophisticated algorithms)
            import random
            service_data = random.choice(services)['Service']
            
            return ServiceConfig(
                name=service_data['Service'],
                host=service_data['Address'],
                port=service_data['Port'],
                tags=service_data.get('Tags', []),
                metadata=service_data.get('Meta', {})
            )
            
        except Exception as e:
            logger.error(f"Failed to get load balanced service {service_name}: {e}")
            return None
    
    def monitor_services(self, interval: int = 30):
        """Monitor all registered services"""
        logger.info("Starting service monitoring...")
        
        while True:
            try:
                for service_name in list(self.services.keys()):
                    self.check_service_health(service_name)
                
                # Log service status
                healthy_count = sum(1 for status in self.service_health.values() 
                                  if status == ServiceStatus.HEALTHY)
                total_count = len(self.services)
                
                logger.info(f"Service health: {healthy_count}/{total_count} services healthy")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                logger.info("Service monitoring stopped")
                break
            except Exception as e:
                logger.error(f"Service monitoring error: {e}")
                time.sleep(interval)
    
    def get_service_topology(self) -> Dict[str, Any]:
        """Get complete service topology"""
        topology = {
            "services": {},
            "health_summary": {
                "total": len(self.services),
                "healthy": 0,
                "unhealthy": 0,
                "unknown": 0
            },
            "service_groups": {}
        }
        
        # Build service details
        for service_name, service in self.services.items():
            status = self.get_service_status(service_name)
            
            topology["services"][service_name] = {
                "config": asdict(service),
                "status": status.value,
                "last_check": time.time()
            }
            
            # Update health summary
            if status == ServiceStatus.HEALTHY:
                topology["health_summary"]["healthy"] += 1
            elif status == ServiceStatus.UNHEALTHY:
                topology["health_summary"]["unhealthy"] += 1
            else:
                topology["health_summary"]["unknown"] += 1
        
        # Group services by tags
        for service in self.services.values():
            for tag in service.tags:
                if tag not in topology["service_groups"]:
                    topology["service_groups"][tag] = []
                topology["service_groups"][tag].append(service.name)
        
        return topology

# Global service registry instance
service_registry = ServiceRegistry(
    consul_host=os.getenv("CONSUL_HOST", "localhost"),
    consul_port=int(os.getenv("CONSUL_PORT", "8500"))
)
