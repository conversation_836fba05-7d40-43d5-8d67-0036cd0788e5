"""
Multi-Tenant Architecture Framework
Implements comprehensive multi-tenancy with data isolation,
tenant management, and resource allocation
"""

import logging
from typing import Dict, List, Optional, Any, Type
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.db import models, connection
from django.contrib.auth.models import User
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
import threading
import json
from enum import Enum

logger = logging.getLogger(__name__)

class TenantStatus(Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TRIAL = "trial"
    EXPIRED = "expired"
    PENDING = "pending"

class IsolationLevel(Enum):
    SHARED_DATABASE = "shared_db"
    SEPARATE_SCHEMA = "separate_schema"
    SEPARATE_DATABASE = "separate_database"

@dataclass
class TenantConfig:
    """Tenant configuration and metadata"""
    tenant_id: str
    name: str
    domain: str
    status: TenantStatus
    isolation_level: IsolationLevel
    created_at: datetime
    subscription_plan: str
    max_users: int
    max_ponds: int
    max_storage_gb: int
    features_enabled: List[str]
    custom_settings: Dict[str, Any]
    billing_info: Dict[str, Any]
    
    def __post_init__(self):
        if isinstance(self.status, str):
            self.status = TenantStatus(self.status)
        if isinstance(self.isolation_level, str):
            self.isolation_level = IsolationLevel(self.isolation_level)

class TenantContext:
    """Thread-local tenant context"""
    _local = threading.local()
    
    @classmethod
    def set_current_tenant(cls, tenant_id: str):
        """Set current tenant for thread"""
        cls._local.tenant_id = tenant_id
    
    @classmethod
    def get_current_tenant(cls) -> Optional[str]:
        """Get current tenant for thread"""
        return getattr(cls._local, 'tenant_id', None)
    
    @classmethod
    def clear_tenant(cls):
        """Clear current tenant"""
        if hasattr(cls._local, 'tenant_id'):
            delattr(cls._local, 'tenant_id')

class TenantManager:
    """
    Multi-Tenant Manager
    Handles tenant lifecycle, data isolation, and resource management
    """
    
    def __init__(self):
        self.tenants: Dict[str, TenantConfig] = {}
        self.tenant_databases: Dict[str, str] = {}
        self.resource_usage: Dict[str, Dict[str, Any]] = {}
        
        # Load existing tenants
        self._load_tenants()
        
        # Setup tenant isolation
        self._setup_tenant_isolation()
    
    def _load_tenants(self):
        """Load tenant configurations from database"""
        try:
            # In production, load from tenant configuration table
            # For now, create sample tenants
            sample_tenants = {
                "acme-farms": TenantConfig(
                    tenant_id="acme-farms",
                    name="ACME Shrimp Farms",
                    domain="acme.shrimp-farm.com",
                    status=TenantStatus.ACTIVE,
                    isolation_level=IsolationLevel.SEPARATE_SCHEMA,
                    created_at=datetime.now(),
                    subscription_plan="enterprise",
                    max_users=100,
                    max_ponds=500,
                    max_storage_gb=1000,
                    features_enabled=["advanced_analytics", "ml_predictions", "api_access"],
                    custom_settings={"timezone": "UTC", "currency": "USD"},
                    billing_info={"plan": "enterprise", "monthly_cost": 999}
                ),
                "coastal-aqua": TenantConfig(
                    tenant_id="coastal-aqua",
                    name="Coastal Aquaculture",
                    domain="coastal.shrimp-farm.com",
                    status=TenantStatus.ACTIVE,
                    isolation_level=IsolationLevel.SHARED_DATABASE,
                    created_at=datetime.now(),
                    subscription_plan="professional",
                    max_users=25,
                    max_ponds=100,
                    max_storage_gb=250,
                    features_enabled=["basic_analytics", "mobile_app"],
                    custom_settings={"timezone": "America/New_York", "currency": "USD"},
                    billing_info={"plan": "professional", "monthly_cost": 299}
                ),
                "demo-tenant": TenantConfig(
                    tenant_id="demo-tenant",
                    name="Demo Tenant",
                    domain="demo.shrimp-farm.com",
                    status=TenantStatus.TRIAL,
                    isolation_level=IsolationLevel.SHARED_DATABASE,
                    created_at=datetime.now(),
                    subscription_plan="trial",
                    max_users=5,
                    max_ponds=10,
                    max_storage_gb=10,
                    features_enabled=["basic_features"],
                    custom_settings={"timezone": "UTC", "currency": "USD"},
                    billing_info={"plan": "trial", "trial_expires": (datetime.now() + timedelta(days=30)).isoformat()}
                )
            }
            
            self.tenants.update(sample_tenants)
            logger.info(f"Loaded {len(sample_tenants)} tenant configurations")
            
        except Exception as e:
            logger.error(f"Failed to load tenants: {e}")
    
    def _setup_tenant_isolation(self):
        """Setup database isolation for tenants"""
        for tenant_id, config in self.tenants.items():
            if config.isolation_level == IsolationLevel.SEPARATE_DATABASE:
                # Setup separate database
                db_name = f"tenant_{tenant_id.replace('-', '_')}"
                self.tenant_databases[tenant_id] = db_name
                self._create_tenant_database(tenant_id, db_name)
            
            elif config.isolation_level == IsolationLevel.SEPARATE_SCHEMA:
                # Setup separate schema
                schema_name = f"tenant_{tenant_id.replace('-', '_')}"
                self._create_tenant_schema(tenant_id, schema_name)
    
    def create_tenant(self, tenant_config: TenantConfig) -> bool:
        """Create new tenant with proper isolation"""
        try:
            # Validate tenant configuration
            if not self._validate_tenant_config(tenant_config):
                return False
            
            # Check if tenant already exists
            if tenant_config.tenant_id in self.tenants:
                logger.error(f"Tenant {tenant_config.tenant_id} already exists")
                return False
            
            # Setup data isolation
            if tenant_config.isolation_level == IsolationLevel.SEPARATE_DATABASE:
                db_name = f"tenant_{tenant_config.tenant_id.replace('-', '_')}"
                if not self._create_tenant_database(tenant_config.tenant_id, db_name):
                    return False
                self.tenant_databases[tenant_config.tenant_id] = db_name
            
            elif tenant_config.isolation_level == IsolationLevel.SEPARATE_SCHEMA:
                schema_name = f"tenant_{tenant_config.tenant_id.replace('-', '_')}"
                if not self._create_tenant_schema(tenant_config.tenant_id, schema_name):
                    return False
            
            # Initialize tenant resources
            self._initialize_tenant_resources(tenant_config)
            
            # Store tenant configuration
            self.tenants[tenant_config.tenant_id] = tenant_config
            self._persist_tenant_config(tenant_config)
            
            # Setup tenant-specific settings
            self._setup_tenant_settings(tenant_config)
            
            logger.info(f"Created tenant: {tenant_config.tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create tenant {tenant_config.tenant_id}: {e}")
            return False
    
    def _validate_tenant_config(self, config: TenantConfig) -> bool:
        """Validate tenant configuration"""
        if not config.tenant_id or not config.name or not config.domain:
            return False
        
        # Check domain uniqueness
        for existing_tenant in self.tenants.values():
            if existing_tenant.domain == config.domain:
                return False
        
        return True
    
    def _create_tenant_database(self, tenant_id: str, db_name: str) -> bool:
        """Create separate database for tenant"""
        try:
            with connection.cursor() as cursor:
                # Create database
                cursor.execute(f'CREATE DATABASE "{db_name}"')
                
                # Run migrations on new database
                # In production, use Django management commands
                logger.info(f"Created database {db_name} for tenant {tenant_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create database for tenant {tenant_id}: {e}")
            return False
    
    def _create_tenant_schema(self, tenant_id: str, schema_name: str) -> bool:
        """Create separate schema for tenant"""
        try:
            with connection.cursor() as cursor:
                # Create schema
                cursor.execute(f'CREATE SCHEMA IF NOT EXISTS "{schema_name}"')
                
                # Create tenant-specific tables in schema
                # This would involve creating all tables with schema prefix
                logger.info(f"Created schema {schema_name} for tenant {tenant_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to create schema for tenant {tenant_id}: {e}")
            return False
    
    def _initialize_tenant_resources(self, config: TenantConfig):
        """Initialize tenant-specific resources"""
        self.resource_usage[config.tenant_id] = {
            "users": 0,
            "ponds": 0,
            "storage_used_gb": 0,
            "api_calls_today": 0,
            "last_activity": datetime.now().isoformat()
        }
    
    def _persist_tenant_config(self, config: TenantConfig):
        """Persist tenant configuration to database"""
        try:
            # In production, save to tenant configuration table
            cache_key = f"tenant_config:{config.tenant_id}"
            cache.set(cache_key, asdict(config), 86400)  # 24 hours
            
        except Exception as e:
            logger.error(f"Failed to persist tenant config: {e}")
    
    def _setup_tenant_settings(self, config: TenantConfig):
        """Setup tenant-specific application settings"""
        try:
            # Configure tenant-specific features
            tenant_settings = {
                "features": config.features_enabled,
                "limits": {
                    "max_users": config.max_users,
                    "max_ponds": config.max_ponds,
                    "max_storage_gb": config.max_storage_gb
                },
                "custom_settings": config.custom_settings
            }
            
            cache_key = f"tenant_settings:{config.tenant_id}"
            cache.set(cache_key, tenant_settings, 86400)
            
        except Exception as e:
            logger.error(f"Failed to setup tenant settings: {e}")
    
    def get_tenant(self, tenant_id: str) -> Optional[TenantConfig]:
        """Get tenant configuration"""
        return self.tenants.get(tenant_id)
    
    def get_tenant_by_domain(self, domain: str) -> Optional[TenantConfig]:
        """Get tenant by domain"""
        for tenant in self.tenants.values():
            if tenant.domain == domain:
                return tenant
        return None
    
    def update_tenant(self, tenant_id: str, updates: Dict[str, Any]) -> bool:
        """Update tenant configuration"""
        try:
            if tenant_id not in self.tenants:
                return False
            
            tenant = self.tenants[tenant_id]
            
            # Update configuration
            for key, value in updates.items():
                if hasattr(tenant, key):
                    setattr(tenant, key, value)
            
            # Persist changes
            self._persist_tenant_config(tenant)
            self._setup_tenant_settings(tenant)
            
            logger.info(f"Updated tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update tenant {tenant_id}: {e}")
            return False
    
    def suspend_tenant(self, tenant_id: str, reason: str) -> bool:
        """Suspend tenant access"""
        try:
            if tenant_id not in self.tenants:
                return False
            
            self.tenants[tenant_id].status = TenantStatus.SUSPENDED
            self._persist_tenant_config(self.tenants[tenant_id])
            
            # Log suspension
            self._log_tenant_event(tenant_id, "suspended", {"reason": reason})
            
            logger.info(f"Suspended tenant {tenant_id}: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to suspend tenant {tenant_id}: {e}")
            return False
    
    def activate_tenant(self, tenant_id: str) -> bool:
        """Activate suspended tenant"""
        try:
            if tenant_id not in self.tenants:
                return False
            
            self.tenants[tenant_id].status = TenantStatus.ACTIVE
            self._persist_tenant_config(self.tenants[tenant_id])
            
            # Log activation
            self._log_tenant_event(tenant_id, "activated", {})
            
            logger.info(f"Activated tenant {tenant_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to activate tenant {tenant_id}: {e}")
            return False
    
    def check_tenant_limits(self, tenant_id: str, resource_type: str, 
                           requested_amount: int = 1) -> Tuple[bool, str]:
        """Check if tenant can use additional resources"""
        try:
            tenant = self.tenants.get(tenant_id)
            if not tenant:
                return False, "Tenant not found"
            
            if tenant.status != TenantStatus.ACTIVE:
                return False, f"Tenant status: {tenant.status.value}"
            
            usage = self.resource_usage.get(tenant_id, {})
            
            # Check specific resource limits
            if resource_type == "users":
                current = usage.get("users", 0)
                limit = tenant.max_users
                if current + requested_amount > limit:
                    return False, f"User limit exceeded ({current}/{limit})"
            
            elif resource_type == "ponds":
                current = usage.get("ponds", 0)
                limit = tenant.max_ponds
                if current + requested_amount > limit:
                    return False, f"Pond limit exceeded ({current}/{limit})"
            
            elif resource_type == "storage":
                current = usage.get("storage_used_gb", 0)
                limit = tenant.max_storage_gb
                if current + requested_amount > limit:
                    return False, f"Storage limit exceeded ({current}GB/{limit}GB)"
            
            return True, "OK"
            
        except Exception as e:
            logger.error(f"Failed to check tenant limits: {e}")
            return False, "Limit check error"
    
    def update_resource_usage(self, tenant_id: str, resource_type: str, 
                            amount: int, operation: str = "add"):
        """Update tenant resource usage"""
        try:
            if tenant_id not in self.resource_usage:
                self.resource_usage[tenant_id] = {
                    "users": 0, "ponds": 0, "storage_used_gb": 0,
                    "api_calls_today": 0, "last_activity": datetime.now().isoformat()
                }
            
            usage = self.resource_usage[tenant_id]
            
            if operation == "add":
                usage[resource_type] = usage.get(resource_type, 0) + amount
            elif operation == "subtract":
                usage[resource_type] = max(0, usage.get(resource_type, 0) - amount)
            elif operation == "set":
                usage[resource_type] = amount
            
            usage["last_activity"] = datetime.now().isoformat()
            
            # Persist usage data
            cache_key = f"tenant_usage:{tenant_id}"
            cache.set(cache_key, usage, 86400)
            
        except Exception as e:
            logger.error(f"Failed to update resource usage: {e}")
    
    def get_tenant_analytics(self, tenant_id: str) -> Dict[str, Any]:
        """Get tenant analytics and usage statistics"""
        try:
            tenant = self.tenants.get(tenant_id)
            if not tenant:
                return {}
            
            usage = self.resource_usage.get(tenant_id, {})
            
            return {
                "tenant_info": {
                    "name": tenant.name,
                    "status": tenant.status.value,
                    "plan": tenant.subscription_plan,
                    "created_at": tenant.created_at.isoformat()
                },
                "resource_usage": usage,
                "limits": {
                    "max_users": tenant.max_users,
                    "max_ponds": tenant.max_ponds,
                    "max_storage_gb": tenant.max_storage_gb
                },
                "utilization": {
                    "users_percent": (usage.get("users", 0) / tenant.max_users) * 100,
                    "ponds_percent": (usage.get("ponds", 0) / tenant.max_ponds) * 100,
                    "storage_percent": (usage.get("storage_used_gb", 0) / tenant.max_storage_gb) * 100
                },
                "features_enabled": tenant.features_enabled,
                "billing_info": tenant.billing_info
            }
            
        except Exception as e:
            logger.error(f"Failed to get tenant analytics: {e}")
            return {}
    
    def _log_tenant_event(self, tenant_id: str, event_type: str, data: Dict[str, Any]):
        """Log tenant events for audit trail"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "tenant_id": tenant_id,
            "event_type": event_type,
            "data": data
        }
        
        # In production, send to audit log system
        logger.info(f"Tenant event: {json.dumps(event)}")
    
    def get_all_tenants(self) -> List[TenantConfig]:
        """Get all tenant configurations"""
        return list(self.tenants.values())
    
    def get_tenant_dashboard(self) -> Dict[str, Any]:
        """Get multi-tenant management dashboard data"""
        total_tenants = len(self.tenants)
        active_tenants = sum(1 for t in self.tenants.values() if t.status == TenantStatus.ACTIVE)
        trial_tenants = sum(1 for t in self.tenants.values() if t.status == TenantStatus.TRIAL)
        
        return {
            "summary": {
                "total_tenants": total_tenants,
                "active_tenants": active_tenants,
                "trial_tenants": trial_tenants,
                "suspended_tenants": total_tenants - active_tenants - trial_tenants
            },
            "resource_utilization": self._calculate_global_utilization(),
            "recent_activity": self._get_recent_tenant_activity(),
            "billing_summary": self._get_billing_summary()
        }
    
    def _calculate_global_utilization(self) -> Dict[str, Any]:
        """Calculate global resource utilization across all tenants"""
        total_users = sum(usage.get("users", 0) for usage in self.resource_usage.values())
        total_ponds = sum(usage.get("ponds", 0) for usage in self.resource_usage.values())
        total_storage = sum(usage.get("storage_used_gb", 0) for usage in self.resource_usage.values())
        
        return {
            "total_users": total_users,
            "total_ponds": total_ponds,
            "total_storage_gb": total_storage,
            "average_utilization": {
                "users": total_users / len(self.tenants) if self.tenants else 0,
                "ponds": total_ponds / len(self.tenants) if self.tenants else 0,
                "storage": total_storage / len(self.tenants) if self.tenants else 0
            }
        }
    
    def _get_recent_tenant_activity(self) -> List[Dict[str, Any]]:
        """Get recent tenant activity"""
        # In production, query from activity log
        return [
            {
                "tenant_id": "acme-farms",
                "activity": "User login",
                "timestamp": datetime.now().isoformat()
            },
            {
                "tenant_id": "coastal-aqua",
                "activity": "Pond created",
                "timestamp": (datetime.now() - timedelta(minutes=30)).isoformat()
            }
        ]
    
    def _get_billing_summary(self) -> Dict[str, Any]:
        """Get billing summary across all tenants"""
        total_revenue = sum(
            tenant.billing_info.get("monthly_cost", 0)
            for tenant in self.tenants.values()
            if tenant.status == TenantStatus.ACTIVE
        )
        
        return {
            "total_monthly_revenue": total_revenue,
            "active_subscriptions": len([t for t in self.tenants.values() if t.status == TenantStatus.ACTIVE]),
            "trial_conversions_needed": len([t for t in self.tenants.values() if t.status == TenantStatus.TRIAL])
        }

# Global tenant manager instance
tenant_manager = TenantManager()
