<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Shrimp Farm Field Worker App - Mobile Simulator</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .simulator-wrapper {
            display: flex;
            gap: 30px;
            align-items: flex-start;
            max-width: 1200px;
        }

        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #1976D2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .app-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f5f5f5;
        }

        .header-section {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            color: white;
        }

        .greeting { font-size: 16px; opacity: 0.9; margin-bottom: 4px; }
        .user-name { font-size: 24px; font-weight: 700; margin-bottom: 4px; }
        .date-info { font-size: 14px; opacity: 0.8; }

        .status-indicator {
            background: rgba(255, 152, 0, 0.9);
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-top: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .search-area {
            padding: 16px;
            background: white;
        }

        .search-input {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            font-size: 16px;
            width: 100%;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 16px;
        }

        .metric-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .metric-card:hover { transform: translateY(-2px); }
        .metric-icon { font-size: 32px; margin-bottom: 8px; }
        .metric-number { font-size: 28px; font-weight: 700; margin-bottom: 4px; }
        .metric-label { font-size: 12px; color: #666; font-weight: 600; }

        .progress-section {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-title { font-size: 16px; font-weight: 600; }
        .progress-percentage { font-size: 24px; font-weight: 700; color: #4CAF50; }

        .progress-bar {
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            margin-bottom: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 85%;
            border-radius: 4px;
            animation: fillProgress 2s ease-out;
        }

        @keyframes fillProgress {
            from { width: 0%; }
            to { width: 85%; }
        }

        .actions-section { padding: 0 16px 16px; }
        .section-header { font-size: 20px; font-weight: 700; margin-bottom: 16px; padding: 0 4px; }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .action-item {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .action-icon { font-size: 24px; color: #2196F3; margin-bottom: 8px; }
        .action-label { font-size: 14px; font-weight: 600; color: #333; }

        .tasks-section { padding: 0 16px 16px; }

        .task-item {
            background: white;
            margin-bottom: 8px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #F44336;
            cursor: pointer;
            transition: all 0.2s;
        }

        .task-item:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .task-name { font-size: 16px; font-weight: 600; color: #333; flex: 1; }

        .priority-tag {
            background: #F44336;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }

        .task-info { font-size: 12px; color: #666; margin-bottom: 12px; }

        .task-buttons {
            display: flex;
            gap: 8px;
        }

        .task-button {
            padding: 8px 12px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s;
        }

        .btn-primary { background: #2196F3; color: white; }
        .btn-danger { background: #F44336; color: white; }

        .navigation-bar {
            height: 80px;
            background: white;
            display: flex;
            border-top: 1px solid #e0e0e0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }

        .nav-button {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .nav-button:hover { background: #f5f5f5; }
        .nav-button.active { color: #2196F3; }
        .nav-button:not(.active) { color: #757575; }
        .nav-icon { font-size: 24px; margin-bottom: 4px; }
        .nav-text { font-size: 12px; font-weight: 600; }

        .controls-panel {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 320px;
        }

        .controls-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
        }

        .control-section {
            margin-bottom: 20px;
        }

        .control-heading {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 8px;
        }

        .control-button {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: #2196F3;
            color: white;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s;
        }

        .control-button:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        .control-button.secondary {
            background: #f5f5f5;
            color: #333;
        }

        .control-button.secondary:hover {
            background: #e0e0e0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        .feature-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .feature-description {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="simulator-wrapper">
        <!-- Mobile Phone Display -->
        <div class="phone-frame">
            <div class="phone-screen">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>🔋100% 📶 WiFi</div>
                    <div id="live-time">2:30 PM</div>
                </div>

                <!-- App Content -->
                <div class="app-content">
                    <div class="header-section">
                        <div class="greeting">Good Afternoon</div>
                        <div class="user-name">Field Worker</div>
                        <div class="date-info">Wednesday, January 17, 2025</div>
                        <div class="status-indicator">
                            🔄 Working Offline - 3 pending sync
                        </div>
                    </div>

                    <div class="search-area">
                        <div class="search-input">
                            🔍 <input type="text" placeholder="Search tasks..." style="border: none; background: none; outline: none; flex: 1;">
                        </div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-card" style="background: #E3F2FD;" onclick="showAlert('📅 Today: 12 tasks scheduled')">
                            <div class="metric-icon">📅</div>
                            <div class="metric-number">12</div>
                            <div class="metric-label">Today's Tasks</div>
                        </div>
                        <div class="metric-card" style="background: #FFF3E0;" onclick="showAlert('▶️ In Progress: 3 active tasks')">
                            <div class="metric-icon">▶️</div>
                            <div class="metric-number">3</div>
                            <div class="metric-label">In Progress</div>
                        </div>
                        <div class="metric-card" style="background: #E8F5E8;" onclick="showAlert('✅ Completed: 8 tasks finished today!')">
                            <div class="metric-icon">✅</div>
                            <div class="metric-number">8</div>
                            <div class="metric-label">Completed</div>
                        </div>
                        <div class="metric-card" style="background: #FFEBEE;" onclick="showAlert('⚠️ Overdue: 2 tasks need attention')">
                            <div class="metric-icon">⚠️</div>
                            <div class="metric-number">2</div>
                            <div class="metric-label">Overdue</div>
                        </div>
                    </div>

                    <div class="progress-section">
                        <div class="progress-header">
                            <div class="progress-title">Completion Rate</div>
                            <div class="progress-percentage">85%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div style="font-size: 12px; color: #666;">8 of 12 tasks completed</div>
                    </div>

                    <div class="actions-section">
                        <div class="section-header">Quick Actions</div>
                        <div class="actions-grid">
                            <div class="action-item" onclick="showAlert('📝 Opening task creation form...')">
                                <div class="action-icon">➕</div>
                                <div class="action-label">Create Task</div>
                            </div>
                            <div class="action-item" onclick="showAlert('📷 Activating QR scanner...')">
                                <div class="action-icon">📷</div>
                                <div class="action-label">Scan QR</div>
                            </div>
                            <div class="action-item" onclick="showAlert('💧 Starting water quality test...')">
                                <div class="action-icon">💧</div>
                                <div class="action-label">Water Test</div>
                            </div>
                            <div class="action-item" onclick="showAlert('📊 Loading performance reports...')">
                                <div class="action-icon">📊</div>
                                <div class="action-label">Reports</div>
                            </div>
                        </div>
                    </div>

                    <div class="tasks-section">
                        <div class="section-header">Priority Tasks</div>
                        <div class="task-item" onclick="showAlert('📋 Opening Water Quality Check details...')">
                            <div class="task-header">
                                <div class="task-name">💧 Water Quality Check</div>
                                <div class="priority-tag">URGENT</div>
                            </div>
                            <div class="task-info">📍 Pond A-1 • ⏰ Due: 14:00 • ⏱️ 30 min</div>
                            <div class="task-buttons">
                                <button class="task-button btn-primary" onclick="event.stopPropagation(); executeTask('Water Quality Check')">
                                    ▶️ Start
                                </button>
                                <button class="task-button btn-danger" onclick="event.stopPropagation(); cancelTask('Water Quality Check')">
                                    ❌ Cancel
                                </button>
                            </div>
                        </div>

                        <div class="task-item" style="border-left-color: #FF9800;" onclick="showAlert('🔧 Opening Equipment Maintenance details...')">
                            <div class="task-header">
                                <div class="task-name">🔧 Equipment Maintenance</div>
                                <div class="priority-tag" style="background: #FF9800;">HIGH</div>
                            </div>
                            <div class="task-info">📍 Pump Station • ⏰ Due: 16:30 • ⏱️ 1 hour</div>
                            <div class="task-buttons">
                                <button class="task-button btn-primary" onclick="event.stopPropagation(); executeTask('Equipment Maintenance')">
                                    ▶️ Start
                                </button>
                                <button class="task-button btn-danger" onclick="event.stopPropagation(); cancelTask('Equipment Maintenance')">
                                    ❌ Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom Navigation -->
                <div class="navigation-bar">
                    <div class="nav-button active">
                        <div class="nav-icon">📊</div>
                        <div class="nav-text">Dashboard</div>
                    </div>
                    <div class="nav-button" onclick="showAlert('📋 Task list loading...')">
                        <div class="nav-icon">📋</div>
                        <div class="nav-text">Tasks</div>
                    </div>
                    <div class="nav-button" onclick="showAlert('📷 QR Scanner activating...')">
                        <div class="nav-icon">📷</div>
                        <div class="nav-text">Scanner</div>
                    </div>
                    <div class="nav-button" onclick="showAlert('📈 Analytics loading...')">
                        <div class="nav-icon">📈</div>
                        <div class="nav-text">Reports</div>
                    </div>
                    <div class="nav-button" onclick="showAlert('👤 Profile opening...')">
                        <div class="nav-icon">👤</div>
                        <div class="nav-text">Profile</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Panel -->
        <div class="controls-panel">
            <div class="controls-title">🎮 Interactive Demo Controls</div>
            
            <div class="feature-box">
                <div class="feature-title">📱 Live Mobile App Demo</div>
                <div class="feature-description">Click any element in the phone screen to interact with the app!</div>
            </div>

            <div class="control-section">
                <div class="control-heading">🎯 Task Actions</div>
                <button class="control-button" onclick="simulateTaskStart()">▶️ Start Priority Task</button>
                <button class="control-button" onclick="simulateTaskComplete()">✅ Complete Task</button>
                <button class="control-button secondary" onclick="simulateDataSync()">🔄 Sync All Data</button>
            </div>

            <div class="control-section">
                <div class="control-heading">📱 App Features</div>
                <button class="control-button" onclick="demoOfflineMode()">🔄 Offline Capabilities</button>
                <button class="control-button" onclick="demoPhotoCapture()">📷 Photo Capture</button>
                <button class="control-button" onclick="demoGPSTracking()">📍 GPS Tracking</button>
                <button class="control-button secondary" onclick="demoAnalytics()">📈 Real-time Analytics</button>
            </div>

            <div class="control-section">
                <div class="control-heading">🔧 Simulation Tools</div>
                <button class="control-button secondary" onclick="toggleNetworkMode()">📶 Toggle Network</button>
                <button class="control-button secondary" onclick="addSampleTask()">➕ Add New Task</button>
                <button class="control-button secondary" onclick="showAppDetails()">ℹ️ App Information</button>
            </div>

            <div style="margin-top: 20px; padding: 16px; background: #f8f8f8; border-radius: 8px; font-size: 12px; color: #666;">
                <strong>💡 Interactive Demo:</strong> This simulator demonstrates all key features of the Shrimp Farm Field Worker mobile app. Every element is clickable and responsive!
            </div>
        </div>
    </div>

    <script>
        // Live time update
        function updateClock() {
            const now = new Date();
            const timeStr = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            document.getElementById('live-time').textContent = timeStr;
        }
        setInterval(updateClock, 1000);
        updateClock();

        function showAlert(message) {
            const alert = document.createElement('div');
            alert.className = 'notification';
            alert.innerHTML = message.replace(/\n/g, '<br>');
            document.body.appendChild(alert);
            setTimeout(() => alert.remove(), 4000);
        }

        function executeTask(taskName) {
            showAlert(`🚀 Starting "${taskName}"\n⏱️ Timer activated\n📍 Location recorded\n📱 Form opened`);
            setTimeout(() => showAlert(`✅ "${taskName}" is now in progress`), 2000);
        }

        function cancelTask(taskName) {
            showAlert(`❌ "${taskName}" cancelled\n📝 Reason logged\n🔄 Sync queued`);
        }

        function simulateTaskStart() {
            showAlert(`🎯 Task simulation started\n📱 Opening completion form\n📸 Camera ready\n📊 Data fields loaded`);
        }

        function simulateTaskComplete() {
            showAlert(`✅ Task completed!\n📸 3 photos captured\n📊 Measurements recorded\n🔄 Syncing...`);
            setTimeout(() => showAlert(`☁️ Sync successful!\n📈 Dashboard updated\n🏆 Great work!`), 3000);
        }

        function simulateDataSync() {
            showAlert(`🔄 Syncing data...\n📤 3 tasks uploading\n📸 12 photos syncing\n📊 Updating metrics`);
            setTimeout(() => {
                showAlert(`✅ Sync complete!\n📱 All data current\n🌐 Online mode active`);
                const indicator = document.querySelector('.status-indicator');
                indicator.innerHTML = '✅ All data synchronized';
                indicator.style.background = 'rgba(76, 175, 80, 0.9)';
            }, 4000);
        }

        function demoOfflineMode() {
            showAlert(`🔄 Offline Mode Demo:\n• SQLite local storage\n• Auto-sync when online\n• Photo caching\n• Conflict resolution\n• 99.9% data integrity`);
        }

        function demoPhotoCapture() {
            showAlert(`📷 Photo Capture Demo:\n• High-quality capture\n• Auto-compression\n• GPS location tagging\n• Offline storage\n• Batch upload`);
        }

        function demoGPSTracking() {
            showAlert(`📍 GPS Tracking Demo:\n• Auto location logging\n• Pond geofencing\n• Route optimization\n• Location-based tasks\n• Real-time tracking`);
        }

        function demoAnalytics() {
            showAlert(`📈 Analytics Demo:\n• Real-time metrics\n• Performance trends\n• Productivity insights\n• Custom reports\n• Predictive analytics`);
        }

        function toggleNetworkMode() {
            const indicator = document.querySelector('.status-indicator');
            const isOffline = indicator.textContent.includes('Offline');
            
            if (isOffline) {
                indicator.innerHTML = '🌐 Online - Connected';
                indicator.style.background = 'rgba(76, 175, 80, 0.9)';
                showAlert('📶 Network restored!\n🔄 Auto-sync active\n☁️ Cloud features enabled');
            } else {
                indicator.innerHTML = '🔄 Working Offline - 3 pending';
                indicator.style.background = 'rgba(255, 152, 0, 0.9)';
                showAlert('📱 Offline mode\n💾 Local storage active\n🔄 Changes queued');
            }
        }

        function addSampleTask() {
            showAlert(`➕ Creating task...\n📝 "Feed Quality Check"\n🎯 Priority: High\n📍 Pond B-3\n⏰ Due: 18:00`);
            setTimeout(() => showAlert(`✅ Task created!\n🔔 Notification sent\n📊 Dashboard updated`), 3000);
        }

        function showAppDetails() {
            showAlert(`📱 Shrimp Farm Field Worker\n🔧 Version: 1.0.0\n⚡ React Native\n💾 Offline-first\n🔐 Enterprise security\n📊 Real-time analytics\n🎯 99.9% reliability`);
        }

        // Touch feedback
        document.addEventListener('click', function(e) {
            const clickable = e.target.closest('.action-item, .task-button, .nav-button, .metric-card, .control-button');
            if (clickable) {
                clickable.style.transform = 'scale(0.95)';
                setTimeout(() => clickable.style.transform = 'scale(1)', 150);
            }
        });

        // Welcome message
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                showAlert(`🎉 Welcome to the Interactive Demo!\n📱 Shrimp Farm Field Worker App\n🎮 Click any element to explore`);
            }, 1000);
        });
    </script>
</body>
</html>
