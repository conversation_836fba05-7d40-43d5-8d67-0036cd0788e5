{% extends 'base.html' %}
{% load static %}

{% block title %}Phase 11: Revolutionary Shrimp Farming Technologies - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .revolutionary-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff6b6b 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .revolutionary-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: quantum-sweep 3s infinite;
    }
    
    @keyframes quantum-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .revolutionary-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 25px;
        border: 2px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }
    
    .revolutionary-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        border-color: rgba(255,255,255,0.4);
    }
    
    .revolutionary-badge {
        position: absolute;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9em;
        font-weight: bold;
        animation: pulse-glow 2s infinite;
    }
    
    @keyframes pulse-glow {
        0%, 100% { 
            opacity: 1; 
            box-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        50% { 
            opacity: 0.8; 
            box-shadow: 0 0 30px rgba(255,255,255,0.8);
        }
    }
    
    .quantum-badge { background: linear-gradient(45deg, #667eea, #764ba2); }
    .genetic-badge { background: linear-gradient(45deg, #00c851, #007e33); }
    .global-badge { background: linear-gradient(45deg, #33b5e5, #0099cc); }
    .neural-badge { background: linear-gradient(45deg, #ff4444, #cc0000); }
    .nano-badge { background: linear-gradient(45deg, #aa66cc, #9933cc); }
    .marine-badge { background: linear-gradient(45deg, #00bcd4, #0097a7); }
    .space-badge { background: linear-gradient(45deg, #424242, #212121); }
    
    .tech-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
        margin-top: 40px;
    }
    
    .feature-icon {
        font-size: 3em;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(116, 185, 255, 0.5);
    }
    
    .revolutionary-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 40px 0;
    }
    
    .stat-card {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        border: 1px solid rgba(255,255,255,0.2);
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .breakthrough-timeline {
        background: linear-gradient(45deg, #1e3c72, #2a5298);
        border-radius: 20px;
        padding: 30px;
        margin-top: 40px;
    }
    
    .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        border-left: 4px solid #74b9ff;
    }
    
    .timeline-icon {
        font-size: 1.5em;
        margin-right: 15px;
        color: #74b9ff;
    }
    
    .futuristic-button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .futuristic-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.6);
        background: linear-gradient(45deg, #764ba2, #667eea);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Revolutionary Header -->
    <div class="revolutionary-header">
        <div class="position-relative">
            <h1><i class="fas fa-rocket me-3"></i>Phase 11: Revolutionary Shrimp Farming Technologies</h1>
            <p class="lead mb-0">Next-Generation Features Beyond Current Technology</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark me-2">🌟 QUANTUM READY</span>
                <span class="badge bg-light text-dark me-2">🧬 GENETIC OPTIMIZATION</span>
                <span class="badge bg-light text-dark me-2">🌍 GLOBAL NETWORK</span>
                <span class="badge bg-light text-dark me-2">🧠 NEURAL INTERFACES</span>
                <span class="badge bg-light text-dark">🚀 SPACE READY</span>
            </div>
        </div>
    </div>

    <!-- Revolutionary Statistics -->
    <div class="revolutionary-stats">
        <div class="stat-card">
            <div class="stat-number">7</div>
            <h5>Revolutionary Modules</h5>
            <p>Next-Gen Technologies</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">∞</div>
            <h5>Quantum Advantage</h5>
            <p>Exponential Speedup</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <h5>Genetic Precision</h5>
            <p>CRISPR Optimization</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">🌍</div>
            <h5>Global Scale</h5>
            <p>Planetary Network</p>
        </div>
    </div>

    <!-- Revolutionary Technologies Grid -->
    <div class="tech-grid">
    </div>

    <!-- Breakthrough Timeline -->
    <div class="breakthrough-timeline">
        <h3><i class="fas fa-timeline me-2"></i>Revolutionary Breakthrough Timeline</h3>
        <p class="mb-4">The evolution of shrimp farming technology beyond current limitations</p>
        
        <p class="text-center mt-4">Timeline content has been simplified for core functionality focus.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate revolutionary cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.revolutionary-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Add quantum effects to badges
    document.querySelectorAll('.quantum-badge').forEach(badge => {
        setInterval(() => {
            const hue = Math.random() * 360;
            badge.style.background = `linear-gradient(45deg, hsl(${hue}, 70%, 60%), hsl(${(hue + 60) % 360}, 70%, 60%))`;
        }, 2000);
    });
    
    // Futuristic button effects
    document.querySelectorAll('.futuristic-button').forEach(button => {
        button.addEventListener('click', function() {
            // Create ripple effect
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255,255,255,0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Add ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
