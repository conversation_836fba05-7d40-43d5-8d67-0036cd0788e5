{% extends "base.html" %}
{% load static %}

{% block title %}Fixed Cumulative Map Dashboard{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #2563eb;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
    }

    .dashboard-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    }

    .stat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }

    .stat-icon.farms { background: linear-gradient(135deg, #667eea, #764ba2); }
    .stat-icon.ponds { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .stat-icon.aerators { background: linear-gradient(135deg, #4facfe, #00f2fe); }
    .stat-icon.area { background: linear-gradient(135deg, #43e97b, #38f9d7); }
    .stat-icon.workers { background: linear-gradient(135deg, #8B5CF6, #A855F7); }
    .stat-icon.geofences { background: linear-gradient(135deg, #F59E0B, #F97316); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .stat-change {
        font-size: 0.8rem;
        margin-top: 8px;
        padding: 4px 8px;
        border-radius: 20px;
        display: inline-block;
        background: #dcfce7;
        color: #166534;
    }    .map-container {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        position: relative;
        min-height: 1080px; /* 1000px map + 80px padding/header */
        height: auto;
    }

    .map-header {
        background: linear-gradient(135deg, var(--primary-color), #1e40af);
        color: white;
        padding: 20px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .map-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .map-controls {
        display: flex;
        gap: 10px;
    }

    .map-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .map-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    .map-btn.active {
        background: white;
        color: var(--primary-color);
    }    #map {
        height: 1000px;
        width: 100%;
    }

    .debug-panel {
        position: absolute;
        top: 10px;
        right: 10px;
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        max-width: 300px;
        z-index: 1000;
        font-size: 12px;
        max-height: 250px;
        overflow-y: auto;
    }

    .legend-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .legend-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .legend-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px;
        background: #f8fafc;
        border-radius: 10px;
        transition: background 0.3s ease;
    }

    .legend-item:hover {
        background: #e2e8f0;
    }

    .legend-marker {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .legend-marker.farm { background: #2563eb; }
    .legend-marker.pond-active { background: #10b981; }
    .legend-marker.pond-maintenance { background: #f59e0b; }
    .legend-marker.pond-empty { background: #6b7280; }
    .legend-marker.pond-harvested { background: #8b5cf6; }
    .legend-marker.worker { background: #8B5CF6; }
    .legend-marker.geofence { background: #F59E0B; }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px;
        }
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        #map {
            height: 400px;
        }
        .debug-panel {
            max-width: 250px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container fixed-cumulative">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="text-white mb-2">
                <i class="fas fa-globe-americas"></i> Fixed Cumulative Map Dashboard
            </h1>
            <p class="text-white-50 mb-0">Integrated labor management with farm and pond mapping</p>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon farms">
                    <i class="fas fa-industry"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Total Farms</div>
            <div class="stat-change">
                <i class="fas fa-arrow-up"></i> {{ farms_with_location }} with GPS
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon ponds">
                    <i class="fas fa-water"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
            <div class="stat-change">
                <i class="fas fa-check-circle"></i> {{ active_ponds }} Active
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon aerators">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_aerators }}</div>
            <div class="stat-label">Total Aerators</div>
            <div class="stat-change">
                <i class="fas fa-bolt"></i> {{ active_aerators }} Active
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon area">
                    <i class="fas fa-expand-arrows-alt"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_area|floatformat:0 }}</div>
            <div class="stat-label">Total Area (m²)</div>
            <div class="stat-change">
                <i class="fas fa-map"></i> {{ ponds_with_location }} Mapped
            </div>
        </div>

        <!-- Labor Management Statistics -->
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon workers">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_workers|default:0 }}</div>
            <div class="stat-label">Total Workers</div>
            <div class="stat-change">
                <i class="fas fa-user-check"></i> {{ active_workers|default:0 }} Active
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon geofences">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_geofences|default:0 }}</div>
            <div class="stat-label">Geofences</div>
            <div class="stat-change">
                <i class="fas fa-shield-alt"></i> Safety Zones
            </div>
        </div>
    </div>

    <!-- Interactive Map -->
    <div class="map-container">
        <div class="map-header">
            <h3 class="map-title">
                <i class="fas fa-map-marked-alt"></i> Interactive Farm & Labor Management Map
            </h3>
            <div class="map-controls">
                <button class="map-btn active" id="showAllBtn" onclick="toggleLayer('all')">
                    <i class="fas fa-eye"></i> Show All
                </button>
                <button class="map-btn" id="showFarmsBtn" onclick="toggleLayer('farms')">
                    <i class="fas fa-industry"></i> Farms
                </button>
                <button class="map-btn" id="showPondsBtn" onclick="toggleLayer('ponds')">
                    <i class="fas fa-water"></i> Ponds
                </button>
                <button class="map-btn" id="showWorkersBtn" onclick="toggleLayer('workers')">
                    <i class="fas fa-users"></i> Workers
                </button>
                <button class="map-btn" id="showGeofencesBtn" onclick="toggleLayer('geofences')">
                    <i class="fas fa-map-marked-alt"></i> Geofences
                </button>
                <button class="map-btn" id="satelliteBtn" onclick="toggleMapType()">
                    <i class="fas fa-satellite"></i> Satellite
                </button>
                <a href="{% url 'ponds:comprehensive_pond_dashboard' %}" class="map-btn" style="text-decoration: none;">
                    <i class="fas fa-th-large"></i> Comprehensive Dashboard
                </a>
            </div>
        </div>
        
        <!-- Debug Panel -->
        <div class="debug-panel" id="debug-panel">
            <h4 style="margin: 0 0 10px 0; color: #007bff;">🔍 Map Debug Info</h4>
            <div id="debug-content">
                <div>⏳ Initializing debug panel...</div>
            </div>
        </div>
        
        <div id="map"></div>
    </div>

    <!-- Legend Panel -->
    <div class="legend-panel">
        <h4 class="legend-title">
            <i class="fas fa-info-circle"></i> Map Legend
        </h4>
        <div class="legend-grid">
            <div class="legend-item">
                <div class="legend-marker farm"></div>
                <span>Farm Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-active"></div>
                <span>Active Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-maintenance"></div>
                <span>Maintenance Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-empty"></div>
                <span>Empty Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-harvested"></div>
                <span>Harvested Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker worker"></div>
                <span>Worker Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker geofence"></div>
                <span>Geofence Zone</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// =============================================================================
// FIXED CUMULATIVE MAP DASHBOARD WITH LABOR MANAGEMENT INTEGRATION
// =============================================================================

// Global variables
let map = null;
let farmMarkers = [];
let pondMarkers = [];
let pondPolygons = [];
let workerMarkers = [];
let geofenceShapes = [];
let currentMapType = 'roadmap';
let visibleLayers = ['farms', 'ponds', 'workers', 'geofences'];

// Data from Django (with error handling)
let farmsData, pondsData, workersData, geofencesData, centerLat, centerLng;

try {
    farmsData = {{ farms_data|safe }};
    pondsData = {{ ponds_data|safe }};
    workersData = {{ workers_data|safe }};
    geofencesData = {{ geofences_data|safe }};
    
    // Ensure center coordinates are valid numbers
    centerLat = parseFloat({{ center_lat|default:"13.0827" }});
    centerLng = parseFloat({{ center_lng|default:"80.2707" }});
    
    // Validate center coordinates
    if (isNaN(centerLat) || isNaN(centerLng)) {
        centerLat = 13.0827;  // Default to Chennai, India
        centerLng = 80.2707;
    }
    
} catch (e) {
    console.error('Error parsing Django data:', e);
    farmsData = [];
    pondsData = [];
    workersData = [];
    geofencesData = [];
    centerLat = 13.0827;
    centerLng = 80.2707;
}
    centerLng = 80.2707;
}

// =============================================================================
// DEBUG LOGGING SYSTEM
// =============================================================================

function updateDebugPanel(message, type = 'info') {
    const debugContent = document.getElementById('debug-content');
    if (debugContent) {
        const timestamp = new Date().toLocaleTimeString();
        const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
        debugContent.innerHTML += `<div style="color: ${color}; margin: 2px 0; font-size: 11px;">${timestamp}: ${message}</div>`;
        debugContent.scrollTop = debugContent.scrollHeight;
    }
    console.log(`[${type.toUpperCase()}] ${message}`);
}

// =============================================================================
// GOOGLE MAPS INITIALIZATION
// =============================================================================

function initMap() {
    updateDebugPanel('🗺️ initMap() callback triggered by Google Maps API', 'success');
    
    // Verify Google Maps API is loaded
    if (typeof google === 'undefined' || !google.maps) {
        updateDebugPanel('❌ Google Maps API not loaded', 'error');
        document.getElementById('map').innerHTML = 
            '<div style="padding: 20px; text-align: center; color: red; background: #fee;">Google Maps API failed to load. Check API key and network connection.</div>';
        return;
    }
    
    updateDebugPanel('✅ Google Maps API loaded successfully', 'success');
    updateDebugPanel(`📦 Google Maps version: ${google.maps.version}`, 'info');
    
    // Get map container
    const mapContainer = document.getElementById('map');
    if (!mapContainer) {
        updateDebugPanel('❌ Map container not found', 'error');
        return;
    }
    
    updateDebugPanel('✅ Map container found', 'success');
    updateDebugPanel(`📍 Map center: ${centerLat}, ${centerLng}`, 'info');
    updateDebugPanel(`📊 Data loaded: ${farmsData.length} farms, ${pondsData.length} ponds, ${workersData.length} workers, ${geofencesData.length} geofences`, 'info');
      try {
        // Validate center coordinates before map initialization
        if (!centerLat || !centerLng || isNaN(centerLat) || isNaN(centerLng)) {
            updateDebugPanel('⚠️ Invalid center coordinates, using defaults', 'warning');
            centerLat = 13.0827;  // Default to Chennai, India
            centerLng = 80.2707;
        }
        
        updateDebugPanel(`📍 Final map center: ${centerLat}, ${centerLng}`, 'info');
        
        // Initialize map
        map = new google.maps.Map(mapContainer, {
            zoom: 10,
            center: { lat: parseFloat(centerLat), lng: parseFloat(centerLng) },
            mapTypeId: currentMapType,
            zoomControl: true,
            mapTypeControl: true,
            scaleControl: true,
            streetViewControl: false,
            rotateControl: false,
            fullscreenControl: true
        });
        
        updateDebugPanel('✅ Map object created successfully', 'success');
        
        // Add markers and shapes
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addGeofenceShapes();
        
        // Fit map to show all markers
        fitMapToMarkers();
        
        updateDebugPanel('🎉 Map initialization complete!', 'success');
        
    } catch (error) {
        updateDebugPanel(`❌ Error initializing map: ${error.message}`, 'error');
        console.error('Full map initialization error:', error);
        mapContainer.innerHTML = `
            <div style="padding: 20px; text-align: center; color: red; background: #fee;">
                <h4>Map Initialization Error</h4>
                <p>${error.message}</p>
                <small>Check the browser console for more details</small>
            </div>
        `;
    }
}

// =============================================================================
// MARKER CREATION FUNCTIONS
// =============================================================================

function addFarmMarkers() {
    updateDebugPanel('📍 Adding farm markers...', 'info');
    
    farmsData.forEach(function(farm) {
        // Validate coordinates before creating marker
        if (!farm.latitude || !farm.longitude || 
            isNaN(farm.latitude) || isNaN(farm.longitude) ||
            farm.latitude === null || farm.longitude === null) {
            updateDebugPanel(`⚠️ Skipping farm "${farm.name}" - invalid coordinates (${farm.latitude}, ${farm.longitude})`, 'warning');
            return;
        }
        
        const farmMarker = new google.maps.Marker({
            position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
            map: map,
            title: farm.name,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 12,
                fillColor: '#2563eb',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 3
            }
        });
        
        farmMarker.addListener('click', function() {
            showFarmInfo(farm);
        });
        
        farmMarkers.push(farmMarker);
    });
    
    updateDebugPanel(`✅ Added ${farmMarkers.length} farm markers`, 'success');
}

function addPondMarkers() {
    updateDebugPanel('🏊 Adding pond markers...', 'info');
    
    pondsData.forEach(function(pond) {
        // Validate coordinates before creating marker
        if (!pond.latitude || !pond.longitude || 
            isNaN(pond.latitude) || isNaN(pond.longitude) ||
            pond.latitude === null || pond.longitude === null) {
            updateDebugPanel(`⚠️ Skipping pond "${pond.name}" - invalid coordinates (${pond.latitude}, ${pond.longitude})`, 'warning');
            return;
        }
        
        let pondColor = '#10b981'; // active - green
        if (pond.status === 'maintenance') pondColor = '#f59e0b';
        else if (pond.status === 'empty') pondColor = '#6b7280';
        else if (pond.status === 'harvested') pondColor = '#8b5cf6';
        
        const pondMarker = new google.maps.Marker({
            position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
            map: map,
            title: pond.name,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 8,
                fillColor: pondColor,
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2
            }
        });
        
        pondMarker.addListener('click', function() {
            showPondInfo(pond);
        });
        
        pondMarkers.push(pondMarker);
        
        // Add pond boundary if available
        if (pond.boundary && pond.boundary.length > 0) {
            const polygon = new google.maps.Polygon({
                paths: pond.boundary.map(coord => ({ lat: coord.lat, lng: coord.lng })),
                strokeColor: pondColor,
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: pondColor,
                fillOpacity: 0.2,
                map: map
            });
            
            polygon.addListener('click', function() {
                showPondInfo(pond);
            });
            
            pondPolygons.push(polygon);
        }
    });
    
    updateDebugPanel(`✅ Added ${pondMarkers.length} pond markers`, 'success');
}

function addWorkerMarkers() {
    updateDebugPanel('👥 Adding worker markers...', 'info');
    
    workersData.forEach(function(worker) {
        // Validate coordinates before creating marker
        if (!worker.latitude || !worker.longitude || 
            isNaN(worker.latitude) || isNaN(worker.longitude) ||
            worker.latitude === null || worker.longitude === null) {
            updateDebugPanel(`⚠️ Skipping worker "${worker.name}" - invalid coordinates (${worker.latitude}, ${worker.longitude})`, 'warning');
            return;
        }
        
        const workerMarker = new google.maps.Marker({
            position: { lat: parseFloat(worker.latitude), lng: parseFloat(worker.longitude) },
            map: map,
            title: `${worker.name} - ${worker.team}`,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 10,
                fillColor: '#8B5CF6',
                fillOpacity: 1,
                strokeColor: '#ffffff',
                strokeWeight: 2
            }
        });
        
        workerMarker.addListener('click', function() {
            showWorkerInfo(worker);
        });
        
        workerMarkers.push(workerMarker);
    });
    
    updateDebugPanel(`✅ Added ${workerMarkers.length} worker markers`, 'success');
}

function addGeofenceShapes() {
    updateDebugPanel('🛡️ Adding geofences...', 'info');
    
    geofencesData.forEach(function(geofence) {
        let fillColor = '#F59E0B';
        let strokeColor = '#D97706';
        
        if (geofence.type === 'restricted') {
            fillColor = '#EF4444';
            strokeColor = '#DC2626';
        } else if (geofence.type === 'pond') {
            fillColor = '#10B981';
            strokeColor = '#059669';
        } else if (geofence.type === 'warehouse') {
            fillColor = '#6366F1';
            strokeColor = '#4F46E5';
        }
        
        let geofenceShape = null;
        
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            geofenceShape = new google.maps.Circle({
                strokeColor: strokeColor,
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: fillColor,
                fillOpacity: 0.2,
                map: map,
                center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                radius: geofence.radius
            });
        } else if (geofence.boundary && geofence.boundary.length > 0) {
            const paths = geofence.boundary.map(coord => ({
                lat: parseFloat(coord.lat || coord.latitude),
                lng: parseFloat(coord.lng || coord.longitude)
            }));
            
            geofenceShape = new google.maps.Polygon({
                paths: paths,
                strokeColor: strokeColor,
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: fillColor,
                fillOpacity: 0.2,
                map: map
            });
        }
        
        if (geofenceShape) {
            geofenceShape.addListener('click', function() {
                showGeofenceInfo(geofence);
            });
            
            geofenceShapes.push({
                shape: geofenceShape,
                data: geofence
            });
        }
    });
    
    updateDebugPanel(`✅ Added ${geofenceShapes.length} geofences`, 'success');
}

// =============================================================================
// INFO WINDOW FUNCTIONS
// =============================================================================

function showFarmInfo(farm) {
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 250px;">
                <h5 style="margin: 0 0 10px 0; color: #2563eb;">
                    <i class="fas fa-industry"></i> ${farm.name}
                </h5>
                <p><strong>Location:</strong> ${farm.location || 'Not specified'}</p>
                <p><strong>Total Ponds:</strong> ${farm.total_ponds || 0}</p>
                <p><strong>Active Ponds:</strong> ${farm.active_ponds || 0}</p>
                <p><strong>Total Area:</strong> ${(farm.total_area || 0).toFixed(1)} m²</p>
            </div>
        `
    });
    
    infoWindow.setPosition({ lat: farm.latitude, lng: farm.longitude });
    infoWindow.open(map);
}

function showPondInfo(pond) {
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 250px;">
                <h5 style="margin: 0 0 10px 0; color: #10b981;">
                    <i class="fas fa-water"></i> ${pond.name}
                </h5>
                <p><strong>Farm:</strong> ${pond.farm_name || 'Unknown'}</p>
                <p><strong>Status:</strong> <span style="text-transform: capitalize;">${pond.status}</span></p>
                <p><strong>Species:</strong> ${pond.species || 'Not specified'}</p>
                <p><strong>Size:</strong> ${pond.size || 0} m²</p>
                <p><strong>Water Quality:</strong> ${pond.water_quality || 'Unknown'}</p>
            </div>
        `
    });
    
    infoWindow.setPosition({ lat: pond.latitude, lng: pond.longitude });
    infoWindow.open(map);
}

function showWorkerInfo(worker) {
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 250px;">
                <h5 style="margin: 0 0 10px 0; color: #8B5CF6;">
                    <i class="fas fa-user"></i> ${worker.name}
                </h5>
                <p><strong>Employee ID:</strong> ${worker.employee_id || 'Not assigned'}</p>
                <p><strong>Team:</strong> ${worker.team || 'No team'}</p>
                <p><strong>Status:</strong> <span style="text-transform: capitalize;">${worker.status}</span></p>
                <p><strong>Skill Level:</strong> ${worker.skill_level}</p>
                <p><strong>Current Task:</strong> ${worker.current_task || 'No active task'}</p>
            </div>
        `
    });
    
    infoWindow.setPosition({ lat: worker.latitude, lng: worker.longitude });
    infoWindow.open(map);
}

function showGeofenceInfo(geofence) {
    const infoWindow = new google.maps.InfoWindow({
        content: `
            <div style="padding: 10px; max-width: 250px;">
                <h5 style="margin: 0 0 10px 0; color: #F59E0B;">
                    <i class="fas fa-map-marked-alt"></i> ${geofence.name}
                </h5>
                <p><strong>Type:</strong> <span style="text-transform: capitalize;">${geofence.type}</span></p>
                <p><strong>Description:</strong> ${geofence.description || 'No description'}</p>
                <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                ${geofence.shape_type === 'circle' ? 
                    `<p><strong>Radius:</strong> ${geofence.radius}m</p>` : 
                    `<p><strong>Area:</strong> ${geofence.boundary ? geofence.boundary.length : 0} points</p>`
                }
            </div>
        `
    });
    
    const position = geofence.shape_type === 'circle' && geofence.center_latitude ? 
        { lat: geofence.center_latitude, lng: geofence.center_longitude } :
        { lat: centerLat, lng: centerLng };
    
    infoWindow.setPosition(position);
    infoWindow.open(map);
}

// =============================================================================
// LAYER CONTROL FUNCTIONS
// =============================================================================

function toggleLayer(layer) {
    updateDebugPanel(`🔄 Toggling layer: ${layer}`, 'info');
    
    // Update button states
    document.querySelectorAll('.map-btn').forEach(btn => btn.classList.remove('active'));
    
    if (layer === 'all') {
        visibleLayers = ['farms', 'ponds', 'workers', 'geofences'];
        document.getElementById('showAllBtn').classList.add('active');
    } else if (layer === 'farms') {
        visibleLayers = ['farms'];
        document.getElementById('showFarmsBtn').classList.add('active');
    } else if (layer === 'ponds') {
        visibleLayers = ['ponds'];
        document.getElementById('showPondsBtn').classList.add('active');
    } else if (layer === 'workers') {
        visibleLayers = ['workers'];
        document.getElementById('showWorkersBtn').classList.add('active');
    } else if (layer === 'geofences') {
        visibleLayers = ['geofences'];
        document.getElementById('showGeofencesBtn').classList.add('active');
    }
    
    // Update marker visibility
    farmMarkers.forEach(marker => marker.setVisible(visibleLayers.includes('farms')));
    pondMarkers.forEach(marker => marker.setVisible(visibleLayers.includes('ponds')));
    pondPolygons.forEach(polygon => polygon.setVisible(visibleLayers.includes('ponds')));
    workerMarkers.forEach(marker => marker.setVisible(visibleLayers.includes('workers')));
    geofenceShapes.forEach(geofenceData => geofenceData.shape.setVisible(visibleLayers.includes('geofences')));
}

function toggleMapType() {
    currentMapType = currentMapType === 'roadmap' ? 'hybrid' : 'roadmap';
    map.setMapTypeId(currentMapType);
    
    const btn = document.getElementById('satelliteBtn');
    btn.innerHTML = currentMapType === 'hybrid' ? 
        '<i class="fas fa-map"></i> Road' : 
        '<i class="fas fa-satellite"></i> Satellite';
    
    updateDebugPanel(`🗺️ Map type changed to: ${currentMapType}`, 'info');
}

function fitMapToMarkers() {
    const allMarkers = [...farmMarkers, ...pondMarkers, ...workerMarkers];
    
    if (allMarkers.length === 0) {
        updateDebugPanel('⚠️ No markers to fit map bounds', 'info');
        return;
    }
    
    const bounds = new google.maps.LatLngBounds();
    allMarkers.forEach(marker => bounds.extend(marker.getPosition()));
    
    // Include geofence bounds
    geofenceShapes.forEach(geofenceData => {
        const geofence = geofenceData.data;
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude) {
            bounds.extend(new google.maps.LatLng(geofence.center_latitude, geofence.center_longitude));
        } else if (geofence.boundary && geofence.boundary.length > 0) {
            geofence.boundary.forEach(coord => {
                bounds.extend(new google.maps.LatLng(
                    parseFloat(coord.lat || coord.latitude),
                    parseFloat(coord.lng || coord.longitude)
                ));
            });
        }
    });
    
    map.fitBounds(bounds);
    updateDebugPanel('🎯 Map bounds fitted to markers', 'success');
}

// =============================================================================
// ERROR HANDLING AND INITIALIZATION
// =============================================================================

// Global error handler for Google Maps authentication
window.gm_authFailure = function() {
    updateDebugPanel('❌ Google Maps authentication failed!', 'error');
    document.getElementById('map').innerHTML = 
        '<div style="padding: 20px; text-align: center; color: red; background: #fee;">Google Maps authentication failed. Check API key and restrictions.</div>';
};

// Script loading error handler
function handleScriptError() {
    updateDebugPanel('❌ Failed to load Google Maps script', 'error');
    document.getElementById('map').innerHTML = 
        '<div style="padding: 20px; text-align: center; color: red; background: #fee;">Failed to load Google Maps script. Check network connection.</div>';
}

// Initialize debug panel
updateDebugPanel('🚀 Starting fixed cumulative map initialization...', 'info');
updateDebugPanel(`📊 Data loaded: ${farmsData.length} farms, ${pondsData.length} ponds, ${workersData.length} workers, ${geofencesData.length} geofences`, 'info');
updateDebugPanel(`📍 Center coordinates: ${centerLat}, ${centerLng}`, 'info');
updateDebugPanel('🔑 API key loaded and ready', 'info');
updateDebugPanel('📡 Waiting for Google Maps API...', 'info');

// Timeout check
setTimeout(function() {
    if (typeof google === 'undefined') {
        updateDebugPanel('⏰ Timeout: Google Maps API did not load within 15 seconds', 'error');
    }
}, 15000);

</script>

<!-- Google Maps JavaScript API -->
<script>
    // Double-check API key
    console.log("Using Google Maps API Key: ", "{{ google_maps_api_key }}");
    if (!("{{ google_maps_api_key }}")) {
        updateDebugPanel('⚠️ No Google Maps API key provided', 'error');
        document.getElementById('map').innerHTML = 
            '<div style="padding: 20px; text-align: center; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 5px;">' +
            '<h4>Missing API Key</h4><p>No Google Maps API key was provided. Check your settings.</p>' +
            '<p>Try our test pages: <a href="/maps-test/">Maps Test</a> | <a href="/direct-maps-test/">Direct Test</a> | <a href="/minimal-maps-test/">Minimal Test</a></p>' +
            '</div>';
    }
</script>

<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleScriptError()">
</script>
{% endblock %}
