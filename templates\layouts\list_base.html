{% extends 'base.html' %}
{% load static %}

{% block title %}{% block list_title %}List{% endblock %} - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- List Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            {% block list_header_icon %}<i class="fas fa-list text-primary me-2"></i>{% endblock %}
            <h1 class="h3 mb-0">{% block list_header %}List{% endblock %}</h1>
        </div>
        <div class="d-flex align-items-center">
            {% include 'components/header_controls.html' %}
            {% include 'components/theme_toggle.html' %}
            <div class="ms-3">
                {% block list_header_actions %}
                <!-- List header actions will go here -->
                {% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Filters Section -->
    {% block filters_section %}
    <!-- Filters will go here if needed -->
    {% endblock %}
    
    <!-- List Content -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">{% block list_card_title %}Items{% endblock %}</h5>
            {% block list_card_actions %}
            <!-- Additional card actions will go here -->
            {% endblock %}
        </div>
        <div class="card-body p-0">
            {% block list_content %}
            <!-- List content will go here -->
            {% endblock %}
        </div>
    </div>
    
    {% block pagination %}
    <!-- Pagination will go here if needed -->
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
{% block list_extra_js %}
<!-- List-specific JavaScript will go here -->
{% endblock %}
{% endblock %}