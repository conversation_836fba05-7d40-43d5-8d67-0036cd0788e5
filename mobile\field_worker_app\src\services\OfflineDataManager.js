/**
 * Offline Data Manager
 * Handles offline data storage, synchronization, and conflict resolution
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLite from 'react-native-sqlite-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import RNFS from 'react-native-fs';
import moment from 'moment';

// Enable SQLite debugging
SQLite.DEBUG(true);
SQLite.enablePromise(true);

class OfflineDataManager {
  constructor() {
    this.db = null;
    this.isOnline = true;
    this.syncQueue = [];
    this.conflictQueue = [];
    this.lastSyncTime = null;
    
    this.initializeDatabase();
    this.initializeNetworkListener();
    this.loadSyncQueue();
  }

  /**
   * Initialize SQLite database for offline storage
   */
  async initializeDatabase() {
    try {
      this.db = await SQLite.openDatabase({
        name: 'FieldWorkerOffline.db',
        location: 'default',
        createFromLocation: '~FieldWorkerOffline.db'
      });

      await this.createTables();
      console.log('Offline database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize offline database:', error);
    }
  }

  /**
   * Create database tables for offline storage
   */
  async createTables() {
    const tables = [
      // Tasks table
      `CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        type TEXT NOT NULL,
        status TEXT NOT NULL,
        priority TEXT NOT NULL,
        due_date TEXT NOT NULL,
        pond_id TEXT,
        pond_name TEXT,
        assigned_by TEXT,
        estimated_duration INTEGER,
        instructions TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL,
        started_at TEXT,
        completed_at TEXT,
        cancelled_at TEXT,
        completion_data TEXT,
        local_changes INTEGER DEFAULT 0,
        sync_status TEXT DEFAULT 'pending',
        conflict_data TEXT
      )`,

      // Sync queue table
      `CREATE TABLE IF NOT EXISTS sync_queue (
        id TEXT PRIMARY KEY,
        action TEXT NOT NULL,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp TEXT NOT NULL,
        retry_count INTEGER DEFAULT 0,
        error_message TEXT
      )`,

      // Offline media table
      `CREATE TABLE IF NOT EXISTS offline_media (
        id TEXT PRIMARY KEY,
        file_path TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER,
        related_table TEXT,
        related_id TEXT,
        upload_status TEXT DEFAULT 'pending',
        created_at TEXT NOT NULL
      )`,

      // App settings table
      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )`
    ];

    for (const tableSQL of tables) {
      await this.db.executeSql(tableSQL);
    }
  }

  /**
   * Initialize network connectivity listener
   */
  initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected;
      
      if (wasOffline && this.isOnline) {
        console.log('Network restored, starting sync...');
        this.startSync();
      }
    });
  }

  /**
   * Load sync queue from storage
   */
  async loadSyncQueue() {
    try {
      const [results] = await this.db.executeSql(
        'SELECT * FROM sync_queue ORDER BY timestamp ASC'
      );
      
      this.syncQueue = [];
      for (let i = 0; i < results.rows.length; i++) {
        const row = results.rows.item(i);
        this.syncQueue.push({
          ...row,
          data: JSON.parse(row.data)
        });
      }
      
      console.log(`Loaded ${this.syncQueue.length} items from sync queue`);
    } catch (error) {
      console.error('Failed to load sync queue:', error);
    }
  }

  /**
   * Save data offline with automatic sync queuing
   */
  async saveOffline(tableName, data, action = 'upsert') {
    try {
      // Save to local database
      await this.saveToLocalDB(tableName, data);
      
      // Add to sync queue
      await this.addToSyncQueue(action, tableName, data.id, data);
      
      // Attempt immediate sync if online
      if (this.isOnline) {
        this.startSync();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to save offline data:', error);
      return false;
    }
  }

  /**
   * Save data to local SQLite database
   */
  async saveToLocalDB(tableName, data) {
    const columns = Object.keys(data);
    const values = Object.values(data);
    const placeholders = columns.map(() => '?').join(', ');
    
    const sql = `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')}) VALUES (${placeholders})`;
    
    await this.db.executeSql(sql, values);
  }

  /**
   * Load data from local database
   */
  async loadFromLocalDB(tableName, conditions = {}) {
    try {
      let sql = `SELECT * FROM ${tableName}`;
      const params = [];
      
      if (Object.keys(conditions).length > 0) {
        const whereClause = Object.keys(conditions)
          .map(key => `${key} = ?`)
          .join(' AND ');
        sql += ` WHERE ${whereClause}`;
        params.push(...Object.values(conditions));
      }
      
      const [results] = await this.db.executeSql(sql, params);
      const items = [];
      
      for (let i = 0; i < results.rows.length; i++) {
        const row = results.rows.item(i);
        
        // Parse JSON fields
        if (row.instructions) {
          row.instructions = JSON.parse(row.instructions);
        }
        if (row.completion_data) {
          row.completion_data = JSON.parse(row.completion_data);
        }
        if (row.conflict_data) {
          row.conflict_data = JSON.parse(row.conflict_data);
        }
        
        items.push(row);
      }
      
      return items;
    } catch (error) {
      console.error('Failed to load from local database:', error);
      return [];
    }
  }

  /**
   * Add item to sync queue
   */
  async addToSyncQueue(action, tableName, recordId, data) {
    const syncItem = {
      id: this.generateSyncId(),
      action,
      table_name: tableName,
      record_id: recordId,
      data: JSON.stringify(data),
      timestamp: moment().toISOString(),
      retry_count: 0,
      error_message: null
    };
    
    // Save to database
    await this.saveToLocalDB('sync_queue', syncItem);
    
    // Add to memory queue
    this.syncQueue.push({
      ...syncItem,
      data: data // Keep parsed data in memory
    });
  }

  /**
   * Start synchronization process
   */
  async startSync() {
    if (!this.isOnline || this.syncQueue.length === 0) {
      return;
    }
    
    console.log(`Starting sync of ${this.syncQueue.length} items...`);
    
    const failedItems = [];
    
    for (const item of this.syncQueue) {
      try {
        await this.syncSingleItem(item);
        
        // Remove from database on successful sync
        await this.db.executeSql(
          'DELETE FROM sync_queue WHERE id = ?',
          [item.id]
        );
        
      } catch (error) {
        console.error(`Failed to sync item ${item.id}:`, error);
        
        // Increment retry count
        item.retry_count++;
        item.error_message = error.message;
        
        if (item.retry_count < 3) {
          failedItems.push(item);
          
          // Update retry count in database
          await this.db.executeSql(
            'UPDATE sync_queue SET retry_count = ?, error_message = ? WHERE id = ?',
            [item.retry_count, item.error_message, item.id]
          );
        } else {
          console.error(`Max retries reached for item ${item.id}`);
          
          // Move to conflict queue
          this.conflictQueue.push(item);
          
          // Remove from sync queue
          await this.db.executeSql(
            'DELETE FROM sync_queue WHERE id = ?',
            [item.id]
          );
        }
      }
    }
    
    // Update sync queue with failed items
    this.syncQueue = failedItems;
    
    // Update last sync time
    this.lastSyncTime = moment().toISOString();
    await this.saveSetting('last_sync_time', this.lastSyncTime);
    
    console.log(`Sync completed. ${failedItems.length} items failed.`);
  }

  /**
   * Sync single item with server
   */
  async syncSingleItem(item) {
    const { action, table_name, record_id, data } = item;
    
    // This would make actual API calls to the server
    // For now, simulate API calls
    
    const apiEndpoint = this.getAPIEndpoint(table_name);
    
    switch (action) {
      case 'create':
        // await axios.post(apiEndpoint, data);
        console.log(`API: Creating ${table_name} record`, record_id);
        break;
        
      case 'update':
      case 'upsert':
        // await axios.put(`${apiEndpoint}/${record_id}`, data);
        console.log(`API: Updating ${table_name} record`, record_id);
        break;
        
      case 'delete':
        // await axios.delete(`${apiEndpoint}/${record_id}`);
        console.log(`API: Deleting ${table_name} record`, record_id);
        break;
    }
    
    // Update local record sync status
    await this.db.executeSql(
      `UPDATE ${table_name} SET sync_status = 'synced', local_changes = 0 WHERE id = ?`,
      [record_id]
    );
  }

  /**
   * Get API endpoint for table
   */
  getAPIEndpoint(tableName) {
    const endpoints = {
      tasks: '/api/field-worker/tasks',
      media: '/api/field-worker/media',
      reports: '/api/field-worker/reports'
    };
    
    return `https://api.shrimp-farm.com${endpoints[tableName] || '/api/data'}`;
  }

  /**
   * Save media file offline
   */
  async saveMediaOffline(filePath, fileType, relatedTable, relatedId) {
    try {
      // Generate unique filename
      const fileName = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const extension = filePath.split('.').pop();
      const offlineFileName = `${fileName}.${extension}`;
      
      // Create offline media directory
      const offlineDir = `${RNFS.DocumentDirectoryPath}/offline_media`;
      await RNFS.mkdir(offlineDir);
      
      // Copy file to offline directory
      const offlinePath = `${offlineDir}/${offlineFileName}`;
      await RNFS.copyFile(filePath, offlinePath);
      
      // Get file size
      const fileStats = await RNFS.stat(offlinePath);
      
      // Save media record
      const mediaRecord = {
        id: this.generateMediaId(),
        file_path: offlinePath,
        file_type: fileType,
        file_size: fileStats.size,
        related_table: relatedTable,
        related_id: relatedId,
        upload_status: 'pending',
        created_at: moment().toISOString()
      };
      
      await this.saveToLocalDB('offline_media', mediaRecord);
      
      return mediaRecord;
    } catch (error) {
      console.error('Failed to save media offline:', error);
      return null;
    }
  }

  /**
   * Upload pending media files
   */
  async uploadPendingMedia() {
    try {
      const pendingMedia = await this.loadFromLocalDB('offline_media', {
        upload_status: 'pending'
      });
      
      for (const media of pendingMedia) {
        try {
          // This would upload to server
          // const uploadResult = await this.uploadMediaToServer(media);
          
          console.log(`Uploading media file: ${media.file_path}`);
          
          // Update upload status
          await this.db.executeSql(
            'UPDATE offline_media SET upload_status = ? WHERE id = ?',
            ['uploaded', media.id]
          );
          
          // Delete local file after successful upload
          await RNFS.unlink(media.file_path);
          
        } catch (error) {
          console.error(`Failed to upload media ${media.id}:`, error);
          
          // Update error status
          await this.db.executeSql(
            'UPDATE offline_media SET upload_status = ? WHERE id = ?',
            ['failed', media.id]
          );
        }
      }
    } catch (error) {
      console.error('Failed to upload pending media:', error);
    }
  }

  /**
   * Handle sync conflicts
   */
  async handleConflicts() {
    for (const conflict of this.conflictQueue) {
      try {
        // Fetch latest server data
        const serverData = await this.fetchServerData(conflict.table_name, conflict.record_id);
        
        if (serverData) {
          // Apply conflict resolution strategy
          const resolvedData = await this.resolveConflict(conflict.data, serverData);
          
          // Update local data with resolved version
          await this.saveToLocalDB(conflict.table_name, resolvedData);
          
          // Add resolved data to sync queue
          await this.addToSyncQueue('update', conflict.table_name, conflict.record_id, resolvedData);
        }
      } catch (error) {
        console.error(`Failed to resolve conflict for ${conflict.id}:`, error);
      }
    }
    
    // Clear conflict queue
    this.conflictQueue = [];
  }

  /**
   * Resolve data conflicts using last-write-wins strategy
   */
  async resolveConflict(localData, serverData) {
    const localTimestamp = moment(localData.updated_at);
    const serverTimestamp = moment(serverData.updated_at);
    
    // Use last-write-wins strategy
    if (localTimestamp.isAfter(serverTimestamp)) {
      console.log('Using local data (newer)');
      return localData;
    } else {
      console.log('Using server data (newer)');
      return serverData;
    }
  }

  /**
   * Fetch data from server
   */
  async fetchServerData(tableName, recordId) {
    try {
      const apiEndpoint = this.getAPIEndpoint(tableName);
      // const response = await axios.get(`${apiEndpoint}/${recordId}`);
      // return response.data;
      
      // Simulate server response
      console.log(`Fetching server data for ${tableName}:${recordId}`);
      return null;
    } catch (error) {
      console.error('Failed to fetch server data:', error);
      return null;
    }
  }

  /**
   * Save app setting
   */
  async saveSetting(key, value) {
    const setting = {
      key,
      value: JSON.stringify(value),
      updated_at: moment().toISOString()
    };
    
    await this.saveToLocalDB('app_settings', setting);
  }

  /**
   * Load app setting
   */
  async loadSetting(key, defaultValue = null) {
    try {
      const settings = await this.loadFromLocalDB('app_settings', { key });
      
      if (settings.length > 0) {
        return JSON.parse(settings[0].value);
      }
      
      return defaultValue;
    } catch (error) {
      console.error('Failed to load setting:', error);
      return defaultValue;
    }
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    return {
      isOnline: this.isOnline,
      pendingSync: this.syncQueue.length,
      conflicts: this.conflictQueue.length,
      lastSyncTime: this.lastSyncTime
    };
  }

  /**
   * Clear all offline data
   */
  async clearOfflineData() {
    try {
      const tables = ['tasks', 'sync_queue', 'offline_media', 'app_settings'];
      
      for (const table of tables) {
        await this.db.executeSql(`DELETE FROM ${table}`);
      }
      
      // Clear memory queues
      this.syncQueue = [];
      this.conflictQueue = [];
      
      console.log('All offline data cleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
    }
  }

  /**
   * Generate unique sync ID
   */
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique media ID
   */
  generateMediaId() {
    return `media_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats() {
    try {
      const stats = {};
      const tables = ['tasks', 'sync_queue', 'offline_media', 'app_settings'];
      
      for (const table of tables) {
        const [results] = await this.db.executeSql(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = results.rows.item(0).count;
      }
      
      return stats;
    } catch (error) {
      console.error('Failed to get database stats:', error);
      return {};
    }
  }
}

// Export singleton instance
export default new OfflineDataManager();
