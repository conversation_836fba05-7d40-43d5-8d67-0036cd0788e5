<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Integration Dashboard - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #64748b;
            font-weight: 500;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .weather-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s ease;
        }

        .weather-card:hover {
            transform: translateY(-2px);
        }

        .weather-icon {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .temp-display {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
        }

        .alert-weather {
            border-left: 4px solid;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
        }

        .alert-storm { border-left-color: #dc3545; background-color: #f8d7da; }
        .alert-rain { border-left-color: #0dcaf0; background-color: #d1ecf1; }
        .alert-heat { border-left-color: #fd7e14; background-color: #ffeaa7; }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>🌤️ Weather Integration Dashboard</h1>
        <p>Monitor weather conditions, forecasts, and automated responses</p>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="#" class="quick-action">
                <i class="fas fa-cloud-sun"></i>
                <span>Current Weather</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-calendar-alt"></i>
                <span>7-Day Forecast</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Weather Alerts</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-cogs"></i>
                <span>Automation</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-chart-line"></i>
                <span>Weather Trends</span>
            </a>
        </div>
    </div>

    <!-- Current Weather Stats -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-warning">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="stats-value">28°C</div>
                <div class="stats-label">Temperature</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-info">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="stats-value">75%</div>
                <div class="stats-label">Humidity</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-primary">
                    <i class="fas fa-wind"></i>
                </div>
                <div class="stats-value">12 km/h</div>
                <div class="stats-label">Wind Speed</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-secondary">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stats-value">8 km</div>
                <div class="stats-label">Visibility</div>
            </div>
        </div>
    </div>

    <!-- Current Conditions -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-cloud-sun me-2"></i>
                Current Weather Conditions
            </h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-4 text-center">
                    <div class="weather-icon text-warning">
                        <i class="fas fa-sun"></i>
                    </div>
                    <h4>Partly Cloudy</h4>
                    <p class="text-muted">Feels like 31°C</p>
                </div>
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-6">
                            <strong>Pressure:</strong> 1013 hPa<br>
                            <strong>UV Index:</strong> 7 (High)<br>
                            <strong>Dew Point:</strong> 22°C
                        </div>
                        <div class="col-6">
                            <strong>Wind Direction:</strong> SW<br>
                            <strong>Cloud Cover:</strong> 40%<br>
                            <strong>Last Updated:</strong> 5 min ago
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Alerts -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Active Weather Alerts
            </h5>
        </div>
        <div class="card-body">
            <div class="alert-weather alert-rain">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-cloud-rain me-2"></i>Heavy Rain Warning</h6>
                        <p class="mb-1">Expected 50-75mm rainfall in next 6 hours</p>
                        <small class="text-muted">Issued: 1 hour ago | Valid until: 11:00 PM</small>
                    </div>
                    <button class="btn btn-sm btn-primary">Take Action</button>
                </div>
            </div>

            <div class="alert-weather alert-heat">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-thermometer-full me-2"></i>High Temperature Advisory</h6>
                        <p class="mb-1">Temperature may reach 35°C tomorrow afternoon</p>
                        <small class="text-muted">Issued: 30 min ago | Valid until: Tomorrow 6:00 PM</small>
                    </div>
                    <button class="btn btn-sm btn-warning">Monitor</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 5-Day Forecast -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-week me-2"></i>
                5-Day Weather Forecast
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md">
                    <div class="weather-card text-center">
                        <div><strong>Today</strong></div>
                        <div class="weather-icon text-warning" style="font-size: 2rem;">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="temp-display" style="font-size: 1.5rem;">28°/22°</div>
                        <small class="text-muted">Sunny</small>
                    </div>
                </div>
                <div class="col-md">
                    <div class="weather-card text-center">
                        <div><strong>Tomorrow</strong></div>
                        <div class="weather-icon text-info" style="font-size: 2rem;">
                            <i class="fas fa-cloud-rain"></i>
                        </div>
                        <div class="temp-display" style="font-size: 1.5rem;">25°/20°</div>
                        <small class="text-muted">Rainy</small>
                    </div>
                </div>
                <div class="col-md">
                    <div class="weather-card text-center">
                        <div><strong>Thu</strong></div>
                        <div class="weather-icon text-secondary" style="font-size: 2rem;">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <div class="temp-display" style="font-size: 1.5rem;">27°/21°</div>
                        <small class="text-muted">Cloudy</small>
                    </div>
                </div>
                <div class="col-md">
                    <div class="weather-card text-center">
                        <div><strong>Fri</strong></div>
                        <div class="weather-icon text-warning" style="font-size: 2rem;">
                            <i class="fas fa-cloud-sun"></i>
                        </div>
                        <div class="temp-display" style="font-size: 1.5rem;">30°/24°</div>
                        <small class="text-muted">Partly Cloudy</small>
                    </div>
                </div>
                <div class="col-md">
                    <div class="weather-card text-center">
                        <div><strong>Sat</strong></div>
                        <div class="weather-icon text-warning" style="font-size: 2rem;">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="temp-display" style="font-size: 1.5rem;">32°/25°</div>
                        <small class="text-muted">Sunny</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Automation -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-cogs me-2"></i>
                Weather-Based Automation
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100">
                        <i class="fas fa-fan me-2"></i>
                        Aerator Control
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100">
                        <i class="fas fa-water me-2"></i>
                        Water Management
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100">
                        <i class="fas fa-drumstick-bite me-2"></i>
                        Feeding Schedule
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100">
                        <i class="fas fa-shield-alt me-2"></i>
                        Protection Mode
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-check-circle text-success me-2"></i>Active Rules</h6>
                            <ul class="mb-0 small">
                                <li>Enable aerators if temperature > 30°C</li>
                                <li>Reduce feeding if rain > 20mm/hour</li>
                                <li>Close pond covers if wind > 40 km/h</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="p-3 bg-light rounded">
                            <h6><i class="fas fa-clock text-info me-2"></i>Recent Actions</h6>
                            <ul class="mb-0 small">
                                <li>10:30 AM: Adjusted pond aerators</li>
                                <li>09:15 AM: Activated water cooling</li>
                                <li>08:45 AM: Modified feeding schedule</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Data Sources -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-satellite me-2"></i>
                Data Sources & Integration
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <button class="btn btn-outline-primary w-100">
                        <i class="fas fa-globe me-2"></i>
                        Weather APIs
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-outline-success w-100">
                        <i class="fas fa-wifi me-2"></i>
                        Local Sensors
                    </button>
                </div>
            </div>
            <div class="mt-3 p-3 bg-light rounded">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Weather data updated every 15 minutes from multiple sources including OpenWeatherMap, local IoT sensors,
                    and satellite imagery. Last sync: 5 minutes ago.
                </small>
            </div>
        </div>
    </div>

    <!-- Development Notice -->
    <div class="alert alert-info mt-4">
        <h6><i class="fas fa-info-circle me-2"></i>Weather Integration System</h6>
        <p class="mb-0">
            Comprehensive weather monitoring with automated responses, predictive analytics, and integration with farm equipment.
            Supports multiple weather APIs and IoT sensors for accurate local conditions.
        </p>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
