"""
Django management command for query optimization
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import connection
from core.query_profiler import query_profiler, generate_optimization_report
import json
import time

class Command(BaseCommand):
    help = 'Analyze and optimize database queries'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--profile',
            action='store_true',
            help='Start query profiling for specified duration'
        )
        parser.add_argument(
            '--duration',
            type=int,
            default=60,
            help='Profiling duration in seconds (default: 60)'
        )
        parser.add_argument(
            '--report',
            action='store_true',
            help='Generate optimization report'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear profiling data'
        )
        parser.add_argument(
            '--analyze-slow',
            action='store_true',
            help='Analyze slow queries and suggest optimizations'
        )
        parser.add_argument(
            '--fix-indexes',
            action='store_true',
            help='Generate SQL for missing indexes'
        )
    
    def handle(self, *args, **options):
        if options['clear']:
            self.clear_profiling_data()
        
        if options['profile']:
            self.profile_queries(options['duration'])
        
        if options['report']:
            self.generate_report()
        
        if options['analyze_slow']:
            self.analyze_slow_queries()
        
        if options['fix_indexes']:
            self.generate_index_fixes()
    
    def clear_profiling_data(self):
        """Clear all profiling data"""
        query_profiler.clear_data()
        self.stdout.write(
            self.style.SUCCESS('✅ Profiling data cleared')
        )
    
    def profile_queries(self, duration):
        """Profile queries for specified duration"""
        self.stdout.write(f'🔍 Starting query profiling for {duration} seconds...')
        
        query_profiler.start_profiling()
        
        try:
            # Simulate some activity or wait for real traffic
            self.stdout.write('Profiling active. Generate some database activity...')
            time.sleep(duration)
        finally:
            query_profiler.stop_profiling()
        
        report = query_profiler.get_performance_report()
        
        self.stdout.write(
            self.style.SUCCESS(f'✅ Profiling completed. Captured {report["summary"]["total_queries"]} queries')
        )
        
        if report['summary']['slow_queries_count'] > 0:
            self.stdout.write(
                self.style.WARNING(f'⚠️  Found {report["summary"]["slow_queries_count"]} slow queries')
            )
    
    def generate_report(self):
        """Generate comprehensive optimization report"""
        self.stdout.write('📊 Generating optimization report...')
        
        report = generate_optimization_report()
        
        # Display summary
        if report['profiler_data']:
            summary = report['profiler_data']['summary']
            self.stdout.write('\n📈 Query Performance Summary:')
            self.stdout.write(f'  Total Queries: {summary["total_queries"]}')
            self.stdout.write(f'  Total Time: {summary["total_time"]}s')
            self.stdout.write(f'  Average Time: {summary["average_time"]}s')
            self.stdout.write(f'  Slow Queries: {summary["slow_queries_count"]}')
            self.stdout.write(f'  Duplicate Groups: {summary["duplicate_query_groups"]}')
        
        # Display optimizations
        self.stdout.write('\n🔧 Optimization Suggestions:')
        for model, optimizations in report['optimizations'].items():
            if optimizations:
                self.stdout.write(f'\n  {model}:')
                for opt in optimizations:
                    self.stdout.write(f'    ⚠️  {opt["issue"]}')
                    self.stdout.write(f'    💡 {opt["solution"]}')
        
        # Save detailed report
        report_file = f'query_optimization_report_{int(time.time())}.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        self.stdout.write(f'\n📄 Detailed report saved to: {report_file}')
    
    def analyze_slow_queries(self):
        """Analyze slow queries and provide specific suggestions"""
        self.stdout.write('🐌 Analyzing slow queries...')
        
        report = query_profiler.get_performance_report()
        
        if not report['slowest_queries']:
            self.stdout.write(self.style.SUCCESS('✅ No slow queries found!'))
            return
        
        self.stdout.write(f'\n🔍 Top {len(report["slowest_queries"])} Slowest Queries:')
        
        for i, query in enumerate(report['slowest_queries'], 1):
            self.stdout.write(f'\n{i}. Execution Time: {query["execution_time"]}s')
            self.stdout.write(f'   Query: {query["query"]}')
            if query['params']:
                self.stdout.write(f'   Params: {query["params"]}')
            
            # Provide specific suggestions
            suggestions = self._analyze_query(query['query'])
            for suggestion in suggestions:
                self.stdout.write(f'   💡 {suggestion}')
        
        # Display optimization suggestions
        if report['optimization_suggestions']:
            self.stdout.write('\n🔧 General Optimization Suggestions:')
            for suggestion in report['optimization_suggestions']:
                self.stdout.write(f'  {suggestion["type"]}: {suggestion["message"]}')
                self.stdout.write(f'    💡 {suggestion["suggestion"]}')
    
    def _analyze_query(self, sql):
        """Analyze individual query and provide suggestions"""
        suggestions = []
        sql_upper = sql.upper()
        
        # Check for missing JOINs
        if 'WHERE' in sql_upper and 'JOIN' not in sql_upper:
            if any(table in sql_upper for table in ['_ID =', '_ID IN']):
                suggestions.append('Consider using JOIN instead of WHERE with foreign keys')
        
        # Check for SELECT *
        if 'SELECT *' in sql_upper:
            suggestions.append('Avoid SELECT * - specify only needed columns')
        
        # Check for missing LIMIT
        if 'SELECT' in sql_upper and 'LIMIT' not in sql_upper and 'COUNT' not in sql_upper:
            suggestions.append('Consider adding LIMIT to prevent large result sets')
        
        # Check for inefficient LIKE queries
        if 'LIKE %' in sql_upper:
            suggestions.append('LIKE queries starting with % cannot use indexes efficiently')
        
        # Check for ORDER BY without LIMIT
        if 'ORDER BY' in sql_upper and 'LIMIT' not in sql_upper:
            suggestions.append('ORDER BY without LIMIT can be expensive on large tables')
        
        return suggestions
    
    def generate_index_fixes(self):
        """Generate SQL for missing indexes"""
        self.stdout.write('🔨 Generating index optimization SQL...')
        
        # Analyze current database structure
        with connection.cursor() as cursor:
            # Get table information
            cursor.execute("""
                SELECT table_name, column_name, data_type
                FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name LIKE '%_pond%' OR table_name LIKE '%_water%'
                ORDER BY table_name, ordinal_position
            """)
            
            columns = cursor.fetchall()
        
        # Generate index suggestions
        index_suggestions = []
        
        # Common patterns that need indexes
        common_indexes = [
            ('ponds_pond', 'farm_id', 'Foreign key index'),
            ('ponds_pond', 'status', 'Status filtering'),
            ('water_quality_waterqualityreading', 'pond_id', 'Foreign key index'),
            ('water_quality_waterqualityreading', 'timestamp', 'Date filtering'),
            ('water_quality_waterqualityreading', ['pond_id', 'timestamp'], 'Composite index for pond + date queries'),
        ]
        
        for table, column, description in common_indexes:
            if isinstance(column, list):
                # Composite index
                column_list = ', '.join(column)
                index_name = f"idx_{table}_{'_'.join(column)}"
                sql = f"CREATE INDEX CONCURRENTLY {index_name} ON {table} ({column_list});"
            else:
                # Single column index
                index_name = f"idx_{table}_{column}"
                sql = f"CREATE INDEX CONCURRENTLY {index_name} ON {table} ({column});"
            
            index_suggestions.append({
                'table': table,
                'columns': column,
                'description': description,
                'sql': sql
            })
        
        # Display suggestions
        self.stdout.write('\n📋 Suggested Indexes:')
        sql_file_content = ['-- Database Index Optimization SQL', '-- Generated by optimize_queries command', '']
        
        for suggestion in index_suggestions:
            self.stdout.write(f'\n  Table: {suggestion["table"]}')
            self.stdout.write(f'  Columns: {suggestion["columns"]}')
            self.stdout.write(f'  Purpose: {suggestion["description"]}')
            self.stdout.write(f'  SQL: {suggestion["sql"]}')
            
            sql_file_content.append(f'-- {suggestion["description"]}')
            sql_file_content.append(suggestion['sql'])
            sql_file_content.append('')
        
        # Save SQL file
        sql_file = f'index_optimization_{int(time.time())}.sql'
        with open(sql_file, 'w') as f:
            f.write('\n'.join(sql_file_content))
        
        self.stdout.write(f'\n📄 Index SQL saved to: {sql_file}')
        self.stdout.write('\n⚠️  Review the SQL before executing in production!')
        self.stdout.write('   Use CONCURRENTLY to avoid locking tables during index creation.')
