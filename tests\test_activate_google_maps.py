import unittest
from unittest.mock import patch, MagicMock, call
import os
import sys
import tempfile
from io import StringIO
import builtins

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the script (need to modify sys.path to import it as a module)
sys.path.append(os.path.abspath(os.path.dirname(os.path.dirname(__file__))))
with patch('webbrowser.open'):  # Mock webbrowser.open to prevent actual browser opening
    import activate_google_maps


class TestActivateGoogleMaps(unittest.TestCase):
    """Test suite for the activate_google_maps.py script."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.TemporaryDirectory()
        self.original_cwd = os.getcwd()
        os.chdir(self.temp_dir.name)
        
        # Create a temporary .env file
        self.env_file = os.path.join(self.temp_dir.name, '.env')
        with open(self.env_file, 'w') as f:
            f.write("GOOGLE_MAPS_API_KEY=old_test_key\n")
            f.write("VITE_GOOGLE_MAPS_API_KEY=old_test_key\n")
            f.write("MAP_PROVIDER=google\n")
        
        # Sample API key results for mocking
        self.valid_key_result = {
            'status': 'success',
            'message': 'API key is valid and working',
            'apis_enabled': ['Static Maps API', 'JavaScript Maps API'],
        }
        
        self.invalid_key_result = {
            'status': 'error',
            'message': 'API key is invalid',
            'apis_enabled': [],
        }

    def tearDown(self):
        """Clean up after tests."""
        os.chdir(self.original_cwd)
        self.temp_dir.cleanup()

    @patch('webbrowser.open')
    @patch('builtins.input')
    def test_open_browser_links(self, mock_input, mock_open):
        """Test opening browser links."""
        # Mock user input to answer 'y' to opening browser links
        mock_input.return_value = 'y'
        
        # Call the function
        activate_google_maps.open_browser_links()
        
        # Verify browser was opened for each link
        self.assertEqual(mock_open.call_count, 2)  # Console URL and Domain Restriction URL
        mock_open.assert_any_call(activate_google_maps.CONSOLE_URL)
        mock_open.assert_any_call(activate_google_maps.DOMAIN_RESTRICTION_URL)

    @patch('webbrowser.open')
    @patch('builtins.input')
    def test_open_browser_links_with_apis_missing(self, mock_input, mock_open):
        """Test opening browser links with specific APIs missing."""
        # Mock user input to answer 'y' to opening browser links
        mock_input.return_value = 'y'
        
        # Call the function with missing APIs
        activate_google_maps.open_browser_links(['JavaScript Maps API', 'Places API'])
        
        # Verify browser was opened for each link including API activation links
        self.assertGreaterEqual(mock_open.call_count, 4)  # Console, Domain, and 2 API links
        mock_open.assert_any_call(activate_google_maps.CONSOLE_URL)
        mock_open.assert_any_call(activate_google_maps.API_ACTIVATION_URLS['JavaScript Maps API'])
        mock_open.assert_any_call(activate_google_maps.API_ACTIVATION_URLS['Places API'])

    @patch('webbrowser.open')
    @patch('builtins.input')
    def test_open_browser_links_skip(self, mock_input, mock_open):
        """Test skipping browser links opening."""
        # Set skip_browser flag
        args_backup = activate_google_maps.args
        activate_google_maps.args = MagicMock(skip_browser=True)
        
        try:
            # Call the function
            activate_google_maps.open_browser_links()
            
            # Verify browser was not opened
            mock_open.assert_not_called()
            mock_input.assert_not_called()
        finally:
            # Restore args
            activate_google_maps.args = args_backup

    @patch('activate_google_maps.update_env_file')
    @patch('getpass.getpass')
    @patch('builtins.input')
    @patch('webbrowser.open')
    def test_main_new_key_workflow(self, mock_open, mock_input, mock_getpass, mock_update_env):
        """Test the main workflow for adding a new API key."""
        # Mock user inputs
        mock_input.side_effect = ['y']  # Yes to open browser
        mock_getpass.return_value = 'new_test_key'  # API key input
        mock_update_env.return_value = True  # Successfully updated .env file
        
        # Mock test_api_key function
        with patch('activate_google_maps.test_api_key') as mock_test_api:
            mock_test_api.return_value = True  # API key is valid
            
            # Call main function
            activate_google_maps.main()
            
            # Verify workflow
            mock_open.assert_called()  # Browser was opened
            mock_getpass.assert_called_once()  # Asked for API key
            mock_update_env.assert_called_once_with('new_test_key')  # Updated .env with new key
            mock_test_api.assert_called_once_with('new_test_key')  # Tested the new key

    @patch('activate_google_maps.update_env_file')
    @patch('sys.argv', ['activate_google_maps.py', '--key=command_line_key'])
    def test_command_line_key(self, mock_update_env):
        """Test providing API key via command line argument."""
        # Recreate the argument parser with our command line args
        activate_google_maps.parser = activate_google_maps.argparse.ArgumentParser(description='Activate Google Maps API Key')
        activate_google_maps.parser.add_argument('--key', help='Set a Google Maps API key')
        activate_google_maps.parser.add_argument('--force', action='store_true', help='Override existing key without confirmation')
        activate_google_maps.parser.add_argument('--skip-test', action='store_true', help='Skip API key testing')
        activate_google_maps.parser.add_argument('--skip-browser', action='store_true', help='Skip opening browser links')
        activate_google_maps.args = activate_google_maps.parser.parse_args()
        
        # Mock test_api_key function to return success
        with patch('activate_google_maps.test_api_key') as mock_test_api:
            mock_test_api.return_value = True
            
            # Call main function
            activate_google_maps.main()
            
            # Verify key from command line was used
            mock_update_env.assert_called_once_with('command_line_key')
            mock_test_api.assert_called_once_with('command_line_key')

    @patch('builtins.input')
    def test_existing_key_workflow(self, mock_input):
        """Test workflow when an existing key is found."""
        # Set up environment with existing key
        os.environ['GOOGLE_MAPS_API_KEY'] = 'existing_key'
        
        # Mock user input to keep existing key
        mock_input.return_value = 'n'  # No to replacing key
        
        # Mock test_api_key function
        with patch('activate_google_maps.test_api_key') as mock_test_api:
            mock_test_api.return_value = True  # API key is valid
            
            # Call main function
            activate_google_maps.main()
            
            # Verify workflow
            mock_input.assert_called_once()  # Asked about replacing key
            mock_test_api.assert_called_once_with('existing_key')  # Tested the existing key

    @patch('activate_google_maps.update_env_file')
    @patch('builtins.input')
    def test_force_replace_existing_key(self, mock_input, mock_update_env):
        """Test forcing replacement of an existing key."""
        # Set up environment with existing key
        os.environ['GOOGLE_MAPS_API_KEY'] = 'existing_key'
        
        # Set force flag
        args_backup = activate_google_maps.args
        activate_google_maps.args = MagicMock(force=True, key='force_key', skip_test=False, skip_browser=True)
        
        try:
            # Mock test_api_key function
            with patch('activate_google_maps.test_api_key') as mock_test_api:
                mock_test_api.return_value = True  # API key is valid
                
                # Call main function
                activate_google_maps.main()
                
                # Verify workflow
                mock_input.assert_not_called()  # Didn't ask about replacing key due to force flag
                mock_update_env.assert_called_once_with('force_key')  # Updated with force key
                mock_test_api.assert_called_once_with('force_key')  # Tested the force key
        finally:
            # Restore args
            activate_google_maps.args = args_backup

    def test_update_env_file_new_file(self):
        """Test updating a new .env file."""
        # Remove existing .env file
        os.remove(self.env_file)
        
        # Call update_env_file
        result = activate_google_maps.update_env_file('new_test_key')
        
        # Verify result
        self.assertTrue(result)
        
        # Verify file contents
        with open(self.env_file, 'r') as f:
            content = f.read()
            self.assertIn('GOOGLE_MAPS_API_KEY=new_test_key', content)
            self.assertIn('VITE_GOOGLE_MAPS_API_KEY=new_test_key', content)
            self.assertIn('MAP_PROVIDER=google', content)

    def test_update_env_file_existing_file(self):
        """Test updating an existing .env file."""
        # Call update_env_file
        result = activate_google_maps.update_env_file('updated_test_key')
        
        # Verify result
        self.assertTrue(result)
        
        # Verify file contents
        with open(self.env_file, 'r') as f:
            content = f.read()
            self.assertIn('GOOGLE_MAPS_API_KEY=updated_test_key', content)
            self.assertIn('VITE_GOOGLE_MAPS_API_KEY=updated_test_key', content)
            self.assertIn('MAP_PROVIDER=google', content)

    @patch('core.api_key_checker.check_api_key')
    def test_test_api_key_success(self, mock_check_api_key):
        """Test API key testing with a valid key."""
        # Mock check_api_key to return success
        mock_check_api_key.return_value = self.valid_key_result
        
        # Call test_api_key
        result = activate_google_maps.test_api_key('valid_test_key')
        
        # Verify result
        self.assertTrue(result)
        mock_check_api_key.assert_called_once_with('valid_test_key')

    @patch('core.api_key_checker.check_api_key')
    def test_test_api_key_failure(self, mock_check_api_key):
        """Test API key testing with an invalid key."""
        # Mock check_api_key to return failure
        mock_check_api_key.return_value = self.invalid_key_result
        
        # Call test_api_key
        result = activate_google_maps.test_api_key('invalid_test_key')
        
        # Verify result
        self.assertFalse(result)
        mock_check_api_key.assert_called_once_with('invalid_test_key')

    @patch('sys.argv', ['activate_google_maps.py', '--skip-test'])
    def test_skip_test_option(self):
        """Test the skip-test option."""
        # Recreate the argument parser with our command line args
        activate_google_maps.parser = activate_google_maps.argparse.ArgumentParser(description='Activate Google Maps API Key')
        activate_google_maps.parser.add_argument('--key', help='Set a Google Maps API key')
        activate_google_maps.parser.add_argument('--force', action='store_true', help='Override existing key without confirmation')
        activate_google_maps.parser.add_argument('--skip-test', action='store_true', help='Skip API key testing')
        activate_google_maps.parser.add_argument('--skip-browser', action='store_true', help='Skip opening browser links')
        activate_google_maps.args = activate_google_maps.parser.parse_args()
        
        # Mock necessary functions
        with patch('activate_google_maps.update_env_file') as mock_update_env, \
             patch('getpass.getpass') as mock_getpass, \
             patch('activate_google_maps.test_api_key') as mock_test_api:
            
            mock_getpass.return_value = 'test_key'
            mock_update_env.return_value = True
            
            # Call main function
            activate_google_maps.main()
            
            # Verify test_api_key was not called due to skip-test flag
            mock_test_api.assert_not_called()


if __name__ == '__main__':
    unittest.main()