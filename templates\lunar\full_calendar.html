<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Full Lunar Calendar - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- FullCalendar CSS -->
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    .lunar-calendar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .lunar-calendar-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: lunar-sweep 4s infinite;
    }
    
    @keyframes lunar-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .calendar-container {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }
    
    .fc {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .fc-toolbar {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 15px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .fc-toolbar-title {
        color: white !important;
        font-weight: bold;
        font-size: 1.5em;
    }
    
    .fc-button {
        background: rgba(255,255,255,0.2) !important;
        border: 1px solid rgba(255,255,255,0.3) !important;
        color: white !important;
        border-radius: 10px !important;
        transition: all 0.3s ease;
    }
    
    .fc-button:hover {
        background: rgba(255,255,255,0.3) !important;
        transform: translateY(-2px);
    }
    
    .fc-daygrid-day {
        border: 1px solid #e0e0e0;
        transition: all 0.3s ease;
    }
    
    .fc-daygrid-day:hover {
        background: rgba(102, 126, 234, 0.1);
    }
    
    .fc-day-today {
        background: rgba(102, 126, 234, 0.2) !important;
        border: 2px solid #667eea !important;
    }
    
    .lunar-event {
        border-radius: 8px;
        padding: 4px 8px;
        margin: 2px 0;
        font-size: 0.85em;
        font-weight: bold;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .lunar-event:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .lunar-amavasya {
        background: linear-gradient(45deg, #2c3e50, #34495e);
        color: white;
    }
    
    .lunar-pournami {
        background: linear-gradient(45deg, #f39c12, #e67e22);
        color: white;
    }
    
    .lunar-astami {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
    }
    
    .lunar-navami {
        background: linear-gradient(45deg, #9b59b6, #8e44ad);
        color: white;
    }
    
    /* FullCalendar specific lunar event styling */
    .fc-event-amavasya {
        background: linear-gradient(45deg, #2c3e50, #34495e) !important;
        border-color: #2c3e50 !important;
        color: white !important;
    }
    
    .fc-event-pournami {
        background: linear-gradient(45deg, #f39c12, #e67e22) !important;
        border-color: #f39c12 !important;
        color: white !important;
    }
    
    .fc-event-astami {
        background: linear-gradient(45deg, #e74c3c, #c0392b) !important;
        border-color: #e74c3c !important;
        color: white !important;
    }
    
    .fc-event-navami {
        background: linear-gradient(45deg, #9b59b6, #8e44ad) !important;
        border-color: #9b59b6 !important;
        color: white !important;
    }
    
    .lunar-legend {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin: 20px 0;
        flex-wrap: wrap;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        border-radius: 20px;
        background: rgba(255,255,255,0.8);
        border: 1px solid rgba(0,0,0,0.1);
    }
    
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
    }
    
    .calendar-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: rgba(255,255,255,0.9);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 1px solid rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .month-navigation {
        display: flex;
        justify-content: center;
        gap: 10px;
        margin: 20px 0;
        flex-wrap: wrap;
    }
    
    .month-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 20px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: bold;
    }
    
    .month-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .month-btn.active {
        background: linear-gradient(45deg, #f39c12, #e67e22);
    }
    
    .lunar-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
    }
    
    .lunar-modal-content {
        background: white;
        margin: 5% auto;
        padding: 30px;
        border-radius: 20px;
        width: 80%;
        max-width: 600px;
        position: relative;
        animation: modalSlideIn 0.3s ease;
    }
    
    @keyframes modalSlideIn {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }
    
    .modal-close {
        position: absolute;
        top: 15px;
        right: 20px;
        font-size: 2em;
        cursor: pointer;
        color: #999;
    }
    
    .modal-close:hover {
        color: #333;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/" class="quick-action">
                    <i class="fas fa-calendar"></i>
                    Lunar Dashboard
                </a>
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/api/" class="quick-action">
                    <i class="fas fa-code"></i>
                    API Access
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid">
    <!-- Lunar Calendar Header -->
    <div class="lunar-calendar-header">
        <div class="position-relative">
            <h1><i class="fas fa-calendar-alt me-3"></i>Full Lunar Calendar</h1>
            <p class="lead mb-0">Complete lunar cycle tracking for optimal shrimp farm management</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">📅 FULL YEAR VIEW</span>
                <span class="badge bg-light text-dark me-2">🌙 COMPLETE CYCLES</span>
                <span class="badge bg-light text-dark me-2">📊 DETAILED ANALYTICS</span>
                <span class="badge bg-light text-dark">🎯 SHRIMP FARM OPTIMIZED</span>
            </div>
        </div>
    </div>

    <!-- Calendar Statistics -->
    <div class="calendar-stats">
        <div class="stat-card">
            <div class="stat-number">{{ year_stats.total_new_moons|default:12 }}</div>
            <h6>New Moons</h6>
            <p class="mb-0">🌑 Amavasya</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ year_stats.total_full_moons|default:12 }}</div>
            <h6>Full Moons</h6>
            <p class="mb-0">🌕 Pournami</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ year_stats.total_astami|default:24 }}</div>
            <h6>Astami Days</h6>
            <p class="mb-0">🌗 8th Day</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ year_stats.total_navami|default:24 }}</div>
            <h6>Navami Days</h6>
            <p class="mb-0">🌘 9th Day</p>
        </div>
    </div>

    <!-- Lunar Events Legend -->
    <div class="lunar-legend">
        <div class="legend-item">
            <div class="legend-color" style="background: linear-gradient(45deg, #2c3e50, #34495e);"></div>
            <span>🌑 Amavasya (New Moon)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: linear-gradient(45deg, #f39c12, #e67e22);"></div>
            <span>🌕 Pournami (Full Moon)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: linear-gradient(45deg, #e74c3c, #c0392b);"></div>
            <span>🌗 Astami (8th Day)</span>
        </div>
        <div class="legend-item">
            <div class="legend-color" style="background: linear-gradient(45deg, #9b59b6, #8e44ad);"></div>
            <span>🌘 Navami (9th Day)</span>
        </div>
    </div>

    <!-- Calendar Container -->
    <div class="calendar-container">
        <div id="lunar-calendar"></div>
    </div>

    <!-- Quick Navigation -->
    <div class="month-navigation">
        <button class="month-btn" onclick="goToMonth(1)">Jan</button>
        <button class="month-btn" onclick="goToMonth(2)">Feb</button>
        <button class="month-btn" onclick="goToMonth(3)">Mar</button>
        <button class="month-btn" onclick="goToMonth(4)">Apr</button>
        <button class="month-btn" onclick="goToMonth(5)">May</button>
        <button class="month-btn" onclick="goToMonth(6)">Jun</button>
        <button class="month-btn" onclick="goToMonth(7)">Jul</button>
        <button class="month-btn" onclick="goToMonth(8)">Aug</button>
        <button class="month-btn" onclick="goToMonth(9)">Sep</button>
        <button class="month-btn" onclick="goToMonth(10)">Oct</button>
        <button class="month-btn" onclick="goToMonth(11)">Nov</button>
        <button class="month-btn" onclick="goToMonth(12)">Dec</button>
    </div>
</div>

<!-- Lunar Event Modal -->
<div id="lunarModal" class="lunar-modal">
    <div class="lunar-modal-content">
        <span class="modal-close" onclick="closeLunarModal()">&times;</span>
        <div id="modalContent">
            <!-- Dynamic content will be loaded here -->
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const calendarEl = document.getElementById('lunar-calendar');
    
    if (!calendarEl) {
        console.error('❌ Calendar container not found! Element with ID "lunar-calendar" does not exist.');
        return;
    }
    
    console.log('✅ Calendar container found, initializing FullCalendar...');
    
    // Helper function to get event colors
    function getEventColor(eventType) {
        const colors = {
            'amavasya': '#2c3e50',
            'pournami': '#f39c12', 
            'astami': '#e74c3c',
            'navami': '#9b59b6'
        };
        return colors[eventType] || '#95a5a6';
    }
    
    // Initialize FullCalendar
    const calendar = new FullCalendar.Calendar(calendarEl, {
        initialView: 'dayGridMonth',
        headerToolbar: {
            left: 'prev,next today',
            center: 'title',
            right: 'dayGridMonth,dayGridYear'
        },
        height: 'auto',
        eventDisplay: 'block',
        dayMaxEvents: false,
        events: function(fetchInfo, successCallback, failureCallback) {
            // Fetch lunar events from API
            console.log('🔍 FullCalendar requesting events for:', fetchInfo.startStr, 'to', fetchInfo.endStr);
            
            fetch(`/lunar/api/calendar/public/?start=${fetchInfo.startStr}&end=${fetchInfo.endStr}`)
                .then(response => {
                    console.log('📡 API Response received:', response.status, response.statusText);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('📊 Raw API data:', data);
                    console.log('📅 Events in data:', data.events ? data.events.length : 'No events array');
                    
                    if (!data.events || !Array.isArray(data.events)) {
                        console.error('❌ Invalid data format - events is not an array:', data);
                        successCallback([]);
                        return;
                    }
                    
                    const events = data.events.map(event => {
                        console.log('🎯 Processing event:', event.title, 'Type:', event.type);
                        return {
                            id: event.id,
                            title: event.title,
                            start: event.date,
                            backgroundColor: getEventColor(event.type),
                            borderColor: getEventColor(event.type),
                            textColor: 'white',
                            extendedProps: {
                                tithi: event.tithi,
                                phase_info: event.phase_info,
                                recommendations: event.recommendations,
                                importance: event.importance,
                                type: event.type
                            }
                        };
                    });
                    
                    console.log('🎯 Final events for FullCalendar:', events);
                    successCallback(events);
                })
                .catch(error => {
                    console.error('❌ Error fetching lunar events:', error);
                    failureCallback(error);
                });
        },
        eventClick: function(info) {
            showLunarEventDetails(info.event);
        },
        eventDidMount: function(info) {
            console.log('🎨 Event mounted:', info.event.title, 'Type:', info.event.extendedProps.type);
            
            // Add CSS class based on event type
            if (info.event.extendedProps.type) {
                const className = `fc-event-${info.event.extendedProps.type}`;
                info.el.classList.add(className);
                console.log('  Added CSS class:', className);
            }
            
            // Add tooltip
            info.el.title = `${info.event.title} - ${info.event.extendedProps.tithi}`;
        },
        dayCellDidMount: function(info) {
            // Add custom styling to cells
            const date = info.date;
            const today = new Date();
            
            if (date.toDateString() === today.toDateString()) {
                info.el.classList.add('fc-day-today');
            }
        }
    });
    
    calendar.render();
    
    // Store calendar instance globally
    window.lunarCalendar = calendar;
});

function goToMonth(month) {
    const currentDate = window.lunarCalendar.getDate();
    const newDate = new Date(currentDate.getFullYear(), month - 1, 1);
    window.lunarCalendar.gotoDate(newDate);
    
    // Update active button
    document.querySelectorAll('.month-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

function showLunarEventDetails(event) {
    const modal = document.getElementById('lunarModal');
    const content = document.getElementById('modalContent');
    
    const props = event.extendedProps;
    
    content.innerHTML = `
        <h3>${event.title}</h3>
        <p><strong>Date:</strong> ${event.start.toLocaleDateString()}</p>
        <p><strong>Tithi:</strong> ${props.tithi}</p>
        <p><strong>Importance:</strong> <span class="badge bg-${props.importance === 'high' ? 'danger' : 'warning'}">${props.importance}</span></p>
        
        <h5>Phase Information:</h5>
        <ul>
            <li><strong>Phase:</strong> ${props.phase_info?.phase_name || 'N/A'}</li>
            <li><strong>Illumination:</strong> ${props.phase_info?.phase_percentage || 'N/A'}%</li>
        </ul>
        
        <h5>Shrimp Farm Recommendations:</h5>
        <ul>
            ${props.recommendations?.map(rec => `<li>${rec}</li>`).join('') || '<li>No specific recommendations</li>'}
        </ul>
        
        <div class="mt-3">
            <button class="btn btn-primary" onclick="closeLunarModal()">Close</button>
        </div>
    `;
    
    modal.style.display = 'block';
}

function closeLunarModal() {
    document.getElementById('lunarModal').style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('lunarModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}
</script>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- FullCalendar JS -->
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
</body>
</html>
