from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()


class Alert(models.Model):
    """Main alert model combining functionality from alerts and AI alerts"""
    
    TYPE_CHOICES = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('critical', 'Critical'),
        ('security', 'Security'),
        ('maintenance', 'Maintenance'),
        ('water_quality', 'Water Quality'),
        ('disease', 'Disease'),
        ('feeding', 'Feeding'),
        ('harvest', 'Harvest'),
        ('weather', 'Weather'),
        ('equipment', 'Equipment'),
        ('ai_prediction', 'AI Prediction'),
        ('anomaly', 'Anomaly Detection'),
        ('lunar', 'Lunar Event'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    TRIGGER_CHOICES = [
        ('manual', 'Manual'),
        ('automatic', 'Automatic'),
        ('ai_generated', 'AI Generated'),
        ('sensor_threshold', 'Sensor Threshold'),
        ('schedule', 'Scheduled'),
        ('rule_based', 'Rule Based'),
    ]
    
    # Basic alert information
    title = models.CharField(max_length=200)
    message = models.TextField()
    type = models.CharField(max_length=20, choices=TYPE_CHOICES, default='info')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    trigger_type = models.CharField(max_length=20, choices=TRIGGER_CHOICES, default='manual')
    
    # Metadata
    source = models.CharField(max_length=100, blank=True, help_text="Source system/module that generated the alert")
    severity_level = models.IntegerField(default=1, validators=[MinValueValidator(1), MaxValueValidator(10)], 
                                       help_text="Numeric severity level (1-10)")
    
    # Associations
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='user_alerts')
    pond = models.ForeignKey('ponds.Pond', on_delete=models.CASCADE, null=True, blank=True, related_name='pond_alerts')
    farm = models.ForeignKey('ponds.Farm', on_delete=models.CASCADE, null=True, blank=True, related_name='farm_alerts')
    
    # Status tracking
    is_active = models.BooleanField(default=True)
    is_read = models.BooleanField(default=False)
    is_acknowledged = models.BooleanField(default=False)
    is_resolved = models.BooleanField(default=False)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    scheduled_for = models.DateTimeField(null=True, blank=True, help_text="When alert should be displayed")
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When alert should be automatically cleared")
    
    # User tracking
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                                      related_name='acknowledged_user_alerts')
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, 
                                  related_name='resolved_user_alerts')
    
    # Additional data
    data = models.JSONField(default=dict, blank=True, help_text="Additional alert-specific data")
    recommendations = models.JSONField(default=list, blank=True, help_text="Recommended actions")
    
    # Notification tracking
    notification_sent = models.BooleanField(default=False)
    notification_channels = models.JSONField(default=list, blank=True, help_text="Channels where notification was sent")
    
    # Backwards compatibility fields
    read = models.BooleanField(default=False)  # Maps to is_read
    scheduled_date = models.DateField(null=True, blank=True)  # Maps to scheduled_for
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['type', 'is_active', '-created_at']),
            models.Index(fields=['user', 'is_read', '-created_at']),
            models.Index(fields=['pond', '-created_at']),
            models.Index(fields=['farm', '-created_at']),
            models.Index(fields=['priority', 'is_resolved', '-created_at']),
            models.Index(fields=['severity_level', '-created_at']),
            models.Index(fields=['scheduled_for']),
        ]
        verbose_name = 'Alert'
        verbose_name_plural = 'Alerts'
    
    def __str__(self):
        return f"{self.title} ({self.get_type_display()})"
    
    def save(self, *args, **kwargs):
        # Sync backwards compatibility fields
        if self.is_read != self.read:
            self.read = self.is_read
        if self.scheduled_for and not self.scheduled_date:
            self.scheduled_date = self.scheduled_for.date()
        elif self.scheduled_date and not self.scheduled_for:
            self.scheduled_for = timezone.datetime.combine(self.scheduled_date, timezone.datetime.min.time())
        super().save(*args, **kwargs)
    
    def acknowledge(self, user):
        """Mark alert as acknowledged"""
        self.is_acknowledged = True
        self.acknowledged_at = timezone.now()
        self.acknowledged_by = user
        self.save(update_fields=['is_acknowledged', 'acknowledged_at', 'acknowledged_by'])
    
    def resolve(self, user):
        """Mark alert as resolved"""
        self.is_resolved = True
        self.resolved_at = timezone.now()
        self.resolved_by = user
        self.save(update_fields=['is_resolved', 'resolved_at', 'resolved_by'])
    
    def mark_read(self):
        """Mark alert as read"""
        self.is_read = True
        self.read = True
        self.save(update_fields=['is_read', 'read'])
    
    @property
    def timestamp(self):
        """Return a human-readable timestamp - backwards compatibility"""
        return self.age
    
    @property
    def age(self):
        """Get alert age in human readable format"""
        now = timezone.now()
        diff = now - self.created_at
        
        if diff.days == 0:
            if diff.seconds < 60:
                return 'Just now'
            elif diff.seconds < 3600:
                minutes = diff.seconds // 60
                return f'{minutes} minute{"s" if minutes > 1 else ""} ago'
            else:
                hours = diff.seconds // 3600
                return f'{hours} hour{"s" if hours > 1 else ""} ago'
        elif diff.days == 1:
            return 'Yesterday'
        else:
            return self.created_at.strftime('%b %d, %Y')
    
    @property
    def is_expired(self):
        """Check if alert has expired"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False
    
    @property
    def is_scheduled(self):
        """Check if alert is scheduled for future"""
        if self.scheduled_for:
            return timezone.now() < self.scheduled_for
        elif self.scheduled_date:
            return timezone.now().date() < self.scheduled_date
        return False
        """Check if alert is scheduled for future"""
        return self.scheduled_date is not None and self.scheduled_date > timezone.now().date()


class AlertSetting(models.Model):
    """User preferences for alert notifications"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='alert_settings')

    # Email notification settings
    email_notifications = models.BooleanField(default=True, help_text="Receive email notifications for alerts")
    email_critical_only = models.BooleanField(default=False, help_text="Only receive emails for critical alerts")
    daily_digest = models.BooleanField(default=False, help_text="Receive a daily digest instead of immediate notifications")

    # Push notification settings (for mobile app)
    push_notifications = models.BooleanField(default=True, help_text="Receive push notifications for alerts")
    push_critical_only = models.BooleanField(default=False, help_text="Only receive push notifications for critical alerts")

    # Alert type preferences
    notify_water_quality = models.BooleanField(default=True, help_text="Receive water quality alerts")
    notify_disease = models.BooleanField(default=True, help_text="Receive disease alerts")
    notify_predictive = models.BooleanField(default=True, help_text="Receive predictive alerts")
    notify_maintenance = models.BooleanField(default=True, help_text="Receive maintenance alerts")

    # Notification schedule
    quiet_hours_start = models.TimeField(null=True, blank=True, help_text="Start of quiet hours (no notifications)")
    quiet_hours_end = models.TimeField(null=True, blank=True, help_text="End of quiet hours (no notifications)")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Alert settings for {self.user.username}"

    def should_notify_for_alert(self, alert):
        """Check if user should be notified for this alert type"""
        if alert.type == 'critical':
            return True  # Always notify for critical alerts

        # Check alert type preferences
        if alert.title.startswith('[Water Quality]') and not self.notify_water_quality:
            return False
        if alert.title.startswith('[Disease]') and not self.notify_disease:
            return False
        if alert.title.startswith('[Prediction]') and not self.notify_predictive:
            return False
        if alert.title.startswith('[Maintenance]') and not self.notify_maintenance:
            return False

        return True

    def is_quiet_hours(self):
        """Check if current time is within quiet hours"""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        now = timezone.localtime().time()

        # Handle case where quiet hours span midnight
        if self.quiet_hours_start > self.quiet_hours_end:
            return now >= self.quiet_hours_start or now <= self.quiet_hours_end
        else:
            return self.quiet_hours_start <= now <= self.quiet_hours_end


class AlertRule(models.Model):
    """Rules for automatic alert generation"""
    
    RULE_TYPES = [
        ('threshold', 'Threshold Based'),
        ('pattern', 'Pattern Based'),
        ('ml_prediction', 'ML Prediction'),
        ('anomaly', 'Anomaly Detection'),
        ('schedule', 'Schedule Based'),
        ('condition', 'Condition Based'),
    ]
    
    COMPARISON_OPERATORS = [
        ('gt', 'Greater Than'),
        ('gte', 'Greater Than or Equal'),
        ('lt', 'Less Than'),
        ('lte', 'Less Than or Equal'),
        ('eq', 'Equal'),
        ('ne', 'Not Equal'),
        ('in', 'In Range'),
        ('out', 'Out of Range'),
    ]
    
    # Basic rule information
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    rule_type = models.CharField(max_length=20, choices=RULE_TYPES)
    is_active = models.BooleanField(default=True)
    
    # Scope
    farms = models.ManyToManyField('ponds.Farm', blank=True, related_name='farm_alert_rules')
    ponds = models.ManyToManyField('ponds.Pond', blank=True, related_name='pond_alert_rules')
    
    # Condition definition
    parameter = models.CharField(max_length=100, help_text="Parameter to monitor (e.g., 'temperature', 'ph')")
    operator = models.CharField(max_length=10, choices=COMPARISON_OPERATORS)
    threshold_value = models.FloatField(null=True, blank=True)
    threshold_min = models.FloatField(null=True, blank=True)
    threshold_max = models.FloatField(null=True, blank=True)
    
    # ML Model association (for ML-based alerts)
    ml_model = models.ForeignKey('ai_ml.MLModel', on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='alert_rules',
                               help_text="ML model to use for predictions in this alert rule")
    
    # Lunar phase configuration
    lunar_phase = models.CharField(max_length=50, null=True, blank=True,
                                           help_text="Lunar phase to trigger alert (e.g., 'Amavasya', 'Pournami')")
    lunar_days_before = models.IntegerField(null=True, blank=True,
                                                help_text="Days before lunar event to trigger alert")
    
    # Weather condition configuration
    weather_condition = models.CharField(max_length=100, null=True, blank=True,
                                       help_text="Weather condition to trigger alert (e.g., 'Heavy Rain', 'High Temperature')")
    
    # Time-based conditions
    time_window_minutes = models.IntegerField(default=60, help_text="Time window to evaluate condition")
    consecutive_violations = models.IntegerField(default=1, help_text="Number of consecutive violations to trigger alert")
    
    # Anomaly detection settings
    anomaly_threshold = models.FloatField(null=True, blank=True,
                                        validators=[MinValueValidator(0), MaxValueValidator(1)],
                                              help_text="Minimum anomaly score to trigger alert")
    
    # Schedule settings
    schedule_cron = models.CharField(max_length=100, blank=True, help_text="Cron expression for scheduled alerts")
    
    # Alert generation
    alert_type = models.CharField(max_length=20, choices=Alert.TYPE_CHOICES, default='warning')
    alert_priority = models.CharField(max_length=10, choices=Alert.PRIORITY_CHOICES, default='medium')
    
    # Alert message template
    alert_title_template = models.CharField(max_length=200, 
                                          help_text="Template for alert title with placeholders")
    alert_message_template = models.TextField(
        help_text="Template for alert message with placeholders")
    
    # Recommendations
    recommendations = models.JSONField(default=list, blank=True,
                                     help_text="Recommendations to address the alert")
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    last_triggered = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = "Alert Rule"
        verbose_name_plural = "Alert Rules"
    
    def __str__(self):
        return self.name
    
    def evaluate_condition(self, value):
        """Evaluate if the condition is met"""
        if self.operator == 'gt':
            return value > self.threshold_value
        elif self.operator == 'gte':
            return value >= self.threshold_value
        elif self.operator == 'lt':
            return value < self.threshold_value
        elif self.operator == 'lte':
            return value <= self.threshold_value
        elif self.operator == 'eq':
            return value == self.threshold_value
        elif self.operator == 'ne':
            return value != self.threshold_value
        elif self.operator == 'in':
            return self.threshold_min <= value <= self.threshold_max
        elif self.operator == 'out':
            return value < self.threshold_min or value > self.threshold_max
        return False
    
    def generate_alert(self, pond=None, farm=None, data=None):
        """Generate an alert based on this rule"""
        # Replace placeholders in templates
        title = self.alert_title_template
        message = self.alert_message_template
        
        if data:
            for key, value in data.items():
                title = title.replace(f"{{{key}}}", str(value))
                message = message.replace(f"{{{key}}}", str(value))
        
        alert = Alert.objects.create(
            title=title,
            message=message,
            type=self.alert_type,
            priority=self.alert_priority,
            trigger_type='rule_based',
            source=f"Alert Rule: {self.name}",
            pond=pond,
            farm=farm,
            data=data or {},
            recommendations=self.recommendations
        )
        
        # Update last triggered
        self.last_triggered = timezone.now()
        self.save(update_fields=['last_triggered'])
        
        return alert


class AlertNotificationSetting(models.Model):
    """User preferences for alert notifications"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='user_alert_notification_settings')

    # Email notification settings
    email_notifications = models.BooleanField(default=True, help_text="Receive email notifications for alerts")
    email_critical_only = models.BooleanField(default=False, help_text="Only receive emails for critical alerts")
    daily_digest = models.BooleanField(default=False, help_text="Receive a daily digest instead of immediate notifications")

    # Push notification settings (for mobile app)
    push_notifications = models.BooleanField(default=True, help_text="Receive push notifications for alerts")
    push_critical_only = models.BooleanField(default=False, help_text="Only receive push notifications for critical alerts")

    # Alert type preferences
    notify_water_quality = models.BooleanField(default=True, help_text="Receive water quality alerts")
    notify_disease = models.BooleanField(default=True, help_text="Receive disease alerts")
    notify_feeding = models.BooleanField(default=True, help_text="Receive feeding alerts")
    notify_harvest = models.BooleanField(default=True, help_text="Receive harvest alerts")
    notify_weather = models.BooleanField(default=True, help_text="Receive weather alerts")
    notify_equipment = models.BooleanField(default=True, help_text="Receive equipment alerts")
    notify_ai_prediction = models.BooleanField(default=True, help_text="Receive AI prediction alerts")
    notify_anomaly = models.BooleanField(default=True, help_text="Receive anomaly detection alerts")
    notify_lunar = models.BooleanField(default=True, help_text="Receive lunar event alerts")
    notify_security = models.BooleanField(default=True, help_text="Receive security alerts")
    notify_maintenance = models.BooleanField(default=True, help_text="Receive maintenance alerts")

    # Notification schedule
    quiet_hours_start = models.TimeField(null=True, blank=True, help_text="Start of quiet hours (no notifications)")
    quiet_hours_end = models.TimeField(null=True, blank=True, help_text="End of quiet hours (no notifications)")

    # Frequency settings
    max_notifications_per_hour = models.IntegerField(default=10, help_text="Maximum notifications per hour")
    batch_similar_alerts = models.BooleanField(default=True, help_text="Batch similar alerts together")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Alert Notification Setting'
        verbose_name_plural = 'Alert Notification Settings'

    def __str__(self):
        return f"{self.user.username} - Alert Settings"

    def should_notify(self, alert):
        """Check if user should be notified for this alert"""
        if not self.email_notifications and not self.push_notifications:
            return False
        
        # Check alert type preferences
        if alert.type == 'water_quality' and not self.notify_water_quality:
            return False
        if alert.type == 'disease' and not self.notify_disease:
            return False
        if alert.type == 'feeding' and not self.notify_feeding:
            return False
        if alert.type == 'harvest' and not self.notify_harvest:
            return False
        if alert.type == 'weather' and not self.notify_weather:
            return False
        if alert.type == 'equipment' and not self.notify_equipment:
            return False
        if alert.type == 'ai_prediction' and not self.notify_ai_prediction:
            return False
        if alert.type == 'anomaly' and not self.notify_anomaly:
            return False
        if alert.type == 'lunar' and not self.notify_lunar:
            return False
        if alert.type == 'security' and not self.notify_security:
            return False
        if alert.type == 'maintenance' and not self.notify_maintenance:
            return False

        return True

    def is_quiet_hours(self):
        """Check if current time is within quiet hours"""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        now = timezone.localtime().time()

        # Handle case where quiet hours span midnight
        if self.quiet_hours_start > self.quiet_hours_end:
            return now >= self.quiet_hours_start or now <= self.quiet_hours_end
        else:
            return self.quiet_hours_start <= now <= self.quiet_hours_end


class AlertHistory(models.Model):
    """History of alert state changes"""
    
    ACTION_CHOICES = [
        ('created', 'Created'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('escalated', 'Escalated'),
        ('closed', 'Closed'),
        ('reopened', 'Reopened'),
    ]
    
    alert = models.ForeignKey(Alert, on_delete=models.CASCADE, related_name='history')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    notes = models.TextField(blank=True)
    timestamp = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'Alert History'
        verbose_name_plural = 'Alert Histories'
    
    def __str__(self):
        return f"{self.alert.title} - {self.get_action_display()}"


class AlertTemplate(models.Model):
    """Templates for common alert types"""
    
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    alert_type = models.CharField(max_length=20, choices=Alert.TYPE_CHOICES)
    priority = models.CharField(max_length=10, choices=Alert.PRIORITY_CHOICES, default='medium')
    
    # Template content
    title_template = models.CharField(max_length=200)
    message_template = models.TextField()
    
    # Default settings
    default_recommendations = models.JSONField(default=list, blank=True)
    default_data = models.JSONField(default=dict, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']
        verbose_name = 'Alert Template'
        verbose_name_plural = 'Alert Templates'
    
    def __str__(self):
        return self.name
    
    def create_alert(self, context=None, **kwargs):
        """Create an alert from this template"""
        context = context or {}
        
        # Replace placeholders in templates
        title = self.title_template
        message = self.message_template
        
        for key, value in context.items():
            title = title.replace(f"{{{key}}}", str(value))
            message = message.replace(f"{{{key}}}", str(value))
        
        # Create alert with template defaults and provided overrides
        alert_data = {
            'title': title,
            'message': message,
            'type': self.alert_type,
            'priority': self.priority,
            'trigger_type': 'template',
            'recommendations': self.default_recommendations,
            'data': {**self.default_data, **context}
        }
        alert_data.update(kwargs)
        
        return Alert.objects.create(**alert_data)


# Backwards compatibility - alias for existing code
SystemAlert = Alert  # For code that references SystemAlert from security app
AIAlertRule = AlertRule  # For code that references AIAlertRule from ai_ml app
