<!DOCTYPE html>
<html>
<head>
    <title>📱 Shrimp Farm Mobile App Demo</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .phone {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background: #1976D2;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            flex: 1;
            background: #f5f5f5;
            overflow-y: auto;
        }
        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            color: white;
        }
        .greeting { font-size: 16px; opacity: 0.9; }
        .user-name { font-size: 24px; font-weight: 700; margin: 4px 0; }
        .date { font-size: 14px; opacity: 0.8; }
        .offline { 
            background: rgba(255,152,0,0.9); 
            padding: 8px 12px; 
            border-radius: 16px; 
            font-size: 12px; 
            margin-top: 12px; 
            display: inline-block;
        }
        .search {
            padding: 16px;
            background: white;
        }
        .search-box {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 16px;
            border: none;
            width: 100%;
        }
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 16px;
        }
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .stat-card:hover { transform: translateY(-2px); }
        .stat-icon { font-size: 32px; margin-bottom: 8px; }
        .stat-number { font-size: 28px; font-weight: 700; margin-bottom: 4px; }
        .stat-label { font-size: 12px; color: #666; font-weight: 600; }
        .progress {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        .progress-title { font-size: 16px; font-weight: 600; }
        .progress-rate { font-size: 24px; font-weight: 700; color: #4CAF50; }
        .progress-bar {
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 85%;
            border-radius: 4px;
            animation: fill 2s ease-out;
        }
        @keyframes fill { from { width: 0%; } to { width: 85%; } }
        .actions {
            padding: 0 16px 16px;
        }
        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            padding: 0 4px;
        }
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        .action-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s;
        }
        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .action-icon { font-size: 24px; color: #2196F3; margin-bottom: 8px; }
        .action-text { font-size: 14px; font-weight: 600; color: #333; }
        .tasks {
            padding: 0 16px 16px;
        }
        .task-card {
            background: white;
            margin-bottom: 8px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #F44336;
            cursor: pointer;
            transition: all 0.2s;
        }
        .task-card:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .task-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .task-title { font-size: 16px; font-weight: 600; color: #333; flex: 1; }
        .priority {
            background: #F44336;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }
        .task-info { font-size: 12px; color: #666; margin-bottom: 12px; }
        .task-buttons {
            display: flex;
            gap: 8px;
        }
        .task-btn {
            padding: 8px 12px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-start { background: #2196F3; color: white; }
        .btn-cancel { background: #F44336; color: white; }
        .nav {
            height: 80px;
            background: white;
            display: flex;
            border-top: 1px solid #e0e0e0;
        }
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        .nav-item:hover { background: #f5f5f5; }
        .nav-item.active { color: #2196F3; }
        .nav-item:not(.active) { color: #757575; }
        .nav-icon { font-size: 24px; margin-bottom: 4px; }
        .nav-label { font-size: 12px; font-weight: 600; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body>
    <div class="phone">
        <div class="screen">
            <div class="status-bar">
                <div>🔋100% 📶 WiFi</div>
                <div id="time">2:30 PM</div>
            </div>
            
            <div class="content">
                <div class="header">
                    <div class="greeting">Good Afternoon</div>
                    <div class="user-name">Field Worker</div>
                    <div class="date">Wednesday, January 17, 2025</div>
                    <div class="offline">🔄 Working Offline - 3 pending sync</div>
                </div>

                <div class="search">
                    <input class="search-box" placeholder="🔍 Search tasks..." readonly>
                </div>

                <div class="stats">
                    <div class="stat-card" style="background: #E3F2FD;" onclick="notify('📅 Today: 12 tasks scheduled')">
                        <div class="stat-icon">📅</div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">Today's Tasks</div>
                    </div>
                    <div class="stat-card" style="background: #FFF3E0;" onclick="notify('▶️ In Progress: 3 active tasks')">
                        <div class="stat-icon">▶️</div>
                        <div class="stat-number">3</div>
                        <div class="stat-label">In Progress</div>
                    </div>
                    <div class="stat-card" style="background: #E8F5E8;" onclick="notify('✅ Completed: 8 tasks finished!')">
                        <div class="stat-icon">✅</div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card" style="background: #FFEBEE;" onclick="notify('⚠️ Overdue: 2 tasks need attention')">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-number">2</div>
                        <div class="stat-label">Overdue</div>
                    </div>
                </div>

                <div class="progress">
                    <div class="progress-header">
                        <div class="progress-title">Completion Rate</div>
                        <div class="progress-rate">85%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div style="font-size: 12px; color: #666;">8 of 12 tasks completed</div>
                </div>

                <div class="actions">
                    <div class="section-title">Quick Actions</div>
                    <div class="actions-grid">
                        <div class="action-card" onclick="notify('📝 Opening task creation form...')">
                            <div class="action-icon">➕</div>
                            <div class="action-text">Create Task</div>
                        </div>
                        <div class="action-card" onclick="notify('📷 Activating QR scanner...')">
                            <div class="action-icon">📷</div>
                            <div class="action-text">Scan QR</div>
                        </div>
                        <div class="action-card" onclick="notify('💧 Starting water quality test...')">
                            <div class="action-icon">💧</div>
                            <div class="action-text">Water Test</div>
                        </div>
                        <div class="action-card" onclick="notify('📊 Loading reports...')">
                            <div class="action-icon">📊</div>
                            <div class="action-text">Reports</div>
                        </div>
                    </div>
                </div>

                <div class="tasks">
                    <div class="section-title">Priority Tasks</div>
                    <div class="task-card" onclick="notify('📋 Opening Water Quality Check details...')">
                        <div class="task-header">
                            <div class="task-title">💧 Water Quality Check</div>
                            <div class="priority">URGENT</div>
                        </div>
                        <div class="task-info">📍 Pond A-1 • ⏰ Due: 14:00 • ⏱️ 30 min</div>
                        <div class="task-buttons">
                            <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Water Quality Check')">▶️ Start</button>
                            <button class="task-btn btn-cancel" onclick="event.stopPropagation(); cancelTask('Water Quality Check')">❌ Cancel</button>
                        </div>
                    </div>

                    <div class="task-card" style="border-left-color: #FF9800;" onclick="notify('🔧 Opening Equipment Maintenance...')">
                        <div class="task-header">
                            <div class="task-title">🔧 Equipment Maintenance</div>
                            <div class="priority" style="background: #FF9800;">HIGH</div>
                        </div>
                        <div class="task-info">📍 Pump Station • ⏰ Due: 16:30 • ⏱️ 1 hour</div>
                        <div class="task-buttons">
                            <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Equipment Maintenance')">▶️ Start</button>
                            <button class="task-btn btn-cancel" onclick="event.stopPropagation(); cancelTask('Equipment Maintenance')">❌ Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="nav">
                <div class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">Dashboard</div>
                </div>
                <div class="nav-item" onclick="notify('📋 Task list loading...')">
                    <div class="nav-icon">📋</div>
                    <div class="nav-label">Tasks</div>
                </div>
                <div class="nav-item" onclick="notify('📷 QR Scanner activating...')">
                    <div class="nav-icon">📷</div>
                    <div class="nav-label">Scanner</div>
                </div>
                <div class="nav-item" onclick="notify('📈 Analytics loading...')">
                    <div class="nav-icon">📈</div>
                    <div class="nav-label">Reports</div>
                </div>
                <div class="nav-item" onclick="notify('👤 Profile opening...')">
                    <div class="nav-icon">👤</div>
                    <div class="nav-label">Profile</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('time').textContent = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }
        setInterval(updateTime, 1000);
        updateTime();

        function notify(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        function startTask(taskName) {
            notify(`🚀 Starting "${taskName}" - Timer activated, location recorded`);
            setTimeout(() => notify(`✅ "${taskName}" is now in progress`), 2000);
        }

        function cancelTask(taskName) {
            notify(`❌ "${taskName}" cancelled - Reason logged, sync queued`);
        }

        // Welcome message
        setTimeout(() => notify('🎉 Welcome to Shrimp Farm Field Worker App! Click any element to interact.'), 1000);
    </script>
</body>
</html>
