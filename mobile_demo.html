<!DOCTYPE html>
<html>
<head>
    <title>📱 Shrimp Farm Mobile App Demo</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        .phone {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.4);
        }
        .screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .status-bar {
            height: 44px;
            background: #1976D2;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .content {
            flex: 1;
            background: #f5f5f5;
            overflow-y: auto;
        }
        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            color: white;
        }
        .greeting { font-size: 16px; opacity: 0.9; }
        .user-name { font-size: 24px; font-weight: 700; margin: 4px 0; }
        .date { font-size: 14px; opacity: 0.8; }
        .location-status {
            font-size: 13px;
            opacity: 0.9;
            margin: 8px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .offline {
            background: rgba(255, 152, 0, 0.9);
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-top: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }
        .gps-indicator {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        .search {
            padding: 16px;
            background: white;
        }
        .search-box {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 16px;
            font-size: 16px;
            border: none;
            width: 100%;
        }
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 16px;
        }
        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }
        .stat-card:hover { transform: translateY(-2px); }
        .stat-icon { font-size: 32px; margin-bottom: 8px; }
        .stat-number { font-size: 28px; font-weight: 700; margin-bottom: 4px; }
        .stat-label { font-size: 12px; color: #666; font-weight: 600; }
        .progress {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .progress-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
        }
        .progress-title { font-size: 16px; font-weight: 600; }
        .progress-rate { font-size: 24px; font-weight: 700; color: #4CAF50; }
        .progress-bar {
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 85%;
            border-radius: 4px;
            animation: fill 2s ease-out;
        }
        @keyframes fill { from { width: 0%; } to { width: 85%; } }
        .actions {
            padding: 0 16px 16px;
        }
        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            padding: 0 4px;
        }
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        .action-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s;
        }
        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .action-icon { font-size: 24px; color: #2196F3; margin-bottom: 8px; }
        .action-text { font-size: 14px; font-weight: 600; color: #333; }
        .tasks {
            padding: 0 16px 16px;
        }
        .task-card {
            background: white;
            margin-bottom: 8px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #F44336;
            cursor: pointer;
            transition: all 0.2s;
        }
        .task-card:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }
        .task-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .task-title { font-size: 16px; font-weight: 600; color: #333; flex: 1; }
        .priority {
            background: #F44336;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }
        .task-info { font-size: 12px; color: #666; margin-bottom: 12px; }
        .task-buttons {
            display: flex;
            gap: 8px;
        }
        .task-btn {
            padding: 8px 12px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-start { background: #2196F3; color: white; }
        .btn-cancel { background: #F44336; color: white; }
        .nav {
            height: 80px;
            background: white;
            display: flex;
            border-top: 1px solid #e0e0e0;
        }
        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        .nav-item:hover { background: #f5f5f5; }
        .nav-item.active { color: #2196F3; }
        .nav-item:not(.active) { color: #757575; }
        .nav-icon { font-size: 24px; margin-bottom: 4px; }
        .nav-label { font-size: 12px; font-weight: 600; }
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        /* Camera Modal Styles */
        .camera-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .camera-container {
            width: 90%;
            max-width: 400px;
            background: white;
            border-radius: 20px;
            padding: 20px;
            text-align: center;
        }

        .camera-preview {
            width: 100%;
            height: 300px;
            background: #f0f0f0;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #ccc;
            position: relative;
            overflow: hidden;
        }

        .camera-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 15px;
        }

        .camera-controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .camera-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-capture {
            background: #4CAF50;
            color: white;
            font-size: 16px;
        }

        .btn-close {
            background: #f44336;
            color: white;
        }

        .photo-gallery {
            display: flex;
            gap: 10px;
            overflow-x: auto;
            padding: 10px 0;
            margin-bottom: 15px;
        }

        .photo-thumbnail {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #ddd;
            cursor: pointer;
        }

        .task-completion-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 1000;
            display: none;
            justify-content: center;
            align-items: center;
        }

        .completion-form {
            width: 90%;
            max-width: 400px;
            background: white;
            border-radius: 20px;
            padding: 25px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .form-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }

        .photo-section {
            margin-bottom: 20px;
        }

        .add-photo-btn {
            width: 100%;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            background: #f9f9f9;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .add-photo-btn:hover {
            border-color: #2196F3;
            background: #f0f8ff;
            color: #2196F3;
        }
    </style>
</head>
<body>
    <div class="phone">
        <div class="screen">
            <div class="status-bar">
                <div>🔋100% 📶 WiFi</div>
                <div id="time">2:30 PM</div>
            </div>
            
            <div class="content">
                <div class="header">
                    <div class="greeting">Good Afternoon</div>
                    <div class="user-name">Field Worker - Rajesh Kumar</div>
                    <div class="date">Wednesday, January 17, 2025</div>
                    <div class="location-status">
                        🕐 <span id="work-duration">2h 15m</span> on shift
                    </div>
                    <div class="offline">🔄 Working Offline - 3 pending sync</div>
                </div>

                <div class="search">
                    <input class="search-box" placeholder="🔍 Search tasks..." readonly>
                </div>

                <div class="stats">
                    <div class="stat-card" style="background: #E3F2FD;" onclick="notify('📅 Today: 12 tasks scheduled')">
                        <div class="stat-icon">📅</div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">Today's Tasks</div>
                    </div>
                    <div class="stat-card" style="background: #FFF3E0;" onclick="notify('▶️ In Progress: 3 active tasks')">
                        <div class="stat-icon">▶️</div>
                        <div class="stat-number">3</div>
                        <div class="stat-label">In Progress</div>
                    </div>
                    <div class="stat-card" style="background: #E8F5E8;" onclick="notify('✅ Completed: 8 tasks finished!')">
                        <div class="stat-icon">✅</div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card" style="background: #FFEBEE;" onclick="notify('⚠️ Overdue: 2 tasks need attention')">
                        <div class="stat-icon">⚠️</div>
                        <div class="stat-number">2</div>
                        <div class="stat-label">Overdue</div>
                    </div>
                </div>



                <div class="progress">
                    <div class="progress-header">
                        <div class="progress-title">Completion Rate</div>
                        <div class="progress-rate">85%</div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div style="font-size: 12px; color: #666;">8 of 12 tasks completed</div>
                </div>

                <div class="actions">
                    <div class="section-title">Quick Actions</div>
                    <div class="actions-grid">
                        <div class="action-card" onclick="notify('📝 Opening task creation form...')">
                            <div class="action-icon">➕</div>
                            <div class="action-text">Create Task</div>
                        </div>
                        <div class="action-card" onclick="openCamera()">
                            <div class="action-icon">📷</div>
                            <div class="action-text">Take Photo</div>
                        </div>
                        <div class="action-card" onclick="openQRScanner()">
                            <div class="action-icon">📱</div>
                            <div class="action-text">Scan QR</div>
                        </div>
                        <div class="action-card" onclick="notify('📊 Loading performance reports...')">
                            <div class="action-icon">📊</div>
                            <div class="action-text">Reports</div>
                        </div>
                    </div>
                </div>

                <div class="tasks">
                    <div class="section-title">Priority Tasks</div>
                    <div class="task-card" onclick="notify('📋 Opening Water Quality Check details...')">
                        <div class="task-header">
                            <div class="task-title">💧 Water Quality Check</div>
                            <div class="priority">URGENT</div>
                        </div>
                        <div class="task-info">📍 Pond A-1 • ⏰ Due: 14:00 • ⏱️ 30 min</div>
                        <div class="task-buttons">
                            <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Water Quality Check')">▶️ Start</button>
                            <button class="task-btn" style="background: #FF9800; color: white;" onclick="event.stopPropagation(); openTaskCompletion('Water Quality Check')">📷 Complete</button>
                        </div>
                    </div>

                    <div class="task-card" style="border-left-color: #FF9800;" onclick="notify('🔧 Opening Equipment Maintenance...')">
                        <div class="task-header">
                            <div class="task-title">🔧 Equipment Maintenance</div>
                            <div class="priority" style="background: #FF9800;">HIGH</div>
                        </div>
                        <div class="task-info">📍 Pump Station • ⏰ Due: 16:30 • ⏱️ 1 hour</div>
                        <div class="task-buttons">
                            <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Equipment Maintenance')">▶️ Start</button>
                            <button class="task-btn btn-cancel" onclick="event.stopPropagation(); cancelTask('Equipment Maintenance')">❌ Cancel</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="nav">
                <div class="nav-item active">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">Dashboard</div>
                </div>
                <div class="nav-item" onclick="notify('📋 Task list loading...')">
                    <div class="nav-icon">📋</div>
                    <div class="nav-label">Tasks</div>
                </div>
                <div class="nav-item" onclick="openQRScanner()">
                    <div class="nav-icon">📷</div>
                    <div class="nav-label">Scanner</div>
                </div>
                <div class="nav-item" onclick="notify('📈 Analytics loading...')">
                    <div class="nav-icon">📈</div>
                    <div class="nav-label">Reports</div>
                </div>
                <div class="nav-item" onclick="notify('👤 Profile opening...')">
                    <div class="nav-icon">👤</div>
                    <div class="nav-label">Profile</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Camera Modal -->
    <div class="camera-modal" id="cameraModal">
        <div class="camera-container">
            <h3 style="margin-bottom: 15px; color: #333;">📷 Capture Task Photo</h3>
            <div class="camera-preview" id="cameraPreview">
                📷
                <video class="camera-video" id="cameraVideo" style="display: none;" autoplay playsinline></video>
            </div>
            <div class="camera-controls">
                <button class="camera-btn btn-capture" onclick="capturePhoto()">📸 Capture</button>
                <button class="camera-btn btn-close" onclick="closeCamera()">❌ Close</button>
            </div>
            <div class="photo-gallery" id="photoGallery"></div>
        </div>
    </div>

    <!-- QR Scanner Modal -->
    <div class="camera-modal" id="qrScannerModal">
        <div class="camera-container">
            <h3 style="margin-bottom: 15px; color: #333;">📱 QR Code Scanner</h3>
            <div class="camera-preview" id="qrPreview">
                📱
                <video class="camera-video" id="qrVideo" style="display: none;" autoplay playsinline></video>
                <div id="qrOverlay" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; border: 3px solid #4CAF50; border-radius: 15px; display: none;"></div>
            </div>
            <div class="camera-controls">
                <button class="camera-btn btn-capture" onclick="simulateQRScan()">📱 Simulate Scan</button>
                <button class="camera-btn btn-close" onclick="closeQRScanner()">❌ Close</button>
            </div>
            <div id="qrResult" style="margin-top: 15px; padding: 15px; background: #f0f8ff; border-radius: 8px; display: none;">
                <h4 style="margin-bottom: 10px; color: #333;">📋 Scanned Equipment</h4>
                <div id="qrResultContent"></div>
            </div>
        </div>
    </div>

    <!-- Task Completion Modal -->
    <div class="task-completion-modal" id="taskCompletionModal">
        <div class="completion-form">
            <h3 class="form-title" id="completionTaskName">Complete Task</h3>

            <div class="form-group">
                <label class="form-label">📝 Task Notes</label>
                <textarea class="form-textarea" id="taskNotes" placeholder="Enter task completion notes, observations, or issues..."></textarea>
            </div>

            <div class="photo-section">
                <label class="form-label">📷 Task Photos (<span id="photoCount">0</span>)</label>
                <div class="photo-gallery" id="completionPhotoGallery"></div>
                <button class="add-photo-btn" onclick="openCamera()">
                    📷 Add Photo
                </button>
            </div>

            <div style="display: flex; gap: 15px; margin-top: 25px;">
                <button class="camera-btn btn-capture" style="flex: 1;" onclick="submitTaskCompletion()">
                    ✅ Complete Task
                </button>
                <button class="camera-btn btn-close" style="flex: 1;" onclick="closeTaskCompletion()">
                    ❌ Cancel
                </button>
            </div>
        </div>
    </div>

    <script>
        function updateTime() {
            const now = new Date();
            document.getElementById('time').textContent = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }
        setInterval(updateTime, 1000);
        updateTime();

        function notify(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        }

        // Offline Task Management System
        let offlineTasks = JSON.parse(localStorage.getItem('offlineTasks') || '[]');
        let pendingSync = JSON.parse(localStorage.getItem('pendingSync') || '[]');
        let isOnline = navigator.onLine;

        // Update offline status indicator
        function updateOfflineStatus() {
            const indicator = document.querySelector('.offline');
            const pendingCount = pendingSync.length;

            if (isOnline && pendingCount === 0) {
                indicator.innerHTML = '🌐 Online - All data synced';
                indicator.style.background = 'rgba(76, 175, 80, 0.9)';
            } else if (isOnline && pendingCount > 0) {
                indicator.innerHTML = `🔄 Syncing - ${pendingCount} pending`;
                indicator.style.background = 'rgba(255, 152, 0, 0.9)';
            } else {
                indicator.innerHTML = `📱 Offline - ${pendingCount} cached`;
                indicator.style.background = 'rgba(255, 152, 0, 0.9)';
            }
        }

        function startTask(taskName) {
            const taskData = {
                id: Date.now(),
                name: taskName,
                status: 'in_progress',
                startTime: new Date().toISOString(),
                workerId: workerId,
                location: {
                    lat: currentCoords.lat,
                    lng: currentCoords.lng
                }
            };

            // Store task locally
            offlineTasks.push(taskData);
            localStorage.setItem('offlineTasks', JSON.stringify(offlineTasks));

            notify(`🚀 Starting "${taskName}"\n⏱️ Timer activated\n📍 Location recorded\n💾 Saved offline`);
            setTimeout(() => notify(`✅ "${taskName}" is now in progress`), 2000);

            updateOfflineStatus();
        }

        function completeTask(taskName, photos = [], notes = '') {
            const taskIndex = offlineTasks.findIndex(task => task.name === taskName && task.status === 'in_progress');

            if (taskIndex !== -1) {
                offlineTasks[taskIndex].status = 'completed';
                offlineTasks[taskIndex].completedTime = new Date().toISOString();
                offlineTasks[taskIndex].photos = photos;
                offlineTasks[taskIndex].notes = notes;
                offlineTasks[taskIndex].completionLocation = {
                    lat: currentCoords.lat,
                    lng: currentCoords.lng
                };

                // Add to pending sync queue
                pendingSync.push(offlineTasks[taskIndex]);

                // Update local storage
                localStorage.setItem('offlineTasks', JSON.stringify(offlineTasks));
                localStorage.setItem('pendingSync', JSON.stringify(pendingSync));

                notify(`✅ "${taskName}" completed!\n📸 ${photos.length} photos captured\n📝 Notes saved\n🔄 Queued for sync`);

                // Try to sync if online
                if (isOnline) {
                    syncPendingTasks();
                }
            }

            updateOfflineStatus();
        }

        function cancelTask(taskName) {
            const taskIndex = offlineTasks.findIndex(task => task.name === taskName);

            if (taskIndex !== -1) {
                offlineTasks[taskIndex].status = 'cancelled';
                offlineTasks[taskIndex].cancelledTime = new Date().toISOString();
                localStorage.setItem('offlineTasks', JSON.stringify(offlineTasks));
            }

            notify(`❌ "${taskName}" cancelled\n📝 Reason logged\n💾 Saved offline`);
            updateOfflineStatus();
        }

        // Sync pending tasks when online
        function syncPendingTasks() {
            if (!isOnline || pendingSync.length === 0) return;

            notify(`🔄 Syncing ${pendingSync.length} completed tasks...`);

            // Simulate API calls for each pending task
            pendingSync.forEach((task, index) => {
                setTimeout(() => {
                    // In real app: fetch('/labor/api/tasks/complete/', { method: 'POST', body: JSON.stringify(task) })
                    console.log('📤 Syncing task to server:', task);

                    // Remove from pending sync after successful upload
                    pendingSync.splice(index, 1);
                    localStorage.setItem('pendingSync', JSON.stringify(pendingSync));

                    if (index === pendingSync.length) {
                        notify('✅ All tasks synced successfully!');
                        updateOfflineStatus();
                    }
                }, index * 1000);
            });
        }

        // Network status monitoring
        window.addEventListener('online', () => {
            isOnline = true;
            notify('🌐 Connection restored! Auto-syncing data...');
            updateOfflineStatus();
            setTimeout(syncPendingTasks, 1000);
        });

        window.addEventListener('offline', () => {
            isOnline = false;
            notify('📱 Working offline - Data will sync when connected');
            updateOfflineStatus();
        });

        // Silent GPS Tracking (Background Service Simulation)
        let workerId = 'FW001'; // Field Worker ID
        let currentCoords = { lat: 10.7905, lng: 106.6800 }; // Sample coordinates
        let workStartTime = new Date(Date.now() - 2.25 * 60 * 60 * 1000); // 2h 15m ago

        function updateWorkDuration() {
            const now = new Date();
            const duration = now - workStartTime;
            const hours = Math.floor(duration / (1000 * 60 * 60));
            const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
            document.getElementById('work-duration').textContent = `${hours}h ${minutes}m`;
        }

        // Silent GPS tracking - sends data to labor management system
        function sendLocationToLaborManagement() {
            // Simulate GPS coordinates change (real app would use navigator.geolocation)
            currentCoords.lat += (Math.random() - 0.5) * 0.001;
            currentCoords.lng += (Math.random() - 0.5) * 0.001;

            const locationData = {
                latitude: currentCoords.lat,
                longitude: currentCoords.lng,
                accuracy: Math.random() * 5 + 3, // 3-8 meter accuracy
                task_status: 'active',
                battery_level: Math.floor(Math.random() * 30) + 70 // 70-100%
            };

            // Real API call to labor management system
            // fetch(`/labor/api/workers/${workerId}/update-location/`, {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //         'X-CSRFToken': getCookie('csrftoken')
            //     },
            //     body: JSON.stringify(locationData)
            // }).then(response => response.json())
            //   .then(data => console.log('📍 GPS data sent to Labor Management:', data))
            //   .catch(error => console.error('GPS sync error:', error));

            // For demo purposes, just log the data
            console.log('📍 Silent GPS Update sent to Labor Management:', {
                endpoint: `/labor/api/workers/${workerId}/update-location/`,
                data: locationData,
                timestamp: new Date().toISOString()
            });
        }

        function showLocationTracking() {
            notify(`📍 GPS Tracking Status:
🌐 Live Location: Active
📊 Coordinates: ${currentCoords.lat.toFixed(6)}, ${currentCoords.lng.toFixed(6)}
⏱️ Work Duration: ${document.getElementById('work-duration').textContent}
📍 Current Zone: ${document.getElementById('current-location').textContent}
🔄 Last Update: ${new Date().toLocaleTimeString()}`);
        }

        function showLocationMap() {
            notify(`🗺️ Opening Location Map...
📍 Your Position: ${document.getElementById('current-location').textContent}
👥 Team Members: 12 workers online
🎯 Nearby Tasks: 3 pending
📊 Coverage Area: 85% complete
🔄 Real-time tracking active`);
        }

        function showLaborManagement() {
            notify(`👥 Labor Management Dashboard:
📊 Team Status: 12/15 workers active
⏰ Shift Progress: 6h 15m / 8h
🎯 Tasks Completed: 68/85 (80%)
📍 Location Coverage: All zones monitored
🔔 Alerts: 2 pending check-ins
💼 Supervisor: Manager Online`);
        }

        // Auto-update functions
        setInterval(updateWorkDuration, 60000); // Update every minute
        setInterval(sendLocationToLaborManagement, 30000); // Send GPS data every 30 seconds
        updateWorkDuration(); // Initial call

        // Start silent GPS tracking immediately
        sendLocationToLaborManagement();

        // Enhanced GPS tracking simulation
        function startGPSTracking() {
            if (!gpsTrackingActive) {
                gpsTrackingActive = true;
                document.querySelector('.offline').innerHTML = '🌐 GPS Tracking Active - Live Location <div class="gps-indicator"></div>';
                notify('📍 GPS tracking activated - Continuous location monitoring started');
            }
        }

        function stopGPSTracking() {
            gpsTrackingActive = false;
            document.querySelector('.offline').innerHTML = '🔄 GPS Offline - Location cached';
            notify('📍 GPS tracking paused - Working in offline mode');
        }

        // Initialize GPS tracking
        startGPSTracking();

        // Photo Capture System
        let currentTaskPhotos = [];
        let currentStream = null;
        let currentTaskForCompletion = null;

        function openCamera() {
            const modal = document.getElementById('cameraModal');
            const video = document.getElementById('cameraVideo');
            const preview = document.getElementById('cameraPreview');

            modal.style.display = 'flex';

            // Request camera access
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment', // Use back camera
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            })
            .then(stream => {
                currentStream = stream;
                video.srcObject = stream;
                video.style.display = 'block';
                preview.style.fontSize = '0';
            })
            .catch(err => {
                console.error('Camera access denied:', err);
                notify('📷 Camera access denied. Using simulated capture.');
                // Fallback for demo - show camera icon
                preview.innerHTML = '📷<br><small>Camera Simulation</small>';
            });
        }

        function capturePhoto() {
            const video = document.getElementById('cameraVideo');
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');

            if (video.videoWidth > 0) {
                // Real camera capture
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0);
                const photoData = canvas.toDataURL('image/jpeg', 0.8);
                addPhotoToGallery(photoData);
            } else {
                // Simulated capture for demo
                const simulatedPhoto = generateSimulatedPhoto();
                addPhotoToGallery(simulatedPhoto);
            }

            notify('📸 Photo captured successfully!');
        }

        function generateSimulatedPhoto() {
            // Create a simulated photo with timestamp
            const canvas = document.createElement('canvas');
            canvas.width = 400;
            canvas.height = 300;
            const ctx = canvas.getContext('2d');

            // Background
            ctx.fillStyle = '#e3f2fd';
            ctx.fillRect(0, 0, 400, 300);

            // Add some visual elements
            ctx.fillStyle = '#2196f3';
            ctx.fillRect(50, 50, 300, 200);

            // Add text
            ctx.fillStyle = 'white';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('📷 Task Photo', 200, 130);
            ctx.fillText(new Date().toLocaleString(), 200, 160);
            ctx.fillText('Shrimp Farm Guardian', 200, 190);

            return canvas.toDataURL('image/jpeg', 0.8);
        }

        function addPhotoToGallery(photoData) {
            const photoObj = {
                id: Date.now(),
                data: photoData,
                timestamp: new Date().toISOString(),
                location: { lat: currentCoords.lat, lng: currentCoords.lng }
            };

            currentTaskPhotos.push(photoObj);
            updatePhotoGalleries();
        }

        function updatePhotoGalleries() {
            const galleries = ['photoGallery', 'completionPhotoGallery'];

            galleries.forEach(galleryId => {
                const gallery = document.getElementById(galleryId);
                if (gallery) {
                    gallery.innerHTML = currentTaskPhotos.map(photo =>
                        `<img src="${photo.data}" class="photo-thumbnail" onclick="viewPhoto('${photo.id}')" alt="Task photo">`
                    ).join('');
                }
            });

            // Update photo count
            const photoCount = document.getElementById('photoCount');
            if (photoCount) {
                photoCount.textContent = currentTaskPhotos.length;
            }
        }

        function closeCamera() {
            const modal = document.getElementById('cameraModal');
            const video = document.getElementById('cameraVideo');

            modal.style.display = 'none';

            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
            }

            video.style.display = 'none';
            video.srcObject = null;
        }

        function openTaskCompletion(taskName) {
            currentTaskForCompletion = taskName;
            const modal = document.getElementById('taskCompletionModal');
            const taskNameElement = document.getElementById('completionTaskName');

            taskNameElement.textContent = `Complete: ${taskName}`;
            modal.style.display = 'flex';

            updatePhotoGalleries();
        }

        function closeTaskCompletion() {
            const modal = document.getElementById('taskCompletionModal');
            modal.style.display = 'none';

            // Clear form
            document.getElementById('taskNotes').value = '';
            currentTaskPhotos = [];
            updatePhotoGalleries();
        }

        function submitTaskCompletion() {
            const notes = document.getElementById('taskNotes').value;
            const photos = currentTaskPhotos;

            if (currentTaskForCompletion) {
                completeTask(currentTaskForCompletion, photos, notes);
                closeTaskCompletion();
            }
        }

        function viewPhoto(photoId) {
            const photo = currentTaskPhotos.find(p => p.id == photoId);
            if (photo) {
                notify(`📷 Photo Details:\n📅 ${new Date(photo.timestamp).toLocaleString()}\n📍 Location: ${photo.location.lat.toFixed(6)}, ${photo.location.lng.toFixed(6)}`);
            }
        }

        // QR Code Scanner System
        let qrStream = null;
        const equipmentDatabase = {
            'EQ001': { name: 'Water Pump A1', location: 'Pond A-1', status: 'Active', lastMaintenance: '2025-01-10' },
            'EQ002': { name: 'Aerator B2', location: 'Pond B-2', status: 'Maintenance Required', lastMaintenance: '2024-12-15' },
            'EQ003': { name: 'Feed Dispenser C1', location: 'Feed Station', status: 'Active', lastMaintenance: '2025-01-05' },
            'EQ004': { name: 'Water Quality Sensor', location: 'Pond A-1', status: 'Active', lastMaintenance: '2025-01-12' },
            'EQ005': { name: 'Backup Generator', location: 'Equipment Shed', status: 'Standby', lastMaintenance: '2024-12-20' }
        };

        function openQRScanner() {
            const modal = document.getElementById('qrScannerModal');
            const video = document.getElementById('qrVideo');
            const preview = document.getElementById('qrPreview');
            const overlay = document.getElementById('qrOverlay');

            modal.style.display = 'flex';
            document.getElementById('qrResult').style.display = 'none';

            // Request camera access for QR scanning
            navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                }
            })
            .then(stream => {
                qrStream = stream;
                video.srcObject = stream;
                video.style.display = 'block';
                overlay.style.display = 'block';
                preview.style.fontSize = '0';

                notify('📱 QR Scanner active - Point camera at equipment QR code');
            })
            .catch(err => {
                console.error('Camera access denied:', err);
                notify('📱 Camera access denied. Use "Simulate Scan" button.');
                preview.innerHTML = '📱<br><small>QR Scanner Simulation</small>';
            });
        }

        function simulateQRScan() {
            // Simulate scanning a random equipment QR code
            const equipmentIds = Object.keys(equipmentDatabase);
            const randomId = equipmentIds[Math.floor(Math.random() * equipmentIds.length)];
            processQRCode(randomId);
        }

        function processQRCode(qrData) {
            const equipment = equipmentDatabase[qrData];

            if (equipment) {
                displayEquipmentInfo(qrData, equipment);
                notify(`📱 QR Code scanned successfully!\n🔧 Equipment: ${equipment.name}\n📍 Location: ${equipment.location}`);
            } else {
                notify(`❌ Unknown QR Code: ${qrData}\nPlease scan a valid equipment QR code.`);
            }
        }

        function displayEquipmentInfo(equipmentId, equipment) {
            const resultDiv = document.getElementById('qrResult');
            const contentDiv = document.getElementById('qrResultContent');

            const statusColor = equipment.status === 'Active' ? '#4CAF50' :
                               equipment.status === 'Maintenance Required' ? '#FF9800' : '#2196F3';

            contentDiv.innerHTML = `
                <div style="margin-bottom: 15px;">
                    <strong>🔧 ${equipment.name}</strong><br>
                    <small>ID: ${equipmentId}</small>
                </div>
                <div style="margin-bottom: 10px;">
                    📍 <strong>Location:</strong> ${equipment.location}
                </div>
                <div style="margin-bottom: 10px;">
                    🔄 <strong>Status:</strong> <span style="color: ${statusColor}; font-weight: 600;">${equipment.status}</span>
                </div>
                <div style="margin-bottom: 15px;">
                    🗓️ <strong>Last Maintenance:</strong> ${equipment.lastMaintenance}
                </div>
                <div style="display: flex; gap: 10px;">
                    <button class="camera-btn btn-capture" style="flex: 1; font-size: 12px;" onclick="createEquipmentTask('${equipmentId}')">
                        📋 Create Task
                    </button>
                    <button class="camera-btn" style="flex: 1; font-size: 12px; background: #FF9800;" onclick="reportIssue('${equipmentId}')">
                        ⚠️ Report Issue
                    </button>
                </div>
            `;

            resultDiv.style.display = 'block';
        }

        function createEquipmentTask(equipmentId) {
            const equipment = equipmentDatabase[equipmentId];
            const taskName = `Maintenance: ${equipment.name}`;

            // Add to task list
            const taskData = {
                id: Date.now(),
                name: taskName,
                equipmentId: equipmentId,
                location: equipment.location,
                priority: equipment.status === 'Maintenance Required' ? 'HIGH' : 'NORMAL',
                createdTime: new Date().toISOString()
            };

            notify(`📋 Task created successfully!\n🔧 ${taskName}\n📍 ${equipment.location}\n🎯 Priority: ${taskData.priority}`);
            closeQRScanner();
        }

        function reportIssue(equipmentId) {
            const equipment = equipmentDatabase[equipmentId];

            notify(`⚠️ Issue reported for ${equipment.name}\n📍 Location: ${equipment.location}\n🔔 Maintenance team notified\n📋 Ticket #${Date.now().toString().slice(-6)} created`);
            closeQRScanner();
        }

        function closeQRScanner() {
            const modal = document.getElementById('qrScannerModal');
            const video = document.getElementById('qrVideo');
            const overlay = document.getElementById('qrOverlay');

            modal.style.display = 'none';

            if (qrStream) {
                qrStream.getTracks().forEach(track => track.stop());
                qrStream = null;
            }

            video.style.display = 'none';
            overlay.style.display = 'none';
            video.srcObject = null;
            document.getElementById('qrResult').style.display = 'none';
        }

        // Welcome message
        setTimeout(() => notify('🎉 Welcome to Shrimp Farm Field Worker App!\n📍 GPS tracking active\n📷 Photo capture ready\n📱 QR scanner enabled'), 1000);
    </script>
</body>
</html>
