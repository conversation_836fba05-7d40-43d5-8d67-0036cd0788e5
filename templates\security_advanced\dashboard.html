<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Security Dashboard - Enterprise Security Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #f8fafc;
        }
        
        .security-container {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        
        .security-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .security-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: securityPattern 15s linear infinite;
        }
        
        @keyframes securityPattern {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }
        
        .security-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .security-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            animation: pulse-line 2s ease-in-out infinite;
        }
        
        @keyframes pulse-line {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border-color: rgba(148, 163, 184, 0.4);
        }
        
        .metric-card.critical { --accent-color: #dc2626; }
        .metric-card.warning { --accent-color: #f59e0b; }
        .metric-card.success { --accent-color: #16a34a; }
        .metric-card.info { --accent-color: #0ea5e9; }
        
        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-color) 100%);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #f8fafc;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .metric-value.updating {
            color: var(--accent-color);
            transform: scale(1.05);
        }
        
        .metric-label {
            color: #cbd5e1;
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .metric-change {
            font-size: 0.9rem;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
        }
        
        .metric-change.positive {
            background: rgba(220, 38, 38, 0.2);
            color: #fca5a5;
        }
        
        .metric-change.negative {
            background: rgba(22, 163, 74, 0.2);
            color: #86efac;
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-panel {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        }
        
        .panel-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #f8fafc;
        }
        
        .live-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #16a34a;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .live-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #16a34a;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .events-feed {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .event-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.3s ease;
        }
        
        .event-item:hover {
            background: rgba(51, 65, 85, 0.3);
        }
        
        .event-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            flex-shrink: 0;
        }
        
        .event-icon.critical { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
        .event-icon.high { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .event-icon.medium { background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%); }
        .event-icon.low { background: linear-gradient(135deg, #16a34a 0%, #15803d 100%); }
        
        .event-content {
            flex: 1;
        }
        
        .event-title {
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 5px;
        }
        
        .event-description {
            color: #cbd5e1;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .event-meta {
            display: flex;
            gap: 15px;
            font-size: 0.8rem;
            color: #94a3b8;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135dc, #0ea5e9 0%, #0284c7 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
            border: none;
            cursor: pointer;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
            color: white;
        }
        
        .quick-action.danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }
        
        .quick-action.danger:hover {
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        }
        
        @media (max-width: 768px) {
            .security-container {
                margin: 10px;
                padding: 20px;
            }
            
            .charts-section {
                grid-template-columns: 1fr;
            }
            
            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>

<div class="security-container">
    <!-- Security Header -->
    <div class="security-header">
        <h1 class="security-title">🔒 Security Dashboard</h1>
        <p class="security-subtitle">Enterprise Security & Compliance Center</p>
        
        <!-- Quick Actions -->
        <div class="quick-actions mt-4">
            <a href="{% url 'security_advanced:events' %}" class="quick-action">
                <i class="fas fa-list"></i>
                <span>View Events</span>
            </a>
            <a href="{% url 'security_advanced:audit_logs' %}" class="quick-action">
                <i class="fas fa-search"></i>
                <span>Audit Logs</span>
            </a>
            <a href="{% url 'security_advanced:compliance_reports' %}" class="quick-action">
                <i class="fas fa-file-alt"></i>
                <span>Compliance</span>
            </a>
            <button class="quick-action danger" onclick="refreshDashboard()">
                <i class="fas fa-sync"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Security Metrics -->
    <div class="metrics-grid">
        <div class="metric-card critical">
            <div class="metric-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="metric-value" id="total-events">{{ metrics.total_events_24h }}</div>
            <div class="metric-label">Security Events (24h)</div>
            <div class="metric-change positive">+{{ metrics.total_events_24h }} today</div>
        </div>
        
        <div class="metric-card warning">
            <div class="metric-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="metric-value" id="critical-events">{{ metrics.critical_events_24h }}</div>
            <div class="metric-label">Critical Events (24h)</div>
            <div class="metric-change {% if metrics.critical_events_24h > 0 %}positive{% else %}negative{% endif %}">
                {% if metrics.critical_events_24h > 0 %}+{{ metrics.critical_events_24h }}{% else %}No critical events{% endif %}
            </div>
        </div>
        
        <div class="metric-card info">
            <div class="metric-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="metric-value" id="active-sessions">{{ metrics.active_sessions }}</div>
            <div class="metric-label">Active Sessions</div>
            <div class="metric-change {% if metrics.suspicious_sessions > 0 %}positive{% else %}negative{% endif %}">
                {{ metrics.suspicious_sessions }} suspicious
            </div>
        </div>
        
        <div class="metric-card warning">
            <div class="metric-icon">
                <i class="fas fa-ban"></i>
            </div>
            <div class="metric-value" id="failed-logins">{{ metrics.failed_logins_24h }}</div>
            <div class="metric-label">Failed Logins (24h)</div>
            <div class="metric-change {% if metrics.failed_logins_24h > 0 %}positive{% else %}negative{% endif %}">
                {% if metrics.failed_logins_24h > 0 %}+{{ metrics.failed_logins_24h }}{% else %}No failures{% endif %}
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <!-- Security Trends Chart -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">Security Event Trends</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            <canvas id="security-trends-chart" width="400" height="200"></canvas>
        </div>

        <!-- Recent Events Feed -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">Recent Security Events</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            
            <div class="events-feed" id="events-feed">
                {% for event in recent_events %}
                <div class="event-item">
                    <div class="event-icon {{ event.severity }}">
                        <i class="fas fa-{% if event.event_type == 'login_failed' %}ban{% elif event.event_type == 'suspicious_activity' %}exclamation-triangle{% elif event.event_type == 'data_access' %}eye{% else %}shield-alt{% endif %}"></i>
                    </div>
                    <div class="event-content">
                        <div class="event-title">{{ event.get_event_type_display }}</div>
                        <div class="event-description">{{ event.description|truncatechars:80 }}</div>
                        <div class="event-meta">
                            <span><i class="fas fa-user"></i> {{ event.user.username|default:"Anonymous" }}</span>
                            <span><i class="fas fa-map-marker-alt"></i> {{ event.ip_address }}</span>
                            <span><i class="fas fa-clock"></i> {{ event.timestamp|timesince }} ago</span>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shield-alt fa-3x mb-3"></i>
                    <p>No recent security events</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Security Dashboard JavaScript
let securityChart = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔒 Security Dashboard initializing...');
    
    initializeSecurityChart();
    startRealTimeUpdates();
});

function initializeSecurityChart() {
    const ctx = document.getElementById('security-trends-chart').getContext('2d');
    
    securityChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Total Events',
                data: [],
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Critical Events',
                data: [],
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                tension: 0.4,
                fill: false
            }, {
                label: 'Failed Logins',
                data: [],
                borderColor: '#0ea5e9',
                backgroundColor: 'rgba(14, 165, 233, 0.1)',
                tension: 0.4,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top',
                    labels: {
                        color: '#f8fafc'
                    }
                }
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Time',
                        color: '#cbd5e1'
                    },
                    ticks: {
                        color: '#94a3b8'
                    },
                    grid: {
                        color: 'rgba(148, 163, 184, 0.2)'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Events',
                        color: '#cbd5e1'
                    },
                    ticks: {
                        color: '#94a3b8'
                    },
                    grid: {
                        color: 'rgba(148, 163, 184, 0.2)'
                    }
                }
            }
        }
    });
    
    // Load initial data
    loadSecurityAnalytics();
}

function loadSecurityAnalytics() {
    fetch('{% url "security_advanced:analytics_api" %}?days=7')
        .then(response => response.json())
        .then(data => {
            updateSecurityChart(data.event_trends);
        })
        .catch(error => {
            console.error('Failed to load security analytics:', error);
        });
}

function updateSecurityChart(trends) {
    if (!securityChart || !trends.length) return;
    
    const labels = trends.map(item => item.date);
    const totalEvents = trends.map(item => item.total_events);
    const criticalEvents = trends.map(item => item.critical_events);
    const failedLogins = trends.map(item => item.failed_logins || 0);
    
    securityChart.data.labels = labels;
    securityChart.data.datasets[0].data = totalEvents;
    securityChart.data.datasets[1].data = criticalEvents;
    securityChart.data.datasets[2].data = failedLogins;
    
    securityChart.update('none');
}

function startRealTimeUpdates() {
    // Update dashboard every 30 seconds
    setInterval(() => {
        refreshDashboard();
    }, 30000);
}

function refreshDashboard() {
    console.log('🔄 Refreshing security dashboard...');
    
    // Animate refresh
    document.querySelectorAll('.metric-value').forEach(el => {
        el.classList.add('updating');
        setTimeout(() => {
            el.classList.remove('updating');
        }, 500);
    });
    
    // Reload analytics
    loadSecurityAnalytics();
    
    // In a real implementation, this would fetch updated metrics
    // For demo, we'll just show the refresh animation
    setTimeout(() => {
        console.log('✅ Dashboard refreshed');
    }, 1000);
}

// Auto-refresh every 5 minutes
setInterval(refreshDashboard, 300000);
</script>

</body>
</html>
