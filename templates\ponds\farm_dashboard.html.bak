{% extends "base.html" %}
{% load static %}

{% block title %}Farm Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s;
        height: 100%;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
    }
    
    .stats-value {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .farm-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s;
        height: 100%;
    }
    
    .farm-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .farm-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .farm-body {
        padding: 1rem;
    }
    
    .farm-footer {
        padding: 1rem;
        background-color: #f8f9fa;
        border-top: 1px solid rgba(0,0,0,0.1);
    }
    
    .map-container {
        height: 400px;
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Farm Dashboard</h1>
        <div class="btn-group">
            <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i> All Farms
            </a>
            <a href="{% url 'ponds:farm_create' %}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i> Add Farm
            </a>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_farms }}</div>
                        <div class="stats-label">Total Farms</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-water"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_ponds }}</div>
                        <div class="stats-label">Total Ponds</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_area|floatformat:2 }}</div>
                        <div class="stats-label">Total Area (hectares)</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-fish"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ active_ponds }}</div>
                        <div class="stats-label">Active Ponds</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Farm Map -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">Farm Locations</h5>
        </div>
        <div class="card-body p-0">
            <div id="farmMap" class="map-container">
                <!-- Map will be loaded here -->
                <div class="d-flex justify-content-center align-items-center h-100 bg-light">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="visually-hidden">Loading map...</span>
                        </div>
                        <p class="mb-0">Loading map...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Recent Farms -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Farms</h5>
                    <a href="{% url 'ponds:farm_list' %}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if recent_farms %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>Size</th>
                                        <th>Ponds</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for farm in recent_farms %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'ponds:farm_detail' farm.pk %}" class="text-decoration-none fw-medium">
                                                    {{ farm.name }}
                                                </a>
                                            </td>
                                            <td>{{ farm.location|default:"--" }}</td>
                                            <td>{{ farm.size }} hectares</td>
                                            <td>
                                                <span class="badge bg-primary">{{ farm.total_ponds }} ponds</span>
                                                {% if farm.active_ponds %}
                                                    <span class="badge bg-success">{{ farm.active_ponds }} active</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'ponds:farm_detail' farm.pk %}" class="btn btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'ponds:farm_update' farm.pk %}" class="btn btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No farms found.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Farm Distribution Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Farm Size Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="farmSizeChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Pond Status Distribution -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Pond Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="pondStatusChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Water Quality Overview -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Water Quality Overview</h5>
                </div>
                <div class="card-body">
                    <div id="waterQualityChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'ponds:farm_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Farm
                        </a>
                        <a href="{% url 'ponds:pond_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Pond
                        </a>
                        <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i> View All Farms
                        </a>
                        <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i> View All Ponds
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize farm map
        const farmMap = L.map('farmMap').setView([0, 0], 2);
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(farmMap);
        
        // Add farm markers
        {% for farm in farms %}
            {% if farm.latitude and farm.longitude %}
                const farmMarker{{ farm.id }} = L.marker([{{ farm.latitude }}, {{ farm.longitude }}])
                    .addTo(farmMap)
                    .bindPopup('<strong>{{ farm.name }}</strong><br>{{ farm.location }}<br>{{ farm.total_ponds }} ponds<br><a href="{% url "ponds:farm_detail" farm.pk %}">View Details</a>');
                
                // If this is the first farm with coordinates, center the map on it
                {% if forloop.first %}
                    farmMap.setView([{{ farm.latitude }}, {{ farm.longitude }}], 10);
                {% endif %}
            {% endif %}
        {% endfor %}
        
        // Initialize farm size distribution chart
        const farmSizeCtx = document.getElementById('farmSizeChart').getContext('2d');
        const farmSizeChart = new Chart(farmSizeCtx, {
            type: 'bar',
            data: {
                labels: [{% for farm in farms %}'{{ farm.name }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'Farm Size (hectares)',
                    data: [{% for farm in farms %}{{ farm.size }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Size (hectares)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Farm'
                        }
                    }
                }
            }
        });
        
        // Initialize pond status distribution chart
        const pondStatusCtx = document.getElementById('pondStatusChart').getContext('2d');
        const pondStatusChart = new Chart(pondStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Maintenance', 'Empty', 'Harvested'],
                datasets: [{
                    data: [
                        {{ active_ponds }}, 
                        {{ maintenance_ponds|default:0 }}, 
                        {{ empty_ponds|default:0 }}, 
                        {{ harvested_ponds|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(108, 117, 125, 1)',
                        'rgba(111, 66, 193, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Initialize water quality chart
        const waterQualityCtx = document.getElementById('waterQualityChart').getContext('2d');
        const waterQualityChart = new Chart(waterQualityCtx, {
            type: 'pie',
            data: {
                labels: ['Good', 'Average', 'Poor'],
                datasets: [{
                    data: [
                        {{ good_water_quality|default:0 }}, 
                        {{ average_water_quality|default:0 }}, 
                        {{ poor_water_quality|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
    
    .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: white;
    }
    
    .stats-value {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .stats-label {
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    .farm-card {
        border-radius: 0.5rem;
        overflow: hidden;
        transition: transform 0.2s;
        height: 100%;
    }
    
    .farm-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    
    .farm-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
    
    .farm-body {
        padding: 1rem;
    }
    
    .farm-footer {
        padding: 1rem;
        background-color: #f8f9fa;
        border-top: 1px solid rgba(0,0,0,0.1);
    }
    
    .map-container {
        height: 400px;
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 1.5rem;
    }
    
    .chart-container {
        height: 300px;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Farm Dashboard</h1>
        <div class="btn-group">
            <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i> All Farms
            </a>
            <a href="{% url 'ponds:farm_create' %}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-2"></i> Add Farm
            </a>
        </div>
    </div>
    
    <!-- Stats Overview -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_farms }}</div>
                        <div class="stats-label">Total Farms</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-water"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_ponds }}</div>
                        <div class="stats-label">Total Ponds</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-ruler-combined"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ total_area|floatformat:2 }}</div>
                        <div class="stats-label">Total Area (hectares)</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card shadow-sm">
                <div class="card-body d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-fish"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ active_ponds }}</div>
                        <div class="stats-label">Active Ponds</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Farm Map -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">Farm Locations</h5>
        </div>
        <div class="card-body p-0">
            <div id="farmMap" class="map-container">
                <!-- Map will be loaded here -->
                <div class="d-flex justify-content-center align-items-center h-100 bg-light">
                    <div class="text-center">
                        <div class="spinner-border text-primary mb-2" role="status">
                            <span class="visually-hidden">Loading map...</span>
                        </div>
                        <p class="mb-0">Loading map...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Recent Farms -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Recent Farms</h5>
                    <a href="{% url 'ponds:farm_list' %}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body p-0">
                    {% if recent_farms %}
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Name</th>
                                        <th>Location</th>
                                        <th>Size</th>
                                        <th>Ponds</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for farm in recent_farms %}
                                        <tr>
                                            <td>
                                                <a href="{% url 'ponds:farm_detail' farm.pk %}" class="text-decoration-none fw-medium">
                                                    {{ farm.name }}
                                                </a>
                                            </td>
                                            <td>{{ farm.location|default:"--" }}</td>
                                            <td>{{ farm.size }} hectares</td>
                                            <td>
                                                <span class="badge bg-primary">{{ farm.total_ponds }} ponds</span>
                                                {% if farm.active_ponds %}
                                                    <span class="badge bg-success">{{ farm.active_ponds }} active</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="{% url 'ponds:farm_detail' farm.pk %}" class="btn btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{% url 'ponds:farm_update' farm.pk %}" class="btn btn-outline-secondary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <p class="text-muted mb-0">No farms found.</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Farm Distribution Chart -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Farm Size Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="farmSizeChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Pond Status Distribution -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Pond Status Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="pondStatusChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Water Quality Overview -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Water Quality Overview</h5>
                </div>
                <div class="card-body">
                    <div id="waterQualityChart" class="chart-container">
                        <!-- Chart will be loaded here -->
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center">
                                <div class="spinner-border text-primary mb-2" role="status">
                                    <span class="visually-hidden">Loading chart...</span>
                                </div>
                                <p class="mb-0">Loading chart...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{% url 'ponds:farm_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Farm
                        </a>
                        <a href="{% url 'ponds:pond_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus-circle me-2"></i> Add New Pond
                        </a>
                        <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i> View All Farms
                        </a>
                        <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-list me-2"></i> View All Ponds
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize farm map
        const farmMap = L.map('farmMap').setView([0, 0], 2);
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(farmMap);
        
        // Add farm markers
        {% for farm in farms %}
            {% if farm.latitude and farm.longitude %}
                const farmMarker{{ farm.id }} = L.marker([{{ farm.latitude }}, {{ farm.longitude }}])
                    .addTo(farmMap)
                    .bindPopup('<strong>{{ farm.name }}</strong><br>{{ farm.location }}<br>{{ farm.total_ponds }} ponds<br><a href="{% url "ponds:farm_detail" farm.pk %}">View Details</a>');
                
                // If this is the first farm with coordinates, center the map on it
                {% if forloop.first %}
                    farmMap.setView([{{ farm.latitude }}, {{ farm.longitude }}], 10);
                {% endif %}
            {% endif %}
        {% endfor %}
        
        // Initialize farm size distribution chart
        const farmSizeCtx = document.getElementById('farmSizeChart').getContext('2d');
        const farmSizeChart = new Chart(farmSizeCtx, {
            type: 'bar',
            data: {
                labels: [{% for farm in farms %}'{{ farm.name }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
                datasets: [{
                    label: 'Farm Size (hectares)',
                    data: [{% for farm in farms %}{{ farm.size }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Size (hectares)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Farm'
                        }
                    }
                }
            }
        });
        
        // Initialize pond status distribution chart
        const pondStatusCtx = document.getElementById('pondStatusChart').getContext('2d');
        const pondStatusChart = new Chart(pondStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Maintenance', 'Empty', 'Harvested'],
                datasets: [{
                    data: [
                        {{ active_ponds }}, 
                        {{ maintenance_ponds|default:0 }}, 
                        {{ empty_ponds|default:0 }}, 
                        {{ harvested_ponds|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(111, 66, 193, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(108, 117, 125, 1)',
                        'rgba(111, 66, 193, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Initialize water quality chart
        const waterQualityCtx = document.getElementById('waterQualityChart').getContext('2d');
        const waterQualityChart = new Chart(waterQualityCtx, {
            type: 'pie',
            data: {
                labels: ['Good', 'Average', 'Poor'],
                datasets: [{
                    data: [
                        {{ good_water_quality|default:0 }}, 
                        {{ average_water_quality|default:0 }}, 
                        {{ poor_water_quality|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}