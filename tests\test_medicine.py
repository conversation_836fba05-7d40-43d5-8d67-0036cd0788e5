import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from medicine.models import Medicine, TreatmentPlan, MedicineApplication
from ponds.models import Pond
from users.models import User
# Note: Farm model is not implemented yet - creating simple farm placeholder


class MedicineModelTests(TestCase):
    """Test suite for the medicine models."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Note: Farm model not properly implemented, creating pond without farm reference
        self.pond = Pond.objects.create(
            name='Test Pond',
            area=1000.0,
            depth=2.5,
            shape='rectangular',
            coordinates=[
                {'latitude': 10.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 20.0},
            ],
            status='active',
            water_source='river',
            bottom_type='mud',
            created_by=self.user
        )

    def test_medicine_creation(self):
        """Test creating a medicine."""
        medicine = Medicine.objects.create(
            name='Oxytetracycline',
            category='antibiotic',
            active_ingredient='Oxytetracycline hydrochloride',
            concentration=250.0,
            unit='mg/g',
            manufacturer='Aqua Pharma Inc.',
            description='Broad-spectrum antibiotic for bacterial infections',
            dosage_instructions='1-2 g per 100 kg of feed for 7-10 days',
            withdrawal_period=21,  # days
            storage_instructions='Store in a cool, dry place away from direct sunlight',
            created_by=self.user
        )
        
        self.assertEqual(medicine.name, 'Oxytetracycline')
        self.assertEqual(medicine.category, 'antibiotic')
        self.assertEqual(medicine.active_ingredient, 'Oxytetracycline hydrochloride')
        self.assertEqual(medicine.concentration, 250.0)
        self.assertEqual(medicine.unit, 'mg/g')
        self.assertEqual(medicine.withdrawal_period, 21)
        self.assertEqual(medicine.created_by, self.user)
        
        self.assertEqual(str(medicine), 'Oxytetracycline (antibiotic)')

    def test_treatment_creation(self):
        """Test creating a medicine treatment."""
        medicine = Medicine.objects.create(
            name='Oxytetracycline',
            category='antibiotic',
            active_ingredient='Oxytetracycline hydrochloride',
            concentration=250.0,
            unit='mg/g',
            manufacturer='Aqua Pharma Inc.',
            created_by=self.user
        )
        
        treatment = TreatmentPlan.objects.create(
            pond=self.pond,
            name='Vibriosis Treatment',
            description='Treatment for bacterial infection outbreak',
            start_date=datetime.now().date(),
            end_date=(datetime.now() + timedelta(days=7)).date(),
            status='in_progress',
            created_by=self.user
        )
        
        self.assertEqual(treatment.pond, self.pond)
        self.assertEqual(treatment.disease, 'Vibriosis')
        self.assertEqual(treatment.status, 'in_progress')
        self.assertEqual(treatment.created_by, self.user)
        
        self.assertEqual(str(treatment), f'Treatment for {self.pond.name}: Vibriosis')

    def test_application_creation(self):
        """Test creating a medicine application."""
        medicine = Medicine.objects.create(
            name='Oxytetracycline',
            category='antibiotic',
            active_ingredient='Oxytetracycline hydrochloride',
            concentration=250.0,
            unit='mg/g',
            manufacturer='Aqua Pharma Inc.',
            created_by=self.user
        )
        
        treatment = TreatmentPlan.objects.create(
            pond=self.pond,
            name='Vibriosis Treatment',
            description='Treatment for bacterial infection caused by Vibrio species with symptoms: Lethargy, reduced feeding, red spots on shell',
            start_date=datetime.now().date(),
            end_date=(datetime.now() + timedelta(days=7)).date(),
            status='in_progress',
            created_by=self.user
        )
        
        application = MedicineApplication.objects.create(
            treatment=treatment,
            medicine=medicine,
            application_date=datetime.now().date(),
            dosage=2.0,
            dosage_unit='g/kg feed',
            application_method='mixed with feed',
            applied_by=self.user,
            notes='First day of treatment'
        )
        
        self.assertEqual(application.treatment, treatment)
        self.assertEqual(application.medicine, medicine)
        self.assertEqual(application.dosage, 2.0)
        self.assertEqual(application.dosage_unit, 'g/kg feed')
        self.assertEqual(application.application_method, 'mixed with feed')
        self.assertEqual(application.applied_by, self.user)
        
        self.assertEqual(str(application), f'Oxytetracycline application on {application.application_date}')


class MedicineAPITests(TestCase):
    """Test suite for the medicine API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Note: Farm model not properly implemented, creating pond without farm reference
        self.pond = Pond.objects.create(
            name='Test Pond',
            area=1000.0,
            depth=2.5,
            shape='rectangular',
            coordinates=[
                {'latitude': 10.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 20.0},
            ],
            status='active',
            water_source='river',
            bottom_type='mud',
            created_by=self.user
        )
        
        # Create medicines
        self.medicine1 = Medicine.objects.create(
            name='Oxytetracycline',
            category='antibiotic',
            active_ingredient='Oxytetracycline hydrochloride',
            concentration=250.0,
            unit='mg/g',
            manufacturer='Aqua Pharma Inc.',
            description='Broad-spectrum antibiotic for bacterial infections',
            dosage_instructions='1-2 g per 100 kg of feed for 7-10 days',
            withdrawal_period=21,
            created_by=self.user
        )
        
        self.medicine2 = Medicine.objects.create(
            name='Formalin',
            category='disinfectant',
            active_ingredient='Formaldehyde',
            concentration=37.0,
            unit='%',
            manufacturer='Aqua Chemicals Inc.',
            description='Used to treat external parasites and fungal infections',
            dosage_instructions='15-25 ppm for 1 hour bath treatment',
            withdrawal_period=0,
            created_by=self.user
        )
        
        # Create treatment
        self.treatment = TreatmentPlan.objects.create(
            pond=self.pond,
            name='Vibriosis Treatment',
            description='Treatment for bacterial infection caused by Vibrio species with symptoms: Lethargy, reduced feeding, red spots on shell. Treatment for bacterial infection outbreak',
            start_date=datetime.now().date(),
            end_date=(datetime.now() + timedelta(days=7)).date(),
            status='in_progress',
            created_by=self.user
        )
        
        # Create applications
        self.application1 = MedicineApplication.objects.create(
            medicine=self.medicine1,
            pond=self.pond,
            application_date=datetime.now(),
            dosage=2.0,
            dosage_unit='g/kg feed',
            application_method='feed',
            quantity_used=50.0,
            reason='First day of treatment',
            applied_by=self.user,
            notes='First day of treatment'
        )
        
        self.application2 = MedicineApplication.objects.create(
            medicine=self.medicine1,
            pond=self.pond,
            application_date=(datetime.now() - timedelta(days=1)),
            dosage=2.0,
            dosage_unit='g/kg feed',
            application_method='feed',
            quantity_used=50.0,
            reason='Second day of treatment',
            applied_by=self.user,
            notes='Second day of treatment'
        )
        
        # Authenticate the client
        self.client.force_authenticate(user=self.user)

    def test_get_medicines(self):
        """Test retrieving medicines."""
        url = reverse('medicine-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['name'], 'Oxytetracycline')
        self.assertEqual(response.data[1]['name'], 'Formalin')

    def test_create_medicine(self):
        """Test creating a new medicine."""
        url = reverse('medicine-list')
        data = {
            'name': 'Copper Sulfate',
            'category': 'algaecide',
            'active_ingredient': 'Copper Sulfate Pentahydrate',
            'concentration': 25.0,
            'unit': '%',
            'manufacturer': 'Aqua Chemicals Inc.',
            'description': 'Used to control algae and certain parasites',
            'dosage_instructions': '0.5-1.0 ppm for pond treatment',
            'withdrawal_period': 0
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Copper Sulfate')
        self.assertEqual(response.data['category'], 'algaecide')
        self.assertEqual(Medicine.objects.count(), 3)

    def test_get_treatments(self):
        """Test retrieving treatments."""
        url = reverse('treatmentplan-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Vibriosis Treatment')
        self.assertEqual(response.data[0]['status'], 'in_progress')

    def test_create_treatment(self):
        """Test creating a new treatment."""
        url = reverse('treatmentplan-list')
        data = {
            'pond': self.pond.id,
            'name': 'White Spot Disease Treatment',
            'description': 'Treatment for parasitic infection - symptoms: White spots on shell, lethargy',
            'start_date': datetime.now().date().isoformat(),
            'end_date': (datetime.now() + timedelta(days=5)).date().isoformat(),
            'status': 'planned'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'White Spot Disease Treatment')
        self.assertEqual(response.data['status'], 'planned')
        self.assertEqual(TreatmentPlan.objects.count(), 2)

    def test_update_treatment(self):
        """Test updating a treatment."""
        url = reverse('treatmentplan-detail', args=[self.treatment.id])
        data = {
            'status': 'completed',
            'description': 'Treatment completed successfully'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'completed')
        self.assertEqual(response.data['notes'], 'Treatment completed successfully')
        
        # Verify the update in the database
        self.treatment.refresh_from_db()
        self.assertEqual(self.treatment.status, 'completed')
        self.assertEqual(self.treatment.notes, 'Treatment completed successfully')

    def test_get_applications(self):
        """Test retrieving applications."""
        url = reverse('medicineapplication-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['dosage'], 2.0)
        self.assertEqual(response.data[0]['application_method'], 'mixed with feed')

    def test_create_application(self):
        """Test creating a new application."""
        url = reverse('medicineapplication-list')
        data = {
            'treatment': self.treatment.id,
            'medicine': self.medicine1.id,
            'application_date': datetime.now().date().isoformat(),
            'dosage': 1.5,
            'dosage_unit': 'g/kg feed',
            'application_method': 'mixed with feed',
            'notes': 'Third day of treatment'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['dosage'], 1.5)
        self.assertEqual(response.data['notes'], 'Third day of treatment')
        self.assertEqual(MedicineApplication.objects.count(), 3)

    def test_filter_treatments_by_pond(self):
        """Test filtering treatments by pond."""
        # Create another pond and treatment
        pond2 = Pond.objects.create(
            name='Second Pond',
            area=800.0,
            depth=2.0,
            shape='rectangular',
            coordinates=[
                {'latitude': 12.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 22.0},
            ],
            status='active',
            water_source='well',
            bottom_type='sand',
            created_by=self.user
        )
        
        treatment2 = TreatmentPlan.objects.create(
            pond=pond2,
            name='Gill Disease Treatment',
            description='Bacterial gill disease treatment - symptoms: Damaged gills, gasping at surface',
            start_date=datetime.now().date(),
            end_date=(datetime.now() + timedelta(days=5)).date(),
            status='in_progress',
            created_by=self.user
        )
        
        # Filter by the first pond
        url = f"{reverse('treatmentplan-list')}?pond={self.pond.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Vibriosis Treatment')
        
        # Filter by the second pond
        url = f"{reverse('treatmentplan-list')}?pond={pond2.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['disease'], 'Gill Disease')

    def test_filter_treatments_by_status(self):
        """Test filtering treatments by status."""
        # Create another treatment with different status
        treatment2 = TreatmentPlan.objects.create(
            pond=self.pond,
            name='White Spot Disease Treatment',
            description='Parasitic infection treatment - symptoms: White spots on shell, lethargy',
            start_date=(datetime.now() + timedelta(days=2)).date(),
            end_date=(datetime.now() + timedelta(days=7)).date(),
            status='planned',
            created_by=self.user
        )
        
        # Filter by in_progress status
        url = f"{reverse('treatmentplan-list')}?status=in_progress"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['name'], 'Vibriosis Treatment')
        
        # Filter by planned status
        url = f"{reverse('treatmentplan-list')}?status=planned"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['disease'], 'White Spot Disease')

    def test_get_treatment_applications(self):
        """Test retrieving applications for a specific treatment."""
        url = f"{reverse('treatment-applications', args=[self.treatment.id])}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        
        # Verify that applications are ordered by date
        application_dates = [app['application_date'] for app in response.data]
        self.assertEqual(application_dates, sorted(application_dates, reverse=True))

    def test_get_pond_treatment_history(self):
        """Test retrieving treatment history for a pond."""
        url = f"{reverse('pond-treatment-history', args=[self.pond.id])}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['disease'], 'Vibriosis')
        self.assertEqual(len(response.data[0]['applications']), 2)
