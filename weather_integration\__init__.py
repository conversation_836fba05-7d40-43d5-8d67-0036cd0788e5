# Weather Integration Module for Shrimp Farm Guardian
# Advanced weather monitoring and prediction for IoT farm management

__version__ = '1.0.0'
__author__ = 'Shrimp Farm Guardian Team'

# Import main weather services
from .services.weather_service import WeatherService
from .services.weather_prediction_service import WeatherPredictionService
from .services.weather_automation_service import WeatherAutomationService
from .services.weather_alert_service import WeatherAlertService

__all__ = [
    'WeatherService',
    'WeatherPredictionService', 
    'WeatherAutomationService',
    'WeatherAlertService',
]
