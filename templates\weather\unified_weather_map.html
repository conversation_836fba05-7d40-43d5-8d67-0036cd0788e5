{% extends 'base.html' %}
{% load static %}

{% block title %}Unified Weather Map - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .weather-map-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .weather-map-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: map-sweep 6s infinite;
    }

    @keyframes map-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .map-container {
        height: 70vh;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        position: relative;
    }

    #weather-map {
        height: 100%;
        width: 100%;
    }

    .map-controls {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        min-width: 250px;
    }

    .control-group {
        margin-bottom: 20px;
    }

    .control-group:last-child {
        margin-bottom: 0;
    }

    .control-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #2d3436;
        display: block;
    }

    .control-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .control-btn {
        padding: 8px 16px;
        border: 2px solid #667eea;
        background: white;
        color: #667eea;
        border-radius: 20px;
        font-size: 0.9em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .control-btn:hover,
    .control-btn.active {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .weather-legend {
        position: absolute;
        bottom: 20px;
        left: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        max-width: 300px;
    }

    .legend-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #2d3436;
        font-size: 1.1em;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid rgba(0,0,0,0.2);
    }

    .legend-text {
        font-size: 0.9em;
        color: #636e72;
    }

    .weather-info-panel {
        position: absolute;
        top: 50%;
        left: 20px;
        transform: translateY(-50%);
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        backdrop-filter: blur(10px);
        min-width: 300px;
        max-width: 350px;
        display: none;
    }

    .weather-info-panel.show {
        display: block;
        animation: slideInLeft 0.5s ease;
    }

    @keyframes slideInLeft {
        from { transform: translateY(-50%) translateX(-100%); opacity: 0; }
        to { transform: translateY(-50%) translateX(0); opacity: 1; }
    }

    .weather-info-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e9ecef;
    }

    .weather-info-title {
        font-size: 1.3em;
        font-weight: bold;
        color: #2d3436;
        margin: 0;
    }

    .weather-info-close {
        background: none;
        border: none;
        font-size: 1.5em;
        color: #636e72;
        cursor: pointer;
        padding: 5px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .weather-info-close:hover {
        background: #f8f9fa;
        color: #2d3436;
    }

    .weather-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .weather-metric:last-child {
        border-bottom: none;
    }

    .metric-label {
        font-weight: 500;
        color: #636e72;
    }

    .metric-value {
        font-weight: bold;
        color: #2d3436;
        font-size: 1.1em;
    }

    .weather-icon-large {
        font-size: 3em;
        text-align: center;
        margin: 20px 0;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .map-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }

    .stat-card {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    }

    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .stat-label {
        font-size: 0.9em;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2000;
        border-radius: 20px;
    }

    .loading-content {
        text-align: center;
        color: #667eea;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Custom marker styles */
    .pond-marker {
        background: #667eea;
        border: 3px solid white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .pond-marker:hover {
        transform: scale(1.3);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }

    .farm-marker {
        background: #764ba2;
        border: 3px solid white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(118, 75, 162, 0.4);
    }

    .farm-marker:hover {
        transform: scale(1.3);
        box-shadow: 0 6px 20px rgba(118, 75, 162, 0.6);
    }

    .weather-popup {
        max-width: 300px;
        font-family: inherit;
    }

    .popup-header {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 15px;
        margin: -10px -10px 15px -10px;
        border-radius: 10px 10px 0 0;
        text-align: center;
    }

    .popup-title {
        font-size: 1.2em;
        font-weight: bold;
        margin: 0;
    }

    .popup-weather {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 15px;
    }

    .popup-metric {
        text-align: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .popup-metric-value {
        font-size: 1.3em;
        font-weight: bold;
        color: #2d3436;
    }

    .popup-metric-label {
        font-size: 0.8em;
        color: #636e72;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Weather Map Header -->
    <div class="weather-map-header">
        <div class="position-relative">
            <h1><i class="fas fa-map-marked-alt me-3"></i>Unified Weather Map</h1>
            <p class="lead mb-0">Interactive weather monitoring for all farms and ponds</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🗺️ INTERACTIVE MAP</span>
                <span class="badge bg-light text-dark me-2">🌤️ REAL-TIME WEATHER</span>
                <span class="badge bg-light text-dark me-2">📍 CLICKABLE LOCATIONS</span>
                <span class="badge bg-success text-white ms-3">🔗 FULLY INTEGRATED</span>
            </div>
        </div>
    </div>

    <!-- Map Statistics -->
    <div class="map-stats">
        <div class="stat-card">
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Total Farms</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ weather_stations }}</div>
            <div class="stat-label">Weather Stations</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ active_alerts }}</div>
            <div class="stat-label">Active Alerts</div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div id="weather-map"></div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <h4>Loading Weather Map...</h4>
                <p>Fetching farm and pond locations with weather data</p>
            </div>
        </div>

        <!-- Map Controls -->
        <div class="map-controls">
            <div class="control-group">
                <span class="control-label">Map View</span>
                <div class="control-buttons">
                    <button class="control-btn active" data-view="satellite">Satellite</button>
                    <button class="control-btn" data-view="terrain">Terrain</button>
                    <button class="control-btn" data-view="street">Street</button>
                </div>
            </div>

            <div class="control-group">
                <span class="control-label">Weather Layer</span>
                <div class="control-buttons">
                    <button class="control-btn" data-weather="temperature">Temperature</button>
                    <button class="control-btn" data-weather="precipitation">Rain</button>
                    <button class="control-btn" data-weather="wind">Wind</button>
                    <button class="control-btn" data-weather="none">None</button>
                </div>
            </div>

            <div class="control-group">
                <span class="control-label">Show/Hide</span>
                <div class="control-buttons">
                    <button class="control-btn active" data-toggle="farms">Farms</button>
                    <button class="control-btn active" data-toggle="ponds">Ponds</button>
                    <button class="control-btn" data-toggle="weather">Weather</button>
                </div>
            </div>

            <div class="control-group">
                <span class="control-label">Quick Actions</span>
                <div class="control-buttons">
                    <button class="control-btn" onclick="centerOnAllLocations()">
                        <i class="fas fa-expand-arrows-alt me-1"></i>Fit All
                    </button>
                    <button class="control-btn" onclick="refreshWeatherData()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Weather Legend -->
        <div class="weather-legend" id="weather-legend" style="display: none;">
            <div class="legend-title">Weather Legend</div>
            <div id="legend-content">
                <!-- Legend items will be populated by JavaScript -->
            </div>
        </div>

        <!-- Weather Info Panel -->
        <div class="weather-info-panel" id="weather-info-panel">
            <div class="weather-info-header">
                <h3 class="weather-info-title" id="info-title">Location Weather</h3>
                <button class="weather-info-close" onclick="closeWeatherInfo()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="weather-info-content">
                <!-- Weather information will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Quick Navigation -->
    <div class="text-center mt-4">
        <h3>Quick Navigation</h3>
        <div class="mt-3">
            <a href="{% url 'weather:pond_weather_dashboard' %}" class="control-btn me-2">
                <i class="fas fa-fish me-1"></i>Pond Weather Dashboard
            </a>
            <a href="{% url 'weather:weather_dashboard' %}" class="control-btn me-2">
                <i class="fas fa-tachometer-alt me-1"></i>Weather Dashboard
            </a>
            <a href="{% url 'ponds:farm_list' %}" class="control-btn me-2">
                <i class="fas fa-building me-1"></i>Farm Management
            </a>
            <a href="{% url 'ponds:pond_list' %}" class="control-btn">
                <i class="fas fa-water me-1"></i>Pond Management
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global error handlers for Google Maps
window.gm_authFailure = function() {
    console.error('🚨 Google Maps authentication failed');
    const loadingElement = document.getElementById('loading-overlay');
    if (loadingElement) {
        loadingElement.innerHTML = `
            <div class="alert alert-danger m-3">
                <h4><i class="fas fa-exclamation-triangle"></i> Google Maps Authentication Failed</h4>
                <p>There was an issue with the Google Maps API key. Please check:</p>
                <ul>
                    <li>API key is valid and active</li>
                    <li>Maps JavaScript API is enabled</li>
                    <li>Billing is set up for the Google Cloud project</li>
                </ul>
            </div>
        `;
    }
};

// Global variables
let weatherMap;
let farmMarkers = [];
let pondMarkers = [];
let weatherLayer = null;
let currentWeatherType = 'none';
let infoWindow;

// Map data from Django context with error handling
let mapData = {
    farms: [],
    ponds: [],
    center: { lat: 14.5995, lng: 120.9842 }, // Default to Philippines
    bounds: []
};

try {
    // Parse JSON data safely
    const farmsDataStr = '{{ farms_data|escapejs }}';
    const pondsDataStr = '{{ ponds_data|escapejs }}';
    const centerDataStr = '{{ map_center|escapejs }}';
    const boundsDataStr = '{{ map_bounds|escapejs }}';

    if (farmsDataStr && farmsDataStr !== 'None' && farmsDataStr !== '') {
        mapData.farms = JSON.parse(farmsDataStr);
    }

    if (pondsDataStr && pondsDataStr !== 'None' && pondsDataStr !== '') {
        mapData.ponds = JSON.parse(pondsDataStr);
    }

    if (centerDataStr && centerDataStr !== 'None' && centerDataStr !== '') {
        mapData.center = JSON.parse(centerDataStr);
    }

    if (boundsDataStr && boundsDataStr !== 'None' && boundsDataStr !== '') {
        mapData.bounds = JSON.parse(boundsDataStr);
    }

    console.log('Map data loaded successfully:', mapData);
} catch (error) {
    console.error('Error parsing map data:', error);
    console.log('Using default map data');
}

// Initialize Google Maps with comprehensive error handling
function initUnifiedWeatherGoogleMaps() {
    console.log('🌤️ Starting Weather Map initialization...');

    try {
        // Check if Google Maps API is loaded
        if (typeof google === 'undefined') {
            throw new Error('Google Maps API not loaded - google object is undefined');
        }

        if (!google.maps) {
            throw new Error('Google Maps API not loaded - google.maps is undefined');
        }

        console.log('✅ Google Maps API loaded successfully for weather map');

        // Create Google Map
        weatherMap = new google.maps.Map(document.getElementById('weather-map'), {
        center: { lat: mapData.center.lat, lng: mapData.center.lng },
        zoom: 10,
        mapTypeId: google.maps.MapTypeId.SATELLITE,
        mapTypeControl: false,
        fullscreenControl: true,
        streetViewControl: true,
        zoomControl: true,
        gestureHandling: 'cooperative'
    });

    // Create info window
    infoWindow = new google.maps.InfoWindow();

    // Add markers
    addFarmMarkers();
    addPondMarkers();

    // Fit map to bounds if available
    if (mapData.bounds && mapData.bounds.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        mapData.bounds.forEach(bound => {
            bounds.extend(new google.maps.LatLng(bound[0], bound[1]));
        });
        weatherMap.fitBounds(bounds);
    }

    // Setup control handlers
    setupControlHandlers();

    // Hide loading overlay
    document.getElementById('loading-overlay').style.display = 'none';

    } catch (error) {
        console.error('Error initializing weather map:', error);
        const loadingElement = document.getElementById('loading-overlay');
        if (loadingElement) {
            loadingElement.innerHTML = `
                <div class="alert alert-danger m-3">
                    <h4><i class="fas fa-exclamation-triangle"></i> Weather Map Error</h4>
                    <p>Failed to initialize the weather map: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> Refresh Page
                    </button>
                </div>
            `;
        }
    }
}

    // Add farm markers
    function addFarmMarkers() {
        mapData.farms.forEach(farm => {
            if (farm.latitude && farm.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: farm.latitude, lng: farm.longitude },
                    map: weatherMap,
                    title: farm.name,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 12,
                        fillColor: '#764ba2',
                        fillOpacity: 0.8,
                        strokeColor: 'white',
                        strokeWeight: 3
                    },
                    animation: google.maps.Animation.DROP
                });

                // Add click handler
                marker.addListener('click', () => {
                    const content = createFarmInfoWindow(farm);
                    infoWindow.setContent(content);
                    infoWindow.open(weatherMap, marker);
                    showWeatherInfo(farm, 'farm');
                });

                farmMarkers.push(marker);
            }
        });
    }

    // Add pond markers
    function addPondMarkers() {
        mapData.ponds.forEach(pond => {
            if (pond.latitude && pond.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: pond.latitude, lng: pond.longitude },
                    map: weatherMap,
                    title: pond.name,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#667eea',
                        fillOpacity: 0.8,
                        strokeColor: 'white',
                        strokeWeight: 2
                    },
                    animation: google.maps.Animation.DROP
                });

                // Add click handler
                marker.addListener('click', () => {
                    const content = createPondInfoWindow(pond);
                    infoWindow.setContent(content);
                    infoWindow.open(weatherMap, marker);
                    showWeatherInfo(pond, 'pond');

                    // Center map on clicked pond
                    weatherMap.panTo(marker.getPosition());
                    weatherMap.setZoom(Math.max(weatherMap.getZoom(), 15));
                });

                pondMarkers.push(marker);
            }
        });
    }

    // Create farm info window content
    function createFarmInfoWindow(farm) {
        return `
            <div style="max-width: 350px; font-family: inherit;">
                <div style="background: linear-gradient(45deg, #764ba2, #667eea); color: white; padding: 15px; margin: -10px -10px 15px -10px; border-radius: 10px; text-align: center;">
                    <h4 style="margin: 0; font-size: 1.2em;">🏢 ${farm.name}</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Farm Weather Station</p>
                </div>
                <div style="padding: 10px 0;">
                    <p style="margin: 8px 0;"><strong>Location:</strong> ${farm.location || 'Not specified'}</p>
                    <p style="margin: 8px 0;"><strong>Size:</strong> ${farm.size} hectares</p>
                    <p style="margin: 8px 0;"><strong>Ponds:</strong> ${farm.pond_count} ponds</p>
                    ${farm.weather ? `
                        <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">🌤️ Current Weather</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${farm.weather.temperature}°C</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Temperature</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${farm.weather.humidity}%</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Humidity</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${farm.weather.wind_speed} km/h</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Wind</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${farm.weather.condition}</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Condition</div>
                                </div>
                            </div>
                        </div>
                    ` : `
                        <div style="background: linear-gradient(135deg, #636e72, #2d3436); color: white; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: center;">
                            <div style="font-weight: bold;">⚠️ No Weather Data</div>
                            <div style="font-size: 0.9em; opacity: 0.9;">Weather information not available</div>
                        </div>
                    `}
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="/ponds/farms/${farm.id}/" style="background: #764ba2; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 0.9em;">View Farm Details</a>
                    </div>
                </div>
            </div>
        `;
    }

    // Create pond info window content
    function createPondInfoWindow(pond) {
        return `
            <div style="max-width: 350px; font-family: inherit;">
                <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px; margin: -10px -10px 15px -10px; border-radius: 10px; text-align: center;">
                    <h4 style="margin: 0; font-size: 1.2em;">🐟 ${pond.name}</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">Pond Weather Station</p>
                </div>
                <div style="padding: 10px 0;">
                    <p style="margin: 8px 0;"><strong>Farm:</strong> ${pond.farm_name || 'Not assigned'}</p>
                    <p style="margin: 8px 0;"><strong>Size:</strong> ${pond.size} m²</p>
                    <p style="margin: 8px 0;"><strong>Status:</strong> <span style="background: ${pond.status === 'active' ? '#00b894' : '#fdcb6e'}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">${pond.status}</span></p>
                    <p style="margin: 8px 0;"><strong>Species:</strong> ${pond.species || 'Not specified'}</p>
                    ${pond.weather ? `
                        <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                            <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">🌤️ Current Weather</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${pond.weather.temperature}°C</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Temperature</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${pond.weather.humidity}%</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Humidity</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${pond.weather.wind_speed} km/h</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Wind</div>
                                </div>
                                <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                    <div style="font-size: 1.1em; font-weight: bold;">${pond.weather.condition}</div>
                                    <div style="font-size: 0.8em; opacity: 0.9;">Condition</div>
                                </div>
                            </div>
                        </div>
                    ` : `
                        <div style="background: linear-gradient(135deg, #636e72, #2d3436); color: white; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: center;">
                            <div style="font-weight: bold;">⚠️ No Weather Data</div>
                            <div style="font-size: 0.9em; opacity: 0.9;">Weather information not available</div>
                        </div>
                    `}
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="/ponds/${pond.id}/" style="background: #667eea; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 0.9em;">View Pond Details</a>
                    </div>
                </div>
            </div>
        `;
    }

    // Show weather info panel
    function showWeatherInfo(location, type) {
        const panel = document.getElementById('weather-info-panel');
        const title = document.getElementById('info-title');
        const content = document.getElementById('weather-info-content');

        // Set title
        title.textContent = `${type === 'farm' ? '🏢' : '🐟'} ${location.name}`;

        // Create detailed weather content
        if (location.weather) {
            content.innerHTML = `
                <div class="weather-icon-large">
                    ${getWeatherIcon(location.weather.condition)}
                </div>
                <div class="text-center mb-3">
                    <h4>${location.weather.condition}</h4>
                    <p class="text-muted">Last updated: ${location.weather.last_updated || 'Recently'}</p>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Temperature</span>
                    <span class="metric-value">${location.weather.temperature}°C</span>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Humidity</span>
                    <span class="metric-value">${location.weather.humidity}%</span>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Wind Speed</span>
                    <span class="metric-value">${location.weather.wind_speed} km/h</span>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Wind Direction</span>
                    <span class="metric-value">${location.weather.wind_direction}</span>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Pressure</span>
                    <span class="metric-value">${location.weather.pressure} hPa</span>
                </div>
                <div class="weather-metric">
                    <span class="metric-label">Precipitation</span>
                    <span class="metric-value">${location.weather.precipitation} mm</span>
                </div>
                <div class="mt-3">
                    <a href="${type === 'farm' ? '/ponds/farms/' + location.id + '/' : '/ponds/' + location.id + '/'}"
                       class="btn btn-primary w-100">
                        View ${type === 'farm' ? 'Farm' : 'Pond'} Details
                    </a>
                </div>
            `;
        } else {
            content.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h4>No Weather Data</h4>
                    <p>Weather information is not available for this location.</p>
                    <a href="${type === 'farm' ? '/ponds/farms/' + location.id + '/' : '/ponds/' + location.id + '/'}"
                       class="btn btn-primary">
                        View ${type === 'farm' ? 'Farm' : 'Pond'} Details
                    </a>
                </div>
            `;
        }

        // Show panel
        panel.classList.add('show');
    }

    // Get weather icon
    function getWeatherIcon(condition) {
        const icons = {
            'Clear': '<i class="fas fa-sun"></i>',
            'Partly Cloudy': '<i class="fas fa-cloud-sun"></i>',
            'Cloudy': '<i class="fas fa-cloud"></i>',
            'Overcast': '<i class="fas fa-cloud"></i>',
            'Light Rain': '<i class="fas fa-cloud-rain"></i>',
            'Heavy Rain': '<i class="fas fa-cloud-showers-heavy"></i>',
            'Thunderstorm': '<i class="fas fa-bolt"></i>',
            'Snow': '<i class="fas fa-snowflake"></i>',
            'Fog': '<i class="fas fa-smog"></i>',
            'Windy': '<i class="fas fa-wind"></i>'
        };
        return icons[condition] || '<i class="fas fa-question"></i>';
    }

    // Setup control handlers
    function setupControlHandlers() {
        // Map view controls
        document.querySelectorAll('[data-view]').forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.dataset.view;

                // Update active state
                document.querySelectorAll('[data-view]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Switch map type
                const mapTypes = {
                    satellite: google.maps.MapTypeId.SATELLITE,
                    terrain: google.maps.MapTypeId.TERRAIN,
                    street: google.maps.MapTypeId.ROADMAP
                };

                if (mapTypes[view]) {
                    weatherMap.setMapTypeId(mapTypes[view]);
                }
            });
        });

        // Weather layer controls
        document.querySelectorAll('[data-weather]').forEach(btn => {
            btn.addEventListener('click', function() {
                const weatherType = this.dataset.weather;

                // Update active state
                document.querySelectorAll('[data-weather]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Toggle weather layer
                toggleWeatherLayer(weatherType);
            });
        });

        // Show/hide controls
        document.querySelectorAll('[data-toggle]').forEach(btn => {
            btn.addEventListener('click', function() {
                const toggleType = this.dataset.toggle;
                const isActive = this.classList.contains('active');

                // Toggle active state
                this.classList.toggle('active');

                // Toggle markers
                if (toggleType === 'farms') {
                    farmMarkers.forEach(marker => {
                        if (isActive) {
                            marker.setMap(null);
                        } else {
                            marker.setMap(weatherMap);
                        }
                    });
                } else if (toggleType === 'ponds') {
                    pondMarkers.forEach(marker => {
                        if (isActive) {
                            marker.setMap(null);
                        } else {
                            marker.setMap(weatherMap);
                        }
                    });
                }
            });
        });
    }

    // Toggle weather layer
    function toggleWeatherLayer(weatherType) {
        // Remove existing weather layer
        if (weatherLayer) {
            weatherMap.overlayMapTypes.removeAt(0);
            weatherLayer = null;
        }

        // Hide legend
        document.getElementById('weather-legend').style.display = 'none';

        if (weatherType !== 'none') {
            // Add new weather layer using Google Maps ImageMapType
            const weatherUrls = {
                temperature: 'https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=9de243494c0b295cca9337e1e96b00e2',
                precipitation: 'https://tile.openweathermap.org/map/precipitation_new/{z}/{x}/{y}.png?appid=9de243494c0b295cca9337e1e96b00e2',
                wind: 'https://tile.openweathermap.org/map/wind_new/{z}/{x}/{y}.png?appid=9de243494c0b295cca9337e1e96b00e2'
            };

            if (weatherUrls[weatherType]) {
                weatherLayer = new google.maps.ImageMapType({
                    getTileUrl: function(coord, zoom) {
                        return weatherUrls[weatherType]
                            .replace('{z}', zoom)
                            .replace('{x}', coord.x)
                            .replace('{y}', coord.y);
                    },
                    tileSize: new google.maps.Size(256, 256),
                    opacity: 0.6,
                    name: weatherType.charAt(0).toUpperCase() + weatherType.slice(1)
                });

                weatherMap.overlayMapTypes.insertAt(0, weatherLayer);

                // Show legend
                showWeatherLegend(weatherType);
            }
        }

        currentWeatherType = weatherType;
    }

    // Show weather legend
    function showWeatherLegend(weatherType) {
        const legend = document.getElementById('weather-legend');
        const content = document.getElementById('legend-content');

        const legends = {
            temperature: [
                { color: '#313695', text: 'Very Cold (-40°C)' },
                { color: '#4575b4', text: 'Cold (-20°C)' },
                { color: '#74add1', text: 'Cool (0°C)' },
                { color: '#abd9e9', text: 'Mild (10°C)' },
                { color: '#fee090', text: 'Warm (20°C)' },
                { color: '#fdae61', text: 'Hot (30°C)' },
                { color: '#f46d43', text: 'Very Hot (40°C)' },
                { color: '#d73027', text: 'Extreme (50°C)' }
            ],
            precipitation: [
                { color: '#ffffff', text: 'No Rain (0 mm)' },
                { color: '#c6dbef', text: 'Light (0.1 mm)' },
                { color: '#9ecae1', text: 'Light (0.5 mm)' },
                { color: '#6baed6', text: 'Moderate (2 mm)' },
                { color: '#4292c6', text: 'Moderate (5 mm)' },
                { color: '#2171b5', text: 'Heavy (10 mm)' },
                { color: '#084594', text: 'Very Heavy (50 mm)' }
            ],
            wind: [
                { color: '#ffffff', text: 'Calm (0 km/h)' },
                { color: '#c7e9b4', text: 'Light (5 km/h)' },
                { color: '#7fcdbb', text: 'Gentle (15 km/h)' },
                { color: '#41b6c4', text: 'Moderate (25 km/h)' },
                { color: '#2c7fb8', text: 'Fresh (35 km/h)' },
                { color: '#253494', text: 'Strong (50 km/h)' },
                { color: '#081d58', text: 'Gale (75 km/h)' }
            ]
        };

        if (legends[weatherType]) {
            content.innerHTML = legends[weatherType].map(item => `
                <div class="legend-item">
                    <div class="legend-color" style="background-color: ${item.color};"></div>
                    <span class="legend-text">${item.text}</span>
                </div>
            `).join('');

            legend.style.display = 'block';
        }
    }

    // Global functions
    window.centerOnAllLocations = function() {
        if (mapData.bounds && mapData.bounds.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            mapData.bounds.forEach(bound => {
                bounds.extend(new google.maps.LatLng(bound[0], bound[1]));
            });
            weatherMap.fitBounds(bounds);
        }
    };

    window.refreshWeatherData = function() {
        // Show loading
        document.getElementById('loading-overlay').style.display = 'flex';

        // Simulate refresh (in real implementation, this would fetch new data)
        setTimeout(() => {
            document.getElementById('loading-overlay').style.display = 'none';

            // Show success message
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
            notification.innerHTML = '<i class="fas fa-check me-2"></i>Weather data refreshed successfully!';
            document.body.appendChild(notification);

            setTimeout(() => notification.remove(), 3000);
        }, 2000);
    };

    window.closeWeatherInfo = function() {
        document.getElementById('weather-info-panel').classList.remove('show');
    };

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Google Maps to load, then initialize
    if (typeof google !== 'undefined' && google.maps) {
        initUnifiedWeatherGoogleMaps();
    } else {
        // If Google Maps not loaded yet, wait for it
        window.initUnifiedWeatherGoogleMaps = initUnifiedWeatherGoogleMaps;
    }
});
</script>
<script src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=places,weather&callback=initUnifiedWeatherGoogleMaps"
        async defer onerror="handleGoogleMapsError()"></script>

<script>
// Handle Google Maps loading errors
function handleGoogleMapsError() {
    console.error('🚨 Failed to load Google Maps script for weather map');
    const loadingElement = document.getElementById('loading-overlay');
    if (loadingElement) {
        loadingElement.innerHTML = `
            <div class="alert alert-warning m-3">
                <h4><i class="fas fa-wifi"></i> Connection Issue</h4>
                <p>Unable to load Google Maps for weather data. Please check your internet connection and try refreshing the page.</p>
                <button class="btn btn-primary" onclick="location.reload()">
                    <i class="fas fa-refresh"></i> Refresh Page
                </button>
            </div>
        `;
    }
}
</script>
{% endblock %}