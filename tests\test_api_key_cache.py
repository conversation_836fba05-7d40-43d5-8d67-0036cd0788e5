import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import time
from django.test import TestCase
from django.core.cache import cache

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Try to import the cache module
try:
    from core.api_key_cache import ApiKeyCache, api_key_cache, CACHE_PREFIX, CACHE_TIMEOUT
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False


@unittest.skipIf(not CACHE_AVAILABLE, "API key cache module not available")
class TestAPIKeyCache(TestCase):
    """Test suite for the API key cache functionality."""

    def setUp(self):
        """Set up test environment."""
        # Sample test key and results
        self.test_key = "AIzaSyTestKey12345678901234567890"
        self.test_results = {
            'key': self.test_key,
            'key_length': 39,
            'status': 'success',
            'message': 'API key is valid and working',
            'apis_enabled': ['Static Maps API', 'JavaScript Maps API'],
            'restrictions': {},
            'cached': False,
            'timestamp': time.time()
        }
        
        # Clear the cache before tests
        api_key_cache.clear()
        cache.clear()

    def tearDown(self):
        """Clean up after tests."""
        # Clear the cache after tests
        api_key_cache.clear()
        cache.clear()

    def test_singleton_pattern(self):
        """Test that ApiKeyCache is a proper singleton."""
        cache1 = ApiKeyCache()
        cache2 = ApiKeyCache()
        
        # Both instances should be the same object
        self.assertIs(cache1, cache2)
        self.assertIs(cache1, api_key_cache)

    def test_get_cache_key(self):
        """Test the cache key generation."""
        cache_key = api_key_cache._get_cache_key(self.test_key)
        expected_key = f"{CACHE_PREFIX}{self.test_key}"
        
        self.assertEqual(cache_key, expected_key)

    def test_set_and_get(self):
        """Test setting and getting cache entries."""
        # Set cache entry
        api_key_cache.set(self.test_key, self.test_results)
        
        # Get cache entry
        results = api_key_cache.get(self.test_key)
        
        # Verify
        self.assertEqual(results, self.test_results)
        
        # Also check that it's in the Django cache
        cache_key = api_key_cache._get_cache_key(self.test_key)
        django_cached = cache.get(cache_key)
        self.assertEqual(django_cached, self.test_results)

    def test_get_nonexistent_key(self):
        """Test getting a non-existent key returns None."""
        results = api_key_cache.get("nonexistent_key")
        self.assertIsNone(results)

    def test_clear_specific_key(self):
        """Test clearing a specific key from the cache."""
        # Set multiple cache entries
        api_key_cache.set(self.test_key, self.test_results)
        api_key_cache.set("second_key", {"status": "error"})
        
        # Clear specific key
        api_key_cache.clear(self.test_key)
        
        # Verify first key is gone
        self.assertIsNone(api_key_cache.get(self.test_key))
        
        # Verify second key is still there
        self.assertIsNotNone(api_key_cache.get("second_key"))

    def test_clear_all_keys(self):
        """Test clearing all keys from the cache."""
        # Set multiple cache entries
        api_key_cache.set(self.test_key, self.test_results)
        api_key_cache.set("second_key", {"status": "error"})
        
        # Clear all keys
        api_key_cache.clear()
        
        # Verify local cache is empty
        self.assertEqual(api_key_cache._local_cache, {})
        
        # Verify both keys are gone
        self.assertIsNone(api_key_cache.get(self.test_key))
        self.assertIsNone(api_key_cache.get("second_key"))

    @patch('time.time')
    def test_refresh_clears_old_entries(self, mock_time):
        """Test that refresh clears old entries when they expire."""
        # Set the initial time
        initial_time = 1000000
        mock_time.return_value = initial_time
        
        # Set cache entries
        api_key_cache.set(self.test_key, self.test_results)
        
        # Verify cache entry exists
        self.assertIsNotNone(api_key_cache.get(self.test_key))
        
        # Advance time beyond cache timeout
        mock_time.return_value = initial_time + CACHE_TIMEOUT + 1
        
        # Call refresh
        api_key_cache.refresh()
        
        # Verify local cache is cleared
        self.assertEqual(api_key_cache._local_cache, {})
        
        # Verify _last_cleared is updated
        self.assertEqual(api_key_cache._last_cleared, initial_time + CACHE_TIMEOUT + 1)

    @patch('time.time')
    def test_refresh_does_nothing_for_recent_entries(self, mock_time):
        """Test that refresh doesn't clear entries when they haven't expired."""
        # Set the initial time
        initial_time = 1000000
        mock_time.return_value = initial_time
        
        # Set cache entries and save the local cache
        api_key_cache.set(self.test_key, self.test_results)
        original_cache = api_key_cache._local_cache.copy()
        
        # Advance time but not beyond cache timeout
        mock_time.return_value = initial_time + (CACHE_TIMEOUT / 2)
        
        # Call refresh
        api_key_cache.refresh()
        
        # Verify local cache is not cleared
        self.assertEqual(api_key_cache._local_cache, original_cache)
        
        # Verify _last_cleared is not updated
        self.assertEqual(api_key_cache._last_cleared, initial_time)


if __name__ == '__main__':
    unittest.main()