{% extends 'base.html' %}
{% load static %}

{% block title %}Production-Ready Global Platform - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .production-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .production-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: enterprise-sweep 4s infinite;
    }
    
    @keyframes enterprise-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .production-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 25px;
        border: 2px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }
    
    .production-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        border-color: rgba(255,255,255,0.4);
    }
    
    .production-badge {
        position: absolute;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9em;
        font-weight: bold;
        animation: pulse-enterprise 2s infinite;
    }
    
    @keyframes pulse-enterprise {
        0%, 100% { 
            opacity: 1; 
            box-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        50% { 
            opacity: 0.8; 
            box-shadow: 0 0 30px rgba(255,255,255,0.8);
        }
    }
    
    .cloud-badge { background: linear-gradient(45deg, #667eea, #764ba2); }
    .mobile-badge { background: linear-gradient(45deg, #f093fb, #f5576c); }
    .api-badge { background: linear-gradient(45deg, #4facfe, #00f2fe); }
    .global-badge { background: linear-gradient(45deg, #43e97b, #38f9d7); }
    
    .production-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
        margin-top: 40px;
    }
    
    .feature-icon {
        font-size: 3.5em;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(116, 185, 255, 0.5);
    }
    
    .production-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin: 40px 0;
    }
    
    .stat-card {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        background: rgba(255,255,255,0.15);
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.8em;
        font-weight: bold;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .enterprise-timeline {
        background: linear-gradient(45deg, #1e3c72, #2a5298);
        border-radius: 20px;
        padding: 30px;
        margin-top: 40px;
    }
    
    .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        border-left: 4px solid #74b9ff;
        transition: all 0.3s ease;
    }
    
    .timeline-item:hover {
        background: rgba(255,255,255,0.15);
        transform: translateX(10px);
    }
    
    .timeline-icon {
        font-size: 1.8em;
        margin-right: 20px;
        color: #74b9ff;
    }
    
    .enterprise-button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 35px;
        color: white;
        font-weight: bold;
        font-size: 1.1em;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .enterprise-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.6);
        background: linear-gradient(45deg, #764ba2, #667eea);
    }
    
    .deployment-status {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-top: 20px;
    }
    
    .status-indicator {
        width: 15px;
        height: 15px;
        border-radius: 50%;
        animation: blink-enterprise 1.5s infinite;
    }
    
    .status-live { background: #00ff00; }
    .status-deploying { background: #ffff00; }
    .status-ready { background: #00ffff; }
    
    @keyframes blink-enterprise {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Production Header -->
    <div class="production-header">
        <div class="position-relative">
            <h1><i class="fas fa-rocket me-3"></i>Production-Ready Global Platform</h1>
            <p class="lead mb-0">Enterprise-Grade Shrimp Farming Platform for Worldwide Deployment</p>
            <div class="mt-4">
                <span class="badge bg-light text-dark me-2">🔧 ENTERPRISE READY</span>
                <span class="badge bg-light text-dark me-2">📱 MOBILE FIRST</span>
                <span class="badge bg-light text-dark me-2">🔗 API DRIVEN</span>
                <span class="badge bg-light text-dark">🌍 GLOBALLY SCALABLE</span>
            </div>
        </div>
    </div>

    <!-- Production Statistics -->
    <div class="production-stats">
        <div class="stat-card">
            <div class="stat-number">99.9%</div>
            <h5>Uptime SLA</h5>
            <p>Enterprise Reliability</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">50+</div>
            <h5>Global Regions</h5>
            <p>Worldwide Coverage</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">10M+</div>
            <h5>API Calls/Day</h5>
            <p>Scalable Architecture</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">24/7</div>
            <h5>Support</h5>
            <p>Global Operations</p>
        </div>
    </div>

    <!-- Production Features Grid -->
    <div class="production-grid">
        <!-- Production Deployment -->
        <div class="production-card">
            <span class="production-badge cloud-badge">CLOUD</span>
            <div class="feature-icon"><i class="fas fa-cloud"></i></div>
            <h3>Enterprise Cloud Infrastructure</h3>
            <p>Multi-cloud deployment with Kubernetes orchestration, auto-scaling, and enterprise-grade security across AWS, Azure, and GCP.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Kubernetes Orchestration</li>
                <li><i class="fas fa-check text-success me-2"></i>Auto-scaling & Load Balancing</li>
                <li><i class="fas fa-check text-success me-2"></i>CI/CD Pipelines</li>
                <li><i class="fas fa-check text-success me-2"></i>SOC2 & ISO27001 Compliance</li>
            </ul>
            <div class="deployment-status">
                <div class="status-indicator status-live"></div>
                <span>Production Live</span>
            </div>
            <button class="enterprise-button mt-3">Deploy to Cloud</button>
        </div>

        <!-- Mobile Ecosystem -->
        <div class="production-card">
            <span class="production-badge mobile-badge">MOBILE</span>
            <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
            <h3>Complete Mobile Ecosystem</h3>
            <p>Flutter cross-platform apps, Progressive Web Apps, wearable integration, and voice assistant support for comprehensive mobile experience.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Flutter Cross-platform App</li>
                <li><i class="fas fa-check text-success me-2"></i>Progressive Web App</li>
                <li><i class="fas fa-check text-success me-2"></i>Wearable Integration</li>
                <li><i class="fas fa-check text-success me-2"></i>Voice Assistant Support</li>
            </ul>
            <div class="deployment-status">
                <div class="status-indicator status-ready"></div>
                <span>App Store Ready</span>
            </div>
            <button class="enterprise-button mt-3">Download Apps</button>
        </div>

        <!-- API Platform -->
        <div class="production-card">
            <span class="production-badge api-badge">API</span>
            <div class="feature-icon"><i class="fas fa-code"></i></div>
            <h3>Developer API Platform</h3>
            <p>Comprehensive API marketplace with SDKs, webhooks, and third-party integrations for building custom solutions and extensions.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>RESTful API with GraphQL</li>
                <li><i class="fas fa-check text-success me-2"></i>SDKs for 10+ Languages</li>
                <li><i class="fas fa-check text-success me-2"></i>Webhook System</li>
                <li><i class="fas fa-check text-success me-2"></i>API Marketplace</li>
            </ul>
            <div class="deployment-status">
                <div class="status-indicator status-live"></div>
                <span>API Live</span>
            </div>
            <button class="enterprise-button mt-3">Access API Docs</button>
        </div>

        <!-- Global Expansion -->
        <div class="production-card">
            <span class="production-badge global-badge">GLOBAL</span>
            <div class="feature-icon"><i class="fas fa-globe"></i></div>
            <h3>Worldwide Deployment</h3>
            <p>Multi-language support, regional compliance, local currency integration, and cultural adaptation for global market expansion.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>50+ Language Support</li>
                <li><i class="fas fa-check text-success me-2"></i>Regional Compliance (GDPR, CCPA)</li>
                <li><i class="fas fa-check text-success me-2"></i>Multi-currency Support</li>
                <li><i class="fas fa-check text-success me-2"></i>Cultural Adaptation</li>
            </ul>
            <div class="deployment-status">
                <div class="status-indicator status-deploying"></div>
                <span>Global Rollout</span>
            </div>
            <button class="enterprise-button mt-3">Expand Globally</button>
        </div>
    </div>

    <!-- Enterprise Timeline -->
    <div class="enterprise-timeline">
        <h3><i class="fas fa-rocket me-2"></i>Production Deployment Timeline</h3>
        <p class="mb-4">From development to global enterprise deployment</p>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-cloud"></i></div>
            <div>
                <h5>Cloud Infrastructure Setup</h5>
                <p>Multi-cloud deployment with enterprise security and compliance</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-mobile"></i></div>
            <div>
                <h5>Mobile App Development</h5>
                <p>Cross-platform mobile applications with offline capabilities</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-code"></i></div>
            <div>
                <h5>API Platform Launch</h5>
                <p>Developer ecosystem with SDKs and marketplace</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-globe"></i></div>
            <div>
                <h5>Global Market Expansion</h5>
                <p>Worldwide deployment with localization and compliance</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-users"></i></div>
            <div>
                <h5>Enterprise Customer Onboarding</h5>
                <p>Large-scale commercial deployments and support</p>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mt-5">
        <h2>Ready for Enterprise Deployment</h2>
        <p class="lead">The world's most advanced shrimp farming platform is production-ready</p>
        <div class="mt-4">
            <button class="enterprise-button me-3">Start Free Trial</button>
            <button class="enterprise-button me-3">Request Demo</button>
            <button class="enterprise-button">Contact Sales</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate production cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.production-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Animate statistics counters
    const counters = document.querySelectorAll('.stat-number');
    counters.forEach(counter => {
        const target = counter.textContent;
        if (target.includes('%')) {
            const num = parseFloat(target);
            animateCounter(counter, 0, num, '%');
        } else if (target.includes('M+')) {
            const num = parseFloat(target);
            animateCounter(counter, 0, num, 'M+');
        } else if (target.includes('+')) {
            const num = parseInt(target);
            animateCounter(counter, 0, num, '+');
        }
    });
    
    function animateCounter(element, start, end, suffix) {
        const duration = 2000;
        const increment = (end - start) / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                element.textContent = end + suffix;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current) + suffix;
            }
        }, 16);
    }
    
    // Enterprise button effects
    document.querySelectorAll('.enterprise-button').forEach(button => {
        button.addEventListener('click', function() {
            // Create enterprise ripple effect
            const ripple = document.createElement('span');
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255,255,255,0.6)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'enterprise-ripple 0.6s linear';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            
            this.style.position = 'relative';
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Add enterprise ripple animation
const style = document.createElement('style');
style.textContent = `
    @keyframes enterprise-ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
