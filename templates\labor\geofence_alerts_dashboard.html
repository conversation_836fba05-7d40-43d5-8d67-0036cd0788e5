<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚨 Geofence Alerts Dashboard - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .alerts-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .alerts-header {
            background: linear-gradient(135deg, #f44336 0%, #e91e63 50%, #9c27b0 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            color: white;
            text-align: center;
        }

        .alert-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #f44336;
            transition: transform 0.3s ease;
        }

        .alert-card:hover {
            transform: translateX(5px);
        }

        .alert-card.entry {
            border-left-color: #4CAF50;
        }

        .alert-card.exit {
            border-left-color: #FF9800;
        }

        .alert-card.violation {
            border-left-color: #f44336;
        }

        .alert-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .alert-icon {
            font-size: 1.5rem;
            margin-right: 15px;
        }

        .alert-icon.entry { color: #4CAF50; }
        .alert-icon.exit { color: #FF9800; }
        .alert-icon.violation { color: #f44336; }

        .alert-time {
            font-size: 0.9rem;
            color: #666;
            margin-left: auto;
        }

        .alert-details {
            margin-left: 45px;
        }

        .worker-name {
            font-weight: 600;
            color: #333;
        }

        .geofence-name {
            color: #2196F3;
            font-weight: 500;
        }

        .live-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(76, 175, 80, 0.1);
            border: 2px solid #4CAF50;
            border-radius: 20px;
            padding: 8px 16px;
            color: #4CAF50;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
        }

        .geofence-status {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .geofence-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
        }

        .geofence-item:last-child {
            border-bottom: none;
        }

        .geofence-info {
            flex: 1;
        }

        .geofence-workers {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 5px 15px;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .filter-controls {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn-filter {
            margin: 5px;
            border-radius: 20px;
            padding: 8px 16px;
            border: 2px solid #e0e0e0;
            background: white;
            color: #666;
            transition: all 0.3s ease;
        }

        .btn-filter.active {
            background: #f44336;
            color: white;
            border-color: #f44336;
        }

        .real-time-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .real-time-alert.show {
            transform: translateX(0);
        }

        .alert-sound-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            transition: all 0.3s ease;
        }

        .alert-sound-toggle:hover {
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="alerts-container">
        <div class="alerts-header">
            <h1 class="display-4 fw-bold mb-3">🚨 Geofence Alerts Dashboard</h1>
            <p class="lead mb-0">Real-time monitoring of worker zone entries and exits</p>
            <div class="live-indicator mt-3">
                <div class="live-dot"></div>
                Live Monitoring Active
            </div>
        </div>

        <!-- Statistics Row -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-value text-danger" id="totalAlertsToday">{{ total_events_today|default:0 }}</div>
                <div class="stat-label">Alerts Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-success" id="workersInZones">{{ workers_in_geofences|length }}</div>
                <div class="stat-label">Workers in Zones</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-info" id="activeGeofences">{{ active_geofences|length }}</div>
                <div class="stat-label">Active Geofences</div>
            </div>
            <div class="stat-card">
                <div class="stat-value text-warning" id="recentAlerts">5</div>
                <div class="stat-label">Last Hour</div>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-2">🔍 Filter Alerts</h6>
                    <button class="btn-filter active" onclick="filterAlerts('all')">All Events</button>
                    <button class="btn-filter" onclick="filterAlerts('entry')">Entries</button>
                    <button class="btn-filter" onclick="filterAlerts('exit')">Exits</button>
                    <button class="btn-filter" onclick="filterAlerts('violation')">Violations</button>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-2">⏰ Time Range</h6>
                    <button class="btn-filter active" onclick="filterTime('1h')">Last Hour</button>
                    <button class="btn-filter" onclick="filterTime('4h')">4 Hours</button>
                    <button class="btn-filter" onclick="filterTime('today')">Today</button>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Recent Alerts -->
            <div class="col-md-8">
                <div class="geofence-status">
                    <h3 class="mb-4">📋 Recent Geofence Events</h3>
                    <div id="alertsList">
                        {% for event in recent_events %}
                        <div class="alert-card {{ event.event_type }}">
                            <div class="alert-header">
                                <i class="fas fa-{% if event.event_type == 'enter' %}sign-in-alt{% elif event.event_type == 'exit' %}sign-out-alt{% else %}exclamation-triangle{% endif %} alert-icon {{ event.event_type }}"></i>
                                <div class="alert-time">{{ event.timestamp|timesince }} ago</div>
                            </div>
                            <div class="alert-details">
                                <div class="worker-name">{{ event.worker.name }}</div>
                                <div>
                                    {% if event.event_type == 'enter' %}
                                        Entered <span class="geofence-name">{{ event.geofence.name }}</span>
                                    {% elif event.event_type == 'exit' %}
                                        Exited <span class="geofence-name">{{ event.geofence.name }}</span>
                                    {% else %}
                                        Violation in <span class="geofence-name">{{ event.geofence.name }}</span>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    📍 {{ event.latitude|floatformat:6 }}, {{ event.longitude|floatformat:6 }}
                                </small>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-info-circle fa-2x mb-3"></i>
                            <p>No recent geofence events</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Current Status -->
            <div class="col-md-4">
                <div class="geofence-status">
                    <h3 class="mb-4">🎯 Current Zone Status</h3>
                    {% for geofence in active_geofences %}
                    <div class="geofence-item">
                        <div class="geofence-info">
                            <div class="fw-bold">{{ geofence.name }}</div>
                            <small class="text-muted">{{ geofence.description }}</small>
                        </div>
                        <div class="geofence-workers">
                            {{ geofence.workers_count|default:0 }} workers
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <div class="geofence-status">
                    <h3 class="mb-4">👥 Workers in Zones</h3>
                    {% for item in workers_in_geofences %}
                    <div class="geofence-item">
                        <div class="geofence-info">
                            <div class="fw-bold">{{ item.worker.name }}</div>
                            <small class="text-muted">{{ item.geofence.name }}</small>
                        </div>
                        <div class="geofence-workers">
                            {{ item.duration.seconds|floatformat:0 }}m
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center text-muted py-3">
                        <p>No workers currently in monitored zones</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Alert Notification -->
    <div class="real-time-alert" id="realTimeAlert">
        <div class="fw-bold mb-1" id="alertTitle">New Geofence Alert</div>
        <div id="alertMessage">Worker entered restricted zone</div>
    </div>

    <!-- Sound Toggle Button -->
    <button class="alert-sound-toggle" id="soundToggle" onclick="toggleAlertSound()" title="Toggle Alert Sound">
        <i class="fas fa-volume-up" id="soundIcon"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Geofence Alert System
        let alertSoundEnabled = true;
        let currentFilter = 'all';
        let currentTimeFilter = '1h';

        // Simulate real-time alerts
        function simulateRealTimeAlert() {
            const alertTypes = [
                { type: 'entry', worker: 'Rajesh Kumar', zone: 'Pond A-1', icon: 'sign-in-alt' },
                { type: 'exit', worker: 'Priya Sharma', zone: 'Feed Storage', icon: 'sign-out-alt' },
                { type: 'violation', worker: 'Amit Singh', zone: 'Restricted Area', icon: 'exclamation-triangle' }
            ];

            const alert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
            
            showRealTimeAlert(alert);
            addAlertToList(alert);
            updateStatistics();
            
            if (alertSoundEnabled) {
                playAlertSound();
            }
        }

        function showRealTimeAlert(alert) {
            const alertDiv = document.getElementById('realTimeAlert');
            const titleDiv = document.getElementById('alertTitle');
            const messageDiv = document.getElementById('alertMessage');

            titleDiv.textContent = `${alert.type.toUpperCase()} Alert`;
            messageDiv.textContent = `${alert.worker} ${alert.type === 'entry' ? 'entered' : alert.type === 'exit' ? 'exited' : 'violated'} ${alert.zone}`;

            alertDiv.classList.add('show');
            
            setTimeout(() => {
                alertDiv.classList.remove('show');
            }, 5000);
        }

        function addAlertToList(alert) {
            const alertsList = document.getElementById('alertsList');
            const alertCard = document.createElement('div');
            alertCard.className = `alert-card ${alert.type}`;
            alertCard.innerHTML = `
                <div class="alert-header">
                    <i class="fas fa-${alert.icon} alert-icon ${alert.type}"></i>
                    <div class="alert-time">just now</div>
                </div>
                <div class="alert-details">
                    <div class="worker-name">${alert.worker}</div>
                    <div>
                        ${alert.type === 'entry' ? 'Entered' : alert.type === 'exit' ? 'Exited' : 'Violation in'} 
                        <span class="geofence-name">${alert.zone}</span>
                    </div>
                    <small class="text-muted">📍 Live Location</small>
                </div>
            `;
            
            alertsList.insertBefore(alertCard, alertsList.firstChild);
            
            // Remove old alerts (keep only 20)
            const alerts = alertsList.children;
            if (alerts.length > 20) {
                alertsList.removeChild(alerts[alerts.length - 1]);
            }
        }

        function updateStatistics() {
            const totalAlerts = document.getElementById('totalAlertsToday');
            const recentAlerts = document.getElementById('recentAlerts');
            
            totalAlerts.textContent = parseInt(totalAlerts.textContent) + 1;
            recentAlerts.textContent = parseInt(recentAlerts.textContent) + 1;
        }

        function playAlertSound() {
            // Create audio context for alert sound
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }

        function toggleAlertSound() {
            alertSoundEnabled = !alertSoundEnabled;
            const icon = document.getElementById('soundIcon');
            
            if (alertSoundEnabled) {
                icon.className = 'fas fa-volume-up';
            } else {
                icon.className = 'fas fa-volume-mute';
            }
        }

        function filterAlerts(type) {
            currentFilter = type;
            document.querySelectorAll('.btn-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            const alerts = document.querySelectorAll('.alert-card');
            alerts.forEach(alert => {
                if (type === 'all' || alert.classList.contains(type)) {
                    alert.style.display = 'block';
                } else {
                    alert.style.display = 'none';
                }
            });
        }

        function filterTime(range) {
            currentTimeFilter = range;
            document.querySelectorAll('.btn-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // In real implementation, this would filter by actual timestamps
            console.log(`Filtering alerts for time range: ${range}`);
        }

        // Initialize real-time simulation
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate alerts every 10-30 seconds
            setInterval(() => {
                if (Math.random() > 0.7) {
                    simulateRealTimeAlert();
                }
            }, Math.random() * 20000 + 10000);
        });
    </script>
</body>
</html>
