import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from water_quality.models import WaterQualityParameter, WaterQualityReading
from ponds.models import Pond
from users.models import User
# Note: Farm model is not implemented yet - creating simple farm placeholder


class WaterQualityModelTests(TestCase):
    """Test suite for the water quality models."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Note: Farm model not properly implemented, creating pond without farm reference
        self.pond = Pond.objects.create(
            name='Test Pond',
            area=1000.0,
            depth=2.5,
            shape='rectangular',
            coordinates=[
                {'latitude': 10.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 20.0},
            ],
            status='active',
            water_source='river',
            bottom_type='mud',
            created_by=self.user
        )
        
        # Create water quality parameters
        self.temperature_param = WaterQualityParameter.objects.create(
            name='Temperature',
            unit='°C',
            min_value=25.0,
            max_value=32.0,
            optimal_value=28.0,
            description='Water temperature',
            created_by=self.user
        )
        
        self.ph_param = WaterQualityParameter.objects.create(
            name='pH',
            unit='pH',
            min_value=7.0,
            max_value=8.5,
            optimal_value=7.8,
            description='Water pH level',
            created_by=self.user
        )
        
        self.oxygen_param = WaterQualityParameter.objects.create(
            name='Dissolved Oxygen',
            unit='mg/L',
            min_value=4.0,
            max_value=None,
            optimal_value=6.0,
            description='Dissolved oxygen level',
            created_by=self.user
        )

    def test_parameter_creation(self):
        """Test creating water quality parameters."""
        self.assertEqual(self.temperature_param.name, 'Temperature')
        self.assertEqual(self.temperature_param.unit, '°C')
        self.assertEqual(self.temperature_param.min_value, 25.0)
        self.assertEqual(self.temperature_param.max_value, 32.0)
        self.assertEqual(self.temperature_param.optimal_value, 28.0)
        self.assertEqual(self.temperature_param.created_by, self.user)
        
        self.assertEqual(str(self.temperature_param), 'Temperature (°C)')

    def test_record_creation(self):
        """Test creating water quality records."""
        record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=27.5,
            recorded_at=datetime.now(),
            recorded_by=self.user,
            notes='Test measurement'
        )
        
        self.assertEqual(record.pond, self.pond)
        self.assertEqual(record.parameter, self.temperature_param)
        self.assertEqual(record.value, 27.5)
        self.assertEqual(record.recorded_by, self.user)
        self.assertEqual(record.notes, 'Test measurement')
        
        self.assertEqual(str(record), f'Temperature: 27.5 °C ({self.pond.name})')

    def test_record_status(self):
        """Test the status property of water quality records."""
        # Normal value
        normal_record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=28.0,  # Optimal value
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        self.assertEqual(normal_record.status, 'normal')
        
        # Warning (high) value
        high_record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=31.5,  # Close to max but not exceeding
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        self.assertEqual(high_record.status, 'warning')
        
        # Warning (low) value
        low_record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=25.5,  # Close to min but not below
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        self.assertEqual(low_record.status, 'warning')
        
        # Critical (high) value
        critical_high_record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=33.0,  # Above max
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        self.assertEqual(critical_high_record.status, 'critical')
        
        # Critical (low) value
        critical_low_record = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=24.0,  # Below min
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        self.assertEqual(critical_low_record.status, 'critical')


class WaterQualityAPITests(TestCase):
    """Test suite for the water quality API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Note: Farm model not properly implemented, creating pond without farm reference
        self.pond = Pond.objects.create(
            name='Test Pond',
            area=1000.0,
            depth=2.5,
            shape='rectangular',
            coordinates=[
                {'latitude': 10.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 20.0},
            ],
            status='active',
            water_source='river',
            bottom_type='mud',
            created_by=self.user
        )
        
        # Create water quality parameters
        self.temperature_param = WaterQualityParameter.objects.create(
            name='Temperature',
            unit='°C',
            min_value=25.0,
            max_value=32.0,
            optimal_value=28.0,
            description='Water temperature',
            created_by=self.user
        )
        
        self.ph_param = WaterQualityParameter.objects.create(
            name='pH',
            unit='pH',
            min_value=7.0,
            max_value=8.5,
            optimal_value=7.8,
            description='Water pH level',
            created_by=self.user
        )
        
        # Create some water quality records
        self.record1 = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=27.5,
            recorded_at=datetime.now() - timedelta(days=1),
            recorded_by=self.user
        )
        
        self.record2 = WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.ph_param,
            value=7.5,
            recorded_at=datetime.now() - timedelta(hours=12),
            recorded_by=self.user
        )
        
        # Authenticate the client
        self.client.force_authenticate(user=self.user)

    def test_get_parameters(self):
        """Test retrieving water quality parameters."""
        url = reverse('waterqualityparameter-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['name'], 'Temperature')
        self.assertEqual(response.data[1]['name'], 'pH')

    def test_create_parameter(self):
        """Test creating a new water quality parameter."""
        url = reverse('waterqualityparameter-list')
        data = {
            'name': 'Ammonia',
            'unit': 'mg/L',
            'min_value': 0.0,
            'max_value': 0.1,
            'optimal_value': 0.0,
            'description': 'Ammonia level in water'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'Ammonia')
        self.assertEqual(response.data['unit'], 'mg/L')
        self.assertEqual(WaterQualityParameter.objects.count(), 3)

    def test_get_records(self):
        """Test retrieving water quality records."""
        url = reverse('waterqualityreading-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)

    def test_create_record(self):
        """Test creating a new water quality record."""
        url = reverse('waterqualityreading-list')
        data = {
            'pond': self.pond.id,
            'parameter': self.temperature_param.id,
            'value': 29.0,
            'recorded_at': datetime.now().isoformat(),
            'notes': 'Afternoon measurement'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['value'], 29.0)
        self.assertEqual(response.data['status'], 'normal')
        self.assertEqual(WaterQualityReading.objects.count(), 3)

    def test_filter_records_by_pond(self):
        """Test filtering water quality records by pond."""
        # Create another pond and record
        pond2 = Pond.objects.create(
            name='Second Pond',
            farm=self.farm,
            area=800.0,
            depth=2.0,
            shape='rectangular',
            coordinates=[
                {'latitude': 12.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 22.0},
            ],
            status='active',
            water_source='well',
            bottom_type='sand',
            created_by=self.user
        )
        
        WaterQualityReading.objects.create(
            pond=pond2,
            parameter=self.temperature_param,
            value=26.0,
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        
        # Filter by the first pond
        url = f"{reverse('waterqualityreading-list')}?pond={self.pond.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        
        # Filter by the second pond
        url = f"{reverse('waterqualityreading-list')}?pond={pond2.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['value'], 26.0)

    def test_filter_records_by_parameter(self):
        """Test filtering water quality records by parameter."""
        # Filter by temperature parameter
        url = f"{reverse('waterqualityreading-list')}?parameter={self.temperature_param.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['value'], 27.5)
        
        # Filter by pH parameter
        url = f"{reverse('waterqualityreading-list')}?parameter={self.ph_param.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['value'], 7.5)

    def test_filter_records_by_date_range(self):
        """Test filtering water quality records by date range."""
        # Create a record for today
        WaterQualityReading.objects.create(
            pond=self.pond,
            parameter=self.temperature_param,
            value=28.0,
            recorded_at=datetime.now(),
            recorded_by=self.user
        )
        
        # Filter by today's date
        today = datetime.now().date().isoformat()
        url = f"{reverse('waterqualityreading-list')}?date_from={today}&date_to={today}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['value'], 28.0)
        
        # Filter by last 2 days
        two_days_ago = (datetime.now() - timedelta(days=2)).date().isoformat()
        url = f"{reverse('waterqualityreading-list')}?date_from={two_days_ago}&date_to={today}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 3)

    def test_get_latest_records(self):
        """Test retrieving the latest water quality records for a pond."""
        url = f"{reverse('pond-latest-water-quality', args=[self.pond.id])}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # One for each parameter
        
        # Verify that we get the latest record for each parameter
        for record in response.data:
            if record['parameter']['name'] == 'Temperature':
                self.assertEqual(record['value'], 27.5)
            elif record['parameter']['name'] == 'pH':
                self.assertEqual(record['value'], 7.5)
