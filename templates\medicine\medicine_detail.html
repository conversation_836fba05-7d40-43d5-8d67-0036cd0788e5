<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ medicine.name }} - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-pills medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Detail View</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">{{ medicine.name }}</h2>
                    <p class="mb-0 opacity-75">Medicine information and usage details</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Stock Level</small>
                                <strong>{{ medicine.stock_quantity }} {{ medicine.unit }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/medicine/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to List
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/medicine/{{ medicine.id }}/edit/" class="quick-action">
                <i class="fas fa-edit"></i>
                Edit Medicine
            </a>
            <a href="/medicine/application/create/?medicine_id={{ medicine.id }}" class="quick-action">
                <i class="fas fa-syringe"></i>
                Record Application
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-list"></i>
                Medicine List
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
    
    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-4">
            <!-- Medicine Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Medicine Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Category</p>
                        <p class="mb-0">{{ medicine.category.name }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Status</p>
                        <span class="badge {% if medicine.status == 'In Stock' %}bg-success{% elif medicine.status == 'Low Stock' %}bg-warning text-dark{% elif medicine.status == 'Out of Stock' %}bg-danger{% else %}bg-secondary{% endif %}">
                            {{ medicine.status }}
                        </span>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Stock Quantity</p>
                        <p class="mb-0">{{ medicine.stock_quantity }} {{ medicine.unit }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Expiry Date</p>
                        {% if medicine.expiry_date %}
                        <p class="mb-0">
                            {{ medicine.expiry_date|date:"M d, Y" }}
                            {% if medicine.is_expired %}
                            <span class="badge bg-danger">Expired</span>
                            {% elif medicine.days_until_expiry < 30 %}
                            <span class="badge bg-warning text-dark">Expires in {{ medicine.days_until_expiry }} days</span>
                            {% endif %}
                        </p>
                        {% else %}
                        <p class="text-muted mb-0">Not set</p>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Supplier</p>
                        <p class="mb-0">{{ medicine.supplier|default:"Not specified" }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Purchase Date</p>
                        <p class="mb-0">{{ medicine.purchase_date|date:"M d, Y"|default:"Not specified" }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Purchase Price</p>
                        <p class="mb-0">{{ medicine.purchase_price|default:"Not specified" }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Description and Instructions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Description & Instructions</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Description</p>
                        <p class="mb-0">{{ medicine.description|default:"No description provided"|linebreaks }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Dosage Instructions</p>
                        <p class="mb-0">{{ medicine.dosage_instructions|default:"No dosage instructions provided"|linebreaks }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Notes</p>
                        <p class="mb-0">{{ medicine.notes|default:"No notes"|linebreaks }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="col-lg-8">
            <!-- Usage Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Usage Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="bg-light p-3 rounded text-center">
                                <h6 class="text-muted mb-1">Total Used</h6>
                                <h3 class="mb-0">{{ total_used }} {{ medicine.unit }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="bg-light p-3 rounded text-center">
                                <h6 class="text-muted mb-1">Current Stock</h6>
                                <h3 class="mb-0">{{ medicine.stock_quantity }} {{ medicine.unit }}</h3>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="bg-light p-3 rounded text-center">
                                <h6 class="text-muted mb-1">Applications</h6>
                                <h3 class="mb-0">{{ recent_applications.count }}</h3>
                            </div>
                        </div>
                    </div>
                    
                    {% if usage_by_pond %}
                    <h6 class="mb-3">Usage by Pond</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Pond</th>
                                    <th>Total Used</th>
                                    <th>Applications</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for usage in usage_by_pond %}
                                <tr>
                                    <td>{{ usage.pond__name }}</td>
                                    <td>{{ usage.total_used }} {{ medicine.unit }}</td>
                                    <td>{{ usage.application_count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No usage data available for this medicine.
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Recent Applications -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Applications</h5>
                    <a href="{% url 'medicine:application_list' %}?medicine={{ medicine.id }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_applications %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Pond</th>
                                    <th>Quantity</th>
                                    <th>Reason</th>
                                    <th>Applied By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for application in recent_applications %}
                                <tr>
                                    <td>{{ application.application_date|date:"M d, Y" }}</td>
                                    <td>{{ application.pond.name }}</td>
                                    <td>{{ application.quantity_used }} {{ medicine.unit }}</td>
                                    <td>{{ application.reason }}</td>
                                    <td>{{ application.applied_by }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No applications recorded for this medicine.
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Treatment Plans -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Treatment Plans Using This Medicine</h5>
                    <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-sm btn-outline-primary">
                        View All Plans
                    </a>
                </div>
                <div class="card-body">
                    {% if treatment_steps %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Treatment Plan</th>
                                    <th>Pond</th>
                                    <th>Day</th>
                                    <th>Dosage</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for step in treatment_steps %}
                                <tr>
                                    <td>
                                        <a href="{% url 'medicine:treatment_plan_detail' step.treatment_plan.id %}">
                                            {{ step.treatment_plan.name }}
                                        </a>
                                    </td>
                                    <td>{{ step.treatment_plan.pond.name }}</td>
                                    <td>Day {{ step.day_number }}</td>
                                    <td>{{ step.dosage }}</td>
                                    <td>
                                        <span class="badge {% if step.status == 'Completed' %}bg-success{% elif step.status == 'Pending' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">
                                            {{ step.status }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This medicine is not used in any treatment plans.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
