{% extends 'base.html' %}
{% load alert_filters %}
{% load static %}

{% block title %}Modern Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    /* Override base template styles for full-width modern dashboard */
    .main-content {
        margin-left: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }

    .sidebar {
        display: none !important;
    }
    
    /* Ensure dark background for entire page and dashboard */
    html, body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%) !important;
        background-color: #0f172a !important;
        color: #ffffff !important;
        min-height: 100vh;
        width: 100%;
        overflow-x: hidden;
    }
    
    .modern-dashboard {
        background: transparent !important;
        min-height: 100vh;
        padding: 0;
    }

    .dashboard-header {
        background: rgba(15, 23, 42, 0.7);
        backdrop-filter: blur(20px);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
    }

    .dashboard-subtitle {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 0;
    }

    .modern-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        padding: 24px;
        margin-bottom: 24px;
        transition: all 0.3s ease;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        position: relative;
    }

    .modern-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
        pointer-events: none;
    }

    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }

    .stat-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(102, 126, 234, 0.1);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 8px;
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 8px;
    }

    .stat-trend {
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .stat-trend.positive {
        color: #10b981;
    }

    .stat-trend.negative {
        color: #ef4444;
    }

    .widget-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 2px solid #f1f5f9;
    }

    .widget-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .widget-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 18px;
    }

    .pond-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .pond-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 16px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border: 1px solid rgba(102, 126, 234, 0.1);
        position: relative;
        overflow: hidden;
    }

    .pond-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .pond-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    }

    .pond-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .pond-name {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .pond-size {
        color: #6b7280;
        font-size: 0.9rem;
        margin: 0;
    }

    .pond-status-icon {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 16px;
    }

    .progress-modern {
        height: 8px;
        border-radius: 4px;
        background: #e5e7eb;
        overflow: hidden;
        margin-bottom: 16px;
    }

    .progress-bar-modern {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .pond-metrics {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 16px;
    }

    .metric-item {
        background: #f8fafc;
        border-radius: 8px;
        padding: 12px;
        text-align: center;
    }

    .metric-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 4px;
    }

    .metric-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: #1f2937;
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.good {
        background: #d1fae5;
        color: #065f46;
    }

    .status-badge.average {
        background: #fef3c7;
        color: #92400e;
    }

    .status-badge.poor {
        background: #fee2e2;
        color: #991b1b;
    }

    .btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        color: white;
        padding: 8px 16px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }

    .btn-modern:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        color: white;
        text-decoration: none;
    }

    .alert-modern {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 12px;
        border-left: 4px solid;
        transition: all 0.3s ease;
    }

    .alert-modern:hover {
        transform: translateX(4px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
    }

    .alert-modern.critical {
        border-left-color: #ef4444;
        background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    }

    .alert-modern.warning {
        border-left-color: #f59e0b;
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    }

    .alert-modern.info {
        border-left-color: #3b82f6;
        background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    }

    .search-modern {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 8px 16px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .search-modern:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Dark theme for cards */
    .card {
        background: rgba(30, 41, 59, 0.8) !important;
        border: 1px solid rgba(74, 85, 104, 0.3) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        color: white !important;
    }
    
    .card-header {
        background: rgba(15, 23, 42, 0.6) !important;
        border-bottom: 1px solid rgba(74, 85, 104, 0.3) !important;
        color: #ffffff !important;
    }
    
    .card-body {
        color: #e2e8f0 !important;
    }
    
    .card-footer {
        background: rgba(17, 24, 39, 0.6) !important;
        border-top: 1px solid rgba(74, 85, 104, 0.3) !important;
        color: #cbd5e1 !important;
    }
    
    /* Make text in tables visible */
    table {
        color: #e2e8f0 !important;
    }
    
    /* Make sure all text is visible */
    .text-dark, .text-body {
        color: #e2e8f0 !important;
    }
    
    /* Fix background for inputs and form controls */
    input, select, textarea, .form-control {
        background-color: rgba(15, 23, 42, 0.8) !important;
        border-color: rgba(74, 85, 104, 0.5) !important;
        color: white !important;
    }
    
    /* Fix dropdown menus */
    .dropdown-menu {
        background-color: #1e293b !important;
        border-color: #334155 !important;
    }
    
    .dropdown-item {
        color: #e2e8f0 !important;
    }
    
    .dropdown-item:hover {
        background-color: #334155 !important;
    }

    /* Fix bg-light elements */
    .bg-light, div[class*="bg-light"] {
        background-color: rgba(30, 41, 59, 0.6) !important;
        color: #e2e8f0 !important;
    }
    
    /* Fix progress bars */
    .progress {
        background-color: rgba(15, 23, 42, 0.5) !important;
    }
    
    .progress-bar, .progress-bar-modern {
        background-color: #3b82f6 !important;
    }

    /* Enhance badges and labels for dark theme */
    .badge {
        color: white !important;
    }
    
    /* Ensure buttons have good contrast */
    .btn-light {
        background-color: #334155 !important;
        border-color: #475569 !important;
        color: white !important;
    }
    
    .btn-outline-light {
        border-color: #475569 !important;
        color: #e2e8f0 !important;
    }
    
    .btn-outline-light:hover {
        background-color: #334155 !important;
        color: white !important;
    }
    
    /* Fix list groups */
    .list-group-item {
        background-color: rgba(30, 41, 59, 0.4) !important;
        border-color: rgba(74, 85, 104, 0.3) !important;
        color: #e2e8f0 !important;
    }

    /* Enhanced Responsive Design for Mobile & Web */
    @media (max-width: 1200px) {
        .dashboard-title {
            font-size: 2.2rem;
        }
        
        .stat-value {
            font-size: 2.2rem;
        }
    }

    @media (max-width: 992px) {
        .modern-dashboard {
            padding: 15px;
        }

        .dashboard-header {
            padding: 20px 0;
            margin-bottom: 20px;
        }

        .dashboard-title {
            font-size: 2rem;
        }

        .stat-card {
            margin-bottom: 20px;
        }

        .pond-grid {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .widget-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
    }

    @media (max-width: 768px) {
        .modern-dashboard {
            padding: 10px;
        }

        .dashboard-header {
            padding: 15px 0;
            margin-bottom: 15px;
        }

        .dashboard-title {
            font-size: 1.8rem;
            text-align: center;
        }

        .dashboard-subtitle {
            font-size: 1rem;
            text-align: center;
        }

        /* Mobile Navigation */
        .navbar-brand {
            font-size: 1.1rem;
        }

        .nav-link {
            padding: 0.5rem 0.75rem;
        }

        /* Mobile Stats Grid */
        .stat-card {
            padding: 20px;
            margin-bottom: 15px;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
            margin-bottom: 12px;
        }

        .stat-value {
            font-size: 2rem;
        }

        .stat-label {
            font-size: 0.85rem;
        }

        /* Mobile Pond Grid */
        .pond-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .pond-card {
            padding: 16px;
        }

        /* Mobile IoT Widgets */
        .iot-widget {
            margin-bottom: 15px;
            padding: 16px;
        }

        .power-phases .row {
            gap: 8px;
        }

        .phase-display {
            padding: 8px;
        }

        /* Mobile Map */
        #dashboard-map-container {
            height: 300px;
        }

        /* Mobile Cards */
        .modern-card, .card {
            padding: 16px;
            margin-bottom: 16px;
        }

        /* Mobile Buttons */
        .btn-modern {
            padding: 10px 16px;
            font-size: 0.9rem;
        }

        /* Mobile Tables */
        .table-responsive {
            font-size: 0.85rem;
        }
    }

    @media (max-width: 576px) {
        .modern-dashboard {
            padding: 8px;
        }

        .dashboard-header {
            padding: 10px 0;
            margin-bottom: 10px;
        }

        .dashboard-title {
            font-size: 1.6rem;
        }

        .dashboard-subtitle {
            font-size: 0.9rem;
        }

        /* Extra Small Mobile Adjustments */
        .stat-card {
            padding: 16px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.8rem;
        }

        .stat-icon {
            width: 45px;
            height: 45px;
            font-size: 18px;
        }

        /* Mobile Navigation Collapse */
        .navbar-nav {
            text-align: center;
        }

        .nav-item {
            margin: 0.25rem 0;
        }

        /* IoT Widget Mobile */
        .iot-widget {
            padding: 12px;
        }

        .widget-title {
            font-size: 1.1rem;
        }

        .power-phases h6 {
            font-size: 0.85rem;
        }

        .phase-display {
            padding: 6px;
        }

        .phase-display div:first-child {
            font-size: 0.6rem;
        }

        .phase-display div:last-child {
            font-size: 0.9rem;
        }

        /* Mobile Map Controls */
        #dashboard-map-container {
            height: 250px;
        }

        .map-legend {
            font-size: 0.7rem;
            padding: 6px;
        }

        /* Mobile Quick Stats */
        .row.mb-3 .col-md-3 {
            margin-bottom: 0.5rem;
        }

        .bg-light.rounded.p-2 {
            padding: 0.75rem !important;
        }

        /* Mobile Search */
        .search-modern {
            width: 100%;
            margin-top: 10px;
        }
    }

    /* Touch-friendly interactions for mobile */
    @media (hover: none) and (pointer: coarse) {
        .iot-widget:hover,
        .stat-card:hover,
        .pond-card:hover,
        .modern-card:hover {
            transform: none;
            box-shadow: initial;
        }

        .btn-modern:hover {
            transform: none;
        }

        /* Larger touch targets */
        .btn, .btn-modern {
            min-height: 44px;
            min-width: 44px;
        }

        .nav-link {
            min-height: 44px;
            display: flex;
            align-items: center;
        }
    }

    /* Landscape mobile orientation */
    @media (max-width: 768px) and (orientation: landscape) {
        .dashboard-header {
            padding: 10px 0;
            margin-bottom: 15px;
        }

        .dashboard-title {
            font-size: 1.6rem;
        }

        #dashboard-map-container {
            height: 280px;
        }
    }

    /* IoT Dashboard Animations */
    @keyframes pulse {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        50% {
            opacity: 0.5;
            transform: scale(1.1);
        }
        100% {
            opacity: 1;
            transform: scale(1);
        }
    }

    .live-dot {
        animation: pulse 1.5s infinite;
    }

    /* IoT Widget Hover Effects */
    .iot-widget:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .iot-stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0,0,0,0.2);
        transition: all 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="modern-dashboard" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 0;">
    <!-- Modern Navigation Bar -->
    <nav class="navbar navbar-expand-lg" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); border-bottom: 1px solid rgba(255, 255, 255, 0.2);">
        <div class="container-fluid">
            <a class="navbar-brand text-white fw-bold" href="{% url 'core:dashboard' %}">
                <i class="fas fa-water me-2"></i>
                Shrimp Farm Guardian
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white active" href="{% url 'core:dashboard' %}">
                            <i class="fas fa-tachometer-alt"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'ponds:pond_list' %}">
                            <i class="fas fa-water"></i> Ponds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'water_quality:dashboard' %}">
                            <i class="fas fa-flask"></i> Water Quality
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'feed:feed_list' %}">
                            <i class="fas fa-drumstick-bite"></i> Feed
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'medicine:medicine_dashboard' %}">
                            <i class="fas fa-pills"></i> Medicine
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'harvest:harvest_dashboard' %}">
                            <i class="fas fa-fish"></i> Harvest
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <button class="btn btn-outline-light btn-sm me-2" onclick="toggleSidebar()" title="Toggle Sidebar View">
                            <i class="fas fa-bars"></i>
                        </button>
                    </li>
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle text-white" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ user.username }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'core:command_center_dashboard' %}">Command Center</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'users:logout' %}">Logout</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link text-white" href="{% url 'users:login' %}">
                            <i class="fas fa-sign-in-alt"></i> Login
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- Modern Dashboard Header -->
    <div class="dashboard-header" style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); color: white; padding: 30px 0; margin-bottom: 30px; border-bottom: 1px solid rgba(255, 255, 255, 0.2);">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="dashboard-title" style="font-size: 2.5rem; font-weight: 700; background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 10px;">
                        <i class="fas fa-tachometer-alt me-3"></i>
                        Modern Dashboard
                    </h1>
                    <p class="dashboard-subtitle" style="font-size: 1.1rem; color: rgba(255, 255, 255, 0.8); margin-bottom: 0;">
                        Comprehensive overview of your shrimp farming operations
                        | Last updated: {{ "now"|date:"M d, Y H:i" }}
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light me-2" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i>
                        Refresh
                    </button>
                    <a href="{% url 'core:command_center_dashboard' %}" class="btn btn-light">
                        <i class="fas fa-satellite-dish"></i>
                        Command Center
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Modern Stats Grid -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); border-radius: 16px; padding: 24px; margin-bottom: 20px; box-shadow: 0 4px 20px rgba(0,0,0,0.08); transition: all 0.3s ease; border: 1px solid rgba(102, 126, 234, 0.1); position: relative; overflow: hidden;">
                    <div class="stat-icon" style="width: 60px; height: 60px; border-radius: 16px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-bottom: 16px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);">
                        <i class="fas fa-water"></i>
                    </div>
                    <div class="stat-value" style="font-size: 2.5rem; font-weight: 700; margin-bottom: 8px; background: linear-gradient(135deg, #1f2937 0%, #374151 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">{{ active_ponds_count }}</div>
                    <div class="stat-label" style="color: #6b7280; font-size: 0.9rem; font-weight: 500; margin-bottom: 8px;">Active Ponds</div>
                    {% if active_ponds_count > 0 %}
                    <div class="stat-trend positive" style="font-size: 0.8rem; font-weight: 600; display: flex; align-items: center; gap: 4px; color: #10b981;">
                        <i class="fas fa-arrow-up"></i>
                        <span>+20% from last cycle</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-fish"></i>
                    </div>
                    <div class="stat-value">{{ total_biomass|floatformat:0 }}kg</div>
                    <div class="stat-label">Total Biomass</div>
                    {% if total_biomass > 0 %}
                    <div class="stat-trend positive">
                        <i class="fas fa-arrow-up"></i>
                        <span>+15% from last cycle</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stat-value">{{ feed_used_mtd }}kg</div>
                    <div class="stat-label">Feed Used (MTD)</div>
                    <div class="stat-trend negative">
                        <i class="fas fa-arrow-down"></i>
                        <span>-5% optimized</span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-value">{{ active_alerts_count }}</div>
                    <div class="stat-label">Active Alerts</div>
                    <div class="stat-trend {% if active_alerts_count == 0 %}positive{% else %}negative{% endif %}">
                        <i class="fas fa-{% if active_alerts_count == 0 %}check{% else %}bell{% endif %}"></i>
                        <span>{% if active_alerts_count == 0 %}All clear{% else %}Needs attention{% endif %}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="row">
            <!-- Left Column -->
            <div class="col-lg-8">
                <!-- Farm & Pond Maps Integration -->
                <div class="modern-card mb-4">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <div class="widget-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            Farm & Pond Locations
                        </h3>
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="toggleMapView()" id="mapToggleBtn">
                                <i class="fas fa-expand"></i> Expand Map
                            </button>
                        </div>
                    </div>

                    <!-- Integrated Google Maps -->
                    <div id="dashboard-map-container" style="height: 400px; border-radius: 12px; overflow: hidden; margin-bottom: 20px; position: relative;">
                        <div id="dashboard-map" style="height: 100%; width: 100%;"></div>

                        <!-- Map Controls Overlay -->
                        <div style="position: absolute; top: 10px; right: 10px; z-index: 1000;">
                            <div class="btn-group-vertical" role="group">
                                <button type="button" class="btn btn-sm btn-light" onclick="centerDashboardMap()" title="Center Map">
                                    <i class="fas fa-crosshairs"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-light" onclick="toggleDashboardMapType()" title="Toggle Satellite">
                                    <i class="fas fa-satellite"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-light" onclick="refreshMapData()" title="Refresh Data">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Map Legend -->
                        <div style="position: absolute; bottom: 10px; left: 10px; background: rgba(255,255,255,0.9); padding: 8px; border-radius: 6px; font-size: 0.8rem;">
                            <div class="d-flex align-items-center gap-3">
                                <div class="d-flex align-items-center gap-1">
                                    <div style="width: 12px; height: 12px; background: #667eea; border-radius: 50%; border: 2px solid white;"></div>
                                    <span>Farms</span>
                                </div>
                                <div class="d-flex align-items-center gap-1">
                                    <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; border: 1px solid white;"></div>
                                    <span>Active Ponds</span>
                                </div>
                                <div class="d-flex align-items-center gap-1">
                                    <div style="width: 8px; height: 8px; background: #f59e0b; border-radius: 50%; border: 1px solid white;"></div>
                                    <span>Maintenance</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats Row -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="fw-bold text-primary">{{ farms.count }}</div>
                                <div class="small text-muted">Total Farms</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="fw-bold text-success">{{ ponds.count }}</div>
                                <div class="small text-muted">Total Ponds</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="fw-bold text-info">{{ active_ponds_count }}</div>
                                <div class="small text-muted">Active Ponds</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-2 bg-light rounded">
                                <div class="fw-bold text-warning">{{ ponds|length|add:"-"|add:active_ponds_count }}</div>
                                <div class="small text-muted">Inactive</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Ponds Overview -->
                <div class="modern-card">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <div class="widget-icon">
                                <i class="fas fa-water"></i>
                            </div>
                            Ponds Overview
                        </h3>
                        <div>
                            <input type="text" class="search-modern" placeholder="Search ponds..." id="pondSearch">
                        </div>
                    </div>
                    <div class="pond-grid" id="pondsContainer">
                        {% for pond in ponds %}
                        <div class="pond-card pond-search-item" data-name="{{ pond.name|lower }}">
                            <div class="pond-header">
                                <div>
                                    <h5 class="pond-name">{{ pond.name }}</h5>
                                    <p class="pond-size">{{ pond.size }} m²</p>
                                </div>
                                <div class="pond-status-icon">
                                    <i class="fas fa-water"></i>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-2">
                                    <span class="metric-label">Occupancy</span>
                                    <span class="metric-value">{{ pond.occupancy }}%</span>
                                </div>
                                <div class="progress-modern">
                                    <div class="progress-bar-modern" style="width: {{ pond.occupancy }}%;"></div>
                                </div>
                            </div>

                            <div class="pond-metrics">
                                <div class="metric-item">
                                    <div class="metric-label">Water Quality</div>
                                    <div class="metric-value">
                                        <span class="status-badge {% if pond.water_quality == 'Good' %}good{% elif pond.water_quality == 'Average' %}average{% else %}poor{% endif %}">
                                            {{ pond.water_quality }}
                                        </span>
                                    </div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-label">Feeding Status</div>
                                    <div class="metric-value">{{ pond.feeding_status }}</div>
                                </div>
                            </div>

                            <a href="{% url 'ponds:pond_detail' pond.id %}" class="btn-modern w-100">View Details</a>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="alert-modern info">
                                <div class="alert-title">No ponds available</div>
                                <div class="alert-message">
                                    <a href="{% url 'ponds:pond_create' %}" class="btn-modern">Create a pond</a> to get started.
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

            <!-- Feeding Schedule -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Today's Feeding Schedule</h5>
                        <p class="text-muted mb-0 small">Feeding tasks for all ponds</p>
                    </div>
                    <div class="bg-primary bg-opacity-10 p-2 rounded-circle">
                        <i class="fas fa-drumstick-bite text-primary"></i>
                    </div>
                </div>
                <div class="card-body">
                    {% for task in feeding_tasks %}
                    <div class="border rounded p-3 mb-2 {% if task.status == 'completed' %}bg-success bg-opacity-10 border-success{% elif task.status == 'missed' %}bg-danger bg-opacity-10 border-danger{% elif task.status == 'current' %}bg-warning bg-opacity-10 border-warning{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <p class="mb-0 fw-medium">{{ task.pond.name }}</p>
                                <div class="text-muted small">
                                    <span>{{ task.time|time:"h:i A" }}</span> •
                                    <span>{{ task.feed_type.name }}</span> •
                                    <span>{{ task.quantity_display }}</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                {% if task.status == 'completed' %}
                                <i class="fas fa-check text-success me-2"></i>
                                {% elif task.status == 'missed' %}
                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                {% elif task.status == 'current' %}
                                <i class="fas fa-clock text-warning me-2"></i>
                                {% else %}
                                <i class="fas fa-clock text-muted me-2"></i>
                                {% endif %}

                                {% if task.status == 'scheduled' or task.status == 'current' %}
                                <form method="post" action="{% url 'feed:mark_task_completed' task.id %}">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-outline-primary">Mark Done</button>
                                </form>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <p class="text-muted">No feeding tasks scheduled for today.</p>
                        <a href="{% url 'feed:create_task' %}" class="btn btn-sm btn-primary">Create Task</a>
                    </div>
                    {% endfor %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'feed:task_list' %}" class="btn btn-sm btn-outline-primary w-100">View Full Schedule</a>
                </div>
            </div>

            <!-- Medicine Inventory -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Medicine Management</h5>
                        <p class="text-muted mb-0 small">Inventory, treatments, and applications</p>
                    </div>
                    <div class="bg-primary bg-opacity-10 p-2 rounded-circle">
                        <i class="fas fa-pills text-primary"></i>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="mb-2">Low Stock Medicines</h6>
                            {% for medicine in low_stock_medicines %}
                            <div class="border rounded p-2 mb-2 {% if medicine.status == 'Out of Stock' %}bg-danger bg-opacity-10 border-danger{% else %}bg-warning bg-opacity-10 border-warning{% endif %}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-0 fw-medium">{{ medicine.name }}</p>
                                        <p class="text-muted small mb-0">{{ medicine.stock_quantity }} {{ medicine.unit }} remaining</p>
                                    </div>
                                    <span class="badge {% if medicine.status == 'Out of Stock' %}bg-danger{% else %}bg-warning text-dark{% endif %}">
                                        {{ medicine.status }}
                                    </span>
                                </div>
                            </div>
                            {% empty %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <span>All medicines are in stock</span>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-2">Active Treatment Plans</h6>
                            {% for plan in active_treatment_plans %}
                            <div class="border rounded p-2 mb-2 bg-primary bg-opacity-10 border-primary">
                                <p class="mb-0 fw-medium">{{ plan.name }}</p>
                                <p class="text-muted small mb-0">
                                    {{ plan.pond.name }} •
                                    {{ plan.completed_steps_count }}/{{ plan.total_steps_count }} steps •
                                    {{ plan.progress_percentage }}%
                                </p>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ plan.progress_percentage }}%;" aria-valuenow="{{ plan.progress_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>No active treatment plans</span>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-2">Recent Applications</h6>
                            {% for application in recent_applications %}
                            <div class="border rounded p-2 mb-2">
                                <p class="mb-0 fw-medium">{{ application.medicine.name }}</p>
                                <p class="text-muted small mb-0">
                                    {{ application.quantity_used }} {{ application.medicine.unit }} •
                                    {{ application.pond.name }} •
                                    {{ application.application_date|date:"M d" }}
                                </p>
                                {% if application.effectiveness != 'Unknown' %}
                                <span class="badge {% if application.effectiveness == 'Very Effective' %}bg-success{% elif application.effectiveness == 'Moderately Effective' %}bg-info{% elif application.effectiveness == 'Slightly Effective' %}bg-warning text-dark{% else %}bg-danger{% endif %}">
                                    {{ application.effectiveness }}
                                </span>
                                {% endif %}
                            </div>
                            {% empty %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>No recent medicine applications</span>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="col-md-6">
                            <h6 class="mb-2">Treatment Templates</h6>
                            <div class="d-flex flex-wrap">
                                <a href="{% url 'medicine:treatment_template_apply' template_id='white_spot' %}" class="btn btn-sm btn-outline-primary me-2 mb-2">White Spot</a>
                                <a href="{% url 'medicine:treatment_template_apply' template_id='vibriosis' %}" class="btn btn-sm btn-outline-primary me-2 mb-2">Vibriosis</a>
                                <a href="{% url 'medicine:treatment_template_apply' template_id='black_gill' %}" class="btn btn-sm btn-outline-primary me-2 mb-2">Black Gill</a>
                                <a href="{% url 'medicine:treatment_template_apply' template_id='hepatopancreatic_necrosis' %}" class="btn btn-sm btn-outline-primary me-2 mb-2">AHPND</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="row">
                        <div class="col-md-6 mb-2 mb-md-0">
                            <a href="{% url 'medicine:medicine_dashboard' %}" class="btn btn-primary btn-sm w-100">
                                <i class="fas fa-chart-line me-1"></i> Medicine Dashboard
                            </a>
                        </div>
                        <div class="col-md-6">
                            <a href="{% url 'medicine:medicine_list' %}" class="btn btn-outline-primary btn-sm w-100">
                                <i class="fas fa-list me-1"></i> Medicine Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Harvests -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Upcoming Harvests</h5>
                        <p class="text-muted mb-0 small">Next 30 days</p>
                    </div>
                    <div class="bg-primary bg-opacity-10 p-2 rounded-circle">
                        <i class="fas fa-calendar-alt text-primary"></i>
                    </div>
                </div>
                <div class="card-body">
                    {% for plan in upcoming_harvests %}
                    <div class="border rounded p-3 mb-2 {% if plan.days_until_harvest <= 7 %}bg-warning bg-opacity-10 border-warning{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <p class="mb-0 fw-medium">{{ plan.pond.name }}</p>
                                <div class="text-muted small">
                                    <span>{{ plan.planned_date|date:"M d, Y" }}</span> •
                                    <span>{{ plan.estimated_biomass|floatformat:0 }} kg</span> •
                                    <span>{{ plan.estimated_size|floatformat:1 }}g ({{ plan.estimated_count }} pcs/kg)</span>
                                </div>
                            </div>
                            <div>
                                {% if plan.days_until_harvest <= 7 %}
                                <span class="badge bg-danger">{{ plan.days_until_harvest }} days</span>
                                {% else %}
                                <span class="badge bg-info">{{ plan.days_until_harvest }} days</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <p class="text-muted">No harvests planned for the next 30 days.</p>
                        <a href="{% url 'harvest:plan_create' %}" class="btn btn-sm btn-primary">Create Harvest Plan</a>
                    </div>
                    {% endfor %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'harvest:harvest_planning' %}" class="btn btn-sm btn-outline-primary w-100">View Harvest Planning</a>
                </div>
            </div>
        </div>

            <!-- Right Column -->
            <div class="col-lg-4">
                <!-- Alerts -->
                <div class="modern-card">
                    <div class="widget-header">
                        <h3 class="widget-title">
                            <div class="widget-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            System Alerts
                        </h3>
                        <span class="status-badge {% if alerts %}{% if alerts|filter_type:'critical'|length > 0 %}poor{% elif alerts|filter_type:'warning'|length > 0 %}average{% else %}good{% endif %}{% else %}good{% endif %}">
                            {% if alerts %}
                            {{ alerts|filter_type:'critical'|length }} critical, {{ alerts|filter_type:'warning'|length }} warning
                            {% else %}
                            All clear
                            {% endif %}
                        </span>
                    </div>
                    <div style="max-height: 400px; overflow-y: auto;">
                        {% for alert in alerts %}
                        <div class="alert-modern {% if alert.type == 'critical' %}critical{% elif alert.type == 'warning' %}warning{% else %}info{% endif %}">
                            <div class="d-flex">
                                <div class="me-3">
                                    {% if alert.type == 'critical' %}
                                    <i class="fas fa-exclamation-circle text-danger"></i>
                                    {% elif alert.type == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                    {% else %}
                                    <i class="fas fa-info-circle text-info"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <div class="alert-title">{{ alert.title }}</div>
                                    <div class="alert-message">{{ alert.message }}</div>
                                    <div class="alert-meta">{{ alert.timestamp }}</div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-4">
                            <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted">No alerts at this time</p>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'alerts:alert_list' %}" class="btn-modern w-100">View All Alerts</a>
                    </div>
                </div>

            <!-- Lunar Calendar -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Lunar Calendar & Treatments</h5>
                        <p class="text-muted mb-0 small">Important lunar days for shrimp farming</p>
                    </div>
                    <div class="bg-primary bg-opacity-10 p-2 rounded-circle">
                        <i class="fas fa-moon text-primary"></i>
                    </div>
                </div>
                <div class="card-body">
                    {% for day in lunar_days %}
                    <div class="d-flex justify-content-between align-items-center border-bottom pb-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="{% if day.phase == 'Astami' or day.phase == 'Amavasya' or day.phase == 'Pournami' or day.phase == 'Pournami' or day.phase == 'Pournami' %}bg-success{% elif day.phase == 'Navami' %}bg-warning{% else %}bg-purple{% endif %} bg-opacity-10 rounded-circle p-2 me-3">
                                <span class="{% if day.phase == 'Astami' or day.phase == 'Amavasya' or day.phase == 'Pournami' or day.phase == 'Pournami' %}text-success{% elif day.phase == 'Navami' or day.phase == 'Pournami' %}text-warning{% else %}text-purple{% endif %}">{{ day.phase|first }}</span>
                            </div>
                            <div>
                                <p class="mb-0 fw-medium">{{ day.phase }}</p>
                                <p class="text-muted small mb-0">{{ day.formatted_date }}</p>
                                {% if day.phase == 'Astami' or day.phase == 'Amavasya' or day.phase == 'Pournami' or day.phase == 'Pournami' or day.phase == 'Pournami' %}
                                <span class="badge bg-success">Probiotics</span>
                                {% elif day.phase == 'Navami' %}
                                <span class="badge bg-warning text-dark">Minerals</span>
                                {% endif %}
                            </div>
                        </div>
                        <div>
                            {% if day.days_away == 0 %}
                            <span class="badge bg-danger">Today</span>
                            {% elif day.days_away == 1 %}
                            <span class="badge bg-warning">Tomorrow</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ day.days_away }} days</span>
                            {% endif %}
                            <a href="{% url 'lunar:lunar_day_detail' pk=day.id %}" class="btn btn-sm btn-outline-primary ms-2">
                                <i class="fas fa-tasks"></i>
                            </a>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <p class="text-muted">No upcoming lunar days found.</p>
                    </div>
                    {% endfor %}

                    <h6 class="mt-4 mb-3">Pending Treatments</h6>
                    {% for treatment in pending_treatments %}
                    <div class="d-flex justify-content-between align-items-center border-bottom pb-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="{% if treatment.treatment_type == 'PROBIOTIC' %}bg-success{% elif treatment.treatment_type == 'MINERAL' %}bg-warning{% else %}bg-info{% endif %} bg-opacity-10 rounded-circle p-2 me-3">
                                <i class="fas {% if treatment.treatment_type == 'PROBIOTIC' %}fa-flask{% elif treatment.treatment_type == 'MINERAL' %}fa-vial{% else %}fa-prescription-bottle{% endif %} {% if treatment.treatment_type == 'PROBIOTIC' %}text-success{% elif treatment.treatment_type == 'MINERAL' %}text-warning{% else %}text-info{% endif %}"></i>
                            </div>
                            <div>
                                <p class="mb-0 fw-medium">{{ treatment.get_treatment_type_display }}</p>
                                <p class="text-muted small mb-0">{{ treatment.pond.name }} - {{ treatment.quantity }} {{ treatment.unit }}</p>
                                <p class="text-muted small mb-0">{{ treatment.lunar_day.phase }} ({{ treatment.lunar_day.date|date:"M d" }})</p>
                            </div>
                        </div>
                        <div>
                            <a href="{% url 'lunar:treatment_apply' pk=treatment.id %}" class="btn btn-sm btn-success">
                                <i class="fas fa-check me-1"></i> Apply
                            </a>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No pending treatments.
                    </div>
                    {% endfor %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'lunar:lunar_calendar' %}" class="btn btn-sm btn-outline-primary w-100">
                        <i class="fas fa-calendar me-1"></i> View Lunar Calendar
                    </a>
                </div>
            </div>

            <!-- Weather Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">Weather Conditions</h5>
                        <p class="text-muted mb-0 small">Current and forecasted weather</p>
                    </div>
                    <div class="bg-primary bg-opacity-10 p-2 rounded-circle">
                        <i class="fas fa-cloud-sun-rain text-primary"></i>
                    </div>
                </div>
                <div class="card-body">
                    {% if weather_data %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h3 class="mb-0">{{ weather_data.temperature|floatformat:1 }}°C</h3>
                            <p class="text-muted small mb-0">{{ weather_data.station.location }}</p>
                        </div>
                        <div class="d-flex align-items-center">
                            {% if 'sunny' in weather_data.condition|lower or 'clear' in weather_data.condition|lower %}
                            <i class="fas fa-sun text-warning me-1"></i>
                            {% elif 'cloud' in weather_data.condition|lower %}
                            <i class="fas fa-cloud text-secondary me-1"></i>
                            {% elif 'rain' in weather_data.condition|lower or 'shower' in weather_data.condition|lower %}
                            <i class="fas fa-cloud-rain text-primary me-1"></i>
                            {% elif 'storm' in weather_data.condition|lower or 'thunder' in weather_data.condition|lower %}
                            <i class="fas fa-bolt text-warning me-1"></i>
                            {% else %}
                            <i class="fas fa-cloud-sun text-primary me-1"></i>
                            {% endif %}
                            <span>{{ weather_data.condition }}</span>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-4">
                            <div class="bg-light rounded p-2 text-center">
                                <i class="fas fa-tint text-primary mb-1"></i>
                                <p class="text-muted small mb-0">Humidity</p>
                                <p class="mb-0 small fw-medium">{{ weather_data.humidity|floatformat:0 }}%</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light rounded p-2 text-center">
                                <i class="fas fa-wind text-primary mb-1"></i>
                                <p class="text-muted small mb-0">Wind</p>
                                <p class="mb-0 small fw-medium">{{ weather_data.wind_speed|floatformat:1 }} km/h</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light rounded p-2 text-center">
                                <i class="fas fa-cloud-rain text-primary mb-1"></i>
                                <p class="text-muted small mb-0">Rain</p>
                                <p class="mb-0 small fw-medium">{{ weather_data.precipitation|floatformat:1 }} mm</p>
                            </div>
                        </div>
                    </div>

                    {% if weather_alerts %}
                    <h6 class="mb-2">Active Weather Alerts</h6>
                    {% for alert in weather_alerts %}
                    <div class="alert {% if alert.severity == 'High' or alert.severity == 'Extreme' %}alert-danger{% elif alert.severity == 'Medium' %}alert-warning{% else %}alert-info{% endif %} mb-2 py-2">
                        <div class="d-flex">
                            <div class="me-2">
                                {% if alert.severity == 'High' or alert.severity == 'Extreme' %}
                                <i class="fas fa-exclamation-circle text-danger"></i>
                                {% elif alert.severity == 'Medium' %}
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% else %}
                                <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div>
                                <p class="mb-0 small fw-medium">{{ alert.title }}</p>
                                <p class="mb-0 small">{{ alert.alert_type }} - {{ alert.severity }}</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">Weather data not available.</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:weather_dashboard' %}" class="btn btn-sm btn-outline-primary w-100">View Weather Details</a>
                </div>
            </div>

            <!-- Quick Links -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <a href="{% url 'medicine:medicine_list' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                <i class="fas fa-pills mb-2" style="font-size: 1.5rem;"></i>
                                <span>Medicines</span>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{% url 'harvest:harvest_planning' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                <i class="fas fa-calendar-alt mb-2" style="font-size: 1.5rem;"></i>
                                <span>Harvests</span>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{% url 'weather:weather_dashboard' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                <i class="fas fa-cloud-sun-rain mb-2" style="font-size: 1.5rem;"></i>
                                <span>Weather</span>
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="{% url 'feed:task_list' %}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                                <i class="fas fa-drumstick-bite mb-2" style="font-size: 1.5rem;"></i>
                                <span>Feeding</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- IoT Dashboard Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="modern-card">
                    <div class="widget-header d-flex justify-content-between align-items-center mb-4">
                        <h3 class="widget-title mb-0">
                            <div class="widget-icon me-2" style="width: 40px; height: 40px; border-radius: 12px; display: inline-flex; align-items: center; justify-content: center; font-size: 18px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                <i class="fas fa-satellite-dish"></i>
                            </div>
                            IoT Monitoring & Control Center
                        </h3>
                        <div class="d-flex gap-2">
                            <span class="badge bg-success d-flex align-items-center gap-1">
                                <div class="live-dot" style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; animation: pulse 1.5s infinite;"></div>
                                LIVE
                            </span>
                            <a href="{% url 'realtime_monitoring' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-external-link-alt"></i> Full Dashboard
                            </a>
                        </div>
                    </div>

                    <!-- IoT Stats Row -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="iot-stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 12px; padding: 20px; text-align: center;">
                                <div class="iot-icon mb-2" style="font-size: 2rem;">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="iot-value" style="font-size: 1.8rem; font-weight: 700;">24</div>
                                <div class="iot-label" style="font-size: 0.9rem; opacity: 0.9;">IoT Devices</div>
                                <div class="iot-status" style="font-size: 0.8rem; margin-top: 8px;">
                                    <span class="badge bg-light text-dark">22 Online</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="iot-stat-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border-radius: 12px; padding: 20px; text-align: center;">
                                <div class="iot-icon mb-2" style="font-size: 2rem;">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="iot-value" style="font-size: 1.8rem; font-weight: 700;">12.5kW</div>
                                <div class="iot-label" style="font-size: 0.9rem; opacity: 0.9;">Power Usage</div>
                                <div class="iot-status" style="font-size: 0.8rem; margin-top: 8px;">
                                    <span class="badge bg-light text-dark">Normal</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="iot-stat-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; border-radius: 12px; padding: 20px; text-align: center;">
                                <div class="iot-icon mb-2" style="font-size: 2rem;">
                                    <i class="fas fa-thermometer-half"></i>
                                </div>
                                <div class="iot-value" style="font-size: 1.8rem; font-weight: 700;">28.5°C</div>
                                <div class="iot-label" style="font-size: 0.9rem; opacity: 0.9;">Avg Temperature</div>
                                <div class="iot-status" style="font-size: 0.8rem; margin-top: 8px;">
                                    <span class="badge bg-light text-dark">Optimal</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="iot-stat-card" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border-radius: 12px; padding: 20px; text-align: center;">
                                <div class="iot-icon mb-2" style="font-size: 2rem;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="iot-value" style="font-size: 1.8rem; font-weight: 700;">3</div>
                                <div class="iot-label" style="font-size: 0.9rem; opacity: 0.9;">Active Alerts</div>
                                <div class="iot-status" style="font-size: 0.8rem; margin-top: 8px;">
                                    <span class="badge bg-light text-dark">Attention</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- IoT Dashboard Grid -->
                    <div class="row">
                        <!-- Real-time Monitoring -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="iot-widget" style="background: rgba(102, 126, 234, 0.1); border-radius: 12px; padding: 20px; border: 1px solid rgba(102, 126, 234, 0.2);">
                                <div class="widget-header d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0" style="color: #374151;">
                                        <i class="fas fa-chart-line me-2" style="color: #667eea;"></i>
                                        Real-time Data
                                    </h5>
                                    <a href="{% url 'realtime_monitoring' %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                                <div class="sensor-readings">
                                    <div class="reading-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Water Quality:</span>
                                        <span class="badge bg-success">Excellent</span>
                                    </div>
                                    <div class="reading-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">pH Level:</span>
                                        <span style="font-weight: 600; color: #374151;">7.8</span>
                                    </div>
                                    <div class="reading-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Dissolved O2:</span>
                                        <span style="font-weight: 600; color: #374151;">6.5 mg/L</span>
                                    </div>
                                    <div class="reading-item d-flex justify-content-between align-items-center">
                                        <span style="color: #6b7280;">Salinity:</span>
                                        <span style="font-weight: 600; color: #374151;">15 ppt</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Electricity Monitoring -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="iot-widget" style="background: rgba(16, 185, 129, 0.1); border-radius: 12px; padding: 20px; border: 1px solid rgba(16, 185, 129, 0.2);">
                                <div class="widget-header d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0" style="color: #374151;">
                                        <i class="fas fa-plug me-2" style="color: #10b981;"></i>
                                        3-Phase Power Monitor
                                    </h5>
                                    <a href="{% url 'electricity:dashboard' %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                                
                                <!-- 3-Phase Voltage Display -->
                                <div class="power-phases mb-3">
                                    <h6 style="color: #374151; font-size: 0.9rem; margin-bottom: 12px;">Voltage (V)</h6>
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(239, 68, 68, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L1</div>
                                                <div style="font-weight: 600; color: #374151;" id="voltage-l1-dash">230</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L2</div>
                                                <div style="font-weight: 600; color: #374151;" id="voltage-l2-dash">228</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(59, 130, 246, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L3</div>
                                                <div style="font-weight: 600; color: #374151;" id="voltage-l3-dash">232</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 3-Phase Current Display -->
                                <div class="power-phases mb-3">
                                    <h6 style="color: #374151; font-size: 0.9rem; margin-bottom: 12px;">Current (A)</h6>
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(239, 68, 68, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L1</div>
                                                <div style="font-weight: 600; color: #374151;" id="current-l1-dash">18.2</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(245, 158, 11, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L2</div>
                                                <div style="font-weight: 600; color: #374151;" id="current-l2-dash">17.8</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="phase-display text-center" style="background: rgba(59, 130, 246, 0.1); border-radius: 8px; padding: 10px;">
                                                <div style="font-size: 0.7rem; color: #6b7280; margin-bottom: 4px;">L3</div>
                                                <div style="font-weight: 600; color: #374151;" id="current-l3-dash">19.1</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Power Summary -->
                                <div class="power-summary">
                                    <div class="reading-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Total Power:</span>
                                        <span style="font-weight: 600; color: #374151;" id="total-power-dash">12.5 kW</span>
                                    </div>
                                    <div class="reading-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Power Factor:</span>
                                        <span style="font-weight: 600; color: #374151;" id="power-factor-dash">0.95</span>
                                    </div>
                                    <div class="reading-item d-flex justify-content-between align-items-center">
                                        <span style="color: #6b7280;">Daily Cost:</span>
                                        <span style="font-weight: 600; color: #374151;" id="daily-cost-dash">$24.50</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Device Status -->
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="iot-widget" style="background: rgba(245, 158, 11, 0.1); border-radius: 12px; padding: 20px; border: 1px solid rgba(245, 158, 11, 0.2);">
                                <div class="widget-header d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="mb-0" style="color: #374151;">
                                        <i class="fas fa-cogs me-2" style="color: #f59e0b;"></i>
                                        Device Status
                                    </h5>
                                    <a href="{% url 'iot_devices' %}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                                <div class="device-status">
                                    <div class="device-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Aerators:</span>
                                        <span class="badge bg-success">8/8 Online</span>
                                    </div>
                                    <div class="device-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Water Pumps:</span>
                                        <span class="badge bg-success">4/4 Online</span>
                                    </div>
                                    <div class="device-item d-flex justify-content-between align-items-center mb-2">
                                        <span style="color: #6b7280;">Sensors:</span>
                                        <span class="badge bg-warning text-dark">10/12 Online</span>
                                    </div>
                                    <div class="device-item d-flex justify-content-between align-items-center">
                                        <span style="color: #6b7280;">Feeders:</span>
                                        <span class="badge bg-success">6/6 Online</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions Row -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="iot-actions d-flex flex-wrap gap-2">
                                <a href="{% url 'realtime_monitoring' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-satellite-dish me-1"></i> Real-time Dashboard
                                </a>
                                <a href="{% url 'electricity:dashboard' %}" class="btn btn-success btn-sm">
                                    <i class="fas fa-bolt me-1"></i> Electricity Monitor
                                </a>
                                <a href="{% url 'iot_devices' %}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-microchip me-1"></i> Device Manager
                                </a>
                                <a href="{% url 'computer_vision' %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye me-1"></i> Computer Vision
                                </a>
                                <a href="{% url 'alerts:alert_list' %}" class="btn btn-danger btn-sm">
                                    <i class="fas fa-bell me-1"></i> Alert Center
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // Modern Dashboard JavaScript functionality

    // Pond search functionality
    document.getElementById('pondSearch').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const pondCards = document.querySelectorAll('.pond-search-item');

        pondCards.forEach(card => {
            const pondName = card.dataset.name;
            if (pondName.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    });

    // Dashboard refresh functionality
    function refreshDashboard() {
        // Show loading state
        const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
        const originalText = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Simulate refresh (in real app, this would make an AJAX call)
        setTimeout(() => {
            location.reload();
        }, 1000);
    }

    // Toggle sidebar functionality
    function toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');

        if (sidebar.style.display === 'none' || sidebar.style.display === '') {
            // Show sidebar (traditional view)
            sidebar.style.display = 'block';
            mainContent.style.marginLeft = '250px';
            mainContent.style.padding = '2rem';
            document.querySelector('.modern-dashboard').style.display = 'none';

            // Redirect to traditional dashboard
            window.location.href = '{% url "core:dashboard" %}';
        }
    }

   

    // Add smooth animations to cards
    document.addEventListener('DOMContentLoaded', function() {
        // Animate cards on load
        const cards = document.querySelectorAll('.modern-card, .stat-card, .pond-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Add hover effects to buttons
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Auto-refresh dashboard every 5 minutes
        setInterval(() => {
            // In a real app, this would update data via AJAX
            console.log('Auto-refreshing dashboard data...');
        }, 300000); // 5 minutes

        // Add real-time clock
        updateClock();
        setInterval(updateClock, 1000);
    });

    // Real-time clock function
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleTimeString();
        const dateString = now.toLocaleDateString();

        // Update any clock elements if they exist
        const clockElements = document.querySelectorAll('.dashboard-clock');
        clockElements.forEach(element => {
            element.textContent = `${dateString} ${timeString}`;
        });
    }

    // Add notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshDashboard();
        }

        // Ctrl/Cmd + F for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('pondSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }
    });

    // Add progressive loading for better performance
    function loadDashboardData() {
        // This would load data progressively in a real app
        console.log('Loading dashboard data...');
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Modern Dashboard initialized successfully!');
        showNotification('Dashboard loaded successfully!', 'success');
    });
</script>
{% endblock %}
{% endblock %}
