<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pond Management - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
<style>
    .pond-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .pond-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 6s infinite;
    }
    
    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .view-toggle {
        display: flex;
        gap: 10px;
        margin-bottom: 30px;
        justify-content: center;
    }
    
    .toggle-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        color: white;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }
    
    .toggle-btn:hover,
    .toggle-btn.active {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }
    

    
    .pond-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .pond-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
        position: relative;
        overflow: hidden;
    }
    
    .pond-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }
    
    .pond-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2);
    }
    
    .pond-header-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .pond-name {
        font-size: 1.4em;
        font-weight: bold;
        color: #2d3436;
        margin: 0;
    }
    
    .pond-status {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8em;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .status-active {
        background: linear-gradient(45deg, #00b894, #00cec9);
        color: white;
    }
    
    .status-maintenance {
        background: linear-gradient(45deg, #fdcb6e, #e17055);
        color: white;
    }
    
    .status-empty {
        background: linear-gradient(45deg, #636e72, #2d3436);
        color: white;
    }
    
    .pond-details {
        margin: 20px 0;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .detail-row:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        color: #636e72;
        font-weight: 500;
    }
    
    .detail-value {
        color: #2d3436;
        font-weight: bold;
    }
    
    .weather-info {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        border-radius: 15px;
        padding: 15px;
        margin: 15px 0;
        text-align: center;
    }
    
    .weather-temp {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .weather-condition {
        font-size: 0.9em;
        opacity: 0.9;
    }
    
    .pond-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
    
    .action-btn {
        flex: 1;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 15px;
        padding: 10px 15px;
        color: white;
        font-weight: bold;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        font-size: 0.9em;
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .action-btn.secondary {
        background: linear-gradient(45deg, #74b9ff, #0984e3);
    }
    
    .action-btn.secondary:hover {
        box-shadow: 0 8px 20px rgba(116, 185, 255, 0.4);
    }
    
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .stat-card {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stat-label {
        font-size: 0.9em;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    /* Theme toggle styling for purple header */
    .pond-header .theme-toggle-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .pond-header .theme-toggle {
        width: 60px;
        height: 30px;
        background: rgba(255,255,255,0.2);
        border-radius: 15px;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .pond-header .theme-toggle-slider {
        width: 26px;
        height: 26px;
        background: white;
        border-radius: 50%;
        position: absolute;
        top: 2px;
        left: 2px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #667eea;
    }

    .pond-header .theme-label {
        font-size: 18px;
        opacity: 0.7;
        transition: all 0.3s ease;
    }

    .pond-header .theme-label.active {
        opacity: 1;
        transform: scale(1.1);
    }

    .pond-header .header-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    /* Enhanced GPS Map Styles */
    .gps-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .gps-stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 30px 25px;
        text-align: center;
        color: white;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .gps-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }

    .gps-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.3);
    }

    .gps-stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        opacity: 0.9;
    }

    .gps-stat-value {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .gps-stat-label {
        font-size: 1.1rem;
        font-weight: 500;
        opacity: 0.9;
    }

    /* GPS Control Panel */
    .gps-control-panel {
        background: rgba(45, 55, 72, 0.95);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.1);
    }

    .gps-panel-title {
        color: #a0aec0;
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .gps-status-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 25px;
    }

    .gps-status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #e2e8f0;
        font-size: 0.9rem;
    }

    .gps-status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
    }

    .gps-status-working { background-color: #ef4444; }
    .gps-status-available { background-color: #10b981; }
    .gps-status-break { background-color: #f59e0b; }
    .gps-status-traveling { background-color: #8b5cf6; }
    .gps-status-offline { background-color: #6b7280; }

    /* GPS Layer Controls */
    .gps-layer-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 25px;
    }

    .gps-layer-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-layer-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .gps-layer-btn.active {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }

    .gps-layer-btn.gps-ponds { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }
    .gps-layer-btn.gps-aerators.active {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
    }
    .gps-layer-btn.gps-workers { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); }
    .gps-layer-btn.gps-geofences { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }

    /* GPS Map Controls */
    .gps-map-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .gps-map-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-map-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .gps-map-btn.gps-refresh { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); }
    .gps-map-btn.gps-fit { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }

    /* GPS Map Container */
    .gps-map-container {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0,0,0,0.2);
        position: relative;
        margin-bottom: 30px;
    }



    #map {
        width: 100%;
        height: 500px;
    }

    .gps-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #666;
        height: 500px;
    }

    .gps-loading::before {
        content: "🗺️ ";
        animation: spin 2s linear infinite;
        margin-right: 10px;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Weather Overlay Styles - Top Corner Position */
    .weather-overlay {
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        min-width: 280px;
        max-width: 320px;
        z-index: 1000;
        animation: weatherSlideIn 0.6s ease-out;
        color: rgba(255, 255, 255, 0.95);
        text-shadow: 0 1px 3px rgba(0,0,0,0.5);
        cursor: pointer;
    }

    .weather-overlay.dark {
        background: rgba(0, 0, 0, 0.2);
        color: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255,255,255,0.15);
    }

    .weather-overlay:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: scale(1.02);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
    }

    .weather-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 10px;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .weather-icon {
        font-size: 2rem;
        animation: weatherPulse 2s ease-in-out infinite;
    }

    .weather-temp {
        font-size: 2.5rem;
        font-weight: 700;
        color: #667eea;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        animation: tempGlow 3s ease-in-out infinite;
    }

    .weather-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 10px;
    }

    .weather-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .weather-item i {
        width: 16px;
        text-align: center;
    }

    .weather-numbers {
        position: absolute;
        color: rgba(102, 126, 234, 0.7);
        font-size: 1.2rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        pointer-events: none;
        z-index: 999;
        animation: numberFloat 4s ease-in-out infinite;
    }

    .weather-animation {
        position: absolute;
        pointer-events: none;
        z-index: 998;
    }

    .rain-animation {
        width: 100px;
        height: 100px;
        background: linear-gradient(180deg, transparent 0%, rgba(59, 130, 246, 0.3) 100%);
        border-radius: 50%;
        animation: rainPulse 2s ease-in-out infinite;
    }

    .sun-animation {
        width: 80px;
        height: 80px;
        background: radial-gradient(circle, rgba(251, 191, 36, 0.4) 0%, transparent 70%);
        border-radius: 50%;
        animation: sunGlow 3s ease-in-out infinite;
    }

    .cloud-animation {
        width: 120px;
        height: 60px;
        background: rgba(156, 163, 175, 0.4);
        border-radius: 30px;
        animation: cloudFloat 5s ease-in-out infinite;
    }

    @keyframes weatherSlideIn {
        from {
            opacity: 0;
            transform: translateX(100%) translateY(-20px) scale(0.9);
            backdrop-filter: blur(0px);
        }
        to {
            opacity: 1;
            transform: translateX(0) translateY(0) scale(1);
            backdrop-filter: blur(20px);
        }
    }

    @keyframes weatherPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }

    @keyframes tempGlow {
        0%, 100% { text-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        50% { text-shadow: 0 4px 20px rgba(102, 126, 234, 0.4); }
    }

    @keyframes numberFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-5px); }
    }

    @keyframes rainPulse {
        0%, 100% { opacity: 0.3; transform: scale(1); }
        50% { opacity: 0.6; transform: scale(1.1); }
    }

    @keyframes sunGlow {
        0%, 100% { opacity: 0.4; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.2); }
    }

    @keyframes cloudFloat {
        0%, 100% { transform: translateX(0px); }
        50% { transform: translateX(10px); }
    }

    /* Google Maps Info Window Overrides - Small and Transparent */
    .gm-style .gm-style-iw {
        background: transparent !important;
        box-shadow: none !important;
        border: none !important;
        border-radius: 0 !important;
        max-width: 250px !important;
    }

    .gm-style .gm-style-iw-c {
        padding: 0 !important;
        border-radius: 0 !important;
        background: transparent !important;
        box-shadow: none !important;
        max-width: 250px !important;
    }

    .gm-style .gm-style-iw-d {
        overflow: hidden !important;
        max-height: none !important;
        padding: 0 !important;
    }

    .gm-style .gm-style-iw-t::after {
        display: none !important;
    }

    /* Hide the close button background */
    .gm-style .gm-style-iw button {
        background: rgba(255, 255, 255, 0.2) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
        border-radius: 50% !important;
        backdrop-filter: blur(10px) !important;
    }

    /* Weather Control Panel */
    .weather-controls {
        background: rgba(45, 55, 72, 0.95);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        backdrop-filter: blur(10px);
    }

    .weather-toggle {
        display: flex;
        align-items: center;
        gap: 10px;
        color: #e2e8f0;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .weather-toggle:hover {
        background: rgba(255,255,255,0.1);
    }

    .weather-toggle.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    /* Integrated Weather Section Styles */
    .integrated-weather-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 15px;
        margin: 15px 0;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .weather-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;
        font-weight: 600;
        color: #2d3436;
    }

    .weather-icon {
        font-size: 1.2rem;
    }

    .weather-title {
        font-size: 0.95rem;
        color: #636e72;
    }

    .weather-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .weather-item {
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px;
        background: rgba(255, 255, 255, 0.5);
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .weather-item:hover {
        background: rgba(255, 255, 255, 0.7);
        transform: translateY(-2px);
    }

    .weather-icon-small {
        font-size: 1rem;
        width: 20px;
        text-align: center;
    }

    .weather-value {
        font-size: 0.85rem;
        font-weight: 600;
        color: #2d3436;
    }

    /* Pond Header Actions */
    .pond-header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .location-zoom-btn, .pond-detail-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        color: white;
        padding: 6px 8px;
        font-size: 0.8rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 28px;
    }

    .location-zoom-btn:hover, .pond-detail-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .location-zoom-btn {
        background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    }

    .location-zoom-btn:hover {
        box-shadow: 0 4px 12px rgba(225, 112, 85, 0.4);
    }

    /* Notification Animations */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    </style>
</head>
<body>
<div class="container-fluid">
    <!-- Pond Management Header -->
    <div class="pond-header">
        <div class="position-relative d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="fas fa-fish me-3"></i>Pond Management</h1>
                <p class="lead mb-0">Comprehensive pond monitoring and management system</p>
                <div class="mt-3">
                    <span class="badge bg-light text-dark me-2">🐟 {{ ponds.count }} PONDS</span>
                    <span class="badge bg-light text-dark me-2">🏢 {{ farms.count }} FARMS</span>
                    <span class="badge bg-success text-white me-2">🌤️ WEATHER INTEGRATED</span>
                    <span class="badge bg-info text-white">🗺️ MAPPING AVAILABLE</span>
                </div>
            </div>
            <div class="header-controls d-flex align-items-center gap-3">
                {% if user.is_authenticated %}
                    <!-- Notification Center -->
                    {% include 'components/notification_center.html' %}
                {% endif %}
                <div class="theme-toggle-wrapper">
                    <span class="theme-label" id="lightLabel">☀️</span>
                    <div class="theme-toggle" id="themeToggle">
                        <div class="theme-toggle-slider">
                            <i class="fas fa-moon"></i>
                        </div>
                    </div>
                    <span class="theme-label active" id="darkLabel">🌙</span>
                </div>
            </div>
        </div>
    </div>

    <!-- View Toggle and Filters -->
    <div class="view-toggle">
        <a href="{% url 'ponds:pond_list' %}" class="toggle-btn active">
            <i class="fas fa-list"></i>List View
        </a>
        <a href="{% url 'ponds:cumulative_map_dashboard' %}" class="toggle-btn">
            <i class="fas fa-globe-americas"></i>Cumulative Map
        </a>
        <a href="{% url 'weather:unified_weather_map' %}" class="toggle-btn secondary">
            <i class="fas fa-cloud"></i>Weather Map
        </a>

        <!-- Farm Filter Dropdown -->
        {% if farms %}
        <div class="dropdown" style="display: inline-block;">
            <button class="toggle-btn dropdown-toggle" type="button" id="farmFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                <i class="fas fa-building"></i>
                {% if farm_filter %}
                    {{ farm_filter.name|truncatechars:15 }}
                {% else %}
                    Filter by Farm
                {% endif %}
            </button>
            <ul class="dropdown-menu" aria-labelledby="farmFilterDropdown">
                <li><a class="dropdown-item" href="{% url 'ponds:pond_list' %}">
                    <i class="fas fa-list me-2"></i>All Ponds
                </a></li>
                <li><hr class="dropdown-divider"></li>
                {% for farm in farms %}
                <li><a class="dropdown-item" href="{% url 'ponds:pond_list' %}?farm={{ farm.id }}">
                    <i class="fas fa-building me-2"></i>{{ farm.name }}
                    <span class="badge bg-primary ms-2">{{ farm.total_ponds|default:0 }}</span>
                </a></li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}

        <div class="dropdown" style="display: inline-block;">
            <button class="toggle-btn primary dropdown-toggle" type="button" id="createDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-plus-circle"></i>Create with Map
            </button>
            <ul class="dropdown-menu" aria-labelledby="createDropdown">
                <li><a class="dropdown-item" href="{% url 'ponds:pond_create_with_map' %}">
                    <i class="fas fa-fish me-2"></i>Create Pond with Map
                </a></li>
                <li><a class="dropdown-item" href="{% url 'ponds:farm_create_with_map' %}">
                    <i class="fas fa-home me-2"></i>Create Farm with Map
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="{% url 'ponds:pond_create' %}">
                    <i class="fas fa-list me-2"></i>Create without Map
                </a></li>
            </ul>
        </div>
    </div>

    <!-- Enhanced GPS Stats Cards -->
    <div class="gps-stats-grid">
        <div class="gps-stat-card">
            <div class="gps-stat-icon">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="gps-stat-value" id="farms-count">{{ farms.count }}</div>
            <div class="gps-stat-label">Active Farms</div>
        </div>

        <div class="gps-stat-card">
            <div class="gps-stat-icon">
                <i class="fas fa-water"></i>
            </div>
            <div class="gps-stat-value" id="ponds-count">{{ ponds.count }}</div>
            <div class="gps-stat-label">Monitored Ponds</div>
        </div>

        <div class="gps-stat-card">
            <div class="gps-stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="gps-stat-value" id="workers-count">0</div>
            <div class="gps-stat-label">GPS Workers</div>
        </div>

        <div class="gps-stat-card">
            <div class="gps-stat-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="gps-stat-value" id="security-zones">0</div>
            <div class="gps-stat-label">Security Zones</div>
        </div>
    </div>

    <!-- Enhanced GPS Control Panel -->
    <div class="gps-control-panel">
        <div class="gps-panel-title">
            <i class="fas fa-circle"></i>
            WORKER STATUS LEGEND
        </div>
        <div class="gps-status-legend">
            <div class="gps-status-item">
                <div class="gps-status-dot gps-status-working"></div>
                <span>Working</span>
            </div>
            <div class="gps-status-item">
                <div class="gps-status-dot gps-status-available"></div>
                <span>Available</span>
            </div>
            <div class="gps-status-item">
                <div class="gps-status-dot gps-status-break"></div>
                <span>On Break</span>
            </div>
            <div class="gps-status-item">
                <div class="gps-status-dot gps-status-traveling"></div>
                <span>Traveling</span>
            </div>
            <div class="gps-status-item">
                <div class="gps-status-dot gps-status-offline"></div>
                <span>Offline</span>
            </div>
        </div>

        <!-- Layer Controls -->
        <div class="gps-panel-title">
            <i class="fas fa-layer-group"></i>
            LAYER CONTROLS
        </div>
        <div class="gps-layer-controls">
            <button class="gps-layer-btn active" id="farms-layer">
                <i class="fas fa-warehouse"></i>
                FARMS
            </button>
            <button class="gps-layer-btn gps-ponds active" id="ponds-layer">
                <i class="fas fa-water"></i>
                PONDS
            </button>
            <button class="gps-layer-btn gps-workers active" id="workers-layer">
                <i class="fas fa-users"></i>
                WORKERS
            </button>
            <button class="gps-layer-btn gps-aerators active" id="aerators-layer">
                <i class="fas fa-fan"></i>
                AERATORS
            </button>
            <button class="gps-layer-btn gps-geofences" id="geofences-layer">
                <i class="fas fa-draw-polygon"></i>
                GEOFENCES
            </button>
        </div>



        <!-- Map Controls -->
        <div class="gps-panel-title">
            <i class="fas fa-cog"></i>
            MAP CONTROLS
        </div>
        <div class="gps-map-controls">
            <button class="gps-map-btn" id="center-map">
                <i class="fas fa-crosshairs"></i>
                CENTER MAP
            </button>
            <button class="gps-map-btn gps-refresh" id="refresh-data">
                <i class="fas fa-sync-alt"></i>
                REFRESH
            </button>
            <button class="gps-map-btn gps-fit" id="fit-all">
                <i class="fas fa-expand-arrows-alt"></i>
                FIT ALL
            </button>
        </div>
    </div>

    <!-- Enhanced GPS Map Container -->
    <div class="gps-map-container">
        <div id="map" class="gps-loading">Loading Enhanced GPS Map...</div>
    </div>



    <!-- Grid View -->
    <div id="grid-container">
        {% if ponds %}
        <div class="pond-grid">
            {% for pond in ponds %}
            <div class="pond-card"
                 data-pond-id="{{ pond.id }}"
                 data-lat="{{ pond.latitude|default:'' }}"
                 data-lng="{{ pond.longitude|default:'' }}">
                <div class="pond-header-info">
                    <h3 class="pond-name">{{ pond.name }}</h3>
                    <div class="pond-header-actions">
                        <span class="pond-status status-{{ pond.status }}">{{ pond.status|title }}</span>
                        {% if pond.latitude and pond.longitude %}
                        <button class="location-zoom-btn" onclick="zoomToPond({{ pond.latitude }}, {{ pond.longitude }}, '{{ pond.name }}'); event.stopPropagation();" title="Zoom to location">
                            <i class="fas fa-map-marker-alt"></i>
                        </button>
                        {% endif %}
                        <button class="pond-detail-btn" onclick="window.location.href='{% url 'ponds:pond_detail' pond.id %}'; event.stopPropagation();" title="View details">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="pond-details">
                    {% if pond.farm %}
                    <div class="detail-row" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px 12px; border-radius: 8px; margin-bottom: 10px;">
                        <span class="detail-label" style="color: rgba(255,255,255,0.9);">🏢 Farm:</span>
                        <span class="detail-value" style="color: white; font-weight: bold;">
                            <a href="{% url 'ponds:farm_detail' pond.farm.id %}" style="color: white; text-decoration: none;">
                                {{ pond.farm.name }}
                            </a>
                        </span>
                    </div>
                    {% else %}
                    <div class="detail-row" style="background: linear-gradient(135deg, #636e72 0%, #2d3436 100%); color: white; padding: 8px 12px; border-radius: 8px; margin-bottom: 10px;">
                        <span class="detail-label" style="color: rgba(255,255,255,0.9);">🏢 Farm:</span>
                        <span class="detail-value" style="color: white; font-weight: bold;">Not assigned</span>
                    </div>
                    {% endif %}
                    <div class="detail-row">
                        <span class="detail-label">📏 Size:</span>
                        <span class="detail-value">{{ pond.size }} m²</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">🦐 Species:</span>
                        <span class="detail-value">{{ pond.species|default:"Not specified" }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">💧 Water Quality:</span>
                        <span class="detail-value">{{ pond.water_quality|default:"Not assessed" }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">📅 Created:</span>
                        <span class="detail-value">{{ pond.created_at|date:"M d, Y" }}</span>
                    </div>
                    {% if pond.created_by %}
                    <div class="detail-row">
                        <span class="detail-label">👤 Created By:</span>
                        <span class="detail-value">{{ pond.created_by.username }}</span>
                    </div>
                    {% endif %}
                    {% if pond.latitude and pond.longitude %}
                    <div class="detail-row">
                        <span class="detail-label">📍 Location:</span>
                        <span class="detail-value">{{ pond.latitude|floatformat:4 }}°N, {{ pond.longitude|floatformat:4 }}°E</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Integrated Weather Information -->
                {% if pond.latitude and pond.longitude %}
                <div class="integrated-weather-section">
                    <div class="weather-header">
                        <span class="weather-icon">🌤️</span>
                        <span class="weather-title">Weather Conditions</span>
                    </div>
                    <div class="weather-grid" id="weather-grid-{{ pond.id }}">
                        <div class="weather-item">
                            <span class="weather-icon-small">🌡️</span>
                            <span class="weather-value" id="temp-{{ pond.id }}">28°C</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-icon-small">💧</span>
                            <span class="weather-value" id="humidity-{{ pond.id }}">75%</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-icon-small">💨</span>
                            <span class="weather-value" id="wind-{{ pond.id }}">12 m/s</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-icon-small">📊</span>
                            <span class="weather-value" id="pressure-{{ pond.id }}">1013 hPa</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-icon-small">👁️</span>
                            <span class="weather-value" id="visibility-{{ pond.id }}">10 km</span>
                        </div>
                        <div class="weather-item">
                            <span class="weather-icon-small">☀️</span>
                            <span class="weather-value" id="uv-{{ pond.id }}">UV 5</span>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="weather-info" style="background: linear-gradient(135deg, #636e72, #2d3436);">
                    <div class="weather-temp">📍 No Location</div>
                    <div class="weather-condition">Add coordinates for Google Maps</div>
                </div>
                {% endif %}

                <div class="pond-actions">
                    <a href="{% url 'ponds:pond_detail' pond.id %}" class="action-btn">
                        <i class="fas fa-eye me-1"></i>Details
                    </a>
                    {% if pond.farm %}
                    <a href="{% url 'ponds:farm_detail' pond.farm.id %}" class="action-btn" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        <i class="fas fa-building me-1"></i>Farm
                    </a>
                    {% endif %}
                    {% if pond.latitude and pond.longitude %}
                    <a href="{% url 'weather:pond_weather_map' pond.id %}" class="action-btn secondary">
                        <i class="fas fa-cloud me-1"></i>Weather
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-fish fa-4x text-muted mb-3"></i>
            <h3>No Ponds Found</h3>
            <p>No ponds match your current filters. Try adjusting your search criteria or create a new pond.</p>
            <a href="{% url 'ponds:pond_create_with_map' %}" class="toggle-btn">
                <i class="fas fa-plus-circle me-2"></i>Create Your First Pond with Map
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    console.log('🚀 Enhanced Pond Management with GPS System Starting...');

    // Layer visibility state
    const layerState = {
        farms: true,
        ponds: true,
        workers: true,
        aerators: true,
        geofences: false,
        weather: true,
        weatherAnimations: false,
        weatherNumbers: false
    };

    // Layer groups for map
    const layerGroups = {
        farms: null,
        ponds: null,
        workers: null,
        geofences: null
    };

    // Weather overlays storage
    let weatherOverlays = [];
    let weatherAnimations = [];
    let weatherNumbers = [];

    // Step 1: Initialize all data first
    const mapData = {
        farms: {{ farms_data|default:"[]"|safe }},
        ponds: {{ ponds_data|default:"[]"|safe }},
        workers: {{ workers_data|default:"[]"|safe }},
        aerators: {{ aerators_data|default:"[]"|safe }},
        weather: {{ weather_data|default:"[]"|safe }},
        centerLat: {{ center_lat|default:"13.0827" }},
        centerLng: {{ center_lng|default:"80.2707" }},
        apiKey: "{{ google_maps_api_key|default:"" }}"
    };

    console.log('📊 Map Data Loaded:', {
        farms: mapData.farms.length,
        ponds: mapData.ponds.length,
        workers: mapData.workers.length,
        weather: mapData.weather.length,
        center: [mapData.centerLat, mapData.centerLng]
    });

    // Step 2: Update info display
    document.addEventListener('DOMContentLoaded', function() {
        updateStats();
        initializeWeatherData(); // Initialize weather for integrated cards

        // Add hover effects to pond cards
        const pondCards = document.querySelectorAll('.pond-card');
        pondCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });

    // Step 3: Define global variables for Google Maps
    let map;
    let markers = [];

    // Step 4: Define the global initMap function
    function initMap() {
        console.log('🗺️ Google Maps API loaded! Initializing enhanced GPS map...');

        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }

        try {
            // Calculate optimal map center and bounds based on pond and farm locations
            let mapCenter = { lat: mapData.centerLat, lng: mapData.centerLng };
            let shouldFitBounds = false;
            const bounds = new google.maps.LatLngBounds();

            // If we have pond data, prioritize pond locations for centering
            if (mapData.ponds.length > 0) {
                console.log('🎯 Including pond locations in map bounds...');
                mapData.ponds.forEach(pond => {
                    if (pond.latitude && pond.longitude) {
                        bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
                        shouldFitBounds = true;
                    }
                });
            }

            // Also include farm locations
            if (mapData.farms.length > 0) {
                console.log('🎯 Including farm locations in map bounds...');
                mapData.farms.forEach(farm => {
                    if (farm.latitude && farm.longitude) {
                        bounds.extend(new google.maps.LatLng(farm.latitude, farm.longitude));
                        shouldFitBounds = true;
                    }
                });
            }

            // Calculate center of all locations
            const allLocations = [...mapData.ponds, ...mapData.farms].filter(item => item.latitude && item.longitude);
            if (allLocations.length > 0) {
                const avgLat = allLocations.reduce((sum, item) => sum + item.latitude, 0) / allLocations.length;
                const avgLng = allLocations.reduce((sum, item) => sum + item.longitude, 0) / allLocations.length;
                mapCenter = { lat: avgLat, lng: avgLng };

                console.log(`📍 Combined center: ${avgLat}, ${avgLng} with ${allLocations.length} locations`);
            }

            // Create the map with combined center
            map = new google.maps.Map(mapElement, {
                zoom: shouldFitBounds ? 10 : 12,
                center: mapCenter,
                mapTypeId: 'roadmap'
            });

            console.log('✅ Map created successfully with ponds & farms center!');
            mapElement.classList.remove('gps-loading');

            // Add farm markers
            mapData.farms.forEach((farm, index) => {
                if (farm.latitude && farm.longitude) {
                    const marker = new google.maps.Marker({
                        position: { lat: farm.latitude, lng: farm.longitude },
                        map: map,
                        title: farm.name || `Farm ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#28a745" stroke="white" stroke-width="2"/>
                                    <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">F</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });

                    // Add info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${farm.name || 'Farm'}</h4>
                                <p><strong>Location:</strong> ${farm.latitude}, ${farm.longitude}</p>
                                <p><strong>Ponds:</strong> ${farm.pond_count || 'N/A'}</p>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to farm location
                        map.setCenter(marker.getPosition());
                        map.setZoom(16);
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });

            // Add pond markers
            mapData.ponds.forEach((pond, index) => {
                if (pond.latitude && pond.longitude) {
                    // Color based on status
                    const statusColors = {
                        'active': '#007bff',
                        'inactive': '#6c757d',
                        'maintenance': '#ffc107',
                        'harvesting': '#28a745'
                    };

                    const color = statusColors[pond.status] || '#007bff';

                    const marker = new google.maps.Marker({
                        position: { lat: pond.latitude, lng: pond.longitude },
                        map: map,
                        title: pond.name || `Pond ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Background circle -->
                                    <circle cx="20" cy="20" r="18" fill="white" stroke="${color}" stroke-width="3" opacity="0.95"/>

                                    <!-- Green landscape base -->
                                    <ellipse cx="20" cy="32" rx="15" ry="6" fill="#27ae60"/>
                                    <ellipse cx="20" cy="30" rx="12" ry="4" fill="#2ecc71"/>

                                    <!-- Water waves -->
                                    <path d="M8 28 Q12 26, 16 28 Q20 30, 24 28 Q28 26, 32 28" stroke="#3498db" stroke-width="1.5" fill="none"/>
                                    <path d="M8 30 Q12 28, 16 30 Q20 32, 24 30 Q28 28, 32 30" stroke="#5dade2" stroke-width="1" fill="none"/>

                                    <!-- Main shrimp body (curved) -->
                                    <path d="M8 20 Q10 15, 15 16 Q20 17, 25 18 Q30 19, 32 20 Q30 21, 25 22 Q20 23, 15 24 Q10 25, 8 20 Z" fill="#e74c3c" stroke="#c0392b" stroke-width="1"/>

                                    <!-- Shrimp segments -->
                                    <path d="M12 18 Q16 17, 20 18" stroke="#c0392b" stroke-width="0.8" fill="none"/>
                                    <path d="M13 20 Q17 19, 21 20" stroke="#c0392b" stroke-width="0.8" fill="none"/>
                                    <path d="M12 22 Q16 23, 20 22" stroke="#c0392b" stroke-width="0.8" fill="none"/>

                                    <!-- Shrimp head -->
                                    <ellipse cx="8.5" cy="20" rx="2.5" ry="3.5" fill="#e67e22" stroke="#d35400" stroke-width="0.8"/>

                                    <!-- Eye -->
                                    <circle cx="7.5" cy="18.5" r="1" fill="#2c3e50"/>
                                    <circle cx="7.3" cy="18.2" r="0.3" fill="white"/>

                                    <!-- Antennae -->
                                    <path d="M6.5 17 Q4 15, 3 13" stroke="#d35400" stroke-width="1" fill="none"/>
                                    <path d="M7 17.5 Q5 15.5, 4 13.5" stroke="#d35400" stroke-width="1" fill="none"/>

                                    <!-- Tail -->
                                    <path d="M32 20 Q34 18, 36 19 Q35 20, 36 21 Q34 22, 32 20" fill="#e67e22" stroke="#d35400" stroke-width="0.8"/>

                                    <!-- Gear (technology symbol) -->
                                    <circle cx="12" cy="12" r="3" fill="#f39c12" stroke="#e67e22" stroke-width="0.8"/>
                                    <circle cx="12" cy="12" r="1.5" fill="none" stroke="#e67e22" stroke-width="0.6"/>
                                    <rect x="10.5" y="9" width="3" height="1" fill="#e67e22"/>
                                    <rect x="10.5" y="14" width="3" height="1" fill="#e67e22"/>
                                    <rect x="9" y="11.5" width="1" height="1" fill="#e67e22"/>
                                    <rect x="14" y="11.5" width="1" height="1" fill="#e67e22"/>

                                    <!-- Circuit pattern -->
                                    <path d="M4 8 L6 8 L6 6 L8 6" stroke="#f39c12" stroke-width="0.8" fill="none"/>
                                    <circle cx="4" cy="8" r="0.5" fill="#f39c12"/>
                                    <circle cx="6" cy="6" r="0.5" fill="#f39c12"/>
                                    <circle cx="8" cy="6" r="0.5" fill="#f39c12"/>

                                    <!-- Wind turbine -->
                                    <line x1="28" y1="12" x2="28" y2="8" stroke="#ecf0f1" stroke-width="1"/>
                                    <path d="M28 8 L26 6 L28 8 L30 6" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="0.5"/>
                                    <circle cx="28" cy="8" r="0.8" fill="#95a5a6"/>

                                    <!-- Tech circuit lines -->
                                    <path d="M32 6 L34 6 L34 8 L36 8" stroke="#f39c12" stroke-width="0.6" fill="none"/>
                                    <circle cx="32" cy="6" r="0.3" fill="#f39c12"/>
                                    <circle cx="36" cy="8" r="0.3" fill="#f39c12"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(40, 40)
                        }
                    });

                    // Add combined info window with weather
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div id="pond-info-${index}" style="
                                padding: 15px;
                                background: rgba(255, 255, 255, 0.15);
                                border-radius: 15px;
                                backdrop-filter: blur(20px);
                                border: 1px solid rgba(255,255,255,0.2);
                                color: rgba(255, 255, 255, 0.95);
                                text-shadow: 0 1px 3px rgba(0,0,0,0.5);
                                font-size: 0.85rem;
                                line-height: 1.3;
                                min-width: 250px;
                                max-width: 280px;
                            ">
                                <!-- Pond Information Section -->
                                <div style="margin-bottom: 12px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 10px;">
                                    <h4 style="margin: 0 0 8px 0; font-size: 1.1rem; font-weight: 600; color: rgba(255, 255, 255, 0.98); display: flex; align-items: center;">
                                        🦐 ${pond.name || 'Pond'}
                                    </h4>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Farm:</span>
                                        <span style="font-weight: 600;">${pond.farm_name}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Status:</span>
                                        <span style="font-weight: 600; color: ${pond.status === 'active' ? '#2ecc71' : pond.status === 'maintenance' ? '#f39c12' : '#e74c3c'};">${pond.status}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Size:</span>
                                        <span style="font-weight: 600;">${pond.size} m²</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Species:</span>
                                        <span style="font-weight: 600;">${pond.species}</span>
                                    </div>
                                </div>

                                <!-- Weather Information Section -->
                                <div id="weather-section-${index}">
                                    <h5 style="margin: 0 0 6px 0; font-size: 0.95rem; font-weight: 600; color: rgba(255, 255, 255, 0.98); display: flex; align-items: center;">
                                        🌤️ Weather Conditions
                                    </h5>
                                    <div style="text-align: center; opacity: 0.7; font-size: 0.8rem;">
                                        Loading weather data...
                                    </div>
                                </div>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to pond location
                        map.setCenter(marker.getPosition());
                        map.setZoom(18);

                        // Clear any existing weather overlays
                        clearWeatherOverlays();

                        // Open combined info window
                        infoWindow.open(map, marker);

                        // Load weather data into the combined info window
                        setTimeout(() => {
                            loadWeatherForPondInfo(pond.latitude, pond.longitude, index);
                        }, 300); // Delay to allow info window to open
                    });

                    markers.push(marker);
                }
            });

            // Add worker markers
            mapData.workers.forEach((worker, index) => {
                if (worker.latitude && worker.longitude) {
                    // Color based on status
                    const statusColors = {
                        'working': '#ef4444',
                        'available': '#10b981',
                        'break': '#f59e0b',
                        'traveling': '#8b5cf6',
                        'offline': '#6b7280'
                    };

                    const color = statusColors[worker.status] || '#10b981';

                    const marker = new google.maps.Marker({
                        position: { lat: worker.latitude, lng: worker.longitude },
                        map: map,
                        title: worker.name || `Worker ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="10" cy="10" r="8" fill="${color}" stroke="white" stroke-width="2"/>
                                    <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">W</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(20, 20)
                        }
                    });

                    // Add info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${worker.name || 'Worker'}</h4>
                                <p><strong>Employee ID:</strong> ${worker.employee_id || 'N/A'}</p>
                                <p><strong>Status:</strong> ${worker.status}</p>
                                <p><strong>Team:</strong> ${worker.team}</p>
                                <p><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</p>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to worker location
                        map.setCenter(marker.getPosition());
                        map.setZoom(19);
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });

            // Add aerator markers
            mapData.aerators.forEach((aerator, index) => {
                if (aerator.latitude && aerator.longitude) {
                    // Color based on status
                    const statusColors = {
                        'active': '#10b981',      // Green - running
                        'inactive': '#6b7280',   // Gray - stopped
                        'maintenance': '#f59e0b', // Orange - maintenance
                        'faulty': '#ef4444'      // Red - error
                    };

                    const color = statusColors[aerator.status] || '#6b7280';

                    // Get aerator type icon
                    const getAeratorIcon = (type) => {
                        const icons = {
                            'paddle_wheel': '🌀',
                            'fountain': '⛲',
                            'diffuser': '💨',
                            'venturi': '🌊',
                            'surface': '🔄',
                            'submersible': '⚡'
                        };
                        return icons[type] || '⚙️';
                    };

                    const marker = new google.maps.Marker({
                        position: { lat: aerator.latitude, lng: aerator.longitude },
                        map: map,
                        title: aerator.name || `Aerator ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                    <!-- Background circle -->
                                    <circle cx="12.5" cy="12.5" r="11" fill="${color}" stroke="white" stroke-width="2" opacity="0.9"/>

                                    <!-- Aerator symbol -->
                                    <circle cx="12.5" cy="12.5" r="7" fill="none" stroke="white" stroke-width="1.5"/>
                                    <circle cx="12.5" cy="12.5" r="4" fill="none" stroke="white" stroke-width="1"/>

                                    <!-- Rotation blades -->
                                    <path d="M12.5 5.5 L12.5 19.5 M5.5 12.5 L19.5 12.5" stroke="white" stroke-width="1.2" opacity="0.8"/>
                                    <path d="M8.5 8.5 L16.5 16.5 M16.5 8.5 L8.5 16.5" stroke="white" stroke-width="1" opacity="0.6"/>

                                    <!-- Center dot -->
                                    <circle cx="12.5" cy="12.5" r="1.5" fill="white"/>

                                    <!-- Status indicator -->
                                    <circle cx="20" cy="5" r="3" fill="${aerator.status === 'active' ? '#10b981' : aerator.status === 'faulty' ? '#ef4444' : '#f59e0b'}" stroke="white" stroke-width="1"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });

                    // Add combined info window with aerator details
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="
                                padding: 15px;
                                background: rgba(255, 255, 255, 0.15);
                                border-radius: 15px;
                                backdrop-filter: blur(20px);
                                border: 1px solid rgba(255,255,255,0.2);
                                color: rgba(255, 255, 255, 0.95);
                                text-shadow: 0 1px 3px rgba(0,0,0,0.5);
                                font-size: 0.85rem;
                                line-height: 1.3;
                                min-width: 250px;
                                max-width: 280px;
                            ">
                                <!-- Aerator Information Section -->
                                <div style="margin-bottom: 12px; border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 10px;">
                                    <h4 style="margin: 0 0 8px 0; font-size: 1.1rem; font-weight: 600; color: rgba(255, 255, 255, 0.98); display: flex; align-items: center;">
                                        ${getAeratorIcon(aerator.aerator_type)} ${aerator.name}
                                    </h4>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Pond:</span>
                                        <span style="font-weight: 600;">${aerator.pond_name}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Farm:</span>
                                        <span style="font-weight: 600;">${aerator.farm_name}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Type:</span>
                                        <span style="font-weight: 600;">${aerator.aerator_type_display}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Status:</span>
                                        <span style="font-weight: 600; color: ${aerator.status === 'active' ? '#2ecc71' : aerator.status === 'maintenance' ? '#f39c12' : aerator.status === 'faulty' ? '#e74c3c' : '#95a5a6'};">${aerator.status_display}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Power:</span>
                                        <span style="font-weight: 600;">${aerator.power_rating || 'N/A'} ${aerator.power_rating ? 'HP' : ''}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Source:</span>
                                        <span style="font-weight: 600;">${aerator.power_source_display}</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Hours:</span>
                                        <span style="font-weight: 600;">${aerator.operating_hours || 0}h</span>
                                    </div>
                                    <div style="margin: 3px 0; display: flex; justify-content: space-between;">
                                        <span style="opacity: 0.8;">Efficiency:</span>
                                        <span style="font-weight: 600;">${aerator.efficiency || 0}%</span>
                                    </div>
                                    ${aerator.is_automated ? '<div style="margin-top: 6px; text-align: center; font-size: 0.75rem; color: #3498db;">🤖 Automated Control</div>' : ''}
                                </div>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to aerator location
                        map.setCenter(marker.getPosition());
                        map.setZoom(20);
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                }
            });

            // Auto-fit map to show all locations
            if (shouldFitBounds && bounds && !bounds.isEmpty()) {
                console.log('🔍 Auto-fitting map bounds to show all pond & farm locations...');
                map.fitBounds(bounds);

                setTimeout(() => {
                    const currentZoom = map.getZoom();
                    if (currentZoom > 15) {
                        map.setZoom(15);
                    } else if (currentZoom < 10) {
                        map.setZoom(10);
                    }
                    console.log(`🎯 Final map zoom level: ${map.getZoom()}`);
                }, 100);
            }

            // Initialize interactive controls
            initializeControls();

        } catch (error) {
            console.error('❌ Error creating map:', error);
            handleMapError();
        }
    }

    // Combined pond and weather info functions
    function loadWeatherForPondInfo(lat, lng, pondIndex) {
        console.log(`🌤️ Loading weather data for pond info window...`);

        // Generate weather data for this pond
        const weatherData = generateWeatherData(lat, lng);

        // Update the weather section in the info window
        const weatherSection = document.getElementById(`weather-section-${pondIndex}`);
        if (weatherSection) {
            weatherSection.innerHTML = `
                <h5 style="margin: 0 0 8px 0; font-size: 0.95rem; font-weight: 600; color: rgba(255, 255, 255, 0.98); display: flex; align-items: center;">
                    ${getWeatherIcon(weatherData.condition)} Weather Conditions
                </h5>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 6px; font-size: 0.8rem;">
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1.2rem;">🌡️</span>
                        <span style="font-weight: 600; color: #3498db;">${weatherData.temperature}°C</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1rem;">💧</span>
                        <span style="opacity: 0.9;">${weatherData.humidity}%</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1rem;">💨</span>
                        <span style="opacity: 0.9;">${weatherData.windSpeed} m/s</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1rem;">📊</span>
                        <span style="opacity: 0.9;">${weatherData.pressure} hPa</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1rem;">👁️</span>
                        <span style="opacity: 0.9;">${weatherData.visibility} km</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 4px;">
                        <span style="font-size: 1rem;">☀️</span>
                        <span style="opacity: 0.9;">UV ${weatherData.uvIndex}</span>
                    </div>
                </div>
                <div style="margin-top: 8px; text-align: center; font-size: 0.75rem; opacity: 0.6;">
                    Live weather data
                </div>
            `;
        }

        console.log(`✅ Weather data loaded for pond info window`);
    }

    // Weather overlay functions - Show only on pond click (legacy function)
    function showWeatherForPond(lat, lng, pondName) {
        console.log(`🌤️ Showing weather for pond: ${pondName}`);

        // Clear any existing weather overlays
        clearWeatherOverlays();

        // Generate weather data for this pond
        const weatherData = generateWeatherData(lat, lng);

        // Create transparent weather overlay
        createTransparentWeatherOverlay(lat, lng, weatherData, pondName);

        console.log(`✅ Weather overlay created for ${pondName}`);
    }

    function addWeatherForLocation(lat, lng, name, type) {
        // Simulate weather data (in real implementation, fetch from weather API)
        const weatherData = generateWeatherData(lat, lng);

        if (layerState.weather) {
            createWeatherOverlay(lat, lng, weatherData, name, type);
        }

        if (layerState.weatherAnimations) {
            createWeatherAnimation(lat, lng, weatherData);
        }

        if (layerState.weatherNumbers) {
            createWeatherNumbers(lat, lng, weatherData);
        }
    }

    function generateWeatherData(lat, lng) {
        // Simulate realistic weather data based on location
        const baseTemp = 28 + (Math.sin(lat * 0.1) * 5); // Vary by latitude
        const humidity = 65 + (Math.random() * 20);
        const windSpeed = 5 + (Math.random() * 10);

        const conditions = ['sunny', 'cloudy', 'rainy', 'partly-cloudy'];
        const condition = conditions[Math.floor(Math.random() * conditions.length)];

        return {
            temperature: Math.round(baseTemp + (Math.random() * 6 - 3)),
            humidity: Math.round(humidity),
            windSpeed: Math.round(windSpeed * 10) / 10,
            condition: condition,
            pressure: Math.round(1013 + (Math.random() * 20 - 10)),
            visibility: Math.round(8 + (Math.random() * 4)),
            uvIndex: Math.round(Math.random() * 11)
        };
    }

    function createTransparentWeatherOverlay(lat, lng, weather, name) {
        // Create weather overlay as fixed positioned element
        const div = document.createElement('div');
        div.className = 'weather-overlay';
        div.innerHTML = `
            <div class="weather-header">
                <i class="fas ${getWeatherIconFA(weather.condition)} weather-icon"></i>
                <span>${name}</span>
                <button style="background: none; border: none; color: rgba(255,255,255,0.7); float: right; font-size: 1.2rem; cursor: pointer; padding: 0; margin-left: 10px;" onclick="clearWeatherOverlays()">×</button>
            </div>
            <div class="weather-temp">${weather.temperature}°C</div>
            <div class="weather-details">
                <div class="weather-item">
                    <i class="fas fa-tint"></i>
                    <span>${weather.humidity}%</span>
                </div>
                <div class="weather-item">
                    <i class="fas fa-wind"></i>
                    <span>${weather.windSpeed} m/s</span>
                </div>
                <div class="weather-item">
                    <i class="fas fa-thermometer-half"></i>
                    <span>${weather.pressure} hPa</span>
                </div>
                <div class="weather-item">
                    <i class="fas fa-eye"></i>
                    <span>${weather.visibility} km</span>
                </div>
            </div>
            <div style="margin-top: 10px; font-size: 0.8rem; opacity: 0.7; text-align: center;">
                📍 ${name} Weather Data
            </div>
        `;

        // Add click to close functionality
        div.addEventListener('click', (e) => {
            if (e.target.tagName !== 'BUTTON') {
                clearWeatherOverlays();
            }
        });

        // Add to document body
        document.body.appendChild(div);
        weatherOverlays.push(div);
    }

    function createWeatherAnimation(lat, lng, weather) {
        const overlay = new google.maps.OverlayView();

        overlay.onAdd = function() {
            const div = document.createElement('div');
            div.className = `weather-animation ${weather.condition}-animation`;

            this.div_ = div;
            const panes = this.getPanes();
            panes.overlayLayer.appendChild(div);
        };

        overlay.draw = function() {
            const overlayProjection = this.getProjection();
            const position = overlayProjection.fromLatLngToDivPixel(new google.maps.LatLng(lat, lng));

            const div = this.div_;
            div.style.left = (position.x - 50) + 'px';
            div.style.top = (position.y - 50) + 'px';
        };

        overlay.onRemove = function() {
            if (this.div_) {
                this.div_.parentNode.removeChild(this.div_);
                this.div_ = null;
            }
        };

        overlay.setMap(map);
        weatherAnimations.push(overlay);
    }

    function createWeatherNumbers(lat, lng, weather) {
        const overlay = new google.maps.OverlayView();

        overlay.onAdd = function() {
            const div = document.createElement('div');
            div.className = 'weather-numbers';
            div.innerHTML = `${weather.temperature}°`;

            this.div_ = div;
            const panes = this.getPanes();
            panes.overlayLayer.appendChild(div);
        };

        overlay.draw = function() {
            const overlayProjection = this.getProjection();
            const position = overlayProjection.fromLatLngToDivPixel(new google.maps.LatLng(lat, lng));

            const div = this.div_;
            div.style.left = (position.x + 20) + 'px';
            div.style.top = (position.y - 30) + 'px';
        };

        overlay.onRemove = function() {
            if (this.div_) {
                this.div_.parentNode.removeChild(this.div_);
                this.div_ = null;
            }
        };

        overlay.setMap(map);
        weatherNumbers.push(overlay);
    }

    function getWeatherIcon(condition) {
        const icons = {
            'sunny': '☀️',
            'cloudy': '☁️',
            'rainy': '🌧️',
            'partly-cloudy': '⛅'
        };
        return icons[condition] || '☀️';
    }

    function getWeatherIconFA(condition) {
        const icons = {
            'sunny': 'fa-sun',
            'cloudy': 'fa-cloud',
            'rainy': 'fa-cloud-rain',
            'partly-cloudy': 'fa-cloud-sun'
        };
        return icons[condition] || 'fa-sun';
    }

    function clearWeatherOverlays() {
        // Remove weather overlay elements from DOM
        weatherOverlays.forEach(element => {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
            }
        });

        // Clear arrays
        weatherOverlays = [];
        weatherAnimations = [];
        weatherNumbers = [];

        console.log('🧹 Weather overlays cleared');
    }

    function toggleWeatherLayer(layerType, button) {
        layerState[layerType] = !layerState[layerType];

        if (layerState[layerType]) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        // Refresh weather overlays
        addWeatherOverlays();

        console.log(`🌤️ Toggled ${layerType}: ${layerState[layerType] ? 'ON' : 'OFF'}`);
    }

    // Update stats function
    function updateStats() {
        document.getElementById('farms-count').textContent = mapData.farms.length;
        document.getElementById('ponds-count').textContent = mapData.ponds.length;
        document.getElementById('workers-count').textContent = mapData.workers.length;
        document.getElementById('security-zones').textContent = '0'; // Placeholder
    }

    // Initialize interactive controls
    function initializeControls() {
        console.log('🎮 Initializing interactive controls...');

        // Layer toggle buttons
        document.getElementById('farms-layer').addEventListener('click', function() {
            toggleLayer('farms', this);
        });

        document.getElementById('ponds-layer').addEventListener('click', function() {
            toggleLayer('ponds', this);
        });

        document.getElementById('workers-layer').addEventListener('click', function() {
            toggleLayer('workers', this);
        });

        document.getElementById('aerators-layer').addEventListener('click', function() {
            toggleLayer('aerators', this);
        });

        document.getElementById('geofences-layer').addEventListener('click', function() {
            toggleLayer('geofences', this);
        });

        // Map control buttons
        document.getElementById('center-map').addEventListener('click', centerMap);
        document.getElementById('refresh-data').addEventListener('click', refreshData);
        document.getElementById('fit-all').addEventListener('click', fitAllMarkers);

        // Fullscreen toggle (if fullscreen button exists)
        const fullscreenBtn = document.querySelector('.gps-fullscreen-btn');
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', toggleFullscreen);
        }

        console.log('✅ Interactive controls initialized');
    }

    function toggleFullscreen() {
        const mapContainer = document.querySelector('.gps-map-container');

        if (!document.fullscreenElement) {
            mapContainer.requestFullscreen().catch(err => {
                console.log('Fullscreen not supported');
            });
        } else {
            document.exitFullscreen();
        }
    }

    // Map control functions
    function centerMap() {
        if (map) {
            // Calculate center from all visible markers
            const bounds = new google.maps.LatLngBounds();
            let hasVisibleMarkers = false;

            markers.forEach(marker => {
                if (marker.getVisible()) {
                    bounds.extend(marker.getPosition());
                    hasVisibleMarkers = true;
                }
            });

            if (hasVisibleMarkers) {
                map.fitBounds(bounds);
                // Add some padding
                setTimeout(() => {
                    const currentZoom = map.getZoom();
                    if (currentZoom > 15) {
                        map.setZoom(15);
                    } else if (currentZoom < 10) {
                        map.setZoom(10);
                    }
                }, 100);
            } else {
                // Fallback to default center
                map.setCenter({ lat: mapData.centerLat, lng: mapData.centerLng });
                map.setZoom(12);
            }
            console.log('🎯 Map centered');
        }
    }

    function refreshData() {
        console.log('🔄 Refreshing data...');
        const refreshBtn = document.getElementById('refresh-data');
        const icon = refreshBtn.querySelector('i');

        icon.style.animation = 'spin 1s linear infinite';

        setTimeout(() => {
            // Update stats
            updateStats();
            icon.style.animation = '';
            console.log('✅ Data refreshed');
        }, 1000);
    }

    function fitAllMarkers() {
        if (map && markers.length > 0) {
            const bounds = new google.maps.LatLngBounds();

            markers.forEach(marker => {
                if (marker.getVisible()) {
                    bounds.extend(marker.getPosition());
                }
            });

            if (!bounds.isEmpty()) {
                map.fitBounds(bounds);
                console.log('📏 Fitted all visible markers');
            }
        }
    }

    // Layer toggle function
    function toggleLayer(layerType, button) {
        layerState[layerType] = !layerState[layerType];

        if (layerState[layerType]) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }

        // Filter markers based on layer state
        markers.forEach(marker => {
            const title = marker.getTitle().toLowerCase();
            let shouldShow = false;

            if (layerState.farms && title.includes('farm')) shouldShow = true;
            if (layerState.ponds && title.includes('pond')) shouldShow = true;
            if (layerState.workers && title.includes('worker')) shouldShow = true;
            if (layerState.aerators && title.includes('aerator')) shouldShow = true;

            marker.setVisible(shouldShow);
        });

        console.log(`🔄 Toggled ${layerType} layer: ${layerState[layerType] ? 'ON' : 'OFF'}`);
    }

    // Error handler
    function handleMapError() {
        console.error('❌ Failed to load Google Maps');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.classList.remove('gps-loading');
            mapElement.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i><br>
                    Failed to load Enhanced GPS Maps<br>
                    <small>Please check your internet connection and refresh the page</small>
                </div>
            `;
        }
    }

    // Initialize weather data for all ponds with integrated cards
    function initializeWeatherData() {
        const pondCards = document.querySelectorAll('.pond-card');
        pondCards.forEach(card => {
            const pondId = card.dataset.pondId;
            const lat = card.dataset.lat;
            const lng = card.dataset.lng;

            if (lat && lng) {
                fetchWeatherData(lat, lng, pondId);
            }
        });
    }

    // Fetch weather data for integrated cards
    async function fetchWeatherData(lat, lng, pondId) {
        try {
            // Using a demo weather API - replace with your actual API key
            const response = await fetch(`https://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lng}&appid=demo&units=metric`);

            // For demo purposes, use simulated data
            const simulatedData = {
                main: {
                    temp: Math.round(25 + Math.random() * 10), // 25-35°C
                    humidity: Math.round(60 + Math.random() * 30), // 60-90%
                    pressure: Math.round(1000 + Math.random() * 30) // 1000-1030 hPa
                },
                wind: {
                    speed: Math.round(5 + Math.random() * 15) // 5-20 m/s
                },
                visibility: Math.round(8000 + Math.random() * 4000) // 8-12 km
            };

            // Update weather grid elements
            const tempElement = document.getElementById(`temp-${pondId}`);
            const humidityElement = document.getElementById(`humidity-${pondId}`);
            const windElement = document.getElementById(`wind-${pondId}`);
            const pressureElement = document.getElementById(`pressure-${pondId}`);
            const visibilityElement = document.getElementById(`visibility-${pondId}`);
            const uvElement = document.getElementById(`uv-${pondId}`);

            if (tempElement) tempElement.textContent = `${simulatedData.main.temp}°C`;
            if (humidityElement) humidityElement.textContent = `${simulatedData.main.humidity}%`;
            if (windElement) windElement.textContent = `${simulatedData.wind.speed} m/s`;
            if (pressureElement) pressureElement.textContent = `${simulatedData.main.pressure} hPa`;
            if (visibilityElement) visibilityElement.textContent = `${Math.round(simulatedData.visibility / 1000)} km`;
            if (uvElement) uvElement.textContent = `UV ${Math.round(3 + Math.random() * 5)}`; // UV 3-8

        } catch (error) {
            console.log('Weather data simulation for pond', pondId);
            // Keep default values if simulation fails
        }
    }

    // Zoom to specific pond location
    function zoomToPond(lat, lng, pondName) {
        if (window.map) {
            const pondLocation = new google.maps.LatLng(lat, lng);

            // Zoom to the pond location
            window.map.setCenter(pondLocation);
            window.map.setZoom(18); // Close zoom level

            // Create a temporary marker with info window
            const marker = new google.maps.Marker({
                position: pondLocation,
                map: window.map,
                title: pondName,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#e17055" stroke="#fff" stroke-width="3"/>
                            <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-weight="bold">🦐</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40),
                    anchor: new google.maps.Point(20, 20)
                },
                animation: google.maps.Animation.BOUNCE
            });

            // Create info window
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="padding: 10px; text-align: center;">
                        <h3 style="margin: 0 0 5px 0; color: #2d3436;">🦐 ${pondName}</h3>
                        <p style="margin: 0; color: #636e72; font-size: 0.9rem;">
                            Lat: ${lat.toFixed(6)}<br>
                            Lng: ${lng.toFixed(6)}
                        </p>
                    </div>
                `
            });

            // Show info window
            infoWindow.open(window.map, marker);

            // Stop bouncing after 2 seconds
            setTimeout(() => {
                marker.setAnimation(null);
            }, 2000);

            // Remove marker after 10 seconds
            setTimeout(() => {
                marker.setMap(null);
                infoWindow.close();
            }, 10000);

            // Show success message
            showNotification(`Zoomed to ${pondName}`, 'success');
        } else {
            showNotification('Map not loaded yet. Please wait...', 'warning');
        }
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#00b894' : type === 'warning' ? '#fdcb6e' : '#0984e3'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // Make functions globally accessible
    window.initMap = initMap;
    window.handleMapError = handleMapError;
    window.clearWeatherOverlays = clearWeatherOverlays;
    window.initializeWeatherData = initializeWeatherData;
    window.zoomToPond = zoomToPond;

    console.log('✅ Enhanced Pond Management GPS System with Integrated Weather setup complete');
</script>

<!-- Load Google Maps API -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>

</body>
</html>
