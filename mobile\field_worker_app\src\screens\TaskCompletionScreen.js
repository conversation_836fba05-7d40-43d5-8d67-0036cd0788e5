/**
 * Task Completion Screen
 * Allows field workers to complete tasks with photos, notes, and measurements
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
  PermissionsAndroid,
  Platform
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import TaskManager, { TaskType } from '../services/TaskManager';

const TaskCompletionScreen = ({ route, navigation }) => {
  const { taskId } = route.params;
  const [task, setTask] = useState(null);
  const [notes, setNotes] = useState('');
  const [photos, setPhotos] = useState([]);
  const [measurements, setMeasurements] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadTask();
  }, [taskId]);

  const loadTask = () => {
    try {
      const taskData = TaskManager.getTaskById(taskId);
      if (taskData) {
        setTask(taskData);
        initializeMeasurements(taskData.type);
      } else {
        Alert.alert('Error', 'Task not found', [
          { text: 'OK', onPress: () => navigation.goBack() }
        ]);
      }
    } catch (error) {
      console.error('Error loading task:', error);
      Alert.alert('Error', 'Failed to load task details');
    }
  };

  const initializeMeasurements = (taskType) => {
    const measurementFields = getMeasurementFields(taskType);
    const initialMeasurements = {};
    
    measurementFields.forEach(field => {
      initialMeasurements[field.key] = '';
    });
    
    setMeasurements(initialMeasurements);
  };

  const getMeasurementFields = (taskType) => {
    switch (taskType) {
      case TaskType.WATER_QUALITY_CHECK:
        return [
          { key: 'temperature', label: 'Temperature (°C)', type: 'number' },
          { key: 'ph', label: 'pH Level', type: 'number' },
          { key: 'dissolved_oxygen', label: 'Dissolved Oxygen (mg/L)', type: 'number' },
          { key: 'salinity', label: 'Salinity (ppt)', type: 'number' },
          { key: 'turbidity', label: 'Turbidity (NTU)', type: 'number' }
        ];
      case TaskType.FEEDING:
        return [
          { key: 'feed_amount', label: 'Feed Amount (kg)', type: 'number' },
          { key: 'feed_type', label: 'Feed Type', type: 'text' },
          { key: 'feeding_time', label: 'Feeding Time (minutes)', type: 'number' }
        ];
      case TaskType.POND_CLEANING:
        return [
          { key: 'cleaning_duration', label: 'Cleaning Duration (hours)', type: 'number' },
          { key: 'waste_removed', label: 'Waste Removed (kg)', type: 'number' },
          { key: 'equipment_used', label: 'Equipment Used', type: 'text' }
        ];
      case TaskType.EQUIPMENT_MAINTENANCE:
        return [
          { key: 'maintenance_type', label: 'Maintenance Type', type: 'text' },
          { key: 'parts_replaced', label: 'Parts Replaced', type: 'text' },
          { key: 'maintenance_duration', label: 'Duration (hours)', type: 'number' }
        ];
      case TaskType.DISEASE_INSPECTION:
        return [
          { key: 'shrimp_inspected', label: 'Shrimp Inspected (count)', type: 'number' },
          { key: 'diseased_found', label: 'Diseased Found (count)', type: 'number' },
          { key: 'disease_type', label: 'Disease Type', type: 'text' },
          { key: 'treatment_applied', label: 'Treatment Applied', type: 'text' }
        ];
      case TaskType.INVENTORY_CHECK:
        return [
          { key: 'items_counted', label: 'Items Counted', type: 'number' },
          { key: 'discrepancies', label: 'Discrepancies Found', type: 'number' },
          { key: 'inventory_type', label: 'Inventory Type', type: 'text' }
        ];
      default:
        return [];
    }
  };

  const requestCameraPermission = async () => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: 'Camera Permission',
            message: 'This app needs access to camera to take photos',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn(err);
        return false;
      }
    }
    return true;
  };

  const showImagePicker = () => {
    Alert.alert(
      'Add Photo',
      'Choose an option',
      [
        { text: 'Camera', onPress: openCamera },
        { text: 'Gallery', onPress: openGallery },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('Permission Denied', 'Camera permission is required to take photos');
      return;
    }

    const options = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchCamera(options, (response) => {
      if (response.assets && response.assets[0]) {
        const newPhoto = response.assets[0];
        setPhotos(prev => [...prev, newPhoto.uri]);
      }
    });
  };

  const openGallery = () => {
    const options = {
      mediaType: 'photo',
      quality: 0.8,
      maxWidth: 1024,
      maxHeight: 1024,
    };

    launchImageLibrary(options, (response) => {
      if (response.assets && response.assets[0]) {
        const newPhoto = response.assets[0];
        setPhotos(prev => [...prev, newPhoto.uri]);
      }
    });
  };

  const removePhoto = (index) => {
    setPhotos(prev => prev.filter((_, i) => i !== index));
  };

  const updateMeasurement = (key, value) => {
    setMeasurements(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const validateCompletion = () => {
    if (!notes.trim()) {
      Alert.alert('Validation Error', 'Please add completion notes');
      return false;
    }

    const measurementFields = getMeasurementFields(task.type);
    const requiredFields = measurementFields.filter(field => field.required);
    
    for (const field of requiredFields) {
      if (!measurements[field.key] || measurements[field.key].trim() === '') {
        Alert.alert('Validation Error', `Please fill in ${field.label}`);
        return false;
      }
    }

    return true;
  };

  const handleCompleteTask = async () => {
    if (!validateCompletion()) {
      return;
    }

    setLoading(true);
    
    try {
      const completionData = {
        notes: notes.trim(),
        photos,
        measurements,
        completedBy: 'Current User', // Would get from auth context
        completionLocation: null, // Would get from GPS
        timestamp: new Date().toISOString()
      };

      await TaskManager.completeTask(taskId, completionData);
      
      Alert.alert(
        'Success',
        'Task completed successfully!',
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.navigate('TaskList');
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error completing task:', error);
      Alert.alert('Error', 'Failed to complete task. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderMeasurementFields = () => {
    const fields = getMeasurementFields(task?.type);
    
    if (fields.length === 0) {
      return null;
    }

    return (
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Measurements</Text>
        {fields.map((field) => (
          <View key={field.key} style={styles.inputGroup}>
            <Text style={styles.inputLabel}>{field.label}</Text>
            <TextInput
              style={styles.input}
              value={measurements[field.key]}
              onChangeText={(value) => updateMeasurement(field.key, value)}
              placeholder={`Enter ${field.label.toLowerCase()}`}
              keyboardType={field.type === 'number' ? 'numeric' : 'default'}
            />
          </View>
        ))}
      </View>
    );
  };

  const renderPhotos = () => (
    <View style={styles.section}>
      <View style={styles.sectionHeader}>
        <Text style={styles.sectionTitle}>Photos</Text>
        <TouchableOpacity 
          style={styles.addPhotoButton}
          onPress={showImagePicker}
        >
          <Icon name="add-a-photo" size={20} color="#2196F3" />
          <Text style={styles.addPhotoText}>Add Photo</Text>
        </TouchableOpacity>
      </View>
      
      {photos.length > 0 ? (
        <View style={styles.photoGrid}>
          {photos.map((photo, index) => (
            <View key={index} style={styles.photoContainer}>
              <Image source={{ uri: photo }} style={styles.photo} />
              <TouchableOpacity
                style={styles.removePhotoButton}
                onPress={() => removePhoto(index)}
              >
                <Icon name="close" size={16} color="#fff" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      ) : (
        <View style={styles.noPhotos}>
          <Icon name="photo-camera" size={48} color="#ccc" />
          <Text style={styles.noPhotosText}>No photos added yet</Text>
        </View>
      )}
    </View>
  );

  if (!task) {
    return (
      <View style={styles.loadingContainer}>
        <Icon name="hourglass-empty" size={48} color="#ccc" />
        <Text style={styles.loadingText}>Loading task...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Task Info */}
        <View style={styles.taskInfo}>
          <Text style={styles.taskTitle}>{task.title}</Text>
          <Text style={styles.taskDescription}>{task.description}</Text>
        </View>

        {/* Notes Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Completion Notes *</Text>
          <TextInput
            style={styles.notesInput}
            value={notes}
            onChangeText={setNotes}
            placeholder="Describe what was completed, any issues encountered, or additional observations..."
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Measurements */}
        {renderMeasurementFields()}

        {/* Photos */}
        {renderPhotos()}
      </ScrollView>

      {/* Complete Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.completeButton, loading && styles.disabledButton]}
          onPress={handleCompleteTask}
          disabled={loading}
        >
          {loading ? (
            <Icon name="hourglass-empty" size={24} color="#fff" />
          ) : (
            <Icon name="check" size={24} color="#fff" />
          )}
          <Text style={styles.completeButtonText}>
            {loading ? 'Completing...' : 'Complete Task'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  taskInfo: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  taskTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 8,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  section: {
    backgroundColor: '#fff',
    padding: 16,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#333',
    backgroundColor: '#fff',
    minHeight: 100,
  },
  addPhotoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  addPhotoText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '600',
    marginLeft: 4,
  },
  photoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  photoContainer: {
    position: 'relative',
  },
  photo: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#F44336',
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPhotos: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  noPhotosText: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
  },
  footer: {
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#4CAF50',
    paddingVertical: 16,
    borderRadius: 8,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  completeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#fff',
  },
});

export default TaskCompletionScreen;
