<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .debug-box { background: #f0f0f0; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; border-left: 4px solid #f44336; }
        .success { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .info { background: #e3f2fd; border-left: 4px solid #2196f3; }
        #map { height: 300px; background: #ccc; border: 2px solid #333; margin: 20px 0; }
        .log { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔍 Google Maps API Debug Tool</h1>
    
    <div class="debug-box info">
        <h3>📋 Configuration</h3>
        <p><strong>API Key:</strong> {{ google_maps_api_key|slice:":20" }}...</p>
        <p><strong>API Key Length:</strong> {{ google_maps_api_key|length }} characters</p>
        <p><strong>Domain:</strong> <span id="domain"></span></p>
        <p><strong>Protocol:</strong> <span id="protocol"></span></p>
    </div>

    <div class="debug-box" id="status-box">
        <h3>📊 Status</h3>
        <p id="status">🔄 Initializing...</p>
    </div>

    <div class="debug-box">
        <h3>🗺️ Map Container</h3>
        <div id="map"></div>
    </div>

    <div class="debug-box">
        <h3>📝 Console Log</h3>
        <div class="log" id="console-log"></div>
    </div>

    <div class="debug-box">
        <h3>🔧 Manual Tests</h3>
        <button onclick="testApiKey()">Test API Key</button>
        <button onclick="testNetworkAccess()">Test Network</button>
        <button onclick="testJavaScript()">Test JavaScript</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEl = document.getElementById('console-log');
            const statusEl = document.getElementById('status');
            const statusBox = document.getElementById('status-box');
            
            console.log(`[${type}] ${message}`);
            logEl.innerHTML += `<div>[${timestamp}] [${type.toUpperCase()}] ${message}</div>`;
            logEl.scrollTop = logEl.scrollHeight;
            
            statusEl.textContent = message;
            statusBox.className = `debug-box ${type}`;
        }

        // Set domain info
        document.getElementById('domain').textContent = window.location.hostname;
        document.getElementById('protocol').textContent = window.location.protocol;

        // Test functions
        function testApiKey() {
            const apiKey = '{{ google_maps_api_key }}';
            log(`Testing API key: ${apiKey.substring(0, 20)}...`);
            
            if (!apiKey || apiKey.length < 30) {
                log('❌ API key appears to be invalid or too short', 'error');
                return false;
            }
            
            if (!apiKey.startsWith('AIza')) {
                log('❌ API key does not start with "AIza" - may be invalid', 'error');
                return false;
            }
            
            log('✅ API key format appears valid', 'success');
            return true;
        }

        function testNetworkAccess() {
            log('Testing network access to Google APIs...');
            
            fetch('https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}')
                .then(response => {
                    if (response.ok) {
                        log('✅ Network access to Google Maps API successful', 'success');
                    } else {
                        log(`❌ Network error: ${response.status} ${response.statusText}`, 'error');
                    }
                })
                .catch(error => {
                    log(`❌ Network error: ${error.message}`, 'error');
                });
        }

        function testJavaScript() {
            log('Testing JavaScript environment...');
            
            if (typeof google !== 'undefined') {
                log('✅ Google object is available', 'success');
                if (google.maps) {
                    log('✅ Google Maps API is loaded', 'success');
                } else {
                    log('❌ Google Maps API not loaded', 'error');
                }
            } else {
                log('❌ Google object not found', 'error');
            }
        }

        function clearLog() {
            document.getElementById('console-log').innerHTML = '';
        }

        // Global error handler
        window.onerror = function(msg, url, line, col, error) {
            log(`❌ JavaScript Error: ${msg} at line ${line}`, 'error');
            return false;
        };

        // Google Maps callback
        function initMap() {
            log('🗺️ Google Maps callback triggered!', 'success');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 10.8231, lng: 106.6297 },
                    zoom: 10
                });
                
                new google.maps.Marker({
                    position: { lat: 10.8231, lng: 106.6297 },
                    map: map,
                    title: 'Test Location'
                });
                
                log('🎉 Google Maps created successfully!', 'success');
                
            } catch (error) {
                log(`❌ Error creating map: ${error.message}`, 'error');
            }
        }

        // Handle script load error
        function handleScriptError() {
            log('❌ Failed to load Google Maps script', 'error');
            log('Possible causes:', 'info');
            log('- Invalid API key', 'info');
            log('- API key restrictions (domain, IP, referrer)', 'info');
            log('- Quota exceeded', 'info');
            log('- Network connectivity issues', 'info');
            log('- Firewall blocking Google APIs', 'info');
        }

        // Initialize
        log('🚀 Starting Google Maps debug session...');
        log(`Domain: ${window.location.hostname}`);
        log(`Protocol: ${window.location.protocol}`);
        
        // Run initial tests
        setTimeout(() => {
            testApiKey();
            testJavaScript();
        }, 1000);
    </script>
    
    <!-- Google Maps API with error handling -->
    <script 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap" 
        async 
        defer 
        onerror="handleScriptError()">
    </script>
</body>
</html>
