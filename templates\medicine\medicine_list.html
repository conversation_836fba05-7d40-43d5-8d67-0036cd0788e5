<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicine Inventory - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-pills medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Inventory Management</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Medicine Inventory</h2>
                    <p class="mb-0 opacity-75">Track and manage your shrimp farm medicine inventory</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Total Items</small>
                                <strong>{{ total_medicines|default:"0" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/medicine/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Add Medicine
            </a>
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-tags"></i>
                Categories
            </a>
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-clipboard-list"></i>
                Applications
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
    
    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <p class="text-muted mb-1">Total Medicines</p>
                            <h3 class="mb-0">{{ total_medicines }}</h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-pills text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <p class="text-muted mb-1">Low Stock</p>
                            <h3 class="mb-0">{{ low_stock_count }}</h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <p class="text-muted mb-1">Out of Stock</p>
                            <h3 class="mb-0">{{ out_of_stock_count }}</h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-times-circle text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <p class="text-muted mb-1">Expired</p>
                            <h3 class="mb-0">{{ expired_count }}</h3>
                        </div>
                        <div class="bg-secondary bg-opacity-10 p-3 rounded-circle">
                            <i class="fas fa-calendar-times text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" name="search" class="form-control" placeholder="Search medicines..." value="{{ search_query }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-select">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if selected_category == category.id|stringformat:"i" %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="In Stock" {% if selected_status == 'In Stock' %}selected{% endif %}>In Stock</option>
                        <option value="Low Stock" {% if selected_status == 'Low Stock' %}selected{% endif %}>Low Stock</option>
                        <option value="Out of Stock" {% if selected_status == 'Out of Stock' %}selected{% endif %}>Out of Stock</option>
                        <option value="Expired" {% if selected_status == 'Expired' %}selected{% endif %}>Expired</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">Filter</button>
                        <a href="/medicine/medicine/" class="btn btn-outline-secondary">Reset</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Medicine List -->
    <div class="card">
        <div class="card-body">
            {% if medicines %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Stock</th>
                            <th>Expiry Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for medicine in medicines %}
                        <tr>
                            <td>{{ medicine.name }}</td>
                            <td>{{ medicine.category.name }}</td>
                            <td>{{ medicine.stock_quantity }} {{ medicine.unit }}</td>
                            <td>
                                {% if medicine.expiry_date %}
                                {{ medicine.expiry_date|date:"M d, Y" }}
                                {% if medicine.is_expired %}
                                <span class="badge bg-danger">Expired</span>
                                {% elif medicine.days_until_expiry < 30 %}
                                <span class="badge bg-warning text-dark">Expires in {{ medicine.days_until_expiry }} days</span>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">Not set</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge {% if medicine.status == 'In Stock' %}bg-success{% elif medicine.status == 'Low Stock' %}bg-warning text-dark{% elif medicine.status == 'Out of Stock' %}bg-danger{% else %}bg-secondary{% endif %}">
                                    {{ medicine.status }}
                                </span>
                            </td>
                            <td>
                                <a href="/medicine/medicine/{{ medicine.id }}/" class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/medicine/medicine/{{ medicine.id }}/edit/" class="btn btn-sm btn-outline-secondary me-1">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="/medicine/application/create/?medicine_id={{ medicine.id }}" class="btn btn-sm btn-outline-success">
                                    <i class="fas fa-syringe"></i>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-pills text-muted mb-3" style="font-size: 3rem;"></i>
                <h5>No Medicines Found</h5>
                <p class="text-muted">
                    {% if search_query or selected_category or selected_status %}
                    No medicines match your filter criteria.
                    <a href="/medicine/medicine/">Clear filters</a>
                    {% else %}
                    You haven't added any medicines yet.
                    {% endif %}
                </p>
                <a href="/medicine/medicine/create/" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i> Add Medicine
                </a>
            </div>
            {% endif %}
        </div>
    </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
