<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="map"></div>

    <script>
        // Make sure initMap is globally accessible
        function initMap() {
            console.log('Google Maps API loaded successfully!');
            
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 } // Chennai, India
            });
            
            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Marker'
            });
            
            console.log('Map created successfully!');
        }

        function handleMapError() {
            console.error('Failed to load Google Maps API');
            document.getElementById('map').innerHTML = '<p style="color: red;">Failed to load Google Maps API</p>';
        }
    </script>

    <!-- Load Google Maps API with the same key -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZq_es-geometry%3A184%3A214&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
