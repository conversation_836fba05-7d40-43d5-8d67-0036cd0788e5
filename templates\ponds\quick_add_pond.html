{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Pond to {{ farm.name }} - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
<style>
        .quick-add-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .quick-add-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: header-sweep 6s infinite;
        }

        @keyframes header-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .quick-add-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            transition: all 0.4s ease;
        }

        .quick-add-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

    .form-section {
        margin-bottom: 25px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .form-section h5 {
        color: #667eea;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .coordinate-helper {
        background: #e8f5e8;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        border-left: 4px solid #4caf50;
    }

    .farm-info {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        border-left: 4px solid #2196f3;
    }
</style>
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="quick-add-header">
        <div class="position-relative">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2"><i class="fas fa-plus me-3"></i>Add Pond</h1>
                    <p class="mb-2" style="opacity: 0.9;">Adding new pond to {{ farm.name }}</p>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-building"></i> {{ farm.total_ponds }} existing ponds
                    </span>
                </div>
                <div>
                    <a href="{% url 'ponds:farm_detail' farm.pk %}" class="action-btn" style="background: linear-gradient(45deg, #74b9ff, #0984e3);">
                        <i class="fas fa-arrow-left"></i> Back to Farm
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Add Form -->
    <div class="quick-add-card">
        <!-- Farm Info -->
        <div class="farm-info">
            <h5><i class="fas fa-building"></i> Farm: {{ farm.name }}</h5>
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-1"><strong>Location:</strong> {{ farm.location }}</p>
                    <p class="mb-1"><strong>Total Ponds:</strong> {{ farm.total_ponds }}</p>
                </div>
                <div class="col-md-6">
                    {% if farm.latitude and farm.longitude %}
                    <p class="mb-1"><strong>Coordinates:</strong> {{ farm.latitude }}, {{ farm.longitude }}</p>
                    {% endif %}
                    <p class="mb-1"><strong>Contact:</strong> {{ farm.contact_person|default:"Not specified" }}</p>
                </div>
            </div>
        </div>

        <form method="post" id="quickAddForm">
            {% csrf_token %}
            
            <!-- Pond Details -->
            <div class="form-section">
                <h5><i class="fas fa-water"></i> Pond Details</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <strong>Pond Name</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.size.id_for_label }}" class="form-label">
                                <strong>Size (m²)</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.size }}
                            {% if form.size.errors %}
                                <div class="text-danger">{{ form.size.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.species.id_for_label }}" class="form-label">
                                <strong>Species</strong>
                            </label>
                            {{ form.species }}
                            {% if form.species.errors %}
                                <div class="text-danger">{{ form.species.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.density.id_for_label }}" class="form-label">
                                <strong>Stocking Density</strong>
                            </label>
                            {{ form.density }}
                            {% if form.density.errors %}
                                <div class="text-danger">{{ form.density.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">
                                <strong>Status</strong>
                            </label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <div class="text-danger">{{ form.status.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.occupancy.id_for_label }}" class="form-label">
                                <strong>Occupancy (%)</strong>
                            </label>
                            {{ form.occupancy }}
                            {% if form.occupancy.errors %}
                                <div class="text-danger">{{ form.occupancy.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.stocked_date.id_for_label }}" class="form-label">
                                <strong>Stocked Date</strong>
                            </label>
                            {{ form.stocked_date }}
                            {% if form.stocked_date.errors %}
                                <div class="text-danger">{{ form.stocked_date.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location -->
            <div class="form-section">
                <h5><i class="fas fa-map-marker-alt"></i> Location</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.latitude.id_for_label }}" class="form-label">
                                <strong>Latitude</strong>
                            </label>
                            {{ form.latitude }}
                            {% if form.latitude.errors %}
                                <div class="text-danger">{{ form.latitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.longitude.id_for_label }}" class="form-label">
                                <strong>Longitude</strong>
                            </label>
                            {{ form.longitude }}
                            {% if form.longitude.errors %}
                                <div class="text-danger">{{ form.longitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="coordinate-helper">
                    <small><strong>📍 Location Helper:</strong></small><br>
                    {% if farm.latitude and farm.longitude %}
                    <small>Farm Center: {{ farm.latitude }}, {{ farm.longitude }}</small><br>
                    <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="useFarmLocation()">
                        <i class="fas fa-crosshairs"></i> Use Farm Location
                    </button>
                    {% endif %}
                    <button type="button" class="btn btn-sm btn-outline-info mt-2" onclick="getCurrentLocation()">
                        <i class="fas fa-location-arrow"></i> Get Current Location
                    </button>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <a href="{% url 'ponds:farm_detail' farm.pk %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <div>
                    <button type="button" class="btn btn-info me-2" onclick="addAnotherPond()">
                        <i class="fas fa-plus"></i> Add Another
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Add Pond
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Location helpers
function useFarmLocation() {
    {% if farm.latitude and farm.longitude %}
    document.getElementById('{{ form.latitude.id_for_label }}').value = {{ farm.latitude }};
    document.getElementById('{{ form.longitude.id_for_label }}').value = {{ farm.longitude }};
    showAlert('success', 'Farm location coordinates applied');
    {% else %}
    showAlert('error', 'Farm coordinates not available');
    {% endif %}
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            document.getElementById('{{ form.latitude.id_for_label }}').value = position.coords.latitude.toFixed(6);
            document.getElementById('{{ form.longitude.id_for_label }}').value = position.coords.longitude.toFixed(6);
            showAlert('success', 'Current location coordinates applied');
        }, function(error) {
            showAlert('error', 'Unable to get current location: ' + error.message);
        });
    } else {
        showAlert('error', 'Geolocation is not supported by this browser');
    }
}

function addAnotherPond() {
    // Store current form data
    const formData = new FormData(document.getElementById('quickAddForm'));
    
    // Submit current pond
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => {
        if (response.ok) {
            showAlert('success', 'Pond added! Ready for next pond.');
            // Clear form for next pond
            document.getElementById('quickAddForm').reset();
            // Auto-increment pond name
            const nameField = document.getElementById('{{ form.name.id_for_label }}');
            const currentName = nameField.value;
            const match = currentName.match(/(\d+)$/);
            if (match) {
                const newNumber = parseInt(match[1]) + 1;
                nameField.value = currentName.replace(/\d+$/, newNumber);
            }
        } else {
            showAlert('error', 'Error adding pond. Please check the form.');
        }
    })
    .catch(error => {
        showAlert('error', 'Network error. Please try again.');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Form validation
document.getElementById('quickAddForm').addEventListener('submit', function(e) {
    const name = document.getElementById('{{ form.name.id_for_label }}').value;
    const size = document.getElementById('{{ form.size.id_for_label }}').value;
    
    if (!name.trim()) {
        e.preventDefault();
        showAlert('error', 'Pond name is required');
        return;
    }
    
    if (!size || size <= 0) {
        e.preventDefault();
        showAlert('error', 'Valid pond size is required');
        return;
    }
});

// Initialize form
document.addEventListener('DOMContentLoaded', function() {
    // Auto-suggest pond name based on existing ponds
    const existingPonds = {{ farm.total_ponds }};
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    if (!nameField.value) {
        nameField.value = `Pond ${existingPonds + 1}`;
    }
    
    // Set default coordinates to farm location if available
    {% if farm.latitude and farm.longitude %}
    const latField = document.getElementById('{{ form.latitude.id_for_label }}');
    const lngField = document.getElementById('{{ form.longitude.id_for_label }}');
    if (!latField.value) latField.value = {{ farm.latitude }};
    if (!lngField.value) lngField.value = {{ farm.longitude }};
    {% endif %}
});
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
