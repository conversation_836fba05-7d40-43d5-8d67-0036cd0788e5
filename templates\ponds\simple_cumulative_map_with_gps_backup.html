{% extends 'base.html' %}
{% load static %}

{% block title %}Simple Cumulative Map with GPS & Geofences{% endblock %}

{% block extra_css %}
<style>
    /* Override base layout styles for full-width map */
    .main-content {
        padding: 1rem !important;
    }
    
    body {
        margin: 0;
        padding: 0;
    }
    
    .container-fluid {
        padding: 0 !important;
        max-width: 100% !important;
        margin: 0 !important;
    }
    
    #map {
        height: 600px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
        outline: none;
    }
    
    .map-container {
        margin: 10px 0;
        padding: 15px;
        background: rgba(30, 41, 59, 0.8);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 100%;
        box-sizing: border-box;
    }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 15px 0;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .controls {
        margin: 15px 0;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }
      .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-primary {
        background: #3b82f6;
        color: white;
    }
    
    .btn-success {
        background: #10b981;
        color: white;
    }
    
    .btn-info {
        background: #06b6d4;
        color: white;
    }
    
    .btn-warning {
        background: #f59e0b;
        color: white;
    }
    
    .debug-info {
        background: rgba(30, 41, 59, 0.6);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #94a3b8;
        backdrop-filter: blur(10px);
    }
    
    /* Page title styling */
    h1 {
        color: #e2e8f0 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 1.5rem;
    }
    
    /* Advanced Controls Styling */
    .advanced-controls {
        background: rgba(30, 41, 59, 0.9);
        border-radius: 12px;
        padding: 15px;
        margin: 10px 0;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(74, 85, 104, 0.3);
    }
    
    .control-section {
        margin-bottom: 15px;
    }
    
    .control-section h6 {
        color: #94a3b8;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 8px;
    }
    
    .slider-container {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 8px 0;
    }
    
    .time-slider {
        flex: 1;
        height: 6px;
        border-radius: 3px;
        background: rgba(74, 85, 104, 0.5);
        outline: none;
        -webkit-appearance: none;
    }
    
    .time-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #667eea;
        cursor: pointer;
        border: 2px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    /* Environmental Data Styling */
    .env-data-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        background: rgba(30, 41, 59, 0.95);
        border-radius: 8px;
        padding: 10px;
        min-width: 200px;
        border: 1px solid rgba(74, 85, 104, 0.3);
        backdrop-filter: blur(10px);
        z-index: 1000;
    }
    
    .env-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid rgba(74, 85, 104, 0.2);
    }
    
    .env-metric:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        color: #94a3b8;
        font-size: 0.8rem;
    }
    
    .metric-value {
        color: #e2e8f0;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .metric-status {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-left: 8px;
    }
    
    .status-good { background: #10b981; }
    .status-warning { background: #f59e0b; }
    .status-critical { background: #ef4444; }
    
    /* Search Box Styling */
    .search-container {
        position: relative;
        margin: 10px 0;
    }
    
    .search-input {
        width: 100%;
        padding: 10px 40px 10px 15px;
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 8px;
        background: rgba(30, 41, 59, 0.8);
        color: #e2e8f0;
        font-size: 14px;
    }
    
    .search-input::placeholder {
        color: #94a3b8;
    }
    
    .search-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #94a3b8;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="text-center mb-4">
        <i class="fas fa-map-marked-alt"></i>
        {{ page_title }}
    </h1>
    
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div><i class="fas fa-warehouse"></i></div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Farms</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-water"></i></div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Ponds</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-users"></i></div>
            <div class="stat-number">{{ total_workers }}</div>
            <div class="stat-label">GPS Workers</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-shield-alt"></i></div>
            <div class="stat-number">{{ total_geofences }}</div>
            <div class="stat-label">Geofences</div>
        </div>
    </div>    <!-- Enhanced Search & Filter Controls -->
    <div class="search-container">
        <input type="text" id="mapSearch" class="search-input" placeholder="Search workers, ponds, farms..." onkeyup="performMapSearch(this.value)">
        <i class="fas fa-search search-icon"></i>
    </div>

    <!-- Advanced Controls -->
    <div class="advanced-controls">
        <div class="control-section">
            <h6><i class="fas fa-layer-group"></i> Layer Controls</h6>
            <div class="controls">
                <button class="btn btn-primary" onclick="toggleFarms()">
                    <i class="fas fa-warehouse"></i>
                    Toggle Farms
                </button>
                <button class="btn btn-success" onclick="togglePonds()">
                    <i class="fas fa-water"></i>
                    Toggle Ponds
                </button>
                <button class="btn btn-info" onclick="toggleWorkers()">
                    <i class="fas fa-users"></i>
                    Toggle Workers
                </button>
                <button class="btn btn-warning" onclick="toggleGeofences()">
                    <i class="fas fa-shield-alt"></i>
                    Toggle Geofences
                </button>
                <button class="btn btn-success" onclick="togglePondBoundaries()">
                    <i class="fas fa-draw-polygon"></i>
                    Toggle Pond Boundaries
                </button>
            </div>
        </div>
        
        <div class="control-section">
            <h6><i class="fas fa-thermometer-half"></i> Environmental Data</h6>
            <div class="controls">
                <button class="btn btn-info" onclick="toggleTemperatureHeatmap()">
                    <i class="fas fa-temperature-high"></i>
                    Temperature Heatmap
                </button>
                <button class="btn btn-warning" onclick="toggleWaterQuality()">
                    <i class="fas fa-flask"></i>
                    Water Quality
                </button>
                <button class="btn btn-success" onclick="toggleWorkerTrails()">
                    <i class="fas fa-route"></i>
                    Worker Trails
                </button>
                <button class="btn btn-danger" onclick="toggleAlertZones()">
                    <i class="fas fa-exclamation-triangle"></i>
                    Alert Zones
                </button>
            </div>
        </div>
        
        <div class="control-section">
            <h6><i class="fas fa-clock"></i> Time Controls</h6>
            <div class="slider-container">
                <label>Historical Data:</label>
                <input type="range" id="timeSlider" class="time-slider" min="0" max="24" value="24" onchange="updateTimeView(this.value)">
                <span id="timeDisplay">Now</span>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                    Center Map
                </button>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh Data
                </button>
                <button class="btn btn-warning" onclick="toggleRealTimeMode()">
                    <i class="fas fa-broadcast-tower"></i>
                    <span id="realtimeText">Enable Real-time</span>
                </button>
            </div>
        </div>
    </div>
      <!-- Debug Info -->
    <div class="debug-info">
        <strong>Debug Info:</strong><br>
        Map Center: {{ center_lat|floatformat:6 }}, {{ center_lng|floatformat:6 }}<br>
        Farms: {{ total_farms }} | Ponds: {{ total_ponds }} | Workers: {{ total_workers }} | Geofences: {{ total_geofences }}<br>
        Active Workers: {{ active_workers }}<br>
        <strong>Raw Data Preview:</strong><br>
        Workers Data: {{ workers_data|truncatechars:200 }}<br>
        Geofences Data: {{ geofences_data|truncatechars:200 }}<br>
        <span id="jsDebugInfo">JavaScript data loading...</span>
    </div>
      <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
        
        <!-- Environmental Data Overlay -->
        <div class="env-data-overlay" id="envDataOverlay">
            <h6 style="color: #e2e8f0; margin-bottom: 10px; font-size: 0.9rem;">
                <i class="fas fa-thermometer-half"></i> Environmental Data
            </h6>
            <div class="env-metric">
                <span class="metric-label">Water Temp</span>
                <span class="metric-value" id="waterTemp">26.5°C</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">pH Level</span>
                <span class="metric-value" id="phLevel">7.2</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Dissolved O₂</span>
                <span class="metric-value" id="oxygenLevel">6.8 mg/L</span>
                <div class="metric-status status-warning"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Salinity</span>
                <span class="metric-value" id="salinity">15 ppt</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Air Temp</span>
                <span class="metric-value" id="airTemp">28.3°C</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Wind Speed</span>
                <span class="metric-value" id="windSpeed">3.2 m/s</span>
                <div class="metric-status status-good"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let workerMarkers = [];
let geofenceOverlays = [];
let pondBoundaries = [];
let temperatureHeatmap = null;
let waterQualityMarkers = [];
let workerTrails = [];
let alertZones = [];
let realTimeInterval = null;
let isRealTimeMode = false;

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const workersData = {{ workers_data|safe }};
const geofencesData = {{ geofences_data|safe }};
const centerLat = {{ center_lat }};
const centerLng = {{ center_lng }};

// Visibility toggles
let farmsVisible = true;
let pondsVisible = true;
let workersVisible = true;
let geofencesVisible = true;
let pondBoundariesVisible = false;  // Add pond boundaries toggle

function initMap() {
    console.log('=== INITIALIZING MAP ===');
    console.log('Raw farms data:', {{ farms_data|safe }});
    console.log('Raw ponds data:', {{ ponds_data|safe }});
    console.log('Raw workers data:', {{ workers_data|safe }});
    console.log('Raw geofences data:', {{ geofences_data|safe }});
    
    console.log('Parsed data counts:');
    console.log('Farms:', farmsData.length);
    console.log('Ponds:', pondsData.length);
    console.log('Workers:', workersData.length);
    console.log('Geofences:', geofencesData.length);
    
    console.log('Center coordinates:', centerLat, centerLng);
    
    // Update debug info
    document.getElementById('jsDebugInfo').innerHTML = 
        `JS Loaded - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Geofences: ${geofencesData.length}`;
    
    // Initialize map
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: { lat: centerLat, lng: centerLng },
        mapTypeId: google.maps.MapTypeId.ROADMAP
    });
    
    console.log('Map initialized');
      // Add markers and overlays
    addFarmMarkers();
    addPondMarkers();
    addWorkerMarkers();
    addGeofenceOverlays();
    addPondBoundaries();  // Add pond boundaries
      console.log('All markers and overlays added');
    
    // Initialize advanced features
    initializeAdvancedFeatures();
}

function addFarmMarkers() {
    console.log('Adding farm markers...');
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: farm.latitude, lng: farm.longitude },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
                        <circle cx="12" cy="12" r="10" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-family="Arial">F</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });
          const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${farm.name}</h5>
                    <p><strong>Type:</strong> Farm</p>
                    <p><strong>Description:</strong> ${farm.description}</p>
                    <p><strong>Location:</strong> ${farm.latitude.toFixed(6)}, ${farm.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            // Automatically zoom to farm when marker is clicked
            zoomToFarm(farm.id, farm.latitude, farm.longitude);
        });
        
        farmMarkers.push(marker);
    });
    console.log(`Added ${farmMarkers.length} farm markers`);
}

function addPondMarkers() {
    console.log('Adding pond markers...');
    pondsData.forEach(pond => {
        const marker = new google.maps.Marker({
            position: { lat: pond.latitude, lng: pond.longitude },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
                        <circle cx="12" cy="12" r="10" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-family="Arial">P</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${pond.name}</h5>
                    <p><strong>Type:</strong> Pond</p>
                    <p><strong>Farm:</strong> ${pond.farm_name}</p>
                    <p><strong>Status:</strong> ${pond.status}</p>
                    <p><strong>Location:</strong> ${pond.latitude.toFixed(6)}, ${pond.longitude.toFixed(6)}</p>
                    <p style="font-size: 12px; color: #666; margin-top: 10px;">
                        <i class="fas fa-info-circle"></i> Automatically zoomed to pond location
                    </p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            // Automatically zoom to pond when marker is clicked
            zoomToPond(pond.id, pond.latitude, pond.longitude);
        });
        
        pondMarkers.push(marker);
    });
    console.log(`Added ${pondMarkers.length} pond markers`);
}

function addWorkerMarkers() {
    console.log('Adding worker markers...');
    workersData.forEach(worker => {
        const statusColors = {
            'working': '#ef4444',
            'available': '#10b981',
            'on_break': '#f59e0b',
            'traveling': '#8b5cf6',
            'offline': '#6b7280'
        };
        
        const color = statusColors[worker.status] || '#6b7280';
        
        const marker = new google.maps.Marker({
            position: { lat: worker.latitude, lng: worker.longitude },
            map: map,
            title: worker.name,            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
                        <circle cx="16" cy="16" r="14" fill="${color}" stroke="#ffffff" stroke-width="2"/>
                        <circle cx="16" cy="16" r="10" fill="#f8f9fa"/>
                        <circle cx="16" cy="12" r="4" fill="#fdbcb4"/>
                        <circle cx="14" cy="11" r="0.5" fill="#333"/>
                        <circle cx="18" cy="11" r="0.5" fill="#333"/>
                        <circle cx="16" cy="13" r="0.3" fill="#f4a261"/>
                        <path d="M 14.5 14 Q 16 15 17.5 14" stroke="#333" stroke-width="0.5" fill="none"/>
                        <rect x="12" y="18" width="8" height="6" fill="#4a90e2" rx="2"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${worker.name}</h5>
                    <p><strong>Type:</strong> Worker</p>
                    <p><strong>Employee ID:</strong> ${worker.employee_id}</p>
                    <p><strong>Team:</strong> ${worker.team}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold;">${worker.status}</span></p>
                    <p><strong>Skill Level:</strong> ${worker.skill_level}</p>
                    <p><strong>Location:</strong> ${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</p>
                    <p><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</p>
                </div>
            `
        });
          marker.addListener('click', () => {
            // Automatically zoom to worker when marker is clicked
            zoomToWorker(worker.employee_id, worker.latitude, worker.longitude);
        });
        
        workerMarkers.push(marker);
    });
    console.log(`Added ${workerMarkers.length} worker markers`);
}

function addGeofenceOverlays() {
    console.log('Adding geofence overlays...');
    geofencesData.forEach(geofence => {
        let overlay;
        
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            // Create circle overlay
            overlay = new google.maps.Circle({
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map,
                center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                radius: geofence.radius
            });
            
        } else if (geofence.shape_type === 'polygon' && geofence.boundary && geofence.boundary.length > 0) {
            // Create polygon overlay
            const coords = geofence.boundary.map(coord => ({
                lat: parseFloat(coord[0]),
                lng: parseFloat(coord[1])
            }));
            
            overlay = new google.maps.Polygon({
                paths: coords,
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map
            });
        }
        
        if (overlay) {
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h5>${geofence.name}</h5>
                        <p><strong>Type:</strong> ${geofence.geofence_type}</p>
                        <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                        <p><strong>Description:</strong> ${geofence.description}</p>
                        <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                `
            });
            
            overlay.addListener('click', (event) => {
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
            });
            
            geofenceOverlays.push(overlay);
        }
    });    console.log(`Added ${geofenceOverlays.length} geofence overlays`);
}

// Add pond boundaries visualization
function addPondBoundaries() {
    console.log('Adding pond boundaries...');
    pondsData.forEach(pond => {
        if (pond.boundary && pond.boundary.length > 2) {
            try {
                // Convert boundary coordinates to Google Maps format
                const coords = pond.boundary.map(coord => {
                    // Handle different coordinate formats
                    if (Array.isArray(coord) && coord.length >= 2) {
                        return {
                            lat: parseFloat(coord[0]),
                            lng: parseFloat(coord[1])
                        };
                    } else if (coord.lat !== undefined && coord.lng !== undefined) {
                        return {
                            lat: parseFloat(coord.lat),
                            lng: parseFloat(coord.lng)
                        };
                    }
                    return null;
                }).filter(coord => coord !== null);
                
                if (coords.length > 2) {
                    const pondBoundary = new google.maps.Polygon({
                        paths: coords,
                        strokeColor: '#10b981',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: '#10b981',
                        fillOpacity: 0.1,
                        map: pondBoundariesVisible ? map : null,
                        visible: pondBoundariesVisible
                    });
                    
                    // Add click event to boundary
                    pondBoundary.addListener('click', (event) => {
                        zoomToPond(pond.id, pond.latitude, pond.longitude);
                    });
                    
                    pondBoundaries.push(pondBoundary);
                    console.log(`Added boundary for pond ${pond.name} with ${coords.length} coordinates`);
                }
            } catch (error) {
                console.error(`Error adding boundary for pond ${pond.name}:`, error);
            }
        }
    });
    console.log(`Added ${pondBoundaries.length} pond boundaries`);
}

// Toggle functions
function toggleFarms() {
    farmsVisible = !farmsVisible;
    farmMarkers.forEach(marker => {
        marker.setVisible(farmsVisible);
    });
}

function togglePonds() {
    pondsVisible = !pondsVisible;
    pondMarkers.forEach(marker => {
        marker.setVisible(pondsVisible);
    });
}

function toggleWorkers() {
    workersVisible = !workersVisible;
    workerMarkers.forEach(marker => {
        marker.setVisible(workersVisible);
    });
}

function toggleGeofences() {
    geofencesVisible = !geofencesVisible;
    geofenceOverlays.forEach(overlay => {
        overlay.setVisible(geofencesVisible);
    });
}

function togglePondBoundaries() {
    pondBoundariesVisible = !pondBoundariesVisible;
    pondBoundaries.forEach(boundary => {
        boundary.setVisible(pondBoundariesVisible);
    });
}

function centerMap() {
    map.setCenter({ lat: centerLat, lng: centerLng });
    map.setZoom(12);
}

// Zoom to specific pond with enhanced view
function zoomToPond(pondId, lat, lng) {
    console.log(`Auto-zooming to pond ${pondId} at (${lat}, ${lng})`);
    
    // Center on the pond
    map.setCenter({ lat: lat, lng: lng });
    
    // Set high zoom level to show pond details
    map.setZoom(18);
    
    // Show pond boundaries when zooming
    if (!pondBoundariesVisible) {
        togglePondBoundaries();
    }
    
    // Find pond data to show additional details
    const pond = pondsData.find(p => p.id === pondId);
    if (pond) {
        // Add a small delay to let the zoom animation complete
        setTimeout(() => {
            // Create a more detailed info window for zoomed view
            const detailedInfoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="max-width: 350px;">
                        <h4 style="margin: 0 0 10px 0; color: #1f2937;">
                            <i class="fas fa-water" style="color: #10b981; margin-right: 8px;"></i>
                            ${pond.name}
                        </h4>
                        <div style="display: grid; gap: 5px; font-size: 13px;">
                            <div><strong>Farm:</strong> ${pond.farm_name}</div>
                            <div><strong>Status:</strong> <span style="color: ${getStatusColor(pond.status)}; font-weight: bold;">${pond.status}</span></div>
                            <div><strong>Size:</strong> ${pond.size || 'N/A'} acres</div>
                            <div><strong>Species:</strong> ${pond.species || 'Unknown'}</div>
                            <div><strong>Water Quality:</strong> ${pond.water_quality || 'Unknown'}</div>
                            <div><strong>Depth:</strong> ${pond.depth || 'N/A'} meters</div>
                            <div><strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
                            ${pond.boundary && pond.boundary.length > 0 ? 
                                '<div><strong>Boundary:</strong> <span style="color: #10b981;">✅ Visible</span></div>' : 
                                '<div><strong>Boundary:</strong> <span style="color: #ef4444;">❌ Not defined</span></div>'
                            }
                            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280;">
                                <i class="fas fa-mouse-pointer"></i> Clicked to auto-zoom • 
                                <i class="fas fa-search-plus"></i> Zoom level: 18
                            </div>
                            <div style="margin-top: 10px; text-align: center;">
                                <button onclick="map.setZoom(12); map.setCenter({ lat: centerLat, lng: centerLng }); window.currentInfoWindow.close();" 
                                        style="padding: 8px 12px; background: #6b7280; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 8px; font-size: 12px;">
                                    <i class="fas fa-compress-arrows-alt"></i> Back to Overview
                                </button>
                                <button onclick="fitPondBounds(${pondId})" 
                                        style="padding: 8px 12px; background: #10b981; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                    <i class="fas fa-expand-arrows-alt"></i> Fit Pond Bounds
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                position: { lat: lat, lng: lng }
            });
            
            // Close any existing info windows
            if (window.currentInfoWindow) {
                window.currentInfoWindow.close();
            }
            
            // Open the detailed info window
            detailedInfoWindow.open(map);
            window.currentInfoWindow = detailedInfoWindow;
        }, 800); // Wait for zoom animation to complete
        
        // Highlight the pond marker temporarily
        const pondMarker = pondMarkers.find(marker => {
            const pos = marker.getPosition();
            return Math.abs(pos.lat() - lat) < 0.000001 && Math.abs(pos.lng() - lng) < 0.000001;
        });
        
        if (pondMarker) {
            // Temporarily change marker appearance
            const originalIcon = pondMarker.getIcon();
            pondMarker.setIcon({
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48">
                        <circle cx="12" cy="12" r="10" fill="#10b981" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="12" cy="12" r="6" fill="#ffffff" opacity="0.3"/>
                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="8" font-family="Arial" font-weight="bold">POND</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(48, 48)
    
    // Find worker data to show additional details
    const worker = workersData.find(w => w.employee_id === workerId);
    if (worker) {
        const statusColors = {
            'working': '#ef4444',
            'available': '#10b981',
            'on_break': '#f59e0b',
            'traveling': '#8b5cf6',
            'offline': '#6b7280'
        };
        
        const statusColor = statusColors[worker.status] || '#6b7280';
        
        // Add a small delay to let the zoom animation complete
        setTimeout(() => {
            // Create a detailed info window for zoomed view
            const detailedInfoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="max-width: 350px;">
                        <h4 style="margin: 0 0 10px 0; color: #1f2937;">
                            <i class="fas fa-user" style="color: ${statusColor}; margin-right: 8px;"></i>
                            ${worker.name}
                        </h4>
                        <div style="display: grid; gap: 5px; font-size: 13px;">
                            <div><strong>Employee ID:</strong> ${worker.employee_id}</div>
                            <div><strong>Team:</strong> ${worker.team || 'Unassigned'}</div>
                            <div><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${worker.status}</span></div>
                            <div><strong>Skill Level:</strong> ${worker.skill_level || 'N/A'}</div>
                            <div><strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
                            <div><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</div>
                            <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280;">
                                <i class="fas fa-mouse-pointer"></i> Clicked to auto-zoom • 
                                <i class="fas fa-search-plus"></i> Zoom level: 18
                            </div>
                            <div style="margin-top: 10px; text-align: center;">
                                <button onclick="map.setZoom(12); map.setCenter({ lat: centerLat, lng: centerLng }); window.currentInfoWindow.close();" 
                                        style="padding: 8px 12px; background: #6b7280; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;">
                                    <i class="fas fa-compress-arrows-alt"></i> Back to Overview
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                position: { lat: lat, lng: lng }
            });
            
            // Close any existing info windows
            if (window.currentInfoWindow) {
                window.currentInfoWindow.close();
            }
            
            // Open the detailed info window
            detailedInfoWindow.open(map);
            window.currentInfoWindow = detailedInfoWindow;
        }, 800); // Wait for zoom animation to complete
        
        // Highlight the worker marker temporarily
        const workerMarker = workerMarkers.find(marker => {
            const pos = marker.getPosition();
            return Math.abs(pos.lat() - lat) < 0.000001 && Math.abs(pos.lng() - lng) < 0.000001;
        });
        
        if (workerMarker) {
            // Temporarily change marker appearance
            const originalIcon = workerMarker.getIcon();            workerMarker.setIcon({
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="48" height="48">
                        <circle cx="24" cy="24" r="22" fill="${statusColor}" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="24" cy="24" r="16" fill="#f8f9fa"/>
                        <circle cx="24" cy="18" r="6" fill="#fdbcb4"/>
                        <circle cx="21" cy="17" r="1" fill="#333"/>
                        <circle cx="27" cy="17" r="1" fill="#333"/>
                        <circle cx="24" cy="20" r="0.5" fill="#f4a261"/>
                        <path d="M 21 21.5 Q 24 23 27 21.5" stroke="#333" stroke-width="1" fill="none"/>
                        <rect x="18" y="28" width="12" height="8" fill="#4a90e2" rx="3"/>
                        <text x="24" y="44" text-anchor="middle" fill="${statusColor}" font-size="4" font-family="Arial" font-weight="bold">WORKER</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(48, 48)
            });
            
            // Restore original icon after 4 seconds
            setTimeout(() => {
                workerMarker.setIcon(originalIcon);
            }, 4000);
        }
    }
}

// Fit map bounds to show entire pond boundary
function fitPondBounds(pondId) {
    const pond = pondsData.find(p => p.id === pondId);
    if (pond && pond.boundary && pond.boundary.length > 2) {
        const bounds = new google.maps.LatLngBounds();
        
        pond.boundary.forEach(coord => {
            if (Array.isArray(coord) && coord.length >= 2) {
                bounds.extend(new google.maps.LatLng(parseFloat(coord[0]), parseFloat(coord[1])));
            } else if (coord.lat !== undefined && coord.lng !== undefined) {
                bounds.extend(new google.maps.LatLng(parseFloat(coord.lat), parseFloat(coord.lng)));
            }
        });
        
        map.fitBounds(bounds);
        
        // Add some padding
        setTimeout(() => {
            map.setZoom(Math.max(map.getZoom() - 1, 15));
        }, 500);
    } else {
        // Fallback to standard zoom if no boundary
        zoomToPond(pondId, pond.latitude, pond.longitude);
    }
}

// Helper function to get status color
function getStatusColor(status) {
    const statusColors = {
        'active': '#10b981',
        'maintenance': '#f59e0b',
        'empty': '#6b7280',
        'harvested': '#8b5cf6'
    };
    return statusColors[status] || '#6b7280';
}

// Handle map loading errors
function handleMapError() {
    console.error('Google Maps failed to load');
    document.getElementById('map').innerHTML = `
        <div style="padding: 50px; text-align: center; color: #ef4444;">
            <h3>Error Loading Map</h3>
            <p>Google Maps failed to load. Please check your API key and internet connection.</p>
        </div>
    `;
}

// ===== ADVANCED FEATURES =====

// Initialize advanced features
function initializeAdvancedFeatures() {
    console.log('Initializing advanced features...');
    
    // Add temperature sensors
    addTemperatureSensors();
    
    // Add water quality markers
    addWaterQualityMarkers();
    
    // Add alert zones
    addAlertZones();
    
    // Start environmental data updates
    updateEnvironmentalData();
    setInterval(updateEnvironmentalData, 30000); // Update every 30 seconds
    
    console.log('Advanced features initialized');
}

// Temperature Heatmap
function toggleTemperatureHeatmap() {
    if (temperatureHeatmap) {
        temperatureHeatmap.setMap(temperatureHeatmap.getMap() ? null : map);
    } else {
        createTemperatureHeatmap();
    }
}

function createTemperatureHeatmap() {
    const heatmapData = [];
    
    // Generate temperature data points around ponds
    pondsData.forEach(pond => {
        const baseTemp = 26 + Math.random() * 4; // 26-30°C
        
        // Add multiple points around each pond
        for (let i = 0; i < 8; i++) {
            const angle = (i * 45) * Math.PI / 180;
            const distance = 0.001; // ~100m radius
            
            heatmapData.push({
                location: new google.maps.LatLng(
                    pond.latitude + Math.cos(angle) * distance,
                    pond.longitude + Math.sin(angle) * distance
                ),
                weight: baseTemp
            });
        }
    });
    
    temperatureHeatmap = new google.maps.visualization.HeatmapLayer({
        data: heatmapData,
        map: map,
        gradient: [
            'rgba(0, 255, 255, 0)',
            'rgba(0, 255, 255, 1)',
            'rgba(0, 191, 255, 1)',
            'rgba(0, 127, 255, 1)',
            'rgba(0, 63, 255, 1)',
            'rgba(0, 0, 255, 1)',
            'rgba(0, 0, 223, 1)',
            'rgba(0, 0, 191, 1)',
            'rgba(0, 0, 159, 1)',
            'rgba(0, 0, 127, 1)',
            'rgba(63, 0, 91, 1)',
            'rgba(127, 0, 63, 1)',
            'rgba(191, 0, 31, 1)',
            'rgba(255, 0, 0, 1)'
        ],
        radius: 50,
        opacity: 0.6
    });
    
    console.log('Temperature heatmap created');
}

// Add temperature sensors
function addTemperatureSensors() {
    pondsData.forEach((pond, index) => {
        if (index % 3 === 0) { // Add sensor to every 3rd pond
            const temp = (26 + Math.random() * 4).toFixed(1);
            const status = temp > 29 ? 'critical' : temp > 27.5 ? 'warning' : 'good';
            
            const sensorMarker = new google.maps.Marker({
                position: { 
                    lat: pond.latitude + 0.0005, 
                    lng: pond.longitude + 0.0005 
                },
                map: map,
                title: `Temperature Sensor - ${temp}°C`,
                icon: {
                    url: 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20">
                            <circle cx="12" cy="12" r="10" fill="${status === 'critical' ? '#ef4444' : status === 'warning' ? '#f59e0b' : '#10b981'}" stroke="#ffffff" stroke-width="2"/>
                            <text x="12" y="16" text-anchor="middle" fill="white" font-size="8" font-family="Arial">T</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(20, 20)
                }
            });
            
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="text-align: center;">
                        <h6><i class="fas fa-thermometer-half"></i> Temperature Sensor</h6>
                        <p><strong>Temperature:</strong> ${temp}°C</p>
                        <p><strong>Status:</strong> <span style="color: ${status === 'critical' ? '#ef4444' : status === 'warning' ? '#f59e0b' : '#10b981'};">${status.toUpperCase()}</span></p>
                        <p><strong>Pond:</strong> ${pond.name}</p>
                    </div>
                `
            });
            
            sensorMarker.addListener('click', () => {
                infoWindow.open(map, sensorMarker);
            });
        }
    });
}

// Water Quality Markers
function toggleWaterQuality() {
    if (waterQualityMarkers.length > 0) {
        waterQualityMarkers.forEach(marker => {
            marker.setVisible(!marker.getVisible());
        });
    } else {
        addWaterQualityMarkers();
    }
}

function addWaterQualityMarkers() {
    pondsData.forEach((pond, index) => {
        if (index % 2 === 0) { // Add to every 2nd pond
            const ph = (6.8 + Math.random() * 1.4).toFixed(1);
            const oxygen = (5.5 + Math.random() * 3).toFixed(1);
            const status = ph < 6.5 || ph > 8.5 || oxygen < 6 ? 'warning' : 'good';
            
            const wqMarker = new google.maps.Marker({
                position: { 
                    lat: pond.latitude - 0.0005, 
                    lng: pond.longitude - 0.0005 
                },
                map: map,
                title: `Water Quality - pH: ${ph}, O₂: ${oxygen}mg/L`,
                icon: {
                    url: 'data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18">
                            <circle cx="12" cy="12" r="9" fill="${status === 'warning' ? '#f59e0b' : '#06b6d4'}" stroke="#ffffff" stroke-width="2"/>
                            <text x="12" y="16" text-anchor="middle" fill="white" font-size="8" font-family="Arial">WQ</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(18, 18)
                }
            });
            
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="text-align: center;">
                        <h6><i class="fas fa-flask"></i> Water Quality</h6>
                        <p><strong>pH Level:</strong> ${ph}</p>
                        <p><strong>Dissolved O₂:</strong> ${oxygen} mg/L</p>
                        <p><strong>Status:</strong> <span style="color: ${status === 'warning' ? '#f59e0b' : '#06b6d4'};">${status.toUpperCase()}</span></p>
                        <p><strong>Pond:</strong> ${pond.name}</p>
                    </div>
                `
            });
            
            wqMarker.addListener('click', () => {
                infoWindow.open(map, wqMarker);
            });
            
            waterQualityMarkers.push(wqMarker);
        }
    });
}

// Worker Trails
function toggleWorkerTrails() {
    if (workerTrails.length > 0) {
        workerTrails.forEach(trail => {
            trail.setVisible(!trail.getVisible());
        });
    } else {
        createWorkerTrails();
    }
}

function createWorkerTrails() {
    workersData.forEach(worker => {
        // Generate a simple trail path
        const trailCoords = [];
        const currentLat = worker.latitude;
        const currentLng = worker.longitude;
        
        // Create a trail with 10 points over the last hour
        for (let i = 0; i < 10; i++) {
            const offset = i * 0.0002; // Small movements
            trailCoords.push({
                lat: currentLat - offset + (Math.random() - 0.5) * 0.0001,
                lng: currentLng - offset + (Math.random() - 0.5) * 0.0001
            });
        }
        
        const trailPath = new google.maps.Polyline({
            path: trailCoords,
            geodesic: true,
            strokeColor: worker.status === 'working' ? '#ef4444' : '#10b981',
            strokeOpacity: 0.8,
            strokeWeight: 3,
            map: map
        });
        
        workerTrails.push(trailPath);
    });
    
    console.log(`Created ${workerTrails.length} worker trails`);
}

// Alert Zones
function toggleAlertZones() {
    if (alertZones.length > 0) {
        alertZones.forEach(zone => {
            zone.setVisible(!zone.getVisible());
        });
    } else {
        addAlertZones();
    }
}

function addAlertZones() {
    // Add some emergency/alert zones
    const alertAreas = [
        {
            center: { lat: centerLat + 0.01, lng: centerLng + 0.01 },
            radius: 200,
            type: 'emergency',
            description: 'Emergency Assembly Point'
        },
        {
            center: { lat: centerLat - 0.005, lng: centerLng + 0.015 },
            radius: 150,
            type: 'restricted',
            description: 'Equipment Maintenance Zone'
        },
        {
            center: { lat: centerLat + 0.008, lng: centerLng - 0.01 },
            radius: 100,
            type: 'warning',
            description: 'Low Oxygen Alert Zone'
        }
    ];
    
    alertAreas.forEach(alert => {
        const alertZone = new google.maps.Circle({
            strokeColor: alert.type === 'emergency' ? '#dc2626' : alert.type === 'restricted' ? '#f59e0b' : '#ef4444',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: alert.type === 'emergency' ? '#dc2626' : alert.type === 'restricted' ? '#f59e0b' : '#ef4444',
            fillOpacity: 0.3,
            map: map,
            center: alert.center,
            radius: alert.radius
        });
        
        // Add pulsing effect
        let growing = true;
        setInterval(() => {
            const currentRadius = alertZone.getRadius();
            if (growing) {
                alertZone.setRadius(currentRadius + 5);
                if (currentRadius > alert.radius + 20) growing = false;
            } else {
                alertZone.setRadius(currentRadius - 5);
                if (currentRadius < alert.radius - 20) growing = true;
            }
        }, 200);
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="text-align: center;">
                    <h6><i class="fas fa-exclamation-triangle"></i> ${alert.type.toUpperCase()} ZONE</h6>
                    <p>${alert.description}</p>
                    <p><strong>Radius:</strong> ${alert.radius}m</p>
                </div>
            `
        });
        
        alertZone.addListener('click', (event) => {
            infoWindow.setPosition(event.latLng);
            infoWindow.open(map);
        });
        
        alertZones.push(alertZone);
    });
}

// Search functionality
function performMapSearch(query) {
    if (query.length < 2) {
        // Reset all markers to visible if search is cleared
        [...farmMarkers, ...pondMarkers, ...workerMarkers].forEach(marker => {
            marker.setVisible(true);
        });
        return;
    }
    
    query = query.toLowerCase();
    
    // Search and filter markers
    farmMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && farmsVisible);
    });
    
    pondMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && pondsVisible);
    });
    
    workerMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && workersVisible);
    });
}

// Time controls
function updateTimeView(hours) {
    const timeDisplay = document.getElementById('timeDisplay');
    if (hours == 24) {
        timeDisplay.textContent = 'Now';
    } else {
        const time = new Date();
        time.setHours(time.getHours() - (24 - hours));
        timeDisplay.textContent = time.toLocaleTimeString();
    }
    
    // Here you could filter data based on time
    console.log(`Viewing data from ${hours} hours ago`);
}

// Real-time mode
function toggleRealTimeMode() {
    isRealTimeMode = !isRealTimeMode;
    const button = document.getElementById('realtimeText');
    
    if (isRealTimeMode) {
        button.textContent = 'Disable Real-time';
        realTimeInterval = setInterval(refreshData, 5000); // Update every 5 seconds
        console.log('Real-time mode enabled');
    } else {
        button.textContent = 'Enable Real-time';
        if (realTimeInterval) {
            clearInterval(realTimeInterval);
            realTimeInterval = null;
        }
        console.log('Real-time mode disabled');
    }
}

// Environmental data updates
function updateEnvironmentalData() {
    // Simulate real environmental data
    const waterTemp = (26 + Math.random() * 4).toFixed(1);
    const phLevel = (6.8 + Math.random() * 1.4).toFixed(1);
    const oxygenLevel = (5.5 + Math.random() * 3).toFixed(1);
    const salinity = (14 + Math.random() * 2).toFixed(0);
    const airTemp = (27 + Math.random() * 4).toFixed(1);
    const windSpeed = (2 + Math.random() * 3).toFixed(1);
    
    // Update display
    document.getElementById('waterTemp').textContent = `${waterTemp}°C`;
    document.getElementById('phLevel').textContent = phLevel;
    document.getElementById('oxygenLevel').textContent = `${oxygenLevel} mg/L`;
    document.getElementById('salinity').textContent = `${salinity} ppt`;
    document.getElementById('airTemp').textContent = `${airTemp}°C`;
    document.getElementById('windSpeed').textContent = `${windSpeed} m/s`;
    
    // Update status indicators
    updateStatusIndicator('waterTemp', waterTemp, 26, 30);
    updateStatusIndicator('phLevel', phLevel, 6.5, 8.5);
    updateStatusIndicator('oxygenLevel', oxygenLevel, 6, 9);
}

function updateStatusIndicator(elementId, value, minGood, maxGood) {
    const element = document.getElementById(elementId).parentElement;
    const indicator = element.querySelector('.metric-status');
    
    if (value < minGood || value > maxGood) {
        indicator.className = 'metric-status status-critical';
    } else if (value < minGood + 0.5 || value > maxGood - 0.5) {
        indicator.className = 'metric-status status-warning';
    } else {
        indicator.className = 'metric-status status-good';
    }
}

// Data refresh
function refreshData() {
    console.log('Refreshing map data...');
    
    // Simulate worker position updates
    workersData.forEach((worker, index) => {
        if (Math.random() > 0.7) { // 30% chance of movement
            worker.latitude += (Math.random() - 0.5) * 0.0001;
            worker.longitude += (Math.random() - 0.5) * 0.0001;
            
            // Update marker position
            if (workerMarkers[index]) {
                workerMarkers[index].setPosition({
                    lat: worker.latitude,
                    lng: worker.longitude
                });
            }
        }
    });
    
    // Update environmental data
    updateEnvironmentalData();
    
    console.log('Data refreshed');
}
</script>

<!-- Load Google Maps API with Visualization Library -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=visualization&callback=initMap"
    onerror="handleMapError()">
</script>
{% endblock %}
