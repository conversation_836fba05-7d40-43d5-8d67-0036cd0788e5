{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .farm-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .farm-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: header-sweep 6s infinite;
        }

        @keyframes header-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        #map {
            height: 300px;
            border-radius: 15px;
            margin-bottom: 1rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .form-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            transition: all 0.4s ease;
        }

        .form-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .form-section-title {
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 2px solid #667eea;
            color: #667eea;
            font-weight: 600;
        }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="farm-header">
        <div class="position-relative">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2"><i class="fas fa-building me-3"></i>{{ title }}</h1>
                    <p class="mb-0" style="opacity: 0.9;">Create and manage your shrimp farming facilities</p>
                </div>
                <div>
                    <a href="{% url 'ponds:farm_list' %}" class="action-btn" style="background: linear-gradient(45deg, #74b9ff, #0984e3);">
                        <i class="fas fa-arrow-left"></i> Back to Farms
                    </a>
                </div>
            </div>
        </div>
    </div>
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-1">
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_list' %}">Farms</a></li>
                    {% if farm %}
                        <li class="breadcrumb-item"><a href="{% url 'ponds:farm_detail' farm.pk %}">{{ farm.name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    {% else %}
                        <li class="breadcrumb-item active" aria-current="page">Create</li>
                    {% endif %}
                </ol>
            </nav>
            <h1 class="h3 mb-0">{{ title }}</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <form method="post" id="farmForm">
                {% csrf_token %}
                
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">Basic Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="name" class="form-label">Farm Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" value="{{ farm.name|default:'' }}" required>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" value="{{ farm.location|default:'' }}">
                                <div class="form-text">City, state, or region</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="size" class="form-label">Size (hectares) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="size" name="size" value="{{ farm.size|default:'' }}" step="0.01" min="0" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="4">{{ farm.description|default:'' }}</textarea>
                        </div>
                    </div>
                </div>
                  <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Location</h5>
                    </div>
                    <div class="card-body">
                        <div id="map"></div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="latitude" class="form-label">Latitude</label>
                                <input type="number" class="form-control" id="latitude" name="latitude" value="{{ farm.latitude|default:'' }}" step="0.000001">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="longitude" class="form-label">Longitude</label>
                                <input type="number" class="form-control" id="longitude" name="longitude" value="{{ farm.longitude|default:'' }}" step="0.000001">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2">{{ farm.address|default:'' }}</textarea>
                        </div>
                    </div>
                </div>
                
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">Contact Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="contact_person" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="contact_person" name="contact_person" value="{{ farm.contact_person|default:'' }}">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contact_phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" value="{{ farm.contact_phone|default:'' }}">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="contact_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" value="{{ farm.contact_email|default:'' }}">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="{% if farm %}{% url 'ponds:farm_detail' farm.pk %}{% else %}{% url 'ponds:farm_list' %}{% endif %}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i> {% if farm %}Update{% else %}Create{% endif %} Farm
                    </button>
                </div>
            </form>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Help</h5>
                </div>
                <div class="card-body">
                    <h6>Farm Information</h6>
                    <p class="text-muted small">Create a farm to organize your ponds. Each farm can have multiple ponds and users assigned to it.</p>
                    
                    <h6>Location</h6>
                    <p class="text-muted small">Click on the map to set the farm's location coordinates. This helps with weather forecasts and spatial analysis. For a more comprehensive view, use the Unified Map.</p>
                    
                    <h6>Contact Information</h6>
                    <p class="text-muted small">Add contact details for the farm manager or owner. This information is useful for communication and reporting.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize map
        const map = L.map('map');
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Get latitude and longitude inputs
        const latInput = document.getElementById('latitude');
        const lngInput = document.getElementById('longitude');
        
        // Initialize marker
        let marker;
        
        // Set initial view
        if (latInput.value && lngInput.value) {
            // If coordinates are already set, center map there
            const lat = parseFloat(latInput.value);
            const lng = parseFloat(lngInput.value);
            map.setView([lat, lng], 15);
            
            // Add marker
            marker = L.marker([lat, lng], {draggable: true}).addTo(map);
            
            // Update inputs when marker is dragged
            marker.on('dragend', function(event) {
                const position = marker.getLatLng();
                latInput.value = position.lat.toFixed(6);
                lngInput.value = position.lng.toFixed(6);
            });
        } else {
            // Default view (center of the country or region)
            map.setView([0, 0], 2);
        }
        
        // Click on map to set marker
        map.on('click', function(e) {
            const lat = e.latlng.lat.toFixed(6);
            const lng = e.latlng.lng.toFixed(6);
            
            // Update inputs
            latInput.value = lat;
            lngInput.value = lng;
            
            // Update or create marker
            if (marker) {
                marker.setLatLng(e.latlng);
            } else {
                marker = L.marker(e.latlng, {draggable: true}).addTo(map);
                
                // Update inputs when marker is dragged
                marker.on('dragend', function(event) {
                    const position = marker.getLatLng();
                    latInput.value = position.lat.toFixed(6);
                    lngInput.value = position.lng.toFixed(6);
                });
            }
        });
        
        // Update map when inputs change
        latInput.addEventListener('change', updateMarker);
        lngInput.addEventListener('change', updateMarker);
        
        function updateMarker() {
            const lat = parseFloat(latInput.value);
            const lng = parseFloat(lngInput.value);
            
            if (!isNaN(lat) && !isNaN(lng)) {
                // Update or create marker
                if (marker) {
                    marker.setLatLng([lat, lng]);
                } else {
                    marker = L.marker([lat, lng], {draggable: true}).addTo(map);
                    
                    // Update inputs when marker is dragged
                    marker.on('dragend', function(event) {
                        const position = marker.getLatLng();
                        latInput.value = position.lat.toFixed(6);
                        lngInput.value = position.lng.toFixed(6);
                    });
                }
                
                // Center map on marker
                map.setView([lat, lng], 15);
            }
        }
        
        // Fix map display issue
        setTimeout(function() {
            map.invalidateSize();
        }, 100);
    });
</script>
{% endblock %}