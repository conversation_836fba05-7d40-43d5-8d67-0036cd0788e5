{% extends 'website/base.html' %}

{% block content %}
<!-- Features Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Powerful Features
                </h1>
                <p class="lead mb-0" data-aos="fade-up" data-aos-delay="100">
                    Comprehensive aquaculture management tools designed for modern shrimp farming operations
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Detailed Features Section -->
<section class="py-5">
    <div class="container">
        {% for key, feature in features.items %}
        <div class="row align-items-center mb-5 {% if forloop.counter|divisibleby:2 %}flex-row-reverse{% endif %}">
            <div class="col-lg-6">
                <div {% if forloop.counter|divisibleby:2 %}data-aos="fade-left"{% else %}data-aos="fade-right"{% endif %}>
                    <div class="feature-icon d-inline-block mb-3" 
                         style="background: linear-gradient(135deg, 
                         {% if forloop.counter0 == 0 %}#667eea, #764ba2{% endif %}
                         {% if forloop.counter0 == 1 %}#10b981, #059669{% endif %}
                         {% if forloop.counter0 == 2 %}#f59e0b, #d97706{% endif %}
                         {% if forloop.counter0 == 3 %}#8b5cf6, #7c3aed{% endif %}
                         {% if forloop.counter0 == 4 %}#ef4444, #dc2626{% endif %}
                         {% if forloop.counter0 == 5 %}#06b6d4, #0891b2{% endif %});">
                        <i class="{% if forloop.counter0 == 0 %}fas fa-chart-line{% endif %}
                                  {% if forloop.counter0 == 1 %}fas fa-tint{% endif %}
                                  {% if forloop.counter0 == 2 %}fas fa-robot{% endif %}
                                  {% if forloop.counter0 == 3 %}fas fa-utensils{% endif %}
                                  {% if forloop.counter0 == 4 %}fas fa-cloud-sun{% endif %}
                                  {% if forloop.counter0 == 5 %}fas fa-users{% endif %}"></i>
                    </div>
                    <h2 class="display-6 fw-bold mb-3">{{ feature.title }}</h2>
                    <p class="lead text-muted mb-4">{{ feature.description }}</p>
                    
                    <div class="row g-3">
                        {% for benefit in feature.benefits %}
                        <div class="col-md-6">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>{{ benefit }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="mt-4">
                        <a href="{% url 'website:demo' %}" class="btn btn-gradient">
                            <i class="fas fa-play me-2"></i>See in Action
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="text-center" {% if forloop.counter|divisibleby:2 %}data-aos="fade-right"{% else %}data-aos="fade-left"{% endif %}>
                    <div class="glass p-5 d-inline-block">
                        <div class="feature-demo">
                            {% if forloop.counter0 == 0 %}
                            <!-- IoT Monitoring Demo -->
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="glass-dark p-3 text-center">
                                        <i class="fas fa-thermometer-half text-warning mb-2" style="font-size: 2rem;"></i>
                                        <div class="text-white">
                                            <div class="h5 mb-0">28.5°C</div>
                                            <small>Temperature</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="glass-dark p-3 text-center">
                                        <i class="fas fa-tint text-info mb-2" style="font-size: 2rem;"></i>
                                        <div class="text-white">
                                            <div class="h5 mb-0">7.2 pH</div>
                                            <small>Acidity</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="glass-dark p-3 text-center">
                                        <i class="fas fa-wind text-success mb-2" style="font-size: 2rem;"></i>
                                        <div class="text-white">
                                            <div class="h5 mb-0">5.8 mg/L</div>
                                            <small>Oxygen</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="glass-dark p-3 text-center">
                                        <i class="fas fa-water text-primary mb-2" style="font-size: 2rem;"></i>
                                        <div class="text-white">
                                            <div class="h5 mb-0">28.3 ppt</div>
                                            <small>Salinity</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% elif forloop.counter0 == 1 %}
                            <!-- Water Quality Demo -->
                            <div class="text-center">
                                <i class="fas fa-tint text-primary" style="font-size: 6rem; margin-bottom: 1rem;"></i>
                                <div class="text-white">
                                    <h4>Water Quality Index</h4>
                                    <div class="h2 text-success">Excellent</div>
                                    <small>All parameters within optimal range</small>
                                </div>
                            </div>
                            {% elif forloop.counter0 == 2 %}
                            <!-- AI Disease Detection Demo -->
                            <div class="text-center">
                                <i class="fas fa-robot text-success" style="font-size: 6rem; margin-bottom: 1rem;"></i>
                                <div class="text-white">
                                    <h4>AI Analysis</h4>
                                    <div class="h2 text-success">Healthy</div>
                                    <small>No diseases detected</small>
                                </div>
                            </div>
                            {% elif forloop.counter0 == 3 %}
                            <!-- Feed Management Demo -->
                            <div class="text-center">
                                <i class="fas fa-utensils text-warning" style="font-size: 6rem; margin-bottom: 1rem;"></i>
                                <div class="text-white">
                                    <h4>Next Feeding</h4>
                                    <div class="h2 text-warning">2:30 PM</div>
                                    <small>15.2 kg scheduled</small>
                                </div>
                            </div>
                            {% elif forloop.counter0 == 4 %}
                            <!-- Weather Integration Demo -->
                            <div class="text-center">
                                <i class="fas fa-cloud-sun text-info" style="font-size: 6rem; margin-bottom: 1rem;"></i>
                                <div class="text-white">
                                    <h4>Weather Forecast</h4>
                                    <div class="h2 text-info">Partly Cloudy</div>
                                    <small>Rain expected tomorrow</small>
                                </div>
                            </div>
                            {% else %}
                            <!-- Labor Management Demo -->
                            <div class="text-center">
                                <i class="fas fa-users text-primary" style="font-size: 6rem; margin-bottom: 1rem;"></i>
                                <div class="text-white">
                                    <h4>Active Workers</h4>
                                    <div class="h2 text-primary">12/15</div>
                                    <small>3 on break</small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</section>

<!-- Technology Stack Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-4 fw-bold" data-aos="fade-up">
                    Built with Cutting-Edge Technology
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Enterprise-grade infrastructure for reliable aquaculture management
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up">
                    <i class="fas fa-microchip text-primary mb-3" style="font-size: 3rem;"></i>
                    <h5>IoT Sensors</h5>
                    <p class="text-muted">Advanced sensor networks for real-time data collection</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="100">
                    <i class="fas fa-brain text-success mb-3" style="font-size: 3rem;"></i>
                    <h5>AI & Machine Learning</h5>
                    <p class="text-muted">Intelligent algorithms for predictive analytics</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="200">
                    <i class="fas fa-cloud text-info mb-3" style="font-size: 3rem;"></i>
                    <h5>Cloud Computing</h5>
                    <p class="text-muted">Scalable cloud infrastructure for global access</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="300">
                    <i class="fas fa-shield-alt text-warning mb-3" style="font-size: 3rem;"></i>
                    <h5>Security</h5>
                    <p class="text-muted">Enterprise-grade security and data protection</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div data-aos="fade-right">
                    <h2 class="display-5 fw-bold mb-3">
                        Experience the Future of Aquaculture
                    </h2>
                    <p class="lead mb-0">
                        See how our features can transform your shrimp farming operations
                    </p>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div data-aos="fade-left">
                    <a href="{% url 'website:demo' %}" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-play me-2"></i>Live Demo
                    </a>
                    <a href="{% url 'website:contact' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Contact Sales
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
