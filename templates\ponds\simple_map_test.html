<!DOCTYPE html>
<html>
<head>
    <title>Simple Map Test</title>
    <style>
        #map {
            height: 500px;
            width: 100%;
            background: #f0f0f0;
            border: 1px solid #ccc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .loading { background: #fff3cd; }
        .success { background: #d1edcc; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <h1>Simple Google Maps Test</h1>
    <div id="status" class="status loading">Initializing...</div>
    <div id="map"></div>

    <script>
        console.log('Simple map test starting...');
        
        function updateStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            console.log(message);
        }

        function initMap() {
            console.log('initMap called by Google Maps API');
            updateStatus('Google Maps API loaded successfully', 'success');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 13.0827, lng: 80.2707 },
                    mapTypeId: 'roadmap'
                });
                
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai, India'
                });
                
                updateStatus('Map created successfully with marker!', 'success');
                console.log('Map and marker created successfully');
                
            } catch (error) {
                updateStatus('Error creating map: ' + error.message, 'error');
                console.error('Error creating map:', error);
            }
        }

        function handleMapError() {
            updateStatus('Failed to load Google Maps API', 'error');
            console.error('Google Maps API failed to load');
        }

        // Check if Google Maps loads
        setTimeout(() => {
            if (typeof google === 'undefined') {
                updateStatus('Timeout: Google Maps API did not load within 10 seconds', 'error');
            }
        }, 10000);
        
        updateStatus('Loading Google Maps API...', 'loading');
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
        onerror="handleMapError()">
    </script>
</body>
</html>
