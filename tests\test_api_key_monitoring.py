import unittest
from unittest.mock import patch, MagicMock, call
import os
import sys
import json
import tempfile
from io import StringIO
from django.core.management import call_command
from django.test import TestCase, override_settings
from django.core import mail

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the command to test
from core.management.commands.schedule_api_key_monitoring import Command as MonitoringCommand
from core.api_key_checker import check_api_key, check_environment


class TestAPIKeyMonitoring(TestCase):
    """Test suite for the API key monitoring command."""

    def setUp(self):
        """Set up test environment."""
        # Create a temporary log directory
        self.temp_dir = tempfile.TemporaryDirectory()
        self.log_dir = os.path.join(self.temp_dir.name, 'logs')
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Sample API key results for mocking
        self.valid_key_result = {
            'key': 'AIzaSyValidTestKey12345678901234567890',
            'key_length': 39,
            'status': 'success',
            'message': 'API key is valid and working',
            'apis_enabled': ['Static Maps API', 'JavaScript Maps API'],
            'restrictions': {},
            'cached': False,
            'timestamp': **********
        }
        
        self.invalid_key_result = {
            'key': 'AIzaSyInvalidTestKey123456789012345678',
            'key_length': 39,
            'status': 'error',
            'message': 'API key is invalid',
            'apis_enabled': [],
            'restrictions': {},
            'cached': False,
            'timestamp': **********
        }
        
        self.warning_key_result = {
            'key': 'AIzaSyWarningTestKey12345678901234567',
            'key_length': 39,
            'status': 'warning',
            'message': 'API key has domain restrictions',
            'apis_enabled': ['Static Maps API'],
            'restrictions': {'domains': ['localhost']},
            'cached': False,
            'timestamp': **********
        }
        
        # Sample environment check results
        self.env_match_result = {
            'settings_key': 'AIzaSyValidTestKey12345678901234567890',
            'env_key': 'AIzaSyValidTestKey12345678901234567890',
            'vite_key': 'AIzaSyValidTestKey12345678901234567890',
            'map_provider': 'google',
            'match': True,
            'message': 'All API keys match'
        }
        
        self.env_mismatch_result = {
            'settings_key': 'AIzaSyValidTestKey12345678901234567890',
            'env_key': 'AIzaSyDifferentKey12345678901234567890',
            'vite_key': 'AIzaSyValidTestKey12345678901234567890',
            'map_provider': 'google',
            'match': False,
            'message': 'API keys do not match'
        }

    def tearDown(self):
        """Clean up after tests."""
        self.temp_dir.cleanup()

    @patch('core.management.commands.schedule_api_key_monitoring.check_api_key')
    @patch('core.management.commands.schedule_api_key_monitoring.check_environment')
    @override_settings(BASE_DIR=tempfile.gettempdir())
    def test_monitoring_command_with_valid_key(self, mock_check_env, mock_check_key):
        """Test monitoring command with a valid API key."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command
        out = StringIO()
        call_command('schedule_api_key_monitoring', stdout=out)
        
        # Verify output contains success message
        output = out.getvalue()
        self.assertIn('API Key Status: success', output)
        self.assertIn('APIs Detected: Static Maps API, JavaScript Maps API', output)
        self.assertIn('All API keys match', output)
        
        # Verify the check functions were called
        mock_check_key.assert_called_once()
        mock_check_env.assert_called_once()

    @patch('core.management.commands.schedule_api_key_monitoring.check_api_key')
    @patch('core.management.commands.schedule_api_key_monitoring.check_environment')
    @patch('core.management.commands.schedule_api_key_monitoring.send_mail')
    @override_settings(
        BASE_DIR=tempfile.gettempdir(),
        DEFAULT_FROM_EMAIL='<EMAIL>'
    )
    def test_monitoring_command_with_error_sends_alert(self, mock_send_mail, mock_check_env, mock_check_key):
        """Test monitoring command sends alert when API key has errors."""
        # Mock the API key check to return an error
        mock_check_key.return_value = self.invalid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with email alert
        out = StringIO()
        call_command('schedule_api_key_monitoring', alert_email='<EMAIL>', stdout=out)
        
        # Verify output contains error message
        output = out.getvalue()
        self.assertIn('API Key Status: error', output)
        self.assertIn('API key is invalid', output)
        
        # Verify email was sent
        mock_send_mail.assert_called_once()
        args = mock_send_mail.call_args[0]
        self.assertIn('ERROR', args[0])  # Subject contains ERROR
        self.assertIn('API key is invalid', args[1])  # Body contains the error message
        self.assertEqual('<EMAIL>', args[2])  # From email
        self.assertEqual(['<EMAIL>'], args[3])  # To email

    @patch('core.management.commands.schedule_api_key_monitoring.check_api_key')
    @patch('core.management.commands.schedule_api_key_monitoring.check_environment')
    @patch('core.management.commands.schedule_api_key_monitoring.send_mail')
    @override_settings(
        BASE_DIR=tempfile.gettempdir(),
        DEFAULT_FROM_EMAIL='<EMAIL>'
    )
    def test_monitoring_command_with_warning_threshold(self, mock_send_mail, mock_check_env, mock_check_key):
        """Test monitoring command respects warning threshold."""
        # Mock the API key check to return a warning
        mock_check_key.return_value = self.warning_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with email alert and warning threshold
        out = StringIO()
        call_command(
            'schedule_api_key_monitoring', 
            alert_email='<EMAIL>', 
            threshold='warning',
            stdout=out
        )
        
        # Verify output contains warning message
        output = out.getvalue()
        self.assertIn('API Key Status: warning', output)
        self.assertIn('API key has domain restrictions', output)
        
        # Verify email was sent (warning meets the warning threshold)
        mock_send_mail.assert_called_once()
        args = mock_send_mail.call_args[0]
        self.assertIn('WARNING', args[0])  # Subject contains WARNING
        
        # Reset mock and test with error threshold
        mock_send_mail.reset_mock()
        out = StringIO()
        call_command(
            'schedule_api_key_monitoring', 
            alert_email='<EMAIL>', 
            threshold='error',  # Only send for errors
            stdout=out
        )
        
        # Verify email was NOT sent (warning doesn't meet the error threshold)
        mock_send_mail.assert_not_called()

    @patch('core.management.commands.schedule_api_key_monitoring.check_api_key')
    @patch('core.management.commands.schedule_api_key_monitoring.check_environment')
    @override_settings(BASE_DIR=tempfile.gettempdir())
    def test_monitoring_command_logs_results(self, mock_check_env, mock_check_key):
        """Test monitoring command logs results to file."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Set up log file path
        log_file = os.path.join(self.log_dir, 'api_key_monitoring.log')
        
        # Patch the log file path
        with patch('os.path.join', return_value=log_file):
            # Call the command
            call_command('schedule_api_key_monitoring')
            
            # Verify log file was created
            self.assertTrue(os.path.exists(log_file))
            
            # Verify log contains the expected data
            with open(log_file, 'r') as f:
                log_entry = json.loads(f.readline().strip())
                self.assertEqual(log_entry['api_key_status'], 'success')
                self.assertEqual(log_entry['message'], 'API key is valid and working')
                self.assertEqual(log_entry['apis_detected'], ['Static Maps API', 'JavaScript Maps API'])
                self.assertEqual(log_entry['environment_match'], True)
                self.assertFalse(log_entry['cached'])

    @patch('core.management.commands.schedule_api_key_monitoring.check_api_key')
    @patch('core.management.commands.schedule_api_key_monitoring.check_environment')
    @override_settings(BASE_DIR=tempfile.gettempdir())
    def test_monitoring_command_with_no_cache(self, mock_check_env, mock_check_key):
        """Test monitoring command with no_cache option."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with no_cache
        call_command('schedule_api_key_monitoring', no_cache=True)
        
        # Verify the check function was called with correct parameters
        mock_check_key.assert_called_once_with(use_cache=False, force_refresh=True)

    @patch('core.management.commands.schedule_api_key_monitoring.send_mail')
    @override_settings(DEFAULT_FROM_EMAIL='<EMAIL>')
    def test_monitoring_command_email_error_handling(self, mock_send_mail):
        """Test monitoring command handles email sending errors gracefully."""
        # Mock the send_mail function to raise an exception
        mock_send_mail.side_effect = Exception("SMTP error")
        
        # Create command instance
        command = MonitoringCommand()
        
        # Patch check_api_key and check_environment to return known values
        with patch('core.api_key_checker.check_api_key', return_value=self.invalid_key_result), \
             patch('core.api_key_checker.check_environment', return_value=self.env_match_result), \
             patch('sys.stdout', new=StringIO()):
            
            # Call handle method directly with the alert email
            command.handle(alert_email='<EMAIL>', threshold='error', no_cache=False)
            
            # Verify send_mail was called
            mock_send_mail.assert_called_once()
            
            # No exception should have been raised from the command


if __name__ == '__main__':
    unittest.main()