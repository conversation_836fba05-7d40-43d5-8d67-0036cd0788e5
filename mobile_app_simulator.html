<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Shrimp Farm Field Worker App - Interactive Simulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .simulator-container {
            display: flex;
            gap: 40px;
            align-items: flex-start;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #1976D2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .screen-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            color: white;
        }

        .greeting {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .user-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .date {
            font-size: 14px;
            opacity: 0.8;
        }

        .offline-indicator {
            background: rgba(255, 152, 0, 0.9);
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-top: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .search-container {
            padding: 16px;
            background: white;
        }

        .search-box {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 16px;
        }

        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            font-weight: 600;
        }

        .progress-card {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-title {
            font-size: 16px;
            font-weight: 600;
        }

        .progress-rate {
            font-size: 24px;
            font-weight: 700;
            color: #4CAF50;
        }

        .progress-bar {
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            margin-bottom: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 85%;
            border-radius: 4px;
            animation: progressFill 2s ease-out;
        }

        @keyframes progressFill {
            from { width: 0%; }
            to { width: 85%; }
        }

        .quick-actions {
            padding: 0 16px 16px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            padding: 0 4px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .action-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .action-card:active {
            transform: scale(0.95);
        }

        .action-icon {
            font-size: 24px;
            color: #2196F3;
            margin-bottom: 8px;
        }

        .action-text {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .priority-tasks {
            padding: 0 16px 16px;
        }

        .task-card {
            background: white;
            margin-bottom: 8px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #F44336;
            cursor: pointer;
            transition: all 0.2s;
        }

        .task-card:hover {
            transform: translateX(4px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .task-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            flex: 1;
        }

        .priority-badge {
            background: #F44336;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }

        .task-details {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }

        .task-actions {
            display: flex;
            gap: 8px;
        }

        .task-btn {
            padding: 8px 12px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s;
        }

        .task-btn:hover {
            transform: translateY(-1px);
        }

        .btn-start {
            background: #2196F3;
            color: white;
        }

        .btn-cancel {
            background: #F44336;
            color: white;
        }

        .bottom-nav {
            height: 80px;
            background: white;
            display: flex;
            border-top: 1px solid #e0e0e0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .nav-item:hover {
            background: #f5f5f5;
        }

        .nav-item.active {
            color: #2196F3;
        }

        .nav-item:not(.active) {
            color: #757575;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 12px;
            font-weight: 600;
        }

        .screen {
            display: none;
        }

        .screen.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .controls-panel {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            width: 300px;
        }

        .controls-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            color: #333;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            font-size: 14px;
            font-weight: 600;
            color: #666;
            margin-bottom: 8px;
        }

        .control-btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            background: #2196F3;
            color: white;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s;
        }

        .control-btn:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        .control-btn.secondary {
            background: #f5f5f5;
            color: #333;
        }

        .control-btn.secondary:hover {
            background: #e0e0e0;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 16px 20px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
            max-width: 300px;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        .feature-highlight {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 14px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="simulator-container">
        <!-- Mobile Phone Simulator -->
        <div class="phone-container">
            <div class="phone-screen">
                <!-- Status Bar -->
                <div class="status-bar">
                    <div>🔋100% 📶 WiFi</div>
                    <div id="current-time">2:30 PM</div>
                </div>

                <!-- Dashboard Screen -->
                <div class="screen active" id="dashboard">
                    <div class="screen-content">
                        <div class="header">
                            <div class="greeting">Good Afternoon</div>
                            <div class="user-name">Field Worker</div>
                            <div class="date">Wednesday, January 17, 2025</div>
                            <div class="offline-indicator">
                                🔄 Working Offline - 3 pending sync
                            </div>
                        </div>

                        <div class="search-container">
                            <div class="search-box">
                                🔍 <input type="text" placeholder="Search tasks..." style="border: none; background: none; outline: none; flex: 1;">
                            </div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-card" style="background: #E3F2FD;" onclick="showNotification('📅 Viewing today\'s 12 tasks')">
                                <div class="stat-icon">📅</div>
                                <div class="stat-number">12</div>
                                <div class="stat-label">Today's Tasks</div>
                            </div>
                            <div class="stat-card" style="background: #FFF3E0;" onclick="showNotification('▶️ 3 tasks currently in progress')">
                                <div class="stat-icon">▶️</div>
                                <div class="stat-number">3</div>
                                <div class="stat-label">In Progress</div>
                            </div>
                            <div class="stat-card" style="background: #E8F5E8;" onclick="showNotification('✅ 8 tasks completed today!')">
                                <div class="stat-icon">✅</div>
                                <div class="stat-number">8</div>
                                <div class="stat-label">Completed</div>
                            </div>
                            <div class="stat-card" style="background: #FFEBEE;" onclick="showNotification('⚠️ 2 tasks are overdue - please prioritize')">
                                <div class="stat-icon">⚠️</div>
                                <div class="stat-number">2</div>
                                <div class="stat-label">Overdue</div>
                            </div>
                        </div>

                        <div class="progress-card">
                            <div class="progress-header">
                                <div class="progress-title">Completion Rate</div>
                                <div class="progress-rate">85%</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                            <div style="font-size: 12px; color: #666;">8 of 12 tasks completed</div>
                        </div>

                        <div class="quick-actions">
                            <div class="section-title">Quick Actions</div>
                            <div class="actions-grid">
                                <div class="action-card" onclick="showNotification('📝 Opening task creation form...')">
                                    <div class="action-icon">➕</div>
                                    <div class="action-text">Create Task</div>
                                </div>
                                <div class="action-card" onclick="showNotification('📷 Activating QR code scanner...')">
                                    <div class="action-icon">📷</div>
                                    <div class="action-text">Scan QR</div>
                                </div>
                                <div class="action-card" onclick="showNotification('💧 Starting water quality test procedure...')">
                                    <div class="action-icon">💧</div>
                                    <div class="action-text">Water Test</div>
                                </div>
                                <div class="action-card" onclick="showNotification('📊 Loading performance reports...')">
                                    <div class="action-icon">📊</div>
                                    <div class="action-text">Reports</div>
                                </div>
                            </div>
                        </div>

                        <div class="priority-tasks">
                            <div class="section-title">Priority Tasks</div>
                            <div class="task-card" onclick="showNotification('📋 Opening Water Quality Check details...')">
                                <div class="task-header">
                                    <div class="task-title">💧 Water Quality Check</div>
                                    <div class="priority-badge">URGENT</div>
                                </div>
                                <div class="task-details">📍 Pond A-1 • ⏰ Due: 14:00 • ⏱️ 30 min</div>
                                <div class="task-actions">
                                    <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Water Quality Check')">
                                        ▶️ Start
                                    </button>
                                    <button class="task-btn btn-cancel" onclick="event.stopPropagation(); cancelTask('Water Quality Check')">
                                        ❌ Cancel
                                    </button>
                                </div>
                            </div>

                            <div class="task-card" style="border-left-color: #FF9800;" onclick="showNotification('🔧 Opening Equipment Maintenance details...')">
                                <div class="task-header">
                                    <div class="task-title">🔧 Equipment Maintenance</div>
                                    <div class="priority-badge" style="background: #FF9800;">HIGH</div>
                                </div>
                                <div class="task-details">📍 Pump Station • ⏰ Due: 16:30 • ⏱️ 1 hour</div>
                                <div class="task-actions">
                                    <button class="task-btn btn-start" onclick="event.stopPropagation(); startTask('Equipment Maintenance')">
                                        ▶️ Start
                                    </button>
                                    <button class="task-btn btn-cancel" onclick="event.stopPropagation(); cancelTask('Equipment Maintenance')">
                                        ❌ Cancel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bottom Navigation -->
                <div class="bottom-nav">
                    <div class="nav-item active" onclick="switchScreen('dashboard', this)">
                        <div class="nav-icon">📊</div>
                        <div class="nav-label">Dashboard</div>
                    </div>
                    <div class="nav-item" onclick="showNotification('📋 Task list feature coming soon...')">
                        <div class="nav-icon">📋</div>
                        <div class="nav-label">Tasks</div>
                    </div>
                    <div class="nav-item" onclick="showNotification('📷 QR Scanner activating...')">
                        <div class="nav-icon">📷</div>
                        <div class="nav-label">Scanner</div>
                    </div>
                    <div class="nav-item" onclick="showNotification('📈 Loading analytics dashboard...')">
                        <div class="nav-icon">📈</div>
                        <div class="nav-label">Reports</div>
                    </div>
                    <div class="nav-item" onclick="showNotification('👤 Opening user profile...')">
                        <div class="nav-icon">👤</div>
                        <div class="nav-label">Profile</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Controls Panel -->
        <div class="controls-panel">
            <div class="controls-title">🎮 App Simulator Controls</div>
            
            <div class="feature-highlight">
                <div class="feature-title">📱 Interactive Demo</div>
                <div class="feature-desc">Click on any element in the mobile screen to see interactive features!</div>
            </div>

            <div class="control-group">
                <div class="control-label">🎯 Quick Actions</div>
                <button class="control-btn" onclick="simulateTaskStart()">▶️ Start Urgent Task</button>
                <button class="control-btn" onclick="simulateTaskComplete()">✅ Complete Task</button>
                <button class="control-btn secondary" onclick="simulateSync()">🔄 Sync Data</button>
            </div>

            <div class="control-group">
                <div class="control-label">📊 App Features</div>
                <button class="control-btn" onclick="showFeature('offline')">🔄 Offline Mode</button>
                <button class="control-btn" onclick="showFeature('camera')">📷 Photo Capture</button>
                <button class="control-btn" onclick="showFeature('gps')">📍 GPS Tracking</button>
                <button class="control-btn secondary" onclick="showFeature('analytics')">📈 Analytics</button>
            </div>

            <div class="control-group">
                <div class="control-label">🔧 Simulation Options</div>
                <button class="control-btn secondary" onclick="toggleOfflineMode()">📶 Toggle Network</button>
                <button class="control-btn secondary" onclick="addNewTask()">➕ Add New Task</button>
                <button class="control-btn secondary" onclick="showAppInfo()">ℹ️ App Info</button>
            </div>

            <div style="margin-top: 20px; padding: 16px; background: #f8f8f8; border-radius: 8px; font-size: 12px; color: #666;">
                <strong>💡 Tip:</strong> This is a fully interactive simulator of the Shrimp Farm Field Worker mobile app. All buttons and cards are clickable!
            </div>
        </div>
    </div>

    <script>
        // Update time every second
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            document.getElementById('current-time').textContent = timeString;
        }
        setInterval(updateTime, 1000);
        updateTime();

        function switchScreen(screenId, navElement) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            document.getElementById(screenId).classList.add('active');
            
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            navElement.classList.add('active');
        }

        function startTask(taskName) {
            showNotification(`🚀 Starting task: "${taskName}"\n⏱️ Timer activated\n📍 Location recorded`);
            
            // Simulate task starting
            setTimeout(() => {
                showNotification(`✅ Task "${taskName}" is now in progress`);
            }, 2000);
        }

        function cancelTask(taskName) {
            showNotification(`❌ Task "${taskName}" has been cancelled\n📝 Reason logged\n🔄 Sync queued`);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = message.replace(/\n/g, '<br>');
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        function simulateTaskStart() {
            showNotification(`🎯 Simulating task start...\n📱 Opening task completion form\n📸 Camera ready for photos\n📊 Measurement fields loaded`);
        }

        function simulateTaskComplete() {
            showNotification(`✅ Task completed successfully!\n📸 3 photos captured\n📊 All measurements recorded\n🔄 Syncing to server...`);
            
            setTimeout(() => {
                showNotification(`☁️ Task synced successfully!\n📈 Statistics updated\n🏆 Great job!`);
            }, 3000);
        }

        function simulateSync() {
            showNotification(`🔄 Starting sync process...\n📤 Uploading 3 pending tasks\n📸 Syncing 12 photos\n📊 Updating statistics`);
            
            setTimeout(() => {
                showNotification(`✅ Sync completed!\n📱 All data up to date\n🌐 Online mode active`);
                
                // Update offline indicator
                const indicator = document.querySelector('.offline-indicator');
                indicator.innerHTML = '✅ All data synchronized';
                indicator.style.background = 'rgba(76, 175, 80, 0.9)';
            }, 4000);
        }

        function showFeature(feature) {
            const features = {
                offline: `🔄 Offline Mode Features:\n• SQLite local database\n• Automatic sync when online\n• Photo caching\n• Conflict resolution`,
                camera: `📷 Photo Capture Features:\n• High-quality image capture\n• Automatic compression\n• GPS location tagging\n• Offline storage`,
                gps: `📍 GPS Tracking Features:\n• Automatic location logging\n• Geofencing for ponds\n• Route optimization\n• Location-based tasks`,
                analytics: `📈 Analytics Features:\n• Real-time performance metrics\n• Task completion trends\n• Productivity insights\n• Custom reports`
            };
            
            showNotification(features[feature] || 'Feature information not available');
        }

        function toggleOfflineMode() {
            const indicator = document.querySelector('.offline-indicator');
            const isOffline = indicator.textContent.includes('Offline');
            
            if (isOffline) {
                indicator.innerHTML = '🌐 Online - All systems connected';
                indicator.style.background = 'rgba(76, 175, 80, 0.9)';
                showNotification('📶 Network connection restored!\n🔄 Auto-sync activated\n☁️ Cloud features available');
            } else {
                indicator.innerHTML = '🔄 Working Offline - 3 pending sync';
                indicator.style.background = 'rgba(255, 152, 0, 0.9)';
                showNotification('📱 Switched to offline mode\n💾 Local storage active\n🔄 Changes queued for sync');
            }
        }

        function addNewTask() {
            showNotification(`➕ Creating new task...\n📝 Task form opened\n🎯 Priority: High\n📍 Location: Pond B-3\n⏰ Due: Today 18:00`);
            
            setTimeout(() => {
                showNotification(`✅ New task created!\n📋 "Feed Quality Check" added\n🔔 Notification sent\n📊 Dashboard updated`);
            }, 3000);
        }

        function showAppInfo() {
            showNotification(`📱 Shrimp Farm Field Worker App\n🔧 Version: 1.0.0\n⚡ React Native\n💾 Offline-first architecture\n🔐 Enterprise security\n📊 Real-time analytics`);
        }

        // Add touch feedback for mobile-like experience
        document.addEventListener('click', function(e) {
            if (e.target.closest('.action-card') || 
                e.target.closest('.task-btn') ||
                e.target.closest('.nav-item') ||
                e.target.closest('.stat-card')) {
                
                const element = e.target.closest('.action-card, .task-btn, .nav-item, .stat-card');
                element.style.transform = 'scale(0.95)';
                
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 150);
            }
        });

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            showNotification(`🎉 Welcome to the Shrimp Farm Field Worker App!\n📱 Interactive simulator loaded\n🎮 Try clicking on any element`);
        });
    </script>
</body>
</html>
