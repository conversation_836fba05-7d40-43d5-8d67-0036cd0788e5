<!DOCTYPE html>
<html>
<head>
    <title>Ultra Simple Google Maps Test</title>
    <style>
        #map {
            height: 500px;
            width: 100%;
            background-color: grey;
        }
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>Ultra Simple Google Maps Test</h1>
    
    <div class="status">
        <strong>API Key (partial):</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}
    </div>
    
    <div class="status">
        <strong>Status:</strong> <span id="status">Loading...</span>
    </div>
    
    <div id="map"></div>

    <script>
        function initMap() {
            document.getElementById('status').innerHTML = 'Google Maps API loaded successfully!';
            document.getElementById('status').style.color = 'green';
            
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 }
            });
            
            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Location'
            });
        }
        
        // Error handler
        window.addEventListener('error', function(e) {
            document.getElementById('status').innerHTML = 'Error: ' + e.message;
            document.getElementById('status').style.color = 'red';
        });
        
        // Timeout handler
        setTimeout(function() {
            if (typeof google === 'undefined') {
                document.getElementById('status').innerHTML = 'Timeout: Google Maps API did not load';
                document.getElementById('status').style.color = 'red';
            }
        }, 10000);
    </script>

    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
    </script>
</body>
</html>
