<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Center - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-3px);
        }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .notification-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-left: 4px solid #e9ecef;
        }
        
        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .notification-item.unread {
            border-left-color: #667eea;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        }
        
        .notification-item.critical {
            border-left-color: #dc3545;
        }
        
        .notification-item.high {
            border-left-color: #fd7e14;
        }
        
        .notification-item.medium {
            border-left-color: #ffc107;
        }
        
        .notification-item.low {
            border-left-color: #28a745;
        }
        
        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }
        
        .notification-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .notification-meta {
            display: flex;
            gap: 15px;
            font-size: 0.8rem;
            color: #64748b;
        }
        
        .notification-actions {
            display: flex;
            gap: 10px;
        }
        
        .notification-actions button {
            padding: 4px 8px;
            border: none;
            border-radius: 5px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-mark-read {
            background: #28a745;
            color: white;
        }
        
        .btn-dismiss {
            background: #6c757d;
            color: white;
        }
        
        .priority-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .priority-critical {
            background: #dc3545;
            color: white;
        }
        
        .priority-high {
            background: #fd7e14;
            color: white;
        }
        
        .priority-medium {
            background: #ffc107;
            color: #212529;
        }
        
        .priority-low {
            background: #28a745;
            color: white;
        }
        
        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .filter-tab {
            padding: 10px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            background: white;
            color: #64748b;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: transparent;
        }
        
        .real-time-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #28a745;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 20px;
            justify-content: center;
        }
        
        .real-time-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>🔔 Notification Center</h1>
        <p>Stay updated with real-time alerts and important information</p>
        
        <!-- Real-time indicator -->
        <div class="real-time-indicator">
            <div class="real-time-dot"></div>
            <span>Live Updates Active</span>
        </div>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="{% url 'notifications:preferences' %}" class="quick-action">
                <i class="fas fa-cog"></i>
                <span>Preferences</span>
            </a>
            <button class="quick-action" onclick="markAllRead()">
                <i class="fas fa-check-double"></i>
                <span>Mark All Read</span>
            </button>
            <button class="quick-action" onclick="sendTestNotification()">
                <i class="fas fa-bell"></i>
                <span>Test Notification</span>
            </button>
            <a href="/" class="quick-action">
                <i class="fas fa-home"></i>
                <span>Main Dashboard</span>
            </a>
        </div>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value text-primary">{{ stats.total }}</div>
            <div class="stat-label">Total Notifications</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-warning">{{ stats.unread }}</div>
            <div class="stat-label">Unread</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-success">{{ stats.read }}</div>
            <div class="stat-label">Read</div>
        </div>
        <div class="stat-card">
            <div class="stat-value text-danger">{{ stats.critical }}</div>
            <div class="stat-label">Critical</div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="filter-tabs">
        <a href="{% url 'notifications:center' %}" class="filter-tab {% if not status_filter %}active{% endif %}">
            All
        </a>
        <a href="{% url 'notifications:center' %}?status=sent" class="filter-tab {% if status_filter == 'sent' %}active{% endif %}">
            Unread
        </a>
        <a href="{% url 'notifications:center' %}?status=read" class="filter-tab {% if status_filter == 'read' %}active{% endif %}">
            Read
        </a>
        <a href="{% url 'notifications:center' %}?status=critical" class="filter-tab {% if status_filter == 'critical' %}active{% endif %}">
            Critical
        </a>
    </div>

    <!-- Notifications List -->
    <div class="notifications-list">
        {% for notification in notifications %}
        <div class="notification-item {% if notification.status in 'sent,delivered' %}unread{% endif %} {{ notification.priority }}" 
             data-notification-id="{{ notification.id }}">
            <div class="notification-header">
                <div>
                    <div class="notification-title">{{ notification.subject }}</div>
                    <div class="notification-meta">
                        <span><i class="fas fa-clock"></i> {{ notification.created_at|date:"M d, Y H:i" }}</span>
                        {% if notification.farm %}
                        <span><i class="fas fa-farm"></i> {{ notification.farm.name }}</span>
                        {% endif %}
                        {% if notification.pond %}
                        <span><i class="fas fa-water"></i> {{ notification.pond.name }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="d-flex align-items-start gap-2">
                    <span class="priority-badge priority-{{ notification.priority }}">
                        {{ notification.priority }}
                    </span>
                    <div class="notification-actions">
                        {% if notification.status in 'sent,delivered' %}
                        <button class="btn-mark-read" onclick="markAsRead('{{ notification.id }}')">
                            <i class="fas fa-check"></i>
                        </button>
                        {% endif %}
                        <button class="btn-dismiss" onclick="dismissNotification('{{ notification.id }}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="notification-message">
                {{ notification.message|truncatewords:30 }}
            </div>
        </div>
        {% empty %}
        <div class="text-center py-5">
            <i class="fas fa-bell-slash text-muted fa-3x mb-3"></i>
            <h5>No Notifications</h5>
            <p class="text-muted">You're all caught up! No notifications to display.</p>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if notifications.has_other_pages %}
    <nav aria-label="Notifications pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if notifications.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
            </li>
            {% endif %}
            
            {% for num in notifications.paginator.page_range %}
            {% if notifications.number == num %}
            <li class="page-item active">
                <span class="page-link">{{ num }}</span>
            </li>
            {% else %}
            <li class="page-item">
                <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
            </li>
            {% endif %}
            {% endfor %}
            
            {% if notifications.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ notifications.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Real-time notification updates
let lastUpdate = new Date().toISOString();
let updateInterval;

function startRealTimeUpdates() {
    updateInterval = setInterval(checkForNewNotifications, 10000); // Check every 10 seconds
}

function checkForNewNotifications() {
    fetch(`/notifications/api/?since=${lastUpdate}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.notifications.length > 0) {
                // Update the page with new notifications
                location.reload(); // Simple approach - reload page
            }
            lastUpdate = data.timestamp;
        })
        .catch(error => console.error('Error checking notifications:', error));
}

function markAsRead(notificationId) {
    fetch(`/notifications/mark-read/${notificationId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove unread styling
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('unread');
            
            // Hide mark as read button
            const markReadBtn = notificationElement.querySelector('.btn-mark-read');
            if (markReadBtn) {
                markReadBtn.style.display = 'none';
            }
            
            // Update stats
            updateStats();
        }
    })
    .catch(error => console.error('Error marking notification as read:', error));
}

function dismissNotification(notificationId) {
    if (confirm('Are you sure you want to dismiss this notification?')) {
        fetch(`/notifications/dismiss/${notificationId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Remove notification from view
                const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
                notificationElement.style.opacity = '0';
                setTimeout(() => {
                    notificationElement.remove();
                    updateStats();
                }, 300);
            }
        })
        .catch(error => console.error('Error dismissing notification:', error));
    }
}

function markAllRead() {
    if (confirm('Mark all notifications as read?')) {
        fetch('/notifications/mark-all-read/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            }
        })
        .catch(error => console.error('Error marking all as read:', error));
    }
}

function sendTestNotification() {
    fetch('/notifications/api/test/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            template_type: 'system_alert',
            message: 'This is a test notification from the notification center!'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Test notification sent!');
            setTimeout(() => location.reload(), 1000);
        }
    })
    .catch(error => console.error('Error sending test notification:', error));
}

function updateStats() {
    // Update notification counts
    const unreadCount = document.querySelectorAll('.notification-item.unread').length;
    const totalCount = document.querySelectorAll('.notification-item').length;
    const readCount = totalCount - unreadCount;
    
    // Update stat cards if they exist
    const statCards = document.querySelectorAll('.stat-value');
    if (statCards.length >= 3) {
        statCards[1].textContent = unreadCount; // Unread
        statCards[2].textContent = readCount;   // Read
    }
}

// Initialize real-time updates
document.addEventListener('DOMContentLoaded', function() {
    startRealTimeUpdates();
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});
</script>

</body>
</html>
