<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Water Quality Reading - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 800px;
            margin: 20px auto;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .form-section h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            padding: 12px 24px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        .btn-primary:hover {
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .parameter-input {
            position: relative;
            margin-bottom: 20px;
        }

        .parameter-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            font-size: 1.1rem;
            z-index: 10;
        }

        .parameter-input input {
            padding-left: 45px;
        }

        .range-hint {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }

        .back-link:hover {
            color: #764ba2;
            transform: translateX(-5px);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .live-preview {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .preview-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .preview-item:last-child {
            border-bottom: none;
        }

        .preview-value {
            font-weight: 600;
            color: #2d3748;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-optimal { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-critical { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>

<div class="dashboard-container">
    <a href="/water-quality/" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Dashboard
    </a>

    <div class="dashboard-header">
        <h1>💧 Add Water Quality Reading</h1>
        <p>Record new water quality measurements for pond monitoring</p>
    </div>

    <!-- Live Preview -->
    <div class="live-preview">
        <h6 class="mb-3">
            <i class="fas fa-eye me-2"></i>
            Live Preview
        </h6>
        <div id="live-preview-content">
            <div class="preview-item">
                <span>Overall Status:</span>
                <span class="status-indicator status-optimal" id="overall-status">Optimal</span>
            </div>
            <div class="preview-item">
                <span>Temperature:</span>
                <span class="preview-value" id="preview-temp">-- °C</span>
            </div>
            <div class="preview-item">
                <span>pH Level:</span>
                <span class="preview-value" id="preview-ph">--</span>
            </div>
            <div class="preview-item">
                <span>Dissolved Oxygen:</span>
                <span class="preview-value" id="preview-oxygen">-- mg/L</span>
            </div>
        </div>
    </div>

    <!-- Form -->
    <form method="post" id="water-quality-form">
        <!-- Basic Information -->
        <div class="form-section">
            <h5>
                <i class="fas fa-info-circle text-primary"></i>
                Basic Information
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <label for="pond" class="form-label">Select Pond</label>
                    <select id="pond" name="pond" class="form-select" required>
                        <option value="">Choose pond...</option>
                        <option value="pond-01">Pond A-01 - Active</option>
                        <option value="pond-02">Pond A-02 - Active</option>
                        <option value="pond-03">Pond B-03 - Active</option>
                        <option value="pond-04">Pond B-04 - Maintenance</option>
                        <option value="pond-05">Pond C-05 - Active</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="timestamp" class="form-label">Reading Time</label>
                    <input type="datetime-local" id="timestamp" name="timestamp" class="form-control" required>
                </div>
            </div>
        </div>

        <!-- Water Quality Parameters -->
        <div class="form-section">
            <h5>
                <i class="fas fa-flask text-success"></i>
                Water Quality Parameters
            </h5>
            <div class="form-grid">
                <div class="parameter-input">
                    <i class="fas fa-thermometer-half parameter-icon"></i>
                    <label for="temperature" class="form-label">Temperature (°C)</label>
                    <input type="number" id="temperature" name="temperature" class="form-control" 
                           step="0.1" min="0" max="50" placeholder="Enter temperature">
                    <div class="range-hint">Optimal: 26-30°C</div>
                </div>

                <div class="parameter-input">
                    <i class="fas fa-flask parameter-icon"></i>
                    <label for="ph" class="form-label">pH Level</label>
                    <input type="number" id="ph" name="ph" class="form-control" 
                           step="0.1" min="0" max="14" placeholder="Enter pH level">
                    <div class="range-hint">Optimal: 6.8-7.5</div>
                </div>

                <div class="parameter-input">
                    <i class="fas fa-wind parameter-icon"></i>
                    <label for="oxygen" class="form-label">Dissolved Oxygen (mg/L)</label>
                    <input type="number" id="oxygen" name="oxygen" class="form-control" 
                           step="0.1" min="0" max="20" placeholder="Enter oxygen level">
                    <div class="range-hint">Optimal: >5.0 mg/L</div>
                </div>

                <div class="parameter-input">
                    <i class="fas fa-tint parameter-icon"></i>
                    <label for="salinity" class="form-label">Salinity (ppt)</label>
                    <input type="number" id="salinity" name="salinity" class="form-control" 
                           step="0.1" min="0" max="50" placeholder="Enter salinity">
                    <div class="range-hint">Optimal: 20-30 ppt</div>
                </div>

                <div class="parameter-input">
                    <i class="fas fa-atom parameter-icon"></i>
                    <label for="ammonia" class="form-label">Ammonia (mg/L)</label>
                    <input type="number" id="ammonia" name="ammonia" class="form-control" 
                           step="0.01" min="0" max="5" placeholder="Enter ammonia level">
                    <div class="range-hint">Optimal: <0.1 mg/L</div>
                </div>

                <div class="parameter-input">
                    <i class="fas fa-eye parameter-icon"></i>
                    <label for="turbidity" class="form-label">Turbidity (NTU)</label>
                    <input type="number" id="turbidity" name="turbidity" class="form-control" 
                           step="1" min="0" max="100" placeholder="Enter turbidity">
                    <div class="range-hint">Optimal: <40 NTU</div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
            <h5>
                <i class="fas fa-comment text-info"></i>
                Additional Information
            </h5>
            <div class="row">
                <div class="col-md-6">
                    <label for="weather_condition" class="form-label">Weather Condition</label>
                    <select id="weather_condition" name="weather_condition" class="form-select">
                        <option value="">Select condition...</option>
                        <option value="sunny">Sunny</option>
                        <option value="cloudy">Cloudy</option>
                        <option value="rainy">Rainy</option>
                        <option value="overcast">Overcast</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="sampling_depth" class="form-label">Sampling Depth (cm)</label>
                    <input type="number" id="sampling_depth" name="sampling_depth" class="form-control" 
                           min="0" max="500" placeholder="Enter depth">
                </div>
            </div>
            <div class="mt-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea id="notes" name="notes" class="form-control" rows="3" 
                          placeholder="Any additional observations or notes..."></textarea>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="d-flex gap-3 justify-content-end">
            <a href="/water-quality/" class="btn btn-outline-secondary">
                <i class="fas fa-times me-2"></i>
                Cancel
            </a>
            <button type="button" class="btn btn-outline-primary" onclick="previewReading()">
                <i class="fas fa-eye me-2"></i>
                Preview
            </button>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>
                Save Reading
            </button>
        </div>
    </form>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Set current timestamp
    document.addEventListener('DOMContentLoaded', function() {
        const now = new Date();
        const timestamp = now.toISOString().slice(0, 16);
        document.getElementById('timestamp').value = timestamp;
    });

    // Live preview functionality
    function updateLivePreview() {
        const temp = parseFloat(document.getElementById('temperature').value) || 0;
        const ph = parseFloat(document.getElementById('ph').value) || 0;
        const oxygen = parseFloat(document.getElementById('oxygen').value) || 0;
        const salinity = parseFloat(document.getElementById('salinity').value) || 0;
        const ammonia = parseFloat(document.getElementById('ammonia').value) || 0;

        // Update preview values
        document.getElementById('preview-temp').textContent = temp ? temp + ' °C' : '-- °C';
        document.getElementById('preview-ph').textContent = ph ? ph.toString() : '--';
        document.getElementById('preview-oxygen').textContent = oxygen ? oxygen + ' mg/L' : '-- mg/L';

        // Calculate overall status
        let overallStatus = 'optimal';
        const statusElement = document.getElementById('overall-status');

        // Check temperature (26-30°C optimal)
        if (temp > 0 && (temp < 25 || temp > 32)) {
            overallStatus = 'critical';
        } else if (temp > 0 && (temp < 26 || temp > 30)) {
            overallStatus = 'warning';
        }

        // Check pH (6.8-7.5 optimal)
        if (ph > 0 && (ph < 6.5 || ph > 8.0)) {
            overallStatus = 'critical';
        } else if (ph > 0 && (ph < 6.8 || ph > 7.5)) {
            if (overallStatus !== 'critical') overallStatus = 'warning';
        }

        // Check oxygen (>5.0 optimal)
        if (oxygen > 0 && oxygen < 4.0) {
            overallStatus = 'critical';
        } else if (oxygen > 0 && oxygen < 5.0) {
            if (overallStatus !== 'critical') overallStatus = 'warning';
        }

        // Update status display
        statusElement.className = 'status-indicator status-' + overallStatus;
        statusElement.textContent = overallStatus.charAt(0).toUpperCase() + overallStatus.slice(1);
    }

    // Add event listeners for live preview
    ['temperature', 'ph', 'oxygen', 'salinity', 'ammonia'].forEach(id => {
        document.getElementById(id).addEventListener('input', updateLivePreview);
    });

    // Preview function
    function previewReading() {
        updateLivePreview();
        alert('Preview updated! Check the Live Preview section above.');
    }

    // Form validation
    document.getElementById('water-quality-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Basic validation
        const pond = document.getElementById('pond').value;
        const timestamp = document.getElementById('timestamp').value;
        
        if (!pond || !timestamp) {
            alert('Please select a pond and enter a timestamp.');
            return;
        }

        // Check if at least one parameter is filled
        const params = ['temperature', 'ph', 'oxygen', 'salinity', 'ammonia'];
        const hasParams = params.some(id => document.getElementById(id).value);
        
        if (!hasParams) {
            alert('Please enter at least one water quality parameter.');
            return;
        }

        // Simulate form submission
        alert('Water quality reading saved successfully!');
        window.location.href = '/water-quality/';
    });
</script>

</body>
</html>
