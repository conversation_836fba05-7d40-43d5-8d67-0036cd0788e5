import json
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from users.models import User, UserProfile
from farms.models import Farm


class UserModelTests(TestCase):
    """Test suite for the User model."""

    def test_create_user(self):
        """Test creating a regular user."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.name, 'Test User')
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.check_password('testpassword'))

    def test_create_superuser(self):
        """Test creating a superuser."""
        admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword',
            name='Admin User'
        )
        
        self.assertEqual(admin_user.email, '<EMAIL>')
        self.assertEqual(admin_user.name, 'Admin User')
        self.assertTrue(admin_user.is_active)
        self.assertTrue(admin_user.is_staff)
        self.assertTrue(admin_user.is_superuser)
        self.assertTrue(admin_user.check_password('adminpassword'))

    def test_user_str_representation(self):
        """Test the string representation of a user."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )
        
        self.assertEqual(str(user), '<EMAIL>')

    def test_email_normalization(self):
        """Test that email is normalized when creating a user."""
        email = '<EMAIL>'
        user = User.objects.create_user(
            email=email,
            password='testpassword',
            name='Test User'
        )
        
        self.assertEqual(user.email, email.lower())

    def test_email_required(self):
        """Test that creating a user without an email raises an error."""
        with self.assertRaises(ValueError):
            User.objects.create_user(
                email=None,
                password='testpassword',
                name='Test User'
            )


class UserProfileModelTests(TestCase):
    """Test suite for the UserProfile model."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            name='Test User'
        )

    def test_profile_creation(self):
        """Test creating a user profile."""
        profile = UserProfile.objects.create(
            user=self.user,
            phone_number='1234567890',
            job_title='Farm Manager',
            bio='Experienced farm manager with 10 years in shrimp farming',
            profile_picture='profiles/test.jpg'
        )
        
        self.assertEqual(profile.user, self.user)
        self.assertEqual(profile.phone_number, '1234567890')
        self.assertEqual(profile.job_title, 'Farm Manager')
        self.assertEqual(profile.bio, 'Experienced farm manager with 10 years in shrimp farming')
        self.assertEqual(profile.profile_picture, 'profiles/test.jpg')

    def test_profile_str_representation(self):
        """Test the string representation of a user profile."""
        profile = UserProfile.objects.create(
            user=self.user,
            phone_number='1234567890',
            job_title='Farm Manager'
        )
        
        self.assertEqual(str(profile), f'Profile for {self.user.email}')

    def test_profile_auto_creation(self):
        """Test that a profile is automatically created when a user is created."""
        # The profile should be created by a signal when the user is created
        profile = UserProfile.objects.get(user=self.user)
        
        self.assertIsNotNone(profile)
        self.assertEqual(profile.user, self.user)


class UserAPITests(TestCase):
    """Test suite for the User API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.admin_user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpassword',
            name='Admin User'
        )
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='userpassword',
            name='Regular User'
        )
        
        # Create a farm for testing
        self.farm = Farm.objects.create(
            name='Test Farm',
            location='Test Location',
            owner=self.admin_user
        )

    def test_user_registration(self):
        """Test user registration endpoint."""
        url = reverse('user-register')
        data = {
            'email': '<EMAIL>',
            'password': 'newuserpassword',
            'name': 'New User'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['name'], 'New User')
        self.assertNotIn('password', response.data)
        
        # Verify the user was created in the database
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())

    def test_user_login(self):
        """Test user login endpoint."""
        url = reverse('user-login')
        data = {
            'email': '<EMAIL>',
            'password': 'userpassword'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['name'], 'Regular User')

    def test_invalid_login(self):
        """Test login with invalid credentials."""
        url = reverse('user-login')
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertNotIn('token', response.data)

    def test_get_user_profile(self):
        """Test retrieving user profile."""
        # Authenticate as the regular user
        self.client.force_authenticate(user=self.regular_user)
        
        url = reverse('user-profile')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['name'], 'Regular User')
        self.assertIn('profile', response.data)

    def test_update_user_profile(self):
        """Test updating user profile."""
        # Authenticate as the regular user
        self.client.force_authenticate(user=self.regular_user)
        
        url = reverse('user-profile')
        data = {
            'name': 'Updated User Name',
            'profile': {
                'phone_number': '9876543210',
                'job_title': 'Senior Farm Manager',
                'bio': 'Updated bio information'
            }
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Updated User Name')
        self.assertEqual(response.data['profile']['phone_number'], '9876543210')
        self.assertEqual(response.data['profile']['job_title'], 'Senior Farm Manager')
        self.assertEqual(response.data['profile']['bio'], 'Updated bio information')
        
        # Verify the updates in the database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.name, 'Updated User Name')
        
        profile = UserProfile.objects.get(user=self.regular_user)
        self.assertEqual(profile.phone_number, '9876543210')
        self.assertEqual(profile.job_title, 'Senior Farm Manager')
        self.assertEqual(profile.bio, 'Updated bio information')

    def test_change_password(self):
        """Test changing user password."""
        # Authenticate as the regular user
        self.client.force_authenticate(user=self.regular_user)
        
        url = reverse('user-change-password')
        data = {
            'old_password': 'userpassword',
            'new_password': 'newuserpassword'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify the password was changed
        self.regular_user.refresh_from_db()
        self.assertTrue(self.regular_user.check_password('newuserpassword'))

    def test_admin_list_users(self):
        """Test that admin can list all users."""
        # Authenticate as the admin user
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Admin and regular user

    def test_regular_user_cannot_list_users(self):
        """Test that regular users cannot list all users."""
        # Authenticate as the regular user
        self.client.force_authenticate(user=self.regular_user)
        
        url = reverse('user-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_admin_create_user(self):
        """Test that admin can create a new user."""
        # Authenticate as the admin user
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-list')
        data = {
            'email': '<EMAIL>',
            'password': 'adminuserpassword',
            'name': 'Admin Created User',
            'is_staff': True
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['name'], 'Admin Created User')
        self.assertTrue(response.data['is_staff'])
        
        # Verify the user was created in the database
        self.assertTrue(User.objects.filter(email='<EMAIL>').exists())

    def test_admin_update_user(self):
        """Test that admin can update a user."""
        # Authenticate as the admin user
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-detail', args=[self.regular_user.id])
        data = {
            'name': 'Admin Updated User',
            'is_staff': True
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Admin Updated User')
        self.assertTrue(response.data['is_staff'])
        
        # Verify the updates in the database
        self.regular_user.refresh_from_db()
        self.assertEqual(self.regular_user.name, 'Admin Updated User')
        self.assertTrue(self.regular_user.is_staff)

    def test_admin_delete_user(self):
        """Test that admin can delete a user."""
        # Create a user to delete
        user_to_delete = User.objects.create_user(
            email='<EMAIL>',
            password='deletepassword',
            name='Delete User'
        )
        
        # Authenticate as the admin user
        self.client.force_authenticate(user=self.admin_user)
        
        url = reverse('user-detail', args=[user_to_delete.id])
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify the user was deleted from the database
        self.assertFalse(User.objects.filter(email='<EMAIL>').exists())
