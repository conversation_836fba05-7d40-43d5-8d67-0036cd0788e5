<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
    <style>
        #map { height: 400px; width: 100%; }
        .debug { position: fixed; top: 10px; right: 10px; background: white; padding: 10px; border: 1px solid #ccc; z-index: 1000; }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div class="debug">
        <h4>Debug Info:</h4>
        <div id="debug-info">Loading...</div>
    </div>
    <div id="map"></div>

    <script>
        function updateDebug(message) {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
            console.log(message);
        }

        updateDebug('Starting test...');
        updateDebug('API Key: AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw');

        window.gm_authFailure = function() {
            updateDebug('❌ Authentication failed!');
        };

        function initMap() {
            updateDebug('✅ initMap() called successfully!');
            
            if (typeof google === 'undefined') {
                updateDebug('❌ Google object not found');
                return;
            }
            
            updateDebug('✅ Google object found');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 10,
                    center: { lat: 10.8505, lng: 76.2711 } // Kerala, India
                });
                
                updateDebug('✅ Map created successfully!');
                
                // Add a test marker
                new google.maps.Marker({
                    position: { lat: 10.8505, lng: 76.2711 },
                    map: map,
                    title: 'Test Location'
                });
                
                updateDebug('✅ Marker added successfully!');
                
            } catch (error) {
                updateDebug('❌ Error creating map: ' + error.message);
            }
        }

        // Test if Google Maps script loads within 5 seconds
        setTimeout(() => {
            if (typeof google === 'undefined') {
                updateDebug('❌ Google Maps failed to load within 5 seconds');
                document.getElementById('map').innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Google Maps failed to load. Check console for errors.</div>';
            }
        }, 5000);
    </script>

    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="updateDebug('❌ Script load error'); document.getElementById('map').innerHTML='<div style=&quot;padding: 20px; text-align: center; color: red;&quot;>Failed to load Google Maps script</div>';">
    </script>
</body>
</html>
