{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Cumulative Map - Working{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #1e3a8a 0%, #1f2937 50%, #0f172a 100%);
        color: #e2e8f0;
        font-family: 'Segoe UI', system-ui, sans-serif;
    }
    
    .container-fluid {
        padding: 20px;
        max-width: 100%;
    }
    
    #map {
        height: 70vh;
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }
    
    .map-container {
        margin: 20px 0;
        padding: 20px;
        background: rgba(30, 41, 59, 0.9);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        position: relative;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 15px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
        font-weight: 500;
    }
    
    .controls {
        margin: 20px 0;
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
    
    .btn-primary { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
    .btn-success { background: linear-gradient(135deg, #10b981, #059669); color: white; }
    .btn-info { background: linear-gradient(135deg, #06b6d4, #0891b2); color: white; }
    .btn-warning { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
    .btn-danger { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
    
    h1 {
        color: #e2e8f0;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
        font-weight: 700;
        font-size: 2.5rem;
        text-align: center;
    }
    
    .advanced-controls {
        background: rgba(30, 41, 59, 0.95);
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
    }
    
    .control-section {
        margin-bottom: 20px;
    }
    
    .control-section h6 {
        color: #94a3b8;
        font-size: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 12px;
        border-bottom: 2px solid rgba(74, 85, 104, 0.3);
        padding-bottom: 8px;
    }
    
    .debug-info {
        background: rgba(30, 41, 59, 0.8);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #94a3b8;
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>
        <i class="fas fa-map-marked-alt"></i>
        Enhanced Shrimp Farm GPS System
    </h1>
    
    <!-- Statistics Dashboard -->
    <div class="stats-grid">
        <div class="stat-card">
            <div><i class="fas fa-warehouse" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Active Farms</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-water" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Monitored Ponds</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_workers }}</div>
            <div class="stat-label">GPS Workers</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-fan" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_aerators }}</div>
            <div class="stat-label">Aerators</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-shield-alt" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_geofences }}</div>
            <div class="stat-label">Security Zones</div>
        </div>
    </div>    <!-- Control Panel -->
    <div class="advanced-controls">
        <div class="control-section">
            <h6><i class="fas fa-palette"></i> Worker Status Legend</h6>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 15px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #ef4444; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Working</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #10b981; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Available</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #f59e0b; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">On Break</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #8b5cf6; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Traveling</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #6b7280; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Offline</span>
                </div>
            </div>
        </div>

        <div class="control-section">
            <h6><i class="fas fa-fan"></i> Aerator Status Legend</h6>
            <div style="display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 15px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #007bff; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Active/Running</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #ffc107; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Maintenance</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #dc3545; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Inactive/Off</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                    <div style="width: 16px; height: 16px; border-radius: 50%; background: #6c757d; border: 2px solid white;"></div>
                    <span style="color: #e2e8f0; font-size: 14px;">Unknown</span>
                </div>
            </div>
        </div>

        <div class="control-section">
            <h6><i class="fas fa-layer-group"></i> Layer Controls</h6>
            <div class="controls">
                <button class="btn btn-primary" onclick="toggleFarms()">
                    <i class="fas fa-warehouse"></i>
                    Farms
                </button>
                <button class="btn btn-success" onclick="togglePonds()">
                    <i class="fas fa-water"></i>
                    Ponds
                </button>
                <button class="btn btn-info" onclick="toggleWorkers()">
                    <i class="fas fa-users"></i>
                    Workers
                </button>
                <button class="btn btn-secondary" onclick="toggleAerators()">
                    <i class="fas fa-fan"></i>
                    Aerators
                </button>
                <button class="btn btn-warning" onclick="toggleGeofences()">
                    <i class="fas fa-shield-alt"></i>
                    Geofences
                </button>
            </div>
        </div>
        
        <div class="control-section">
            <h6><i class="fas fa-cogs"></i> Map Controls</h6>
            <div class="controls">
                <button class="btn btn-primary" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                    Center Map
                </button>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button class="btn btn-success" onclick="fitAllMarkers()">
                    <i class="fas fa-expand-arrows-alt"></i>
                    Fit All
                </button>
            </div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
    </div>
    
    <!-- Debug Information -->
    <div class="debug-info">
        <strong>🔧 System Information:</strong><br>
        Map Center: {{ center_lat|floatformat:6 }}, {{ center_lng|floatformat:6 }}<br>
        Data: Farms: {{ total_farms }} | Ponds: {{ total_ponds }} | Workers: {{ total_workers }} | Aerators: {{ total_aerators }} | Geofences: {{ total_geofences }}<br>
        <span id="jsDebugInfo" style="color: #10b981;">✅ System Ready</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 Enhanced Map System Loading...');

// Global Variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let workerMarkers = [];
let aeratorMarkers = [];
let geofenceOverlays = [];

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const workersData = {{ workers_data|safe }};
const aeratorsData = {{ aerators_data|safe }};
const geofencesData = {{ geofences_data|safe }};
const centerLat = parseFloat({{ center_lat }});
const centerLng = parseFloat({{ center_lng }});

// Visibility State
let layerVisibility = {
    farms: true,
    ponds: true,
    workers: true,
    aerators: true,
    geofences: true
};

function initMap() {
    try {
        console.log('🗺️ Initializing Enhanced Map...');
        console.log('📊 Data Summary:', {
            farms: farmsData.length,
            ponds: pondsData.length,
            workers: workersData.length,
            aerators: aeratorsData.length,
            geofences: geofencesData.length
        });
        
        // Initialize Map with enhanced styling
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 12,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    "featureType": "all",
                    "elementType": "geometry.fill",
                    "stylers": [{"saturation": -40}, {"lightness": -10}]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [{"color": "#2563eb"}, {"lightness": -10}]
                }
            ]
        });
        
        console.log('✅ Map initialized successfully');
        
        // Add all map elements
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addAeratorMarkers();
        addGeofenceOverlays();

        // Update debug info
        document.getElementById('jsDebugInfo').innerHTML =
            `✅ System Online - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Aerators: ${aeratorsData.length}, Geofences: ${geofencesData.length}`;
        
        console.log('🎉 Enhanced Map System Ready!');
        
    } catch (error) {
        console.error('❌ Map initialization failed:', error);
        handleMapError(error);
    }
}

function addFarmMarkers() {
    console.log('🏭 Adding farm markers...');
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
                        <circle cx="20" cy="20" r="18" fill="#3b82f6" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="20" cy="20" r="12" fill="#ffffff" opacity="0.2"/>
                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="10" font-family="Arial" font-weight="bold">FARM</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(40, 40)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-warehouse" style="color: #3b82f6; margin-right: 8px;"></i>
                        ${farm.name}
                    </h5>
                    <p><strong>Type:</strong> Farm Facility</p>
                    <p><strong>Description:</strong> ${farm.description || 'Main farming operation'}</p>
                    <p><strong>Location:</strong> ${farm.latitude.toFixed(6)}, ${farm.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            // Zoom to farm location
            map.setCenter(marker.getPosition());
            map.setZoom(15); // Slightly wider zoom for farms to show surrounding area

            // Open info window
            infoWindow.open(map, marker);
        });
        
        farmMarkers.push(marker);
    });
    console.log(`✅ Added ${farmMarkers.length} farm markers`);
}

function addPondMarkers() {
    console.log('🌊 Adding pond markers...');
    pondsData.forEach(pond => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                        <circle cx="18" cy="18" r="16" fill="#10b981" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="10" fill="#ffffff" opacity="0.3"/>
                        <text x="18" y="22" text-anchor="middle" fill="white" font-size="8" font-family="Arial" font-weight="bold">POND</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36)
            }
        });
        
        marker.addListener('click', () => {
            zoomToPond(pond.id, parseFloat(pond.latitude), parseFloat(pond.longitude));
        });
        
        pondMarkers.push(marker);
    });
    console.log(`✅ Added ${pondMarkers.length} pond markers`);
}

function addWorkerMarkers() {
    console.log('👥 Adding worker markers...');
    console.log('📊 Worker data received:', workersData);
    
    workersData.forEach((worker, index) => {
        const statusColors = {
            'working': '#ef4444',
            'available': '#10b981',
            'on_break': '#f59e0b',
            'traveling': '#8b5cf6',
            'offline': '#6b7280'
        };
        
        const color = statusColors[worker.status] || '#6b7280';
        console.log(`👤 Worker ${index + 1}: ${worker.name} - Status: ${worker.status} - Color: ${color}`);
        
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(worker.latitude), lng: parseFloat(worker.longitude) },
            map: map,
            title: worker.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                        <circle cx="18" cy="18" r="16" fill="${color}" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="12" fill="#f8f9fa"/>
                        <circle cx="18" cy="14" r="4" fill="#fdbcb4"/>
                        <circle cx="16.5" cy="13" r="0.7" fill="#333"/>
                        <circle cx="19.5" cy="13" r="0.7" fill="#333"/>
                        <circle cx="18" cy="15" r="0.4" fill="#f4a261"/>
                        <path d="M 16 16 Q 18 17.5 20 16" stroke="#333" stroke-width="0.7" fill="none"/>
                        <rect x="14" y="20" width="8" height="6" fill="#4a90e2" rx="2"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-user" style="color: ${color}; margin-right: 8px;"></i>
                        ${worker.name}
                    </h5>
                    <p><strong>Employee ID:</strong> ${worker.employee_id}</p>
                    <p><strong>Team:</strong> ${worker.team}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold;">${worker.status}</span></p>
                    <p><strong>Location:</strong> ${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            // Zoom to worker location
            map.setCenter(marker.getPosition());
            map.setZoom(16); // Zoom level for detailed view

            // Open info window
            infoWindow.open(map, marker);
        });
        
        workerMarkers.push(marker);
    });
    console.log(`✅ Added ${workerMarkers.length} worker markers`);
}

function addAeratorMarkers() {
    console.log('⚡ Adding aerator markers...');
    console.log('📊 Aerator data received:', aeratorsData);

    aeratorsData.forEach((aerator, index) => {
        const statusColors = {
            'active': '#007bff',
            'running': '#007bff',
            'maintenance': '#ffc107',
            'inactive': '#dc3545',
            'off': '#dc3545'
        };

        const color = statusColors[aerator.status] || '#6c757d';
        console.log(`⚡ Aerator ${index + 1}: ${aerator.name} - Status: ${aerator.status} - Color: ${color}`);

        const marker = new google.maps.Marker({
            position: { lat: parseFloat(aerator.latitude), lng: parseFloat(aerator.longitude) },
            map: map,
            title: aerator.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
                        <circle cx="16" cy="16" r="14" fill="${color}" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="16" cy="16" r="10" fill="#ffffff" opacity="0.3"/>
                        <circle cx="16" cy="16" r="6" fill="#ffffff" opacity="0.5"/>
                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="8" font-family="Arial" font-weight="bold">⚡</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });

        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-fan" style="color: ${color}; margin-right: 8px;"></i>
                        ${aerator.name}
                    </h5>
                    <p><strong>Type:</strong> ${aerator.aerator_type}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold;">${aerator.status}</span></p>
                    <p><strong>Power Rating:</strong> ${aerator.power_rating} kW</p>
                    <p><strong>Power Source:</strong> ${aerator.power_source}</p>
                    <p><strong>Pond:</strong> ${aerator.pond_name}</p>
                    <p><strong>Automated:</strong> ${aerator.is_automated ? 'Yes' : 'No'}</p>
                    <p><strong>Operating Hours:</strong> ${aerator.operating_hours}h</p>
                    <p><strong>Location:</strong> ${aerator.latitude.toFixed(6)}, ${aerator.longitude.toFixed(6)}</p>
                </div>
            `
        });

        marker.addListener('click', () => {
            // Zoom to aerator location
            map.setCenter(marker.getPosition());
            map.setZoom(16); // Zoom level for detailed view

            // Open info window
            infoWindow.open(map, marker);
        });

        aeratorMarkers.push(marker);
    });
    console.log(`✅ Added ${aeratorMarkers.length} aerator markers`);
}

function addGeofenceOverlays() {
    console.log('🛡️ Adding geofence overlays...');
    geofencesData.forEach(geofence => {
        let overlay;
        
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            overlay = new google.maps.Circle({
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map,
                center: { lat: parseFloat(geofence.center_latitude), lng: parseFloat(geofence.center_longitude) },
                radius: parseFloat(geofence.radius)
            });
        } else if (geofence.shape_type === 'polygon' && geofence.boundary && geofence.boundary.length > 0) {
            const coords = geofence.boundary.map(coord => ({
                lat: parseFloat(coord[0]),
                lng: parseFloat(coord[1])
            }));
            
            overlay = new google.maps.Polygon({
                paths: coords,
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map
            });
        }
        
        if (overlay) {
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h5>${geofence.name}</h5>
                        <p><strong>Type:</strong> ${geofence.geofence_type}</p>
                        <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                        <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                `
            });
            
            overlay.addListener('click', (event) => {
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
            });
            
            geofenceOverlays.push(overlay);
        }
    });
    console.log(`✅ Added ${geofenceOverlays.length} geofence overlays`);
}

// Enhanced Pond Zoom Function
function zoomToPond(pondId, lat, lng) {
    console.log(`🎯 Zooming to pond ${pondId}`);
    
    map.setCenter({ lat: lat, lng: lng });
    map.setZoom(18);
    
    setTimeout(() => {
        const pond = pondsData.find(p => p.id === pondId);
        if (pond) {
            const detailedInfoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="max-width: 350px;">
                        <h4 style="margin: 0 0 15px 0; color: #1f2937;">
                            <i class="fas fa-water" style="color: #10b981; margin-right: 8px;"></i>
                            ${pond.name}
                        </h4>
                        <div style="display: grid; gap: 8px; font-size: 14px;">
                            <div><strong>Farm:</strong> ${pond.farm_name}</div>
                            <div><strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
                            <div style="margin-top: 15px; text-align: center;">
                                <button onclick="centerMap()" 
                                        style="padding: 10px 15px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 12px;">
                                    <i class="fas fa-compress-arrows-alt"></i> Back to Overview
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                position: { lat: lat, lng: lng }
            });
            
            if (window.currentInfoWindow) {
                window.currentInfoWindow.close();
            }
            
            detailedInfoWindow.open(map);
            window.currentInfoWindow = detailedInfoWindow;
        }
    }, 800);
}

// Layer Toggle Functions
function toggleFarms() {
    layerVisibility.farms = !layerVisibility.farms;
    farmMarkers.forEach(marker => marker.setVisible(layerVisibility.farms));
    console.log(`🏭 Farms ${layerVisibility.farms ? 'shown' : 'hidden'}`);
}

function togglePonds() {
    layerVisibility.ponds = !layerVisibility.ponds;
    pondMarkers.forEach(marker => marker.setVisible(layerVisibility.ponds));
    console.log(`🌊 Ponds ${layerVisibility.ponds ? 'shown' : 'hidden'}`);
}

function toggleWorkers() {
    layerVisibility.workers = !layerVisibility.workers;
    workerMarkers.forEach(marker => marker.setVisible(layerVisibility.workers));
    console.log(`👥 Workers ${layerVisibility.workers ? 'shown' : 'hidden'}`);
}

function toggleAerators() {
    layerVisibility.aerators = !layerVisibility.aerators;
    aeratorMarkers.forEach(marker => marker.setVisible(layerVisibility.aerators));
    console.log(`⚡ Aerators ${layerVisibility.aerators ? 'shown' : 'hidden'}`);
}

function toggleGeofences() {
    layerVisibility.geofences = !layerVisibility.geofences;
    geofenceOverlays.forEach(overlay => overlay.setVisible(layerVisibility.geofences));
    console.log(`🛡️ Geofences ${layerVisibility.geofences ? 'shown' : 'hidden'}`);
}

function centerMap() {
    map.setCenter({ lat: centerLat, lng: centerLng });
    map.setZoom(12);
    console.log('🎯 Map centered');
}

function refreshData() {
    console.log('🔄 Refreshing data...');
    // Here you could reload markers or update positions
    console.log('✅ Data refreshed');
}

function fitAllMarkers() {
    const bounds = new google.maps.LatLngBounds();

    [...farmMarkers, ...pondMarkers, ...workerMarkers, ...aeratorMarkers].forEach(marker => {
        if (marker.getVisible()) {
            bounds.extend(marker.getPosition());
        }
    });
    
    if (!bounds.isEmpty()) {
        map.fitBounds(bounds);
        setTimeout(() => {
            if (map.getZoom() > 15) map.setZoom(15);
        }, 500);
    }
    
    console.log('📍 Fitted all visible markers');
}

function handleMapError(error) {
    console.error('❌ Map error:', error);
    document.getElementById('map').innerHTML = `
        <div style="padding: 50px; text-align: center; color: #ef4444; background: rgba(30, 41, 59, 0.9); border-radius: 12px;">
            <h3><i class="fas fa-exclamation-triangle"></i> Map Loading Error</h3>
            <p>Failed to load Google Maps. Please check your internet connection and API key.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; margin-top: 15px;">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    `;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 DOM Ready - Waiting for Google Maps...');
});
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
    onerror="handleMapError('Failed to load Google Maps API')">
</script>
{% endblock %}
