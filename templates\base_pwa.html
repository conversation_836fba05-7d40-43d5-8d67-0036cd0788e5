<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ShrimpGuardian">
    <meta name="mobile-web-app-capable" content="yes">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="/static/manifest.json">
    
    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/images/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/images/icon-16x16.png">
    <link rel="apple-touch-icon" href="/static/images/icon-192x192.png">
    <link rel="mask-icon" href="/static/images/icon-192x192.png" color="#667eea">
    
    <!-- Open Graph / Social Media -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="{% block og_title %}Shrimp Farm Guardian{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Comprehensive shrimp farm management system with AI-powered insights{% endblock %}">
    <meta property="og:image" content="/static/images/icon-512x512.png">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{% block twitter_title %}Shrimp Farm Guardian{% endblock %}">
    <meta name="twitter:description" content="{% block twitter_description %}Comprehensive shrimp farm management system{% endblock %}">
    <meta name="twitter:image" content="/static/images/icon-512x512.png">
    
    <title>{% block title %}Shrimp Farm Guardian{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- PWA Styles -->
    <link rel="stylesheet" href="/static/css/pwa.css">
    
    {% block extra_css %}{% endblock %}
    
    <style>
        /* Base PWA Styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
        
        .pwa-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 10px;
            padding: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 20px);
        }
        
        @media (max-width: 768px) {
            .pwa-container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
            }
        }
        
        /* PWA Header */
        .pwa-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
            margin-bottom: 20px;
        }
        
        .pwa-header-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }
        
        .pwa-header-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        /* PWA Navigation */
        .pwa-nav {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 5px;
        }
        
        .pwa-nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 16px;
            background: white;
            color: #64748b;
            text-decoration: none;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .pwa-nav-item:hover,
        .pwa-nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        /* PWA Content */
        .pwa-content {
            margin-bottom: 20px;
        }
        
        /* PWA Footer */
        .pwa-footer {
            text-align: center;
            padding: 20px 0;
            border-top: 1px solid #e9ecef;
            margin-top: 40px;
            color: #64748b;
            font-size: 0.9rem;
        }
        
        /* PWA Loading Overlay */
        .pwa-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        }
        
        .pwa-loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #e9ecef;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        .pwa-loading-text {
            color: #2d3748;
            font-weight: 600;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
    <!-- PWA Loading Overlay -->
    <div id="pwa-loading-overlay" class="pwa-loading-overlay" style="display: none;">
        <div class="pwa-loading-spinner"></div>
        <div class="pwa-loading-text">Loading...</div>
    </div>
    
    <!-- PWA Container -->
    <div class="pwa-container">
        <!-- PWA Header -->
        <header class="pwa-header">
            <h1 class="pwa-header-title">
                {% block header_title %}
                <i class="fas fa-fish me-2"></i>
                {% block page_title %}Shrimp Farm Guardian{% endblock %}
                {% endblock %}
            </h1>
            <div class="pwa-header-actions">
                {% block header_actions %}
                {% load notification_tags %}
                {% notification_bell %}
                {% endblock %}
            </div>
        </header>
        
        <!-- PWA Navigation -->
        {% block navigation %}
        <nav class="pwa-nav">
            <a href="/" class="pwa-nav-item {% if request.resolver_match.url_name == 'main_dashboard' %}active{% endif %}">
                <i class="fas fa-home"></i>
                <span>Dashboard</span>
            </a>
            <a href="/feed/" class="pwa-nav-item {% if '/feed/' in request.path %}active{% endif %}">
                <i class="fas fa-drumstick-bite"></i>
                <span>Feed</span>
            </a>
            <a href="/ponds/" class="pwa-nav-item {% if '/ponds/' in request.path %}active{% endif %}">
                <i class="fas fa-water"></i>
                <span>Ponds</span>
            </a>
            <a href="/notifications/" class="pwa-nav-item {% if '/notifications/' in request.path %}active{% endif %}">
                <i class="fas fa-bell"></i>
                <span>Alerts</span>
            </a>
            <a href="/analytics/enhanced/" class="pwa-nav-item {% if '/analytics/' in request.path %}active{% endif %}">
                <i class="fas fa-chart-line"></i>
                <span>Analytics</span>
            </a>
        </nav>
        {% endblock %}
        
        <!-- PWA Content -->
        <main class="pwa-content">
            {% block content %}
            <div class="text-center py-5">
                <h2>Welcome to Shrimp Farm Guardian</h2>
                <p class="text-muted">Your comprehensive aquaculture management system</p>
            </div>
            {% endblock %}
        </main>
        
        <!-- PWA Footer -->
        <footer class="pwa-footer">
            {% block footer %}
            <div class="d-flex justify-content-center align-items-center gap-3">
                <span>&copy; 2024 Shrimp Farm Guardian</span>
                <span class="text-muted">|</span>
                <span id="connection-status" class="pwa-status-online">
                    <i class="fas fa-wifi"></i> Online
                </span>
            </div>
            {% endblock %}
        </footer>
    </div>
    
    <!-- PWA Bottom Navigation (Mobile) -->
    <nav class="pwa-bottom-nav">
        <div class="pwa-bottom-nav-items">
            <a href="/" class="pwa-bottom-nav-item {% if request.resolver_match.url_name == 'main_dashboard' %}active{% endif %}">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="/feed/" class="pwa-bottom-nav-item {% if '/feed/' in request.path %}active{% endif %}">
                <i class="fas fa-drumstick-bite"></i>
                <span>Feed</span>
            </a>
            <a href="/ponds/" class="pwa-bottom-nav-item {% if '/ponds/' in request.path %}active{% endif %}">
                <i class="fas fa-water"></i>
                <span>Ponds</span>
            </a>
            <a href="/notifications/" class="pwa-bottom-nav-item {% if '/notifications/' in request.path %}active{% endif %}">
                <i class="fas fa-bell"></i>
                <span>Alerts</span>
            </a>
            <a href="/analytics/enhanced/" class="pwa-bottom-nav-item {% if '/analytics/' in request.path %}active{% endif %}">
                <i class="fas fa-chart-line"></i>
                <span>Analytics</span>
            </a>
        </div>
    </nav>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- PWA JavaScript -->
    <script src="/static/js/pwa.js"></script>
    
    {% block extra_js %}{% endblock %}
    
    <script>
        // PWA-specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Update connection status
            function updateConnectionStatus() {
                const statusElement = document.getElementById('connection-status');
                if (navigator.onLine) {
                    statusElement.className = 'pwa-status-online';
                    statusElement.innerHTML = '<i class="fas fa-wifi"></i> Online';
                } else {
                    statusElement.className = 'pwa-status-offline';
                    statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> Offline';
                }
            }
            
            // Listen for connection changes
            window.addEventListener('online', updateConnectionStatus);
            window.addEventListener('offline', updateConnectionStatus);
            
            // Initial status check
            updateConnectionStatus();
            
            // Show loading overlay for navigation
            const links = document.querySelectorAll('a[href^="/"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    if (!e.ctrlKey && !e.metaKey && !e.shiftKey) {
                        showLoadingOverlay();
                    }
                });
            });
            
            // Hide loading overlay when page loads
            window.addEventListener('load', function() {
                hideLoadingOverlay();
            });
        });
        
        function showLoadingOverlay() {
            document.getElementById('pwa-loading-overlay').style.display = 'flex';
        }
        
        function hideLoadingOverlay() {
            document.getElementById('pwa-loading-overlay').style.display = 'none';
        }
        
        // PWA Install prompt handling
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            // Show install button or banner
            if (window.pwaManager) {
                window.pwaManager.showInstallButton();
            }
        });
    </script>
</body>
</html>
