<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ pond.name }} - Pond Details - Shrimp Farm Guardian</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* Full-width container without sidebar */
        .main-container {
            width: 100%;
            padding: 2rem;
            overflow-y: auto;
        }

        .top-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(74, 85, 104, 0.3);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header-info h2 {
            font-size: 1.75rem;
            font-weight: 700;
            color: #e2e8f0;
            margin-bottom: 0.5rem;
        }

        .header-info p {
            color: #94a3b8;
            font-size: 1rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: rgba(74, 85, 104, 0.3);
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
        }

        .theme-toggle-wrapper {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .theme-toggle {
            width: 70px;
            height: 35px;
            background: rgba(74, 85, 104, 0.3);
            border-radius: 25px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .theme-toggle-slider {
            width: 28px;
            height: 28px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            position: absolute;
            top: 3px;
            left: 3px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.75rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .theme-label {
            font-size: 1.25rem;
            transition: all 0.3s ease;
            opacity: 0.6;
        }

        .theme-label.active {
            opacity: 1;
        }

        /* Demo Content */
        .demo-section {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(74, 85, 104, 0.3);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .demo-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .demo-section h3 {
            color: #e2e8f0;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .demo-section p {
            color: #94a3b8;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(74, 85, 104, 0.3);
            border-radius: 20px;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: rgba(102, 126, 234, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #667eea;
            font-size: 1.25rem;
            margin: 0 auto 1rem;
        }

        .feature-title {
            color: #e2e8f0;
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .feature-description {
            color: #94a3b8;
            font-size: 0.875rem;
            line-height: 1.5;
        }

        /* Light Mode */
        body.light-mode {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            color: #1e293b;
        }

        body.light-mode .top-header {
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(203, 213, 225, 0.5);
        }

        body.light-mode .header-info h2 {
            color: #1e293b;
        }

        body.light-mode .header-info p {
            color: #64748b;
        }

        body.light-mode .demo-section {
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(203, 213, 225, 0.5);
        }

        body.light-mode .demo-section h3 {
            color: #1e293b;
        }

        body.light-mode .demo-section p {
            color: #64748b;
        }

        body.light-mode .feature-card {
            background: rgba(255, 255, 255, 0.9);
            border-color: rgba(203, 213, 225, 0.5);
        }

        body.light-mode .feature-title {
            color: #1e293b;
        }

        body.light-mode .feature-description {
            color: #64748b;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @media (max-width: 768px) {
            .main-container { padding: 1rem; }
            .top-header { flex-direction: column; gap: 1rem; text-align: center; }
            .feature-grid { grid-template-columns: 1fr; gap: 1rem; }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="main-container">
        <div class="top-header fade-in">
            <div class="header-info">
                <h2>Pond Details - No Sidebar</h2>
                <p>Full-width layout without sidebar navigation</p>
            </div>
            <div class="header-actions">
                <a href="/ponds/" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to List
                </a>
                <div class="theme-toggle-wrapper">
                    <span class="theme-label" id="lightLabel">☀️</span>
                    <div class="theme-toggle" id="themeToggle">
                        <div class="theme-toggle-slider">
                            <i class="fas fa-moon"></i>
                        </div>
                    </div>
                    <span class="theme-label active" id="darkLabel">🌙</span>
                </div>
            </div>
        </div>

        <div class="demo-section fade-in">
            <div class="demo-icon">
                <i class="fas fa-water"></i>
            </div>
            <h3>Pond Detail Page - No Sidebar</h3>
            <p>This is the pond detail page with a full-width layout. The sidebar has been removed to provide more space for pond information, charts, and data visualization. This layout is perfect for detailed pond monitoring and analysis.</p>
            <a href="/ponds/" class="btn">
                <i class="fas fa-arrow-left"></i> Back to Pond List
            </a>
        </div>

        <div class="feature-grid fade-in">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-expand-arrows-alt"></i>
                </div>
                <div class="feature-title">Full Width Layout</div>
                <div class="feature-description">Maximum screen space utilization for better data visualization and content display.</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">Enhanced Charts</div>
                <div class="feature-description">Larger charts and graphs for better pond monitoring and data analysis.</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="feature-title">Mobile Optimized</div>
                <div class="feature-description">Responsive design that works perfectly on all devices and screen sizes.</div>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <div class="feature-title">Theme Support</div>
                <div class="feature-description">Beautiful light and dark mode themes with smooth transitions.</div>
            </div>
        </div>
    </div>

    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const body = document.body;
        const lightLabel = document.getElementById('lightLabel');
        const darkLabel = document.getElementById('darkLabel');
        const slider = themeToggle.querySelector('.theme-toggle-slider');

        // Load saved theme
        const savedTheme = localStorage.getItem('theme') || 'dark';
        if (savedTheme === 'light') {
            body.classList.add('light-mode');
            slider.style.transform = 'translateX(35px)';
            slider.innerHTML = '<i class="fas fa-sun"></i>';
            lightLabel.classList.add('active');
            darkLabel.classList.remove('active');
        }

        themeToggle.addEventListener('click', () => {
            body.classList.toggle('light-mode');

            if (body.classList.contains('light-mode')) {
                slider.style.transform = 'translateX(35px)';
                slider.innerHTML = '<i class="fas fa-sun"></i>';
                lightLabel.classList.add('active');
                darkLabel.classList.remove('active');
                localStorage.setItem('theme', 'light');
            } else {
                slider.style.transform = 'translateX(0)';
                slider.innerHTML = '<i class="fas fa-moon"></i>';
                lightLabel.classList.remove('active');
                darkLabel.classList.add('active');
                localStorage.setItem('theme', 'dark');
            }
        });

        console.log('🌊 Pond Detail Page Ready - No Sidebar!');
    </script>
</body>
</html>
