<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌤️ {{ pond.name }} - Google Maps Weather</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .map-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        #weather-map {
            height: 500px;
            width: 100%;
            border-radius: 10px;
            border: 3px solid #667eea;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .status-indicator {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .badge-gmaps {
            background: linear-gradient(45deg, #4285f4, #34a853);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .nav-buttons {
            margin-top: 20px;
        }
        
        .nav-buttons a {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-fish"></i> {{ pond.name }}</h1>
                    <p class="mb-2">🌤️ Google Maps Weather Station</p>
                    <span class="badge-gmaps">🗺️ GOOGLE MAPS POWERED</span>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white">
                        <div><strong>📍 Location:</strong></div>
                        <div>{{ pond.latitude }}°N, {{ pond.longitude }}°E</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map Container -->
        <div class="map-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3><i class="fas fa-map"></i> Interactive Weather Map</h3>
                <div id="map-status" class="status-indicator status-info">
                    ⏳ Loading Google Maps...
                </div>
            </div>
            <div id="weather-map"></div>
        </div>

        <!-- Info Panel -->
        <div class="info-card">
            <h4><i class="fas fa-info-circle"></i> Map Information</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>🗺️ Map Provider:</strong> Google Maps</p>
                    <p><strong>📡 API Status:</strong> <span id="api-status">Checking...</span></p>
                    <p><strong>🎯 View Type:</strong> Satellite with Weather Overlay</p>
                </div>
                <div class="col-md-6">
                    {% if weather_data %}
                    <p><strong>🌡️ Temperature:</strong> {{ weather_data.temperature }}°C</p>
                    <p><strong>💧 Humidity:</strong> {{ weather_data.humidity }}%</p>
                    <p><strong>💨 Wind Speed:</strong> {{ weather_data.wind_speed }} km/h</p>
                    <p><strong>☁️ Condition:</strong> {{ weather_data.condition }}</p>
                    {% else %}
                    <p><em>⚠️ No weather data available for this location</em></p>
                    {% endif %}
                </div>
            </div>
            
            <div class="nav-buttons">
                <a href="/ponds/" class="btn btn-primary">
                    <i class="fas fa-fish"></i> All Ponds
                </a>
                <a href="/weather/unified-map/" class="btn btn-info">
                    <i class="fas fa-globe"></i> Unified Weather Map
                </a>
                <a href="/weather/minimal-gmaps/" class="btn btn-success">
                    <i class="fas fa-vial"></i> Test Google Maps
                </a>
                <button onclick="location.reload()" class="btn btn-warning">
                    <i class="fas fa-sync"></i> Refresh Map
                </button>
            </div>
        </div>
    </div>

    <script>
        // Update status function
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('map-status');
            const apiStatusEl = document.getElementById('api-status');
            
            statusEl.textContent = message;
            statusEl.className = `status-indicator status-${type}`;
            
            if (apiStatusEl) {
                apiStatusEl.textContent = type === 'success' ? '✅ Connected' : '❌ Error';
            }
            
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Pond data
        const pondData = {
            name: "{{ pond.name }}",
            lat: {{ pond.latitude }},
            lng: {{ pond.longitude }},
            {% if weather_data %}
            weather: {
                temperature: {{ weather_data.temperature }},
                humidity: {{ weather_data.humidity }},
                wind_speed: {{ weather_data.wind_speed }},
                condition: "{{ weather_data.condition }}"
            }
            {% else %}
            weather: null
            {% endif %}
        };

        // Global map variables
        let map;
        let marker;
        let infoWindow;

        // Initialize Google Maps
        function initMap() {
            try {
                updateStatus('🗺️ Creating Google Maps...', 'info');
                
                // Create map
                map = new google.maps.Map(document.getElementById('weather-map'), {
                    center: { lat: pondData.lat, lng: pondData.lng },
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.SATELLITE,
                    mapTypeControl: true,
                    fullscreenControl: true,
                    streetViewControl: true,
                    zoomControl: true
                });
                
                updateStatus('📍 Adding pond marker...', 'info');
                
                // Create marker
                marker = new google.maps.Marker({
                    position: { lat: pondData.lat, lng: pondData.lng },
                    map: map,
                    title: pondData.name,
                    animation: google.maps.Animation.DROP,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 12,
                        fillColor: '#667eea',
                        fillOpacity: 0.8,
                        strokeColor: 'white',
                        strokeWeight: 3
                    }
                });
                
                // Create info window content
                let content = `
                    <div style="padding: 15px; max-width: 300px;">
                        <h4 style="color: #667eea; margin-bottom: 10px;">
                            <i class="fas fa-fish"></i> ${pondData.name}
                        </h4>
                        <p><strong>📍 Location:</strong> ${pondData.lat}, ${pondData.lng}</p>
                `;
                
                if (pondData.weather) {
                    content += `
                        <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 10px; border-radius: 8px; margin: 10px 0;">
                            <div style="text-align: center; font-weight: bold; margin-bottom: 8px;">🌤️ Current Weather</div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 0.9em;">
                                <div style="text-align: center;">
                                    <div style="font-weight: bold;">${pondData.weather.temperature}°C</div>
                                    <div style="opacity: 0.9;">Temperature</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-weight: bold;">${pondData.weather.humidity}%</div>
                                    <div style="opacity: 0.9;">Humidity</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-weight: bold;">${pondData.weather.wind_speed} km/h</div>
                                    <div style="opacity: 0.9;">Wind</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-weight: bold;">${pondData.weather.condition}</div>
                                    <div style="opacity: 0.9;">Condition</div>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    content += `<p style="color: #666;"><em>⚠️ No weather data available</em></p>`;
                }
                
                content += `
                        <div style="text-align: center; margin-top: 10px;">
                            <span style="background: #667eea; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em;">
                                🗺️ Google Maps
                            </span>
                        </div>
                    </div>
                `;
                
                // Create info window
                infoWindow = new google.maps.InfoWindow({
                    content: content
                });
                
                // Add click listener
                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });
                
                updateStatus('✅ Google Maps loaded successfully!', 'success');
                
                // Auto-open info window after a delay
                setTimeout(() => {
                    infoWindow.open(map, marker);
                    updateStatus('🎉 Weather map ready!', 'success');
                }, 1500);
                
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                console.error('Google Maps Error:', error);
            }
        }

        // Error handler
        function handleMapError() {
            updateStatus('❌ Failed to load Google Maps API', 'error');
        }

        // Initialize on load
        updateStatus('⏳ Initializing weather map...', 'info');
    </script>
    
    <script 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap" 
        async 
        defer 
        onerror="handleMapError()">
    </script>
</body>
</html>
