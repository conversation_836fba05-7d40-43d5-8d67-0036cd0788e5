from django.db import models
from django.utils import timezone
from django.contrib.auth import get_user_model

User = get_user_model()


class MLModel(models.Model):
    """Model for storing machine learning model metadata"""
    MODEL_TYPE_CHOICES = (
        ('WATER_QUALITY_PREDICTION', 'Water Quality Prediction'),
        ('DISEASE_DETECTION', 'Disease Detection'),
        ('GROWTH_PREDICTION', 'Growth Prediction'),
        ('FEEDING_OPTIMIZATION', 'Feeding Optimization'),
        ('HARVEST_PREDICTION', 'Harvest Prediction'),
        ('ANOMALY_DETECTION', 'Anomaly Detection'),
    )
    
    STATUS_CHOICES = (
        ('TRAINING', 'Training'),
        ('ACTIVE', 'Active'),
        ('INACTIVE', 'Inactive'),
        ('FAILED', 'Failed'),
        ('DEPRECATED', 'Deprecated'),
    )
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    model_type = models.CharField(max_length=50, choices=MODEL_TYPE_CHOICES)
    version = models.CharField(max_length=20)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='TRAINING')
    
    # Model file path
    model_file = models.FileField(upload_to='ml_models/', null=True, blank=True)
    
    # Model performance metrics
    accuracy = models.FloatField(null=True, blank=True)
    precision = models.FloatField(null=True, blank=True)
    recall = models.FloatField(null=True, blank=True)
    f1_score = models.FloatField(null=True, blank=True)    # Training metadata
    training_data_start_date = models.DateField(null=True, blank=True)
    training_data_end_date = models.DateField(null=True, blank=True)
    training_duration = models.DurationField(null=True, blank=True)
    training_parameters = models.JSONField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    last_trained_at = models.DateTimeField(null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='ml_models')
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'ML Model'
        verbose_name_plural = 'ML Models'
    
    def __str__(self):
        return f"{self.name} (v{self.version})"


class TrainingJob(models.Model):
    """Model for tracking ML training jobs"""
    STATUS_CHOICES = (
        ('PENDING', 'Pending'),
        ('RUNNING', 'Running'),
        ('COMPLETED', 'Completed'),
        ('FAILED', 'Failed'),
        ('CANCELLED', 'Cancelled'),
    )

    model_type = models.CharField(max_length=50, choices=MLModel.MODEL_TYPE_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')

    # Timing
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Results
    metrics = models.JSONField(default=dict)
    error_message = models.TextField(blank=True)

    # Metadata
    training_data_count = models.IntegerField(null=True, blank=True)
    created_model = models.ForeignKey(MLModel, on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['model_type', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f"Training Job: {self.get_model_type_display()} - {self.status}"

    @property
    def duration(self):
        """Calculate job duration"""
        if self.started_at and self.completed_at:
            return self.completed_at - self.started_at
        elif self.started_at:
            from django.utils import timezone
            return timezone.now() - self.started_at
        return None


class Prediction(models.Model):
    """Model for storing ML model predictions"""
    PREDICTION_TYPE_CHOICES = (
        ('WATER_QUALITY', 'Water Quality'),
        ('DISEASE_RISK', 'Disease Risk'),
        ('GROWTH_RATE', 'Growth Rate'),
        ('FEEDING_AMOUNT', 'Feeding Amount'),
        ('HARVEST_TIME', 'Harvest Time'),
        ('ANOMALY_SCORE', 'Anomaly Score'),
    )
    
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE, related_name='predictions')
    prediction_type = models.CharField(max_length=50, choices=PREDICTION_TYPE_CHOICES)
    pond = models.ForeignKey('ponds.Pond', on_delete=models.CASCADE, related_name='ml_predictions')
    water_quality_reading = models.ForeignKey('water_quality.WaterQualityReading', on_delete=models.CASCADE, null=True, blank=True, related_name='ml_predictions')
    
    # Prediction details
    prediction_value = models.JSONField(help_text="The actual prediction output")
    confidence = models.FloatField()
    input_data = models.JSONField(help_text="Input data used for prediction")
      # Validation
    validated = models.BooleanField(default=False)
    actual_value = models.JSONField(null=True, blank=True, help_text="Actual outcome for validation")
    validated_at = models.DateTimeField(null=True, blank=True)
    validated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='validated_predictions')
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Prediction'
        verbose_name_plural = 'Predictions'
    
    def __str__(self):
        return f"{self.prediction_type} for {self.pond.name} at {self.created_at}"


class AnomalyDetection(models.Model):
    """Model for storing anomaly detection results"""
    ANOMALY_TYPE_CHOICES = (
        ('WATER_QUALITY', 'Water Quality'),
        ('FEEDING_BEHAVIOR', 'Feeding Behavior'),
        ('GROWTH_RATE', 'Growth Rate'),
        ('DISEASE_SYMPTOMS', 'Disease Symptoms'),
        ('EQUIPMENT_MALFUNCTION', 'Equipment Malfunction'),
        ('OTHER', 'Other'),
    )
    
    SEVERITY_CHOICES = (
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('CRITICAL', 'Critical'),
    )
    
    STATUS_CHOICES = (
        ('DETECTED', 'Detected'),
        ('INVESTIGATING', 'Investigating'),
        ('RESOLVED', 'Resolved'),
        ('FALSE_POSITIVE', 'False Positive'),
    )
    
    model = models.ForeignKey(MLModel, on_delete=models.CASCADE, related_name='anomalies')
    pond = models.ForeignKey('ponds.Pond', on_delete=models.CASCADE, related_name='ml_anomalies')
    
    # Anomaly details
    anomaly_type = models.CharField(max_length=50, choices=ANOMALY_TYPE_CHOICES)
    description = models.TextField()
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='DETECTED')    # Detection details
    detected_at = models.DateTimeField(default=timezone.now)
    data_points = models.JSONField(help_text="Data points that triggered the anomaly")
    confidence = models.FloatField()
    
    # Resolution details
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(null=True, blank=True)
    resolved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='resolved_anomalies')
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-detected_at']
        verbose_name = 'Anomaly Detection'
        verbose_name_plural = 'Anomaly Detections'
    
    def __str__(self):
        return f"{self.anomaly_type} anomaly in {self.pond.name} - {self.severity}"
    
    def resolve(self, user, notes=""):
        """Mark the anomaly as resolved"""
        self.status = 'RESOLVED'
        self.resolved_at = timezone.now()
        self.resolved_by = user
        self.resolution_notes = notes
        self.save()
        
        # Create an activity log
        from audit.models import ActivityLog
        ActivityLog.objects.create(
            user=user,
            action='RESOLVE_ANOMALY',
            object_type='AnomalyDetection',
            object_id=self.id,
            description=f"Resolved {self.anomaly_type} anomaly in {self.pond.name}"
        )
    
    def mark_as_investigating(self, user):
        """Mark the anomaly as being investigated"""
        self.status = 'INVESTIGATING'
        self.save()
        
        # Create an activity log
        from audit.models import ActivityLog
        ActivityLog.objects.create(
            user=user,
            action='INVESTIGATE_ANOMALY',
            object_type='AnomalyDetection',
            object_id=self.id,
            description=f"Started investigating {self.anomaly_type} anomaly in {self.pond.name}"
        )
    
    def mark_as_false_positive(self, user, notes=""):
        """Mark the anomaly as a false positive"""
        self.status = 'FALSE_POSITIVE'
        self.resolved_at = timezone.now()
        self.resolved_by = user
        self.resolution_notes = notes
        self.save()
        
        # Create an activity log
        from audit.models import ActivityLog
        ActivityLog.objects.create(
            user=user,
            action='FALSE_POSITIVE_ANOMALY',
            object_type='AnomalyDetection',
            object_id=self.id,
            description=f"Marked {self.anomaly_type} anomaly in {self.pond.name} as false positive"
        )


class TrainingDataset(models.Model):
    """Model for storing training datasets for ML models"""
    DATASET_TYPE_CHOICES = (
        ('WATER_QUALITY', 'Water Quality'),
        ('FEEDING_DATA', 'Feeding Data'),
        ('GROWTH_DATA', 'Growth Data'),
        ('DISEASE_DATA', 'Disease Data'),
        ('HARVEST_DATA', 'Harvest Data'),
        ('ENVIRONMENTAL_DATA', 'Environmental Data'),
    )
    
    name = models.CharField(max_length=100)
    description = models.TextField()
    dataset_type = models.CharField(max_length=50, choices=DATASET_TYPE_CHOICES)
    version = models.CharField(max_length=20)
    
    # Dataset file
    dataset_file = models.FileField(upload_to='ml_datasets/', null=True, blank=True)
    
    # Dataset metadata
    start_date = models.DateField()
    end_date = models.DateField()
    num_records = models.IntegerField()
    features = models.JSONField(help_text="List of features in the dataset")
    
    # Preprocessing information
    preprocessing_steps = models.JSONField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='training_datasets')
    
    # Related models
    ml_models = models.ManyToManyField(MLModel, related_name='training_datasets', blank=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Training Dataset'
        verbose_name_plural = 'Training Datasets'
    
    def __str__(self):
        return f"{self.name} (v{self.version})"


class OptimizationRecommendation(models.Model):
    """Model for storing AI-generated optimization recommendations"""
    RECOMMENDATION_TYPE_CHOICES = (
        ('FEEDING_OPTIMIZATION', 'Feeding Optimization'),
        ('WATER_QUALITY_IMPROVEMENT', 'Water Quality Improvement'),
        ('DISEASE_PREVENTION', 'Disease Prevention'),
        ('GROWTH_ENHANCEMENT', 'Growth Enhancement'),
        ('COST_REDUCTION', 'Cost Reduction'),
        ('HARVEST_TIMING', 'Harvest Timing'),
        ('EQUIPMENT_MAINTENANCE', 'Equipment Maintenance'),
        ('ENVIRONMENTAL_CONTROL', 'Environmental Control'),
    )

    PRIORITY_CHOICES = (
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    )

    STATUS_CHOICES = (
        ('PENDING', 'Pending'),
        ('IMPLEMENTED', 'Implemented'),
        ('REJECTED', 'Rejected'),
        ('EXPIRED', 'Expired'),
    )

    model = models.ForeignKey(MLModel, on_delete=models.CASCADE, related_name='recommendations')
    pond = models.ForeignKey('ponds.Pond', on_delete=models.CASCADE, related_name='ml_recommendations')

    # Recommendation details
    recommendation_type = models.CharField(max_length=50, choices=RECOMMENDATION_TYPE_CHOICES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='PENDING')

    # AI analysis details
    confidence = models.FloatField()
    expected_impact = models.JSONField(help_text="Expected impact metrics")
    supporting_data = models.JSONField(help_text="Data supporting the recommendation")

    # Implementation details
    implementation_steps = models.JSONField(null=True, blank=True)
    estimated_cost = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    estimated_savings = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    implementation_timeline = models.CharField(max_length=100, null=True, blank=True)

    # Tracking
    implemented_at = models.DateTimeField(null=True, blank=True)
    implemented_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='implemented_recommendations')
    implementation_notes = models.TextField(null=True, blank=True)

    # Validation
    actual_impact = models.JSONField(null=True, blank=True, help_text="Actual impact after implementation")
    validated_at = models.DateTimeField(null=True, blank=True)
    validated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='validated_recommendations')

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Optimization Recommendation'
        verbose_name_plural = 'Optimization Recommendations'

    def __str__(self):
        return f"{self.title} for {self.pond.name} - {self.priority}"

    def implement(self, user, notes=""):
        """Mark the recommendation as implemented"""
        self.status = 'IMPLEMENTED'
        self.implemented_at = timezone.now()
        self.implemented_by = user
        self.implementation_notes = notes
        self.save()

        # Create an activity log
        from audit.models import ActivityLog
        ActivityLog.objects.create(
            user=user,
            action='IMPLEMENT_RECOMMENDATION',
            object_type='OptimizationRecommendation',
            object_id=self.id,
            description=f"Implemented recommendation: {self.title} for {self.pond.name}"
        )

    def reject(self, user, notes=""):
        """Mark the recommendation as rejected"""
        self.status = 'REJECTED'
        self.implementation_notes = notes
        self.save()

        # Create an activity log
        from audit.models import ActivityLog
        ActivityLog.objects.create(
            user=user,
            action='REJECT_RECOMMENDATION',
            object_type='OptimizationRecommendation',
            object_id=self.id,
            description=f"Rejected recommendation: {self.title} for {self.pond.name}"
        )


# ===== AI ALERT FUNCTIONALITY MOVED TO ALERTS APP =====
# The AI alert functionality has been consolidated into the main alerts app
# for better organization and to reduce app fragmentation.
# 
# Previous models (AIAlertRule, AIAlert, AIAlertNotificationSetting) are now:
# - AlertRule in alerts app (with AI/ML capabilities)
# - Alert in alerts app (with AI/ML alert types)
# - AlertNotificationSetting in alerts app (with AI/ML notification preferences)
#
# For backwards compatibility, aliases are provided in the alerts app.
