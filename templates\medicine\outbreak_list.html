<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disease Outbreaks - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .medicine-icon {
        font-size: 2.5rem;
        opacity: 0.9;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-action i {
        font-size: 1.1rem;
    }
    
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 15px 15px 0 0 !important;
    }
    
    .outbreak-card {
        border-left: 4px solid #e9ecef;
    }
    
    .outbreak-card.severity-mild {
        border-left-color: #28a745;
        background: rgba(40, 167, 69, 0.03);
    }
    
    .outbreak-card.severity-moderate {
        border-left-color: #ffc107;
        background: rgba(255, 193, 7, 0.03);
    }
    
    .outbreak-card.severity-severe {
        border-left-color: #fd7e14;
        background: rgba(253, 126, 20, 0.03);
    }
    
    .outbreak-card.severity-critical {
        border-left-color: #dc3545;
        background: rgba(220, 53, 69, 0.03);
    }
    
    .filter-section {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-virus medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Disease Outbreaks</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Disease Outbreaks</h2>
                    <p class="mb-0 opacity-75">Monitor and manage disease outbreaks</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Total Outbreaks</small>
                                <strong>{{ outbreaks|length|default:"0" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="/medicine/diseases/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-bacteria"></i> Diseases
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">Loading...</span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/outbreaks/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Report Outbreak
            </a>
            <a href="/medicine/diseases/" class="quick-action">
                <i class="fas fa-bacteria"></i>
                View Diseases
            </a>
            <a href="/medicine/analysis/" class="quick-action">
                <i class="fas fa-chart-bar"></i>
                Analysis
            </a>
        </div>

        <!-- Filters -->
        <div class="filter-section">
            <h5 class="mb-3">Filter Outbreaks</h5>
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="disease" class="form-label">Disease</label>
                    <select name="disease" id="disease" class="form-select">
                        <option value="">All Diseases</option>
                        {% for disease in diseases %}
                        <option value="{{ disease.id }}" {% if selected_disease == disease.id|stringformat:"s" %}selected{% endif %}>
                            {{ disease.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="pond" class="form-label">Pond</label>
                    <select name="pond" id="pond" class="form-select">
                        <option value="">All Ponds</option>
                        {% for pond in ponds %}
                        <option value="{{ pond.id }}" {% if selected_pond == pond.id|stringformat:"s" %}selected{% endif %}>
                            {{ pond.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="from_date" class="form-label">From Date</label>
                    <input type="date" name="from_date" id="from_date" class="form-control" value="{{ from_date }}">
                </div>
                <div class="col-md-2">
                    <label for="to_date" class="form-label">To Date</label>
                    <input type="date" name="to_date" id="to_date" class="form-control" value="{{ to_date }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <div class="d-flex gap-2 w-100">
                        <button type="submit" class="btn btn-primary flex-fill">Filter</button>
                        <a href="/medicine/outbreaks/" class="btn btn-outline-secondary">Reset</a>
                    </div>
                </div>
            </form>
        </div>

        <!-- Outbreaks List -->
        <div class="row">
            {% if outbreaks %}
            {% for outbreak in outbreaks %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card outbreak-card severity-{{ outbreak.severity }}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">{{ outbreak.disease.name }}</h6>
                        <span class="badge bg-{% if outbreak.severity == 'mild' %}success{% elif outbreak.severity == 'moderate' %}warning{% elif outbreak.severity == 'severe' %}orange{% else %}danger{% endif %}">
                            {{ outbreak.get_severity_display }}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <p class="text-muted mb-1 small">Pond</p>
                            <p class="mb-0">{{ outbreak.pond.name }}</p>
                        </div>
                        
                        <div class="mb-2">
                            <p class="text-muted mb-1 small">Duration</p>
                            <p class="mb-0">
                                {{ outbreak.start_date|date:"M d, Y" }}
                                {% if outbreak.end_date %}
                                - {{ outbreak.end_date|date:"M d, Y" }}
                                {% else %}
                                - <span class="text-danger">Ongoing</span>
                                {% endif %}
                            </p>
                        </div>
                        
                        {% if outbreak.mortality_rate %}
                        <div class="mb-2">
                            <p class="text-muted mb-1 small">Mortality Rate</p>
                            <p class="mb-0">{{ outbreak.mortality_rate }}%</p>
                        </div>
                        {% endif %}
                        
                        {% if outbreak.notes %}
                        <div class="mb-2">
                            <p class="text-muted mb-1 small">Notes</p>
                            <p class="mb-0 small">{{ outbreak.notes|truncatechars:100 }}</p>
                        </div>
                        {% endif %}
                        
                        <div class="mb-2">
                            <p class="text-muted mb-1 small">Reported</p>
                            <p class="mb-0 small">
                                {{ outbreak.created_at|date:"M d, Y H:i" }}
                                {% if outbreak.reported_by %}
                                by {{ outbreak.reported_by.get_full_name|default:outbreak.reported_by.username }}
                                {% endif %}
                            </p>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex gap-2">
                            <a href="/medicine/outbreaks/{{ outbreak.id }}/" class="btn btn-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i> View Details
                            </a>
                            <a href="/medicine/outbreaks/{{ outbreak.id }}/edit/" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-virus text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5>No Disease Outbreaks Found</h5>
                        <p class="text-muted">
                            {% if selected_disease or selected_pond or from_date or to_date %}
                            No outbreaks match your filter criteria.
                            <a href="/medicine/outbreaks/">Clear filters</a>
                            {% else %}
                            No disease outbreaks have been reported yet.
                            {% endif %}
                        </p>
                        <a href="/medicine/outbreaks/create/" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Report Outbreak
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Update time immediately and then every minute
        updateTime();
        setInterval(updateTime, 60000);
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
