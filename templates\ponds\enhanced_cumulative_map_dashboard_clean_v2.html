{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unified Map Main Dashboard with Enhanced Cards</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/unified-map-cards.css' %}" rel="stylesheet">

<style>
    /* Reset and base styles */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Roboto', sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        margin: 0;
        padding: 0;
    }
    .dashboard-container {
        padding: 20px;
        max-width: 1400px;
        margin: 0 auto;
    }

    /* New Main Header Styles - Matching the image design */
    .main-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
        overflow: hidden;
        position: relative;
    }

    .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 30px 40px;
        color: white;
        position: relative;
        z-index: 2;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .header-icon {
        font-size: 2.8rem;
        opacity: 0.95;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }

    .header-text h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        text-shadow: 0 2px 8px rgba(0,0,0,0.15);
        letter-spacing: -0.5px;
    }

    .header-text p {
        font-size: 1.1rem;
        margin: 0;
        opacity: 0.9;
        font-weight: 400;
        text-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .header-actions {
        display: flex;
        gap: 12px;
    }

    .header-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        border-radius: 10px;
        padding: 12px 24px;
        font-size: 0.95rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        backdrop-filter: blur(15px);
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        min-width: 110px;
        justify-content: center;
    }

    .header-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        border-color: rgba(255, 255, 255, 0.4);
    }

    .header-btn:active {
        transform: translateY(0);
    }

    .header-btn i {
        font-size: 1.1rem;
    }

    /* Add subtle pattern overlay */
    .main-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.03)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.02)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.4;
        z-index: 1;
    }

    /* Navigation Menu Styles */
    .main-navigation {
        background: white;
        border-radius: 15px;
        margin-bottom: 20px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: visible; /* Changed from hidden to visible to allow dropdowns */
        border: 1px solid rgba(102, 126, 234, 0.1);
        position: relative; /* Added to establish positioning context */
    }

    .nav-container {
        display: flex;
        align-items: center;
        padding: 0;
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .nav-container::-webkit-scrollbar {
        display: none;
    }

    .nav-item {
        flex: 0 0 auto;
        position: relative;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 16px 20px;
        text-decoration: none;
        color: #495057;
        font-weight: 500;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        border-bottom: 3px solid transparent;
        white-space: nowrap;
        position: relative;
        background: transparent;
    }

    .nav-link:hover {
        color: #667eea;
        background: rgba(102, 126, 234, 0.05);
        text-decoration: none;
    }

    .nav-link.active {
        color: #667eea;
        border-bottom-color: #667eea;
        background: rgba(102, 126, 234, 0.08);
    }

    .nav-link i {
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
    }

    .nav-badge {
        background: #dc3545;
        color: white;
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 5px;
        font-weight: 600;
    }

    .nav-badge.success {
        background: #28a745;
    }

    .nav-badge.warning {
        background: #ffc107;
        color: #212529;
    }

    .nav-badge.info {
        background: #17a2b8;
    }

    /* Dropdown styles for navigation */
    .nav-dropdown {
        position: relative;
    }

    .nav-dropdown-content {
        position: absolute;
        top: 100%;
        left: 0;
        background: white;
        min-width: 220px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-radius: 8px;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all 0.3s ease;
        border: 1px solid rgba(0,0,0,0.1);
        pointer-events: none;
    }

    .nav-dropdown:hover .nav-dropdown-content,
    .nav-dropdown-content:hover,
    .nav-dropdown.active .nav-dropdown-content {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
        pointer-events: auto;
    }

    .nav-dropdown:hover .nav-link {
        color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }

    .nav-dropdown-item {
        display: block;
        padding: 12px 16px;
        color: #495057;
        text-decoration: none;
        font-size: 0.85rem;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .nav-dropdown-item:last-child {
        border-bottom: none;
    }

    .nav-dropdown-item:hover {
        background: rgba(102, 126, 234, 0.05);
        color: #667eea;
        text-decoration: none;
    }

    .nav-dropdown-item i {
        width: 16px;
        margin-right: 8px;
        font-size: 0.9rem;
    }

    /* Mobile responsive navigation */
    @media (max-width: 768px) {
        .nav-link {
            padding: 12px 16px;
            font-size: 0.8rem;
        }

        .nav-link span {
            display: none;
        }

        .nav-link i {
            margin-right: 0;
        }
    }

    .map-container {
        width: 100%;
        height: 600px; /* Slightly reduced height for top position */
        border-radius: 15px;
        margin: 20px 0;
        position: relative;
        background: #f8f9fa;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }

    #map {
        width: 100%;
        height: 100%;
        border-radius: 20px;
    }

    .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: #666;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .loading::before {
        content: "🗺️ ";
        animation: spin 2s linear infinite;
        margin-right: 10px;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .map-info {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        padding: 20px;
        border-radius: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .error {
        background: linear-gradient(135deg, #ff7675, #fd79a8);
        color: white;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(255,118,117,0.3);
    }

    .dashboard-header {
        text-align: center;
        margin-bottom: 30px;
        padding: 30px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        color: white;
        box-shadow: 0 15px 35px rgba(102,126,234,0.3);
    }

    .dashboard-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .dashboard-header p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }

    /* Dashboard Stats Grid */
    .dashboard-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #667eea, #764ba2);
    }

    .stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #636e72;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
    }

    /* Dashboard Grid Layout */
    .dashboard-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .main-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .sidebar-content {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    /* GPS Stats Grid */
    .gps-stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }

    .gps-stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
    }

    .gps-stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    .gps-stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .gps-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .gps-stat-label {
        color: #636e72;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.8rem;
    }

    /* GPS Control Panel */
    .gps-control-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .gps-panel-title {
        font-size: 1rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .gps-status-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
    }

    .gps-status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.85rem;
        font-weight: 500;
    }

    .gps-status-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .gps-status-working { background: #00b894; }
    .gps-status-available { background: #0984e3; }
    .gps-status-break { background: #fdcb6e; }
    .gps-status-traveling { background: #6c5ce7; }
    .gps-status-offline { background: #636e72; }

    .gps-layer-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }

    .gps-layer-btn {
        background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-layer-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    .gps-layer-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .gps-layer-btn.gps-ponds.active {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
    }

    .gps-layer-btn.gps-workers.active {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        box-shadow: 0 4px 15px rgba(139, 92, 246, 0.4);
    }

    .gps-layer-btn.gps-aerators.active {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        box-shadow: 0 4px 15px rgba(6, 182, 212, 0.4);
    }

    .gps-layer-btn.gps-geofences.active {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
    }

    .gps-map-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .gps-map-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .gps-map-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    /* Recent Activities Panel */
    .recent-activities-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .activities-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .activities-title {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 8px;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .view-all-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .view-all-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    .activities-list {
        display: flex;
        flex-direction: column;
        gap: 15px;
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 15px;
        background: rgba(255,255,255,0.7);
        border-radius: 12px;
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
        position: relative;
    }

    .activity-item:hover {
        background: rgba(255,255,255,0.9);
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        flex-shrink: 0;
        box-shadow: 0 4px 10px rgba(0,0,0,0.2);
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-size: 0.95rem;
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 4px;
        line-height: 1.3;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #636e72;
        font-weight: 500;
    }

    .activity-description {
        font-size: 0.85rem;
        color: #636e72;
        margin-top: 5px;
        line-height: 1.4;
    }

    /* Activity Type Specific Styles */
    .activity-pond-creation {
        border-left-color: #00b894;
    }
    .activity-pond-creation .activity-icon {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    }

    .activity-farm-creation {
        border-left-color: #667eea;
    }
    .activity-farm-creation .activity-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .activity-water-alert {
        border-left-color: #e17055;
    }
    .activity-water-alert .activity-icon {
        background: linear-gradient(135deg, #e17055 0%, #d63031 100%);
    }

    .activity-worker-assignment {
        border-left-color: #8b5cf6;
    }
    .activity-worker-assignment .activity-icon {
        background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    }

    .activity-aerator-status {
        border-left-color: #06b6d4;
    }
    .activity-aerator-status .activity-icon {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    }

    .activity-weather-update {
        border-left-color: #f59e0b;
    }
    .activity-weather-update .activity-icon {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    .activity-feeding {
        border-left-color: #10b981;
    }
    .activity-feeding .activity-icon {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .activity-maintenance {
        border-left-color: #6b7280;
    }
    .activity-maintenance .activity-icon {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    }

    .activity-harvest {
        border-left-color: #f97316;
    }
    .activity-harvest .activity-icon {
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    }

    /* Widget Styles */
    .widget {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .widget-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f3f4;
    }

    .widget-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .widget-actions {
        display: flex;
        gap: 10px;
    }

    .widget-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 15px;
        padding: 8px 16px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .widget-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
    }

    /* Weather Widget */
    .weather-widget {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        color: white;
    }

    .weather-current {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 20px;
    }

    .weather-icon {
        font-size: 3rem;
    }

    .weather-temp {
        font-size: 2.5rem;
        font-weight: 700;
    }

    .weather-desc {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .weather-details {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .weather-detail {
        background: rgba(255,255,255,0.2);
        padding: 10px;
        border-radius: 10px;
        text-align: center;
    }

    /* Alerts Widget */
    .alerts-widget {
        background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
        color: white;
    }

    .alert-item {
        background: rgba(255,255,255,0.2);
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .alert-item:last-child {
        margin-bottom: 0;
    }

    /* Recent Activities */
    .recent-activities {
        max-height: 300px;
        overflow-y: auto;
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
    }

    .activity-item:hover {
        background: #e9ecef;
        transform: translateX(5px);
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 2px;
    }

    .activity-time {
        font-size: 0.85rem;
        color: #636e72;
    }

    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .quick-action {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .quick-action:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 184, 148, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-action-icon {
        font-size: 2rem;
    }

    .quick-action-text {
        font-weight: 600;
        font-size: 0.9rem;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .dashboard-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .dashboard-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .quick-actions {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 480px) {
        .dashboard-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
</head>

<body>
<div class="dashboard-container">
    <!-- New Main Header -->
    <div class="main-header">
        <div class="header-content">
            <div class="header-left">
                <div class="header-icon">
                    <i class="fas fa-water"></i>
                </div>                <div class="header-text">
                    <h1>Unified Map Main Dashboard</h1>
                    <p>Complete overview of your shrimp farming operations with interactive mapping</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="header-btn refresh-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </button>
                <button class="header-btn export-btn" onclick="exportReport()">
                    <i class="fas fa-download"></i>
                    <span>Export</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Navigation Menu -->
    <div class="main-navigation">
        <div class="nav-container">            <!-- Main Dashboard -->
            <div class="nav-item">
                <a href="/" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Main Dashboard</span>
                </a>
            </div>

            <!-- Farms Management -->
            <div class="nav-item">
                <a href="{% url 'ponds:farm_list' %}" class="nav-link">
                    <i class="fas fa-warehouse"></i>
                    <span>Farms</span>
                    <span class="nav-badge success" id="farms-badge">{{ total_farms|default:0 }}</span>
                </a>
            </div>

            <!-- Ponds Management -->
            <div class="nav-item">
                <a href="{% url 'ponds:pond_list' %}" class="nav-link">
                    <i class="fas fa-water"></i>
                    <span>Ponds</span>
                    <span class="nav-badge info" id="ponds-badge">{{ total_ponds|default:0 }}</span>
                </a>
            </div>

            <!-- Aerators Management -->
            <div class="nav-item">
                <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="nav-link">
                    <i class="fas fa-fan"></i>
                    <span>Aerators</span>
                    <span class="nav-badge warning" id="aerators-badge">{{ total_aerators|default:0 }}</span>
                </a>
            </div>            <!-- Workers & GPS -->
            <div class="nav-item">
                <a href="{% url 'labor:dashboard' %}" class="nav-link">
                    <i class="fas fa-users"></i>
                    <span>Workers</span>
                    <span class="nav-badge" id="workers-badge">{{ total_workers|default:0 }}</span>
                </a>
            </div>

            <!-- Weather & Environment -->
            <div class="nav-item">
                <a href="http://127.0.0.1:8000/weather/" class="nav-link">
                    <i class="fas fa-cloud-sun"></i>
                    <span>Weather</span>
                </a>
            </div>

            <!-- Reports & Analytics -->
            <div class="nav-item">
                <a href="{% url 'ponds:unified_dashboard' %}" class="nav-link">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </a>
            </div>

            <!-- Settings -->
            <div class="nav-item">
                <a href="{% url 'ponds:system_settings' %}" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Enhanced Map Container - NOW AT THE VERY TOP -->
    <div class="map-container" style="margin-bottom: 20px;">        <div id="map" class="loading">
            <div>Loading Unified Map Main Dashboard...</div>
            <small>Preparing pond locations and weather integration</small>
        </div>

        <!-- Map Controls -->
        <div class="map-controls">
            <button class="map-control-btn" onclick="toggleMapType()" title="Toggle Map Type">
                <i class="fas fa-layer-group"></i>
            </button>
            <button class="map-control-btn" onclick="centerOnPonds()" title="Center on Ponds">
                <i class="fas fa-crosshairs"></i>
            </button>
            <button class="map-control-btn" onclick="refreshMapData()" title="Refresh Data">
                <i class="fas fa-sync-alt"></i>
            </button>
        </div>

        <!-- Map Legend -->
        <div class="map-legend">
            <div class="legend-title">
                <i class="fas fa-map-signs"></i> Map Legend
            </div>
            <div class="legend-item">
                <div class="legend-marker pond"></div>
                <span>Ponds</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker farm"></div>
                <span>Farms</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker worker"></div>
                <span>Workers</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker aerator"></div>
                <span>Aerators</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker geofence"></div>
                <span>Geofences</span>
            </div>
        </div>

        <!-- Pond Information Card (will be populated by JavaScript) -->
        <div id="pond-info-card" class="pond-info-card" style="display: none;">
            <!-- Card content will be dynamically generated -->
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Main Content -->
        <div class="main-content">
            <!-- Enhanced GPS Statistics -->
            <div class="gps-stats-grid">
                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="gps-stat-value" id="farms-count">{{ total_farms|default:0 }}</div>
                    <div class="gps-stat-label">Active Farms</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <div class="gps-stat-value" id="ponds-count">{{ total_ponds|default:0 }}</div>
                    <div class="gps-stat-label">Monitored Ponds</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="gps-stat-value" id="workers-count">{{ total_workers|default:0 }}</div>
                    <div class="gps-stat-label">GPS Workers</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-fan"></i>
                    </div>
                    <div class="gps-stat-value" id="aerators-count">{{ total_aerators|default:0 }}</div>
                    <div class="gps-stat-label">Active Aerators</div>
                </div>

                <div class="gps-stat-card">
                    <div class="gps-stat-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="gps-stat-value" id="security-zones">{{ total_geofences|default:0 }}</div>
                    <div class="gps-stat-label">Security Zones</div>
                </div>
            </div>

            <!-- Recent Activities Section -->
            <div class="recent-activities-panel">
                <div class="activities-header">
                    <div class="activities-title">
                        <i class="fas fa-clock"></i>
                        Recent Activities
                    </div>
                    <button class="view-all-btn" onclick="viewAllActivities()">
                        <i class="fas fa-list"></i>
                        View All
                    </button>
                </div>

                <div class="activities-list" id="activities-list">
                    <!-- Activities will be dynamically loaded here -->
                </div>
            </div>

            <!-- Enhanced GPS Control Panel -->
            <div class="gps-control-panel">
                <div class="gps-panel-title">
                    <i class="fas fa-circle"></i>
                    WORKER STATUS LEGEND
                </div>
                <div class="gps-status-legend">
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-working"></div>
                        <span>Working</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-available"></div>
                        <span>Available</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-break"></div>
                        <span>On Break</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-traveling"></div>
                        <span>Traveling</span>
                    </div>
                    <div class="gps-status-item">
                        <div class="gps-status-dot gps-status-offline"></div>
                        <span>Offline</span>
                    </div>
                </div>

                <!-- Layer Controls -->
                <div class="gps-panel-title">
                    <i class="fas fa-layer-group"></i>
                    LAYER CONTROLS
                </div>
                <div class="gps-layer-controls">
                    <button class="gps-layer-btn active" id="farms-layer" onclick="toggleLayer('farms')">
                        <i class="fas fa-warehouse"></i>
                        FARMS
                    </button>
                    <button class="gps-layer-btn gps-ponds active" id="ponds-layer" onclick="toggleLayer('ponds')">
                        <i class="fas fa-water"></i>
                        PONDS
                    </button>
                    <button class="gps-layer-btn gps-workers active" id="workers-layer" onclick="toggleLayer('workers')">
                        <i class="fas fa-users"></i>
                        WORKERS
                    </button>
                    <button class="gps-layer-btn gps-aerators active" id="aerators-layer" onclick="toggleLayer('aerators')">
                        <i class="fas fa-fan"></i>
                        AERATORS
                    </button>
                    <button class="gps-layer-btn gps-geofences" id="geofences-layer" onclick="toggleLayer('geofences')">
                        <i class="fas fa-draw-polygon"></i>
                        GEOFENCES
                    </button>
                </div>

                <!-- Map Controls -->
                <div class="gps-panel-title">
                    <i class="fas fa-cog"></i>
                    MAP CONTROLS
                </div>
                <div class="gps-map-controls">
                    <button class="gps-map-btn" id="center-map" onclick="centerOnPonds()">
                        <i class="fas fa-crosshairs"></i>
                        CENTER MAP
                    </button>
                    <button class="gps-map-btn gps-refresh" id="refresh-data" onclick="refreshMapData()">
                        <i class="fas fa-sync-alt"></i>
                        REFRESH
                    </button>
                    <button class="gps-map-btn gps-fit" id="fit-all" onclick="fitAllMarkers()">
                        <i class="fas fa-expand-arrows-alt"></i>
                        FIT ALL
                    </button>
                    <button class="gps-map-btn" onclick="fullscreenMap()">
                        <i class="fas fa-expand"></i>
                        FULLSCREEN
                    </button>
                </div>
            </div>




        </div>

        <!-- Sidebar Content -->
        <div class="sidebar-content">
            <!-- Weather Widget -->
            <div class="widget weather-widget">
                <div class="widget-header" style="border-color: rgba(255,255,255,0.3);">
                    <h3 class="widget-title" style="color: white;">
                        <i class="fas fa-cloud-sun"></i>
                        Weather Status
                    </h3>
                </div>
                <div class="weather-current">
                    <div class="weather-icon">☀️</div>
                    <div>
                        <div class="weather-temp">{{ current_weather.temperature|default:"--" }}°C</div>
                        <div class="weather-desc">{{ current_weather.description|default:"No data" }}</div>
                    </div>
                </div>
                <div class="weather-details">
                    <div class="weather-detail">
                        <div>💧 Humidity</div>
                        <div>{{ current_weather.humidity|default:"--" }}%</div>
                    </div>
                    <div class="weather-detail">
                        <div>💨 Wind</div>
                        <div>{{ current_weather.wind_speed|default:"--" }} km/h</div>
                    </div>
                </div>
            </div>

            <!-- Alerts Widget -->
            <div class="widget alerts-widget">
                <div class="widget-header" style="border-color: rgba(255,255,255,0.3);">
                    <h3 class="widget-title" style="color: white;">
                        <i class="fas fa-exclamation-triangle"></i>
                        System Alerts
                    </h3>
                </div>
                {% for alert in system_alerts %}
                <div class="alert-item">
                    <i class="fas {{ alert.icon|default:'fa-exclamation-triangle' }}"></i>
                    <div>
                        <div style="font-weight: 600;">{{ alert.title|default:'System Alert' }}</div>
                        <div style="font-size: 0.85rem; opacity: 0.9;">{{ alert.message|default:'No details available' }}</div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-3">
                    <i class="fas fa-check-circle" style="font-size: 2rem; margin-bottom: 10px;"></i>
                    <p style="margin: 0; opacity: 0.9;">All systems normal</p>
                </div>
                {% endfor %}
            </div>

            <!-- Quick Actions -->
            <div class="widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-bolt"></i>                        Quick Actions
                    </h3>
                </div>                <div class="quick-actions">
                    <a href="{% url 'ponds:farm_create_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">🏢</div>
                        <div class="quick-action-text">Create Farm</div>
                    </a>
                    <a href="{% url 'ponds:create_pond_wizard' %}" class="quick-action">
                        <div class="quick-action-icon">🦐</div>
                        <div class="quick-action-text">Create Pond</div>
                    </a>
                    <a href="{% url 'ponds:farm_list' %}" class="quick-action">
                        <div class="quick-action-icon">📊</div>
                        <div class="quick-action-text">View Farms</div>
                    </a>
                    <a href="{% url 'ponds:pond_list' %}" class="quick-action">
                        <div class="quick-action-icon">📋</div>
                        <div class="quick-action-text">View Ponds</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript Section -->
<script>
    console.log('🚀 Clean Enhanced Map Dashboard Starting...');

    // CRITICAL: Define initMap function FIRST in global scope
    window.initMap = function() {
        console.log('🗺️ initMap called by Google Maps API');
        if (typeof actualInitMap === 'function') {
            actualInitMap();
        } else {
            console.error('❌ actualInitMap function not found');
        }
    };
    
    // Step 1: Initialize all data first - Using REAL data from Django context
    const mapData = {
        farms: {{ farms_data|default:"[]"|safe }},
        ponds: {{ ponds_data|default:"[]"|safe }},
        workers: {{ workers_data|default:"[]"|safe }},
        aerators: {{ aerators_data|default:"[]"|safe }},
        geofences: {{ geofences_data|default:"[]"|safe }},
        weather: {{ weather_data|default:"{}"|safe }},
        centerLat: {{ center_lat|default:"13.0827" }},
        centerLng: {{ center_lng|default:"80.2707" }},
        apiKey: "{{ google_maps_api_key }}"
    };
      console.log('📊 Map Data Loaded:', {
        farms: mapData.farms.length,
        ponds: mapData.ponds.length,
        workers: mapData.workers.length,
        aerators: mapData.aerators.length,
        geofences: mapData.geofences.length,
        weather: mapData.weather.length,
        center: [mapData.centerLat, mapData.centerLng]
    });

    // Debug: Log the actual data
    console.log('🔍 DEBUG - Farms Data:', mapData.farms);
    console.log('🔍 DEBUG - Ponds Data:', mapData.ponds);
    console.log('🔍 DEBUG - Workers Data:', mapData.workers);
    console.log('🔍 DEBUG - Aerators Data:', mapData.aerators);
    console.log('🔍 DEBUG - Geofences Data:', mapData.geofences);

    if (mapData.farms.length === 0) {
        console.warn('⚠️ No farms data found!');
    }
    if (mapData.ponds.length === 0) {
        console.warn('⚠️ No ponds data found!');
    }
      // Step 2: Update info display
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('farms-count').textContent = mapData.farms.length;
        document.getElementById('ponds-count').textContent = mapData.ponds.length;
        document.getElementById('workers-count').textContent = mapData.workers.length;
        document.getElementById('aerators-count').textContent = mapData.aerators.length;
        document.getElementById('geofences-count').textContent = mapData.geofences.length;
        document.getElementById('map-center').textContent = `${mapData.centerLat}, ${mapData.centerLng}`;

        // Update active counts
        const activeWorkers = mapData.workers.filter(w => ['working', 'active'].includes(w.status)).length;
        const activeAerators = mapData.aerators.filter(a => a.status === 'active').length;
        document.getElementById('active-workers').textContent = activeWorkers;
        document.getElementById('active-aerators').textContent = activeAerators;

        // Update focus information
        const focusElement = document.getElementById('map-focus');
        const totalItems = mapData.farms.length + mapData.ponds.length + mapData.workers.length + mapData.aerators.length;
        if (totalItems > 0) {
            focusElement.textContent = `All Locations (${totalItems} items)`;
            focusElement.style.color = '#007bff';
        } else {
            focusElement.textContent = 'Default Location (Chennai)';
            focusElement.style.color = '#6c757d';
        }
    });
    
    // Step 3: Define global variables for Google Maps
    let map;
    let markers = [];
    window.mapInitialized = false;

    // Step 4: Define the actual map initialization function
    function actualInitMap() {
        if (window.mapInitialized) {
            console.log('🔄 Map already initialized, skipping...');
            return;
        }

        console.log('🗺️ Google Maps API loaded! Initializing clean map...');
        console.log('🔍 Google object available:', typeof google !== 'undefined');
        console.log('🔍 Google Maps available:', typeof google !== 'undefined' && google.maps);

        window.mapInitialized = true;

        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }

        // Check if Google Maps is available
        if (typeof google === 'undefined' || !google.maps) {
            console.error('❌ Google Maps API not loaded properly');
            mapElement.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #dc3545;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h4>Map Loading Error</h4>
                    <p>Google Maps API failed to load. Please check your internet connection and API key.</p>
                    <button onclick="location.reload()" class="btn btn-primary">
                        <i class="fas fa-refresh"></i> Retry
                    </button>
                </div>
            `;
            return;
        }

        try {
            // Calculate optimal map center and bounds based on pond locations
            let mapCenter = { lat: mapData.centerLat || 13.0827, lng: mapData.centerLng || 80.2707 };
            let shouldFitBounds = false;
            const bounds = new google.maps.LatLngBounds();

            // Ensure we have valid center coordinates
            if (isNaN(mapCenter.lat) || isNaN(mapCenter.lng)) {
                mapCenter = { lat: 13.0827, lng: 80.2707 }; // Default to Chennai, India
                console.log('⚠️ Using default map center due to invalid coordinates');
            }

            // If we have pond data, prioritize pond locations for centering
            if (mapData.ponds.length > 0) {
                console.log('🎯 Centering map based on pond locations...');
                const validPonds = mapData.ponds.filter(pond =>
                    pond.latitude && pond.longitude &&
                    !isNaN(pond.latitude) && !isNaN(pond.longitude)
                );

                if (validPonds.length > 0) {
                    validPonds.forEach(pond => {
                        bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
                        shouldFitBounds = true;
                    });

                    // Calculate center of all valid ponds
                    const avgLat = validPonds.reduce((sum, pond) => sum + pond.latitude, 0) / validPonds.length;
                    const avgLng = validPonds.reduce((sum, pond) => sum + pond.longitude, 0) / validPonds.length;

                    if (!isNaN(avgLat) && !isNaN(avgLng)) {
                        mapCenter = { lat: avgLat, lng: avgLng };
                        console.log(`📍 Pond-centered map: ${avgLat}, ${avgLng} with ${validPonds.length} valid ponds`);
                    }
                } else {
                    console.log('⚠️ No valid pond coordinates found');
                }
            } else if (mapData.farms.length > 0) {
                console.log('🎯 Centering map based on farm locations...');
                const validFarms = mapData.farms.filter(farm =>
                    farm.latitude && farm.longitude &&
                    !isNaN(farm.latitude) && !isNaN(farm.longitude)
                );

                validFarms.forEach(farm => {
                    bounds.extend(new google.maps.LatLng(farm.latitude, farm.longitude));
                    shouldFitBounds = true;
                });
            }
            
            // Create the map with pond-focused center
            map = new google.maps.Map(mapElement, {
                zoom: shouldFitBounds ? 10 : 12, // Will be adjusted by fitBounds if needed
                center: mapCenter,
                mapTypeId: 'roadmap'
            });
            
            console.log('✅ Map created successfully with pond-focused center!');
            mapElement.classList.remove('loading');
            
            // Add farm markers
            mapData.farms.forEach((farm, index) => {
                if (farm.latitude && farm.longitude) {
                    const marker = new google.maps.Marker({
                        position: { lat: farm.latitude, lng: farm.longitude },
                        map: map,
                        title: farm.name || `Farm ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#28a745" stroke="white" stroke-width="2"/>
                                    <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">F</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });
                    
                    // Add info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>${farm.name || 'Farm'}</h4>
                                <p><strong>Location:</strong> ${farm.latitude}, ${farm.longitude}</p>
                                <p><strong>Ponds:</strong> ${farm.pond_count || 'N/A'}</p>
                            </div>
                        `
                    });
                    
                    marker.addListener('click', () => {
                        // Zoom to farm location
                        map.setCenter(marker.getPosition());
                        map.setZoom(15); // Slightly wider zoom for farms to show surrounding area

                        // Open info window
                        infoWindow.open(map, marker);
                    });
                    
                    markers.push(marker);
                }
            });

            // Add pond markers with enhanced cards
            mapData.ponds.forEach((pond, index) => {
                if (pond.latitude && pond.longitude) {
                    const marker = new google.maps.Marker({
                        position: { lat: pond.latitude, lng: pond.longitude },
                        map: map,
                        title: pond.name || `Pond ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#007bff" stroke="white" stroke-width="3"/>
                                    <circle cx="15" cy="15" r="8" fill="rgba(255,255,255,0.3)"/>
                                    <text x="15" y="19" text-anchor="middle" fill="white" font-size="10" font-weight="bold">🦐</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });

                    // Enhanced click handler to show unified card
                    marker.addListener('click', () => {
                        showEnhancedPondCard(pond, marker);

                        // Center map on clicked pond
                        map.setCenter(marker.getPosition());
                        map.setZoom(16);
                    });

                    markers.push(marker);
                }
            });
            
            // Add worker markers
            mapData.workers.forEach((worker, index) => {
                if (worker.latitude && worker.longitude) {
                    const statusColor = getWorkerStatusColor(worker.status);
                    const marker = new google.maps.Marker({
                        position: { lat: worker.latitude, lng: worker.longitude },
                        map: map,
                        title: worker.name || `Worker ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12.5" cy="12.5" r="10" fill="${statusColor}" stroke="white" stroke-width="2"/>
                                    <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">👷</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });

                    // Add info window for worker
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>👷 ${worker.name || 'Worker'}</h4>
                                <p><strong>ID:</strong> ${worker.employee_id || 'N/A'}</p>
                                <p><strong>Status:</strong> <span style="color: ${statusColor};">${worker.status || 'Unknown'}</span></p>
                                <p><strong>Skill Level:</strong> ${worker.skill_level || 'N/A'}</p>
                                <p><strong>Location:</strong> ${worker.latitude}, ${worker.longitude}</p>
                                ${worker.last_update ? `<p><strong>Last Update:</strong> ${new Date(worker.last_update).toLocaleString()}</p>` : ''}
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to worker location
                        map.setCenter(marker.getPosition());
                        map.setZoom(16); // Zoom level for detailed view

                        // Open info window
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                    bounds.extend(new google.maps.LatLng(worker.latitude, worker.longitude));
                    shouldFitBounds = true;
                }
            });

            // Add aerator markers
            mapData.aerators.forEach((aerator, index) => {
                if (aerator.latitude && aerator.longitude) {
                    const statusColor = getAeratorStatusColor(aerator.status);
                    const marker = new google.maps.Marker({
                        position: { lat: aerator.latitude, lng: aerator.longitude },
                        map: map,
                        title: aerator.name || `Aerator ${index + 1}`,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="10" cy="10" r="8" fill="${statusColor}" stroke="white" stroke-width="2"/>
                                    <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">⚡</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(20, 20)
                        }
                    });

                    // Add info window for aerator
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>⚡ ${aerator.name || 'Aerator'}</h4>
                                <p><strong>Type:</strong> ${aerator.aerator_type || 'Unknown'}</p>
                                <p><strong>Status:</strong> <span style="color: ${statusColor};">${aerator.status || 'Unknown'}</span></p>
                                <p><strong>Power:</strong> ${aerator.power_rating || 0} kW</p>
                                <p><strong>Source:</strong> ${aerator.power_source || 'Unknown'}</p>
                                <p><strong>Pond:</strong> ${aerator.pond_name || 'Unknown'}</p>
                                <p><strong>Automated:</strong> ${aerator.is_automated ? 'Yes' : 'No'}</p>
                                <p><strong>Operating Hours:</strong> ${aerator.operating_hours || 0}h</p>
                            </div>
                        `
                    });

                    marker.addListener('click', () => {
                        // Zoom to aerator location
                        map.setCenter(marker.getPosition());
                        map.setZoom(16); // Zoom level for detailed view

                        // Open info window
                        infoWindow.open(map, marker);
                    });

                    markers.push(marker);
                    bounds.extend(new google.maps.LatLng(aerator.latitude, aerator.longitude));
                    shouldFitBounds = true;
                }
            });

            // Add geofences
            mapData.geofences.forEach((geofence) => {
                if (geofence.boundary && geofence.boundary.type === 'circle' && geofence.boundary.center) {
                    const circle = new google.maps.Circle({
                        strokeColor: getGeofenceColor(geofence.geofence_type),
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillColor: getGeofenceColor(geofence.geofence_type),
                        fillOpacity: 0.15,
                        map: map,
                        center: {
                            lat: geofence.boundary.center.lat,
                            lng: geofence.boundary.center.lng
                        },
                        radius: geofence.boundary.radius || 100
                    });

                    // Add info window for geofence
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4>🛡️ ${geofence.name || 'Geofence'}</h4>
                                <p><strong>Type:</strong> ${geofence.geofence_type || 'Unknown'}</p>
                                <p><strong>Description:</strong> ${geofence.description || 'No description'}</p>
                                <p><strong>Radius:</strong> ${geofence.boundary.radius || 0}m</p>
                                <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                            </div>
                        `
                    });

                    circle.addListener('click', (event) => {
                        infoWindow.setPosition(event.latLng);
                        infoWindow.open(map);
                    });
                }
            });

            console.log(`✅ Added ${markers.length} markers to the map`);

            // Auto-fit map to show all locations
            if (shouldFitBounds && bounds && !bounds.isEmpty()) {
                console.log('🔍 Auto-fitting map bounds to show all locations...');
                map.fitBounds(bounds);

                // Add some padding to the bounds
                setTimeout(() => {
                    const currentZoom = map.getZoom();
                    if (currentZoom > 15) {
                        map.setZoom(15); // Don't zoom in too much
                    } else if (currentZoom < 10) {
                        map.setZoom(10); // Don't zoom out too much
                    }
                    console.log(`🎯 Final map zoom level: ${map.getZoom()}`);
                }, 100);
            }
            
        } catch (error) {
            console.error('❌ Error creating map:', error);
            handleMapError();
        }
    }

    // actualInitMap is now called by the global window.initMap function defined at the top

    // Step 5: Define error handler
    function handleMapError() {
        console.error('❌ Failed to load Google Maps');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.classList.remove('loading');
            mapElement.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #dc3545; background: #f8f9fa; border-radius: 10px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px; color: #dc3545;"></i>
                    <h4 style="color: #dc3545; margin-bottom: 15px;">Failed to Load Google Maps</h4>
                    <p style="color: #6c757d; margin-bottom: 20px;">
                        There was an error loading the map. This could be due to:
                    </p>
                    <ul style="text-align: left; color: #6c757d; margin-bottom: 20px; display: inline-block;">
                        <li>Internet connection issues</li>
                        <li>Google Maps API key restrictions</li>
                        <li>API quota exceeded</li>
                        <li>Browser blocking the request</li>
                    </ul>
                    <div>
                        <button onclick="location.reload()" class="btn btn-primary me-2">
                            <i class="fas fa-refresh"></i> Retry
                        </button>
                        <button onclick="console.log('API Key:', 'Hidden for security'); alert('Check browser console for API key details')" class="btn btn-secondary me-2">
                            <i class="fas fa-info-circle"></i> Debug Info
                        </button>
                        <button onclick="loadFallbackMap()" class="btn btn-success">
                            <i class="fas fa-map"></i> Use Fallback Map
                        </button>
                    </div>
                </div>
            `;
        }
    }
    
    // Enhanced Pond Card Functions
    function showEnhancedPondCard(pond, marker) {
        console.log('🎯 Showing enhanced card for pond:', pond.name);

        const cardElement = document.getElementById('pond-info-card');
        if (!cardElement) return;

        // Generate weather data (simulated for demo)
        const weatherData = generateWeatherData(pond.latitude, pond.longitude);

        // Create enhanced card content
        cardElement.innerHTML = createPondCardContent(pond, weatherData);

        // Show card with animation
        cardElement.style.display = 'block';
        cardElement.classList.add('dark'); // Use dark theme for better visibility

        // Add close functionality
        const closeBtn = cardElement.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.onclick = () => hidePondCard();
        }

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (cardElement.style.display !== 'none') {
                hidePondCard();
            }
        }, 10000);
    }

    function createPondCardContent(pond, weather) {
        return `
            <div class="card-header-section">
                <div class="pond-logo">🦐</div>
                <div class="pond-title-section">
                    <h3 class="pond-name">${pond.name || 'Unnamed Pond'}</h3>
                    <p class="pond-id">ID: ${pond.id || 'N/A'}</p>
                </div>
                <button class="close-btn" title="Close">&times;</button>
            </div>

            <div class="pond-details-section">
                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-building"></i> Farm:
                    </span>
                    <span class="detail-value">${pond.farm_name || 'Unknown'}</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-heartbeat"></i> Status:
                    </span>
                    <span class="detail-value">
                        <span class="status-badge ${pond.status === 'active' ? 'status-active' : 'status-inactive'}">
                            ${pond.status || 'Unknown'}
                        </span>
                    </span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-expand-arrows-alt"></i> Size:
                    </span>
                    <span class="detail-value">${pond.size || 'N/A'} m²</span>
                </div>

                <div class="detail-row">
                    <span class="detail-label">
                        <i class="fas fa-fish"></i> Species:
                    </span>
                    <span class="detail-value">${pond.species || 'White Shrimp (L. vannamei)'}</span>
                </div>
            </div>

            <div class="weather-section">
                <div class="weather-header">
                    <i class="fas fa-cloud-sun weather-icon"></i>
                    <h4 class="weather-title">Weather Conditions</h4>
                </div>

                <div class="weather-grid">
                    <div class="weather-item temperature">
                        <i class="fas fa-thermometer-half weather-item-icon"></i>
                        <div class="weather-item-value">${weather.temperature}°C</div>
                        <div class="weather-item-label">Temp</div>
                    </div>

                    <div class="weather-item humidity">
                        <i class="fas fa-tint weather-item-icon"></i>
                        <div class="weather-item-value">${weather.humidity}%</div>
                        <div class="weather-item-label">Humidity</div>
                    </div>

                    <div class="weather-item wind">
                        <i class="fas fa-wind weather-item-icon"></i>
                        <div class="weather-item-value">${weather.windSpeed}</div>
                        <div class="weather-item-label">Wind m/s</div>
                    </div>

                    <div class="weather-item pressure">
                        <i class="fas fa-tachometer-alt weather-item-icon"></i>
                        <div class="weather-item-value">${weather.pressure}</div>
                        <div class="weather-item-label">Pressure</div>
                    </div>

                    <div class="weather-item visibility">
                        <i class="fas fa-eye weather-item-icon"></i>
                        <div class="weather-item-value">${weather.visibility}km</div>
                        <div class="weather-item-label">Visibility</div>
                    </div>

                    <div class="weather-item uv">
                        <i class="fas fa-sun weather-item-icon"></i>
                        <div class="weather-item-value">UV${weather.uvIndex}</div>
                        <div class="weather-item-label">UV Index</div>
                    </div>
                </div>
            </div>
        `;
    }

    function generateWeatherData(lat, lng) {
        // Simulate weather data based on location
        return {
            temperature: Math.round(28 + Math.random() * 8), // 28-36°C
            humidity: Math.round(65 + Math.random() * 20), // 65-85%
            windSpeed: (Math.random() * 15 + 5).toFixed(1), // 5-20 m/s
            pressure: Math.round(1000 + Math.random() * 20), // 1000-1020 hPa
            visibility: Math.round(5 + Math.random() * 10), // 5-15 km
            uvIndex: Math.round(3 + Math.random() * 8) // 3-11
        };
    }

    function hidePondCard() {
        const cardElement = document.getElementById('pond-info-card');
        if (cardElement) {
            cardElement.style.display = 'none';
        }
    }

    // Map Control Functions
    function toggleMapType() {
        if (!map) return;

        const currentType = map.getMapTypeId();
        const newType = currentType === 'roadmap' ? 'satellite' : 'roadmap';
        map.setMapTypeId(newType);

        console.log(`🗺️ Map type changed to: ${newType}`);
    }

    function centerOnPonds() {
        if (!map || mapData.ponds.length === 0) return;

        const bounds = new google.maps.LatLngBounds();
        mapData.ponds.forEach(pond => {
            if (pond.latitude && pond.longitude) {
                bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
            }
        });

        if (!bounds.isEmpty()) {
            map.fitBounds(bounds);
            setTimeout(() => {
                const currentZoom = map.getZoom();
                if (currentZoom > 15) map.setZoom(15);
                if (currentZoom < 10) map.setZoom(10);
            }, 100);
        }

        console.log('🎯 Map centered on all ponds');
    }

    function refreshMapData() {
        console.log('🔄 Refreshing map data...');

        // Show loading indicator
        const refreshBtn = document.querySelector('.map-control-btn:last-child i');
        if (refreshBtn) {
            refreshBtn.classList.add('fa-spin');
        }

        // Simulate data refresh (in real implementation, this would fetch new data)
        setTimeout(() => {
            // Update counts
            document.getElementById('farms-count').textContent = mapData.farms.length;
            document.getElementById('ponds-count').textContent = mapData.ponds.length;
            document.getElementById('workers-count').textContent = mapData.workers.length;
            document.getElementById('aerators-count').textContent = mapData.aerators.length;
            document.getElementById('geofences-count').textContent = mapData.geofences.length;

            // Update active counts
            const activeWorkers = mapData.workers.filter(w => ['working', 'active'].includes(w.status)).length;
            const activeAerators = mapData.aerators.filter(a => a.status === 'active').length;
            document.getElementById('active-workers').textContent = activeWorkers;
            document.getElementById('active-aerators').textContent = activeAerators;

            // Remove loading indicator
            if (refreshBtn) {
                refreshBtn.classList.remove('fa-spin');
            }

            console.log('✅ Map data refreshed');
        }, 1000);
    }

    // Helper functions for status colors
    function getWorkerStatusColor(status) {
        switch(status) {
            case 'working':
            case 'active': return '#28a745'; // Green
            case 'break': return '#ffc107'; // Yellow
            case 'offline':
            case 'inactive': return '#dc3545'; // Red
            default: return '#6c757d'; // Gray
        }
    }

    function getAeratorStatusColor(status) {
        switch(status) {
            case 'active':
            case 'running': return '#007bff'; // Blue
            case 'maintenance': return '#ffc107'; // Yellow
            case 'inactive':
            case 'off': return '#dc3545'; // Red
            default: return '#6c757d'; // Gray
        }
    }

    function getGeofenceColor(type) {
        switch(type) {
            case 'restricted': return '#dc3545'; // Red
            case 'safe': return '#28a745'; // Green
            case 'warning': return '#ffc107'; // Yellow
            case 'work_area': return '#007bff'; // Blue
            default: return '#6f42c1'; // Purple
        }
    }

    // Dashboard Functions
    function refreshDashboard() {
        console.log('🔄 Refreshing dashboard...');

        // Show loading state
        const refreshBtn = document.querySelector('.refresh-btn');
        if (refreshBtn) {
            const icon = refreshBtn.querySelector('i');
            if (icon) {
                icon.classList.add('fa-spin');
                console.log('✅ Refresh button animation started');
            }
        } else {
            console.log('❌ Refresh button not found');
        }

        // Simulate refresh (in real implementation, this would reload data)
        setTimeout(() => {
            // Update statistics
            refreshMapData();

            // Update weather data (simulated)
            updateWeatherWidget();

            // Remove loading state
            if (refreshBtn) {
                const icon = refreshBtn.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-spin');
                    console.log('✅ Refresh button animation stopped');
                }
            }

            console.log('✅ Dashboard refreshed successfully');
        }, 1500);
    }

    function exportReport() {
        console.log('📊 Exporting dashboard report...');

        // Create report data
        const reportData = {
            timestamp: new Date().toISOString(),
            farms: mapData.farms.length,
            ponds: mapData.ponds.length,
            workers: mapData.workers.length,
            aerators: mapData.aerators.length,
            geofences: mapData.geofences.length,
            activeWorkers: mapData.workers.filter(w => ['working', 'active'].includes(w.status)).length,
            activeAerators: mapData.aerators.filter(a => a.status === 'active').length
        };

        // Create and download CSV
        const csvContent = "data:text/csv;charset=utf-8,"
            + "Metric,Value\n"
            + `Export Date,${new Date().toLocaleDateString()}\n`
            + `Total Farms,${reportData.farms}\n`
            + `Total Ponds,${reportData.ponds}\n`
            + `Total Workers,${reportData.workers}\n`
            + `Active Workers,${reportData.activeWorkers}\n`
            + `Total Aerators,${reportData.aerators}\n`
            + `Active Aerators,${reportData.activeAerators}\n`
            + `Geofences,${reportData.geofences}`;

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", `shrimp_farm_report_${new Date().toISOString().split('T')[0]}.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('✅ Report exported');
    }

    function viewAllActivities() {
        console.log('📋 Viewing all activities...');

        const activities = generateRecentActivities();
        const activitiesHtml = activities.map(activity => `
            <div class="activity-item activity-${activity.type}" style="margin-bottom: 10px;">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                    ${activity.description ? `<div class="activity-description">${activity.description}</div>` : ''}
                </div>
            </div>
        `).join('');

        // Create modal-like display for all activities
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 600px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 20px 40px rgba(0,0,0,0.3);
                position: relative;
            ">
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #f1f3f4;
                    padding-bottom: 15px;
                ">
                    <h2 style="
                        margin: 0;
                        color: #2d3436;
                        font-size: 1.5rem;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    ">
                        <i class="fas fa-clock"></i>
                        All Recent Activities
                    </h2>
                    <button onclick="this.closest('div').parentElement.remove()" style="
                        background: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 50%;
                        width: 35px;
                        height: 35px;
                        cursor: pointer;
                        font-size: 1.2rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">×</button>
                </div>
                <div style="display: flex; flex-direction: column; gap: 15px;">
                    ${activitiesHtml}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    function updateWeatherWidget() {
        // Simulate weather update
        const tempElement = document.querySelector('.weather-temp');
        const descElement = document.querySelector('.weather-desc');
        const humidityElement = document.querySelector('.weather-details .weather-detail:first-child div:last-child');
        const windElement = document.querySelector('.weather-details .weather-detail:last-child div:last-child');

        if (tempElement) {
            const newTemp = Math.round(25 + Math.random() * 10);
            tempElement.textContent = `${newTemp}°C`;
        }

        if (humidityElement) {
            const newHumidity = Math.round(60 + Math.random() * 30);
            humidityElement.textContent = `${newHumidity}%`;
        }

        if (windElement) {
            const newWind = Math.round(5 + Math.random() * 15);
            windElement.textContent = `${newWind} km/h`;
        }

        console.log('🌤️ Weather widget updated');
    }

    // Layer control functions
    function toggleLayer(layerType) {
        const btn = document.getElementById(layerType + '-layer');
        btn.classList.toggle('active');

        // Toggle visibility based on layer type
        switch(layerType) {
            case 'farms':
                // Toggle farm markers visibility
                console.log('🏭 Toggling farms layer');
                break;
            case 'ponds':
                // Toggle pond markers visibility
                console.log('🌊 Toggling ponds layer');
                break;
            case 'workers':
                // Toggle worker markers visibility
                console.log('👷 Toggling workers layer');
                break;
            case 'aerators':
                // Toggle aerator markers visibility
                console.log('⚡ Toggling aerators layer');
                break;
            case 'geofences':
                // Toggle geofence visibility
                console.log('🛡️ Toggling geofences layer');
                break;
        }
    }

    function fitAllMarkers() {
        if (map && markers.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            markers.forEach(marker => {
                bounds.extend(marker.getPosition());
            });

            if (!bounds.isEmpty()) {
                map.fitBounds(bounds);
                setTimeout(() => {
                    const currentZoom = map.getZoom();
                    if (currentZoom > 15) map.setZoom(15);
                    if (currentZoom < 8) map.setZoom(8);
                }, 100);
            }
            console.log('🎯 Fitted all markers in view');
        }
    }

    function fullscreenMap() {
        const mapContainer = document.getElementById('map');
        if (mapContainer.requestFullscreen) {
            mapContainer.requestFullscreen();
        } else if (mapContainer.webkitRequestFullscreen) {
            mapContainer.webkitRequestFullscreen();
        } else if (mapContainer.msRequestFullscreen) {
            mapContainer.msRequestFullscreen();
        }
        console.log('🖥️ Entering fullscreen mode');
    }

    // Recent Activities Functions
    function generateRecentActivities() {
        const activities = [
            {
                id: 1,
                type: 'pond-creation',
                icon: 'fas fa-plus',
                title: 'New pond created in Farm A',
                description: 'Pond A-07 has been successfully created with 2,500 m² area',
                time: '2 minutes ago',
                timestamp: new Date(Date.now() - 2 * 60 * 1000)
            },
            {
                id: 2,
                type: 'water-alert',
                icon: 'fas fa-thermometer-half',
                title: 'Water temperature alert in Pond 3',
                description: 'Temperature reached 32°C, exceeding optimal range',
                time: '15 minutes ago',
                timestamp: new Date(Date.now() - 15 * 60 * 1000)
            },
            {
                id: 3,
                type: 'worker-assignment',
                icon: 'fas fa-user-plus',
                title: 'New worker assigned to Farm B',
                description: 'Maria Santos assigned to morning shift monitoring',
                time: '1 hour ago',
                timestamp: new Date(Date.now() - 60 * 60 * 1000)
            },
            {
                id: 4,
                type: 'aerator-status',
                icon: 'fas fa-fan',
                title: 'Aerator maintenance completed',
                description: 'Aerator AE-05 in Pond B-03 back online after maintenance',
                time: '2 hours ago',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                id: 5,
                type: 'weather-update',
                icon: 'fas fa-cloud-sun',
                title: 'Weather conditions updated',
                description: 'Partly cloudy, 28°C, humidity 75%, wind 12 km/h',
                time: '3 hours ago',
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000)
            },
            {
                id: 6,
                type: 'feeding',
                icon: 'fas fa-fish',
                title: 'Feeding schedule completed',
                description: 'Morning feeding completed for Ponds A-01 to A-05',
                time: '4 hours ago',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                id: 7,
                type: 'farm-creation',
                icon: 'fas fa-warehouse',
                title: 'New farm registered',
                description: 'Farm Delta registered with 8 ponds and 15 hectares',
                time: '6 hours ago',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)
            },
            {
                id: 8,
                type: 'water-alert',
                icon: 'fas fa-tint',
                title: 'Water quality check completed',
                description: 'pH levels normal, dissolved oxygen at optimal levels',
                time: '8 hours ago',
                timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000)
            },
            {
                id: 9,
                type: 'maintenance',
                icon: 'fas fa-tools',
                title: 'Equipment maintenance scheduled',
                description: 'Weekly maintenance for pumps and filters in Farm C',
                time: '12 hours ago',
                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000)
            },
            {
                id: 10,
                type: 'harvest',
                icon: 'fas fa-shopping-basket',
                title: 'Harvest completed',
                description: '2.5 tons of shrimp harvested from Pond B-02',
                time: '1 day ago',
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
        ];

        return activities.sort((a, b) => b.timestamp - a.timestamp);
    }

    function renderRecentActivities() {
        const activitiesList = document.getElementById('activities-list');
        if (!activitiesList) return;

        const activities = generateRecentActivities();
        const recentActivities = activities.slice(0, 5); // Show only 5 most recent

        activitiesList.innerHTML = recentActivities.map(activity => `
            <div class="activity-item activity-${activity.type}" onclick="viewActivityDetails('${activity.id}')">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${activity.time}</div>
                    ${activity.description ? `<div class="activity-description">${activity.description}</div>` : ''}
                </div>
            </div>
        `).join('');

        console.log('✅ Recent activities rendered');
    }

    function viewActivityDetails(activityId) {
        console.log(`📋 Viewing details for activity ${activityId}`);
        // In a real implementation, this would show detailed activity information
        const activities = generateRecentActivities();
        const activity = activities.find(a => a.id == activityId);

        if (activity) {
            alert(`Activity Details:\n\n${activity.title}\n${activity.description}\nTime: ${activity.time}`);
        }
    }

    function addNewActivity(type, title, description) {
        console.log(`➕ Adding new activity: ${title}`);

        // In a real implementation, this would save to database
        // For demo, we'll just refresh the activities list
        setTimeout(() => {
            renderRecentActivities();
        }, 500);
    }

    // Simulate real-time activity updates
    function simulateActivityUpdates() {
        const activityTypes = [
            {
                type: 'water-alert',
                icon: 'fas fa-thermometer-half',
                titles: [
                    'Water temperature alert in Pond {pond}',
                    'pH level warning in Pond {pond}',
                    'Dissolved oxygen low in Pond {pond}'
                ],
                descriptions: [
                    'Temperature reached {temp}°C, monitoring required',
                    'pH level at {ph}, adjusting chemical balance',
                    'Oxygen level at {oxygen}ppm, activating aerators'
                ]
            },
            {
                type: 'aerator-status',
                icon: 'fas fa-fan',
                titles: [
                    'Aerator activated in Pond {pond}',
                    'Aerator maintenance required',
                    'Aerator performance optimized'
                ],
                descriptions: [
                    'Automatic activation due to low oxygen levels',
                    'Scheduled maintenance for optimal performance',
                    'Energy efficiency improved by 15%'
                ]
            },
            {
                type: 'feeding',
                icon: 'fas fa-fish',
                titles: [
                    'Feeding completed in Pond {pond}',
                    'Feeding schedule updated',
                    'Feed quality check passed'
                ],
                descriptions: [
                    '{amount}kg of premium feed distributed',
                    'Adjusted for optimal growth conditions',
                    'Nutritional content verified and approved'
                ]
            }
        ];

        // Add a new simulated activity every 30 seconds
        setInterval(() => {
            const randomType = activityTypes[Math.floor(Math.random() * activityTypes.length)];
            const randomTitle = randomType.titles[Math.floor(Math.random() * randomType.titles.length)];
            const randomDesc = randomType.descriptions[Math.floor(Math.random() * randomType.descriptions.length)];

            // Replace placeholders with random values
            const pondNumber = Math.floor(Math.random() * 10) + 1;
            const temp = Math.floor(Math.random() * 8) + 28;
            const ph = (Math.random() * 2 + 7).toFixed(1);
            const oxygen = (Math.random() * 3 + 5).toFixed(1);
            const amount = Math.floor(Math.random() * 50) + 20;

            const title = randomTitle
                .replace('{pond}', `A-${pondNumber.toString().padStart(2, '0')}`)
                .replace('{temp}', temp)
                .replace('{ph}', ph)
                .replace('{oxygen}', oxygen)
                .replace('{amount}', amount);

            const description = randomDesc
                .replace('{pond}', `A-${pondNumber.toString().padStart(2, '0')}`)
                .replace('{temp}', temp)
                .replace('{ph}', ph)
                .replace('{oxygen}', oxygen)
                .replace('{amount}', amount);

            addNewActivity(randomType.type, title, description);
        }, 30000); // Every 30 seconds
    }

    // Initialize activities when page loads
    document.addEventListener('DOMContentLoaded', function() {
        renderRecentActivities();
        simulateActivityUpdates();
        console.log('✅ Recent Activities system initialized');
    });

    // Step 6: Make functions globally accessible
    // window.initMap is already defined at the top of the script
    window.handleMapError = handleMapError;
    window.showEnhancedPondCard = showEnhancedPondCard;
    window.hidePondCard = hidePondCard;
    window.toggleMapType = toggleMapType;
    window.centerOnPonds = centerOnPonds;
    window.refreshMapData = refreshMapData;
    window.refreshDashboard = refreshDashboard;
    window.exportReport = exportReport;
    window.viewAllActivities = viewAllActivities;
    window.updateWeatherWidget = updateWeatherWidget;
    window.toggleLayer = toggleLayer;
    window.fitAllMarkers = fitAllMarkers;
    window.fullscreenMap = fullscreenMap;
    window.renderRecentActivities = renderRecentActivities;
    window.viewActivityDetails = viewActivityDetails;
    window.addNewActivity = addNewActivity;
    window.toggleDropdown = toggleDropdown;

    console.log('✅ Unified Map Dashboard with Enhanced Cards setup complete');

    // Test function availability
    console.log('🔍 Testing function availability:');
    console.log('- refreshDashboard:', typeof window.refreshDashboard);
    console.log('- exportReport:', typeof window.exportReport);
    console.log('- toggleMapType:', typeof window.toggleMapType);
    console.log('- centerOnPonds:', typeof window.centerOnPonds);
    console.log('- refreshMapData:', typeof window.refreshMapData);

    // Initialize navigation and activities on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 DOM loaded, initializing navigation and activities...');

        // Initialize navigation dropdowns
        initializeNavigation();

        // Initialize activities
        renderRecentActivities();

        // Test button clicks
        const refreshBtn = document.querySelector('.refresh-btn');
        const exportBtn = document.querySelector('.export-btn');

        if (refreshBtn) {
            console.log('✅ Refresh button found');
        } else {
            console.log('❌ Refresh button not found');
        }

        if (exportBtn) {
            console.log('✅ Export button found');
        } else {
            console.log('❌ Export button not found');
        }
    });

    // Toggle dropdown function for click navigation
    function toggleDropdown(dropdownId) {
        console.log('🔽 Toggling dropdown:', dropdownId);

        // Close all other dropdowns first
        document.querySelectorAll('.nav-dropdown').forEach(dropdown => {
            if (dropdown.id !== dropdownId) {
                dropdown.classList.remove('active');
            }
        });

        // Toggle the clicked dropdown
        const dropdown = document.getElementById(dropdownId);
        if (dropdown) {
            dropdown.classList.toggle('active');
            console.log('✅ Dropdown toggled:', dropdownId, dropdown.classList.contains('active'));
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.nav-dropdown')) {
            document.querySelectorAll('.nav-dropdown').forEach(dropdown => {
                dropdown.classList.remove('active');
            });
        }
    });

    // Navigation functionality
    function initializeNavigation() {
        console.log('🧭 Initializing navigation...');

        // Handle dropdown toggles
        const dropdownItems = document.querySelectorAll('.nav-dropdown');

        dropdownItems.forEach(dropdown => {
            const trigger = dropdown.querySelector('.nav-link');
            const content = dropdown.querySelector('.nav-dropdown-content');

            if (trigger && content) {
                // Show dropdown on hover
                dropdown.addEventListener('mouseenter', () => {
                    content.style.opacity = '1';
                    content.style.visibility = 'visible';
                    content.style.transform = 'translateY(0)';
                });

                // Hide dropdown when mouse leaves
                dropdown.addEventListener('mouseleave', () => {
                    content.style.opacity = '0';
                    content.style.visibility = 'hidden';
                    content.style.transform = 'translateY(-10px)';
                });

                // Handle click events for dropdown items
                const dropdownLinks = content.querySelectorAll('.nav-dropdown-item');
                dropdownLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        console.log('🔗 Navigation link clicked:', link.textContent.trim());
                        // Let the default link behavior handle navigation
                    });
                });
            }
        });

        // Handle direct navigation links (non-dropdown)
        const directLinks = document.querySelectorAll('.nav-link:not(.nav-dropdown .nav-link)');
        directLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                console.log('🔗 Direct navigation link clicked:', link.textContent.trim());
                // Let the default link behavior handle navigation
            });
        });

        // Add click handlers for main navigation items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            const link = item.querySelector('.nav-link');
            if (link && !item.classList.contains('nav-dropdown')) {
                link.addEventListener('click', (e) => {
                    // Remove active class from all nav links
                    document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                    // Add active class to clicked link
                    link.classList.add('active');
                });
            }
        });

        console.log('✅ Navigation initialized successfully');

        // Test dropdown functionality
        const dropdownCount = document.querySelectorAll('.nav-dropdown').length;
        const linkCount = document.querySelectorAll('.nav-dropdown-item').length;
        console.log(`📊 Navigation stats: ${dropdownCount} dropdowns, ${linkCount} dropdown links`);

        // Fallback map initialization if Google Maps doesn't load in 10 seconds
        setTimeout(function() {
            const mapElement = document.getElementById('map');
            if (mapElement && mapElement.classList.contains('loading')) {
                console.log('⚠️ Google Maps taking too long, showing fallback...');
                mapElement.innerHTML = `
                    <div style="padding: 40px; text-align: center; background: #f8f9fa; border-radius: 10px;">
                        <i class="fas fa-map" style="font-size: 3rem; color: #6c757d; margin-bottom: 20px;"></i>
                        <h4 style="color: #495057;">Map Loading...</h4>
                        <p style="color: #6c757d;">The interactive map is loading. This may take a moment.</p>
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                `;
            }
        }, 10000);
    }

    // initMap function is already defined above and assigned to window.initMap
</script>

<!-- Google Maps API Loading -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry&loading=async"
    onerror="handleMapError()">
</script>

<script>
    // Global error handler for Google Maps API authentication failures
    window.gm_authFailure = function() {
        console.error('❌ Google Maps API authentication failed');
        document.getElementById('map').innerHTML = `
            <div style="padding: 40px; text-align: center; color: #dc3545; background: #f8f9fa; border-radius: 10px;">
                <i class="fas fa-key" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <h4>Google Maps Authentication Failed</h4>
                <p>API key is invalid or has restrictions. Please check your Google Maps API configuration.</p>
                <small>API Key: [Hidden for security]</small>
                <br><br>
                <button onclick="location.reload()" class="btn btn-danger">
                    <i class="fas fa-refresh"></i> Retry
                </button>
            </div>
        `;
    };

    // Log API key for debugging
    console.log('🔑 Google Maps API Key loaded:', typeof '{{ google_maps_api_key }}' !== 'undefined');

    // Fallback initialization in case callback doesn't work
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔄 DOM loaded, checking if Google Maps is available...');

        // Wait a bit for Google Maps to load
        setTimeout(function() {
            if (typeof google !== 'undefined' && google.maps && !window.mapInitialized) {
                console.log('🔄 Fallback: Initializing Google Maps...');
                if (typeof window.initMap === 'function') {
                    window.initMap();
                } else {
                    console.error('❌ initMap function not found');
                }
            } else if (typeof google === 'undefined') {
                console.log('⚠️ Google Maps still not loaded after timeout');
            }
        }, 3000);
    });
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
