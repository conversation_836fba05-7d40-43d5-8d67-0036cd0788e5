<!DOCTYPE html>
<html>
<head>
    <title>Large Map Size Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        #map {
            width: 100% !important;
            height: 1200px !important;
            min-height: 1200px !important;
            max-height: none !important;
            border: 2px solid #ddd;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .dimensions {
            font-weight: bold;
            color: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Large Map Size Test - 1200px Height</h1>
        
        <div id="map">
            <div>
                <i>Map container with 1200px height</i><br>
                <small>This represents the increased map size</small>
            </div>
        </div>
        
        <div class="info">
            <h3>Map Container Information:</h3>
            <p><span class="dimensions">Height:</span> <span id="height-display">Calculating...</span></p>
            <p><span class="dimensions">Width:</span> <span id="width-display">Calculating...</span></p>
            <p><span class="dimensions">CSS Height:</span> <span id="css-height">Calculating...</span></p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mapElement = document.getElementById('map');
            
            // Force dimensions programmatically as well
            mapElement.style.height = '1200px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '1200px';
            
            // Display dimensions
            function updateDimensions() {
                const computedStyle = window.getComputedStyle(mapElement);
                document.getElementById('height-display').textContent = mapElement.offsetHeight + 'px';
                document.getElementById('width-display').textContent = mapElement.offsetWidth + 'px';
                document.getElementById('css-height').textContent = computedStyle.height;
            }
            
            updateDimensions();
            
            // Update on window resize
            window.addEventListener('resize', updateDimensions);
            
            console.log('Map container dimensions:');
            console.log('Height:', mapElement.offsetHeight + 'px');
            console.log('Width:', mapElement.offsetWidth + 'px');
            console.log('CSS Height:', window.getComputedStyle(mapElement).height);
        });
    </script>
</body>
</html>
