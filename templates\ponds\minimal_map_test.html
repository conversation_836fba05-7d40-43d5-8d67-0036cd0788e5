{% extends "base.html" %}
{% load static %}

{% block title %}Minimal Map Test{% endblock %}

{% block extra_css %}
<style>
    .map-container {
        height: 600px;
        width: 100%;
        border: 2px solid #ddd;
        border-radius: 8px;
        margin: 20px 0;
    }
    .debug-panel {
        background: white;
        padding: 15px;
        border-radius: 8px;
        margin: 10px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        font-family: monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
    }
    .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
    }
    .success { background: #d4edda; color: #155724; }
    .error { background: #f8d7da; color: #721c24; }
    .info { background: #d1ecf1; color: #0c5460; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>🗺️ Minimal Google Maps Test</h1>
    
    <div id="status" class="status info">⏳ Initializing Google Maps...</div>
    
    <div class="debug-panel">
        <h4>Debug Information:</h4>
        <div><strong>API Key:</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}</div>
        <div><strong>Center:</strong> {{ center_lat }}, {{ center_lng }}</div>
        <div><strong>Farms:</strong> {{ farms_data|length|default:"0" }}</div>
        <div><strong>Ponds:</strong> {{ ponds_data|length|default:"0" }}</div>
        <hr>
        <div id="debug-log">Starting initialization...<br></div>
    </div>
    
    <div id="map" class="map-container"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Simple logging function
    function log(message, type = 'info') {
        console.log(message);
        const debugLog = document.getElementById('debug-log');
        const timestamp = new Date().toLocaleTimeString();
        const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
        debugLog.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
        debugLog.scrollTop = debugLog.scrollHeight;
    }

    function updateStatus(message, type) {
        const statusEl = document.getElementById('status');
        statusEl.textContent = message;
        statusEl.className = `status ${type}`;
    }

    // Test data from Django
    const API_KEY = '{{ google_maps_api_key|escapejs }}';
    const centerLat = {{ center_lat|default:13.0827 }};
    const centerLng = {{ center_lng|default:80.2707 }};
    
    log(`🔑 API Key loaded: ${API_KEY.substring(0, 10)}...`);
    log(`📍 Center coordinates: ${centerLat}, ${centerLng}`);

    // Global error handler for authentication
    window.gm_authFailure = function() {
        log('❌ Google Maps authentication failed!', 'error');
        updateStatus('❌ Authentication Failed', 'error');
        document.getElementById('map').innerHTML = 
            '<div style="padding: 20px; text-align: center; color: red; background: #fee;">Google Maps authentication failed. Check API key and restrictions.</div>';
    };

    // Main initialization function
    function initMap() {
        log('🎯 initMap() function called by Google Maps API', 'success');
        updateStatus('🗺️ Google Maps API loaded', 'success');
        
        // Verify Google Maps is available
        if (typeof google === 'undefined') {
            log('❌ Google object is undefined', 'error');
            updateStatus('❌ Google Maps not available', 'error');
            return;
        }
        
        if (!google.maps) {
            log('❌ google.maps is undefined', 'error');
            updateStatus('❌ Google Maps library not available', 'error');
            return;
        }
        
        log('✅ Google Maps API is available', 'success');
        log(`📦 Google Maps version: ${google.maps.version}`);
        
        // Get map container
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            log('❌ Map container element not found', 'error');
            updateStatus('❌ Map container missing', 'error');
            return;
        }
        
        log('✅ Map container element found', 'success');
        
        try {
            // Create the map
            const map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                // Add some basic controls
                zoomControl: true,
                mapTypeControl: true,
                scaleControl: true,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: true
            });
            
            log('✅ Map object created successfully', 'success');
            updateStatus('✅ Map initialized successfully!', 'success');
            
            // Add a simple test marker
            const marker = new google.maps.Marker({
                position: { lat: centerLat, lng: centerLng },
                map: map,
                title: 'Test Location',
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 8,
                    fillColor: '#2563eb',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2
                }
            });
            
            log('✅ Test marker added successfully', 'success');
            
            // Add click listener to verify interactivity
            map.addListener('click', function(event) {
                log(`📍 Map clicked at: ${event.latLng.lat().toFixed(6)}, ${event.latLng.lng().toFixed(6)}`, 'info');
                
                new google.maps.Marker({
                    position: event.latLng,
                    map: map,
                    title: 'Clicked location',
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 6,
                        fillColor: '#10b981',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    }
                });
            });
            
            log('✅ Map is fully interactive and working!', 'success');
            
        } catch (error) {
            log(`❌ Error creating map: ${error.message}`, 'error');
            updateStatus('❌ Map creation failed', 'error');
            console.error('Full error:', error);
            
            // Show detailed error in map container
            mapElement.innerHTML = `
                <div style="padding: 20px; text-align: center; color: red; background: #fee;">
                    <h4>Map Creation Error</h4>
                    <p>${error.message}</p>
                    <small>Check the browser console for more details</small>
                </div>
            `;
        }
    }

    // Initialize logging
    log('🚀 Starting minimal Google Maps test...');
    log('📡 Waiting for Google Maps API to load...');
    
    // Error handler for script loading failures
    function handleScriptError() {
        log('❌ Failed to load Google Maps script', 'error');
        updateStatus('❌ Script loading failed', 'error');
        document.getElementById('map').innerHTML = 
            '<div style="padding: 20px; text-align: center; color: red; background: #fee;">Failed to load Google Maps script. Check network connection and API key.</div>';
    }
    
    // Timeout check
    setTimeout(function() {
        if (typeof google === 'undefined') {
            log('⏰ Timeout: Google Maps API did not load within 10 seconds', 'error');
            updateStatus('⏰ Loading timeout', 'error');
        }
    }, 10000);
</script>

<!-- Google Maps JavaScript API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleScriptError()">
</script>
{% endblock %}
