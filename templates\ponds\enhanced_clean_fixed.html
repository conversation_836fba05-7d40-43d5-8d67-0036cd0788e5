{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Cumulative Map - GPS & Advanced Features{% endblock %}

{% block extra_css %}
<style>
    /* Modern Dark Theme for Enhanced Map */
    body {
        background: linear-gradient(135deg, #1e3a8a 0%, #1f2937 50%, #0f172a 100%);
        color: #e2e8f0;
        font-family: 'Segoe UI', system-ui, sans-serif;
    }
    
    .container-fluid {
        padding: 0;
        max-width: 100%;
    }
    
    #map {
        height: 70vh;
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.1);
    }
    
    .map-container {
        margin: 20px 0;
        padding: 20px;
        background: rgba(30, 41, 59, 0.9);
        border-radius: 16px;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        position: relative;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 15px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .stat-label {
        font-size: 1rem;
        opacity: 0.9;
        font-weight: 500;
    }
    
    .controls {
        margin: 20px 0;
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .btn {
        padding: 12px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
    }
    
    .btn-primary { background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; }
    .btn-success { background: linear-gradient(135deg, #10b981, #059669); color: white; }
    .btn-info { background: linear-gradient(135deg, #06b6d4, #0891b2); color: white; }
    .btn-warning { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
    .btn-danger { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
    
    h1 {
        color: #e2e8f0;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        margin-bottom: 2rem;
        font-weight: 700;
        font-size: 2.5rem;
    }
    
    .advanced-controls {
        background: rgba(30, 41, 59, 0.95);
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
    }
    
    .control-section {
        margin-bottom: 20px;
    }
    
    .control-section h6 {
        color: #94a3b8;
        font-size: 1rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 12px;
        border-bottom: 2px solid rgba(74, 85, 104, 0.3);
        padding-bottom: 8px;
    }
    
    .search-container {
        position: relative;
        margin: 15px 0;
    }
    
    .search-input {
        width: 100%;
        padding: 15px 50px 15px 20px;
        border: 2px solid rgba(74, 85, 104, 0.3);
        border-radius: 12px;
        background: rgba(30, 41, 59, 0.8);
        color: #e2e8f0;
        font-size: 16px;
        transition: all 0.3s ease;
    }
    
    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        outline: none;
    }
    
    .search-input::placeholder {
        color: #94a3b8;
    }
    
    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #94a3b8;
        font-size: 18px;
    }
    
    .env-data-overlay {
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(30, 41, 59, 0.95);
        border-radius: 12px;
        padding: 15px;
        min-width: 220px;
        border: 1px solid rgba(74, 85, 104, 0.3);
        backdrop-filter: blur(20px);
        z-index: 1000;
    }
    
    .env-metric {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid rgba(74, 85, 104, 0.2);
    }
    
    .env-metric:last-child {
        border-bottom: none;
    }
    
    .metric-label {
        color: #94a3b8;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .metric-value {
        color: #e2e8f0;
        font-weight: 700;
        font-size: 1rem;
    }
    
    .metric-status {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-left: 10px;
    }
    
    .status-good { background: #10b981; }
    .status-warning { background: #f59e0b; }
    .status-critical { background: #ef4444; }
    
    .time-controls {
        background: rgba(30, 41, 59, 0.8);
        border-radius: 12px;
        padding: 15px;
        margin: 15px 0;
    }
    
    .slider-container {
        display: flex;
        align-items: center;
        gap: 15px;
        margin: 10px 0;
    }
    
    .time-slider {
        flex: 1;
        height: 8px;
        border-radius: 4px;
        background: rgba(74, 85, 104, 0.5);
        outline: none;
        -webkit-appearance: none;
    }
    
    .time-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        cursor: pointer;
        border: 3px solid #fff;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .debug-info {
        background: rgba(30, 41, 59, 0.8);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        font-family: 'Courier New', monospace;
        font-size: 13px;
        color: #94a3b8;
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="text-center mb-4">
        <i class="fas fa-map-marked-alt"></i>
        Enhanced Shrimp Farm GPS Tracking System
    </h1>
    
    <!-- Statistics Dashboard -->
    <div class="stats-grid">
        <div class="stat-card">
            <div><i class="fas fa-warehouse" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Active Farms</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-water" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Monitored Ponds</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_workers }}</div>
            <div class="stat-label">GPS Tracked Workers</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-shield-alt" style="font-size: 2rem; margin-bottom: 10px;"></i></div>
            <div class="stat-number">{{ total_geofences }}</div>
            <div class="stat-label">Security Zones</div>
        </div>
    </div>
    
    <!-- Enhanced Search & Filter Controls -->
    <div class="search-container">
        <input type="text" id="mapSearch" class="search-input" placeholder="Search workers, ponds, farms, or locations..." onkeyup="performMapSearch(this.value)">
        <i class="fas fa-search search-icon"></i>
    </div>

    <!-- Advanced Control Panel -->
    <div class="advanced-controls">
        <div class="control-section">
            <h6><i class="fas fa-layer-group"></i> Layer Controls</h6>
            <div class="controls">
                <button class="btn btn-primary" onclick="toggleFarms()">
                    <i class="fas fa-warehouse"></i>
                    Farms
                </button>
                <button class="btn btn-success" onclick="togglePonds()">
                    <i class="fas fa-water"></i>
                    Ponds
                </button>
                <button class="btn btn-info" onclick="toggleWorkers()">
                    <i class="fas fa-users"></i>
                    Workers
                </button>
                <button class="btn btn-warning" onclick="toggleGeofences()">
                    <i class="fas fa-shield-alt"></i>
                    Geofences
                </button>
                <button class="btn btn-success" onclick="togglePondBoundaries()">
                    <i class="fas fa-draw-polygon"></i>
                    Boundaries
                </button>
            </div>
        </div>
        
        <div class="control-section">
            <h6><i class="fas fa-thermometer-half"></i> Environmental Monitoring</h6>
            <div class="controls">
                <button class="btn btn-info" onclick="toggleTemperatureHeatmap()">
                    <i class="fas fa-temperature-high"></i>
                    Heatmap
                </button>
                <button class="btn btn-warning" onclick="toggleWaterQuality()">
                    <i class="fas fa-flask"></i>
                    Water Quality
                </button>
                <button class="btn btn-success" onclick="toggleWorkerTrails()">
                    <i class="fas fa-route"></i>
                    Worker Trails
                </button>
                <button class="btn btn-danger" onclick="toggleAlertZones()">
                    <i class="fas fa-exclamation-triangle"></i>
                    Alert Zones
                </button>
            </div>
        </div>
        
        <div class="control-section">
            <h6><i class="fas fa-clock"></i> Time & Data Controls</h6>
            <div class="time-controls">
                <div class="slider-container">
                    <label style="color: #94a3b8; font-weight: 600;">Historical View:</label>
                    <input type="range" id="timeSlider" class="time-slider" min="0" max="24" value="24" onchange="updateTimeView(this.value)">
                    <span id="timeDisplay" style="color: #e2e8f0; font-weight: 600; min-width: 80px;">Now</span>
                </div>
            </div>
            <div class="controls">
                <button class="btn btn-primary" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                    Center
                </button>
                <button class="btn btn-info" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button class="btn btn-warning" onclick="toggleRealTimeMode()">
                    <i class="fas fa-broadcast-tower"></i>
                    <span id="realtimeText">Real-time</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Map Container with Environmental Overlay -->
    <div class="map-container">
        <div id="map"></div>
        
        <!-- Environmental Data Overlay -->
        <div class="env-data-overlay" id="envDataOverlay">
            <h6 style="color: #e2e8f0; margin-bottom: 15px; font-size: 1rem; font-weight: 700;">
                <i class="fas fa-thermometer-half"></i> Live Environmental Data
            </h6>
            <div class="env-metric">
                <span class="metric-label">Water Temperature</span>
                <span class="metric-value" id="waterTemp">26.5°C</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">pH Level</span>
                <span class="metric-value" id="phLevel">7.2</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Dissolved O₂</span>
                <span class="metric-value" id="oxygenLevel">6.8 mg/L</span>
                <div class="metric-status status-warning"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Salinity</span>
                <span class="metric-value" id="salinity">15 ppt</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Air Temperature</span>
                <span class="metric-value" id="airTemp">28.3°C</span>
                <div class="metric-status status-good"></div>
            </div>
            <div class="env-metric">
                <span class="metric-label">Wind Speed</span>
                <span class="metric-value" id="windSpeed">3.2 m/s</span>
                <div class="metric-status status-good"></div>
            </div>
        </div>
    </div>
    
    <!-- Debug Information -->
    <div class="debug-info">
        <strong>🔧 System Debug Information:</strong><br>
        Map Center: {{ center_lat|floatformat:6 }}, {{ center_lng|floatformat:6 }}<br>
        Data Loaded: Farms: {{ total_farms }} | Ponds: {{ total_ponds }} | Workers: {{ total_workers }} | Geofences: {{ total_geofences }}<br>
        Active Workers: {{ active_workers }}<br>
        <span id="jsDebugInfo" style="color: #10b981;">🔄 Loading JavaScript modules...</span>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
console.log('🚀 Enhanced Map System Loading...');

// Global Variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let workerMarkers = [];
let geofenceOverlays = [];
let pondBoundaries = [];
let temperatureHeatmap = null;
let waterQualityMarkers = [];
let workerTrails = [];
let alertZones = [];
let realTimeInterval = null;
let isRealTimeMode = false;

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const workersData = {{ workers_data|safe }};
const geofencesData = {{ geofences_data|safe }};
const centerLat = parseFloat({{ center_lat }});
const centerLng = parseFloat({{ center_lng }});

// Visibility State
let layerVisibility = {
    farms: true,
    ponds: true,
    workers: true,
    geofences: true,
    boundaries: false
};

function initMap() {
    try {
        console.log('🗺️ Initializing Enhanced Map...');
        console.log('📊 Data Summary:', {
            farms: farmsData.length,
            ponds: pondsData.length,
            workers: workersData.length,
            geofences: geofencesData.length
        });
        
        // Initialize Map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 12,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    "featureType": "all",
                    "elementType": "geometry.fill",
                    "stylers": [{"saturation": -40}, {"lightness": -10}]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [{"color": "#2563eb"}, {"lightness": -10}]
                }
            ]
        });
        
        console.log('✅ Map initialized successfully');
        
        // Add all map elements
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addGeofenceOverlays();
        initializeAdvancedFeatures();
        
        // Update debug info
        document.getElementById('jsDebugInfo').innerHTML = 
            `✅ System Online - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Geofences: ${geofencesData.length}`;
        
        console.log('🎉 Enhanced Map System Ready!');
        
    } catch (error) {
        console.error('❌ Map initialization failed:', error);
        handleMapError(error);
    }
}

function addFarmMarkers() {
    console.log('🏭 Adding farm markers...');
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
                        <circle cx="20" cy="20" r="18" fill="#3b82f6" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="20" cy="20" r="12" fill="#ffffff" opacity="0.2"/>
                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="10" font-family="Arial" font-weight="bold">FARM</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(40, 40)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-warehouse" style="color: #3b82f6; margin-right: 8px;"></i>
                        ${farm.name}
                    </h5>
                    <p><strong>Type:</strong> Farm Facility</p>
                    <p><strong>Description:</strong> ${farm.description || 'Main farming operation'}</p>
                    <p><strong>Location:</strong> ${farm.latitude.toFixed(6)}, ${farm.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        farmMarkers.push(marker);
    });
    console.log(`✅ Added ${farmMarkers.length} farm markers`);
}

function addPondMarkers() {
    console.log('🌊 Adding pond markers...');
    pondsData.forEach(pond => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                        <circle cx="18" cy="18" r="16" fill="#10b981" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="10" fill="#ffffff" opacity="0.3"/>
                        <text x="18" y="22" text-anchor="middle" fill="white" font-size="8" font-family="Arial" font-weight="bold">POND</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36)
            }
        });
        
        marker.addListener('click', () => {
            zoomToPond(pond.id, parseFloat(pond.latitude), parseFloat(pond.longitude));
        });
        
        pondMarkers.push(marker);
    });
    console.log(`✅ Added ${pondMarkers.length} pond markers`);
}

function addWorkerMarkers() {
    console.log('👥 Adding worker markers...');
    workersData.forEach(worker => {
        const statusColors = {
            'working': '#ef4444',
            'available': '#10b981',
            'on_break': '#f59e0b',
            'traveling': '#8b5cf6',
            'offline': '#6b7280'
        };
        
        const color = statusColors[worker.status] || '#6b7280';
        
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(worker.latitude), lng: parseFloat(worker.longitude) },
            map: map,
            title: worker.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                        <circle cx="18" cy="18" r="16" fill="${color}" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="12" fill="#f8f9fa"/>
                        <circle cx="18" cy="14" r="4" fill="#fdbcb4"/>
                        <circle cx="16.5" cy="13" r="0.7" fill="#333"/>
                        <circle cx="19.5" cy="13" r="0.7" fill="#333"/>
                        <circle cx="18" cy="15" r="0.4" fill="#f4a261"/>
                        <path d="M 16 16 Q 18 17.5 20 16" stroke="#333" stroke-width="0.7" fill="none"/>
                        <rect x="14" y="20" width="8" height="6" fill="#4a90e2" rx="2"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-user" style="color: ${color}; margin-right: 8px;"></i>
                        ${worker.name}
                    </h5>
                    <p><strong>Employee ID:</strong> ${worker.employee_id}</p>
                    <p><strong>Team:</strong> ${worker.team}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold;">${worker.status}</span></p>
                    <p><strong>Location:</strong> ${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</p>
                    <p><strong>Last Update:</strong> ${worker.last_update ? new Date(worker.last_update).toLocaleString() : 'N/A'}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        workerMarkers.push(marker);
    });
    console.log(`✅ Added ${workerMarkers.length} worker markers`);
}

function addGeofenceOverlays() {
    console.log('🛡️ Adding geofence overlays...');
    geofencesData.forEach(geofence => {
        let overlay;
        
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            overlay = new google.maps.Circle({
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map,
                center: { lat: parseFloat(geofence.center_latitude), lng: parseFloat(geofence.center_longitude) },
                radius: parseFloat(geofence.radius)
            });
        } else if (geofence.shape_type === 'polygon' && geofence.boundary && geofence.boundary.length > 0) {
            const coords = geofence.boundary.map(coord => ({
                lat: parseFloat(coord[0]),
                lng: parseFloat(coord[1])
            }));
            
            overlay = new google.maps.Polygon({
                paths: coords,
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map
            });
        }
        
        if (overlay) {
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h5>${geofence.name}</h5>
                        <p><strong>Type:</strong> ${geofence.geofence_type}</p>
                        <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                        <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                `
            });
            
            overlay.addListener('click', (event) => {
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
            });
            
            geofenceOverlays.push(overlay);
        }
    });
    console.log(`✅ Added ${geofenceOverlays.length} geofence overlays`);
}

function initializeAdvancedFeatures() {
    console.log('⚡ Initializing advanced features...');
    
    // Start environmental data simulation
    updateEnvironmentalData();
    setInterval(updateEnvironmentalData, 30000);
    
    console.log('✅ Advanced features initialized');
}

// Enhanced Pond Zoom Function
function zoomToPond(pondId, lat, lng) {
    console.log(`🎯 Zooming to pond ${pondId}`);
    
    map.setCenter({ lat: lat, lng: lng });
    map.setZoom(18);
    
    setTimeout(() => {
        const pond = pondsData.find(p => p.id === pondId);
        if (pond) {
            const detailedInfoWindow = new google.maps.InfoWindow({
                content: `
                    <div style="max-width: 350px;">
                        <h4 style="margin: 0 0 15px 0; color: #1f2937;">
                            <i class="fas fa-water" style="color: #10b981; margin-right: 8px;"></i>
                            ${pond.name}
                        </h4>
                        <div style="display: grid; gap: 8px; font-size: 14px;">
                            <div><strong>Farm:</strong> ${pond.farm_name}</div>
                            <div><strong>Coordinates:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}</div>
                            <div style="margin-top: 15px; text-align: center;">
                                <button onclick="centerMap()" 
                                        style="padding: 10px 15px; background: #6b7280; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 12px;">
                                    <i class="fas fa-compress-arrows-alt"></i> Back to Overview
                                </button>
                            </div>
                        </div>
                    </div>
                `,
                position: { lat: lat, lng: lng }
            });
            
            if (window.currentInfoWindow) {
                window.currentInfoWindow.close();
            }
            
            detailedInfoWindow.open(map);
            window.currentInfoWindow = detailedInfoWindow;
        }
    }, 800);
}

// Layer Toggle Functions
function toggleFarms() {
    layerVisibility.farms = !layerVisibility.farms;
    farmMarkers.forEach(marker => marker.setVisible(layerVisibility.farms));
}

function togglePonds() {
    layerVisibility.ponds = !layerVisibility.ponds;
    pondMarkers.forEach(marker => marker.setVisible(layerVisibility.ponds));
}

function toggleWorkers() {
    layerVisibility.workers = !layerVisibility.workers;
    workerMarkers.forEach(marker => marker.setVisible(layerVisibility.workers));
}

function toggleGeofences() {
    layerVisibility.geofences = !layerVisibility.geofences;
    geofenceOverlays.forEach(overlay => overlay.setVisible(layerVisibility.geofences));
}

function togglePondBoundaries() {
    layerVisibility.boundaries = !layerVisibility.boundaries;
    // Implementation would go here
}

function toggleTemperatureHeatmap() {
    if (temperatureHeatmap) {
        temperatureHeatmap.setMap(temperatureHeatmap.getMap() ? null : map);
    } else {
        createTemperatureHeatmap();
    }
}

function toggleWaterQuality() {
    console.log('🧪 Toggling water quality markers');
}

function toggleWorkerTrails() {
    console.log('📍 Toggling worker trails');
}

function toggleAlertZones() {
    console.log('⚠️ Toggling alert zones');
}

function createTemperatureHeatmap() {
    const heatmapData = [];
    
    pondsData.forEach(pond => {
        const baseTemp = 26 + Math.random() * 4;
        for (let i = 0; i < 8; i++) {
            const angle = (i * 45) * Math.PI / 180;
            const distance = 0.001;
            
            heatmapData.push({
                location: new google.maps.LatLng(
                    parseFloat(pond.latitude) + Math.cos(angle) * distance,
                    parseFloat(pond.longitude) + Math.sin(angle) * distance
                ),
                weight: baseTemp
            });
        }
    });
    
    temperatureHeatmap = new google.maps.visualization.HeatmapLayer({
        data: heatmapData,
        map: map,
        radius: 50,
        opacity: 0.6
    });
    
    console.log('🌡️ Temperature heatmap created');
}

function centerMap() {
    map.setCenter({ lat: centerLat, lng: centerLng });
    map.setZoom(12);
}

function performMapSearch(query) {
    if (query.length < 2) {
        [...farmMarkers, ...pondMarkers, ...workerMarkers].forEach(marker => marker.setVisible(true));
        return;
    }
    
    query = query.toLowerCase();
    
    farmMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && layerVisibility.farms);
    });
    
    pondMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && layerVisibility.ponds);
    });
    
    workerMarkers.forEach(marker => {
        const visible = marker.getTitle().toLowerCase().includes(query);
        marker.setVisible(visible && layerVisibility.workers);
    });
}

function updateTimeView(hours) {
    const timeDisplay = document.getElementById('timeDisplay');
    if (hours == 24) {
        timeDisplay.textContent = 'Now';
    } else {
        const time = new Date();
        time.setHours(time.getHours() - (24 - hours));
        timeDisplay.textContent = time.toLocaleTimeString();
    }
}

function toggleRealTimeMode() {
    isRealTimeMode = !isRealTimeMode;
    const button = document.getElementById('realtimeText');
    
    if (isRealTimeMode) {
        button.textContent = 'Stop Real-time';
        realTimeInterval = setInterval(refreshData, 5000);
        console.log('⚡ Real-time mode enabled');
    } else {
        button.textContent = 'Real-time';
        if (realTimeInterval) {
            clearInterval(realTimeInterval);
            realTimeInterval = null;
        }
        console.log('⏸️ Real-time mode disabled');
    }
}

function refreshData() {
    console.log('🔄 Refreshing data...');
    updateEnvironmentalData();
}

function updateEnvironmentalData() {
    const waterTemp = (26 + Math.random() * 4).toFixed(1);
    const phLevel = (6.8 + Math.random() * 1.4).toFixed(1);
    const oxygenLevel = (5.5 + Math.random() * 3).toFixed(1);
    const salinity = (14 + Math.random() * 2).toFixed(0);
    const airTemp = (27 + Math.random() * 4).toFixed(1);
    const windSpeed = (2 + Math.random() * 3).toFixed(1);
    
    document.getElementById('waterTemp').textContent = `${waterTemp}°C`;
    document.getElementById('phLevel').textContent = phLevel;
    document.getElementById('oxygenLevel').textContent = `${oxygenLevel} mg/L`;
    document.getElementById('salinity').textContent = `${salinity} ppt`;
    document.getElementById('airTemp').textContent = `${airTemp}°C`;
    document.getElementById('windSpeed').textContent = `${windSpeed} m/s`;
    
    updateStatusIndicator('waterTemp', waterTemp, 26, 30);
    updateStatusIndicator('phLevel', phLevel, 6.5, 8.5);
    updateStatusIndicator('oxygenLevel', oxygenLevel, 6, 9);
}

function updateStatusIndicator(elementId, value, minGood, maxGood) {
    const element = document.getElementById(elementId).parentElement;
    const indicator = element.querySelector('.metric-status');
    
    if (value < minGood || value > maxGood) {
        indicator.className = 'metric-status status-critical';
    } else if (value < minGood + 0.5 || value > maxGood - 0.5) {
        indicator.className = 'metric-status status-warning';
    } else {
        indicator.className = 'metric-status status-good';
    }
}

function handleMapError(error) {
    console.error('❌ Map error:', error);
    document.getElementById('map').innerHTML = `
        <div style="padding: 50px; text-align: center; color: #ef4444; background: rgba(30, 41, 59, 0.9); border-radius: 12px;">
            <h3><i class="fas fa-exclamation-triangle"></i> Map Loading Error</h3>
            <p>Failed to load Google Maps. Please check your internet connection and API key.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; margin-top: 15px;">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    `;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 DOM Ready - Waiting for Google Maps...');
});
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=visualization&callback=initMap"
    onerror="handleMapError('Failed to load Google Maps API')">
</script>
{% endblock %}
