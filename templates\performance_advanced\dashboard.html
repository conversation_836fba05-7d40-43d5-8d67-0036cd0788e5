<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Performance Dashboard - System Optimization Center</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- No external chart library needed -->
    
    <style>
        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #f8fafc;
        }
        
        .performance-container {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        
        .performance-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .performance-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.05) 10px,
                rgba(255,255,255,0.05) 20px
            );
            animation: performancePattern 20s linear infinite;
        }
        
        @keyframes performancePattern {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }
        
        .performance-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .performance-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 100%);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
            animation: pulse-line 2s ease-in-out infinite;
        }
        
        @keyframes pulse-line {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
            border-color: rgba(148, 163, 184, 0.4);
        }
        
        .metric-card.excellent { --accent-color: #16a34a; }
        .metric-card.good { --accent-color: #0ea5e9; }
        .metric-card.warning { --accent-color: #f59e0b; }
        .metric-card.critical { --accent-color: #dc2626; }
        
        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-color) 100%);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }
        
        .metric-value {
            font-size: 2.2rem;
            font-weight: 700;
            color: #f8fafc;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .metric-value.updating {
            color: var(--accent-color);
            transform: scale(1.05);
        }
        
        .metric-label {
            color: #cbd5e1;
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .metric-status {
            font-size: 0.9rem;
            padding: 4px 12px;
            border-radius: 20px;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-panel {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        }
        
        .panel-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #f8fafc;
        }
        
        .live-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #16a34a;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .live-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #16a34a;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .system-status {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(51, 65, 85, 0.3);
            border-radius: 10px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }
        
        .status-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .status-label {
            font-size: 0.9rem;
            color: #cbd5e1;
        }
        
        .alerts-feed {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .alert-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
            transition: all 0.3s ease;
        }
        
        .alert-item:hover {
            background: rgba(51, 65, 85, 0.3);
        }
        
        .alert-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            flex-shrink: 0;
        }
        
        .alert-icon.critical { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); }
        .alert-icon.warning { background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); }
        .alert-icon.info { background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%); }
        
        .alert-content {
            flex: 1;
        }
        
        .alert-title {
            font-weight: 600;
            color: #f8fafc;
            margin-bottom: 5px;
        }
        
        .alert-description {
            color: #cbd5e1;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .alert-time {
            color: #94a3b8;
            font-size: 0.8rem;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(14, 165, 233, 0.3);
            border: none;
            cursor: pointer;
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(14, 165, 233, 0.4);
            color: white;
        }
        
        .quick-action.success {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
            box-shadow: 0 4px 15px rgba(22, 163, 74, 0.3);
        }
        
        .quick-action.success:hover {
            box-shadow: 0 8px 25px rgba(22, 163, 74, 0.4);
        }
        
        .simple-chart {
            height: 300px;
            background: rgba(51, 65, 85, 0.3);
            border-radius: 10px;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .chart-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #cbd5e1;
            font-size: 1.1rem;
            text-align: center;
        }

        .chart-bars {
            display: flex;
            align-items: end;
            height: 100%;
            gap: 8px;
            padding: 20px 0;
        }

        .chart-bar {
            flex: 1;
            background: linear-gradient(to top, #0ea5e9, #0284c7);
            border-radius: 4px 4px 0 0;
            min-height: 10px;
            position: relative;
            transition: all 0.3s ease;
        }

        .chart-bar:hover {
            background: linear-gradient(to top, #0284c7, #0369a1);
            transform: scaleY(1.05);
        }

        .chart-bar-label {
            position: absolute;
            bottom: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: #94a3b8;
            white-space: nowrap;
        }

        .chart-bar-value {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            color: #f8fafc;
            background: rgba(15, 23, 42, 0.8);
            padding: 2px 6px;
            border-radius: 4px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .chart-bar:hover .chart-bar-value {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .performance-container {
                margin: 10px;
                padding: 20px;
            }

            .charts-section {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .system-status {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

<div class="performance-container">
    <!-- Performance Header -->
    <div class="performance-header">
        <h1 class="performance-title">⚡ Performance Dashboard</h1>
        <p class="performance-subtitle">System Optimization & Monitoring Center</p>
        
        <!-- Quick Actions -->
        <div class="quick-actions mt-4">
            <a href="{% url 'performance_advanced:metrics' %}" class="quick-action">
                <i class="fas fa-chart-line"></i>
                <span>View Metrics</span>
            </a>
            <a href="{% url 'performance_advanced:database_queries' %}" class="quick-action">
                <i class="fas fa-database"></i>
                <span>Database</span>
            </a>
            <a href="{% url 'performance_advanced:alerts' %}" class="quick-action">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Alerts</span>
            </a>
            <button class="quick-action success" onclick="optimizeSystem()">
                <i class="fas fa-rocket"></i>
                <span>Optimize</span>
            </button>
        </div>
    </div>

    <!-- Performance Metrics -->
    <div class="metrics-grid">
        <div class="metric-card {% if metrics.avg_response_time < 200 %}excellent{% elif metrics.avg_response_time < 500 %}good{% elif metrics.avg_response_time < 1000 %}warning{% else %}critical{% endif %}">
            <div class="metric-icon">
                <i class="fas fa-tachometer-alt"></i>
            </div>
            <div class="metric-value" id="avg-response-time">{{ metrics.avg_response_time|floatformat:0 }}ms</div>
            <div class="metric-label">Avg Response Time</div>
            <div class="metric-status">
                {% if metrics.avg_response_time < 200 %}Excellent{% elif metrics.avg_response_time < 500 %}Good{% elif metrics.avg_response_time < 1000 %}Warning{% else %}Critical{% endif %}
            </div>
        </div>
        
        <div class="metric-card {% if metrics.slow_requests == 0 %}excellent{% elif metrics.slow_requests < 10 %}good{% elif metrics.slow_requests < 50 %}warning{% else %}critical{% endif %}">
            <div class="metric-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="metric-value" id="slow-requests">{{ metrics.slow_requests }}</div>
            <div class="metric-label">Slow Requests (24h)</div>
            <div class="metric-status">
                {% if metrics.slow_requests == 0 %}No Issues{% elif metrics.slow_requests < 10 %}Minor{% elif metrics.slow_requests < 50 %}Moderate{% else %}High{% endif %}
            </div>
        </div>
        
        <div class="metric-card {% if metrics.slow_queries == 0 %}excellent{% elif metrics.slow_queries < 5 %}good{% elif metrics.slow_queries < 20 %}warning{% else %}critical{% endif %}">
            <div class="metric-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="metric-value" id="slow-queries">{{ metrics.slow_queries }}</div>
            <div class="metric-label">Slow Queries (24h)</div>
            <div class="metric-status">
                {% if metrics.slow_queries == 0 %}Optimized{% elif metrics.slow_queries < 5 %}Good{% elif metrics.slow_queries < 20 %}Review{% else %}Critical{% endif %}
            </div>
        </div>
        
        <div class="metric-card {% if metrics.critical_alerts == 0 %}excellent{% elif metrics.critical_alerts < 3 %}warning{% else %}critical{% endif %}">
            <div class="metric-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="metric-value" id="active-alerts">{{ metrics.active_alerts }}</div>
            <div class="metric-label">Active Alerts</div>
            <div class="metric-status">
                {{ metrics.critical_alerts }} Critical
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-section">
        <!-- Performance Trends Chart -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">Performance Trends</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            <div id="performance-trends-chart" class="simple-chart">
                <div class="chart-message">Loading performance data...</div>
            </div>
        </div>

        <!-- System Status Panel -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">System Status</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            
            {% if latest_system %}
            <div class="system-status">
                <div class="status-item">
                    <div class="status-value" style="color: {% if latest_system.cpu_percent < 70 %}#16a34a{% elif latest_system.cpu_percent < 85 %}#f59e0b{% else %}#dc2626{% endif %}">
                        {{ latest_system.cpu_percent|floatformat:1 }}%
                    </div>
                    <div class="status-label">CPU Usage</div>
                </div>
                
                <div class="status-item">
                    <div class="status-value" style="color: {% if latest_system.memory_percent < 70 %}#16a34a{% elif latest_system.memory_percent < 85 %}#f59e0b{% else %}#dc2626{% endif %}">
                        {{ latest_system.memory_percent|floatformat:1 }}%
                    </div>
                    <div class="status-label">Memory</div>
                </div>
                
                <div class="status-item">
                    <div class="status-value" style="color: {% if latest_system.disk_percent < 80 %}#16a34a{% elif latest_system.disk_percent < 90 %}#f59e0b{% else %}#dc2626{% endif %}">
                        {{ latest_system.disk_percent|floatformat:1 }}%
                    </div>
                    <div class="status-label">Disk Usage</div>
                </div>
            </div>
            {% endif %}
            
            <!-- Recent Alerts -->
            <div class="alerts-feed" id="alerts-feed">
                <h6 class="text-light mb-3">Recent Alerts</h6>
                {% for alert in recent_alerts %}
                <div class="alert-item">
                    <div class="alert-icon {{ alert.severity }}">
                        <i class="fas fa-{% if alert.alert_type == 'slow_response' %}clock{% elif alert.alert_type == 'high_cpu' %}microchip{% elif alert.alert_type == 'high_memory' %}memory{% elif alert.alert_type == 'low_disk' %}hdd{% else %}exclamation-triangle{% endif %}"></i>
                    </div>
                    <div class="alert-content">
                        <div class="alert-title">{{ alert.title }}</div>
                        <div class="alert-description">{{ alert.description|truncatechars:60 }}</div>
                        <div class="alert-time">{{ alert.created_at|timesince }} ago</div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <p>No recent alerts</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Performance Dashboard JavaScript
let isUpdating = false; // Flag to prevent multiple simultaneous updates

document.addEventListener('DOMContentLoaded', function() {
    console.log('⚡ Performance Dashboard initializing...');

    initializePerformanceChart();
    startRealTimeUpdates();
});

function initializePerformanceChart() {
    const chartContainer = document.getElementById('performance-trends-chart');
    if (!chartContainer) {
        console.error('Chart container not found');
        return;
    }

    console.log('Initializing simple performance chart...');

    // Load initial data
    loadPerformanceAnalytics();
}

function loadPerformanceAnalytics() {
    if (isUpdating) {
        console.log('Update already in progress, skipping...');
        return;
    }

    isUpdating = true;
    console.log('Loading performance analytics...');

    // Set a timeout to prevent hanging
    const timeoutId = setTimeout(() => {
        console.warn('Analytics request timed out');
        isUpdating = false;
        showFallbackChart();
    }, 10000); // 10 second timeout

    fetch('{% url "performance_advanced:analytics_api" %}?hours=24')
        .then(response => {
            clearTimeout(timeoutId);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Analytics data received:', data);
            if (data && data.response_trends) {
                updatePerformanceChart(data.response_trends);
            } else {
                console.warn('No response_trends in data');
                showFallbackChart();
            }
        })
        .catch(error => {
            clearTimeout(timeoutId);
            console.error('Failed to load performance analytics:', error);
            showFallbackChart();
        })
        .finally(() => {
            isUpdating = false;
            console.log('Analytics loading completed');
        });
}

function showFallbackChart() {
    console.log('Showing fallback chart data');
    // Show some sample data instead of empty chart
    const fallbackData = [
        { hour: new Date(Date.now() - 3600000).toISOString(), avg_response_time: 150 },
        { hour: new Date(Date.now() - 1800000).toISOString(), avg_response_time: 200 },
        { hour: new Date().toISOString(), avg_response_time: 180 }
    ];
    updatePerformanceChart(fallbackData);
}

function updatePerformanceChart(trends) {
    const chartContainer = document.getElementById('performance-trends-chart');
    if (!chartContainer) {
        console.warn('Chart container not found');
        return;
    }

    try {
        console.log('Updating simple chart with data:', trends);

        // Handle empty or invalid data
        if (!trends || !Array.isArray(trends) || trends.length === 0) {
            chartContainer.innerHTML = '<div class="chart-message">No performance data available</div>';
            return;
        }

        // Limit and process data points
        const maxPoints = 12;
        const limitedTrends = trends.slice(-maxPoints);

        // Find max value for scaling
        const maxValue = Math.max(...limitedTrends.map(item => parseFloat(item.avg_response_time) || 0));
        const scaledMax = Math.max(maxValue, 100); // Minimum scale of 100ms

        // Create chart HTML
        let chartHTML = '<div class="chart-bars">';

        limitedTrends.forEach((item, index) => {
            const responseTime = parseFloat(item.avg_response_time) || 0;
            const height = Math.max((responseTime / scaledMax) * 100, 5); // Minimum 5% height

            let label = `Point ${index + 1}`;
            if (item.hour) {
                try {
                    const date = new Date(item.hour);
                    label = date.getHours() + ':00';
                } catch (e) {
                    label = `H${index + 1}`;
                }
            }

            chartHTML += `
                <div class="chart-bar" style="height: ${height}%">
                    <div class="chart-bar-value">${responseTime.toFixed(1)}ms</div>
                    <div class="chart-bar-label">${label}</div>
                </div>
            `;
        });

        chartHTML += '</div>';

        // Update chart container
        chartContainer.innerHTML = chartHTML;

        console.log('Simple chart updated with', limitedTrends.length, 'points');

    } catch (error) {
        console.error('Error updating simple chart:', error);
        chartContainer.innerHTML = '<div class="chart-message">Error loading chart data</div>';
    }
}

function startRealTimeUpdates() {
    // Disable automatic updates to prevent infinite loops
    console.log('Real-time updates disabled to prevent chart loops');

    // Optional: Add manual refresh button functionality
    const refreshButton = document.createElement('button');
    refreshButton.textContent = '🔄 Refresh Data';
    refreshButton.className = 'btn btn-outline-light btn-sm';
    refreshButton.onclick = () => {
        console.log('Manual refresh triggered');
        loadPerformanceAnalytics();
    };

    // Add refresh button to header if it exists
    const header = document.querySelector('.performance-header');
    if (header) {
        header.appendChild(refreshButton);
    }
}

function refreshDashboard() {
    console.log('🔄 Manual refresh triggered...');

    // Animate refresh
    document.querySelectorAll('.metric-value').forEach(el => {
        el.classList.add('updating');
        setTimeout(() => {
            el.classList.remove('updating');
        }, 500);
    });

    // Reload analytics once
    loadPerformanceAnalytics();

    console.log('✅ Dashboard refreshed');
}

function optimizeSystem() {
    console.log('🚀 Starting system optimization...');
    
    // Show optimization animation
    const button = event.target.closest('.quick-action');
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Optimizing...</span>';
    button.disabled = true;
    
    // Simulate optimization process
    setTimeout(() => {
        button.innerHTML = '<i class="fas fa-check"></i> <span>Optimized!</span>';
        
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 2000);
    }, 3000);
    
    // In a real implementation, this would trigger actual optimization routines
}

// Auto-refresh disabled to prevent infinite loops
// setInterval(refreshDashboard, 300000);
</script>

</body>
</html>
