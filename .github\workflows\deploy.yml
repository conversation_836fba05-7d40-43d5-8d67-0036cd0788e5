name: Deploy Shrimp Farm Guardian

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Set up SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
        
    - name: Add SSH known hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -H ${{ secrets.SERVER_IP }} >> ~/.ssh/known_hosts
        
    - name: Deploy to production server
      run: |
        ssh ${{ secrets.SSH_USER }}@${{ secrets.SERVER_IP }} << 'EOF'
          cd ${{ secrets.PROJECT_PATH }}
          
          # Pull latest changes
          git pull
          
          # Create .env file if it doesn't exist
          if [ ! -f .env ]; then
            echo "Creating .env file from secrets..."
            cat > .env << 'ENVFILE'
            ${{ secrets.ENV_FILE_CONTENT }}
            ENVFILE
          fi
          
          # Run deployment script
          chmod +x scripts/deploy.sh
          ./scripts/deploy.sh
          
          # Run database migrations
          docker-compose exec -T api python manage.py migrate
          
          # Collect static files
          docker-compose exec -T api python manage.py collectstatic --noinput
          
          # Clear cache
          docker-compose exec -T api python manage.py clear_cache
          
          echo "Deployment completed successfully!"
        EOF
        
    - name: Verify deployment
      run: |
        # Wait for services to be ready
        sleep 30
        
        # Check if the application is responding
        curl -sSf https://${{ secrets.DOMAIN_NAME }}/api/health || exit 1
        
        echo "Deployment verification successful!"
        
    - name: Send notification
      if: always()
      uses: rtCamp/action-slack-notify@v2
      env:
        SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
        SLACK_CHANNEL: deployments
        SLACK_TITLE: Deployment Status
        SLACK_MESSAGE: |
          Deployment to production ${{ job.status == 'success' && 'succeeded' || 'failed' }}!
          
          Repository: ${{ github.repository }}
          Branch: ${{ github.ref_name }}
          Commit: ${{ github.sha }}
          Triggered by: ${{ github.actor }}
        SLACK_COLOR: ${{ job.status == 'success' && 'good' || 'danger' }}
