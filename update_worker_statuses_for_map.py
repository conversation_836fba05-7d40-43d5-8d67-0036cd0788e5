#!/usr/bin/env python3
"""
Update worker statuses for enhanced map testing
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

from labor.models import Worker

def update_worker_statuses_for_testing():
    print("🔄 UPDATING WORKER STATUSES FOR ENHANCED MAP TESTING")
    print("=" * 60)
    
    # Get workers with GPS data
    workers = list(Worker.objects.filter(
        current_latitude__isnull=False,
        current_longitude__isnull=False
    )[:10])
    
    if not workers:
        print("❌ No workers with GPS data found!")
        return
        
    print(f"📊 Found {len(workers)} workers with GPS data")
    
    # Define status options to cycle through
    status_options = ['working', 'available', 'on_break', 'traveling', 'offline']
    
    print("\n🎨 Assigning varied statuses:")
    
    for i, worker in enumerate(workers):
        old_status = getattr(worker, 'status', 'unknown')
        new_status = status_options[i % len(status_options)]
        
        # Update worker status
        worker.status = new_status
        worker.save()
        
        print(f"   Worker {worker.id:2d}: {worker.name:20} | {old_status:10} → {new_status:10}")
    
    print(f"\n✅ Updated {len(workers)} worker statuses")
    
    # Verify the update
    print("\n🔍 VERIFICATION:")
    status_count = {}
    for worker in workers:
        worker.refresh_from_db()  # Reload from database
        status = worker.status
        status_count[status] = status_count.get(status, 0) + 1
        
    for status, count in status_count.items():
        print(f"   {status.upper():12}: {count:2d} workers")
    
    print(f"\n🎯 Status distribution: {len(status_count)} different statuses")
    return True

if __name__ == "__main__":
    update_worker_statuses_for_testing()
