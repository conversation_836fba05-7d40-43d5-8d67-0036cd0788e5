{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .map-container {
        height: 400px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .form-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .section-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 15px;
        border-bottom: 2px solid #3498db;
        padding-bottom: 5px;
    }
    
    .marker-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
    }
    
    .instructions {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .coordinate-display {
        font-family: monospace;
        background: #f1f1f1;
        padding: 5px;
        border-radius: 3px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-home mr-2"></i>{{ page_title }}
                </h2>
                <a href="{% url 'ponds:farm_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Farms
                </a>
            </div>
            
            <!-- Instructions -->
            <div class="instructions">
                <h5><i class="fas fa-info-circle mr-2"></i>Instructions:</h5>
                <ul class="mb-0">
                    <li>Click on the map to set the farm location</li>
                    <li>Fill in the farm details below the map</li>
                    <li>Click "Create Farm" to save</li>
                </ul>
            </div>
            
            <form method="post" id="farmForm">
                {% csrf_token %}
                
                <!-- Map Section -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>Farm Location
                    </h4>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button type="button" id="getCurrentLocation" class="btn btn-info btn-sm">
                                <i class="fas fa-crosshairs mr-1"></i>Get Current Location
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <span class="coordinate-display" id="coordinateDisplay">
                                Click on map to set location
                            </span>
                        </div>
                    </div>
                    
                    <div id="map" class="map-container"></div>
                    
                    <div class="marker-info">
                        <strong>Farm Location:</strong>
                        <div id="farmLocationInfo">No location selected</div>
                    </div>
                </div>
                
                <!-- Form Fields Section -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-edit mr-2"></i>Farm Details
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}">Farm Name *</label>
                                {{ form.name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.location.id_for_label }}">Location Description</label>
                                {{ form.location }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.size.id_for_label }}">Farm Size (hectares)</label>
                                {{ form.size }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_person.id_for_label }}">Contact Person</label>
                                {{ form.contact_person }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_phone.id_for_label }}">Contact Phone</label>
                                {{ form.contact_phone }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.contact_email.id_for_label }}">Contact Email</label>
                                {{ form.contact_email }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden fields for coordinates -->
                    <input type="hidden" id="latitude" name="latitude">
                    <input type="hidden" id="longitude" name="longitude">
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save mr-2"></i>Create Farm
                    </button>
                    <a href="{% url 'ponds:farm_list' %}" class="btn btn-secondary btn-lg ml-3">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let map;
let farmMarker = null;
let currentLocationMarker = null;

function initMap() {
    // Default center (Chennai, India)
    const defaultCenter = { lat: 13.0827, lng: 80.2707 };
    
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 15,
        center: defaultCenter,
        mapTypeId: 'satellite'
    });
    
    // Add click listener for farm placement
    map.addListener('click', function(event) {
        setFarmLocation(event.latLng);
    });
    
    // Try to get user's current location
    getCurrentLocation();
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };
            
            map.setCenter(userLocation);
            map.setZoom(17);
            
            // Add current location marker
            if (currentLocationMarker) {
                currentLocationMarker.setMap(null);
            }
            
            currentLocationMarker = new google.maps.Marker({
                position: userLocation,
                map: map,
                title: 'Your Current Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
                }
            });
            
            updateCoordinateDisplay(userLocation.lat, userLocation.lng);
        }, function() {
            console.log('Geolocation failed');
        });
    }
}

function setFarmLocation(latLng) {
    // Remove existing farm marker
    if (farmMarker) {
        farmMarker.setMap(null);
    }
    
    // Create new farm marker
    farmMarker = new google.maps.Marker({
        position: latLng,
        map: map,
        title: 'Farm Location',
        icon: {
            url: 'https://maps.google.com/mapfiles/ms/icons/yellow-dot.png',
            scaledSize: new google.maps.Size(32, 32)
        }
    });
    
    // Update form fields
    document.getElementById('latitude').value = latLng.lat();
    document.getElementById('longitude').value = latLng.lng();
    
    // Update display
    document.getElementById('farmLocationInfo').innerHTML = 
        `Lat: ${latLng.lat().toFixed(6)}, Lng: ${latLng.lng().toFixed(6)}`;
    
    updateCoordinateDisplay(latLng.lat(), latLng.lng());
}

function updateCoordinateDisplay(lat, lng) {
    document.getElementById('coordinateDisplay').textContent = 
        `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
}

// Event listeners
document.getElementById('getCurrentLocation').addEventListener('click', getCurrentLocation);

// Form validation
document.getElementById('farmForm').addEventListener('submit', function(e) {
    const latitude = document.getElementById('latitude').value;
    const longitude = document.getElementById('longitude').value;
    
    if (!latitude || !longitude) {
        e.preventDefault();
        alert('Please select a location on the map for the farm.');
        return false;
    }
});
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
</script>
{% endblock %}
