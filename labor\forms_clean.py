from django import forms
from .models import Team, Worker, Task, WorkLog, Schedule, Shift, Geofence, LocationLog, GeofenceEvent, HeatMapData, WeatherLog
from .equipment import Equipment, EquipmentAssignment, EquipmentMaintenance, EquipmentIssue
from ponds.models import Pond


class TeamForm(forms.ModelForm):
    class Meta:
        model = Team
        fields = ['name', 'description']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }


class WorkerForm(forms.ModelForm):
    class Meta:
        model = Worker
        fields = ['name', 'phone', 'email', 'address', 'skill_level', 'team', 'hourly_rate', 'is_active']
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
            'hourly_rate': forms.NumberInput(attrs={'step': '0.01'}),
        }


class TaskForm(forms.ModelForm):
    class Meta:
        model = Task
        fields = ['title', 'description', 'pond', 'assigned_to', 'assigned_team',
                  'status', 'priority', 'due_date', 'estimated_hours', 'task_type',
                  'location_notes', 'required_skills', 'safety_requirements']
        widgets = {
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter a clear, descriptive task title...',
                'maxlength': '200'
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Provide detailed instructions, objectives, and any special requirements...'
            }),
            'pond': forms.Select(attrs={
                'class': 'form-select'
            }),
            'assigned_to': forms.Select(attrs={
                'class': 'form-select'
            }),
            'assigned_team': forms.Select(attrs={
                'class': 'form-select'
            }),
            'status': forms.Select(attrs={
                'class': 'form-select'
            }),
            'priority': forms.Select(attrs={
                'class': 'form-select'
            }),
            'task_type': forms.Select(attrs={
                'class': 'form-select'
            }),
            'due_date': forms.DateTimeInput(attrs={
                'type': 'datetime-local',
                'class': 'form-control'
            }),
            'estimated_hours': forms.NumberInput(attrs={
                'step': '0.5',
                'min': '0.5',
                'max': '24',
                'class': 'form-control',
                'placeholder': '2.0'
            }),
            'location_notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Optional: Specific location details, landmarks, or access instructions...'
            }),
            'required_skills': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Water testing, Equipment operation, Safety protocols...'
            }),
            'safety_requirements': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 2,
                'placeholder': 'Safety equipment, precautions, or special considerations...'
            }),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Customize field labels and help text
        self.fields['title'].help_text = "Clear, action-oriented title that describes what needs to be done"
        self.fields['description'].help_text = "Detailed instructions, objectives, materials needed, and success criteria"
        self.fields['pond'].help_text = "Select the specific pond where this task will be performed"
        self.fields['assigned_to'].help_text = "Choose a specific worker to assign this task to"
        self.fields['assigned_team'].help_text = "Alternatively, assign to an entire team"
        self.fields['priority'].help_text = "Set priority level based on urgency and importance"
        self.fields['due_date'].help_text = "When should this task be completed?"
        self.fields['estimated_hours'].help_text = "Estimated time to complete this task"
        self.fields['task_type'].help_text = "Select the type of task being performed"
        self.fields['location_notes'].help_text = "Provide specific location details within the pond area"
        self.fields['required_skills'].help_text = "List skills needed for this task (comma-separated)"
        self.fields['safety_requirements'].help_text = "Specify safety requirements and precautions"
        
        # Make certain fields required
        self.fields['title'].required = True
        self.fields['description'].required = True
        self.fields['priority'].required = True
        
        # Filter active workers and teams only
        from .models import Worker, Team
        self.fields['assigned_to'].queryset = Worker.objects.filter(is_active=True).order_by('name')
        self.fields['assigned_team'].queryset = Team.objects.filter(is_active=True).order_by('name')
        
        # Add empty labels for better UX
        self.fields['pond'].empty_label = "Select a pond..."
        self.fields['assigned_to'].empty_label = "Choose a worker..."
        self.fields['assigned_team'].empty_label = "Choose a team..."


class TaskCompletionForm(forms.ModelForm):
    class Meta:
        model = Task
        fields = ['completion_notes']
        widgets = {
            'completion_notes': forms.Textarea(attrs={'rows': 4}),
        }


class WorkLogForm(forms.ModelForm):
    class Meta:
        model = WorkLog
        fields = ['worker', 'task', 'description', 'start_time', 'end_time', 'break_duration']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'start_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'end_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'break_duration': forms.TextInput(attrs={'placeholder': 'HH:MM:SS'}),
        }


class ScheduleForm(forms.ModelForm):
    class Meta:
        model = Schedule
        fields = ['name', 'title', 'description', 'team', 'start_date', 'end_date']
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'Schedule name'}),
            'title': forms.TextInput(attrs={'placeholder': 'Schedule title (optional)'}),
            'description': forms.Textarea(attrs={'rows': 3}),
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make name required but title optional
        self.fields['name'].required = True
        self.fields['title'].required = False


class ShiftForm(forms.ModelForm):
    class Meta:
        model = Shift
        fields = ['schedule', 'worker', 'shift_type', 'start_time', 'end_time', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
            'start_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'end_time': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
        }


class GeofenceForm(forms.ModelForm):
    class Meta:
        model = Geofence
        fields = ['name', 'description', 'geofence_type', 'boundary', 'pond',
                  'radius', 'center_latitude', 'center_longitude', 'is_active']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
            'boundary': forms.Textarea(attrs={'rows': 5, 'class': 'json-input'}),
            'center_latitude': forms.NumberInput(attrs={'step': 'any'}),
            'center_longitude': forms.NumberInput(attrs={'step': 'any'}),
            'radius': forms.NumberInput(attrs={'step': 'any'}),
        }


class LocationLogForm(forms.ModelForm):
    class Meta:
        model = LocationLog
        fields = ['worker', 'latitude', 'longitude', 'altitude', 'accuracy',
                  'timestamp', 'battery_level', 'connection_type']
        widgets = {
            'timestamp': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'latitude': forms.NumberInput(attrs={'step': 'any'}),
            'longitude': forms.NumberInput(attrs={'step': 'any'}),
            'altitude': forms.NumberInput(attrs={'step': 'any'}),
            'accuracy': forms.NumberInput(attrs={'step': 'any'}),
            'battery_level': forms.NumberInput(attrs={'step': 'any', 'min': 0, 'max': 100}),
        }


class WorkerLocationUpdateForm(forms.Form):
    latitude = forms.FloatField(widget=forms.NumberInput(attrs={'step': 'any'}))
    longitude = forms.FloatField(widget=forms.NumberInput(attrs={'step': 'any'}))
    altitude = forms.FloatField(required=False, widget=forms.NumberInput(attrs={'step': 'any'}))
    accuracy = forms.FloatField(required=False, widget=forms.NumberInput(attrs={'step': 'any'}))
    battery_level = forms.FloatField(required=False, widget=forms.NumberInput(attrs={'step': 'any', 'min': 0, 'max': 100}))
    connection_type = forms.CharField(required=False, max_length=20)


class HeatMapFilterForm(forms.Form):
    start_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    end_date = forms.DateField(widget=forms.DateInput(attrs={'type': 'date'}))
    data_type = forms.ChoiceField(choices=[
        ('worker_density', 'Worker Density'),
        ('time_spent', 'Time Spent'),
        ('weather_impact', 'Weather Impact')
    ])
    worker = forms.ModelChoiceField(queryset=Worker.objects.all(), required=False)
    pond = forms.ModelChoiceField(queryset=Pond.objects.all(), required=False)


class WeatherLogForm(forms.ModelForm):
    class Meta:
        model = WeatherLog
        fields = ['worker', 'latitude', 'longitude', 'temperature', 'humidity',
                  'pressure', 'wind_speed', 'wind_direction', 'precipitation',
                  'condition', 'timestamp']
        widgets = {
            'timestamp': forms.DateTimeInput(attrs={'type': 'datetime-local'}),
            'latitude': forms.NumberInput(attrs={'step': 'any'}),
            'longitude': forms.NumberInput(attrs={'step': 'any'}),
            'temperature': forms.NumberInput(attrs={'step': 'any'}),
            'humidity': forms.NumberInput(attrs={'step': 'any'}),
            'pressure': forms.NumberInput(attrs={'step': 'any'}),
            'wind_speed': forms.NumberInput(attrs={'step': 'any'}),
            'precipitation': forms.NumberInput(attrs={'step': 'any'}),
        }


# Equipment forms would go here if needed...
