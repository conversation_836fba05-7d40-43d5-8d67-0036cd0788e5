#!/usr/bin/env python
"""
Simple test script for model deployment functionality.
This bypasses Django's test framework to avoid Redis connection issues.
"""

import os
import sys
import tempfile
import shutil
import json
import pickle
from unittest.mock import MagicMock

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')

import django
django.setup()

from enhancements.modules.predictive_analytics.model_deployment import ModelDeploymentService
from enhancements.modules.predictive_analytics.prediction_models import RandomForestModel

def test_model_deployment_service():
    """Test the ModelDeploymentService functionality."""
    print("Testing ModelDeploymentService...")
    
    # Create a temporary directory for testing
    temp_dir = tempfile.mkdtemp()
    print(f"Using temporary directory: {temp_dir}")
    
    try:
        # Create a test service with our temporary directory
        service = ModelDeploymentService()
        service.deployment_dir = os.path.join(temp_dir, 'deployments')
        service.deployment_config_file = os.path.join(service.deployment_dir, 'deployment_config.json')
        
        # Ensure directories exist
        os.makedirs(service.deployment_dir, exist_ok=True)
        
        # Test 1: Initialize deployment config
        print("\n1. Testing deployment config initialization...")
        config = service._initialize_deployment_config()
        assert 'deployments' in config
        assert 'deployment_history' in config
        assert len(config['deployments']) == 0
        assert len(config['deployment_history']) == 0
        print("✓ Deployment config initialization works")
        
        # Test 2: Get deployment config
        print("\n2. Testing get deployment config...")
        config = service.get_deployment_config()
        assert 'deployments' in config
        assert 'deployment_history' in config
        print("✓ Get deployment config works")
        
        # Test 3: Mock model versioning service for deployment test
        print("\n3. Testing model deployment...")
        
        # Create a mock model
        mock_model = RandomForestModel()
        mock_model.target_name = 'temperature'
        mock_model.training_score = 0.95
        
        # Mock the model versioning service
        import enhancements.modules.predictive_analytics.model_deployment as deployment_module
        original_service = deployment_module.model_versioning_service
        
        mock_versioning_service = MagicMock()
        mock_versioning_service.restore_model_version.return_value = {
            'success': True,
            'model': mock_model,
            'metadata': {
                'version_id': 'test-version-id',
                'model_type': 'random_forest',
                'parameter': 'temperature',
                'device_type': None,
                'version_name': 'Test Version'
            }
        }
        mock_versioning_service.set_active_model.return_value = {'success': True}
        
        deployment_module.model_versioning_service = mock_versioning_service
        
        try:
            # Test deployment
            result = service.deploy_model(
                version_id='test-version-id',
                environment='production',
                description='Test deployment'
            )
            
            assert result['success'] == True
            assert 'deployment_id' in result
            print("✓ Model deployment works")
            
            # Test 4: Get deployments
            print("\n4. Testing get deployments...")
            deployments = service.get_deployments()
            assert len(deployments) == 1
            assert deployments[0]['version_id'] == 'test-version-id'
            assert deployments[0]['parameter'] == 'temperature'
            print("✓ Get deployments works")
            
            # Test 5: Get deployment history
            print("\n5. Testing get deployment history...")
            history = service.get_deployment_history()
            assert len(history) == 1
            assert history[0]['version_id'] == 'test-version-id'
            print("✓ Get deployment history works")
            
            # Test 6: Get deployed model
            print("\n6. Testing get deployed model...")
            deployment_id = result['deployment_id']
            deployment_file = os.path.join(service.deployment_dir, f"{deployment_id}.pkl")
            
            # Create the model file manually for this test
            with open(deployment_file, 'wb') as f:
                pickle.dump(mock_model, f)
            
            model, deployment_info = service.get_deployed_model(
                parameter='temperature',
                environment='production'
            )
            
            assert model is not None
            assert deployment_info is not None
            assert deployment_info['version_id'] == 'test-version-id'
            print("✓ Get deployed model works")
            
            # Test 7: Delete deployment
            print("\n7. Testing delete deployment...")
            delete_result = service.delete_deployment(deployment_id)
            assert delete_result['success'] == True
            assert not os.path.exists(deployment_file)
            print("✓ Delete deployment works")
            
        finally:
            # Restore original service
            deployment_module.model_versioning_service = original_service
        
        print("\n✅ All ModelDeploymentService tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir)
        print(f"Cleaned up temporary directory: {temp_dir}")
    
    return True

def test_model_versioning_integration():
    """Test integration with model versioning service."""
    print("\n" + "="*50)
    print("Testing Model Versioning Integration...")
    
    try:
        from enhancements.modules.predictive_analytics.model_versioning import ModelVersioningService
        
        # Create a temporary directory for testing
        temp_dir = tempfile.mkdtemp()
        print(f"Using temporary directory: {temp_dir}")
        
        # Create a test versioning service
        versioning_service = ModelVersioningService()
        versioning_service.versions_dir = os.path.join(temp_dir, 'versions')
        versioning_service.config_dir = os.path.join(temp_dir, 'config')
        versioning_service.active_models_file = os.path.join(versioning_service.config_dir, 'active_models.json')
        
        # Ensure directories exist
        os.makedirs(versioning_service.versions_dir, exist_ok=True)
        os.makedirs(versioning_service.config_dir, exist_ok=True)
        
        # Test saving a model version
        print("\n1. Testing save model version...")
        mock_model = RandomForestModel()
        mock_model.target_name = 'temperature'
        
        result = versioning_service.save_model_version(
            model=mock_model,
            model_type='random_forest',
            parameter='temperature',
            version_name='Test Version',
            description='Test description',
            metrics={'accuracy': 0.95}
        )
        
        assert result['success'] == True
        assert 'version_id' in result
        version_id = result['version_id']
        print("✓ Save model version works")
        
        # Test getting model versions
        print("\n2. Testing get model versions...")
        versions = versioning_service.get_model_versions()
        assert len(versions) == 1
        assert versions[0]['version_id'] == version_id
        print("✓ Get model versions works")
        
        # Test restoring model version
        print("\n3. Testing restore model version...")
        restore_result = versioning_service.restore_model_version(version_id)
        assert restore_result['success'] == True
        assert restore_result['model'] is not None
        print("✓ Restore model version works")
        
        print("\n✅ All Model Versioning tests passed!")
        
        # Clean up
        shutil.rmtree(temp_dir)
        print(f"Cleaned up temporary directory: {temp_dir}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Model Versioning test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting Model Deployment Tests...")
    print("="*50)
    
    success1 = test_model_deployment_service()
    success2 = test_model_versioning_integration()
    
    if success1 and success2:
        print("\n🎉 All tests passed successfully!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
