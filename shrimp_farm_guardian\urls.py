"""
URL configuration for shrimp_farm_guardian project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from core.views_maps_test import (
    test_google_maps_view, direct_google_maps_test, minimal_google_maps_test, 
    maps_diagnostic_dashboard, api_key_test_view, api_key_check_api
)
from django.shortcuts import redirect, render
from django.contrib.auth import views as auth_views
from django.http import HttpResponse, JsonResponse
from django.utils import timezone
from .health import health_check
from django.contrib.auth.decorators import login_required
import monitoring_views
from core.views_monitoring import monitoring_control_center

urlpatterns = [
    # Website URLs (Public)
    path('', include('core.urls_website')),
    
    # Admin and App URLs
    path('admin/', admin.site.urls),
    path('app/', include('core.urls')),
    path('app/users/', include('users.urls')),
    path('app/ponds/', include('ponds.urls')),
    path('app/water-quality/', include('water_quality.urls')),
    path('app/feed/', include('feed.urls')),  # Re-enabled with updated models
    path('app/medicine/', include('medicine.urls')),  # Re-enabled - numpy should be installed
    path('app/lunar/', include(('lunar.urls', 'lunar'), namespace='lunar')),  # Properly added lunar app URLs
    path('app/weather/', include('weather.urls')),
    path('app/alerts/', include('alerts.urls')),  # Unified Alert Management System (includes AI alerts functionality)
    # path('ai-alerts/', include('ai_alerts.urls')),  # AI Alerts module - MERGED into alerts app
    path('app/harvest/', include('harvest.urls')),
    path('app/disease/', include('disease.urls')),  # Re-enabled - numpy should be installed
    path('app/labor/', include('labor.urls')),  # Re-enabled - numpy should be installed
    # path('labor-management/', include('labor_management.urls')),  # Removed for rebuild
    # path('reports/', include('reports.urls')),  # Reports app - temporarily disabled due to missing Farm model
    path('app/enhancements/', include('enhancements.urls')),
    path('app/electricity/', include('electricity.urls')),  # Electricity Management Module
    path('app/digital-twin/', include('digital_twin_api.urls')),  # Re-enabled - numpy should be installed
    path('app/settings/', include('settings.urls')),
    path('app/iot/', include('iot_integration.urls')),  # IoT Device Management

    # Real-time Monitoring
    path('app/monitoring/realtime/', monitoring_views.realtime_dashboard, name='realtime_monitoring'),
    path('app/monitoring/devices/', lambda request: render(request, 'monitoring/iot_devices.html'), name='iot_devices'),
    path('app/monitoring/vision/', lambda request: render(request, 'monitoring/computer_vision.html'), name='computer_vision'),

    # Blockchain & Supply Chain
    path('app/supply-chain/blockchain/', lambda request: render(request, 'supply_chain/blockchain_dashboard.html'), name='blockchain_dashboard'),

    # AR/VR & Immersive Technologies
    path('app/immersive/arvr/', lambda request: render(request, 'immersive/arvr_dashboard.html'), name='arvr_dashboard'),
    path('api/monitoring/sensor-data/', monitoring_views.api_sensor_data, name='api_sensor_data'),
    path('api/monitoring/system-status/', monitoring_views.api_system_status, name='api_system_status'),
    path('api/monitoring/alerts/', monitoring_views.api_alerts, name='api_alerts'),

    # Enhancement Modules - Technical Debt Reduction
    path('app/ai-ml/', include('ai_ml.urls')),  # AI/ML Analytics and Predictions
    # path('security/', include('security_enhancements.urls')),  # Security Management - replaced by comprehensive security
    path('app/infrastructure/', include('devops_infrastructure.urls')),  # DevOps Infrastructure
    path('app/geospatial/', include('geospatial.urls')),  # Geospatial Management

    # Edge Computing & AI Optimization - Phase 9 (consolidated functionality)
    path('app/edge-computing/', include('edge_computing.urls')),  # Edge Computing & Distributed AI
    # path('ai-optimization/', include('ai_optimization.urls')),  # AI Optimization & Quantum Computing - CONSOLIDATED into ai_ml app

    # PHASE 10: ADVANCED SHRIMP FARMING ECOSYSTEM
    path('app/advanced-water-quality/', lambda request: render(request, 'advanced_water_quality/dashboard.html'), name='advanced_water_quality'),
    path('app/shrimp-behavior/', lambda request: render(request, 'shrimp_behavior_analytics/dashboard.html'), name='shrimp_behavior'),
    path('app/mobile-iot/', lambda request: render(request, 'mobile_iot_expansion/dashboard.html'), name='mobile_iot'),
    path('app/blockchain/', lambda request: render(request, 'blockchain_supply_chain/dashboard.html'), name='blockchain'),
    path('app/arvr-digital-twin/', lambda request: render(request, 'arvr_digital_twin/dashboard.html'), name='arvr_digital_twin'),
    path('app/advanced-automation/', lambda request: render(request, 'advanced_automation/dashboard.html'), name='advanced_automation'),
    path('app/business-intelligence/', lambda request: render(request, 'business_intelligence/dashboard.html'), name='business_intelligence'),
    path('app/research-development/', lambda request: render(request, 'research_development/dashboard.html'), name='research_development'),
    path('app/multi-farm-enterprise/', lambda request: render(request, 'multi_farm_enterprise/dashboard.html'), name='multi_farm_enterprise'),

    # Comprehensive Ecosystem Dashboard
    path('app/ecosystem/', lambda request: render(request, 'comprehensive_dashboard.html'), name='comprehensive_ecosystem'),

    # Phase 11: Revolutionary Technologies Dashboard
    path('app/phase11/', lambda request: render(request, 'phase11_revolutionary_dashboard.html'), name='phase11_revolutionary'),

    # Phase 12: Production-Ready Global Platform Dashboard
    path('app/production/', lambda request: render(request, 'production_dashboard.html'), name='production_dashboard'),

    # Dashboard shortcuts
    path('app/medicine-dashboard/', lambda request: redirect('medicine:medicine_dashboard')),
    path('app/direct-labor-dashboard/', lambda request: redirect('labor:direct_dashboard')),
    path('app/standalone-labor-dashboard/', lambda request: redirect('labor:standalone_dashboard')),

    # API URLs - Versioned
    path('api/v1/', include('api.urls')),  # API v1 - current stable
    # path('api/v2/', include('api.urls_v2')),  # API v2 - future version (disabled for now)
    path('api/', include('api.urls')),  # Legacy support - redirects to v1

    # Authentication URLs
    path('accounts/login/', auth_views.LoginView.as_view(), name='login'),
    path('accounts/logout/', auth_views.LogoutView.as_view(), name='logout'),

    # Health check
    path('health/', health_check, name='health_check'),

    # Notification endpoints to fix 404 errors
    path('notifications/', lambda request: JsonResponse({'success': True, 'notifications': [], 'unread_count': 0}), name='notifications'),
    path('notifications/real-time/', lambda request: JsonResponse({'success': True, 'notifications': [], 'timestamp': timezone.now().isoformat()}), name='notifications_realtime'),
    path('notifications/mark-read/', lambda request: JsonResponse({'success': True}), name='mark_notification_read'),
    path('notifications/mark-all-read/', lambda request: JsonResponse({'success': True}), name='mark_all_notifications_read'),

    # Test Google Maps API Key
    path('test-google-maps/', lambda request: HttpResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Google Maps API Key Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .status { padding: 15px; margin: 10px 0; border-radius: 5px; }
            .error { background: #ffebee; color: #c62828; border: 1px solid #ef5350; }
            .success { background: #e8f5e8; color: #2e7d32; border: 1px solid #4caf50; }
            .info { background: #e3f2fd; color: #1976d2; border: 1px solid #2196f3; }
            #map { height: 300px; width: 100%; border: 2px solid #ddd; margin: 10px 0; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        </style>
    </head>
    <body>
        <h1>🗺️ Google Maps API Diagnostic Test</h1>

        <div class="test-section">
            <h3>API Key Information</h3>
            <p><strong>Key:</strong> AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw</p>
            <p><strong>Expected Domain:</strong> 127.0.0.1:8000</p>
        </div>

        <div id="status-container">
            <div id="status-1" class="status info">🔄 Step 1: Loading Google Maps API...</div>
            <div id="status-2" class="status info">⏳ Step 2: Waiting for API initialization...</div>
            <div id="status-3" class="status info">⏳ Step 3: Creating map instance...</div>
        </div>

        <div id="map"></div>

        <div class="test-section">
            <h3>Troubleshooting Information</h3>
            <p>If the map doesn't load, possible issues:</p>
            <ul>
                <li><strong>API Key Restrictions:</strong> Key might be restricted to specific domains</li>
                <li><strong>Billing:</strong> Google Maps requires billing to be enabled</li>
                <li><strong>API Limits:</strong> Daily quota might be exceeded</li>
                <li><strong>Network:</strong> Firewall blocking Google APIs</li>
            </ul>
        </div>

        <script>
            function updateStatus(step, message, type = 'info') {
                const statusEl = document.getElementById('status-' + step);
                statusEl.textContent = message;
                statusEl.className = 'status ' + type;
                console.log('Step ' + step + ':', message);
            }

            function initMap() {
                updateStatus(2, '✅ Step 2: Google Maps API loaded successfully!', 'success');
                updateStatus(3, '🔄 Step 3: Creating map...', 'info');

                try {
                    const map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 8,
                        center: { lat: 14.5995, lng: 120.9842 },
                        mapTypeId: 'roadmap'
                    });

                    new google.maps.Marker({
                        position: { lat: 14.5995, lng: 120.9842 },
                        map: map,
                        title: 'Test Location - Philippines'
                    });

                    updateStatus(3, '🎉 Step 3: Map created successfully! Google Maps is working!', 'success');

                } catch (error) {
                    updateStatus(3, '❌ Step 3: Error creating map: ' + error.message, 'error');
                    console.error('Map creation error:', error);
                }
            }

            window.initMap = initMap;

            // Load Google Maps API
            updateStatus(1, '🔄 Step 1: Loading Google Maps API script...', 'info');

            const script = document.createElement('script');
            script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap';
            script.async = true;
            script.defer = true;

            script.onload = function() {
                updateStatus(1, '✅ Step 1: Google Maps script loaded successfully!', 'success');
            };

            script.onerror = function() {
                updateStatus(1, '❌ Step 1: Failed to load Google Maps script', 'error');
                updateStatus(2, '❌ Step 2: Skipped due to script load failure', 'error');
                updateStatus(3, '❌ Step 3: Skipped due to script load failure', 'error');
            };

            document.head.appendChild(script);

            // Timeout check
            setTimeout(function() {
                if (typeof google === 'undefined') {
                    updateStatus(1, '❌ Step 1: Google Maps API failed to load after 15 seconds', 'error');
                }
            }, 15000);
        </script>
    </body>
    </html>
    """), name='test_google_maps'),

    # Static files app
    path('staticfiles/', include('staticfiles_app.urls')),

    # Chart test app
    path('chart_test/', include('chart_test.urls')),
    
    # Google Maps test views
    path('maps-test/', test_google_maps_view, name='maps_test'),
    path('direct-maps-test/', direct_google_maps_test, name='direct_maps_test'),
    path('minimal-maps-test/', minimal_google_maps_test, name='minimal_maps_test'),
    path('maps-diagnostic/', maps_diagnostic_dashboard, name='maps_diagnostic'),
    path('api-key-test/', api_key_test_view, name='api_key_test'),
    path('api/check-api-key/', api_key_check_api, name='api_key_check_api'),

    # Cumulative Map Dashboard - Direct access URLs
    path('cumulative-map/', include('cumulative_map_urls')),

    # COMPREHENSIVE IMPLEMENTATION - ALL FEATURES
    path('analytics/', include('analytics.urls')),  # Advanced Analytics Dashboard
    # path('reporting/', include('reporting.urls')),  # Comprehensive Reporting System - CONSOLIDATED into analytics app
    path('security/', include('security.urls')),  # Enhanced Security & User Management
    path('notifications/', include('notifications.urls')),  # Smart Notification System
    path('financial/', include('financial.urls')),  # Financial Management Module
    path('enterprise/', include('enterprise.urls')),  # Enterprise Multi-Tenant Module
    # path('ai-ml/', include('ai_ml_advanced.urls')),  # Advanced AI/ML Integration - CONSOLIDATED into ai_ml app
    # path('iot-advanced/', include('iot_advanced.urls')),  # Advanced IoT Device Management - CONSOLIDATED into iot_integration app
    # path('api/v2/', include('api_advanced.urls')),  # Advanced RESTful API System - CONSOLIDATED into api app
    # path('live/', include('realtime_advanced.urls')),  # Advanced Real-time Data Streaming - Temporarily disabled
    # path('design/', include('ui_advanced.urls')),  # Advanced UI/UX Enhancement System - Temporarily disabled
    # path('security/', include('security_advanced.urls')),  # Advanced Security & Compliance Framework - CONSOLIDATED into security app
    # path('performance/', include('performance_advanced.urls')),  # Advanced Performance Optimization & Monitoring - CONSOLIDATED into technical_debt app

    # PWA offline page
    path('offline/', lambda request: render(request, 'offline.html'), name='offline'),

    # Business Intelligence Summary
    path('bi-summary/', lambda request: render(request, 'business_intelligence_summary.html'), name='bi_summary'),

    # Monitoring & IoT Control Center
    path('monitoring-control-center/', monitoring_control_center, name='monitoring_control_center'),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
