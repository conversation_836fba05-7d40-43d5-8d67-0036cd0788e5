<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lunar-Labor Integration - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    .integration-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .integration-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: integration-sweep 4s infinite;
    }
    
    @keyframes integration-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin: 30px 0;
    }
    
    .stat-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 25px;
        border: 2px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        text-align: center;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.4);
    }
    
    .stat-number {
        font-size: 3em;
        font-weight: bold;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .integration-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin: 30px 0;
    }
    
    .integration-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }
    
    .schedule-item {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }
    
    .schedule-item:hover {
        transform: translateX(10px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .alert-item {
        background: linear-gradient(45deg, #fff3cd, #ffeaa7);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        border-left: 5px solid #f39c12;
        transition: all 0.3s ease;
    }
    
    .alert-item.critical {
        background: linear-gradient(45deg, #f8d7da, #f5c6cb);
        border-left-color: #dc3545;
    }
    
    .alert-item.warning {
        background: linear-gradient(45deg, #fff3cd, #ffeaa7);
        border-left-color: #ffc107;
    }
    
    .alert-item.info {
        background: linear-gradient(45deg, #d1ecf1, #bee5eb);
        border-left-color: #17a2b8;
    }
    
    .priority-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85em;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .priority-critical { background: #dc3545; color: white; }
    .priority-high { background: #fd7e14; color: white; }
    .priority-medium { background: #ffc107; color: #212529; }
    .priority-low { background: #28a745; color: white; }
    
    .sync-button {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        font-size: 1.1em;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
    }
    
    .sync-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(40, 167, 69, 0.6);
        background: linear-gradient(45deg, #20c997, #28a745);
        color: white;
    }
    
    .integration-flow {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 30px 0;
        gap: 20px;
    }
    
    .flow-item {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        font-weight: bold;
        text-align: center;
        min-width: 120px;
    }
    
    .flow-arrow {
        font-size: 2em;
        color: #667eea;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/" class="quick-action">
                    <i class="fas fa-calendar"></i>
                    Lunar Calendar
                </a>
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/integration/" class="quick-action">
                    <i class="fas fa-link"></i>
                    Integration Demo
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid">
    <!-- Integration Header -->
    <div class="integration-header">
        <div class="position-relative">
            <h1><i class="fas fa-link me-3"></i>Lunar-Labor Integration</h1>
            <p class="lead mb-0">Synchronized workforce management based on lunar calendar</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🌙 LUNAR CALENDAR</span>
                <span class="badge bg-light text-dark me-2">👥 LABOR MANAGEMENT</span>
                <span class="badge bg-light text-dark me-2">🚨 SMART ALERTS</span>
                <span class="badge bg-success text-white ms-3">🔗 INTEGRATED</span>
            </div>
        </div>
    </div>

    <!-- Integration Flow -->
    <div class="integration-flow">
        <div class="flow-item">🌙 LUNAR</div>
        <div class="flow-arrow">→</div>
        <div class="flow-item">👥 LABOR</div>
        <div class="flow-arrow">→</div>
        <div class="flow-item">🚨 ALERTS</div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ schedule_stats.today_count }}</div>
            <h6>Today's Schedules</h6>
            <p class="mb-0">Lunar-optimized tasks</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ schedule_stats.week_count }}</div>
            <h6>Week Ahead</h6>
            <p class="mb-0">Upcoming schedules</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ alert_stats.active_count }}</div>
            <h6>Active Alerts</h6>
            <p class="mb-0">Requiring attention</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ alert_stats.critical_count }}</div>
            <h6>Critical Alerts</h6>
            <p class="mb-0">Immediate action needed</p>
        </div>
    </div>

    <!-- Integration Panels -->
    <div class="integration-grid">
        <!-- Work Schedules Panel -->
        <div class="integration-panel">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3><i class="fas fa-calendar-check me-2"></i>Lunar Work Schedules</h3>
                <button class="sync-button" onclick="syncLunarSystems()">
                    <i class="fas fa-sync-alt me-2"></i>Sync Systems
                </button>
            </div>

            <!-- Today's Schedules -->
            <h5>Today's Schedules</h5>
            {% for schedule in today_schedules %}
            <div class="schedule-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6>{{ schedule.title }}</h6>
                        <p class="mb-2">{{ schedule.description }}</p>
                        <small class="text-muted">
                            <i class="fas fa-moon me-1"></i>{{ schedule.lunar_day.phase }}
                            <i class="fas fa-users ms-3 me-1"></i>{{ schedule.required_workers }} workers
                            <i class="fas fa-clock ms-3 me-1"></i>{{ schedule.estimated_hours }}h
                        </small>
                    </div>
                    <span class="priority-badge priority-{{ schedule.priority }}">{{ schedule.priority }}</span>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-calendar-times fa-3x mb-3"></i>
                <p>No schedules for today</p>
            </div>
            {% endfor %}

            <!-- Upcoming Schedules -->
            <h5 class="mt-4">Upcoming Schedules</h5>
            {% for schedule in upcoming_schedules %}
            <div class="schedule-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6>{{ schedule.title }}</h6>
                        <p class="mb-2">{{ schedule.description }}</p>
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>{{ schedule.lunar_day.date }}
                            <i class="fas fa-moon ms-3 me-1"></i>{{ schedule.lunar_day.phase }}
                            <i class="fas fa-users ms-3 me-1"></i>{{ schedule.required_workers }} workers
                        </small>
                    </div>
                    <span class="priority-badge priority-{{ schedule.priority }}">{{ schedule.priority }}</span>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-calendar-plus fa-3x mb-3"></i>
                <p>No upcoming schedules</p>
            </div>
            {% endfor %}
        </div>

        <!-- Alerts Panel -->
        <div class="integration-panel">
            <h3><i class="fas fa-bell me-2"></i>Lunar Alerts</h3>

            <!-- Active Alerts -->
            <h5>Active Alerts</h5>
            {% for alert in active_alerts %}
            <div class="alert-item {{ alert.severity }}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6>{{ alert.title }}</h6>
                        <p class="mb-2">{{ alert.message }}</p>
                        <small class="text-muted">
                            <i class="fas fa-moon me-1"></i>{{ alert.lunar_day.phase }}
                            <i class="fas fa-calendar ms-3 me-1"></i>{{ alert.lunar_day.date }}
                            {% if alert.related_pond %}
                            <i class="fas fa-water ms-3 me-1"></i>{{ alert.related_pond.name }}
                            {% endif %}
                        </small>
                    </div>
                    <span class="badge bg-{{ alert.severity }}">{{ alert.severity|upper }}</span>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-bell-slash fa-3x mb-3"></i>
                <p>No active alerts</p>
            </div>
            {% endfor %}

            <!-- Pending Alerts -->
            <h5 class="mt-4">Pending Alerts</h5>
            {% for alert in pending_alerts %}
            <div class="alert-item">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6>{{ alert.title }}</h6>
                        <p class="mb-2">{{ alert.message }}</p>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>Triggers: {{ alert.trigger_date|date:"M d, H:i" }}
                            <i class="fas fa-moon ms-3 me-1"></i>{{ alert.lunar_day.phase }}
                        </small>
                    </div>
                    <span class="badge bg-secondary">PENDING</span>
                </div>
            </div>
            {% empty %}
            <div class="text-center text-muted py-4">
                <i class="fas fa-clock fa-3x mb-3"></i>
                <p>No pending alerts</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Integration Actions -->
    <div class="text-center mt-5">
        <h3>Integration Management</h3>
        <p class="lead">Manage the integration between lunar calendar, labor management, and alerts</p>
        <div class="mt-4">
            <a href="{% url 'lunar:lunar_calendar' %}" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-moon me-2"></i>Lunar Calendar
            </a>
            <a href="{% url 'lunar:full_lunar_calendar' %}" class="btn btn-outline-primary btn-lg me-3">
                <i class="fas fa-calendar-alt me-2"></i>Full Calendar
            </a>
            <button class="btn btn-success btn-lg" onclick="syncLunarSystems()">
                <i class="fas fa-sync-alt me-2"></i>Sync All Systems
            </button>
        </div>
    </div>
</div>

<!-- Sync Status Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">System Synchronization</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="syncStatus">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Synchronizing...</span>
                        </div>
                        <p class="mt-3">Synchronizing lunar systems...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function syncLunarSystems() {
    // Show sync modal
    const syncModal = new bootstrap.Modal(document.getElementById('syncModal'));
    syncModal.show();
    
    // Make sync request
    fetch('{% url "lunar:sync_lunar_systems" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        const statusDiv = document.getElementById('syncStatus');
        
        if (data.success) {
            statusDiv.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                    <h5>Synchronization Successful!</h5>
                    <ul class="list-unstyled mt-3">
                        <li><i class="fas fa-calendar-plus me-2"></i>Schedules created: ${data.data.schedules_created}</li>
                        <li><i class="fas fa-bell me-2"></i>Alerts created: ${data.data.alerts_created}</li>
                        <li><i class="fas fa-bolt me-2"></i>Alerts activated: ${data.data.alerts_activated}</li>
                        <li><i class="fas fa-users me-2"></i>Worker assignments: ${data.data.assignments_made}</li>
                    </ul>
                    <button class="btn btn-primary mt-3" onclick="location.reload()">Refresh Page</button>
                </div>
            `;
        } else {
            statusDiv.innerHTML = `
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <h5>Synchronization Failed</h5>
                    <p class="text-muted">${data.message}</p>
                    <button class="btn btn-secondary mt-3" data-bs-dismiss="modal">Close</button>
                </div>
            `;
        }
    })
    .catch(error => {
        const statusDiv = document.getElementById('syncStatus');
        statusDiv.innerHTML = `
            <div class="text-center">
                <i class="fas fa-times-circle text-danger fa-3x mb-3"></i>
                <h5>Synchronization Error</h5>
                <p class="text-muted">An error occurred during synchronization.</p>
                <button class="btn btn-secondary mt-3" data-bs-dismiss="modal">Close</button>
            </div>
        `;
    });
}
// Auto-refresh every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
