<!DOCTYPE html>
<html>
<head>
    <title>Direct Google Maps Test</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        h1 { color: #333; }
        #map { height: 400px; width: 100%; border: 2px solid #333; margin: 20px 0; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🗺️ Direct Google Maps Implementation</h1>
    
    <div class="info">
        <strong>API Key:</strong> {{ google_maps_api_key|slice:":25" }}...<br>
        <strong>Pond:</strong> {{ pond.name }}<br>
        <strong>Location:</strong> {{ pond.latitude }}, {{ pond.longitude }}
    </div>
    
    <div id="status" class="status">⏳ Loading...</div>
    
    <div id="map"></div>
    
    <div class="info">
        <button onclick="testDirectLoad()">Test Direct Load</button>
        <button onclick="recreateMap()">Recreate Map</button>
        <button onclick="checkGoogleObject()">Check Google Object</button>
    </div>

    <script>
        let map;
        let marker;
        
        const pondData = {
            name: "{{ pond.name }}",
            lat: {{ pond.latitude }},
            lng: {{ pond.longitude }}
        };

        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            console.log(message);
        }

        function createMap() {
            try {
                updateStatus('Creating Google Map...', 'info');
                
                map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: pondData.lat, lng: pondData.lng },
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.SATELLITE
                });
                
                marker = new google.maps.Marker({
                    position: { lat: pondData.lat, lng: pondData.lng },
                    map: map,
                    title: pondData.name
                });
                
                updateStatus('✅ Google Maps created successfully!', 'success');
                
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
            }
        }

        function testDirectLoad() {
            updateStatus('Testing direct Google Maps load...', 'info');
            
            if (typeof google === 'undefined') {
                updateStatus('❌ Google object not found', 'error');
                return;
            }
            
            if (!google.maps) {
                updateStatus('❌ Google Maps not available', 'error');
                return;
            }
            
            updateStatus('✅ Google Maps API is available', 'success');
            createMap();
        }

        function recreateMap() {
            const mapEl = document.getElementById('map');
            mapEl.innerHTML = '';
            createMap();
        }

        function checkGoogleObject() {
            updateStatus('Checking Google object...', 'info');
            console.log('Google object:', typeof google !== 'undefined' ? google : 'undefined');
            console.log('Google Maps:', typeof google !== 'undefined' && google.maps ? 'available' : 'not available');
            
            if (typeof google !== 'undefined' && google.maps) {
                updateStatus('✅ Google Maps API is loaded and ready', 'success');
            } else {
                updateStatus('❌ Google Maps API not loaded', 'error');
            }
        }

        // This function will be called by Google Maps API
        window.initMap = function() {
            updateStatus('Google Maps API callback triggered', 'info');
            createMap();
        };

        // Check if Google is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            updateStatus('Google Maps already loaded', 'success');
            createMap();
        } else {
            updateStatus('Waiting for Google Maps API...', 'info');
        }
    </script>
    
    <!-- Load Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
    </script>
</body>
</html>
