"""
API Throttling Classes
Custom throttling classes for different API endpoints
"""

from rest_framework.throttling import UserRateThrottle, AnonRateThrottle

class CriticalEndpointThrottle(UserRateThrottle):
    """
    Throttle class for critical endpoints that need stricter rate limiting
    Examples: password reset, authentication, sensitive data access
    """
    scope = 'critical'
    
    def allow_request(self, request, view):
        # Check if this is a critical endpoint
        if hasattr(view, 'throttle_scope') and view.throttle_scope == 'critical':
            return super().allow_request(request, view)
        return True

class AuthenticationThrottle(AnonRateThrottle):
    """
    Throttle class for authentication endpoints to prevent brute force attacks
    """
    scope = 'auth'
    
    def allow_request(self, request, view):
        # Only throttle authentication endpoints
        if request.path.startswith('/api/v1/auth/') or request.path.startswith('/api/v2/auth/'):
            return super().allow_request(request, view)
        return True

class UploadThrottle(UserRateThrottle):
    """
    Throttle class for file upload endpoints
    """
    scope = 'upload'
    
    def allow_request(self, request, view):
        # Only throttle upload endpoints and POST requests
        if request.method == 'POST' and hasattr(view, 'throttle_scope') and view.throttle_scope == 'upload':
            return super().allow_request(request, view)
        return True

class IoTDataThrottle(UserRateThrottle):
    """
    Throttle class for IoT data ingestion endpoints
    Higher limits for machine-to-machine communication
    """
    scope = 'iot_data'
    
    def allow_request(self, request, view):
        # Only throttle IoT data endpoints
        if hasattr(view, 'throttle_scope') and view.throttle_scope == 'iot_data':
            return super().allow_request(request, view)
        return True

class BurstRateThrottle(UserRateThrottle):
    """
    Throttle for short bursts of requests (per minute)
    """
    scope = 'burst'

class SustainedRateThrottle(UserRateThrottle):
    """
    Throttle for sustained usage over longer periods (per day)
    """
    scope = 'sustained'
