<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Record Medicine Application - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-syringe medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Application Form</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Record Medicine Application</h2>
                    <p class="mb-0 opacity-75">Record a new medicine application for your pond</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Application</small>
                                <strong>New Record</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/application/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to Applications
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-list"></i>
                Applications List
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                Medicines
            </a>
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-tags"></i>
                Categories
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
        width: 100%;
        margin-top: 1.5rem;
    }

    /* Custom styling for range inputs */
    input[type="range"] {
        -webkit-appearance: none;
        width: 100%;
        height: 8px;
        border-radius: 5px;
        background: #e9ecef;
        outline: none;
    }

    input[type="range"]::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    input[type="range"]::-webkit-slider-thumb:hover {
        background: #2563eb;
        transform: scale(1.1);
    }

    input[type="range"]::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #3b82f6;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
    }

    input[type="range"]::-moz-range-thumb:hover {
        background: #2563eb;
        transform: scale(1.1);
    }

    .range-labels {
        display: flex;
        justify-content: space-between;
        margin-top: 0.5rem;
        font-size: 0.75rem;
        color: #6c757d;
    }

    .treatment-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .treatment-info-title {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .treatment-info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.25rem;
    }

    .treatment-info-label {
        color: #6c757d;
    }

    .treatment-info-value {
        font-weight: 500;
    }

    .application-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
    }

    .application-info-title {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .application-info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.25rem;
    }

    .application-info-label {
        color: #6c757d;
    }

    .application-info-value {
        font-weight: 500;
    }

    .observation-group {
        margin-bottom: 1rem;
    }

    .observation-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .observation-options {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .observation-option {
        display: none;
    }

    .observation-option-label {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        border: 1px solid #e5e7eb;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .observation-option-label:hover {
        background-color: #f3f4f6;
    }

    .observation-option:checked + .observation-option-label {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-syringe text-primary me-2" style="font-size: 1.5rem;"></i>
            <h1 class="h3 mb-0">Record Treatment Application</h1>
        </div>
        <a href="{% url 'medicine:medicine_dashboard' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <form method="post" id="applicationForm">
                {% csrf_token %}

                {% if selected_treatment %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> You are recording an application for the treatment: <strong>{{ selected_treatment.medicine.name }}</strong> for pond <strong>{{ selected_treatment.pond.name }}</strong>.
                </div>
                <input type="hidden" name="treatment" value="{{ selected_treatment.id }}">

                <div class="treatment-info mb-4">
                    <div class="treatment-info-title">Treatment Information</div>
                    <div class="treatment-info-item">
                        <span class="treatment-info-label">Medicine:</span>
                        <span class="treatment-info-value">{{ selected_treatment.medicine.name }}</span>
                    </div>
                    <div class="treatment-info-item">
                        <span class="treatment-info-label">Pond:</span>
                        <span class="treatment-info-value">{{ selected_treatment.pond.name }}</span>
                    </div>
                    <div class="treatment-info-item">
                        <span class="treatment-info-label">Recommended Dosage:</span>
                        <span class="treatment-info-value">{{ selected_treatment.dosage }} {{ selected_treatment.medicine.unit }}/{{ selected_treatment.dosage_unit }}</span>
                    </div>
                    <div class="treatment-info-item">
                        <span class="treatment-info-label">Application Method:</span>
                        <span class="treatment-info-value">{{ selected_treatment.application_method|default_if_none:""|title }}</span>
                    </div>
                </div>

                {% if selected_application %}
                <div class="application-info mb-4">
                    <div class="application-info-title">Scheduled Application</div>
                    <div class="application-info-item">
                        <span class="application-info-label">Day:</span>
                        <span class="application-info-value">Day {{ selected_application.day_number }}</span>
                    </div>
                    <div class="application-info-item">
                        <span class="application-info-label">Scheduled Date:</span>
                        <span class="application-info-value">{{ selected_application.application_date|date:"l, F j, Y" }}</span>
                    </div>
                    <div class="application-info-item">
                        <span class="application-info-label">Recommended Dosage:</span>
                        <span class="application-info-value">{{ selected_application.dosage }} {{ selected_treatment.medicine.unit }}</span>
                    </div>
                </div>
                <input type="hidden" name="application" value="{{ selected_application.id }}">
                {% endif %}

                {% else %}
                <div class="mb-4">
                    <label class="form-label">Select Treatment</label>
                    <div class="row">
                        {% for treatment in active_treatments %}
                        <div class="col-md-6 mb-3">
                            <div class="card treatment-card h-100" onclick="selectTreatment({{ treatment.id }})">
                                <div class="card-body">
                                    <h6 class="card-title">{{ treatment.medicine.name }}</h6>
                                    <p class="card-text small text-muted">{{ treatment.pond.name }}</p>

                                    <div class="mt-3">
                                        <div class="treatment-property">
                                            <span class="treatment-property-label">Start Date:</span>
                                            <span class="treatment-property-value">{{ treatment.start_date|date:"M d, Y" }}</span>
                                        </div>
                                        <div class="treatment-property">
                                            <span class="treatment-property-label">End Date:</span>
                                            <span class="treatment-property-value">{{ treatment.end_date|date:"M d, Y" }}</span>
                                        </div>
                                        <div class="treatment-property">
                                            <span class="treatment-property-label">Dosage:</span>
                                            <span class="treatment-property-value">{{ treatment.dosage }} {{ treatment.medicine.unit }}/{{ treatment.dosage_unit }}</span>
                                        </div>
                                        <div class="treatment-property">
                                            <span class="treatment-property-label">Progress:</span>
                                            <span class="treatment-property-value">{{ treatment.progress }}%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <input type="hidden" name="{{ form.treatment.name }}" id="{{ form.treatment.id_for_label }}" value="{{ form.treatment.value|default:'' }}">
                    {% if form.treatment.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.treatment.errors }}
                        </div>
                    {% endif %}
                </div>
                {% endif %}

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.application_date.id_for_label }}" class="form-label">Application Date</label>
                            <input type="date" name="{{ form.application_date.name }}" id="{{ form.application_date.id_for_label }}" class="form-control" value="{{ form.application_date.value|default:current_date }}" required>
                            {% if form.application_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.application_date.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.application_time.id_for_label }}" class="form-label">Application Time</label>
                            <input type="time" name="{{ form.application_time.name }}" id="{{ form.application_time.id_for_label }}" class="form-control" value="{{ form.application_time.value|default:current_time }}" required>
                            {% if form.application_time.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.application_time.errors }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.applied_by.id_for_label }}" class="form-label">Applied By</label>
                            <select name="{{ form.applied_by.name }}" id="{{ form.applied_by.id_for_label }}" class="form-select" required>
                                <option value="">Select staff member</option>
                                {% for staff in staff_members %}
                                    <option value="{{ staff.id }}" {% if current_user and current_user.id == staff.id %}selected{% endif %}>
                                        {{ staff.name }}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if form.applied_by.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.applied_by.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">Observations</label>

                            <div class="observation-group">
                                <div class="observation-label">Water Clarity</div>
                                <div class="observation-options">
                                    <input type="radio" name="{{ form.water_clarity.name }}" value="clear" id="waterClarityClear" class="observation-option" {% if form.water_clarity.value == 'clear' %}checked{% endif %}>
                                    <label for="waterClarityClear" class="observation-option-label">Clear</label>

                                    <input type="radio" name="{{ form.water_clarity.name }}" value="slightly_cloudy" id="waterClaritySlightlyCloudy" class="observation-option" {% if form.water_clarity.value == 'slightly_cloudy' %}checked{% endif %}>
                                    <label for="waterClaritySlightlyCloudy" class="observation-option-label">Slightly Cloudy</label>

                                    <input type="radio" name="{{ form.water_clarity.name }}" value="cloudy" id="waterClarityCloudy" class="observation-option" {% if form.water_clarity.value == 'cloudy' %}checked{% endif %}>
                                    <label for="waterClarityCloudy" class="observation-option-label">Cloudy</label>

                                    <input type="radio" name="{{ form.water_clarity.name }}" value="very_cloudy" id="waterClarityVeryCloudy" class="observation-option" {% if form.water_clarity.value == 'very_cloudy' %}checked{% endif %}>
                                    <label for="waterClarityVeryCloudy" class="observation-option-label">Very Cloudy</label>
                                </div>
                            </div>

                            <div class="observation-group">
                                <div class="observation-label">Shrimp Behavior</div>
                                <div class="observation-options">
                                    <input type="radio" name="{{ form.shrimp_behavior.name }}" value="normal" id="shrimpBehaviorNormal" class="observation-option" {% if form.shrimp_behavior.value == 'normal' %}checked{% endif %}>
                                    <label for="shrimpBehaviorNormal" class="observation-option-label">Normal</label>

                                    <input type="radio" name="{{ form.shrimp_behavior.name }}" value="lethargic" id="shrimpBehaviorLethargic" class="observation-option" {% if form.shrimp_behavior.value == 'lethargic' %}checked{% endif %}>
                                    <label for="shrimpBehaviorLethargic" class="observation-option-label">Lethargic</label>

                                    <input type="radio" name="{{ form.shrimp_behavior.name }}" value="erratic" id="shrimpBehaviorErratic" class="observation-option" {% if form.shrimp_behavior.value == 'erratic' %}checked{% endif %}>
                                    <label for="shrimpBehaviorErratic" class="observation-option-label">Erratic</label>

                                    <input type="radio" name="{{ form.shrimp_behavior.name }}" value="surface_swimming" id="shrimpBehaviorSurfaceSwimming" class="observation-option" {% if form.shrimp_behavior.value == 'surface_swimming' %}checked{% endif %}>
                                    <label for="shrimpBehaviorSurfaceSwimming" class="observation-option-label">Surface Swimming</label>
                                </div>
                            </div>

                            <div class="observation-group">
                                <div class="observation-label">Side Effects</div>
                                <div class="observation-options">
                                    <input type="radio" name="{{ form.side_effects.name }}" value="none" id="sideEffectsNone" class="observation-option" {% if form.side_effects.value == 'none' %}checked{% endif %}>
                                    <label for="sideEffectsNone" class="observation-option-label">None</label>

                                    <input type="radio" name="{{ form.side_effects.name }}" value="mild" id="sideEffectsMild" class="observation-option" {% if form.side_effects.value == 'mild' %}checked{% endif %}>
                                    <label for="sideEffectsMild" class="observation-option-label">Mild</label>

                                    <input type="radio" name="{{ form.side_effects.name }}" value="moderate" id="sideEffectsModerate" class="observation-option" {% if form.side_effects.value == 'moderate' %}checked{% endif %}>
                                    <label for="sideEffectsModerate" class="observation-option-label">Moderate</label>

                                    <input type="radio" name="{{ form.side_effects.name }}" value="severe" id="sideEffectsSevere" class="observation-option" {% if form.side_effects.value == 'severe' %}checked{% endif %}>
                                    <label for="sideEffectsSevere" class="observation-option-label">Severe</label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            <textarea name="{{ form.notes.name }}" id="{{ form.notes.id_for_label }}" class="form-control" rows="3">{{ form.notes.value|default:'' }}</textarea>
                            {% if form.notes.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.notes.errors }}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6 mx-auto">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">Actual Dosage</h5>
                                <div class="dosage-display">
                                    <span id="dosageValue">{{ form.dosage.value|default:selected_application.dosage|default:selected_treatment.dosage|default:'1.0' }}</span>
                                    <span class="dosage-unit" id="dosageUnitDisplay">{{ selected_treatment.medicine.unit|default:'--' }}</span>
                                </div>

                                <input type="range" name="{{ form.dosage.name }}" id="{{ form.dosage.id_for_label }}" class="dosage-slider" min="0.1" max="10" step="0.1" value="{{ form.dosage.value|default:selected_application.dosage|default:selected_treatment.dosage|default:'1.0' }}" oninput="updateDosage(this.value)">

                                <div class="range-labels">
                                    <div>0.1</div>
                                    <div>5.0</div>
                                    <div>10.0</div>
                                </div>

                                {% if form.dosage.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.dosage.errors }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{% url 'medicine:medicine_dashboard' %}" class="btn btn-outline-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> Record Application
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update dosage display
    function updateDosage(value) {
        document.getElementById('dosageValue').textContent = parseFloat(value).toFixed(1);
    }

    // Select treatment
    function selectTreatment(id) {
        document.getElementById('{{ form.treatment.id_for_label }}').value = id;

        // Update UI to show selected treatment
        const treatmentCards = document.querySelectorAll('.treatment-card');
        treatmentCards.forEach(card => {
            card.classList.remove('selected');
            if (card.querySelector('input[type="hidden"]')?.value == id) {
                card.classList.add('selected');
            }
        });

        // Reload the page with the selected treatment
        window.location.href = "{% url 'medicine:application_create' %}?treatment=" + id;
    }

    // Initialize the form
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dosage display
        updateDosage(document.getElementById('{{ form.dosage.id_for_label }}').value);

        // Initialize treatment selection if value exists
        const treatmentValue = document.getElementById('{{ form.treatment.id_for_label }}')?.value;
        if (treatmentValue) {
            const treatmentCards = document.querySelectorAll('.treatment-card');
            treatmentCards.forEach(card => {
                if (card.querySelector('input[type="hidden"]')?.value == treatmentValue) {
                    card.classList.add('selected');
                }
            });
        }
    });
</script>
{% endblock %}
