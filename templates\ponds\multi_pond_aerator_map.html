<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Pond Aerator Map - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .aerator-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .aerator-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: header-sweep 6s infinite;
        }

        @keyframes header-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
<style>
        #map {
            height: 700px !important;
            width: 100% !important;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            border: 3px solid rgba(255, 255, 255, 0.3);
            background-color: #f8f9fa;
            position: relative;
            overflow: hidden;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
            border: 2px solid transparent;
            transition: all 0.4s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }
    
        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #636e72;
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .legend {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-top: 30px;
            border: 2px solid transparent;
            transition: all 0.4s ease;
        }

        .legend:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }
    
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            padding: 8px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .legend-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(5px);
        }

        .legend-color {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 15px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .pond-marker { background-color: #3b82f6; }
        .aerator-active { background-color: #10b981; }
        .aerator-inactive { background-color: #ef4444; }
        .aerator-maintenance { background-color: #f59e0b; }

        .map-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            transition: all 0.4s ease;
            min-height: 800px;
        }

        .map-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="aerator-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">⚡ Multi-Pond Aerator Map</h1>
                <p class="mb-0" style="opacity: 0.9;">Monitor and manage aerator systems across all ponds</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'ponds:pond_list' %}" class="action-btn">
                    <i class="fas fa-arrow-left"></i> Back to Ponds
                </a>
                <a href="{% url 'ponds:unified_map_dashboard_with_cards' %}" class="action-btn" style="background: linear-gradient(45deg, #74b9ff, #0984e3);">
                    <i class="fas fa-map-marked-alt"></i> Unified Map
                </a>
            </div>
        </div>
    </div>

    <!-- Map Row - Moved to Top -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="map-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0" style="color: #2d3436; font-weight: 600;">
                        <i class="fas fa-map-marked-alt"></i> Aerator Distribution Map
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="action-btn" onclick="fitMapBounds()" style="background: linear-gradient(45deg, #00b894, #00cec9);">
                            <i class="fas fa-expand-arrows-alt"></i> Fit All
                        </button>
                        <button class="action-btn" onclick="refreshMap()" style="background: linear-gradient(45deg, #fdcb6e, #e17055);">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                    </div>
                </div>
                <div id="map" style="height: 700px; width: 100%; background-color: #f8f9fa; border: 2px solid #dee2e6; border-radius: 20px;">
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>🗺️ Loading Aerator Map...</h5>
                            <p class="text-muted">Initializing Google Maps with pond and aerator data...</p>
                            <small class="text-muted">If this takes too long, please check your internet connection</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics and Legend Row -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="stats-card">
                <div class="row">
                    <div class="col-6 stat-item">
                        <div class="stat-number">{{ total_ponds }}</div>
                        <div class="stat-label">Total Ponds</div>
                    </div>
                    <div class="col-6 stat-item">
                        <div class="stat-number">{{ total_aerators }}</div>
                        <div class="stat-label">Total Aerators</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="legend">
                <h6 class="mb-4" style="color: #2d3436; font-weight: 600;">
                    <i class="fas fa-map-signs"></i> Map Legend
                </h6>
                <div class="legend-item">
                    <div class="legend-color pond-marker"></div>
                    <span style="font-weight: 500;">🦐 Pond Location</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-active"></div>
                    <span style="font-weight: 500;">✅ Active Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-inactive"></div>
                    <span style="font-weight: 500;">❌ Inactive Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-maintenance"></div>
                    <span style="font-weight: 500;">🔧 Maintenance Aerator</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    let map;
    let infoWindow;
    
    // Pond data from Django
    const pondsData = {{ ponds_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    
    function initMap() {
        console.log('🗺️ Initializing Google Maps...');
        console.log('📊 Pond Data:', pondsData);
        console.log('📍 Center:', centerLat, centerLng);
        console.log('🔍 Google Maps available:', !!window.google);
        console.log('🔍 Google Maps Maps available:', !!(window.google && window.google.maps));

        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found!');
            return;
        }

        console.log('📦 Map element found:', mapElement);
        console.log('📏 Map element dimensions:', mapElement.offsetWidth, 'x', mapElement.offsetHeight);

        // Clear loading content
        mapElement.innerHTML = '';

        try {
            // Initialize map
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: 'hybrid',
                mapTypeControl: true,
                streetViewControl: true,
                fullscreenControl: true
            });

            console.log('✅ Map initialized successfully!');
            infoWindow = new google.maps.InfoWindow();

            // Add map loaded event listener
            google.maps.event.addListenerOnce(map, 'idle', function() {
                console.log('🎯 Map is fully loaded and idle');
            });

        } catch (error) {
            console.error('❌ Error initializing map:', error);
            mapElement.innerHTML = `
                <div class="alert alert-danger m-3">
                    <h5>Map Initialization Error</h5>
                    <p>Error: ${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">Refresh Page</button>
                </div>
            `;
            return;
        }

        // Add a test marker to verify map is working
        const testMarker = new google.maps.Marker({
            position: { lat: centerLat, lng: centerLng },
            map: map,
            title: 'Test Marker - Map Center',
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="15" cy="15" r="12" fill="#ff0000" stroke="white" stroke-width="2"/>
                        <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-family="Arial">T</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(30, 30)
            }
        });

        console.log('🔴 Test marker added at center position');
        
        // Add markers for each pond and its aerators
        pondsData.forEach(function(pond) {
            if (pond.latitude && pond.longitude) {
                // Add pond marker
                const pondMarker = new google.maps.Marker({
                    position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
                    map: map,
                    title: pond.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="#3b82f6" stroke="white" stroke-width="2"/>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-family="Arial">P</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    }
                });
                
                // Pond info window content
                const pondInfoContent = `
                    <div style="max-width: 250px;">
                        <h6><strong>${pond.name}</strong></h6>
                        <p><strong>Farm:</strong> ${pond.farm_name}</p>
                        <p><strong>Status:</strong> <span class="badge badge-${pond.status === 'active' ? 'success' : 'secondary'}">${pond.status}</span></p>
                        <p><strong>Aerators:</strong> ${pond.aerators.length}</p>
                        <a href="/ponds/${pond.id}/" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                `;
                
                pondMarker.addListener('click', function() {
                    infoWindow.setContent(pondInfoContent);
                    infoWindow.open(map, pondMarker);
                });
                
                // Add aerator markers
                pond.aerators.forEach(function(aerator, index) {
                    if (aerator.latitude && aerator.longitude) {
                        let aeratorColor = '#ef4444'; // inactive - red
                        if (aerator.status === 'active') {
                            aeratorColor = '#10b981'; // active - green
                        } else if (aerator.status === 'maintenance') {
                            aeratorColor = '#f59e0b'; // maintenance - yellow
                        }
                        
                        const aeratorMarker = new google.maps.Marker({
                            position: { lat: parseFloat(aerator.latitude), lng: parseFloat(aerator.longitude) },
                            map: map,
                            title: aerator.name,
                            icon: {
                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                    <svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="12" cy="12" r="10" fill="${aeratorColor}" stroke="white" stroke-width="2"/>
                                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="10" font-family="Arial">A</text>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(24, 24)
                            }
                        });
                        
                        // Aerator info window content
                        const aeratorInfoContent = `
                            <div style="max-width: 200px;">
                                <h6><strong>${aerator.name}</strong></h6>
                                <p><strong>Pond:</strong> ${pond.name}</p>
                                <p><strong>Type:</strong> ${aerator.type}</p>
                                <p><strong>Status:</strong> <span class="badge badge-${aerator.status === 'active' ? 'success' : aerator.status === 'maintenance' ? 'warning' : 'danger'}">${aerator.status}</span></p>
                                ${aerator.power_rating ? `<p><strong>Power:</strong> ${aerator.power_rating} HP</p>` : ''}
                            </div>
                        `;
                        
                        aeratorMarker.addListener('click', function() {
                            infoWindow.setContent(aeratorInfoContent);
                            infoWindow.open(map, aeratorMarker);
                        });
                    }
                });
            }
        });
        
        // Fit map to show all markers
        if (pondsData.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            pondsData.forEach(function(pond) {
                if (pond.latitude && pond.longitude) {
                    bounds.extend(new google.maps.LatLng(parseFloat(pond.latitude), parseFloat(pond.longitude)));
                    
                    pond.aerators.forEach(function(aerator) {
                        if (aerator.latitude && aerator.longitude) {
                            bounds.extend(new google.maps.LatLng(parseFloat(aerator.latitude), parseFloat(aerator.longitude)));
                        }
                    });
                }
            });
            map.fitBounds(bounds);
        }
    }
    
    // Error handling for Google Maps
    window.gm_authFailure = function() {
        console.error('❌ Google Maps authentication failed!');
        document.getElementById('map').innerHTML =
            '<div class="alert alert-danger m-3"><h5>Google Maps Failed to Load</h5><p>Please check your API key or try refreshing the page.</p></div>';
    };

    // Fallback if Google Maps doesn't load within 10 seconds
    setTimeout(function() {
        if (!window.google || !window.google.maps) {
            console.error('❌ Google Maps API failed to load within 10 seconds');
            document.getElementById('map').innerHTML =
                `<div class="alert alert-warning m-3">
                    <h5><i class="fas fa-exclamation-triangle"></i> Map Loading Issue</h5>
                    <p>Google Maps is taking longer than expected to load. This could be due to:</p>
                    <ul>
                        <li>Internet connection issues</li>
                        <li>Google Maps API key restrictions</li>
                        <li>Browser blocking external scripts</li>
                    </ul>
                    <div class="mt-3">
                        <button class="btn btn-primary me-2" onclick="location.reload()">
                            <i class="fas fa-sync-alt"></i> Refresh Page
                        </button>
                        <button class="btn btn-info" onclick="showFallbackMap()">
                            <i class="fas fa-map"></i> Show Simple Map
                        </button>
                    </div>
                </div>`;
        }
    }, 10000);

    // Simple fallback map function
    function showFallbackMap() {
        document.getElementById('map').innerHTML =
            `<div class="p-4 text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px;">
                <h4><i class="fas fa-map-marked-alt"></i> Aerator Distribution Overview</h4>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="bg-white bg-opacity-20 p-3 rounded mb-3">
                            <h5>📍 Total Locations</h5>
                            <p class="mb-0">{{ total_ponds }} ponds with {{ total_aerators }} aerators</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="bg-white bg-opacity-20 p-3 rounded mb-3">
                            <h5>🗺️ Coverage Area</h5>
                            <p class="mb-0">Monitoring systems across multiple farm locations</p>
                        </div>
                    </div>
                </div>
                <p class="mt-3 mb-0 opacity-75">
                    <i class="fas fa-info-circle"></i>
                    Interactive map temporarily unavailable. Please refresh to try loading Google Maps again.
                </p>
            </div>`;
    }

    // Additional map control functions
    function fitMapBounds() {
        if (pondsData.length > 0) {
            const bounds = new google.maps.LatLngBounds();
            pondsData.forEach(function(pond) {
                if (pond.latitude && pond.longitude) {
                    bounds.extend(new google.maps.LatLng(parseFloat(pond.latitude), parseFloat(pond.longitude)));

                    pond.aerators.forEach(function(aerator) {
                        if (aerator.latitude && aerator.longitude) {
                            bounds.extend(new google.maps.LatLng(parseFloat(aerator.latitude), parseFloat(aerator.longitude)));
                        }
                    });
                }
            });
            map.fitBounds(bounds);
        }
    }

    function refreshMap() {
        location.reload();
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        console.log('⚡ Multi-Pond Aerator Map loaded without header and sidebar');
        console.log('🔑 Google Maps API Key:', '{{ google_maps_api_key }}');
        console.log('📊 Total Ponds:', {{ total_ponds }});
        console.log('📊 Total Aerators:', {{ total_aerators }});

        // Show loading message and verify map container
        const mapElement = document.getElementById('map');
        if (mapElement) {
            console.log('✅ Map container found');
            console.log('📏 Container dimensions:', mapElement.offsetWidth, 'x', mapElement.offsetHeight);

            if (!window.google) {
                console.log('⏳ Waiting for Google Maps API to load...');

                // Show a temporary map placeholder
                setTimeout(function() {
                    if (!window.google) {
                        mapElement.innerHTML = `
                            <div class="d-flex justify-content-center align-items-center h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px;">
                                <div class="text-center p-4">
                                    <h4><i class="fas fa-map-marked-alt"></i> Map Loading...</h4>
                                    <p>Google Maps is taking longer than expected to load.</p>
                                    <div class="mt-3">
                                        <button class="btn btn-light" onclick="location.reload()">
                                            <i class="fas fa-sync-alt"></i> Refresh Page
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                }, 5000);
            }
        } else {
            console.error('❌ Map container not found!');
        }
    });
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=marker">
</script>

</body>
</html>