{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .map-container {
        height: 400px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .form-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .section-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 15px;
        border-bottom: 2px solid #3498db;
        padding-bottom: 5px;
    }
    
    .marker-info {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        margin-top: 10px;
    }
    
    .aerator-list {
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        background: #f8f9fa;
    }
    
    .aerator-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #dee2e6;
    }
    
    .aerator-item:last-child {
        border-bottom: none;
    }
    
    .btn-aerator {
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 2px 8px;
        font-size: 12px;
    }
    
    .instructions {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .coordinate-display {
        font-family: monospace;
        background: #f1f1f1;
        padding: 5px;
        border-radius: 3px;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary">
                    <i class="fas fa-fish mr-2"></i>{{ page_title }}
                </h2>
                <a href="{% url 'ponds:pond_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Ponds
                </a>
            </div>
            
            <!-- Instructions -->
            <div class="instructions">
                <h5><i class="fas fa-info-circle mr-2"></i>Instructions:</h5>
                <ul class="mb-0">
                    <li>Click on the map to set the pond location</li>
                    <li>Add aerators by clicking the "Add Aerator" button and then clicking on the map</li>
                    <li>Fill in the pond details below the map</li>
                    <li>Click "Create Pond" to save</li>
                </ul>
            </div>
            
            <form method="post" id="pondForm">
                {% csrf_token %}
                
                <!-- Map Section -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-map-marker-alt mr-2"></i>Pond Location & Aerators
                    </h4>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <button type="button" id="getCurrentLocation" class="btn btn-info btn-sm">
                                <i class="fas fa-crosshairs mr-1"></i>Get Current Location
                            </button>
                            <button type="button" id="addAeratorMode" class="btn btn-success btn-sm ml-2">
                                <i class="fas fa-plus mr-1"></i>Add Aerator
                            </button>
                            <button type="button" id="clearAerators" class="btn btn-warning btn-sm ml-2">
                                <i class="fas fa-trash mr-1"></i>Clear Aerators
                            </button>
                        </div>
                        <div class="col-md-6 text-right">
                            <span class="coordinate-display" id="coordinateDisplay">
                                Click on map to set location
                            </span>
                        </div>
                    </div>
                    
                    <div id="map" class="map-container"></div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="marker-info">
                                <strong>Pond Location:</strong>
                                <div id="pondLocationInfo">No location selected</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="marker-info">
                                <strong>Aerators (<span id="aeratorCount">0</span>):</strong>
                                <div id="aeratorsList" class="aerator-list">
                                    <div class="text-muted">No aerators added</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Form Fields Section -->
                <div class="form-section">
                    <h4 class="section-title">
                        <i class="fas fa-edit mr-2"></i>Pond Details
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}">Pond Name *</label>
                                {{ form.name }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.farm.id_for_label }}">Farm *</label>
                                {{ form.farm }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="length">Length (meters) *</label>
                                <input type="number" step="0.1" class="form-control" id="length" name="length" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="width">Width (meters) *</label>
                                <input type="number" step="0.1" class="form-control" id="width" name="width" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="shrimp_seed_count">Shrimp Seeds *</label>
                                <input type="number" class="form-control" id="shrimp_seed_count" name="shrimp_seed_count" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="calculated_area">Calculated Area (m²)</label>
                                <input type="text" class="form-control" id="calculated_area" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.species.id_for_label }}">Species</label>
                                {{ form.species }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.status.id_for_label }}">Status</label>
                                {{ form.status }}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Hidden fields for coordinates and aerators -->
                    <input type="hidden" id="latitude" name="latitude">
                    <input type="hidden" id="longitude" name="longitude">
                    <input type="hidden" id="aerator_positions" name="aerator_positions">
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-save mr-2"></i>Create Pond
                    </button>
                    <a href="{% url 'ponds:pond_list' %}" class="btn btn-secondary btn-lg ml-3">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let map;
let pondMarker = null;
let aeratorMarkers = [];
let aeratorPositions = [];
let isAeratorMode = false;
let currentLocationMarker = null;

function initMap() {
    // Default center (Chennai, India)
    const defaultCenter = { lat: 13.0827, lng: 80.2707 };
    
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 15,
        center: defaultCenter,
        mapTypeId: 'satellite'
    });
    
    // Add click listener for pond placement and aerator placement
    map.addListener('click', function(event) {
        if (isAeratorMode) {
            addAerator(event.latLng);
            isAeratorMode = false;
            document.getElementById('addAeratorMode').classList.remove('btn-danger');
            document.getElementById('addAeratorMode').classList.add('btn-success');
            document.getElementById('addAeratorMode').innerHTML = '<i class="fas fa-plus mr-1"></i>Add Aerator';
        } else {
            setPondLocation(event.latLng);
        }
    });
    
    // Try to get user's current location
    getCurrentLocation();
}

function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            const userLocation = {
                lat: position.coords.latitude,
                lng: position.coords.longitude
            };
            
            map.setCenter(userLocation);
            map.setZoom(17);
            
            // Add current location marker
            if (currentLocationMarker) {
                currentLocationMarker.setMap(null);
            }
            
            currentLocationMarker = new google.maps.Marker({
                position: userLocation,
                map: map,
                title: 'Your Current Location',
                icon: {
                    url: 'https://maps.google.com/mapfiles/ms/icons/blue-dot.png'
                }
            });
            
            updateCoordinateDisplay(userLocation.lat, userLocation.lng);
        }, function() {
            console.log('Geolocation failed');
        });
    }
}

function setPondLocation(latLng) {
    // Remove existing pond marker
    if (pondMarker) {
        pondMarker.setMap(null);
    }
    
    // Create new pond marker
    pondMarker = new google.maps.Marker({
        position: latLng,
        map: map,
        title: 'Pond Location',
        icon: {
            url: 'https://maps.google.com/mapfiles/ms/icons/green-dot.png',
            scaledSize: new google.maps.Size(32, 32)
        }
    });
    
    // Update form fields
    document.getElementById('latitude').value = latLng.lat();
    document.getElementById('longitude').value = latLng.lng();
    
    // Update display
    document.getElementById('pondLocationInfo').innerHTML = 
        `Lat: ${latLng.lat().toFixed(6)}, Lng: ${latLng.lng().toFixed(6)}`;
    
    updateCoordinateDisplay(latLng.lat(), latLng.lng());
}

function addAerator(latLng) {
    const aeratorNumber = aeratorMarkers.length + 1;
    
    const aeratorMarker = new google.maps.Marker({
        position: latLng,
        map: map,
        title: `Aerator ${aeratorNumber}`,
        icon: {
            url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
            scaledSize: new google.maps.Size(24, 24)
        }
    });
    
    aeratorMarkers.push(aeratorMarker);
    aeratorPositions.push({
        lat: latLng.lat(),
        lng: latLng.lng()
    });
    
    updateAeratorsList();
    updateAeratorPositionsField();
}

function removeAerator(index) {
    // Remove marker from map
    aeratorMarkers[index].setMap(null);
    
    // Remove from arrays
    aeratorMarkers.splice(index, 1);
    aeratorPositions.splice(index, 1);
    
    updateAeratorsList();
    updateAeratorPositionsField();
}

function clearAllAerators() {
    // Remove all markers from map
    aeratorMarkers.forEach(marker => marker.setMap(null));
    
    // Clear arrays
    aeratorMarkers = [];
    aeratorPositions = [];
    
    updateAeratorsList();
    updateAeratorPositionsField();
}

function updateAeratorsList() {
    const aeratorsList = document.getElementById('aeratorsList');
    const aeratorCount = document.getElementById('aeratorCount');
    
    aeratorCount.textContent = aeratorPositions.length;
    
    if (aeratorPositions.length === 0) {
        aeratorsList.innerHTML = '<div class="text-muted">No aerators added</div>';
    } else {
        let html = '';
        aeratorPositions.forEach((position, index) => {
            html += `
                <div class="aerator-item">
                    <span>Aerator ${index + 1}: ${position.lat.toFixed(4)}, ${position.lng.toFixed(4)}</span>
                    <button type="button" class="btn-aerator" onclick="removeAerator(${index})">Remove</button>
                </div>
            `;
        });
        aeratorsList.innerHTML = html;
    }
}

function updateAeratorPositionsField() {
    document.getElementById('aerator_positions').value = JSON.stringify(aeratorPositions);
}

function updateCoordinateDisplay(lat, lng) {
    document.getElementById('coordinateDisplay').textContent = 
        `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
}

function calculateArea() {
    const length = parseFloat(document.getElementById('length').value) || 0;
    const width = parseFloat(document.getElementById('width').value) || 0;
    const area = length * width;
    document.getElementById('calculated_area').value = area.toFixed(2);
}

// Event listeners
document.getElementById('getCurrentLocation').addEventListener('click', getCurrentLocation);

document.getElementById('addAeratorMode').addEventListener('click', function() {
    isAeratorMode = !isAeratorMode;
    
    if (isAeratorMode) {
        this.classList.remove('btn-success');
        this.classList.add('btn-danger');
        this.innerHTML = '<i class="fas fa-times mr-1"></i>Cancel Aerator';
    } else {
        this.classList.remove('btn-danger');
        this.classList.add('btn-success');
        this.innerHTML = '<i class="fas fa-plus mr-1"></i>Add Aerator';
    }
});

document.getElementById('clearAerators').addEventListener('click', clearAllAerators);

document.getElementById('length').addEventListener('input', calculateArea);
document.getElementById('width').addEventListener('input', calculateArea);

// Form validation
document.getElementById('pondForm').addEventListener('submit', function(e) {
    const latitude = document.getElementById('latitude').value;
    const longitude = document.getElementById('longitude').value;
    
    if (!latitude || !longitude) {
        e.preventDefault();
        alert('Please select a location on the map for the pond.');
        return false;
    }
});
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
</script>
{% endblock %}
