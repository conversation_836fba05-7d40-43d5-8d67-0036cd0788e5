<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title|default:"Medicine Category" }} - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .medicine-icon {
        font-size: 2.5rem;
        opacity: 0.9;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-action i {
        font-size: 1.1rem;
    }
    
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding: 20px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 1px solid #e0e0e0;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-tag medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Category</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">{{ title|default:"Medicine Category" }}</h2>
                    <p class="mb-0 opacity-75">Organize medicine types and classifications</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <i class="fas fa-tags" style="font-size: 2rem; opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="/medicine/category/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list"></i> All Categories
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">Loading...</span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-list"></i>
                View Categories
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                View Medicines
            </a>
            <a href="/medicine/dashboard/" class="quick-action">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
        </div>

        <!-- Form -->
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card form-card">
                    <div class="card-header text-center">
                        <h5 class="mb-0">
                            <i class="fas fa-tag me-2"></i>
                            {{ title|default:"Medicine Category" }}
                        </h5>
                    </div>
                    <div class="card-body p-4">
                        <form method="post">
                            {% csrf_token %}
                            
                            <div class="mb-4">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <i class="fas fa-tag me-2"></i>Category Name
                                </label>
                                {{ form.name }}
                                {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                                {% if form.name.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.name.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-4">
                                <label for="{{ form.description.id_for_label }}" class="form-label">
                                    <i class="fas fa-align-left me-2"></i>Description (Optional)
                                </label>
                                {{ form.description }}
                                {% if form.description.help_text %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                                {% endif %}
                                {% if form.description.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.description.errors %}
                                    <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <div class="d-flex gap-3 justify-content-center">
                                <button type="submit" class="btn btn-primary px-4">
                                    <i class="fas fa-save me-2"></i>Save Category
                                </button>
                                <a href="/medicine/category/" class="btn btn-outline-secondary px-4">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Update time immediately and then every minute
        updateTime();
        setInterval(updateTime, 60000);

        // Auto-focus first form field
        document.addEventListener('DOMContentLoaded', function() {
            const firstInput = document.querySelector('input[type="text"], textarea');
            if (firstInput) {
                firstInput.focus();
            }
        });
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
