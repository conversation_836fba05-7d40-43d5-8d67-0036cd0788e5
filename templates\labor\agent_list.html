<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agents & Supervisors - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Header */
    .agents-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .agents-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: agents-sweep 4s infinite;
    }
    
    @keyframes agents-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        text-align: center;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #667eea;
        margin-bottom: 10px;
    }

    .stats-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .agents-table-container {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
        position: relative;
        z-index: 10;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .agent-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .agent-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .agent-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
        margin-right: 15px;
    }

    .role-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .role-field-agent {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .role-supervisor {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
        border: 1px solid rgba(0, 123, 255, 0.2);
    }

    .role-contractor {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }

    .role-coordinator {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }

    .role-manager {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }

    .status-active {
        color: #28a745;
        font-weight: 600;
    }

    .status-inactive {
        color: #dc3545;
        font-weight: 600;
    }

    .search-filter-container {
        background: rgba(102, 126, 234, 0.02);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .action-btn {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        margin: 2px;
        display: inline-block;
    }

    .btn-edit {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
        border: 1px solid rgba(0, 123, 255, 0.2);
    }

    .btn-edit:hover {
        background: #007bff;
        color: white;
        transform: translateY(-1px);
    }

    .btn-view {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .btn-view:hover {
        background: #28a745;
        color: white;
        transform: translateY(-1px);
    }

    .btn-delete {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }

    .btn-delete:hover {
        background: #dc3545;
        color: white;
        transform: translateY(-1px);
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Header -->
    <div class="agents-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Agents & Supervisors</h1>
                <p class="lead mb-0">Manage your farm management team</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <a href="/labor/agents/create/" class="control-btn">
                        <i class="fas fa-plus me-2"></i>Add Agent
                    </a>
                    <a href="/labor/dashboard/" class="control-btn">
                        <i class="fas fa-arrow-left me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">12</div>
                <div class="stats-label">Field Agents</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">6</div>
                <div class="stats-label">Supervisors</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">3</div>
                <div class="stats-label">Contractors</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number">9</div>
                <div class="stats-label">Active Today</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="/labor/agents/create/" class="quick-action">
            <i class="fas fa-user-plus"></i>
            Add Agent
        </a>
        <a href="/labor/workers/" class="quick-action">
            <i class="fas fa-users"></i>
            Workers
        </a>
        <a href="/labor/teams/" class="quick-action">
            <i class="fas fa-users-cog"></i>
            Teams
        </a>
        <a href="/labor/reports/" class="quick-action">
            <i class="fas fa-chart-bar"></i>
            Reports
        </a>
    </div>

    <!-- Search and Filter -->
    <div class="search-filter-container">
        <div class="row">
            <div class="col-md-4 mb-3">
                <input type="text" class="form-control" placeholder="Search agents by name..." id="searchInput">
            </div>
            <div class="col-md-3 mb-3">
                <select class="form-select" id="roleFilter">
                    <option value="">All Roles</option>
                    <option value="field-agent">Field Agents</option>
                    <option value="team-supervisor">Team Supervisors</option>
                    <option value="labor-contractor">Labor Contractors</option>
                    <option value="senior-coordinator">Senior Coordinators</option>
                    <option value="shift-manager">Shift Managers</option>
                    <option value="area-manager">Area Managers</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <select class="form-select" id="departmentFilter">
                    <option value="">All Departments</option>
                    <option value="pond-management">Pond Management</option>
                    <option value="feeding-operations">Feeding Operations</option>
                    <option value="water-quality">Water Quality</option>
                    <option value="harvesting">Harvesting</option>
                    <option value="security">Security</option>
                    <option value="maintenance">Maintenance</option>
                </select>
            </div>
            <div class="col-md-2 mb-3">
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Agents List -->
    <div class="agents-table-container">
        <h3 class="section-title">
            <i class="fas fa-user-tie me-2"></i>Management Team
        </h3>
        
        <div id="agentsList">
            <!-- Agent Card 1 -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">RK</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Agent Ravi Kumar</h6>
                        <small class="text-muted">AGT001</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-field-agent">Field Agent</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">Pond Management</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/1/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/1/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>

            <!-- Agent Card 2 -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">PS</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Agent Priya Sharma</h6>
                        <small class="text-muted">AGT002</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-field-agent">Field Agent</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">Feeding Operations</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/2/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/2/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>

            <!-- Supervisor Card -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">RK</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Supervisor Rajesh Kumar</h6>
                        <small class="text-muted">SUP001</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-supervisor">Supervisor</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">Water Quality</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/3/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/3/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>

            <!-- Labor Contractor Card -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">LR</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Labor Contractor Ramesh</h6>
                        <small class="text-muted">CON001</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-contractor">Contractor</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">General Operations</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/4/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/4/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>

            <!-- Senior Coordinator Card -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">SP</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Senior Coordinator Prakash</h6>
                        <small class="text-muted">CRD001</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-coordinator">Coordinator</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">Harvesting</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/5/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/5/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>

            <!-- Shift Manager Card -->
            <div class="agent-card">
                <div class="row align-items-center">
                    <div class="col-md-1">
                        <div class="agent-avatar">SA</div>
                    </div>
                    <div class="col-md-3">
                        <h6 class="mb-1 fw-bold">Shift Manager Ashok</h6>
                        <small class="text-muted">SHM001</small>
                    </div>
                    <div class="col-md-2">
                        <span class="role-badge role-manager">Shift Manager</span>
                    </div>
                    <div class="col-md-2">
                        <small class="text-muted">Security</small>
                    </div>
                    <div class="col-md-2">
                        <span class="status-active">
                            <i class="fas fa-circle me-1"></i>Active
                        </span>
                    </div>
                    <div class="col-md-2 text-end">
                        <a href="/labor/agents/6/" class="action-btn btn-view">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="/labor/agents/6/edit/" class="action-btn btn-edit">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for search and filter functionality -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const roleFilter = document.getElementById('roleFilter');
    const departmentFilter = document.getElementById('departmentFilter');
    const statusFilter = document.getElementById('statusFilter');
    const agentsList = document.getElementById('agentsList');
    const agentCards = agentsList.querySelectorAll('.agent-card');

    function filterAgents() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedRole = roleFilter.value;
        const selectedDepartment = departmentFilter.value;
        const selectedStatus = statusFilter.value;

        agentCards.forEach(card => {
            const name = card.querySelector('h6').textContent.toLowerCase();
            const role = card.querySelector('.role-badge').textContent.toLowerCase().replace(/\s+/g, '-');
            const department = card.querySelector('small:not(.text-muted)').textContent.toLowerCase().replace(/\s+/g, '-');
            const status = card.querySelector('[class*="status-"]').textContent.toLowerCase().includes('active') ? 'active' : 'inactive';

            const matchesSearch = name.includes(searchTerm);
            const matchesRole = !selectedRole || role.includes(selectedRole);
            const matchesDepartment = !selectedDepartment || department.includes(selectedDepartment);
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesRole && matchesDepartment && matchesStatus) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    // Add event listeners for real-time filtering
    searchInput.addEventListener('input', filterAgents);
    roleFilter.addEventListener('change', filterAgents);
    departmentFilter.addEventListener('change', filterAgents);
    statusFilter.addEventListener('change', filterAgents);

    // Add click event for action buttons
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            if (this.classList.contains('btn-delete')) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this agent?')) {
                    // Handle delete action
                    console.log('Delete agent:', this.href);
                }
            }
        });
    });
});
</script>

</body>
</html>
