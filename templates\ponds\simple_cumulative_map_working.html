{% extends 'base.html' %}
{% load static %}

{% block title %}Simple Cumulative Map with GPS & Geofences{% endblock %}

{% block extra_css %}
<style>
    /* Override base layout styles for full-width map */
    .main-content {
        padding: 1rem !important;
    }
    
    .container-fluid {
        padding: 0 !important;
        max-width: 100% !important;
        margin: 0 !important;
    }
    
    #map {
        height: 600px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: none;
        outline: none;
    }
    
    .map-container {
        margin: 10px 0;
        padding: 15px;
        background: rgba(30, 41, 59, 0.8);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        width: 100%;
        box-sizing: border-box;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 15px 0;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        margin: 10px 0;
    }
    
    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    
    .controls {
        margin: 15px 0;
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 5px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .btn-primary { background: #3b82f6; color: white; }
    .btn-success { background: #10b981; color: white; }
    .btn-info { background: #06b6d4; color: white; }
    .btn-warning { background: #f59e0b; color: white; }
    
    .debug-info {
        background: rgba(30, 41, 59, 0.6);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin: 15px 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: #94a3b8;
        backdrop-filter: blur(10px);
    }
    
    h1 {
        color: #e2e8f0 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="text-center mb-4">
        <i class="fas fa-map-marked-alt"></i>
        {{ page_title }}
    </h1>
    
    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div><i class="fas fa-warehouse"></i></div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Farms</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-water"></i></div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Ponds</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-users"></i></div>
            <div class="stat-number">{{ total_workers }}</div>
            <div class="stat-label">GPS Workers</div>
        </div>
        <div class="stat-card">
            <div><i class="fas fa-shield-alt"></i></div>
            <div class="stat-number">{{ total_geofences }}</div>
            <div class="stat-label">Geofences</div>
        </div>
    </div>
    
    <!-- Basic Controls -->
    <div class="controls">
        <button class="btn btn-primary" onclick="toggleFarms()">
            <i class="fas fa-warehouse"></i> Toggle Farms
        </button>
        <button class="btn btn-success" onclick="togglePonds()">
            <i class="fas fa-water"></i> Toggle Ponds
        </button>
        <button class="btn btn-info" onclick="toggleWorkers()">
            <i class="fas fa-users"></i> Toggle Workers
        </button>
        <button class="btn btn-warning" onclick="toggleGeofences()">
            <i class="fas fa-shield-alt"></i> Toggle Geofences
        </button>
        <button class="btn btn-primary" onclick="centerMap()">
            <i class="fas fa-crosshairs"></i> Center Map
        </button>
    </div>
    
    <!-- Debug Info -->
    <div class="debug-info">
        <strong>Debug Info:</strong><br>
        Map Center: {{ center_lat|floatformat:6 }}, {{ center_lng|floatformat:6 }}<br>
        Farms: {{ total_farms }} | Ponds: {{ total_ponds }} | Workers: {{ total_workers }} | Geofences: {{ total_geofences }}<br>
        <span id="jsDebugInfo">JavaScript loading...</span>
    </div>
    
    <!-- Map Container -->
    <div class="map-container">
        <div id="map"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let workerMarkers = [];
let geofenceOverlays = [];

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const workersData = {{ workers_data|safe }};
const geofencesData = {{ geofences_data|safe }};
const centerLat = {{ center_lat }};
const centerLng = {{ center_lng }};

// Visibility toggles
let farmsVisible = true;
let pondsVisible = true;
let workersVisible = true;
let geofencesVisible = true;

function initMap() {
    console.log('=== INITIALIZING MAP ===');
    console.log('Data counts:');
    console.log('Farms:', farmsData.length);
    console.log('Ponds:', pondsData.length);
    console.log('Workers:', workersData.length);
    console.log('Geofences:', geofencesData.length);
    console.log('Center coordinates:', centerLat, centerLng);
    
    // Update debug info
    document.getElementById('jsDebugInfo').innerHTML = 
        `JS Loaded - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Geofences: ${geofencesData.length}`;
    
    // Initialize map
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: { lat: centerLat, lng: centerLng },
        mapTypeId: google.maps.MapTypeId.ROADMAP
    });
    
    console.log('Map initialized');
    
    // Add markers and overlays
    addFarmMarkers();
    addPondMarkers();
    addWorkerMarkers();
    addGeofenceOverlays();
    
    console.log('All markers and overlays added');
}

function addFarmMarkers() {
    console.log('Adding farm markers...');
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: farm.latitude, lng: farm.longitude },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
                        <circle cx="12" cy="12" r="10" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-family="Arial">F</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${farm.name}</h5>
                    <p><strong>Type:</strong> Farm</p>
                    <p><strong>Description:</strong> ${farm.description || 'No description'}</p>
                    <p><strong>Location:</strong> ${farm.latitude.toFixed(6)}, ${farm.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        farmMarkers.push(marker);
    });
    console.log(`Added ${farmMarkers.length} farm markers`);
}

function addPondMarkers() {
    console.log('Adding pond markers...');
    pondsData.forEach(pond => {
        const marker = new google.maps.Marker({
            position: { lat: pond.latitude, lng: pond.longitude },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="32" height="32">
                        <circle cx="12" cy="12" r="10" fill="#10b981" stroke="#ffffff" stroke-width="2"/>
                        <text x="12" y="16" text-anchor="middle" fill="white" font-size="12" font-family="Arial">P</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${pond.name}</h5>
                    <p><strong>Type:</strong> Pond</p>
                    <p><strong>Farm:</strong> ${pond.farm_name || 'Unknown'}</p>
                    <p><strong>Status:</strong> ${pond.status || 'Unknown'}</p>
                    <p><strong>Location:</strong> ${pond.latitude.toFixed(6)}, ${pond.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        pondMarkers.push(marker);
    });
    console.log(`Added ${pondMarkers.length} pond markers`);
}

function addWorkerMarkers() {
    console.log('Adding worker markers...');
    workersData.forEach(worker => {
        const statusColors = {
            'working': '#ef4444',
            'available': '#10b981',
            'on_break': '#f59e0b',
            'traveling': '#8b5cf6',
            'offline': '#6b7280'
        };
        
        const color = statusColors[worker.status] || '#6b7280';
        
        const marker = new google.maps.Marker({
            position: { lat: worker.latitude, lng: worker.longitude },
            map: map,
            title: worker.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
                        <circle cx="16" cy="16" r="14" fill="${color}" stroke="#ffffff" stroke-width="2"/>
                        <circle cx="16" cy="16" r="10" fill="#f8f9fa"/>
                        <circle cx="16" cy="12" r="4" fill="#fdbcb4"/>
                        <circle cx="14" cy="11" r="0.5" fill="#333"/>
                        <circle cx="18" cy="11" r="0.5" fill="#333"/>
                        <circle cx="16" cy="13" r="0.3" fill="#f4a261"/>
                        <path d="M 14.5 14 Q 16 15 17.5 14" stroke="#333" stroke-width="0.5" fill="none"/>
                        <rect x="12" y="18" width="8" height="6" fill="#4a90e2" rx="2"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div>
                    <h5>${worker.name}</h5>
                    <p><strong>Type:</strong> Worker</p>
                    <p><strong>Employee ID:</strong> ${worker.employee_id || 'N/A'}</p>
                    <p><strong>Team:</strong> ${worker.team || 'Unassigned'}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold;">${worker.status}</span></p>
                    <p><strong>Location:</strong> ${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        workerMarkers.push(marker);
    });
    console.log(`Added ${workerMarkers.length} worker markers`);
}

function addGeofenceOverlays() {
    console.log('Adding geofence overlays...');
    geofencesData.forEach(geofence => {
        let overlay;
        
        if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            overlay = new google.maps.Circle({
                strokeColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: geofence.geofence_type === 'restricted' ? '#ef4444' : '#3b82f6',
                fillOpacity: 0.2,
                map: map,
                center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                radius: geofence.radius
            });
        }
        
        if (overlay) {
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h5>${geofence.name}</h5>
                        <p><strong>Type:</strong> ${geofence.geofence_type}</p>
                        <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                        <p><strong>Status:</strong> ${geofence.is_active ? 'Active' : 'Inactive'}</p>
                    </div>
                `
            });
            
            overlay.addListener('click', (event) => {
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
            });
            
            geofenceOverlays.push(overlay);
        }
    });
    console.log(`Added ${geofenceOverlays.length} geofence overlays`);
}

// Toggle functions
function toggleFarms() {
    farmsVisible = !farmsVisible;
    farmMarkers.forEach(marker => {
        marker.setVisible(farmsVisible);
    });
}

function togglePonds() {
    pondsVisible = !pondsVisible;
    pondMarkers.forEach(marker => {
        marker.setVisible(pondsVisible);
    });
}

function toggleWorkers() {
    workersVisible = !workersVisible;
    workerMarkers.forEach(marker => {
        marker.setVisible(workersVisible);
    });
}

function toggleGeofences() {
    geofencesVisible = !geofencesVisible;
    geofenceOverlays.forEach(overlay => {
        overlay.setVisible(geofencesVisible);
    });
}

function centerMap() {
    map.setCenter({ lat: centerLat, lng: centerLng });
    map.setZoom(12);
}

// Handle map loading errors
function handleMapError() {
    console.error('Google Maps failed to load');
    document.getElementById('map').innerHTML = `
        <div style="padding: 50px; text-align: center; color: #ef4444;">
            <h3>Error Loading Map</h3>
            <p>Google Maps failed to load. Please check your API key and internet connection.</p>
        </div>
    `;
}
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
    onerror="handleMapError()">
</script>
{% endblock %}
