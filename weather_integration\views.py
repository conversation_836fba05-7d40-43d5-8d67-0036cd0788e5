from django.shortcuts import render, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Avg, Max, Min
from datetime import datetime, timedelta
import json
import logging

from .models import WeatherStation, WeatherData, WeatherForecast, WeatherAlert, WeatherAutomationRule
from .services.weather_service import WeatherService
from .services.weather_automation_service import WeatherAutomationService
from iot_integration.models import Farm, IoTDevice

logger = logging.getLogger(__name__)


@login_required
def weather_dashboard(request):
    """Main weather dashboard"""
    try:
        # Get user's farms and their weather stations
        user_farms = Farm.objects.filter(owner=request.user)
        weather_stations = WeatherStation.objects.filter(
            latitude__in=[farm.latitude for farm in user_farms if farm.latitude],
            longitude__in=[farm.longitude for farm in user_farms if farm.longitude]
        )
        
        # Get current weather data
        current_weather = []
        for station in weather_stations:
            latest_data = WeatherData.objects.filter(station=station).order_by('-timestamp').first()
            if latest_data:
                current_weather.append({
                    'station': station,
                    'data': latest_data,
                    'age_minutes': (timezone.now() - latest_data.timestamp).total_seconds() / 60
                })
        
        # Get active weather alerts
        active_alerts = WeatherAlert.objects.filter(
            station__in=weather_stations,
            status='active',
            end_time__gt=timezone.now()
        ).order_by('-severity', '-issued_at')[:10]
        
        # Get weather forecast for next 7 days
        weather_forecast = []
        for station in weather_stations:
            forecasts = WeatherForecast.objects.filter(
                station=station,
                forecast_time__gte=timezone.now(),
                forecast_time__lte=timezone.now() + timedelta(days=7)
            ).order_by('forecast_time')[:7]
            
            if forecasts:
                weather_forecast.append({
                    'station': station,
                    'forecasts': forecasts
                })
        
        # Get weather automation summary
        automation_service = WeatherAutomationService()
        automation_summary = automation_service.get_weather_automation_summary(7)
        
        # Get weather statistics for the last 30 days
        weather_stats = self.get_weather_statistics(weather_stations, 30)
        
        context = {
            'weather_stations': weather_stations,
            'current_weather': current_weather,
            'active_alerts': active_alerts,
            'weather_forecast': weather_forecast,
            'automation_summary': automation_summary,
            'weather_stats': weather_stats,
            'user_farms': user_farms,
        }
        
        return render(request, 'weather_integration/dashboard.html', context)
        
    except Exception as e:
        logger.error(f"Weather dashboard error: {e}")
        return render(request, 'weather_integration/dashboard.html', {'error': str(e)})


@login_required
def weather_automation(request):
    """Weather automation management"""
    try:
        # Get user's automation rules
        user_devices = IoTDevice.objects.filter(farm__owner=request.user)
        automation_rules = WeatherAutomationRule.objects.filter(
            Q(target_devices__in=user_devices) | Q(created_by=request.user)
        ).distinct().order_by('-created_at')
        
        # Paginate rules
        paginator = Paginator(automation_rules, 20)
        page_number = request.GET.get('page')
        page_rules = paginator.get_page(page_number)
        
        # Get automation statistics
        automation_stats = {
            'total_rules': automation_rules.count(),
            'active_rules': automation_rules.filter(is_active=True).count(),
            'total_executions': sum(rule.execution_count for rule in automation_rules),
            'success_rate': self.calculate_automation_success_rate(automation_rules),
        }
        
        # Get recent automation logs
        from .models import WeatherAutomationLog
        recent_logs = WeatherAutomationLog.objects.filter(
            rule__in=automation_rules
        ).order_by('-executed_at')[:20]
        
        context = {
            'automation_rules': page_rules,
            'automation_stats': automation_stats,
            'recent_logs': recent_logs,
            'user_devices': user_devices,
        }
        
        return render(request, 'weather_integration/automation.html', context)
        
    except Exception as e:
        logger.error(f"Weather automation error: {e}")
        return render(request, 'weather_integration/automation.html', {'error': str(e)})


@login_required
def weather_alerts(request):
    """Weather alerts management"""
    try:
        # Get user's farms and their weather stations
        user_farms = Farm.objects.filter(owner=request.user)
        weather_stations = WeatherStation.objects.filter(
            latitude__in=[farm.latitude for farm in user_farms if farm.latitude],
            longitude__in=[farm.longitude for farm in user_farms if farm.longitude]
        )
        
        # Get weather alerts
        alerts = WeatherAlert.objects.filter(
            station__in=weather_stations
        ).order_by('-issued_at')
        
        # Filter by status if requested
        status_filter = request.GET.get('status')
        if status_filter:
            alerts = alerts.filter(status=status_filter)
        
        # Filter by severity if requested
        severity_filter = request.GET.get('severity')
        if severity_filter:
            alerts = alerts.filter(severity=severity_filter)
        
        # Paginate alerts
        paginator = Paginator(alerts, 20)
        page_number = request.GET.get('page')
        page_alerts = paginator.get_page(page_number)
        
        # Get alert statistics
        alert_stats = {
            'total_alerts': alerts.count(),
            'active_alerts': alerts.filter(status='active').count(),
            'critical_alerts': alerts.filter(severity='critical').count(),
            'acknowledged_alerts': alerts.filter(status='acknowledged').count(),
        }
        
        context = {
            'alerts': page_alerts,
            'alert_stats': alert_stats,
            'weather_stations': weather_stations,
            'status_filter': status_filter,
            'severity_filter': severity_filter,
        }
        
        return render(request, 'weather_integration/alerts.html', context)
        
    except Exception as e:
        logger.error(f"Weather alerts error: {e}")
        return render(request, 'weather_integration/alerts.html', {'error': str(e)})


@login_required
@require_http_methods(["POST"])
def update_weather_data(request, station_id):
    """Update weather data for a specific station"""
    try:
        station = get_object_or_404(WeatherStation, id=station_id)
        
        # Check if user has access to this station
        user_farms = Farm.objects.filter(owner=request.user)
        if not any(abs(farm.latitude - station.latitude) < 0.1 and 
                  abs(farm.longitude - station.longitude) < 0.1 
                  for farm in user_farms if farm.latitude and farm.longitude):
            return JsonResponse({
                'success': False,
                'error': 'Access denied to this weather station'
            })
        
        # Update weather data
        weather_service = WeatherService()
        result = weather_service.update_weather_station_data(station_id)
        
        if result['success']:
            # Process weather automation rules
            latest_weather = WeatherData.objects.filter(station=station).order_by('-timestamp').first()
            if latest_weather:
                automation_service = WeatherAutomationService()
                automation_result = automation_service.process_weather_automation_rules(latest_weather)
                
                result['automation_result'] = automation_result
        
        return JsonResponse(result)
        
    except Exception as e:
        logger.error(f"Weather data update error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def create_automation_rule(request):
    """Create new weather automation rule"""
    try:
        data = json.loads(request.body)
        
        # Validate required fields
        required_fields = ['name', 'condition_type', 'operator', 'threshold_value', 'action_type']
        for field in required_fields:
            if field not in data:
                return JsonResponse({
                    'success': False,
                    'error': f'Missing required field: {field}'
                })
        
        # Create automation rule
        rule = WeatherAutomationRule.objects.create(
            name=data['name'],
            description=data.get('description', ''),
            condition_type=data['condition_type'],
            operator=data['operator'],
            threshold_value=float(data['threshold_value']),
            threshold_value_max=float(data['threshold_value_max']) if data.get('threshold_value_max') else None,
            action_type=data['action_type'],
            action_parameters=data.get('action_parameters', {}),
            priority=int(data.get('priority', 5)),
            cooldown_period=timedelta(minutes=int(data.get('cooldown_minutes', 30))),
            created_by=request.user
        )
        
        # Add target devices
        if 'target_device_ids' in data:
            target_devices = IoTDevice.objects.filter(
                id__in=data['target_device_ids'],
                farm__owner=request.user
            )
            rule.target_devices.set(target_devices)
        
        # Add target farms
        if 'target_farm_ids' in data:
            target_farms = Farm.objects.filter(
                id__in=data['target_farm_ids'],
                owner=request.user
            )
            rule.target_farms.set(target_farms)
        
        return JsonResponse({
            'success': True,
            'rule_id': rule.id,
            'message': 'Weather automation rule created successfully'
        })
        
    except Exception as e:
        logger.error(f"Create automation rule error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def toggle_automation_rule(request, rule_id):
    """Toggle automation rule active status"""
    try:
        rule = get_object_or_404(WeatherAutomationRule, id=rule_id, created_by=request.user)
        
        rule.is_active = not rule.is_active
        rule.save()
        
        return JsonResponse({
            'success': True,
            'rule_id': rule.id,
            'is_active': rule.is_active,
            'message': f'Rule {"activated" if rule.is_active else "deactivated"} successfully'
        })
        
    except Exception as e:
        logger.error(f"Toggle automation rule error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
@require_http_methods(["POST"])
def acknowledge_weather_alert(request, alert_id):
    """Acknowledge a weather alert"""
    try:
        alert = get_object_or_404(WeatherAlert, id=alert_id)
        
        # Check if user has access to this alert
        user_farms = Farm.objects.filter(owner=request.user)
        if not any(abs(farm.latitude - alert.station.latitude) < 0.1 and 
                  abs(farm.longitude - alert.station.longitude) < 0.1 
                  for farm in user_farms if farm.latitude and farm.longitude):
            return JsonResponse({
                'success': False,
                'error': 'Access denied to this weather alert'
            })
        
        alert.status = 'acknowledged'
        alert.acknowledged_by = request.user
        alert.acknowledged_at = timezone.now()
        alert.save()
        
        return JsonResponse({
            'success': True,
            'alert_id': alert.id,
            'message': 'Weather alert acknowledged successfully'
        })
        
    except Exception as e:
        logger.error(f"Acknowledge weather alert error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def get_weather_forecast_data(request, station_id):
    """Get weather forecast data for charts"""
    try:
        station = get_object_or_404(WeatherStation, id=station_id)
        
        # Get forecast data for next 7 days
        forecasts = WeatherForecast.objects.filter(
            station=station,
            forecast_time__gte=timezone.now(),
            forecast_time__lte=timezone.now() + timedelta(days=7)
        ).order_by('forecast_time')
        
        forecast_data = []
        for forecast in forecasts:
            forecast_data.append({
                'date': forecast.forecast_time.strftime('%Y-%m-%d'),
                'time': forecast.forecast_time.strftime('%H:%M'),
                'temperature_min': forecast.temperature_min,
                'temperature_max': forecast.temperature_max,
                'temperature_avg': forecast.temperature_avg,
                'humidity_avg': forecast.humidity_avg,
                'wind_speed_avg': forecast.wind_speed_avg,
                'precipitation_probability': forecast.precipitation_probability,
                'precipitation_amount': forecast.precipitation_amount,
                'weather_condition': forecast.weather_condition,
            })
        
        return JsonResponse({
            'success': True,
            'station_name': station.name,
            'forecast_data': forecast_data
        })
        
    except Exception as e:
        logger.error(f"Get weather forecast data error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


@login_required
def get_weather_history_data(request, station_id):
    """Get historical weather data for charts"""
    try:
        station = get_object_or_404(WeatherStation, id=station_id)
        
        # Get number of days from request
        days = int(request.GET.get('days', 7))
        
        # Get historical data
        end_time = timezone.now()
        start_time = end_time - timedelta(days=days)
        
        weather_data = WeatherData.objects.filter(
            station=station,
            timestamp__gte=start_time,
            timestamp__lte=end_time
        ).order_by('timestamp')
        
        history_data = []
        for data in weather_data:
            history_data.append({
                'timestamp': data.timestamp.isoformat(),
                'temperature': data.temperature,
                'humidity': data.humidity,
                'pressure': data.pressure,
                'wind_speed': data.wind_speed,
                'wind_direction': data.wind_direction,
                'rainfall': data.rainfall,
                'weather_condition': data.weather_condition,
            })
        
        return JsonResponse({
            'success': True,
            'station_name': station.name,
            'period_days': days,
            'history_data': history_data
        })
        
    except Exception as e:
        logger.error(f"Get weather history data error: {e}")
        return JsonResponse({
            'success': False,
            'error': str(e)
        })


# Helper methods
def get_weather_statistics(self, weather_stations, days: int) -> dict:
    """Get weather statistics for the specified period"""
    try:
        end_time = timezone.now()
        start_time = end_time - timedelta(days=days)
        
        stats = {}
        
        for station in weather_stations:
            weather_data = WeatherData.objects.filter(
                station=station,
                timestamp__gte=start_time,
                timestamp__lte=end_time
            )
            
            if weather_data.exists():
                stats[station.id] = {
                    'station_name': station.name,
                    'avg_temperature': weather_data.aggregate(Avg('temperature'))['temperature__avg'],
                    'max_temperature': weather_data.aggregate(Max('temperature'))['temperature__max'],
                    'min_temperature': weather_data.aggregate(Min('temperature'))['temperature__min'],
                    'avg_humidity': weather_data.aggregate(Avg('humidity'))['humidity__avg'],
                    'max_wind_speed': weather_data.aggregate(Max('wind_speed'))['wind_speed__max'],
                    'total_rainfall': sum(data.rainfall for data in weather_data),
                    'data_points': weather_data.count(),
                }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating weather statistics: {e}")
        return {}


def calculate_automation_success_rate(self, automation_rules) -> float:
    """Calculate automation success rate"""
    try:
        total_executions = sum(rule.execution_count for rule in automation_rules)
        total_successes = sum(rule.success_count for rule in automation_rules)
        
        if total_executions > 0:
            return (total_successes / total_executions) * 100
        else:
            return 0.0
            
    except Exception as e:
        logger.error(f"Error calculating automation success rate: {e}")
        return 0.0
