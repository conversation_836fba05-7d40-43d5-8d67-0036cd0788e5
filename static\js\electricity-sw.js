/**
 * Service Worker for Electricity Monitoring Dashboards
 * Provides offline functionality and caching for mobile and web
 */

const CACHE_NAME = 'electricity-dashboard-v1.2.0';
const STATIC_CACHE = 'static-resources-v1.2.0';
const DATA_CACHE = 'data-cache-v1.2.0';

// URLs to cache for offline functionality
const urlsToCache = [
    '/core/',
    '/electricity/',
    '/electricity/dashboard/',
    '/electricity/devices/',
    '/electricity/alerts/',
    '/electricity/maintenance/',
    '/electricity/bills/',
    '/monitoring/realtime/',
    '/iot/analytics/',
    '/static/css/electricity-dashboard.css',
    '/static/css/electricity-mobile.css',
    '/static/js/electricity-dashboard.js',
    '/static/js/electricity-mobile.js',
    '/static/css/unified-theme.css',
    '/static/css/mobile-responsive.css',
    'https://cdn.jsdelivr.net/npm/chart.js',
    'https://unpkg.com/leaflet@1.7.1/dist/leaflet.css',
    'https://unpkg.com/leaflet@1.7.1/dist/leaflet.js',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

// API endpoints to cache
const apiEndpoints = [
    '/electricity/api/dashboard-data/',
    '/electricity/api/devices/',
    '/api/electricity/power-data/',
    '/api/electricity/voltage-data/',
    '/api/electricity/current-data/',
    '/api/electricity/alerts/',
    '/api/monitoring/realtime-data/',
    '/api/iot/device-status/'
];

// Install event - cache resources
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        Promise.all([
            // Cache static resources
            caches.open(STATIC_CACHE).then(cache => {
                console.log('Caching static resources...');
                return cache.addAll(urlsToCache);
            }),
            
            // Skip waiting to activate immediately
            self.skipWaiting()
        ])
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        Promise.all([
            // Clean up old caches
            caches.keys().then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME && 
                            cacheName !== STATIC_CACHE && 
                            cacheName !== DATA_CACHE) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            }),
            
            // Take control of all clients
            self.clients.claim()
        ])
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle different types of requests
    if (request.method === 'GET') {
        // API requests - cache with network first strategy
        if (isApiRequest(url.pathname)) {
            event.respondWith(handleApiRequest(request));
        }
        // Static resources - cache first strategy
        else if (isStaticResource(url.pathname) || isExternalResource(url.origin)) {
            event.respondWith(handleStaticRequest(request));
        }
        // Dashboard pages - network first with cache fallback
        else if (isDashboardPage(url.pathname)) {
            event.respondWith(handleDashboardRequest(request));
        }
    }
});

// Handle API requests with network first, cache fallback
async function handleApiRequest(request) {
    const cache = await caches.open(DATA_CACHE);
    
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            cache.put(request, networkResponse.clone());
            
            // Send update notification to clients
            notifyClientsOfUpdate(request.url, 'data-updated');
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for API request, trying cache:', request.url);
        
        // Try cache fallback
        const cachedResponse = await cache.match(request);
        if (cachedResponse) {
            // Send offline notification to clients
            notifyClientsOfUpdate(request.url, 'offline-data');
            return cachedResponse;
        }
        
        // Return offline response
        return createOfflineResponse(request);
    }
}

// Handle static resources with cache first strategy
async function handleStaticRequest(request) {
    const cache = await caches.open(STATIC_CACHE);
    
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
        return cachedResponse;
    }
    
    try {
        // Try network if not in cache
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Failed to fetch static resource:', request.url);
        return new Response('Resource not available offline', { status: 503 });
    }
}

// Handle dashboard pages with network first, cache fallback
async function handleDashboardRequest(request) {
    try {
        // Try network first
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // Cache successful responses
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('Network failed for dashboard page, trying cache:', request.url);
        
        // Try cache fallback
        const cache = await caches.open(CACHE_NAME);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        // Return offline page
        return createOfflinePage();
    }
}

// Helper functions
function isApiRequest(pathname) {
    return pathname.startsWith('/api/');
}

function isStaticResource(pathname) {
    return pathname.startsWith('/static/') || 
           pathname.match(/\.(css|js|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf)$/);
}

function isExternalResource(origin) {
    return origin !== self.location.origin;
}

function isDashboardPage(pathname) {
    const dashboardPaths = [
        '/core/',
        '/electricity/',
        '/monitoring/',
        '/iot/'
    ];
    return dashboardPaths.some(path => pathname.startsWith(path));
}

function createOfflineResponse(request) {
    if (request.url.includes('/api/')) {
        // Return cached data or empty response for API requests
        return new Response(JSON.stringify({
            error: 'Offline',
            message: 'No network connection available',
            cached: true,
            timestamp: new Date().toISOString()
        }), {
            status: 503,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache'
            }
        });
    }
    
    return new Response('Service unavailable', { status: 503 });
}

function createOfflinePage() {
    const offlineHtml = `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Offline - Shrimp Farm Guardian</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
                    color: #e2e8f0;
                    margin: 0;
                    padding: 2rem;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    text-align: center;
                }
                .offline-container {
                    max-width: 400px;
                    padding: 2rem;
                    background: rgba(30, 41, 59, 0.8);
                    border-radius: 20px;
                    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                    color: #667eea;
                }
                .offline-title {
                    font-size: 1.5rem;
                    font-weight: 700;
                    margin-bottom: 1rem;
                    color: #e2e8f0;
                }
                .offline-message {
                    color: #94a3b8;
                    margin-bottom: 2rem;
                    line-height: 1.6;
                }
                .retry-btn {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 1.5rem;
                    border-radius: 8px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: transform 0.2s ease;
                }
                .retry-btn:hover {
                    transform: translateY(-1px);
                }
            </style>
        </head>
        <body>
            <div class="offline-container">
                <div class="offline-icon">📡</div>
                <h1 class="offline-title">You're Offline</h1>
                <p class="offline-message">
                    Please check your internet connection and try again. 
                    Some cached data may be available.
                </p>
                <button class="retry-btn" onclick="window.location.reload()">
                    Try Again
                </button>
            </div>
        </body>
        </html>
    `;
    
    return new Response(offlineHtml, {
        headers: {
            'Content-Type': 'text/html',
            'Cache-Control': 'no-cache'
        }
    });
}

// Notify clients of updates
function notifyClientsOfUpdate(url, type) {
    self.clients.matchAll().then(clients => {
        clients.forEach(client => {
            client.postMessage({
                type: 'sw-update',
                updateType: type,
                url: url,
                timestamp: new Date().toISOString()
            });
        });
    });
}

// Background sync for data updates
self.addEventListener('sync', event => {
    if (event.tag === 'electricity-data-sync') {
        event.waitUntil(syncElectricityData());
    }
});

async function syncElectricityData() {
    try {
        // Sync critical electricity data
        const endpoints = [
            '/electricity/api/dashboard-data/',
            '/electricity/api/devices/1/readings/',
            '/api/electricity/power-data/',
            '/api/electricity/alerts/',
            '/api/monitoring/realtime-data/'
        ];
        
        for (const endpoint of endpoints) {
            try {
                const response = await fetch(endpoint);
                if (response.ok) {
                    const cache = await caches.open(DATA_CACHE);
                    await cache.put(endpoint, response.clone());
                }
            } catch (error) {
                console.log('Sync failed for:', endpoint);
            }
        }
        
        notifyClientsOfUpdate('sync', 'background-sync-complete');
    } catch (error) {
        console.log('Background sync failed:', error);
    }
}

// Push notification handling
self.addEventListener('push', event => {
    if (event.data) {
        const data = event.data.json();
        
        const options = {
            body: data.body || 'New electricity monitoring alert',
            icon: '/static/icons/icon-192x192.png',
            badge: '/static/icons/badge-72x72.png',
            vibrate: [100, 50, 100],
            data: {
                url: data.url || '/electricity/dashboard/',
                timestamp: new Date().toISOString()
            },
            actions: [
                {
                    action: 'view',
                    title: 'View Dashboard',
                    icon: '/static/icons/action-view.png'
                },
                {
                    action: 'dismiss',
                    title: 'Dismiss',
                    icon: '/static/icons/action-dismiss.png'
                }
            ],
            requireInteraction: data.priority === 'high',
            silent: data.priority === 'low'
        };
        
        event.waitUntil(
            self.registration.showNotification(data.title || 'Farm Guardian Alert', options)
        );
    }
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'view') {
        const url = event.notification.data.url || '/electricity/dashboard/';
        event.waitUntil(
            clients.openWindow(url)
        );
    }
});

console.log('Electricity Dashboard Service Worker loaded successfully');
