<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 Mobile App ↔ Labor Management Integration Demo</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .demo-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .demo-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .integration-flow {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 30px;
            align-items: center;
            margin-bottom: 40px;
        }

        .mobile-section, .dashboard-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .mobile-icon, .dashboard-icon {
            font-size: 4rem;
            margin-bottom: 15px;
        }

        .mobile-icon { color: #2196F3; }
        .dashboard-icon { color: #4CAF50; }

        .flow-arrow {
            font-size: 3rem;
            color: #FF9800;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.7; }
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: linear-gradient(135deg, #E3F2FD, #BBDEFB);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
            color: #1976D2;
        }

        .feature-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
            color: #333;
        }

        .feature-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .api-demo {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .api-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .api-endpoint {
            background: #2d3748;
            color: #68d391;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin-bottom: 15px;
            overflow-x: auto;
        }

        .api-data {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }

        .demo-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .demo-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #2196F3;
            color: white;
        }

        .btn-success {
            background: #4CAF50;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: rgba(76, 175, 80, 0.1);
            border: 2px solid #4CAF50;
            border-radius: 20px;
            color: #4CAF50;
            font-weight: 600;
            margin: 10px 0;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .architecture-diagram {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            text-align: center;
        }

        .diagram-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .diagram-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .diagram-step {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #2196F3;
        }

        .step-number {
            background: #2196F3;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto 10px;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
        }

        .step-description {
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🔗 Mobile App ↔ Labor Management Integration</h1>
            <p class="demo-subtitle">Real-time GPS tracking and workforce management system</p>
            <div class="status-indicator">
                <div class="status-dot"></div>
                Integration Active - Live Data Flow
            </div>
        </div>

        <div class="integration-flow">
            <div class="mobile-section">
                <div class="mobile-icon">📱</div>
                <h3 class="section-title">Field Worker Mobile App</h3>
                <p>Silent GPS tracking runs in background while workers focus on tasks</p>
            </div>
            
            <div class="flow-arrow">🔄</div>
            
            <div class="dashboard-section">
                <div class="dashboard-icon">💻</div>
                <h3 class="section-title">Labor Management Dashboard</h3>
                <p>Real-time location tracking, heatmaps, and workforce analytics</p>
            </div>
        </div>

        <div class="architecture-diagram">
            <h3 class="diagram-title">📊 Data Flow Architecture</h3>
            <div class="diagram-flow">
                <div class="diagram-step">
                    <div class="step-number">1</div>
                    <div class="step-title">GPS Collection</div>
                    <div class="step-description">Mobile app silently collects GPS coordinates every 30 seconds</div>
                </div>
                <div class="diagram-step">
                    <div class="step-number">2</div>
                    <div class="step-title">API Transmission</div>
                    <div class="step-description">Location data sent to /labor/api/workers/{id}/update-location/</div>
                </div>
                <div class="diagram-step">
                    <div class="step-number">3</div>
                    <div class="step-title">Database Storage</div>
                    <div class="step-description">LocationLog entries created with worker tracking data</div>
                </div>
                <div class="diagram-step">
                    <div class="step-number">4</div>
                    <div class="step-title">Live Dashboard</div>
                    <div class="step-description">Real-time maps, heatmaps, and analytics updated instantly</div>
                </div>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">📍</div>
                <div class="feature-title">Silent GPS Tracking</div>
                <div class="feature-description">Background location collection with 3-8m accuracy, battery optimization, and offline caching</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🗺️</div>
                <div class="feature-title">Live Location Maps</div>
                <div class="feature-description">Real-time worker positions on Google Maps with status indicators and movement trails</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔥</div>
                <div class="feature-title">Movement Heatmaps</div>
                <div class="feature-description">Density visualization showing high-activity areas and worker distribution patterns</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Geofence Alerts</div>
                <div class="feature-description">Automatic notifications when workers enter/exit designated farm areas</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">Productivity Analytics</div>
                <div class="feature-description">Time spent in different zones, task completion rates, and efficiency metrics</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔔</div>
                <div class="feature-title">Safety Monitoring</div>
                <div class="feature-description">Man-down detection, panic alerts, and emergency response coordination</div>
            </div>
        </div>

        <div class="api-demo">
            <h3 class="api-title">🔧 API Integration Example</h3>
            <div class="api-endpoint">
                POST /labor/api/workers/FW001/update-location/
            </div>
            <div class="api-data">
{
  "latitude": 10.790847,
  "longitude": 106.680234,
  "accuracy": 4.2,
  "task_status": "active",
  "battery_level": 85,
  "timestamp": "2025-01-17T14:30:15Z"
}

Response:
{
  "status": "success",
  "message": "Location updated successfully",
  "worker_id": "FW001",
  "location_id": 12345,
  "next_update_in": 30
}
            </div>
        </div>

        <div class="demo-buttons">
            <a href="mobile_demo.html" class="demo-btn btn-primary">
                📱 View Mobile App Demo
            </a>
            <a href="/labor/location-tracking/" class="demo-btn btn-success">
                💻 Open Labor Dashboard
            </a>
            <a href="/labor/dashboard/" class="demo-btn btn-info">
                📊 Main Labor Management
            </a>
        </div>

        <div style="margin-top: 40px; padding: 20px; background: #f8f9fa; border-radius: 15px; text-align: center;">
            <h4 style="color: #333; margin-bottom: 10px;">🎯 Key Benefits</h4>
            <p style="color: #666; line-height: 1.6;">
                <strong>For Workers:</strong> Simple task-focused interface with no GPS distractions<br>
                <strong>For Managers:</strong> Complete workforce visibility with real-time tracking and analytics<br>
                <strong>For Operations:</strong> Improved safety, efficiency, and data-driven decision making
            </p>
        </div>
    </div>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate the flow arrow
            const arrow = document.querySelector('.flow-arrow');
            setInterval(() => {
                arrow.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    arrow.style.transform = 'scale(1)';
                }, 500);
            }, 2000);

            // Simulate live data updates
            const statusDot = document.querySelector('.status-dot');
            setInterval(() => {
                statusDot.style.background = '#FF9800';
                setTimeout(() => {
                    statusDot.style.background = '#4CAF50';
                }, 200);
            }, 3000);
        });
    </script>
</body>
</html>
