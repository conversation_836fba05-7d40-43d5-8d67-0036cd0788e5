{% extends "base.html" %}
{% load static %}

{% block title %}Fresh Cumulative Map Dashboard{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 0;
    }

    /* Force map container size - highest specificity */
    .dashboard-container #map,
    #map,
    div#map {
        width: 100% !important;
        height: 900px !important;
        min-height: 900px !important;
        max-height: none !important;
        display: block !important;
    }

    .dashboard-container {
        min-height: 100vh;
        padding: 20px;
    }

    .header {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
        margin: 0;
        color: #1f2937;
        font-size: 2rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: transform 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
    }

    .stat-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1f2937;
        margin: 10px 0 5px 0;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
    }    .map-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        min-height: 960px; /* 900px map + 60px padding/header */
        height: auto;
    }

    .map-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e5e7eb;
    }

    .map-controls {
        display: flex;
        gap: 10px;
    }

    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #3b82f6;
        color: white;
    }

    .btn-primary:hover {
        background: #2563eb;
    }

    .btn-secondary {
        background: #6b7280;
        color: white;
    }

    .btn-secondary:hover {
        background: #4b5563;
    }    #map {
        width: 100% !important;
        height: 900px !important;
        min-height: 900px !important;
        max-height: none !important;
        border-radius: 10px;
        border: 1px solid #e5e7eb;
    }

    .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 900px;
        color: #6b7280;
        font-size: 1.1rem;
    }

    .error {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 900px;
        color: #ef4444;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 10px;
        text-align: center;
    }

    .legend {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 15px;
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        max-width: 200px;
    }

    .legend-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: #1f2937;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .info-panel {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .status-active { color: #10b981; }
    .status-inactive { color: #ef4444; }
    .status-maintenance { color: #f59e0b; }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container fresh-cumulative">
    <!-- Header -->
    <div class="header">
        <h1>
            <i class="fas fa-map-marked-alt"></i>
            Cumulative Map Dashboard
        </h1>
        <p class="mb-0 text-muted">Comprehensive view of all farms and ponds</p>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-warehouse" style="color: #3b82f6;"></i>
            </div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Total Farms</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-water" style="color: #06b6d4;"></i>
            </div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-play-circle" style="color: #10b981;"></i>
            </div>
            <div class="stat-number">{{ active_ponds }}</div>
            <div class="stat-label">Active Ponds</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-expand-arrows-alt" style="color: #f59e0b;"></i>
            </div>
            <div class="stat-number">{{ total_area|floatformat:1 }}</div>
            <div class="stat-label">Total Area (acres)</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-fan" style="color: #8b5cf6;"></i>
            </div>
            <div class="stat-number">{{ total_aerators }}</div>
            <div class="stat-label">Total Aerators</div>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div class="map-header">
            <h3 style="margin: 0; color: #1f2937;">
                <i class="fas fa-globe-americas"></i>
                Interactive Map
            </h3>
            <div class="map-controls">
                <button class="btn btn-primary" onclick="toggleMapType()">
                    <i class="fas fa-layer-group"></i>
                    Toggle View
                </button>
                <button class="btn btn-secondary" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                    Center Map
                </button>
                <button class="btn btn-secondary" onclick="refreshMap()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Map -->
        <div id="map" class="loading">
            <div>
                <i class="fas fa-spinner fa-spin" style="font-size: 2rem; margin-bottom: 10px;"></i>
                <div>Loading Map...</div>
            </div>
        </div>

        <!-- Map Legend -->
        <div class="legend">
            <div class="legend-title">Legend</div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3b82f6;"></div>
                <span>Farms</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #10b981;"></div>
                <span>Active Ponds</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f59e0b;"></div>
                <span>Maintenance Ponds</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #ef4444;"></div>
                <span>Inactive Ponds</span>
            </div>
        </div>
    </div>

    <!-- Additional Info Panel -->
    <div class="info-panel">
        <h4 style="margin-top: 0;">Quick Statistics</h4>
        <div class="row">
            <div class="col-md-6">
                <p><strong>Farms with Location Data:</strong> {{ farms_with_location }}</p>
                <p><strong>Ponds with Location Data:</strong> {{ ponds_with_location }}</p>
                <p><strong>Active Aerators:</strong> <span class="status-active">{{ active_aerators }}</span></p>
            </div>
            <div class="col-md-6">
                <p><strong>Map Center:</strong> {{ center_lat|floatformat:4 }}, {{ center_lng|floatformat:4 }}</p>
                <p><strong>Last Updated:</strong> <span id="lastUpdated">{{ "now"|date:"M d, Y H:i" }}</span></p>
                <p><strong>Total Workers:</strong> {{ total_workers|default:0 }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables
    let map;
    let markers = [];
    let currentMapType = 'roadmap';
    
    // Map data from Django context
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    const apiKey = '{{ google_maps_api_key }}';
    
    console.log('🗺️ Map initialization starting...');
    console.log('Center coordinates:', centerLat, centerLng);
    console.log('Farms data:', farmsData);
    console.log('Ponds data:', pondsData);    // Initialize map when Google Maps API loads
    function initMap() {
        console.log('🗺️ Google Maps API loaded, initializing map...');
        
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }        // Force map container dimensions
        mapElement.style.height = '500px';
        mapElement.style.width = '100%';
        mapElement.style.minHeight = '500px';
        mapElement.style.display = 'block';
        console.log('📏 Map container size set to:', mapElement.style.height);

        // Check if we have valid coordinates
        if (isNaN(centerLat) || isNaN(centerLng)) {
            console.error('❌ Invalid center coordinates');
            mapElement.innerHTML = '<div class="error"><div><i class="fas fa-exclamation-triangle"></i><br>Invalid map coordinates</div></div>';
            return;
        }

        try {
            // Create the map
            map = new google.maps.Map(mapElement, {
                zoom: 10,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: currentMapType,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    }
                ]
            });            console.log('✅ Map created successfully');
            
            // Force Google Maps to recognize the container size
            setTimeout(() => {
                google.maps.event.trigger(map, 'resize');
                console.log('🔄 Map resize triggered');
            }, 500);
            
            // Add farm markers
            addFarmMarkers();
            
            // Add pond markers
            addPondMarkers();
            
            // Update last updated time
            updateLastUpdatedTime();
            
            console.log('✅ Map initialization complete');
            
        } catch (error) {
            console.error('❌ Error initializing map:', error);
            mapElement.innerHTML = '<div class="error"><div><i class="fas fa-exclamation-triangle"></i><br>Failed to load map: ' + error.message + '</div></div>';
        }
    }

    // Add farm markers to the map
    function addFarmMarkers() {
        if (!farmsData || farmsData.length === 0) {
            console.log('ℹ️ No farms data available');
            return;
        }

        console.log('📍 Adding farm markers:', farmsData.length);
        
        farmsData.forEach(farm => {
            if (farm.latitude && farm.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: farm.latitude, lng: farm.longitude },
                    map: map,
                    title: farm.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="#3b82f6" stroke="white" stroke-width="3"/>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">F</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px; max-width: 250px;">
                            <h5 style="margin: 0 0 10px 0; color: #1f2937;">${farm.name}</h5>
                            <p style="margin: 5px 0;"><strong>Location:</strong> ${farm.location || 'N/A'}</p>
                            <p style="margin: 5px 0;"><strong>Total Ponds:</strong> ${farm.total_ponds || 0}</p>
                            <p style="margin: 5px 0;"><strong>Active Ponds:</strong> ${farm.active_ponds || 0}</p>
                            <p style="margin: 5px 0;"><strong>Total Area:</strong> ${farm.total_area || 0} acres</p>
                            ${farm.contact_person ? `<p style="margin: 5px 0;"><strong>Contact:</strong> ${farm.contact_person}</p>` : ''}
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    // Close any open info windows
                    markers.forEach(m => m.infoWindow && m.infoWindow.close());
                    infoWindow.open(map, marker);
                });

                markers.push({ marker, infoWindow, type: 'farm' });
            }
        });
    }

    // Add pond markers to the map
    function addPondMarkers() {
        if (!pondsData || pondsData.length === 0) {
            console.log('ℹ️ No ponds data available');
            return;
        }

        console.log('📍 Adding pond markers:', pondsData.length);
        
        pondsData.forEach(pond => {
            if (pond.latitude && pond.longitude) {
                // Choose color based on pond status
                let color = '#10b981'; // Default active green
                if (pond.status === 'maintenance') color = '#f59e0b';
                else if (pond.status === 'inactive') color = '#ef4444';

                const marker = new google.maps.Marker({
                    position: { lat: pond.latitude, lng: pond.longitude },
                    map: map,
                    title: pond.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="12.5" cy="12.5" r="10" fill="${color}" stroke="white" stroke-width="2"/>
                                <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">P</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(25, 25)
                    }
                });

                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px; max-width: 250px;">
                            <h5 style="margin: 0 0 10px 0; color: #1f2937;">${pond.name}</h5>
                            <p style="margin: 5px 0;"><strong>Status:</strong> <span class="status-${pond.status}">${pond.status || 'Unknown'}</span></p>
                            <p style="margin: 5px 0;"><strong>Size:</strong> ${pond.size || 'N/A'} acres</p>
                            <p style="margin: 5px 0;"><strong>Depth:</strong> ${pond.depth || 'N/A'} ft</p>
                            <p style="margin: 5px 0;"><strong>Farm:</strong> ${pond.farm_name || 'N/A'}</p>
                            ${pond.aerator_count ? `<p style="margin: 5px 0;"><strong>Aerators:</strong> ${pond.aerator_count}</p>` : ''}
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    // Close any open info windows
                    markers.forEach(m => m.infoWindow && m.infoWindow.close());
                    infoWindow.open(map, marker);
                });

                markers.push({ marker, infoWindow, type: 'pond' });
            }
        });
    }

    // Toggle map type
    function toggleMapType() {
        if (!map) return;
        
        currentMapType = currentMapType === 'roadmap' ? 'satellite' : 'roadmap';
        map.setMapTypeId(currentMapType);
        console.log('🗺️ Map type changed to:', currentMapType);
    }

    // Center the map
    function centerMap() {
        if (!map) return;
        
        map.setCenter({ lat: centerLat, lng: centerLng });
        map.setZoom(10);
        console.log('🎯 Map centered');
    }

    // Refresh the map
    function refreshMap() {
        console.log('🔄 Refreshing map...');
        
        // Clear existing markers
        markers.forEach(item => {
            item.marker.setMap(null);
            if (item.infoWindow) item.infoWindow.close();
        });
        markers = [];
        
        // Re-add markers
        addFarmMarkers();
        addPondMarkers();
        
        // Update timestamp
        updateLastUpdatedTime();
        
        console.log('✅ Map refreshed');
    }

    // Update last updated time
    function updateLastUpdatedTime() {
        const now = new Date();
        const timeString = now.toLocaleString();
        const element = document.getElementById('lastUpdated');
        if (element) {
            element.textContent = timeString;
        }
    }

    // Handle map loading errors
    function handleMapError() {
        console.error('❌ Failed to load Google Maps');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.innerHTML = `
                <div class="error">
                    <div>
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Failed to load Google Maps<br>
                        <small>Please check your internet connection and refresh the page</small>
                    </div>
                </div>            `;
        }
    }

    // Force map container size on page load    document.addEventListener('DOMContentLoaded', function() {        console.log('🔧 DOM loaded - forcing map container size...');
        const mapElement = document.getElementById('map');
        
        if (mapElement) {
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '500px';
            mapElement.style.display = 'block';
            console.log('📏 Map container dimensions forced to 500px height');
        }
    });

    // Additional resize trigger when window loads    window.addEventListener('load', function() {
        console.log('🔧 Window loaded - additional map size enforcement...');        const mapElement = document.getElementById('map');
        
        if (mapElement) {
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '500px';
            console.log('📏 Map container size re-enforced on window load');
            
            // Trigger map resize if map exists
            if (typeof map !== 'undefined' && map) {
                setTimeout(() => {
                    google.maps.event.trigger(map, 'resize');
                    console.log('🔄 Map resize event triggered after window load');
                }, 1000);
            }
        }
    });
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
