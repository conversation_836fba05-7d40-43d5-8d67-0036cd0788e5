/**
 * Mobile-Optimized Electricity Dashboard CSS
 * Enhanced responsive design for mobile and web platforms
 */

/* Mobile-First Approach - Base Mobile Styles */
.electricity-dashboard {
    padding: 0.5rem;
    min-height: 100vh;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
}

/* Mobile Navigation */
.mobile-nav {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
    padding: 0.75rem;
}

.mobile-nav-brand {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Mobile Grid System */
.mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
    padding: 0.5rem;
}

/* Mobile Card Styles */
.mobile-card {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(74, 85, 104, 0.3);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    color: #e2e8f0;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.mobile-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mobile-card-icon {
    width: 20px;
    height: 20px;
    color: #667eea;
}

/* Mobile Statistics */
.mobile-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.mobile-stat-item {
    background: rgba(15, 23, 42, 0.6);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
    border: 1px solid rgba(74, 85, 104, 0.2);
}

.mobile-stat-value {
    font-size: 1.2rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 0.25rem;
}

.mobile-stat-label {
    font-size: 0.7rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.mobile-stat-change {
    font-size: 0.65rem;
    margin-top: 0.25rem;
    font-weight: 500;
}

.mobile-stat-change.positive {
    color: #10b981;
}

.mobile-stat-change.negative {
    color: #ef4444;
}

/* Mobile 3-Phase Display */
.mobile-phase-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin: 0.75rem 0;
}

.mobile-phase-item {
    background: rgba(15, 23, 42, 0.6);
    border-radius: 8px;
    padding: 0.6rem;
    text-align: center;
    border: 1px solid rgba(74, 85, 104, 0.2);
}

.mobile-phase-label {
    font-size: 0.65rem;
    color: #94a3b8;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.mobile-phase-value {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
}

.mobile-phase-unit {
    font-size: 0.6rem;
    color: #94a3b8;
    margin-left: 0.2rem;
}

/* Mobile Charts Container */
.mobile-chart-container {
    position: relative;
    height: 200px;
    margin: 0.75rem 0;
    background: rgba(15, 23, 42, 0.4);
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid rgba(74, 85, 104, 0.2);
}

.mobile-chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #94a3b8;
    font-size: 0.8rem;
    text-align: center;
}

/* Mobile Equipment Status */
.mobile-equipment-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-equipment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.6rem;
    background: rgba(15, 23, 42, 0.6);
    border-radius: 8px;
    border: 1px solid rgba(74, 85, 104, 0.2);
}

.mobile-equipment-name {
    font-size: 0.8rem;
    color: #e2e8f0;
    font-weight: 500;
}

.mobile-equipment-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.mobile-equipment-status.online {
    background: #10b981;
    color: white;
}

.mobile-equipment-status.offline {
    background: #ef4444;
    color: white;
}

.mobile-equipment-status.maintenance {
    background: #f59e0b;
    color: white;
}

/* Mobile Buttons */
.mobile-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.mobile-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.mobile-btn:active {
    transform: translateY(1px);
}

.mobile-btn-secondary {
    background: rgba(74, 85, 104, 0.6);
    border: 1px solid rgba(74, 85, 104, 0.8);
}

.mobile-btn-secondary:hover {
    background: rgba(74, 85, 104, 0.8);
}

/* Mobile Notification System */
.mobile-notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%) translateY(-100px);
    z-index: 9999;
    max-width: 90vw;
    width: auto;
    min-width: 300px;
    background: rgba(30, 41, 59, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    opacity: 0;
}

.mobile-notification.show {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

.notification-info {
    border-left: 4px solid #667eea;
}

.notification-close {
    background: none;
    border: none;
    color: #94a3b8;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    margin-left: auto;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: #e2e8f0;
}

/* Mobile Install Button */
.mobile-install-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-left: auto;
    transition: all 0.3s ease;
}

.mobile-install-btn:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
}

/* Enhanced Chart Containers */
.mobile-chart-container {
    position: relative;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    overflow: hidden;
}

.mobile-chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(74, 85, 104, 0.3);
}

.mobile-chart-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #e2e8f0;
}

.mobile-chart-controls {
    display: flex;
    gap: 0.5rem;
}

.mobile-chart-btn {
    background: rgba(102, 126, 234, 0.2);
    border: 1px solid rgba(102, 126, 234, 0.3);
    color: #a5b4fc;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-chart-btn:hover,
.mobile-chart-btn.active {
    background: rgba(102, 126, 234, 0.4);
    color: #c7d2fe;
}

/* Mobile Action Bar */
.mobile-action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(74, 85, 104, 0.3);
    padding: 0.75rem 1rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.mobile-action-bar.show {
    transform: translateY(0);
}

.mobile-action-btn {
    background: none;
    border: none;
    color: #94a3b8;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem;
    border-radius: 8px;
    font-size: 0.7rem;
    transition: all 0.2s ease;
    min-width: 60px;
}

.mobile-action-btn:hover,
.mobile-action-btn.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.mobile-action-btn i {
    font-size: 1.2rem;
}

/* Phase Monitoring Mobile Enhancements */
.mobile-phase-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

@media (min-width: 480px) {
    .mobile-phase-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .mobile-phase-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.mobile-phase-card {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.mobile-phase-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.mobile-phase-label {
    font-size: 0.75rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
}

.mobile-phase-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: #e2e8f0;
    margin-bottom: 0.25rem;
}

.mobile-phase-unit {
    font-size: 0.8rem;
    color: #64748b;
    margin-bottom: 0.5rem;
}

.mobile-phase-status {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: rgba(16, 185, 129, 0.2);
    color: #34d399;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Mobile Quick Stats */
.mobile-quick-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.mobile-quick-stat {
    background: rgba(15, 23, 42, 0.6);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
    border: 1px solid rgba(74, 85, 104, 0.2);
}

.mobile-quick-stat-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: #e2e8f0;
    margin-bottom: 0.25rem;
}

.mobile-quick-stat-label {
    font-size: 0.65rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Mobile Loading States */
.mobile-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: #94a3b8;
    font-size: 0.9rem;
}

.mobile-loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

/* Error States */
.mobile-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    color: #fca5a5;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.mobile-error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #ef4444;
}

.mobile-retry-btn {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.5);
    color: #fca5a5;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.8rem;
    margin-top: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mobile-retry-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    color: #fee2e2;
}

/* Smooth Scrolling */
@media (max-width: 768px) {
    html {
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }
    
    body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
    }
}

/* Safe Area Insets for iPhone X and newer */
@supports (padding-top: env(safe-area-inset-top)) {
    .mobile-nav {
        padding-top: calc(0.75rem + env(safe-area-inset-top));
        padding-left: calc(0.75rem + env(safe-area-inset-left));
        padding-right: calc(0.75rem + env(safe-area-inset-right));
    }
    
    .mobile-action-bar {
        padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
        padding-left: calc(1rem + env(safe-area-inset-left));
        padding-right: calc(1rem + env(safe-area-inset-right));
    }
    
    .electricity-dashboard {
        padding-bottom: calc(5rem + env(safe-area-inset-bottom));
    }
}

/* Comprehensive Mobile & Web Responsive Enhancements */

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .mobile-card-icon,
    .stat-icon,
    .phase-card-icon {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
    
    .mobile-chart-container canvas {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Web Desktop Responsive Styles */
@media (min-width: 769px) {
    .electricity-dashboard {
        padding: 2rem;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
    }
    
    .power-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .equipment-status-summary {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Web-specific hover effects */
    .modern-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }
    
    .power-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 30px rgba(102, 126, 234, 0.3);
    }
}

/* Tablet Landscape Orientation */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
    
    .power-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .equipment-status-summary {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* Tablet Portrait */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .power-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .equipment-status-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Ultra-wide Desktop */
@media (min-width: 1400px) {
    .electricity-dashboard {
        max-width: 1600px;
        margin: 0 auto;
        padding: 2rem 3rem;
    }
    
    .dashboard-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
    
    .equipment-status-summary {
        grid-template-columns: repeat(6, 1fr);
    }
}

/* Enhanced Touch Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .modern-card:hover,
    .power-card:hover,
    .stat-card:hover {
        transform: none;
        box-shadow: initial;
    }
    
    /* Larger touch targets */
    .btn,
    .form-select,
    .mobile-action-btn {
        min-height: 48px;
        min-width: 48px;
        padding: 1rem 1.5rem;
        border-radius: 12px;
    }
    
    /* Touch-friendly switches */
    .switch {
        min-height: 32px;
        min-width: 60px;
    }
    
    /* Enhanced tap feedback */
    .mobile-card:active,
    .power-card:active,
    .btn:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    .mobile-card,
    .power-card,
    .stat-card,
    .phase-card {
        transition: none;
        animation: none;
    }
    
    .live-dot {
        animation: none;
    }
    
    .chart-container canvas {
        animation: none;
    }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
    .electricity-dashboard {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    }
    
    .mobile-card {
        background: rgba(15, 23, 42, 0.95);
        border-color: rgba(51, 65, 85, 0.5);
    }
    
    .mobile-stat-item {
        background: rgba(0, 0, 0, 0.8);
    }
}

/* Print Styles */
@media print {
    .electricity-dashboard {
        background: white !important;
        color: black !important;
        padding: 1rem !important;
    }
    
    .mobile-nav,
    .mobile-action-bar,
    .btn,
    .form-select {
        display: none !important;
    }
    
    .mobile-card {
        background: white !important;
        border: 1px solid black !important;
        color: black !important;
        box-shadow: none !important;
        page-break-inside: avoid;
    }
    
    .chart-container {
        height: 200px !important;
    }
}

/* Performance Optimizations for Low-End Devices */
@media (max-width: 480px) and (max-device-width: 480px) {
    /* Reduce backdrop filters on low-end devices */
    .mobile-card,
    .power-card {
        backdrop-filter: none;
        -webkit-backdrop-filter: none;
    }
    
    /* Simplified animations */
    .live-dot {
        animation-duration: 3s;
    }
    
    /* Smaller chart canvas for performance */
    .chart-container {
        height: 180px;
    }
    
    /* Reduce box shadows for performance */
    .mobile-card {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
}

/* Progressive Enhancement for Modern Browsers */
@supports (display: grid) and (grid-template-columns: subgrid) {
    .dashboard-grid {
        grid-template-columns: subgrid;
    }
}

@supports (backdrop-filter: blur(20px)) {
    .mobile-nav {
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }
}

@supports (color: color(display-p3 1 0 0)) {
    /* Enhanced colors for wide gamut displays */
    .phase-card {
        background: linear-gradient(135deg, 
            color(display-p3 0.9 0.3 0.3) 0%, 
            color(display-p3 0.8 0.2 0.2) 100%);
    }
}

/* Enhanced Landscape Mobile */
@media (max-height: 500px) and (orientation: landscape) {
    .mobile-nav {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
    
    .mobile-card {
        padding: 0.75rem;
        margin-bottom: 0.5rem;
    }
    
    .mobile-stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.25rem;
    }
    
    .chart-container {
        height: 150px;
    }
}

/* Focus Styles for Keyboard Navigation */
.mobile-card:focus-within,
.power-card:focus-within,
.btn:focus,
.form-select:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

/* Loading States */
.mobile-card.loading {
    opacity: 0.7;
    pointer-events: none;
}

.mobile-card.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #667eea;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
