"""
Enhanced middleware for the Shrimp Farm Guardian application.
"""

import logging
import time
from django.http import HttpResponseForbidden, JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.conf import settings
from django.contrib.auth.models import AnonymousUser

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    Enhanced middleware to add security headers to all responses.
    """

    def process_response(self, request, response):
        # Add security headers
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # Add CSP header for enhanced security
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self' https://maps.googleapis.com; "
            "frame-src 'none';"
        )
        response['Content-Security-Policy'] = csp_policy

        # Add HSTS header for HTTPS enforcement (only in production)
        if not settings.DEBUG and request.is_secure():
            response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

        return response

class RequestLoggingMiddleware:
    """
    Enhanced request logging middleware with security monitoring
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger(__name__)

    def __call__(self, request):
        # Store request start time
        start_time = time.time()

        # Log request
        self.logger.info(f"Request received: {request.method} {request.path}")

        # Check for suspicious patterns
        self._check_suspicious_patterns(request)

        response = self.get_response(request)

        # Calculate response time
        response_time = time.time() - start_time

        # Log response with timing
        if hasattr(response, 'status_code'):
            self.logger.info(f"Response sent: {response.status_code} ({response_time:.3f}s)")

            # Log slow requests
            if response_time > 2.0:
                self.logger.warning(f"Slow request: {request.path} took {response_time:.2f}s")
        else:
            self.logger.warning(f"Response object type: {type(response)} - no status_code attribute")

        return response

    def _check_suspicious_patterns(self, request):
        """Check for suspicious request patterns"""
        client_ip = self._get_client_ip(request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

        # Check for common attack patterns
        suspicious_patterns = [
            'sqlmap', 'nikto', 'nmap', 'masscan', 'zap',
            'burp', 'acunetix', 'nessus', 'openvas'
        ]

        if any(pattern in user_agent.lower() for pattern in suspicious_patterns):
            self.logger.warning(f"Suspicious user agent from {client_ip}: {user_agent}")

    def _get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
