{% extends "base.html" %}
{% load static %}

{% block title %}All Aerators Management - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .aerator-card {
        border: 1px solid #e5e5e5;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }
    
    .aerator-card:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }
    
    .status-badge {
        font-size: 0.8em;
        padding: 4px 8px;
        border-radius: 12px;
        font-weight: bold;
        text-transform: uppercase;
    }
    
    .status-active { background: #10b981; color: white; }
    .status-inactive { background: #ef4444; color: white; }
    .status-maintenance { background: #f59e0b; color: white; }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .action-buttons {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
    }
    
    .btn-sm {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-fan me-2"></i>All Aerators Management</h1>
                <div>
                    <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="btn btn-primary me-2">
                        <i class="fas fa-map-marked-alt"></i> View on Map
                    </a>
                    <a href="{% url 'iot_integration:dashboard' %}" class="btn btn-success">
                        <i class="fas fa-satellite-dish"></i> IoT Control
                    </a>
                </div>
            </div>

            <!-- Statistics Section -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ stats.total }}</div>
                    <div>Total Aerators</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.active }}</div>
                    <div>Active</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.inactive }}</div>
                    <div>Inactive</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.maintenance }}</div>
                    <div>Maintenance</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ stats.total_power|floatformat:1 }}</div>
                    <div>Total Power (HP)</div>
                </div>
            </div>

            <!-- Filters Section -->
            <div class="filter-section">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Search aerator name...">
                    </div>
                    <div class="col-md-3">
                        <label for="pond" class="form-label">Pond</label>
                        <select class="form-select" id="pond" name="pond">
                            <option value="">All Ponds</option>
                            {% for pond in ponds %}
                            <option value="{{ pond.id }}" {% if pond_filter == pond.id|stringformat:"s" %}selected{% endif %}>
                                {{ pond.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="active" {% if status_filter == "active" %}selected{% endif %}>Active</option>
                            <option value="inactive" {% if status_filter == "inactive" %}selected{% endif %}>Inactive</option>
                            <option value="maintenance" {% if status_filter == "maintenance" %}selected{% endif %}>Maintenance</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search"></i> Filter
                            </button>
                            <a href="{% url 'ponds:all_aerators_management' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Aerators List -->
            <div class="row">
                {% for aerator in aerators %}
                <div class="col-lg-6 col-xl-4">
                    <div class="aerator-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <h5 class="mb-0">{{ aerator.name }}</h5>
                            <span class="status-badge status-{{ aerator.status }}">
                                {{ aerator.get_status_display }}
                            </span>
                        </div>
                        
                        <div class="mb-3">
                            <div><strong>Pond:</strong> 
                                <a href="{% url 'ponds:pond_detail' aerator.pond.id %}">{{ aerator.pond.name }}</a>
                            </div>
                            {% if aerator.pond.farm %}
                            <div><strong>Farm:</strong> {{ aerator.pond.farm.name }}</div>
                            {% endif %}
                            <div><strong>Type:</strong> {{ aerator.get_aerator_type_display }}</div>
                            {% if aerator.power_rating %}
                            <div><strong>Power:</strong> {{ aerator.power_rating }} HP</div>
                            {% endif %}
                            {% if aerator.device_id %}
                            <div><strong>Device ID:</strong> <code>{{ aerator.device_id }}</code></div>
                            {% endif %}
                        </div>
                        
                        {% if aerator.latitude and aerator.longitude %}
                        <div class="mb-3 text-muted">
                            <i class="fas fa-map-pin"></i> 
                            {{ aerator.latitude|floatformat:6 }}, {{ aerator.longitude|floatformat:6 }}
                        </div>
                        {% endif %}
                        
                        <div class="action-buttons">
                            <a href="{% url 'ponds:aerator_management' aerator.pond.id %}" 
                               class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{% url 'ponds:edit_aerator' aerator.pond.id aerator.id %}" 
                               class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-edit"></i> Edit
                            </a>
                            {% if aerator.device_id %}
                            <button class="btn btn-sm btn-outline-success" 
                                    onclick="controlAerator('{{ aerator.device_id }}', 'toggle')">
                                <i class="fas fa-power-off"></i> Control
                            </button>
                            {% endif %}
                            {% if aerator.latitude and aerator.longitude %}
                            <button class="btn btn-sm btn-outline-info" 
                                    onclick="showOnMap({{ aerator.latitude }}, {{ aerator.longitude }})">
                                <i class="fas fa-map-marker-alt"></i> Map
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-fan fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Aerators Found</h4>
                        <p class="text-muted">No aerators match your current filters.</p>
                        <a href="{% url 'ponds:pond_list' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Aerators to Ponds
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Modal for IoT Control -->
<div class="modal fade" id="aeratorControlModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Aerator Control</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Aerator control functionality requires IoT integration.</p>
                <p>Visit the <strong>IoT Control Center</strong> for real-time aerator control.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="{% url 'iot_integration:dashboard' %}" class="btn btn-primary">
                    Go to IoT Control Center
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function controlAerator(deviceId, action) {
    // Show modal for now - in production this would make API calls
    const modal = new bootstrap.Modal(document.getElementById('aeratorControlModal'));
    modal.show();
}

function showOnMap(lat, lng) {
    // Open the multi-pond aerator map centered on this aerator
    const mapUrl = `{% url 'ponds:multi_pond_aerator_map' %}?center=${lat},${lng}&zoom=18`;
    window.open(mapUrl, '_blank');
}

// Auto-submit form on dropdown changes
document.getElementById('pond').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
