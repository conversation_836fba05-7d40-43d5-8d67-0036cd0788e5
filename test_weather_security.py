#!/usr/bin/env python3
"""
Security validation test for weather tasks
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_validation_functions():
    """Test the security validation functions"""
    
    # Test coordinate validation
    def validate_coordinates(latitude, longitude):
        try:
            lat = float(latitude)
            lon = float(longitude)
            
            if not (-90 <= lat <= 90):
                return False
            if not (-180 <= lon <= 180):
                return False
                
            return True
        except (ValueError, TypeError):
            return False
    
    # Test sanitization functions
    def sanitize_pond_name(name):
        if not isinstance(name, str):
            return "Unknown Pond"
        
        import re
        sanitized = re.sub(r'[<>"\'\\\0\n\r\t]', '', name)
        
        if len(sanitized) > 100:
            sanitized = sanitized[:100] + "..."
        
        return sanitized or "Unknown Pond"
    
    def validate_email_address(email):
        import re
        
        if not isinstance(email, str) or len(email) > 254:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def validate_phone_number(phone):
        import re
        
        if not isinstance(phone, str):
            return False
        
        cleaned = re.sub(r'[\s\-\(\)\+]', '', phone)
        
        if not cleaned.isdigit() or len(cleaned) < 10 or len(cleaned) > 15:
            return False
        
        return True
    
    # Run tests
    tests_passed = 0
    total_tests = 0
    
    # Test coordinate validation
    total_tests += 5
    if validate_coordinates(45.0, -122.0):  # Valid coordinates
        tests_passed += 1
    if not validate_coordinates(100, 0):    # Invalid latitude
        tests_passed += 1
    if not validate_coordinates(0, 200):    # Invalid longitude
        tests_passed += 1
    if not validate_coordinates("invalid", 0):  # Invalid type
        tests_passed += 1
    if not validate_coordinates(None, None):    # None values
        tests_passed += 1
      # Test pond name sanitization
    total_tests += 4
    if sanitize_pond_name("Valid Pond Name") == "Valid Pond Name":
        tests_passed += 1
    # The script tag should be removed, leaving 'Pondalert(xss)'
    sanitized = sanitize_pond_name("Pond<script>alert('xss')</script>")
    if "script" not in sanitized and "Pond" in sanitized:
        tests_passed += 1
    if sanitize_pond_name("") == "Unknown Pond":
        tests_passed += 1
    if sanitize_pond_name(123) == "Unknown Pond":
        tests_passed += 1
    
    # Test email validation
    total_tests += 4
    if validate_email_address("<EMAIL>"):
        tests_passed += 1
    if not validate_email_address("invalid-email"):
        tests_passed += 1
    if not validate_email_address(""):
        tests_passed += 1
    if not validate_email_address("a" * 300 + "@example.com"):  # Too long
        tests_passed += 1
    
    # Test phone validation
    total_tests += 4
    if validate_phone_number("1234567890"):
        tests_passed += 1
    if validate_phone_number("******-567-8900"):
        tests_passed += 1
    if not validate_phone_number("123"):  # Too short
        tests_passed += 1
    if not validate_phone_number("abc1234567890"):  # Contains letters
        tests_passed += 1
    
    print(f"Security validation tests: {tests_passed}/{total_tests} passed")
    return tests_passed == total_tests

def test_weather_response_validation():
    """Test weather API response validation"""
    
    def validate_weather_response(data):
        if not isinstance(data, dict):
            return False
        
        required_fields = ['main', 'weather', 'wind']
        for field in required_fields:
            if field not in data:
                return False
        
        if not isinstance(data['main'], dict):
            return False
        
        main_required = ['temp', 'humidity', 'pressure']
        for field in main_required:
            if field not in data['main']:
                return False
            if not isinstance(data['main'][field], (int, float)):
                return False
        
        if not isinstance(data['weather'], list) or len(data['weather']) == 0:
            return False
        
        weather_required = ['main', 'description']
        for field in weather_required:
            if field not in data['weather'][0]:
                return False
            if not isinstance(data['weather'][0][field], str):
                return False
        
        if not isinstance(data['wind'], dict):
            return False
        
        if 'speed' not in data['wind'] or not isinstance(data['wind']['speed'], (int, float)):
            return False
        
        return True
    
    # Test valid response
    valid_response = {
        'main': {
            'temp': 25.5,
            'humidity': 60,
            'pressure': 1013
        },
        'weather': [
            {
                'main': 'Clear',
                'description': 'clear sky'
            }
        ],
        'wind': {
            'speed': 3.5
        }
    }
    
    # Test invalid responses
    invalid_responses = [
        {},  # Empty dict
        {'main': {}},  # Missing fields
        {'main': {'temp': '25'}, 'weather': [], 'wind': {'speed': 3}},  # Wrong types
        {'main': {'temp': 25, 'humidity': 60, 'pressure': 1013}, 'weather': [], 'wind': {'speed': 3}},  # Empty weather
    ]
    
    tests_passed = 0
    total_tests = 5
    
    if validate_weather_response(valid_response):
        tests_passed += 1
    
    for invalid_response in invalid_responses:
        if not validate_weather_response(invalid_response):
            tests_passed += 1
    
    print(f"Weather response validation tests: {tests_passed}/{total_tests} passed")
    return tests_passed == total_tests

def main():
    """Run all security tests"""
    print("Running security validation tests for weather tasks...")
    print("=" * 60)
    
    all_passed = True
    
    # Run validation function tests
    if not test_validation_functions():
        all_passed = False
    
    # Run weather response validation tests
    if not test_weather_response_validation():
        all_passed = False
    
    print("=" * 60)
    if all_passed:
        print("✅ All security validation tests PASSED!")
        print("The weather tasks security fixes are working correctly.")
    else:
        print("❌ Some security validation tests FAILED!")
        print("Please review the security fixes implementation.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
