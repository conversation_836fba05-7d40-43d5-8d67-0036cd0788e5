import requests
import json
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
import logging
from typing import Dict, List, Optional, Tuple

from ..models import WeatherStation, WeatherData, WeatherForecast, WeatherAlert

logger = logging.getLogger(__name__)


class WeatherService:
    """Advanced weather service with multiple API providers"""
    
    def __init__(self):
        self.api_providers = {
            'openweathermap': OpenWeatherMapProvider(),
            'weatherapi': WeatherAPIProvider(),
            'accuweather': AccuWeatherProvider(),
            'meteostat': MeteostatProvider(),
        }
        self.default_provider = 'openweathermap'
    
    def get_current_weather(self, latitude: float, longitude: float, provider: str = None) -> Dict:
        """Get current weather data"""
        try:
            provider = provider or self.default_provider
            
            if provider not in self.api_providers:
                raise ValueError(f"Unknown weather provider: {provider}")
            
            api_provider = self.api_providers[provider]
            weather_data = api_provider.get_current_weather(latitude, longitude)
            
            # Standardize the data format
            standardized_data = self.standardize_weather_data(weather_data, provider)
            
            return {
                'success': True,
                'data': standardized_data,
                'provider': provider,
                'timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting current weather: {e}")
            return {
                'success': False,
                'error': str(e),
                'provider': provider
            }
    
    def get_weather_forecast(self, latitude: float, longitude: float, days: int = 7, provider: str = None) -> Dict:
        """Get weather forecast"""
        try:
            provider = provider or self.default_provider
            
            if provider not in self.api_providers:
                raise ValueError(f"Unknown weather provider: {provider}")
            
            api_provider = self.api_providers[provider]
            forecast_data = api_provider.get_forecast(latitude, longitude, days)
            
            # Standardize the forecast data
            standardized_forecast = self.standardize_forecast_data(forecast_data, provider)
            
            return {
                'success': True,
                'data': standardized_forecast,
                'provider': provider,
                'forecast_days': days,
                'timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting weather forecast: {e}")
            return {
                'success': False,
                'error': str(e),
                'provider': provider
            }
    
    def get_weather_alerts(self, latitude: float, longitude: float, provider: str = None) -> Dict:
        """Get weather alerts and warnings"""
        try:
            provider = provider or self.default_provider
            
            if provider not in self.api_providers:
                raise ValueError(f"Unknown weather provider: {provider}")
            
            api_provider = self.api_providers[provider]
            alerts_data = api_provider.get_alerts(latitude, longitude)
            
            # Standardize the alerts data
            standardized_alerts = self.standardize_alerts_data(alerts_data, provider)
            
            return {
                'success': True,
                'data': standardized_alerts,
                'provider': provider,
                'timestamp': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting weather alerts: {e}")
            return {
                'success': False,
                'error': str(e),
                'provider': provider
            }
    
    def update_weather_station_data(self, station_id: int) -> Dict:
        """Update weather data for a specific station"""
        try:
            station = WeatherStation.objects.get(id=station_id)
            
            # Get current weather
            current_weather = self.get_current_weather(
                station.latitude, 
                station.longitude, 
                station.api_provider or self.default_provider
            )
            
            if current_weather['success']:
                # Store current weather data
                weather_data = WeatherData.objects.create(
                    station=station,
                    temperature=current_weather['data']['temperature'],
                    humidity=current_weather['data']['humidity'],
                    pressure=current_weather['data']['pressure'],
                    wind_speed=current_weather['data']['wind_speed'],
                    wind_direction=current_weather['data']['wind_direction'],
                    rainfall=current_weather['data'].get('rainfall', 0),
                    solar_radiation=current_weather['data'].get('solar_radiation'),
                    uv_index=current_weather['data'].get('uv_index'),
                    weather_condition=current_weather['data'].get('condition', ''),
                    visibility=current_weather['data'].get('visibility'),
                    cloud_cover=current_weather['data'].get('cloud_cover'),
                    data_source=current_weather['provider'],
                    quality_score=current_weather['data'].get('quality_score', 1.0)
                )
                
                # Update station last update time
                station.last_update = timezone.now()
                station.save()
                
                # Get and store forecast
                forecast = self.get_weather_forecast(
                    station.latitude, 
                    station.longitude, 
                    7, 
                    station.api_provider or self.default_provider
                )
                
                if forecast['success']:
                    self.store_forecast_data(station, forecast['data'])
                
                # Check for weather alerts
                alerts = self.get_weather_alerts(
                    station.latitude, 
                    station.longitude, 
                    station.api_provider or self.default_provider
                )
                
                if alerts['success']:
                    self.store_weather_alerts(station, alerts['data'])
                
                return {
                    'success': True,
                    'weather_data_id': weather_data.id,
                    'station': station.name,
                    'timestamp': timezone.now().isoformat()
                }
            else:
                return current_weather
                
        except WeatherStation.DoesNotExist:
            return {
                'success': False,
                'error': f"Weather station {station_id} not found"
            }
        except Exception as e:
            logger.error(f"Error updating weather station data: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def standardize_weather_data(self, data: Dict, provider: str) -> Dict:
        """Standardize weather data from different providers"""
        standardized = {
            'temperature': 0,
            'humidity': 0,
            'pressure': 0,
            'wind_speed': 0,
            'wind_direction': 0,
            'rainfall': 0,
            'quality_score': 1.0
        }
        
        if provider == 'openweathermap':
            standardized.update({
                'temperature': data.get('main', {}).get('temp', 0) - 273.15,  # Convert K to C
                'humidity': data.get('main', {}).get('humidity', 0),
                'pressure': data.get('main', {}).get('pressure', 0),
                'wind_speed': data.get('wind', {}).get('speed', 0),
                'wind_direction': data.get('wind', {}).get('deg', 0),
                'rainfall': data.get('rain', {}).get('1h', 0),
                'condition': data.get('weather', [{}])[0].get('description', ''),
                'visibility': data.get('visibility', 0) / 1000,  # Convert m to km
                'cloud_cover': data.get('clouds', {}).get('all', 0),
                'uv_index': data.get('uvi', None),
            })
        elif provider == 'weatherapi':
            current = data.get('current', {})
            standardized.update({
                'temperature': current.get('temp_c', 0),
                'humidity': current.get('humidity', 0),
                'pressure': current.get('pressure_mb', 0),
                'wind_speed': current.get('wind_kph', 0) / 3.6,  # Convert kph to m/s
                'wind_direction': current.get('wind_degree', 0),
                'rainfall': current.get('precip_mm', 0),
                'condition': current.get('condition', {}).get('text', ''),
                'visibility': current.get('vis_km', 0),
                'cloud_cover': current.get('cloud', 0),
                'uv_index': current.get('uv', None),
            })
        
        return standardized
    
    def standardize_forecast_data(self, data: Dict, provider: str) -> List[Dict]:
        """Standardize forecast data from different providers"""
        standardized_forecast = []
        
        if provider == 'openweathermap':
            forecast_list = data.get('list', [])
            for item in forecast_list:
                forecast_item = {
                    'datetime': datetime.fromtimestamp(item.get('dt', 0)),
                    'temperature_min': item.get('main', {}).get('temp_min', 0) - 273.15,
                    'temperature_max': item.get('main', {}).get('temp_max', 0) - 273.15,
                    'temperature_avg': item.get('main', {}).get('temp', 0) - 273.15,
                    'humidity_avg': item.get('main', {}).get('humidity', 0),
                    'wind_speed_avg': item.get('wind', {}).get('speed', 0),
                    'wind_direction_avg': item.get('wind', {}).get('deg', 0),
                    'precipitation_probability': item.get('pop', 0) * 100,
                    'precipitation_amount': item.get('rain', {}).get('3h', 0),
                    'weather_condition': item.get('weather', [{}])[0].get('description', ''),
                    'cloud_cover_avg': item.get('clouds', {}).get('all', 0),
                }
                standardized_forecast.append(forecast_item)
        
        elif provider == 'weatherapi':
            forecast_days = data.get('forecast', {}).get('forecastday', [])
            for day in forecast_days:
                day_data = day.get('day', {})
                forecast_item = {
                    'datetime': datetime.strptime(day.get('date', ''), '%Y-%m-%d'),
                    'temperature_min': day_data.get('mintemp_c', 0),
                    'temperature_max': day_data.get('maxtemp_c', 0),
                    'temperature_avg': day_data.get('avgtemp_c', 0),
                    'humidity_avg': day_data.get('avghumidity', 0),
                    'wind_speed_avg': day_data.get('maxwind_kph', 0) / 3.6,
                    'precipitation_probability': day_data.get('daily_chance_of_rain', 0),
                    'precipitation_amount': day_data.get('totalprecip_mm', 0),
                    'weather_condition': day_data.get('condition', {}).get('text', ''),
                    'uv_index': day_data.get('uv', None),
                }
                standardized_forecast.append(forecast_item)
        
        return standardized_forecast
    
    def standardize_alerts_data(self, data: Dict, provider: str) -> List[Dict]:
        """Standardize weather alerts from different providers"""
        standardized_alerts = []
        
        if provider == 'openweathermap':
            alerts = data.get('alerts', [])
            for alert in alerts:
                alert_item = {
                    'title': alert.get('event', ''),
                    'description': alert.get('description', ''),
                    'severity': self.map_severity(alert.get('severity', '')),
                    'start_time': datetime.fromtimestamp(alert.get('start', 0)),
                    'end_time': datetime.fromtimestamp(alert.get('end', 0)),
                    'source': alert.get('sender_name', ''),
                    'tags': alert.get('tags', []),
                }
                standardized_alerts.append(alert_item)
        
        return standardized_alerts
    
    def map_severity(self, severity: str) -> str:
        """Map provider-specific severity to standard levels"""
        severity_mapping = {
            'minor': 'low',
            'moderate': 'medium',
            'severe': 'high',
            'extreme': 'critical',
            'unknown': 'medium'
        }
        return severity_mapping.get(severity.lower(), 'medium')
    
    def store_forecast_data(self, station: WeatherStation, forecast_data: List[Dict]):
        """Store forecast data in database"""
        try:
            for forecast_item in forecast_data:
                WeatherForecast.objects.update_or_create(
                    station=station,
                    forecast_type='daily',
                    forecast_time=forecast_item['datetime'],
                    defaults={
                        'forecast_horizon': forecast_item['datetime'] - timezone.now(),
                        'temperature_min': forecast_item['temperature_min'],
                        'temperature_max': forecast_item['temperature_max'],
                        'temperature_avg': forecast_item['temperature_avg'],
                        'humidity_avg': forecast_item['humidity_avg'],
                        'wind_speed_avg': forecast_item['wind_speed_avg'],
                        'wind_direction_avg': forecast_item.get('wind_direction_avg', 0),
                        'precipitation_probability': forecast_item['precipitation_probability'],
                        'precipitation_amount': forecast_item['precipitation_amount'],
                        'weather_condition': forecast_item['weather_condition'],
                        'cloud_cover_avg': forecast_item.get('cloud_cover_avg', 0),
                        'confidence_score': 0.8,
                        'model_used': 'API_Forecast',
                        'data_source': station.api_provider or self.default_provider,
                    }
                )
        except Exception as e:
            logger.error(f"Error storing forecast data: {e}")
    
    def store_weather_alerts(self, station: WeatherStation, alerts_data: List[Dict]):
        """Store weather alerts in database"""
        try:
            for alert_item in alerts_data:
                WeatherAlert.objects.update_or_create(
                    station=station,
                    title=alert_item['title'],
                    start_time=alert_item['start_time'],
                    defaults={
                        'alert_type': self.determine_alert_type(alert_item['title']),
                        'severity': alert_item['severity'],
                        'description': alert_item['description'],
                        'end_time': alert_item['end_time'],
                        'threshold_values': {},
                        'current_values': {},
                        'impact_level': alert_item['severity'],
                    }
                )
        except Exception as e:
            logger.error(f"Error storing weather alerts: {e}")
    
    def determine_alert_type(self, title: str) -> str:
        """Determine alert type from title"""
        title_lower = title.lower()
        
        if 'storm' in title_lower or 'thunderstorm' in title_lower:
            return 'storm'
        elif 'rain' in title_lower or 'precipitation' in title_lower:
            return 'heavy_rain'
        elif 'wind' in title_lower:
            return 'high_wind'
        elif 'temperature' in title_lower or 'heat' in title_lower or 'cold' in title_lower:
            return 'temperature_extreme'
        elif 'flood' in title_lower:
            return 'flood'
        elif 'typhoon' in title_lower or 'hurricane' in title_lower:
            return 'typhoon'
        elif 'frost' in title_lower:
            return 'frost'
        else:
            return 'storm'  # Default


class OpenWeatherMapProvider:
    """OpenWeatherMap API provider"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'OPENWEATHERMAP_API_KEY', '')
        self.base_url = 'https://api.openweathermap.org/data/2.5'
    
    def get_current_weather(self, lat: float, lon: float) -> Dict:
        url = f"{self.base_url}/weather"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': self.api_key,
            'units': 'metric'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_forecast(self, lat: float, lon: float, days: int) -> Dict:
        url = f"{self.base_url}/forecast"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': self.api_key,
            'units': 'metric',
            'cnt': days * 8  # 8 forecasts per day (3-hour intervals)
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_alerts(self, lat: float, lon: float) -> Dict:
        url = f"{self.base_url}/onecall"
        params = {
            'lat': lat,
            'lon': lon,
            'appid': self.api_key,
            'exclude': 'minutely,hourly,daily'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()


class WeatherAPIProvider:
    """WeatherAPI.com provider"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'WEATHERAPI_KEY', '')
        self.base_url = 'https://api.weatherapi.com/v1'
    
    def get_current_weather(self, lat: float, lon: float) -> Dict:
        url = f"{self.base_url}/current.json"
        params = {
            'key': self.api_key,
            'q': f"{lat},{lon}",
            'aqi': 'yes'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_forecast(self, lat: float, lon: float, days: int) -> Dict:
        url = f"{self.base_url}/forecast.json"
        params = {
            'key': self.api_key,
            'q': f"{lat},{lon}",
            'days': min(days, 10),  # Max 10 days
            'aqi': 'yes',
            'alerts': 'yes'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_alerts(self, lat: float, lon: float) -> Dict:
        # WeatherAPI includes alerts in forecast response
        return self.get_forecast(lat, lon, 1)


class AccuWeatherProvider:
    """AccuWeather API provider"""
    
    def __init__(self):
        self.api_key = getattr(settings, 'ACCUWEATHER_API_KEY', '')
        self.base_url = 'https://dataservice.accuweather.com'
    
    def get_current_weather(self, lat: float, lon: float) -> Dict:
        # AccuWeather requires location key first
        location_key = self.get_location_key(lat, lon)
        url = f"{self.base_url}/currentconditions/v1/{location_key}"
        params = {
            'apikey': self.api_key,
            'details': 'true'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()[0] if response.json() else {}
    
    def get_forecast(self, lat: float, lon: float, days: int) -> Dict:
        location_key = self.get_location_key(lat, lon)
        forecast_type = '5day' if days <= 5 else '10day'
        url = f"{self.base_url}/forecasts/v1/daily/{forecast_type}/{location_key}"
        params = {
            'apikey': self.api_key,
            'details': 'true',
            'metric': 'true'
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_alerts(self, lat: float, lon: float) -> Dict:
        location_key = self.get_location_key(lat, lon)
        url = f"{self.base_url}/alerts/v1/{location_key}"
        params = {'apikey': self.api_key}
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json()
    
    def get_location_key(self, lat: float, lon: float) -> str:
        url = f"{self.base_url}/locations/v1/cities/geoposition/search"
        params = {
            'apikey': self.api_key,
            'q': f"{lat},{lon}"
        }
        response = requests.get(url, params=params)
        response.raise_for_status()
        return response.json().get('Key', '')


class MeteostatProvider:
    """Meteostat API provider for historical data"""
    
    def __init__(self):
        self.base_url = 'https://meteostat.p.rapidapi.com'
        self.api_key = getattr(settings, 'RAPIDAPI_KEY', '')
    
    def get_current_weather(self, lat: float, lon: float) -> Dict:
        # Meteostat doesn't provide real-time data, use recent hourly data
        url = f"{self.base_url}/stations/hourly"
        headers = {
            'X-RapidAPI-Key': self.api_key,
            'X-RapidAPI-Host': 'meteostat.p.rapidapi.com'
        }
        # Implementation would require finding nearest station first
        return {}
    
    def get_forecast(self, lat: float, lon: float, days: int) -> Dict:
        # Meteostat is primarily for historical data
        return {}
    
    def get_alerts(self, lat: float, lon: float) -> Dict:
        # Meteostat doesn't provide alerts
        return {}
