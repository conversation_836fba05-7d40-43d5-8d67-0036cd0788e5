<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            background-color: #ccc;
        }
        .debug {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    
    <div class="debug">
        <h3>Debug Info:</h3>
        <p>API Key: {{ google_maps_api_key }}</p>
        <p>API Key Length: {{ google_maps_api_key|length }}</p>
    </div>
    
    <div id="map"></div>
    
    <div class="debug">
        <h3>Console Output:</h3>
        <div id="console-output"></div>
    </div>

    <script>
        function log(message) {
            console.log(message);
            const output = document.getElementById('console-output');
            output.innerHTML += '<p>' + message + '</p>';
        }

        log('Starting Google Maps test...');
        log('API Key: {{ google_maps_api_key }}');

        function initMap() {
            log('Google Maps API loaded successfully!');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 10.8231, lng: 106.6297 }, // Ho Chi Minh City
                    zoom: 10,
                    mapTypeId: google.maps.MapTypeId.SATELLITE
                });
                
                const marker = new google.maps.Marker({
                    position: { lat: 10.8231, lng: 106.6297 },
                    map: map,
                    title: 'Test Location'
                });
                
                log('Map and marker created successfully!');
            } catch (error) {
                log('Error creating map: ' + error.message);
            }
        }

        function handleGoogleMapsError() {
            log('Google Maps API failed to load!');
        }

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            log('Google Maps already available');
            initMap();
        } else {
            log('Waiting for Google Maps API to load...');
        }
    </script>
    
    <script src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=places" async defer onerror="handleGoogleMapsError()"></script>
</body>
</html>
