{% extends "base.html" %}
{% load static %}

{% block title %}Enhanced Cumulative Map with Labor & Weather{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #2563eb;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
        --worker-color: #8b5cf6;
        --weather-color: #06b6d4;
    }    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 0;
    }    /* Force map container size - highest specificity */
    .dashboard-container #map,    #map,
    div#map {
        width: 100% !important;
        height: 500px !important;
        min-height: 500px !important;
        max-height: none !important;
        display: block !important;
    }

    .dashboard-container {
        min-height: 100vh;
        padding: 20px;
    }

    .header {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .header h1 {
        margin: 0;
        color: #1f2937;
        font-size: 2.2rem;
        display: flex;
        align-items: center;
        gap: 15px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    }

    .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, var(--primary-color), var(--success-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f2937;
        margin: 10px 0 5px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.95rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }    .map-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 20px;
        position: relative;
        min-height: 580px; /* 500px map + 80px padding/header */
        height: auto;
    }

    .map-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    }

    .map-controls {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 10px 18px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #2563eb, #1d4ed8);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #4b5563, #374151);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(107, 114, 128, 0.3);
    }

    .btn-weather {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        color: white;
    }

    .btn-weather:hover {
        background: linear-gradient(135deg, #0891b2, #0e7490);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(6, 182, 212, 0.3);
    }

    .btn-workers {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
    }

    .btn-workers:hover {
        background: linear-gradient(135deg, #7c3aed, #6d28d9);
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(139, 92, 246, 0.3);    }    #map {
        width: 100% !important;
        height: 500px !important;
        min-height: 500px !important;
        max-height: none !important;
        border-radius: 15px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        position: relative;
    }.loading {
        display: flex;
        flex-direction: column;
        justify-content: center;        align-items: center;
        height: 500px !important;
        min-height: 500px !important;
        color: #6b7280;
        font-size: 1.2rem;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-radius: 15px;
    }

    .loading i {
        font-size: 3rem;
        margin-bottom: 20px;
        animation: spin 2s linear infinite;
        color: var(--primary-color);
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }    .error {
        display: flex;        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 500px;
        color: #ef4444;
        background: linear-gradient(135deg, #fef2f2, #fee2e2);
        border: 2px solid #fecaca;
        border-radius: 15px;
        text-align: center;
    }

    .legend {
        background: rgba(255, 255, 255, 0.95);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 20px;
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 1000;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        max-width: 250px;
        min-width: 200px;
    }

    .legend-title {
        font-weight: bold;
        margin-bottom: 15px;
        color: #1f2937;
        font-size: 1.1rem;
        text-align: center;
        text-transform: uppercase;
        letter-spacing: 1px;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 10px;
    }

    .legend-section {
        margin-bottom: 15px;
    }

    .legend-section-title {
        font-weight: 600;
        color: #374151;
        font-size: 0.9rem;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.85rem;
        padding: 4px 0;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 10px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        border: 2px solid white;
    }

    .legend-icon {
        width: 20px;
        text-align: center;
        margin-right: 8px;
        font-size: 0.9rem;
    }

    .info-panel {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .info-panel h4 {
        color: #1f2937;
        margin-top: 0;
        margin-bottom: 20px;
        font-size: 1.3rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 10px;
    }

    .status-active { 
        color: #10b981; 
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
    }
    .status-inactive { 
        color: #ef4444; 
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(239, 68, 68, 0.2);
    }
    .status-maintenance { 
        color: #f59e0b; 
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(245, 158, 11, 0.2);
    }
    .status-working { 
        color: #8b5cf6; 
        font-weight: 600;
        text-shadow: 0 1px 2px rgba(139, 92, 246, 0.2);
    }

    /* 3D Transparent Letter Overlay Styles */
    .map-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 10;
        overflow: hidden;
        border-radius: 15px;
    }

    .transparent-letter {
        position: absolute;
        font-size: 120px;
        font-weight: 900;
        color: rgba(255, 255, 255, 0.1);
        text-shadow: 
            0 0 20px rgba(255, 255, 255, 0.3),
            0 0 40px rgba(255, 255, 255, 0.2),
            2px 2px 4px rgba(0, 0, 0, 0.1);
        transform: rotateX(20deg) rotateY(-10deg);
        animation: float 6s ease-in-out infinite;
        font-family: 'Arial Black', sans-serif;
        letter-spacing: -5px;
    }

    .letter-w { 
        top: 10%; 
        left: 10%; 
        animation-delay: -1s;
        color: rgba(139, 92, 246, 0.15);
    }
    .letter-e { 
        top: 20%; 
        right: 15%; 
        animation-delay: -2s;
        color: rgba(6, 182, 212, 0.15);
    }
    .letter-a { 
        bottom: 20%; 
        left: 20%; 
        animation-delay: -3s;
        color: rgba(16, 185, 129, 0.15);
    }
    .letter-t { 
        bottom: 15%; 
        right: 10%; 
        animation-delay: -4s;
        color: rgba(59, 130, 246, 0.15);
    }
    .letter-h { 
        top: 50%; 
        left: 50%; 
        transform: translate(-50%, -50%) rotateX(20deg) rotateY(-10deg);
        animation-delay: -5s;
        color: rgba(245, 158, 11, 0.15);
    }

    @keyframes float {
        0%, 100% { 
            transform: translateY(0px) rotateX(20deg) rotateY(-10deg);
            opacity: 0.1;
        }
        50% { 
            transform: translateY(-20px) rotateX(25deg) rotateY(-15deg);
            opacity: 0.2;
        }
    }

    /* Enhanced Weather and Wind Symbols */
    .weather-overlay {
        position: absolute;
        top: 100px;
        left: 20px;
        z-index: 1001;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        min-width: 200px;
    }

    .weather-title {
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .weather-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.85rem;
        padding: 4px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .weather-item:last-child {
        border-bottom: none;
    }

    .wind-indicator {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        border-radius: 50%;
        position: relative;
        animation: windSpin 3s linear infinite;
        margin-left: 5px;
    }

    .wind-indicator::before {
        content: '→';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        font-size: 12px;
    }

    @keyframes windSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .worker-info-panel {
        position: absolute;
        bottom: 20px;
        left: 20px;
        z-index: 1001;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 15px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        max-width: 250px;
    }

    .worker-title {
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .worker-count {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: bold;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        
        .map-header {
            flex-direction: column;
            gap: 15px;
        }
        
        .map-controls {
            width: 100%;
            justify-content: center;
        }
        
        .legend {
            position: relative;
            top: auto;
            right: auto;
            margin-top: 20px;
        }
        
        .weather-overlay,
        .worker-info-panel {
            position: relative;
            top: auto;
            left: auto;
            bottom: auto;
            margin-top: 15px;
        }
        
        .transparent-letter {
            font-size: 60px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container enhanced-cumulative">
    <!-- Header -->
    <div class="header">
        <h1>
            <i class="fas fa-globe-americas"></i>
            Enhanced Cumulative Map Dashboard
            <span style="font-size: 1rem; color: #6b7280; font-weight: normal;">
                with Live Labor Tracking & Weather Integration
            </span>
        </h1>
    </div>

    <!-- Enhanced Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-warehouse"></i>
            </div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Total Farms</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-water"></i>
            </div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-number">{{ total_workers }}</div>
            <div class="stat-label">Active Workers</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-cloud-sun"></i>
            </div>
            <div class="stat-number">{{ weather_stations }}</div>
            <div class="stat-label">Weather Stations</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-play-circle"></i>
            </div>
            <div class="stat-number">{{ active_ponds }}</div>
            <div class="stat-label">Active Ponds</div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-fan"></i>
            </div>
            <div class="stat-number">{{ active_aerators }}</div>
            <div class="stat-label">Active Aerators</div>
        </div>
    </div>

    <!-- Enhanced Map Container -->
    <div class="map-container">
        <div class="map-header">
            <h3 style="margin: 0; color: #1f2937; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-map-marked-alt"></i>
                Live Tracking Map
                <span style="font-size: 0.8rem; color: #6b7280; font-weight: normal;">
                    Real-time Labor & Weather Monitoring
                </span>
            </h3>
            <div class="map-controls">
                <button class="btn btn-primary" onclick="toggleMapType()">
                    <i class="fas fa-layer-group"></i>
                    Toggle View
                </button>
                <button class="btn btn-secondary" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i>
                    Center
                </button>
                <button class="btn btn-weather" onclick="toggleWeatherOverlay()">
                    <i class="fas fa-cloud-sun"></i>
                    Weather
                </button>
                <button class="btn btn-workers" onclick="toggleWorkersDisplay()">
                    <i class="fas fa-users"></i>
                    Workers
                </button>
                <button class="btn btn-secondary" onclick="refreshMap()">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
            </div>
        </div>

        <!-- Map with Transparent 3D Letters Overlay -->
        <div style="position: relative;">
            <div id="map" class="loading">
                <div>
                    <i class="fas fa-spinner fa-spin"></i>
                    <div>Loading Enhanced Map...</div>
                    <div style="font-size: 0.9rem; color: #9ca3af; margin-top: 10px;">
                        Initializing labor tracking and weather integration...
                    </div>
                </div>
            </div>
            
            <!-- 3D Transparent Letters Overlay -->
            <div class="map-overlay">
                <div class="transparent-letter letter-w">W</div>
                <div class="transparent-letter letter-e">E</div>
                <div class="transparent-letter letter-a">A</div>
                <div class="transparent-letter letter-t">T</div>
                <div class="transparent-letter letter-h">H</div>
            </div>

            <!-- Enhanced Legend -->
            <div class="legend">
                <div class="legend-title">Live Map Legend</div>
                
                <div class="legend-section">
                    <div class="legend-section-title">Facilities</div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: linear-gradient(135deg, #3b82f6, #2563eb);"></div>
                        <span>Farms</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #10b981;"></div>
                        <span>Active Ponds</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #f59e0b;"></div>
                        <span>Maintenance</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ef4444;"></div>
                        <span>Inactive</span>
                    </div>
                </div>

                <div class="legend-section">
                    <div class="legend-section-title">Live Tracking</div>
                    <div class="legend-item">
                        <div class="legend-icon"><i class="fas fa-user" style="color: #8b5cf6;"></i></div>
                        <span>Active Workers</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-icon"><i class="fas fa-cloud-sun" style="color: #06b6d4;"></i></div>
                        <span>Weather Stations</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-icon"><i class="fas fa-wind" style="color: #0891b2;"></i></div>
                        <span>Wind Direction</span>
                    </div>
                </div>
            </div>

            <!-- Weather Overlay Panel -->
            <div class="weather-overlay" id="weatherOverlay" style="display: none;">
                <div class="weather-title">
                    <i class="fas fa-cloud-sun"></i>
                    Current Weather
                </div>
                <div id="weatherData">
                    <div class="weather-item">
                        <span>Temperature:</span>
                        <span id="temperature">--°C</span>
                    </div>
                    <div class="weather-item">
                        <span>Humidity:</span>
                        <span id="humidity">--%</span>
                    </div>
                    <div class="weather-item">
                        <span>Wind Speed:</span>
                        <span id="windSpeed">-- km/h</span>
                        <div class="wind-indicator" id="windIndicator"></div>
                    </div>
                    <div class="weather-item">
                        <span>Pressure:</span>
                        <span id="pressure">-- hPa</span>
                    </div>
                </div>
            </div>

            <!-- Worker Info Panel -->
            <div class="worker-info-panel" id="workerPanel">
                <div class="worker-title">
                    <i class="fas fa-users"></i>
                    Live Workers
                    <span class="worker-count" id="workerCount">{{ total_workers }}</span>
                </div>                <div style="font-size: 0.8rem; color: #6b7280;">
                    <div>Active: <span class="status-working">{{ active_workers }}</span></div>
                    <div>Last Update: <span id="lastWorkerUpdate">{{ "now"|date:"H:i" }}</span></div>
                </div>
                
                <!-- Debug Info Panel -->
                <div style="background: #f8f9fa; padding: 8px; margin: 8px 0; border-radius: 6px; font-size: 0.75rem; border-left: 3px solid #8b5cf6;">
                    <strong style="color: #8b5cf6;">🔍 Debug Info:</strong><br>
                    Workers in Template: {{ workers_data|length }}<br>
                    Labor App Available: {% if workers_data|length > 0 %}✅{% else %}❌{% endif %}<br>
                    JS Workers Count: <span id="debugWorkersCount">Loading...</span><br>
                    GPS Workers: <span id="debugGpsWorkers">Loading...</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Info Panel -->
    <div class="info-panel">
        <h4>Real-time Dashboard Statistics</h4>
        <div class="row">
            <div class="col-md-4">
                <p><strong>Farms with Location:</strong> {{ farms_with_location }}</p>
                <p><strong>Ponds with Location:</strong> {{ ponds_with_location }}</p>
                <p><strong>Active Aerators:</strong> <span class="status-active">{{ active_aerators }}</span></p>
            </div>
            <div class="col-md-4">
                <p><strong>Map Center:</strong> {{ center_lat|floatformat:4 }}, {{ center_lng|floatformat:4 }}</p>
                <p><strong>Total Area:</strong> {{ total_area|floatformat:1 }} acres</p>
                <p><strong>Weather Stations:</strong> <span class="status-active">{{ weather_stations }}</span></p>
            </div>
            <div class="col-md-4">
                <p><strong>Active Workers:</strong> <span class="status-working">{{ active_workers }}</span></p>
                <p><strong>Last Updated:</strong> <span id="lastUpdated">{{ "now"|date:"M d, Y H:i" }}</span></p>
                <p><strong>System Status:</strong> <span class="status-active">Online</span></p>            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // CRITICAL: Define data variables FIRST before any functions
    
    // Global variables
    let map;
    let markers = [];
    let weatherMarkers = [];
    let workerMarkers = [];
    let currentMapType = 'roadmap';
    let weatherOverlayVisible = false;
    let workersVisible = true;
      // Map data from Django context - with error handling
    let farmsData = [];
    let pondsData = [];
    let workersData = [];
    let weatherData = [];
    let centerLat = 13.0827;
    let centerLng = 80.2707;
    let apiKey = '{{ google_maps_api_key|default:"" }}';
      try {
        farmsData = {{ farms_data|default:"[]"|safe }};
        pondsData = {{ ponds_data|default:"[]"|safe }};
        workersData = {{ workers_data|default:"[]"|safe }};
        weatherData = {{ weather_data|default:"[]"|safe }};
        centerLat = {{ center_lat|default:"13.0827" }};
        centerLng = {{ center_lng|default:"80.2707" }};
        
        // Fallback to defaults if data is null or undefined
        farmsData = farmsData || [];
        pondsData = pondsData || [];
        workersData = workersData || [];
        weatherData = weatherData || [];
        centerLat = centerLat || 13.0827;
        centerLng = centerLng || 80.2707;    } catch (error) {
        console.error('❌ Error parsing template data:', error);
        // Use defaults
        farmsData = [];
        pondsData = [];
        workersData = [];
        weatherData = [];
        centerLat = 13.0827;
        centerLng = 80.2707;
    }
    
    console.log('🗺️ Enhanced Map initialization starting...');
    console.log('📍 Farms:', farmsData.length);
    console.log('🏊 Ponds:', pondsData.length);
    console.log('👥 Workers:', workersData.length);
    console.log('🌤️ Weather stations:', weatherData.length);
    
    // Debug worker data
    if (workersData && workersData.length > 0) {
        console.log('🔍 Worker details:');
        workersData.forEach((worker, index) => {
            console.log(`  Worker ${index + 1}: ${worker.name} at (${worker.latitude}, ${worker.longitude}) - Status: ${worker.status}`);
        });
    } else {
        console.log('❌ No worker data available for map display');
    }
    
    // Update debug info in the UI
    document.addEventListener('DOMContentLoaded', function() {
        const debugWorkersCount = document.getElementById('debugWorkersCount');
        const debugGpsWorkers = document.getElementById('debugGpsWorkers');
        
        if (debugWorkersCount) {
            debugWorkersCount.textContent = workersData ? workersData.length : 0;
        }
        
        if (debugGpsWorkers) {
            const gpsWorkers = workersData ? workersData.filter(w => w.latitude && w.longitude).length : 0;
            debugGpsWorkers.textContent = gpsWorkers;
        }
    });
    
    // CRITICAL: Define Google Maps callback functions AFTER data is ready
    
    // Initialize map when Google Maps API loads - MUST be globally accessible
    window.initMap = function() {
        console.log('🗺️ Google Maps API loaded, initializing enhanced map...');
        
        // Data should now be available
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }
        
        // Force map container dimensions
        mapElement.style.height = '500px';
        mapElement.style.width = '100%';
        mapElement.style.minHeight = '500px';
        mapElement.style.display = 'block';
        console.log('📏 Map container size set to:', mapElement.style.height);

        // Check if we have valid coordinates
        if (isNaN(centerLat) || isNaN(centerLng)) {
            console.error('❌ Invalid center coordinates');
            mapElement.innerHTML = '<div class="error"><div><i class="fas fa-exclamation-triangle"></i><br>Invalid map coordinates</div></div>';
            return;
        }        try {
            // Calculate optimal map center and bounds based on pond locations
            let shouldFitBounds = false;
            const bounds = new google.maps.LatLngBounds();
            
            // Priority 1: If we have pond data, use pond locations for bounds
            if (pondsData && pondsData.length > 0) {
                console.log('🎯 Enhanced map: prioritizing pond locations for centering...');
                pondsData.forEach(pond => {
                    if (pond.latitude && pond.longitude) {
                        bounds.extend(new google.maps.LatLng(pond.latitude, pond.longitude));
                        shouldFitBounds = true;
                    }
                });
                console.log(`📍 Found ${pondsData.length} ponds for map bounds calculation`);
            } else if (farmsData && farmsData.length > 0) {
                console.log('🎯 Enhanced map: using farm locations for centering...');
                farmsData.forEach(farm => {
                    if (farm.latitude && farm.longitude) {
                        bounds.extend(new google.maps.LatLng(farm.latitude, farm.longitude));
                        shouldFitBounds = true;
                    }
                });
            }
            
            // Create the enhanced map
            map = new google.maps.Map(mapElement, {
                zoom: shouldFitBounds ? 10 : 12, // Will be adjusted by fitBounds if needed
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: currentMapType,
                styles: [
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: 'off' }]
                    },
                    {
                        featureType: 'water',
                        elementType: 'geometry',
                        stylers: [{ color: '#76c7c0' }]
                    },
                    {
                        featureType: 'landscape',
                        elementType: 'geometry',
                        stylers: [{ color: '#f5f5f2' }]
                    }
                ]
            });

            console.log('✅ Enhanced map created successfully!');
            
            // Remove loading state
            mapElement.classList.remove('loading');
            
            // Initialize map features
            initializeMapFeatures();
            
            // Auto-fit map to show all pond locations after features are added
            if (shouldFitBounds && bounds && !bounds.isEmpty()) {
                setTimeout(() => {
                    console.log('🔍 Auto-fitting enhanced map bounds to show all pond locations...');
                    map.fitBounds(bounds);
                    
                    // Ensure reasonable zoom levels
                    setTimeout(() => {
                        const currentZoom = map.getZoom();
                        if (currentZoom > 16) {
                            map.setZoom(16); // Don't zoom in too much
                        } else if (currentZoom < 8) {
                            map.setZoom(8); // Don't zoom out too much
                        }
                        console.log(`🎯 Enhanced map final zoom level: ${map.getZoom()}`);
                    }, 200);
                }, 500); // Wait for markers to be added
            }
            
        } catch (error) {
            console.error('❌ Error creating map:', error);
            handleMapError();
        }
    };

    // Handle map loading errors - MUST be globally accessible
    window.handleMapError = function() {
        console.error('❌ Failed to load Google Maps');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.classList.remove('loading');
            mapElement.innerHTML = `
                <div class="error">
                    <div>
                        <i class="fas fa-exclamation-triangle"></i><br>
                        Failed to load Enhanced Google Maps<br>
                        <small>Please check your internet connection and refresh the page</small>
                    </div>
                </div>
            `;
        }
    };
      // Function to initialize map features (called by the main initMap function)
    function initializeMapFeatures() {
        console.log('🚀 Initializing map features...');
        
        // Add all markers
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addWeatherMarkers();
        
        // Initialize overlays
        updateWeatherOverlay();
        updateWorkerInfo();
        updateLastUpdatedTime();
        
        console.log('✅ Map features initialized');
    }

    // Add enhanced farm markers
    function addFarmMarkers() {
        if (!farmsData || farmsData.length === 0) {
            console.log('ℹ️ No farms data available');
            return;
        }

        console.log('🏢 Adding enhanced farm markers:', farmsData.length);
        
        farmsData.forEach(farm => {
            if (farm.latitude && farm.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: farm.latitude, lng: farm.longitude },
                    map: map,
                    title: farm.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="farmGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <circle cx="17.5" cy="17.5" r="15" fill="url(#farmGrad)" stroke="white" stroke-width="3"/>
                                <text x="17.5" y="23" text-anchor="middle" fill="white" font-size="14" font-weight="bold">F</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(35, 35)
                    },
                    animation: google.maps.Animation.DROP
                });

                // Enhanced info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 15px; max-width: 300px; font-family: 'Segoe UI', sans-serif;">
                            <h4 style="margin: 0 0 15px 0; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">
                                <i class="fas fa-warehouse" style="color: #3b82f6; margin-right: 8px;"></i>
                                ${farm.name}
                            </h4>
                            <div style="display: grid; gap: 8px;">
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Location:</strong> <span>${farm.location || 'N/A'}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Total Ponds:</strong> <span style="color: #06b6d4; font-weight: bold;">${farm.total_ponds || 0}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Active Ponds:</strong> <span style="color: #10b981; font-weight: bold;">${farm.active_ponds || 0}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Total Area:</strong> <span style="color: #f59e0b; font-weight: bold;">${farm.total_area || 0} acres</span>
                                </p>
                                ${farm.contact_person ? `<p style="margin: 8px 0 0 0; font-size: 0.9rem; color: #6b7280;"><strong>Contact:</strong> ${farm.contact_person}</p>` : ''}
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    closeAllInfoWindows();
                    infoWindow.open(map, marker);
                });

                markers.push({ marker, infoWindow, type: 'farm' });
            }
        });
    }

    // Add enhanced pond markers
    function addPondMarkers() {
        if (!pondsData || pondsData.length === 0) {
            console.log('ℹ️ No ponds data available');
            return;
        }

        console.log('🏊 Adding enhanced pond markers:', pondsData.length);
        
        pondsData.forEach(pond => {
            if (pond.latitude && pond.longitude) {
                // Enhanced color scheme based on status
                let color = '#10b981'; // Default active green
                let pulseColor = 'rgba(16, 185, 129, 0.3)';
                if (pond.status === 'maintenance') {
                    color = '#f59e0b';
                    pulseColor = 'rgba(245, 158, 11, 0.3)';
                } else if (pond.status === 'inactive') {
                    color = '#ef4444';
                    pulseColor = 'rgba(239, 68, 68, 0.3)';
                }

                const marker = new google.maps.Marker({
                    position: { lat: pond.latitude, lng: pond.longitude },
                    map: map,
                    title: pond.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="${color}" stroke="white" stroke-width="2"/>
                                <circle cx="15" cy="15" r="18" fill="${pulseColor}" opacity="0.4">
                                    <animate attributeName="r" values="12;20;12" dur="2s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.4;0;0.4" dur="2s" repeatCount="indefinite"/>
                                </circle>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">P</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    },
                    animation: google.maps.Animation.DROP
                });

                // Enhanced info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 15px; max-width: 300px; font-family: 'Segoe UI', sans-serif;">
                            <h4 style="margin: 0 0 15px 0; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">
                                <i class="fas fa-water" style="color: #06b6d4; margin-right: 8px;"></i>
                                ${pond.name}
                            </h4>
                            <div style="display: grid; gap: 8px;">
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Status:</strong> 
                                    <span style="color: ${color}; font-weight: bold; text-transform: capitalize;">
                                        ${pond.status || 'Unknown'}
                                    </span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Size:</strong> <span style="color: #f59e0b; font-weight: bold;">${pond.size || 'N/A'} acres</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Species:</strong> <span>${pond.species || 'N/A'}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Farm:</strong> <span>${pond.farm_name || 'N/A'}</span>
                                </p>
                                ${pond.aerator_count ? `
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Aerators:</strong> 
                                    <span style="color: #8b5cf6; font-weight: bold;">
                                        ${pond.active_aerators}/${pond.aerator_count}
                                    </span>
                                </p>` : ''}
                                ${pond.depth ? `<p style="margin: 8px 0 0 0; font-size: 0.9rem; color: #6b7280;"><strong>Depth:</strong> ${pond.depth} ft</p>` : ''}
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    closeAllInfoWindows();
                    infoWindow.open(map, marker);
                });

                markers.push({ marker, infoWindow, type: 'pond' });
            }
        });
    }

    // Add worker markers with live tracking
    function addWorkerMarkers() {
        if (!workersData || workersData.length === 0) {
            console.log('ℹ️ No workers data available');
            return;
        }

        console.log('👥 Adding worker markers:', workersData.length);
        
        workersData.forEach(worker => {
            if (worker.latitude && worker.longitude) {
                // Worker status color
                let color = '#8b5cf6'; // Default purple
                if (worker.status === 'working') color = '#10b981';
                else if (worker.status === 'break') color = '#f59e0b';
                else if (worker.status === 'offline') color = '#ef4444';

                const marker = new google.maps.Marker({
                    position: { lat: worker.latitude, lng: worker.longitude },
                    map: map,
                    title: worker.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="14" cy="14" r="11" fill="${color}" stroke="white" stroke-width="2"/>
                                <circle cx="14" cy="14" r="16" fill="${color}" opacity="0.2">
                                    <animate attributeName="r" values="11;18;11" dur="1.5s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.3;0;0.3" dur="1.5s" repeatCount="indefinite"/>
                                </circle>
                                <text x="14" y="18" text-anchor="middle" fill="white" font-size="10" font-weight="bold">W</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(28, 28)
                    },
                    animation: google.maps.Animation.BOUNCE
                });

                // Enhanced worker info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 15px; max-width: 300px; font-family: 'Segoe UI', sans-serif;">
                            <h4 style="margin: 0 0 15px 0; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">
                                <i class="fas fa-user" style="color: #8b5cf6; margin-right: 8px;"></i>
                                ${worker.name}
                            </h4>
                            <div style="display: grid; gap: 8px;">
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>ID:</strong> <span>${worker.employee_id || 'N/A'}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Status:</strong> 
                                    <span style="color: ${color}; font-weight: bold; text-transform: capitalize;">
                                        ${worker.status}
                                    </span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Team:</strong> <span>${worker.team}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Skill Level:</strong> <span style="color: #06b6d4; font-weight: bold;">${worker.skill_level}</span>
                                </p>
                                <p style="margin: 0; display: flex; justify-content: space-between;">
                                    <strong>Current Task:</strong> <span style="color: #f59e0b;">${worker.current_task}</span>
                                </p>
                                ${worker.task_location && worker.task_location.pond_name ? `
                                <p style="margin: 8px 0 0 0; padding: 8px; background: #f8fafc; border-radius: 6px; font-size: 0.9rem;">
                                    <strong>Task Location:</strong><br>
                                    📍 ${worker.task_location.pond_name}
                                </p>` : ''}
                                <p style="margin: 8px 0 0 0; font-size: 0.85rem; color: #6b7280;">
                                    <strong>Last Update:</strong> ${new Date(worker.last_update).toLocaleTimeString()}
                                </p>
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    closeAllInfoWindows();
                    infoWindow.open(map, marker);
                });

                workerMarkers.push({ marker, infoWindow, type: 'worker', data: worker });
            }
        });
    }

    // Add weather markers with wind indicators
    function addWeatherMarkers() {
        if (!weatherData || weatherData.length === 0) {
            console.log('ℹ️ No weather data available');
            return;
        }

        console.log('🌤️ Adding weather markers:', weatherData.length);
        
        weatherData.forEach(weather => {
            if (weather.latitude && weather.longitude) {
                const marker = new google.maps.Marker({
                    position: { lat: weather.latitude, lng: weather.longitude },
                    map: map,
                    title: `Weather Station - ${weather.description}`,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <defs>
                                    <linearGradient id="weatherGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
                                        <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
                                    </linearGradient>
                                </defs>
                                <circle cx="16" cy="16" r="13" fill="url(#weatherGrad)" stroke="white" stroke-width="2"/>
                                <circle cx="16" cy="16" r="20" fill="#06b6d4" opacity="0.15">
                                    <animate attributeName="r" values="13;22;13" dur="3s" repeatCount="indefinite"/>
                                    <animate attributeName="opacity" values="0.15;0;0.15" dur="3s" repeatCount="indefinite"/>
                                </circle>
                                <text x="16" y="21" text-anchor="middle" fill="white" font-size="12" font-weight="bold">☀</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(32, 32)
                    }
                });

                // Weather info window with wind direction
                const windDirection = weather.wind_direction || 0;
                const windArrow = `transform: rotate(${windDirection}deg)`;

                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 15px; max-width: 280px; font-family: 'Segoe UI', sans-serif;">
                            <h4 style="margin: 0 0 15px 0; color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 8px;">
                                <i class="fas fa-cloud-sun" style="color: #06b6d4; margin-right: 8px;"></i>
                                Weather Station
                            </h4>
                            <div style="display: grid; gap: 10px;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <strong>Temperature:</strong> 
                                    <span style="color: #ef4444; font-weight: bold; font-size: 1.1rem;">
                                        ${weather.temperature}°C
                                    </span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <strong>Humidity:</strong> 
                                    <span style="color: #3b82f6; font-weight: bold;">${weather.humidity}%</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <strong>Wind:</strong> 
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #06b6d4; font-weight: bold;">${weather.wind_speed} km/h</span>
                                        <div style="width: 20px; height: 20px; background: #06b6d4; border-radius: 50%; display: flex; align-items: center; justify-content: center; ${windArrow}">
                                            <span style="color: white; font-size: 10px;">→</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <strong>Pressure:</strong> 
                                    <span style="color: #8b5cf6; font-weight: bold;">${weather.pressure} hPa</span>
                                </div>
                                <div style="margin-top: 10px; padding: 8px; background: #f8fafc; border-radius: 6px; text-align: center;">
                                    <strong style="color: #06b6d4;">${weather.description}</strong>
                                </div>
                                <p style="margin: 8px 0 0 0; font-size: 0.85rem; color: #6b7280; text-align: center;">
                                    Updated: ${new Date(weather.timestamp).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    closeAllInfoWindows();
                    infoWindow.open(map, marker);
                });

                weatherMarkers.push({ marker, infoWindow, type: 'weather', data: weather });
            }
        });
    }

    // Enhanced control functions
    function toggleMapType() {
        if (!map) return;
        currentMapType = currentMapType === 'roadmap' ? 'satellite' : 'roadmap';
        map.setMapTypeId(currentMapType);
        console.log('🗺️ Map type changed to:', currentMapType);
    }

    function centerMap() {
        if (!map) return;
        map.setCenter({ lat: centerLat, lng: centerLng });
        map.setZoom(12);
        console.log('🎯 Map centered');
    }

    function toggleWeatherOverlay() {
        weatherOverlayVisible = !weatherOverlayVisible;
        const overlay = document.getElementById('weatherOverlay');
        if (overlay) {
            overlay.style.display = weatherOverlayVisible ? 'block' : 'none';
        }
        
        // Toggle weather markers visibility
        weatherMarkers.forEach(item => {
            item.marker.setVisible(weatherOverlayVisible);
        });
        
        console.log('🌤️ Weather overlay:', weatherOverlayVisible ? 'shown' : 'hidden');
    }

    function toggleWorkersDisplay() {
        workersVisible = !workersVisible;
        
        // Toggle worker markers visibility
        workerMarkers.forEach(item => {
            item.marker.setVisible(workersVisible);
        });
        
        // Update button appearance
        const btn = document.querySelector('.btn-workers');
        if (btn) {
            btn.style.opacity = workersVisible ? '1' : '0.5';
        }
        
        console.log('👥 Workers display:', workersVisible ? 'shown' : 'hidden');
    }

    function refreshMap() {
        console.log('🔄 Refreshing enhanced map...');
        
        // Clear existing markers
        [...markers, ...weatherMarkers, ...workerMarkers].forEach(item => {
            item.marker.setMap(null);
            if (item.infoWindow) item.infoWindow.close();
        });
        markers = [];
        weatherMarkers = [];
        workerMarkers = [];
        
        // Re-add all markers
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addWeatherMarkers();
        
        // Update overlays
        updateWeatherOverlay();
        updateWorkerInfo();
        updateLastUpdatedTime();
        
        console.log('✅ Enhanced map refreshed');
    }

    function closeAllInfoWindows() {
        [...markers, ...weatherMarkers, ...workerMarkers].forEach(item => {
            if (item.infoWindow) item.infoWindow.close();
        });
    }

    function updateWeatherOverlay() {
        if (weatherData && weatherData.length > 0) {
            const weather = weatherData[0]; // Use first weather station
            document.getElementById('temperature').textContent = `${weather.temperature}°C`;
            document.getElementById('humidity').textContent = `${weather.humidity}%`;
            document.getElementById('windSpeed').textContent = `${weather.wind_speed} km/h`;
            document.getElementById('pressure').textContent = `${weather.pressure} hPa`;
            
            // Update wind indicator rotation
            const windIndicator = document.getElementById('windIndicator');
            if (windIndicator) {
                windIndicator.style.transform = `rotate(${weather.wind_direction || 0}deg)`;
            }
        }
    }

    function updateWorkerInfo() {
        const activeWorkers = workersData.filter(w => w.status === 'working' || w.status === 'available').length;
        document.getElementById('workerCount').textContent = workersData.length;
        document.getElementById('lastWorkerUpdate').textContent = new Date().toLocaleTimeString();
    }

    function updateLastUpdatedTime() {
        const now = new Date();
        const timeString = now.toLocaleString();
        const element = document.getElementById('lastUpdated');        if (element) {
            element.textContent = timeString;
        }
    }
    
    // Auto-refresh worker locations every 30 seconds
    setInterval(() => {
        if (workersVisible) {
            console.log('🔄 Auto-refreshing worker locations...');
            updateWorkerInfo();
        }
    }, 30000);
    
    // Auto-refresh weather data every 2 minutes
    setInterval(() => {
        if (weatherOverlayVisible) {
            console.log('🔄 Auto-refreshing weather data...');
            updateWeatherOverlay();
        }
    }, 120000);
      // Force map container size on page load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 DOM loaded - forcing map container size...');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '500px';
            mapElement.style.display = 'block';
            console.log('📏 Map container dimensions forced to 500px height');
        }
    });
    
    // Additional resize trigger when window loads
    window.addEventListener('load', function() {
        console.log('🔧 Window loaded - additional map size enforcement...');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '500px';
            console.log('📏 Map container size re-enforced on window load');
            
            // Trigger map resize if map exists
            if (typeof map !== 'undefined' && map) {
                setTimeout(() => {
                    google.maps.event.trigger(map, 'resize');
                    console.log('🔄 Map resize event triggered after window load');
                }, 1000);
            }
        }
    });
    
    // Timeout fallback if Google Maps API doesn't load within 10 seconds
    setTimeout(() => {
        const mapElement = document.getElementById('map');
        if (mapElement && mapElement.classList.contains('loading')) {
            console.warn('⏰ Google Maps API loading timeout');
            handleMapError();
        }
    }, 10000);
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
