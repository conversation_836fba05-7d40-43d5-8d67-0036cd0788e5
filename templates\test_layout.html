<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Test - TIMESTAMP: 2025-06-04-10:31:00</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100vh;
            width: 100vw;
            font-family: Arial, sans-serif;
        }

        .layout-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            height: 100vh;
            width: 100vw;
        }

        .sidebar {
            background-color: #ffffff;
            border-right: 1px solid #ccc;
            padding: 20px;
        }

        .main-content {
            background-color: #f8f9fa;
            padding: 20px;
        }

        .test-marker {
            background: red;
            color: white;
            padding: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="layout-container">
        <div class="sidebar">
        <div class="test-marker">NEW TEMPLATE - WHITE SIDEBAR - TIME: 10:31</div>
            <h3>Navigation</h3>
            <ul>
                <li>Dashboard</li>
                <li>Ponds</li>
                <li>Water Quality</li>
                <li>Feed</li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="test-marker">MAIN CONTENT - NO GAPS</div>
            <h1>Dashboard Content</h1>
            <p>This is the main content area. It should fill the entire remaining space with no gaps.</p>
        </div>
    </div>
</body>
</html>
