{% extends "base.html" %}
{% load static %}

{% block title %}Advanced Search - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .search-container {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 100vh;
    }

    .search-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .search-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 8s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .search-main {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        margin-bottom: 30px;
    }

    .search-box {
        position: relative;
        margin-bottom: 30px;
    }

    .search-input {
        width: 100%;
        padding: 20px 60px 20px 20px;
        border: 3px solid #e9ecef;
        border-radius: 50px;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .search-input:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        outline: none;
    }

    .search-btn {
        position: absolute;
        right: 5px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        border-radius: 50px;
        width: 50px;
        height: 50px;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .search-btn:hover {
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
    }

    .search-filters {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
        padding: 25px;
        background: #f8f9fa;
        border-radius: 15px;
        border: 1px solid #dee2e6;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .filter-label {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .filter-select {
        padding: 10px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        background: white;
        font-size: 0.95rem;
        transition: all 0.3s ease;
    }

    .filter-select:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        outline: none;
    }

    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }

    .search-suggestions.show {
        display: block;
    }

    .suggestion-item {
        padding: 15px 20px;
        border-bottom: 1px solid #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .suggestion-item:hover {
        background: #f8f9fa;
    }

    .suggestion-item:last-child {
        border-bottom: none;
    }

    .suggestion-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .suggestion-content {
        flex: 1;
    }

    .suggestion-title {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 2px;
    }

    .suggestion-subtitle {
        font-size: 0.85rem;
        color: #636e72;
    }

    .search-results {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
    }

    .result-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: 1px solid #e9ecef;
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .result-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        border-color: #28a745;
    }

    .result-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #28a745, #20c997);
    }

    .result-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .result-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.3rem;
    }

    .result-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .result-subtitle {
        color: #636e72;
        font-size: 0.9rem;
    }

    .result-details {
        margin-bottom: 20px;
    }

    .result-detail {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .result-detail:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .result-label {
        font-weight: 500;
        color: #495057;
    }

    .result-value {
        color: #2d3436;
        font-weight: 600;
    }

    .result-actions {
        display: flex;
        gap: 10px;
    }

    .result-btn {
        flex: 1;
        padding: 10px 15px;
        border: 2px solid #28a745;
        border-radius: 10px;
        background: white;
        color: #28a745;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        font-size: 0.9rem;
    }

    .result-btn:hover {
        background: #28a745;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .result-btn.primary {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        border-color: transparent;
    }

    .result-btn.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
    }

    .search-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding: 15px 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border: 1px solid #dee2e6;
    }

    .search-count {
        font-weight: 600;
        color: #495057;
    }

    .search-time {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .no-results {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-results i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }

    .no-results h3 {
        margin-bottom: 15px;
        color: #495057;
    }

    .no-results p {
        margin-bottom: 25px;
        font-size: 1.1rem;
    }

    .search-tips {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: 1px solid #2196f3;
        border-radius: 15px;
        padding: 20px;
        margin-top: 30px;
    }

    .search-tips h4 {
        color: #1976d2;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .search-tips ul {
        margin: 0;
        padding-left: 20px;
    }

    .search-tips li {
        margin-bottom: 8px;
        color: #1565c0;
    }

    /* Loading Animation */
    .search-loading {
        display: none;
        text-align: center;
        padding: 40px;
    }

    .search-loading.show {
        display: block;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #28a745;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .search-container {
            padding: 15px;
        }

        .search-main {
            padding: 25px;
        }

        .search-filters {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .search-results {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .result-card {
            padding: 20px;
        }

        .result-actions {
            flex-direction: column;
        }

        .search-stats {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="search-container">
    <!-- Header -->
    <div class="search-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">🔍 Advanced Search</h1>
                <p class="mb-0" style="opacity: 0.9;">Find farms, ponds, workers, and more with powerful search capabilities</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light" onclick="clearSearch()">
                    <i class="fas fa-eraser me-2"></i> Clear
                </button>
                <button class="btn btn-outline-light" onclick="showSearchTips()">
                    <i class="fas fa-question-circle me-2"></i> Help
                </button>
            </div>
        </div>
    </div>

    <!-- Main Search -->
    <div class="search-main">
        <!-- Search Box -->
        <div class="search-box">
            <input type="text" class="search-input" id="search-input"
                   placeholder="Search for farms, ponds, workers, locations..."
                   autocomplete="off">
            <button class="search-btn" onclick="performSearch()">
                <i class="fas fa-search"></i>
            </button>

            <!-- Search Suggestions -->
            <div class="search-suggestions" id="search-suggestions">
                <!-- Suggestions will be populated here -->
            </div>
        </div>

        <!-- Search Filters -->
        <div class="search-filters">
            <div class="filter-group">
                <label class="filter-label">Category</label>
                <select class="filter-select" id="category-filter">
                    <option value="">All Categories</option>
                    <option value="farms">Farms</option>
                    <option value="ponds">Ponds</option>
                    <option value="workers">Workers</option>
                    <option value="equipment">Equipment</option>
                    <option value="locations">Locations</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Location</label>
                <select class="filter-select" id="location-filter">
                    <option value="">All Locations</option>
                    <option value="chennai">Chennai</option>
                    <option value="coimbatore">Coimbatore</option>
                    <option value="madurai">Madurai</option>
                    <option value="salem">Salem</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select class="filter-select" id="status-filter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="maintenance">Maintenance</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <select class="filter-select" id="date-filter">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Sort By</label>
                <select class="filter-select" id="sort-filter">
                    <option value="relevance">Relevance</option>
                    <option value="name">Name A-Z</option>
                    <option value="date">Date Created</option>
                    <option value="size">Size</option>
                    <option value="location">Location</option>
                </select>
            </div>
        </div>

        <!-- Search Stats -->
        <div class="search-stats" id="search-stats" style="display: none;">
            <div class="search-count" id="search-count">0 results found</div>
            <div class="search-time" id="search-time">Search completed in 0.00 seconds</div>
        </div>

        <!-- Loading -->
        <div class="search-loading" id="search-loading">
            <div class="loading-spinner"></div>
            <p>Searching...</p>
        </div>

        <!-- Search Results -->
        <div class="search-results" id="search-results">
            <!-- Results will be populated here -->
        </div>

        <!-- No Results -->
        <div class="no-results" id="no-results" style="display: none;">
            <i class="fas fa-search"></i>
            <h3>No results found</h3>
            <p>Try adjusting your search terms or filters</p>
            <button class="btn btn-primary" onclick="clearSearch()">
                <i class="fas fa-undo me-2"></i> Clear Search
            </button>
        </div>
    </div>

    <!-- Search Tips -->
    <div class="search-tips" id="search-tips" style="display: none;">
        <h4><i class="fas fa-lightbulb"></i> Search Tips</h4>
        <ul>
            <li>Use quotes for exact phrases: "pond management"</li>
            <li>Use wildcards: farm* will find farm, farms, farming</li>
            <li>Combine terms: farm AND pond</li>
            <li>Exclude terms: pond NOT maintenance</li>
            <li>Search by location: farms in Chennai</li>
            <li>Search by date: created:2024</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let searchTimeout;
    let currentSearchQuery = '';
    let searchResults = [];

    // Initialize search system
    document.addEventListener('DOMContentLoaded', function() {
        setupSearchListeners();
        loadRecentSearches();
    });

    function setupSearchListeners() {
        const searchInput = document.getElementById('search-input');
        const filters = document.querySelectorAll('.filter-select');

        // Search input listeners
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2) {
                    showSuggestions(this.value);
                } else {
                    hideSuggestions();
                }
            }, 300);
        });

        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                showSuggestions(this.value);
            }
        });

        // Filter listeners
        filters.forEach(filter => {
            filter.addEventListener('change', function() {
                if (currentSearchQuery) {
                    performSearch();
                }
            });
        });

        // Click outside to hide suggestions
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.search-box')) {
                hideSuggestions();
            }
        });
    }

    function performSearch() {
        const query = document.getElementById('search-input').value.trim();
        if (!query) return;

        currentSearchQuery = query;

        // Show loading
        showLoading();
        hideResults();
        hideNoResults();

        // Get filter values
        const filters = {
            category: document.getElementById('category-filter').value,
            location: document.getElementById('location-filter').value,
            status: document.getElementById('status-filter').value,
            date: document.getElementById('date-filter').value,
            sort: document.getElementById('sort-filter').value
        };

        // Simulate search API call
        setTimeout(() => {
            const results = mockSearch(query, filters);
            displayResults(results);
            hideLoading();
        }, 1000);

        hideSuggestions();
    }

    function mockSearch(query, filters) {
        // Mock search results - replace with actual API call
        const mockData = [
            {
                type: 'farm',
                id: 1,
                title: 'Green Valley Farm',
                subtitle: 'Chennai, Tamil Nadu',
                details: {
                    'Size': '25 hectares',
                    'Ponds': '12 active',
                    'Workers': '8 assigned',
                    'Status': 'Active'
                },
                url: '/ponds/farm/1/'
            },
            {
                type: 'pond',
                id: 1,
                title: 'Pond Alpha-1',
                subtitle: 'Green Valley Farm',
                details: {
                    'Size': '2.5 hectares',
                    'Species': 'Litopenaeus vannamei',
                    'Water Quality': 'Good',
                    'Status': 'Active'
                },
                url: '/ponds/pond/1/'
            },
            {
                type: 'worker',
                id: 1,
                title: 'Rajesh Kumar',
                subtitle: 'Farm Manager',
                details: {
                    'Farm': 'Green Valley Farm',
                    'Experience': '5 years',
                    'Specialization': 'Water Quality',
                    'Status': 'Active'
                },
                url: '/workers/1/'
            }
        ];

        // Filter results based on query and filters
        let results = mockData.filter(item => {
            const matchesQuery = item.title.toLowerCase().includes(query.toLowerCase()) ||
                                item.subtitle.toLowerCase().includes(query.toLowerCase());

            const matchesCategory = !filters.category || item.type === filters.category;

            return matchesQuery && matchesCategory;
        });

        return results;
    }

    function displayResults(results) {
        const resultsContainer = document.getElementById('search-results');
        const statsContainer = document.getElementById('search-stats');
        const countElement = document.getElementById('search-count');
        const timeElement = document.getElementById('search-time');

        searchResults = results;

        if (results.length === 0) {
            showNoResults();
            statsContainer.style.display = 'none';
            return;
        }

        // Update stats
        countElement.textContent = `${results.length} result${results.length !== 1 ? 's' : ''} found`;
        timeElement.textContent = 'Search completed in 0.85 seconds';
        statsContainer.style.display = 'flex';

        // Generate results HTML
        const resultsHTML = results.map(result => `
            <div class="result-card">
                <div class="result-header">
                    <div class="result-icon">
                        <i class="fas ${getResultIcon(result.type)}"></i>
                    </div>
                    <div>
                        <div class="result-title">${result.title}</div>
                        <div class="result-subtitle">${result.subtitle}</div>
                    </div>
                </div>
                <div class="result-details">
                    ${Object.entries(result.details).map(([key, value]) => `
                        <div class="result-detail">
                            <span class="result-label">${key}:</span>
                            <span class="result-value">${value}</span>
                        </div>
                    `).join('')}
                </div>
                <div class="result-actions">
                    <a href="${result.url}" class="result-btn primary">
                        <i class="fas fa-eye me-1"></i> View Details
                    </a>
                    <button class="result-btn" onclick="addToFavorites(${result.id}, '${result.type}')">
                        <i class="fas fa-heart me-1"></i> Favorite
                    </button>
                </div>
            </div>
        `).join('');

        resultsContainer.innerHTML = resultsHTML;
        showResults();
    }

    function getResultIcon(type) {
        const icons = {
            farm: 'fa-building',
            pond: 'fa-water',
            worker: 'fa-user',
            equipment: 'fa-cog',
            location: 'fa-map-marker-alt'
        };
        return icons[type] || 'fa-circle';
    }

    function showSuggestions(query) {
        const suggestionsContainer = document.getElementById('search-suggestions');

        // Mock suggestions - replace with actual API call
        const suggestions = [
            { type: 'farm', title: 'Green Valley Farm', subtitle: 'Chennai, Tamil Nadu' },
            { type: 'pond', title: 'Pond Alpha-1', subtitle: 'Green Valley Farm' },
            { type: 'worker', title: 'Rajesh Kumar', subtitle: 'Farm Manager' }
        ].filter(item =>
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.subtitle.toLowerCase().includes(query.toLowerCase())
        );

        if (suggestions.length === 0) {
            hideSuggestions();
            return;
        }

        const suggestionsHTML = suggestions.map(suggestion => `
            <div class="suggestion-item" onclick="selectSuggestion('${suggestion.title}')">
                <div class="suggestion-icon">
                    <i class="fas ${getResultIcon(suggestion.type)}"></i>
                </div>
                <div class="suggestion-content">
                    <div class="suggestion-title">${suggestion.title}</div>
                    <div class="suggestion-subtitle">${suggestion.subtitle}</div>
                </div>
            </div>
        `).join('');

        suggestionsContainer.innerHTML = suggestionsHTML;
        suggestionsContainer.classList.add('show');
    }

    function hideSuggestions() {
        document.getElementById('search-suggestions').classList.remove('show');
    }

    function selectSuggestion(title) {
        document.getElementById('search-input').value = title;
        hideSuggestions();
        performSearch();
    }

    function showLoading() {
        document.getElementById('search-loading').classList.add('show');
    }

    function hideLoading() {
        document.getElementById('search-loading').classList.remove('show');
    }

    function showResults() {
        document.getElementById('search-results').style.display = 'grid';
    }

    function hideResults() {
        document.getElementById('search-results').style.display = 'none';
    }

    function showNoResults() {
        document.getElementById('no-results').style.display = 'block';
    }

    function hideNoResults() {
        document.getElementById('no-results').style.display = 'none';
    }

    function clearSearch() {
        document.getElementById('search-input').value = '';
        document.querySelectorAll('.filter-select').forEach(select => select.value = '');
        hideResults();
        hideNoResults();
        document.getElementById('search-stats').style.display = 'none';
        hideSuggestions();
        currentSearchQuery = '';
    }

    function showSearchTips() {
        const tips = document.getElementById('search-tips');
        tips.style.display = tips.style.display === 'none' ? 'block' : 'none';
    }

    function addToFavorites(id, type) {
        // Implementation for adding to favorites
        alert(`Added ${type} ${id} to favorites`);
    }

    function loadRecentSearches() {
        // Implementation for loading recent searches
        console.log('Loading recent searches...');
    }
</script>
{% endblock %}