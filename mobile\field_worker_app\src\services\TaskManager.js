/**
 * Task Management System for Field Workers
 * Handles task assignment, tracking, and completion with offline support
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import moment from 'moment';
import { Alert } from 'react-native';

export const TaskStatus = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  OVERDUE: 'overdue',
  CANCELLED: 'cancelled'
};

export const TaskPriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent'
};

export const TaskType = {
  WATER_QUALITY_CHECK: 'water_quality_check',
  FEEDING: 'feeding',
  POND_CLEANING: 'pond_cleaning',
  EQUIPMENT_MAINTENANCE: 'equipment_maintenance',
  HARVEST_PREPARATION: 'harvest_preparation',
  DISEASE_INSPECTION: 'disease_inspection',
  INVENTORY_CHECK: 'inventory_check',
  REPORT_SUBMISSION: 'report_submission'
};

class TaskManager {
  constructor() {
    this.tasks = [];
    this.syncQueue = [];
    this.isOnline = true;
    this.syncInProgress = false;
    
    this.initializeNetworkListener();
    this.loadTasksFromStorage();
  }

  /**
   * Initialize network connectivity listener
   */
  initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOffline = !this.isOnline;
      this.isOnline = state.isConnected;
      
      if (wasOffline && this.isOnline) {
        console.log('Network restored, syncing tasks...');
        this.syncTasks();
      }
    });
  }

  /**
   * Load tasks from local storage
   */
  async loadTasksFromStorage() {
    try {
      const storedTasks = await AsyncStorage.getItem('field_worker_tasks');
      const storedSyncQueue = await AsyncStorage.getItem('task_sync_queue');
      
      if (storedTasks) {
        this.tasks = JSON.parse(storedTasks);
      }
      
      if (storedSyncQueue) {
        this.syncQueue = JSON.parse(storedSyncQueue);
      }
      
      // Update overdue tasks
      this.updateOverdueTasks();
      
    } catch (error) {
      console.error('Error loading tasks from storage:', error);
    }
  }

  /**
   * Save tasks to local storage
   */
  async saveTasksToStorage() {
    try {
      await AsyncStorage.setItem('field_worker_tasks', JSON.stringify(this.tasks));
      await AsyncStorage.setItem('task_sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Error saving tasks to storage:', error);
    }
  }

  /**
   * Create a new task
   */
  async createTask(taskData) {
    const task = {
      id: this.generateTaskId(),
      ...taskData,
      status: TaskStatus.PENDING,
      createdAt: moment().toISOString(),
      updatedAt: moment().toISOString(),
      localChanges: true,
      syncStatus: 'pending'
    };

    this.tasks.push(task);
    await this.saveTasksToStorage();
    
    // Add to sync queue if online
    if (this.isOnline) {
      this.addToSyncQueue('create', task);
    }
    
    return task;
  }

  /**
   * Update task status and details
   */
  async updateTask(taskId, updates) {
    const taskIndex = this.tasks.findIndex(task => task.id === taskId);
    
    if (taskIndex === -1) {
      throw new Error('Task not found');
    }

    const task = this.tasks[taskIndex];
    const updatedTask = {
      ...task,
      ...updates,
      updatedAt: moment().toISOString(),
      localChanges: true,
      syncStatus: 'pending'
    };

    this.tasks[taskIndex] = updatedTask;
    await this.saveTasksToStorage();
    
    // Add to sync queue if online
    if (this.isOnline) {
      this.addToSyncQueue('update', updatedTask);
    }
    
    return updatedTask;
  }

  /**
   * Start working on a task
   */
  async startTask(taskId) {
    const updates = {
      status: TaskStatus.IN_PROGRESS,
      startedAt: moment().toISOString()
    };
    
    return await this.updateTask(taskId, updates);
  }

  /**
   * Complete a task with results
   */
  async completeTask(taskId, completionData = {}) {
    const updates = {
      status: TaskStatus.COMPLETED,
      completedAt: moment().toISOString(),
      completionData,
      completionNotes: completionData.notes || ''
    };
    
    return await this.updateTask(taskId, updates);
  }

  /**
   * Cancel a task
   */
  async cancelTask(taskId, reason = '') {
    const updates = {
      status: TaskStatus.CANCELLED,
      cancelledAt: moment().toISOString(),
      cancellationReason: reason
    };
    
    return await this.updateTask(taskId, updates);
  }

  /**
   * Get tasks with filtering options
   */
  getTasks(filters = {}) {
    let filteredTasks = [...this.tasks];

    // Filter by status
    if (filters.status) {
      filteredTasks = filteredTasks.filter(task => task.status === filters.status);
    }

    // Filter by priority
    if (filters.priority) {
      filteredTasks = filteredTasks.filter(task => task.priority === filters.priority);
    }

    // Filter by type
    if (filters.type) {
      filteredTasks = filteredTasks.filter(task => task.type === filters.type);
    }

    // Filter by date range
    if (filters.dateFrom) {
      filteredTasks = filteredTasks.filter(task => 
        moment(task.dueDate).isAfter(moment(filters.dateFrom))
      );
    }

    if (filters.dateTo) {
      filteredTasks = filteredTasks.filter(task => 
        moment(task.dueDate).isBefore(moment(filters.dateTo))
      );
    }

    // Filter by pond
    if (filters.pondId) {
      filteredTasks = filteredTasks.filter(task => task.pondId === filters.pondId);
    }

    // Sort by priority and due date
    filteredTasks.sort((a, b) => {
      const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      
      if (priorityDiff !== 0) return priorityDiff;
      
      return moment(a.dueDate).diff(moment(b.dueDate));
    });

    return filteredTasks;
  }

  /**
   * Get today's tasks
   */
  getTodaysTasks() {
    const today = moment().startOf('day');
    const tomorrow = moment().add(1, 'day').startOf('day');
    
    return this.tasks.filter(task => {
      const dueDate = moment(task.dueDate);
      return dueDate.isSameOrAfter(today) && dueDate.isBefore(tomorrow);
    });
  }

  /**
   * Get overdue tasks
   */
  getOverdueTasks() {
    return this.tasks.filter(task => 
      task.status !== TaskStatus.COMPLETED && 
      task.status !== TaskStatus.CANCELLED &&
      moment(task.dueDate).isBefore(moment())
    );
  }

  /**
   * Get task statistics
   */
  getTaskStatistics() {
    const total = this.tasks.length;
    const completed = this.tasks.filter(task => task.status === TaskStatus.COMPLETED).length;
    const inProgress = this.tasks.filter(task => task.status === TaskStatus.IN_PROGRESS).length;
    const overdue = this.getOverdueTasks().length;
    const pending = this.tasks.filter(task => task.status === TaskStatus.PENDING).length;

    return {
      total,
      completed,
      inProgress,
      overdue,
      pending,
      completionRate: total > 0 ? (completed / total * 100).toFixed(1) : 0
    };
  }

  /**
   * Update overdue tasks
   */
  updateOverdueTasks() {
    const now = moment();
    let hasChanges = false;

    this.tasks.forEach(task => {
      if (task.status !== TaskStatus.COMPLETED && 
          task.status !== TaskStatus.CANCELLED &&
          task.status !== TaskStatus.OVERDUE &&
          moment(task.dueDate).isBefore(now)) {
        task.status = TaskStatus.OVERDUE;
        task.updatedAt = now.toISOString();
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.saveTasksToStorage();
    }
  }

  /**
   * Add task to sync queue
   */
  addToSyncQueue(action, task) {
    const syncItem = {
      id: this.generateSyncId(),
      action,
      task,
      timestamp: moment().toISOString(),
      retryCount: 0
    };

    this.syncQueue.push(syncItem);
    this.saveTasksToStorage();
    
    // Trigger sync if not already in progress
    if (!this.syncInProgress) {
      this.syncTasks();
    }
  }

  /**
   * Sync tasks with server
   */
  async syncTasks() {
    if (!this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`Syncing ${this.syncQueue.length} tasks...`);

    const failedSyncs = [];

    for (const syncItem of this.syncQueue) {
      try {
        await this.syncSingleTask(syncItem);
        console.log(`Synced task: ${syncItem.task.id}`);
      } catch (error) {
        console.error(`Failed to sync task ${syncItem.task.id}:`, error);
        syncItem.retryCount++;
        
        if (syncItem.retryCount < 3) {
          failedSyncs.push(syncItem);
        } else {
          console.error(`Max retries reached for task ${syncItem.task.id}`);
        }
      }
    }

    // Update sync queue with failed items
    this.syncQueue = failedSyncs;
    await this.saveTasksToStorage();
    
    this.syncInProgress = false;
    
    if (failedSyncs.length > 0) {
      console.log(`${failedSyncs.length} tasks failed to sync, will retry later`);
    }
  }

  /**
   * Sync single task with server
   */
  async syncSingleTask(syncItem) {
    const { action, task } = syncItem;
    
    // This would make actual API calls to the server
    // For now, simulate API calls
    
    const apiEndpoint = 'https://api.shrimp-farm.com/field-worker/tasks';
    
    switch (action) {
      case 'create':
        // await axios.post(apiEndpoint, task);
        console.log('API: Creating task', task.id);
        break;
        
      case 'update':
        // await axios.put(`${apiEndpoint}/${task.id}`, task);
        console.log('API: Updating task', task.id);
        break;
        
      case 'delete':
        // await axios.delete(`${apiEndpoint}/${task.id}`);
        console.log('API: Deleting task', task.id);
        break;
    }
    
    // Update local task sync status
    const localTask = this.tasks.find(t => t.id === task.id);
    if (localTask) {
      localTask.syncStatus = 'synced';
      localTask.localChanges = false;
    }
  }

  /**
   * Generate unique task ID
   */
  generateTaskId() {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique sync ID
   */
  generateSyncId() {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get task by ID
   */
  getTaskById(taskId) {
    return this.tasks.find(task => task.id === taskId);
  }

  /**
   * Delete task
   */
  async deleteTask(taskId) {
    const taskIndex = this.tasks.findIndex(task => task.id === taskId);
    
    if (taskIndex === -1) {
      throw new Error('Task not found');
    }

    const task = this.tasks[taskIndex];
    this.tasks.splice(taskIndex, 1);
    
    await this.saveTasksToStorage();
    
    // Add to sync queue if online
    if (this.isOnline) {
      this.addToSyncQueue('delete', task);
    }
    
    return true;
  }

  /**
   * Bulk update tasks
   */
  async bulkUpdateTasks(taskIds, updates) {
    const updatedTasks = [];
    
    for (const taskId of taskIds) {
      try {
        const updatedTask = await this.updateTask(taskId, updates);
        updatedTasks.push(updatedTask);
      } catch (error) {
        console.error(`Failed to update task ${taskId}:`, error);
      }
    }
    
    return updatedTasks;
  }

  /**
   * Get sync status
   */
  getSyncStatus() {
    const pendingSync = this.syncQueue.length;
    const localChanges = this.tasks.filter(task => task.localChanges).length;
    
    return {
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
      pendingSync,
      localChanges,
      lastSyncAttempt: this.lastSyncAttempt
    };
  }

  /**
   * Force sync
   */
  async forceSync() {
    if (!this.isOnline) {
      Alert.alert('Offline', 'Cannot sync while offline. Please check your internet connection.');
      return false;
    }
    
    await this.syncTasks();
    return true;
  }
}

// Export singleton instance
export default new TaskManager();
