/**
 * Task Card Component
 * Displays individual task information with actions
 */

import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import moment from 'moment';
import { TaskStatus, TaskPriority, TaskType } from '../services/TaskManager';

const TaskCard = ({ 
  task, 
  onPress, 
  onStart, 
  onComplete, 
  onCancel,
  showActions = true 
}) => {
  
  const getStatusColor = (status) => {
    switch (status) {
      case TaskStatus.PENDING:
        return '#FFA500';
      case TaskStatus.IN_PROGRESS:
        return '#2196F3';
      case TaskStatus.COMPLETED:
        return '#4CAF50';
      case TaskStatus.OVERDUE:
        return '#F44336';
      case TaskStatus.CANCELLED:
        return '#9E9E9E';
      default:
        return '#757575';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case TaskPriority.URGENT:
        return '#D32F2F';
      case TaskPriority.HIGH:
        return '#F57C00';
      case TaskPriority.MEDIUM:
        return '#1976D2';
      case TaskPriority.LOW:
        return '#388E3C';
      default:
        return '#757575';
    }
  };

  const getTaskTypeIcon = (type) => {
    switch (type) {
      case TaskType.WATER_QUALITY_CHECK:
        return 'water-drop';
      case TaskType.FEEDING:
        return 'restaurant';
      case TaskType.POND_CLEANING:
        return 'cleaning-services';
      case TaskType.EQUIPMENT_MAINTENANCE:
        return 'build';
      case TaskType.HARVEST_PREPARATION:
        return 'agriculture';
      case TaskType.DISEASE_INSPECTION:
        return 'medical-services';
      case TaskType.INVENTORY_CHECK:
        return 'inventory';
      case TaskType.REPORT_SUBMISSION:
        return 'description';
      default:
        return 'task';
    }
  };

  const formatDueDate = (dueDate) => {
    const due = moment(dueDate);
    const now = moment();
    
    if (due.isSame(now, 'day')) {
      return `Today ${due.format('HH:mm')}`;
    } else if (due.isSame(now.add(1, 'day'), 'day')) {
      return `Tomorrow ${due.format('HH:mm')}`;
    } else if (due.isBefore(now)) {
      return `Overdue ${due.fromNow()}`;
    } else {
      return due.format('MMM DD, HH:mm');
    }
  };

  const isOverdue = moment(task.dueDate).isBefore(moment()) && 
                   task.status !== TaskStatus.COMPLETED && 
                   task.status !== TaskStatus.CANCELLED;

  const canStart = task.status === TaskStatus.PENDING || task.status === TaskStatus.OVERDUE;
  const canComplete = task.status === TaskStatus.IN_PROGRESS;
  const canCancel = task.status === TaskStatus.PENDING || 
                   task.status === TaskStatus.IN_PROGRESS || 
                   task.status === TaskStatus.OVERDUE;

  const handleStart = () => {
    Alert.alert(
      'Start Task',
      `Are you sure you want to start "${task.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Start', onPress: () => onStart(task.id) }
      ]
    );
  };

  const handleComplete = () => {
    onComplete(task.id);
  };

  const handleCancel = () => {
    Alert.alert(
      'Cancel Task',
      `Are you sure you want to cancel "${task.title}"?`,
      [
        { text: 'No', style: 'cancel' },
        { 
          text: 'Yes', 
          style: 'destructive',
          onPress: () => onCancel(task.id) 
        }
      ]
    );
  };

  return (
    <TouchableOpacity 
      style={[
        styles.container,
        isOverdue && styles.overdueContainer
      ]} 
      onPress={() => onPress(task)}
      activeOpacity={0.7}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Icon 
            name={getTaskTypeIcon(task.type)} 
            size={24} 
            color="#333" 
            style={styles.typeIcon}
          />
          <Text style={styles.title} numberOfLines={2}>
            {task.title}
          </Text>
          <View style={[
            styles.priorityBadge,
            { backgroundColor: getPriorityColor(task.priority) }
          ]}>
            <Text style={styles.priorityText}>
              {task.priority.toUpperCase()}
            </Text>
          </View>
        </View>
        
        <View style={styles.statusRow}>
          <View style={[
            styles.statusBadge,
            { backgroundColor: getStatusColor(task.status) }
          ]}>
            <Text style={styles.statusText}>
              {task.status.replace('_', ' ').toUpperCase()}
            </Text>
          </View>
          {task.localChanges && (
            <Icon name="sync-problem" size={16} color="#FF9800" />
          )}
        </View>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {task.description && (
          <Text style={styles.description} numberOfLines={2}>
            {task.description}
          </Text>
        )}
        
        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Icon name="schedule" size={16} color="#666" />
            <Text style={[
              styles.detailText,
              isOverdue && styles.overdueText
            ]}>
              {formatDueDate(task.dueDate)}
            </Text>
          </View>
          
          {task.pondName && (
            <View style={styles.detailItem}>
              <Icon name="water" size={16} color="#666" />
              <Text style={styles.detailText}>
                {task.pondName}
              </Text>
            </View>
          )}
          
          {task.estimatedDuration && (
            <View style={styles.detailItem}>
              <Icon name="timer" size={16} color="#666" />
              <Text style={styles.detailText}>
                {task.estimatedDuration} min
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Actions */}
      {showActions && (
        <View style={styles.actions}>
          {canStart && (
            <TouchableOpacity 
              style={[styles.actionButton, styles.startButton]}
              onPress={handleStart}
            >
              <Icon name="play-arrow" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Start</Text>
            </TouchableOpacity>
          )}
          
          {canComplete && (
            <TouchableOpacity 
              style={[styles.actionButton, styles.completeButton]}
              onPress={handleComplete}
            >
              <Icon name="check" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Complete</Text>
            </TouchableOpacity>
          )}
          
          {canCancel && (
            <TouchableOpacity 
              style={[styles.actionButton, styles.cancelButton]}
              onPress={handleCancel}
            >
              <Icon name="close" size={20} color="#fff" />
              <Text style={styles.actionButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    marginHorizontal: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  overdueContainer: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  header: {
    marginBottom: 12,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  typeIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  title: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    lineHeight: 22,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#fff',
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
  content: {
    marginBottom: 12,
  },
  description: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  details: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  overdueText: {
    color: '#F44336',
    fontWeight: '600',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 4,
  },
  startButton: {
    backgroundColor: '#2196F3',
  },
  completeButton: {
    backgroundColor: '#4CAF50',
  },
  cancelButton: {
    backgroundColor: '#F44336',
  },
  actionButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#fff',
  },
});

export default TaskCard;
