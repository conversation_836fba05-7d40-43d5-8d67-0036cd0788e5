"""
Health check views for Shrimp Farm Guardian project.
"""

import json
import time
import logging
from django.http import JsonResponse
from django.db import connections
from django.db.utils import OperationalError
from django.core.cache import cache
from django.views.decorators.cache import never_cache
from django.conf import settings
from redis.exceptions import RedisError

logger = logging.getLogger(__name__)


@never_cache
def health_check(request):
    """
    Health check endpoint for the application.
    Checks database and cache connections.
    Returns a 200 OK response if all systems are operational, or a 503 Service Unavailable
    if any component is not functioning properly.
    """
    start_time = time.time()
    health_status = {
        'status': 'ok',
        'components': {
            'database': check_database(),
            'cache': check_cache(),
            'system': check_system_resources(),
            'application': check_application_health(),
        },
        'version': getattr(settings, 'APP_VERSION', '1.0.0'),
        'environment': getattr(settings, 'SENTRY_ENVIRONMENT', 'development'),
        'response_time_ms': 0,
    }
    
    # If any component is not healthy, set overall status to error
    if any(component.get('status') != 'ok' for component in health_status['components'].values()):
        health_status['status'] = 'error'
        logger.error(f"Health check failed: {json.dumps(health_status)}")
        status_code = 503  # Service Unavailable
    else:
        status_code = 200  # OK
    
    # Calculate response time
    health_status['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
    
    return JsonResponse(health_status, status=status_code)


def check_database():
    """Check if the database connection is working."""
    try:
        # Try to get a connection to the database
        db_conn = connections['default']
        db_conn.cursor()
        return {'status': 'ok'}
    except OperationalError as e:
        logger.error(f"Database health check failed: {str(e)}")
        return {
            'status': 'error',
            'message': 'Database connection failed',
            'error': str(e)
        }


def check_cache():
    """Check if the cache connection is working."""
    try:
        # Try to set and get a value from the cache
        cache.set('health_check', 'ok', 10)
        result = cache.get('health_check')
        
        if result == 'ok':
            return {'status': 'ok'}
        else:
            logger.error("Cache health check failed: value not retrieved correctly")
            return {
                'status': 'error',
                'message': 'Cache value not retrieved correctly'
            }
    except RedisError as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return {
            'status': 'error',
            'message': 'Cache connection failed',
            'error': str(e)
        }
    except Exception as e:
        logger.error(f"Cache health check failed: {str(e)}")
        return {
            'status': 'error',
            'message': 'Cache connection failed',
            'error': str(e)
        }


def check_system_resources():
    """Check system resource usage."""
    try:
        import psutil

        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        status = 'ok'
        warnings = []

        # Check for critical resource usage
        if cpu_percent > 90:
            status = 'error'
            warnings.append(f'High CPU usage: {cpu_percent}%')
        elif cpu_percent > 80:
            status = 'warning'
            warnings.append(f'Elevated CPU usage: {cpu_percent}%')

        if memory.percent > 90:
            status = 'error'
            warnings.append(f'High memory usage: {memory.percent}%')
        elif memory.percent > 80:
            status = 'warning'
            warnings.append(f'Elevated memory usage: {memory.percent}%')

        if disk.percent > 90:
            status = 'error'
            warnings.append(f'High disk usage: {disk.percent}%')
        elif disk.percent > 80:
            status = 'warning'
            warnings.append(f'Elevated disk usage: {disk.percent}%')

        result = {
            'status': status,
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'disk_percent': disk.percent,
            'available_memory_gb': round(memory.available / (1024**3), 2),
            'available_disk_gb': round(disk.free / (1024**3), 2)
        }

        if warnings:
            result['warnings'] = warnings

        return result

    except ImportError:
        return {
            'status': 'warning',
            'message': 'psutil not installed - system monitoring unavailable'
        }
    except Exception as e:
        logger.error(f"System resource check failed: {str(e)}")
        return {
            'status': 'error',
            'message': 'System resource check failed',
            'error': str(e)
        }


def check_application_health():
    """Check application-specific health indicators."""
    try:
        # Check if critical models can be accessed
        from django.contrib.auth.models import User

        # Test basic model access
        user_count = User.objects.count()

        # Check if we can access pond models
        try:
            from ponds.models import Pond
            pond_count = Pond.objects.count()
        except:
            pond_count = 'N/A'

        return {
            'status': 'ok',
            'user_count': user_count,
            'pond_count': pond_count,
            'models_accessible': True
        }

    except Exception as e:
        logger.error(f"Application health check failed: {str(e)}")
        return {
            'status': 'error',
            'message': 'Application health check failed',
            'error': str(e),
            'models_accessible': False
        }
