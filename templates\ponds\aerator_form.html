{% extends 'base.html' %}

{% block title %}{{ title }} - {{ pond.name }}{% endblock %}

{% block extra_css %}
<style>
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 30px;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    }

    .form-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border-left: 5px solid #667eea;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .form-section h5 {
        color: #667eea;
        margin-bottom: 15px;
        font-weight: bold;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .helper-box {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        border-left: 4px solid #2196f3;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="form-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-fan"></i> {{ title }}</h1>
                <p class="mb-2">{{ pond.name }} - Configure aerator settings and specifications</p>
                <span class="badge bg-light text-dark">
                    <i class="fas fa-water"></i> Pond Size: {{ pond.size }} m²
                </span>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'ponds:aerator_management' pond.pk %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to Aerators
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="form-card">
        <form method="post" id="aeratorForm">
            {% csrf_token %}
            
            <!-- Basic Information -->
            <div class="form-section">
                <h5><i class="fas fa-info-circle"></i> Basic Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                <strong>Aerator Name</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.aerator_type.id_for_label }}" class="form-label">
                                <strong>Aerator Type</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.aerator_type }}
                            {% if form.aerator_type.errors %}
                                <div class="text-danger">{{ form.aerator_type.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.status.id_for_label }}" class="form-label">
                                <strong>Status</strong>
                            </label>
                            {{ form.status }}
                            {% if form.status.errors %}
                                <div class="text-danger">{{ form.status.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <div class="form-check">
                                {{ form.is_automated }}
                                <label class="form-check-label" for="{{ form.is_automated.id_for_label }}">
                                    <strong>Enable automated operation via IoT</strong>
                                </label>
                            </div>
                            {% if form.is_automated.errors %}
                                <div class="text-danger">{{ form.is_automated.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Power Specifications -->
            <div class="form-section">
                <h5><i class="fas fa-bolt"></i> Power Specifications</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.power_rating.id_for_label }}" class="form-label">
                                <strong>Power Rating (HP/kW)</strong>
                            </label>
                            {{ form.power_rating }}
                            {% if form.power_rating.errors %}
                                <div class="text-danger">{{ form.power_rating.errors }}</div>
                            {% endif %}
                            <div class="helper-box">
                                <small><strong>💡 Power Calculator:</strong></small><br>
                                <small>Recommended: 1-2 HP per 1000 m² for paddle wheel aerators</small><br>
                                <small>For {{ pond.size }} m² pond: Recommended 2-4 HP total</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.power_source.id_for_label }}" class="form-label">
                                <strong>Power Source</strong>
                            </label>
                            {{ form.power_source }}
                            {% if form.power_source.errors %}
                                <div class="text-danger">{{ form.power_source.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location -->
            <div class="form-section">
                <h5><i class="fas fa-map-marker-alt"></i> Location</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.latitude.id_for_label }}" class="form-label">
                                <strong>Latitude</strong>
                            </label>
                            {{ form.latitude }}
                            {% if form.latitude.errors %}
                                <div class="text-danger">{{ form.latitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.longitude.id_for_label }}" class="form-label">
                                <strong>Longitude</strong>
                            </label>
                            {{ form.longitude }}
                            {% if form.longitude.errors %}
                                <div class="text-danger">{{ form.longitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="helper-box">
                    <small><strong>📍 Location Helper:</strong></small><br>
                    <small>Pond Location: {{ pond.latitude }}, {{ pond.longitude }}</small><br>
                    <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="usePondLocation()">
                        <i class="fas fa-crosshairs"></i> Use Pond Location
                    </button>
                </div>
            </div>

            <!-- IoT Integration -->
            <div class="form-section">
                <h5><i class="fas fa-wifi"></i> IoT Integration</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.device_id.id_for_label }}" class="form-label">
                                <strong>Device ID</strong>
                            </label>
                            {{ form.device_id }}
                            <small class="form-text text-muted">Unique identifier for IoT device control</small>
                            {% if form.device_id.errors %}
                                <div class="text-danger">{{ form.device_id.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.installation_date.id_for_label }}" class="form-label">
                                <strong>Installation Date</strong>
                            </label>
                            {{ form.installation_date }}
                            {% if form.installation_date.errors %}
                                <div class="text-danger">{{ form.installation_date.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.last_maintenance.id_for_label }}" class="form-label">
                                <strong>Last Maintenance</strong>
                            </label>
                            {{ form.last_maintenance }}
                            {% if form.last_maintenance.errors %}
                                <div class="text-danger">{{ form.last_maintenance.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="form-section">
                <h5><i class="fas fa-sticky-note"></i> Additional Notes</h5>
                <div class="mb-3">
                    <label for="{{ form.notes.id_for_label }}" class="form-label">
                        <strong>Notes</strong>
                    </label>
                    {{ form.notes }}
                    <small class="form-text text-muted">Any additional information about this aerator</small>
                    {% if form.notes.errors %}
                        <div class="text-danger">{{ form.notes.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <a href="{% url 'ponds:aerator_management' pond.pk %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 
                        {% if aerator %}Update Aerator{% else %}Add Aerator{% endif %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Location helpers
function usePondLocation() {
    document.getElementById('{{ form.latitude.id_for_label }}').value = {{ pond.latitude }};
    document.getElementById('{{ form.longitude.id_for_label }}').value = {{ pond.longitude }};
    showAlert('success', 'Pond location coordinates applied');
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 5000);
}

// Form validation
document.getElementById('aeratorForm').addEventListener('submit', function(e) {
    const name = document.getElementById('{{ form.name.id_for_label }}').value;
    const type = document.getElementById('{{ form.aerator_type.id_for_label }}').value;
    
    if (!name.trim()) {
        e.preventDefault();
        showAlert('error', 'Aerator name is required');
        return;
    }
    
    if (!type) {
        e.preventDefault();
        showAlert('error', 'Please select an aerator type');
        return;
    }
});
</script>
{% endblock %}
