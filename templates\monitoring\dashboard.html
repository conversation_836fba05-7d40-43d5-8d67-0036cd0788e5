<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Shrimp Farm Guardian</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .alert-high { border-left: 4px solid #ef4444; }
        .alert-medium { border-left: 4px solid #f59e0b; }
        .alert-low { border-left: 4px solid #10b981; }
        
        .status-healthy { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-error { color: #ef4444; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">🖥️ System Monitoring Dashboard</h1>
            <p class="text-gray-600">Real-time system performance and health monitoring</p>
            <div class="mt-4">
                <span class="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                    🟢 Monitoring Active
                </span>
                <span class="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm ml-2">
                    Last Updated: <span id="last-updated">{{ current_metrics.timestamp }}</span>
                </span>
            </div>
        </div>

        <!-- System Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- CPU Usage -->
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">CPU Usage</h3>
                        <p class="text-3xl font-bold" id="cpu-usage">{{ current_metrics.system.cpu_percent|floatformat:1 }}%</p>
                    </div>
                    <div class="text-4xl">🖥️</div>
                </div>
                <div class="mt-2">
                    <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                        <div class="bg-white h-2 rounded-full" style="width: {{ current_metrics.system.cpu_percent }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Memory Usage -->
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Memory Usage</h3>
                        <p class="text-3xl font-bold" id="memory-usage">{{ current_metrics.system.memory_percent|floatformat:1 }}%</p>
                    </div>
                    <div class="text-4xl">💾</div>
                </div>
                <div class="mt-2">
                    <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                        <div class="bg-white h-2 rounded-full" style="width: {{ current_metrics.system.memory_percent }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Disk Usage -->
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Disk Usage</h3>
                        <p class="text-3xl font-bold" id="disk-usage">{{ current_metrics.system.disk_percent|floatformat:1 }}%</p>
                    </div>
                    <div class="text-4xl">💿</div>
                </div>
                <div class="mt-2">
                    <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
                        <div class="bg-white h-2 rounded-full" style="width: {{ current_metrics.system.disk_percent }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="metric-card">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold">Database</h3>
                        <p class="text-lg font-bold" id="db-status">
                            {% if current_metrics.database.status == 'healthy' %}
                                🟢 Healthy
                            {% else %}
                                🔴 Error
                            {% endif %}
                        </p>
                    </div>
                    <div class="text-4xl">🗄️</div>
                </div>
                <div class="mt-2">
                    <p class="text-sm opacity-90" id="db-connections">
                        Connections: {{ current_metrics.database.connection_count|default:"N/A" }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- CPU & Memory Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-semibold mb-4">📊 System Resources (Last Hour)</h3>
                <div class="chart-container">
                    <canvas id="systemChart"></canvas>
                </div>
            </div>

            <!-- Cache Performance Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-xl font-semibold mb-4">⚡ Cache Performance</h3>
                <div class="chart-container">
                    <canvas id="cacheChart"></canvas>
                </div>
                <div class="mt-4 grid grid-cols-2 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-green-600" id="cache-hit-rate">
                            {{ current_metrics.cache.hit_rate|floatformat:1 }}%
                        </p>
                        <p class="text-sm text-gray-600">Hit Rate</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-blue-600" id="cache-requests">
                            {{ current_metrics.cache.total_requests }}
                        </p>
                        <p class="text-sm text-gray-600">Total Requests</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts Section -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4">🚨 Recent Alerts</h3>
            <div id="alerts-container">
                {% if recent_alerts %}
                    {% for alert in recent_alerts %}
                        <div class="alert-{{ alert.severity }} bg-gray-50 p-4 mb-3 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-semibold">{{ alert.message }}</p>
                                    <p class="text-sm text-gray-600">{{ alert.timestamp }}</p>
                                </div>
                                <span class="px-2 py-1 rounded text-xs font-semibold
                                    {% if alert.severity == 'high' %}bg-red-100 text-red-800
                                    {% elif alert.severity == 'medium' %}bg-yellow-100 text-yellow-800
                                    {% else %}bg-green-100 text-green-800{% endif %}">
                                    {{ alert.severity|upper }}
                                </span>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <p class="text-gray-500 text-center py-8">✅ No recent alerts - system is running smoothly!</p>
                {% endif %}
            </div>
        </div>

        <!-- Application Metrics -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h3 class="text-xl font-semibold mb-4">📱 Application Metrics</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <p class="text-3xl font-bold text-blue-600">{{ current_metrics.application.user_count }}</p>
                    <p class="text-gray-600">Total Users</p>
                </div>
                <div class="text-center">
                    <p class="text-3xl font-bold text-green-600">{{ current_metrics.application.pond_count }}</p>
                    <p class="text-gray-600">Active Ponds</p>
                </div>
                <div class="text-center">
                    <p class="text-3xl font-bold text-purple-600" id="app-status">
                        {% if current_metrics.application.status == 'healthy' %}Healthy{% else %}Error{% endif %}
                    </p>
                    <p class="text-gray-600">App Status</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Real-time updates using Server-Sent Events
        const eventSource = new EventSource('/monitoring/stream/');
        
        // Initialize charts
        const systemCtx = document.getElementById('systemChart').getContext('2d');
        const cacheCtx = document.getElementById('cacheChart').getContext('2d');
        
        const systemChart = new Chart(systemCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU %',
                    data: [],
                    borderColor: 'rgb(239, 68, 68)',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Memory %',
                    data: [],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        const cacheChart = new Chart(cacheCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cache Hits', 'Cache Misses'],
                datasets: [{
                    data: [{{ current_metrics.cache.total_hits }}, {{ current_metrics.cache.total_misses }}],
                    backgroundColor: ['#10b981', '#ef4444']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // Update dashboard with real-time data
        eventSource.onmessage = function(event) {
            const data = JSON.parse(event.data);
            updateDashboard(data);
        };
        
        function updateDashboard(data) {
            // Update timestamp
            document.getElementById('last-updated').textContent = new Date(data.timestamp).toLocaleTimeString();
            
            // Update metric cards
            document.getElementById('cpu-usage').textContent = data.system.cpu_percent.toFixed(1) + '%';
            document.getElementById('memory-usage').textContent = data.system.memory_percent.toFixed(1) + '%';
            document.getElementById('disk-usage').textContent = data.system.disk_percent.toFixed(1) + '%';
            
            // Update database status
            const dbStatus = document.getElementById('db-status');
            if (data.database.status === 'healthy') {
                dbStatus.innerHTML = '🟢 Healthy';
            } else {
                dbStatus.innerHTML = '🔴 Error';
            }
            
            // Update cache metrics
            if (data.cache.hit_rate !== undefined) {
                document.getElementById('cache-hit-rate').textContent = data.cache.hit_rate.toFixed(1) + '%';
            }
            if (data.cache.total_requests !== undefined) {
                document.getElementById('cache-requests').textContent = data.cache.total_requests;
            }
            
            // Update charts
            updateCharts(data);
        }
        
        function updateCharts(data) {
            const now = new Date(data.timestamp).toLocaleTimeString();
            
            // Update system chart
            systemChart.data.labels.push(now);
            systemChart.data.datasets[0].data.push(data.system.cpu_percent);
            systemChart.data.datasets[1].data.push(data.system.memory_percent);
            
            // Keep only last 20 data points
            if (systemChart.data.labels.length > 20) {
                systemChart.data.labels.shift();
                systemChart.data.datasets[0].data.shift();
                systemChart.data.datasets[1].data.shift();
            }
            
            systemChart.update('none');
            
            // Update cache chart
            if (data.cache.total_hits !== undefined && data.cache.total_misses !== undefined) {
                cacheChart.data.datasets[0].data = [data.cache.total_hits, data.cache.total_misses];
                cacheChart.update('none');
            }
        }
        
        // Refresh alerts every 30 seconds
        setInterval(function() {
            fetch('/monitoring/alerts/')
                .then(response => response.json())
                .then(data => updateAlerts(data.alerts));
        }, 30000);
        
        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            if (alerts.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">✅ No recent alerts - system is running smoothly!</p>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="alert-${alert.severity} bg-gray-50 p-4 mb-3 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-semibold">${alert.message}</p>
                            <p class="text-sm text-gray-600">${new Date(alert.timestamp).toLocaleString()}</p>
                        </div>
                        <span class="px-2 py-1 rounded text-xs font-semibold
                            ${alert.severity === 'high' ? 'bg-red-100 text-red-800' : 
                              alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                              'bg-green-100 text-green-800'}">
                            ${alert.severity.toUpperCase()}
                        </span>
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
