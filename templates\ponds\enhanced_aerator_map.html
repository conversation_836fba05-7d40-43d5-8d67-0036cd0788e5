{% extends "base.html" %}
{% load static %}

{% block title %}{{ pond.name }} - Enhanced Aerator Map{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 600px;
        width: 100%;
        border-radius: 12px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border: 3px solid #e5e7eb;
    }
    
    .enhanced-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .enhanced-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        border-color: #3b82f6;
    }
    
    .control-panel {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
    }
    
    .aerator-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
    }
    
    .stat-card:hover {
        transform: scale(1.05);
    }
    
    .stat-number {
        font-size: 1.8rem;
        font-weight: bold;
        color: #1e40af;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6b7280;
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .aerator-list-enhanced {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .aerator-item-enhanced {
        background: white;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }
    
    .aerator-item-enhanced:hover {
        border-left-color: #3b82f6;
        transform: translateX(5px);
    }
    
    .aerator-item-enhanced.status-active {
        border-left-color: #10b981;
    }
    
    .aerator-item-enhanced.status-maintenance {
        border-left-color: #f59e0b;
    }
    
    .aerator-item-enhanced.status-inactive {
        border-left-color: #ef4444;
    }
    
    .status-badge-enhanced {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #991b1b; }
    .status-maintenance { background-color: #fef3c7; color: #92400e; }
    .status-faulty { background-color: #fde2e8; color: #9f1239; }
    
    .legend-enhanced {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .legend-item-enhanced {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px;
        border-radius: 6px;
        transition: background-color 0.2s ease;
    }
    
    .legend-item-enhanced:hover {
        background-color: #f3f4f6;
    }
    
    .legend-color-enhanced {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        margin-right: 12px;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .pond-marker { background-color: #3b82f6; }
    .aerator-active { background-color: #10b981; }
    .aerator-inactive { background-color: #ef4444; }
    .aerator-maintenance { background-color: #f59e0b; }
    .aerator-faulty { background-color: #9f1239; }
    
    .add-mode-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        z-index: 1000;
        display: none;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .btn-enhanced {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .btn-enhanced:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .btn-add-mode {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }
    
    .btn-add-mode.active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Add Mode Indicator -->
    <div id="addModeIndicator" class="add-mode-indicator">
        <i class="fas fa-mouse-pointer me-2"></i>
        <strong>ADD MODE ACTIVE</strong><br>
        <small>Click on the map to add aerators</small>
    </div>

    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="control-panel">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-2">
                            <i class="fas fa-fan me-2"></i>
                            {{ pond.name }} - Enhanced Aerator Map
                        </h1>
                        <p class="mb-0 opacity-75">Interactive aerator management with click-to-add functionality</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button id="toggleAddMode" class="btn btn-add-mode btn-enhanced">
                            <i class="fas fa-plus me-1"></i> Add Mode
                        </button>
                        <a href="{% url 'ponds:aerator_management' pond_id=pond.id %}" class="btn btn-light btn-enhanced">
                            <i class="fas fa-cogs me-1"></i> Manage
                        </a>
                        <a href="{% url 'ponds:pond_detail' pk=pond.id %}" class="btn btn-outline-light btn-enhanced">
                            <i class="fas fa-arrow-left me-1"></i> Back
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Controls & Info -->
        <div class="col-md-4">
            <!-- Statistics -->
            <div class="enhanced-card p-3 mb-4">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    Aerator Statistics
                </h5>
                <div class="aerator-stats">
                    <div class="stat-card">
                        <div class="stat-number">{{ aerators.count }}</div>
                        <div class="stat-label">Total</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #10b981;">{{ active_count }}</div>
                        <div class="stat-label">Active</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #f59e0b;">{{ maintenance_count }}</div>
                        <div class="stat-label">Maintenance</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #ef4444;">{{ inactive_count }}</div>
                        <div class="stat-label">Inactive</div>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="legend-enhanced">
                <h6 class="mb-3">
                    <i class="fas fa-map-signs text-primary me-2"></i>
                    Map Legend
                </h6>
                <div class="legend-item-enhanced">
                    <div class="legend-color-enhanced pond-marker"></div>
                    <span><strong>Pond Center</strong></span>
                </div>
                <div class="legend-item-enhanced">
                    <div class="legend-color-enhanced aerator-active"></div>
                    <span>Active Aerator</span>
                </div>
                <div class="legend-item-enhanced">
                    <div class="legend-color-enhanced aerator-inactive"></div>
                    <span>Inactive Aerator</span>
                </div>
                <div class="legend-item-enhanced">
                    <div class="legend-color-enhanced aerator-maintenance"></div>
                    <span>Maintenance Aerator</span>
                </div>
                <div class="legend-item-enhanced">
                    <div class="legend-color-enhanced aerator-faulty"></div>
                    <span>Faulty Aerator</span>
                </div>
            </div>

            <!-- Aerator List -->
            <div class="enhanced-card p-3">
                <h6 class="mb-3">
                    <i class="fas fa-list text-primary me-2"></i>
                    Aerators ({{ aerators.count }})
                </h6>
                <div class="aerator-list-enhanced">
                    {% if aerators %}
                        {% for aerator in aerators %}
                        <div class="aerator-item-enhanced status-{{ aerator.status }}" data-aerator-id="{{ aerator.id }}">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center mb-2">
                                        <strong>{{ aerator.name }}</strong>
                                        <span class="status-badge-enhanced status-{{ aerator.status }} ms-2">
                                            {{ aerator.status }}
                                        </span>
                                    </div>
                                    <div class="small text-muted">
                                        <div><i class="fas fa-cog me-1"></i> {{ aerator.get_aerator_type_display }}</div>
                                        {% if aerator.power_rating %}
                                        <div><i class="fas fa-bolt me-1"></i> {{ aerator.power_rating }} HP</div>
                                        {% endif %}
                                        <div><i class="fas fa-map-marker-alt me-1"></i> {{ aerator.latitude|floatformat:4 }}, {{ aerator.longitude|floatformat:4 }}</div>
                                    </div>
                                </div>
                                <div class="text-end">
                                    <button class="btn btn-sm btn-outline-primary" onclick="focusAerator({{ aerator.id }})">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-info-circle text-muted fa-2x mb-3"></i>
                            <p class="text-muted">No aerators found for this pond.</p>
                            <button class="btn btn-primary btn-sm" onclick="enableAddMode()">
                                <i class="fas fa-plus me-1"></i> Add First Aerator
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Right Column - Map -->
        <div class="col-md-8">
            <div class="enhanced-card p-0">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marked-alt me-2"></i>
                        Interactive Aerator Map
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="map"></div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Click aerator markers for details. Use Add Mode to place new aerators.
                        </small>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="resetMapView()">
                                <i class="fas fa-expand-arrows-alt me-1"></i> Reset View
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Aerator Modal -->
<div class="modal fade" id="addAeratorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>
                    Add New Aerator
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddAeratorForm">
                    {% csrf_token %}
                    <input type="hidden" id="aeratorLatitude" name="latitude">
                    <input type="hidden" id="aeratorLongitude" name="longitude">
                    
                    <div class="mb-3">
                        <label class="form-label">Aerator Name</label>
                        <input type="text" class="form-control" id="aeratorName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Aerator Type</label>
                        <select class="form-control" id="aeratorType" name="aerator_type" required>
                            <option value="paddle_wheel">Paddle Wheel</option>
                            <option value="fountain">Fountain</option>
                            <option value="diffuser">Diffuser</option>
                            <option value="venturi">Venturi</option>
                            <option value="surface">Surface Aerator</option>
                            <option value="submersible">Submersible</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Power Rating (HP)</label>
                        <input type="number" class="form-control" id="aeratorPower" name="power_rating" step="0.1" min="0">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-control" id="aeratorStatus" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="faulty">Faulty</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAerator()">
                    <i class="fas fa-save me-1"></i> Add Aerator
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let map;
    let infoWindow;
    let addMode = false;
    let aeratorMarkers = [];
    let pondMarker;

    // Data from Django
    const pondData = {
        id: {{ pond.id }},
        name: "{{ pond.name|escapejs }}",
        latitude: {{ pond.latitude|default:0 }},
        longitude: {{ pond.longitude|default:0 }},
        status: "{{ pond.status|escapejs }}"
    };

    const aeratorsData = [
        {% for aerator in aerators %}
        {
            id: {{ aerator.id }},
            name: "{{ aerator.name|escapejs }}",
            type: "{{ aerator.aerator_type|escapejs }}",
            status: "{{ aerator.status|escapejs }}",
            power_rating: {{ aerator.power_rating|default:0 }},
            latitude: {{ aerator.latitude|default:0 }},
            longitude: {{ aerator.longitude|default:0 }},
            operating_hours: {{ aerator.operating_hours|default:0 }},
            device_id: "{{ aerator.device_id|escapejs }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};

    function initMap() {
        // Initialize map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 18,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: 'hybrid',
            styles: [
                {
                    featureType: 'water',
                    elementType: 'geometry',
                    stylers: [{ color: '#193341' }]
                }
            ]
        });

        infoWindow = new google.maps.InfoWindow();

        // Add click listener for adding aerators
        map.addListener('click', function(event) {
            if (addMode) {
                showAddAeratorModal(event.latLng.lat(), event.latLng.lng());
            }
        });

        // Add pond marker
        addPondMarker();

        // Add aerator markers
        addAeratorMarkers();

        // Fit map to show all markers
        fitMapToMarkers();
    }

    function addPondMarker() {
        if (pondData.latitude && pondData.longitude) {
            pondMarker = new google.maps.Marker({
                position: { lat: pondData.latitude, lng: pondData.longitude },
                map: map,
                title: pondData.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#3b82f6" stroke="white" stroke-width="4"/>
                            <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial" font-weight="bold">P</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40)
                },
                zIndex: 1000
            });

            const pondInfoContent = `
                <div style="max-width: 300px; padding: 10px;">
                    <h5 style="color: #1e40af; margin-bottom: 15px;">
                        <i class="fas fa-water"></i> ${pondData.name}
                    </h5>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div><strong>Status:</strong> <span class="badge badge-${pondData.status === 'active' ? 'success' : 'secondary'}">${pondData.status}</span></div>
                        <div><strong>Aerators:</strong> ${aeratorsData.length}</div>
                        <div><strong>Active:</strong> ${aeratorsData.filter(a => a.status === 'active').length}</div>
                        <div><strong>Total Power:</strong> ${aeratorsData.reduce((sum, a) => sum + (a.power_rating || 0), 0)} HP</div>
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <button class="btn btn-sm btn-primary" onclick="enableAddMode()">
                            <i class="fas fa-plus"></i> Add Aerator
                        </button>
                        <a href="{% url 'ponds:aerator_management' pond_id=pond.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-cogs"></i> Manage
                        </a>
                    </div>
                </div>
            `;

            pondMarker.addListener('click', function() {
                infoWindow.setContent(pondInfoContent);
                infoWindow.open(map, pondMarker);
            });
        }
    }

    function addAeratorMarkers() {
        aeratorMarkers = [];

        aeratorsData.forEach(function(aerator, index) {
            if (aerator.latitude && aerator.longitude) {
                const marker = createAeratorMarker(aerator, index);
                aeratorMarkers.push(marker);
            }
        });
    }

    function createAeratorMarker(aerator, index) {
        let aeratorColor = '#ef4444'; // inactive - red

        if (aerator.status === 'active') {
            aeratorColor = '#10b981'; // active - green
        } else if (aerator.status === 'maintenance') {
            aeratorColor = '#f59e0b'; // maintenance - yellow
        } else if (aerator.status === 'faulty') {
            aeratorColor = '#9f1239'; // faulty - dark red
        }

        const aeratorMarker = new google.maps.Marker({
            position: { lat: aerator.latitude, lng: aerator.longitude },
            map: map,
            title: aerator.name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="16" cy="16" r="14" fill="${aeratorColor}" stroke="white" stroke-width="3"/>
                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-family="Arial" font-weight="bold">A${index + 1}</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32)
            },
            zIndex: 500
        });

        const aeratorInfoContent = `
            <div style="max-width: 280px; padding: 10px;">
                <h6 style="color: #1e40af; margin-bottom: 15px;">
                    <i class="fas fa-fan"></i> ${aerator.name}
                </h6>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                    <div><strong>Type:</strong> ${aerator.type.replace('_', ' ')}</div>
                    <div><strong>Status:</strong> <span class="badge badge-${aerator.status === 'active' ? 'success' : aerator.status === 'maintenance' ? 'warning' : 'danger'}">${aerator.status}</span></div>
                    ${aerator.power_rating ? `<div><strong>Power:</strong> ${aerator.power_rating} HP</div>` : ''}
                    ${aerator.operating_hours ? `<div><strong>Hours:</strong> ${aerator.operating_hours}</div>` : ''}
                    ${aerator.device_id ? `<div><strong>Device ID:</strong> ${aerator.device_id}</div>` : ''}
                    <div><strong>Location:</strong> ${aerator.latitude.toFixed(4)}, ${aerator.longitude.toFixed(4)}</div>
                </div>
                <div style="text-align: center;">
                    <a href="{% url 'ponds:edit_aerator' pond_id=pond.id aerator_id=0 %}".replace('0', aerator.id) class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                    <button class="btn btn-sm btn-outline-danger" onclick="confirmDeleteAerator(${aerator.id})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        `;

        aeratorMarker.addListener('click', function() {
            infoWindow.setContent(aeratorInfoContent);
            infoWindow.open(map, aeratorMarker);
        });

        return aeratorMarker;
    }

    // Add Mode Functions
    function toggleAddMode() {
        addMode = !addMode;
        const button = document.getElementById('toggleAddMode');
        const indicator = document.getElementById('addModeIndicator');

        if (addMode) {
            button.innerHTML = '<i class="fas fa-times me-1"></i> Exit Add Mode';
            button.classList.add('active');
            indicator.style.display = 'block';
            map.setOptions({ cursor: 'crosshair' });
        } else {
            button.innerHTML = '<i class="fas fa-plus me-1"></i> Add Mode';
            button.classList.remove('active');
            indicator.style.display = 'none';
            map.setOptions({ cursor: 'default' });
        }
    }

    function enableAddMode() {
        if (!addMode) {
            toggleAddMode();
        }
    }

    function showAddAeratorModal(lat, lng) {
        document.getElementById('aeratorLatitude').value = lat;
        document.getElementById('aeratorLongitude').value = lng;

        // Generate default name
        const aeratorCount = aeratorsData.length + 1;
        document.getElementById('aeratorName').value = `${pondData.name} - Aerator ${aeratorCount}`;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addAeratorModal'));
        modal.show();
    }

    function saveAerator() {
        const form = document.getElementById('quickAddAeratorForm');
        const formData = new FormData(form);

        // Add pond ID
        formData.append('pond_id', pondData.id);

        fetch(`{% url 'ponds:add_aerator' pond_id=pond.id %}`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addAeratorModal'));
                modal.hide();

                // Show success message
                showAlert('success', 'Aerator added successfully!');

                // Reload page to show new aerator
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showAlert('error', data.error || 'Failed to add aerator');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Failed to add aerator');
        });
    }

    // Utility Functions
    function fitMapToMarkers() {
        if (aeratorMarkers.length > 0 || pondMarker) {
            const bounds = new google.maps.LatLngBounds();

            // Include pond location
            if (pondMarker) {
                bounds.extend(pondMarker.getPosition());
            }

            // Include aerator locations
            aeratorMarkers.forEach(function(marker) {
                bounds.extend(marker.getPosition());
            });

            map.fitBounds(bounds);

            // Ensure minimum zoom level
            const minZoom = 16;
            google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                if (map.getZoom() > minZoom) {
                    map.setZoom(minZoom);
                }
            });
        }
    }

    function resetMapView() {
        fitMapToMarkers();
    }

    function focusAerator(aeratorId) {
        const aerator = aeratorsData.find(a => a.id === aeratorId);
        if (aerator && aerator.latitude && aerator.longitude) {
            map.setCenter({ lat: aerator.latitude, lng: aerator.longitude });
            map.setZoom(20);

            // Find and click the marker
            const marker = aeratorMarkers.find(m => m.getTitle() === aerator.name);
            if (marker) {
                google.maps.event.trigger(marker, 'click');
            }
        }
    }

    function confirmDeleteAerator(aeratorId) {
        if (confirm('Are you sure you want to delete this aerator?')) {
            // Implementation for deleting aerator
            showAlert('info', 'Delete functionality would be implemented here');
        }
    }

    // Utility function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Alert function
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Event Listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('toggleAddMode').addEventListener('click', toggleAddMode);
    });

    // Error handling for Google Maps
    window.gm_authFailure = function() {
        document.getElementById('map').innerHTML =
            '<div class="alert alert-danger m-3">Google Maps failed to load. Please check your API key.</div>';
    };
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
</script>
{% endblock %}
