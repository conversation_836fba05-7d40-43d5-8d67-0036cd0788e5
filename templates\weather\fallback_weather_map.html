<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌤️ {{ pond.name }} - Weather Map (Fallback)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .map-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        #weather-map {
            height: 500px;
            width: 100%;
            border-radius: 10px;
            border: 3px solid #667eea;
        }
        
        .info-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .badge-fallback {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .alert-gmaps {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
            color: white;
            border: none;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1><i class="fas fa-fish"></i> {{ pond.name }}</h1>
                    <p class="mb-2">🌤️ Weather Station (Fallback Map)</p>
                    <span class="badge-fallback">🗺️ SATELLITE MAP ACTIVE</span>
                </div>
                <div class="col-md-4 text-end">
                    <div class="text-white">
                        <div><strong>📍 Location:</strong></div>
                        <div>{{ pond.latitude }}°N, {{ pond.longitude }}°E</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Google Maps Status Alert -->
        <div class="alert alert-gmaps" role="alert">
            <h5><i class="fas fa-exclamation-triangle"></i> Google Maps Status</h5>
            <p class="mb-2">Google Maps is currently unavailable. Using high-quality satellite imagery as fallback.</p>
            <div class="d-flex gap-2">
                <a href="/weather/debug-gmaps/" class="btn btn-light btn-sm">
                    <i class="fas fa-bug"></i> Debug Google Maps
                </a>
                <a href="/weather/working-gmaps/{{ pond.id }}/" class="btn btn-light btn-sm">
                    <i class="fas fa-retry"></i> Retry Google Maps
                </a>
            </div>
        </div>

        <!-- Map Container -->
        <div class="map-container">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3><i class="fas fa-map"></i> Interactive Weather Map</h3>
                <div class="badge bg-success">
                    ✅ Map Loaded Successfully
                </div>
            </div>
            <div id="weather-map"></div>
        </div>

        <!-- Info Panel -->
        <div class="info-card">
            <h4><i class="fas fa-info-circle"></i> Map Information</h4>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>🗺️ Map Provider:</strong> Satellite Imagery (Fallback)</p>
                    <p><strong>📡 Status:</strong> <span class="text-success">✅ Active</span></p>
                    <p><strong>🎯 View Type:</strong> High-Resolution Satellite</p>
                    <p><strong>🌤️ Weather Layer:</strong> Available</p>
                </div>
                <div class="col-md-6">
                    {% if weather_data %}
                    <p><strong>🌡️ Temperature:</strong> {{ weather_data.temperature }}°C</p>
                    <p><strong>💧 Humidity:</strong> {{ weather_data.humidity }}%</p>
                    <p><strong>💨 Wind Speed:</strong> {{ weather_data.wind_speed }} km/h</p>
                    <p><strong>☁️ Condition:</strong> {{ weather_data.condition }}</p>
                    {% else %}
                    <p><em>⚠️ No weather data available for this location</em></p>
                    {% endif %}
                </div>
            </div>
            
            <div class="mt-3">
                <a href="/ponds/" class="btn btn-primary me-2">
                    <i class="fas fa-fish"></i> All Ponds
                </a>
                <a href="/weather/unified-map/" class="btn btn-info me-2">
                    <i class="fas fa-globe"></i> Unified Weather Map
                </a>
                <a href="/weather/debug-gmaps/" class="btn btn-warning me-2">
                    <i class="fas fa-bug"></i> Debug Google Maps
                </a>
                <button onclick="location.reload()" class="btn btn-secondary">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // Pond data
        const pondData = {
            name: "{{ pond.name }}",
            lat: {{ pond.latitude }},
            lng: {{ pond.longitude }},
            {% if weather_data %}
            weather: {
                temperature: {{ weather_data.temperature }},
                humidity: {{ weather_data.humidity }},
                wind_speed: {{ weather_data.wind_speed }},
                condition: "{{ weather_data.condition }}"
            }
            {% else %}
            weather: null
            {% endif %}
        };

        // Initialize Leaflet map with satellite imagery
        const map = L.map('weather-map').setView([pondData.lat, pondData.lng], 15);

        // Add high-quality satellite tile layer
        L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: '&copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 18
        }).addTo(map);

        // Create custom pond marker
        const pondMarker = L.circleMarker([pondData.lat, pondData.lng], {
            radius: 15,
            fillColor: '#667eea',
            color: 'white',
            weight: 4,
            opacity: 1,
            fillOpacity: 0.8
        }).addTo(map);

        // Create popup content
        let popupContent = `
            <div style="max-width: 300px; font-family: inherit;">
                <div style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 15px; margin: -10px -10px 15px -10px; border-radius: 10px; text-align: center;">
                    <h4 style="margin: 0; font-size: 1.2em;"><i class="fas fa-fish"></i> ${pondData.name}</h4>
                    <p style="margin: 5px 0 0 0; font-size: 0.9em;">🗺️ Satellite Weather Station</p>
                </div>
                <div style="padding: 10px 0;">
                    <p style="margin: 8px 0;"><strong>📍 Location:</strong> ${pondData.lat}, ${pondData.lng}</p>
        `;

        if (pondData.weather) {
            popupContent += `
                    <div style="background: linear-gradient(135deg, #74b9ff, #0984e3); color: white; padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="text-align: center; font-weight: bold; margin-bottom: 10px;">🌤️ Current Weather</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                            <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                <div style="font-size: 1.1em; font-weight: bold;">${pondData.weather.temperature}°C</div>
                                <div style="font-size: 0.8em; opacity: 0.9;">Temperature</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                <div style="font-size: 1.1em; font-weight: bold;">${pondData.weather.humidity}%</div>
                                <div style="font-size: 0.8em; opacity: 0.9;">Humidity</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                <div style="font-size: 1.1em; font-weight: bold;">${pondData.weather.wind_speed} km/h</div>
                                <div style="font-size: 0.8em; opacity: 0.9;">Wind</div>
                            </div>
                            <div style="text-align: center; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px;">
                                <div style="font-size: 1.1em; font-weight: bold;">${pondData.weather.condition}</div>
                                <div style="font-size: 0.8em; opacity: 0.9;">Condition</div>
                            </div>
                        </div>
                    </div>
            `;
        } else {
            popupContent += `
                    <div style="background: linear-gradient(135deg, #636e72, #2d3436); color: white; padding: 15px; border-radius: 8px; margin: 15px 0; text-align: center;">
                        <div style="font-weight: bold;">⚠️ No Weather Data</div>
                        <div style="font-size: 0.9em; opacity: 0.9;">Weather information not available</div>
                    </div>
            `;
        }

        popupContent += `
                    <div style="text-align: center; margin-top: 15px;">
                        <a href="/weather/debug-gmaps/" style="background: #fdcb6e; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; margin-right: 8px; font-size: 0.9em;">Debug Google Maps</a>
                        <a href="/ponds/" style="background: #667eea; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 0.9em;">Pond Details</a>
                    </div>
                </div>
            </div>
        `;

        // Bind popup to marker
        pondMarker.bindPopup(popupContent, {
            maxWidth: 350,
            className: 'weather-popup'
        });

        // Add weather layer
        const weatherLayer = L.tileLayer('https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=9de243494c0b295cca9337e1e96b00e2', {
            opacity: 0.6,
            attribution: '© OpenWeatherMap'
        });

        // Add layer control
        const overlayMaps = {
            "🌡️ Temperature": weatherLayer
        };

        L.control.layers(null, overlayMaps, {
            position: 'topright'
        }).addTo(map);

        // Auto-open popup
        setTimeout(() => {
            pondMarker.openPopup();
        }, 1000);

        console.log('✅ Fallback weather map loaded successfully!');
    </script>
</body>
</html>
