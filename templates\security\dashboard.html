{% extends "base.html" %}
{% load static %}

{% block title %}Security Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .security-header {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }
    
    .security-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #dc2626;
    }
    
    .security-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        color: #6b7280;
        font-size: 0.9rem;
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .security-status {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-secure {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-warning {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-critical {
        background: #fee2e2;
        color: #991b1b;
    }
    
    .event-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
        transition: background-color 0.2s ease;
    }
    
    .event-item:hover {
        background-color: #f9fafb;
    }
    
    .event-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
    }
    
    .event-critical { background: #fee2e2; color: #991b1b; }
    .event-warning { background: #fef3c7; color: #92400e; }
    .event-info { background: #dbeafe; color: #1e40af; }
    
    .btn-security {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-security:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
        color: white;
    }
    
    .security-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    @media (max-width: 768px) {
        .security-grid {
            grid-template-columns: 1fr;
        }
        
        .metric-value {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="security-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">🛡️ Security Dashboard</h1>
                <p class="lead mb-0">Comprehensive security monitoring and user management</p>
                <div class="mt-3">
                    <span class="security-status status-secure">
                        <i class="fas fa-shield-alt me-1"></i>
                        System Secure
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button class="btn btn-light" onclick="refreshSecurityData()">
                        <i class="fas fa-sync me-2"></i>Refresh
                    </button>
                    <button class="btn btn-outline-light" onclick="exportSecurityReport()">
                        <i class="fas fa-download me-2"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Security Metrics Grid -->
    <div class="security-grid">
        <div class="security-card">
            <div class="metric-value">{{ stats.total_users|default:0 }}</div>
            <div class="metric-label">Total Users</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-users me-1"></i>
                    Active system users
                </small>
            </div>
        </div>
        
        <div class="security-card">
            <div class="metric-value">{{ stats.active_sessions|default:0 }}</div>
            <div class="metric-label">Active Sessions</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    Currently logged in
                </small>
            </div>
        </div>
        
        <div class="security-card">
            <div class="metric-value">{{ stats.security_events_today|default:0 }}</div>
            <div class="metric-label">Security Events Today</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Detected incidents
                </small>
            </div>
        </div>
        
        <div class="security-card">
            <div class="metric-value">{{ stats.api_keys_active|default:0 }}</div>
            <div class="metric-label">Active API Keys</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-key me-1"></i>
                    API access tokens
                </small>
            </div>
        </div>
    </div>

    <!-- Security Events and Audit Logs -->
    <div class="row">
        <div class="col-lg-6">
            <div class="security-card">
                <h5 class="mb-3">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Recent Security Events
                </h5>
                {% if recent_events %}
                    {% for event in recent_events %}
                    <div class="event-item">
                        <div class="event-icon event-{{ event.severity|default:'info' }}">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ event.title|default:'Security Event' }}</h6>
                            <p class="mb-1 text-muted">{{ event.description|default:'Security event detected' }}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ event.detected_at|default:'Recently'|timesince }} ago
                            </small>
                        </div>
                        <div>
                            <span class="security-status status-{{ event.severity|default:'warning' }}">
                                {{ event.get_severity_display|default:'Warning' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent security events</p>
                    </div>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{% url 'security:security_events' %}" class="btn btn-security btn-sm">
                        View All Events
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="security-card">
                <h5 class="mb-3">
                    <i class="fas fa-clipboard-list me-2"></i>
                    Recent Audit Logs
                </h5>
                {% if recent_audits %}
                    {% for audit in recent_audits %}
                    <div class="event-item">
                        <div class="event-icon event-info">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ audit.action_description|default:'User Action' }}</h6>
                            <p class="mb-1 text-muted">
                                User: {{ audit.user.email|default:'System' }}
                            </p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ audit.timestamp|default:'Recently'|timesince }} ago
                            </small>
                        </div>
                        <div>
                            <span class="security-status status-secure">
                                {{ audit.get_action_type_display|default:'Action' }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent audit logs</p>
                    </div>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{% url 'security:audit_logs' %}" class="btn btn-security btn-sm">
                        View All Logs
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="security-card">
                <h5 class="mb-3">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Security Actions
                </h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'security:user_management' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-users d-block mb-2"></i>
                            Manage Users
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'security:role_management' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-user-tag d-block mb-2"></i>
                            Manage Roles
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'security:api_key_management' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-key d-block mb-2"></i>
                            API Keys
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'security:setup_2fa' %}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-mobile-alt d-block mb-2"></i>
                            Setup 2FA
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshSecurityData() {
        fetch('/security/api/security-status/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error refreshing security data:', error);
        });
    }
    
    function exportSecurityReport() {
        window.open('/security/api/audit-export/', '_blank');
    }
</script>
{% endblock %}
