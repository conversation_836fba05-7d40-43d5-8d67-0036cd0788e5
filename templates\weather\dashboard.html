{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Dashboard - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .weather-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .weather-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: weather-sweep 6s infinite;
        }

        @keyframes weather-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-bottom: 30px;
            border: 2px solid transparent;
            transition: all 0.4s ease;
            height: 100%;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 1.5rem;
        }

        .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #636e72;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    .weather-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .weather-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .weather-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .weather-temp {
        font-size: 3rem;
        font-weight: 700;
    }

    .weather-unit {
        font-size: 1.5rem;
        font-weight: 400;
        vertical-align: super;
    }

    .weather-condition {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
    }

    .weather-location {
        font-size: 1rem;
        color: #6c757d;
    }

    .weather-detail {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .weather-detail-icon {
        width: 2rem;
        text-align: center;
        margin-right: 1rem;
        color: #3b82f6;
    }

    .weather-detail-label {
        color: #6c757d;
        margin-right: 0.5rem;
    }

    .weather-detail-value {
        font-weight: 500;
        margin-left: auto;
    }

    .forecast-day {
        text-align: center;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }

    .forecast-day:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .forecast-date {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .forecast-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .forecast-temp {
        font-weight: 700;
    }

    .forecast-temp-range {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }

    .forecast-high {
        color: #ef4444;
    }

    .forecast-low {
        color: #3b82f6;
    }

    .forecast-condition {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .alert-card {
        border-left: 4px solid transparent;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .alert-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .alert-card.warning {
        border-left-color: #f59e0b;
    }

    .alert-card.danger {
        border-left-color: #ef4444;
    }

    .alert-card.info {
        border-left-color: #3b82f6;
    }

    .alert-icon {
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .alert-icon.warning {
        color: #f59e0b;
    }

    .alert-icon.danger {
        color: #ef4444;
    }

    .alert-icon.info {
        color: #3b82f6;
    }

    .alert-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .alert-time {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .parameter-card {
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .parameter-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .parameter-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .parameter-value {
        font-size: 2rem;
        font-weight: 700;
    }

    .parameter-unit {
        font-size: 1rem;
        color: #6c757d;
    }

    .parameter-label {
        font-size: 1rem;
        color: #6c757d;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
        background-color: #f8f9fa;
        color: #4b5563;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .quick-action-btn:hover {
        background-color: #3b82f6;
        color: white;
    }

    .quick-action-btn i {
        margin-right: 0.75rem;
        font-size: 1.25rem;
        width: 1.5rem;
        text-align: center;
    }
</style>
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="weather-header">
        <div class="position-relative">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2"><i class="fas fa-cloud-sun-rain me-3"></i>Weather Dashboard</h1>
                    <p class="mb-0" style="opacity: 0.9;">Real-time weather monitoring and intelligent automation for your shrimp farms</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'weather:forecast_list' %}" class="action-btn">
                        <i class="fas fa-calendar-alt"></i> Extended Forecast
                    </a>
                    <button class="action-btn" onclick="refreshWeather()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary">
                        <i class="fas fa-cloud-sun-rain"></i>
                    </div>
                    <div>
                        <div class="stats-value">{{ stations.count }}</div>
                        <div class="stats-label">Weather Stations</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success">
                        <i class="fas fa-temperature-high"></i>
                    </div>
                    <div>
                        <div class="stats-value">{% if latest_weather %}{{ latest_weather.temperature|floatformat:1 }}°C{% else %}--{% endif %}</div>
                        <div class="stats-label">Current Temperature</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info">
                        <i class="fas fa-wind"></i>
                    </div>
                    <div>
                        <div class="stats-value">{% if latest_weather %}{{ latest_weather.wind_speed|floatformat:1 }}{% else %}--{% endif %}</div>
                        <div class="stats-label">Wind Speed (km/h)</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-warning">
                        <i class="fas fa-tint"></i>
                    </div>
                    <div>
                        <div class="stats-value">{% if latest_weather %}{{ latest_weather.humidity|floatformat:0 }}%{% else %}--{% endif %}</div>
                        <div class="stats-label">Humidity</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="card mb-4" style="background: rgba(255, 255, 255, 0.95); border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);">
        <div class="card-body">
            <form method="get" action="{% url 'weather:weather_dashboard' %}">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="station" class="form-label">Weather Station</label>
                        <select id="station" name="station" class="form-select" onchange="this.form.submit()">
                            <option value="">All Stations</option>
                            {% for station in stations %}
                                <option value="{{ station.id }}" {% if selected_station.id == station.id %}selected{% endif %}>
                                    {{ station.name }} ({{ station.location }})
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_range" class="form-label">Date Range</label>
                        <select id="date_range" name="date_range" class="form-select">
                            <option value="today">Today</option>
                            <option value="week">Last 7 Days</option>
                            <option value="month">Last 30 Days</option>
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i> Apply Filters
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    {% if not selected_station %}
    <div class="alert alert-info" style="background: rgba(255, 255, 255, 0.95); border: none; border-radius: 15px;">
        <i class="fas fa-info-circle me-2"></i>
        <span>No weather stations found. <a href="{% url 'weather:station_create' %}">Create a weather station</a> to get started.</span>
    </div>
    {% else %}

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-8">
            <!-- Current Weather -->
            <div class="card mb-4" style="background: rgba(255, 255, 255, 0.95); border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);">
                <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="mb-0">Current Weather at {{ selected_station.name }}</h5>
                </div>
                <div class="card-body">
                    {% if latest_weather %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-4">
                                <div class="me-3">
                                    {% if 'sunny' in latest_weather.condition|lower or 'clear' in latest_weather.condition|lower %}
                                    <i class="fas fa-sun text-warning" style="font-size: 3rem;"></i>
                                    {% elif 'cloud' in latest_weather.condition|lower %}
                                    <i class="fas fa-cloud text-secondary" style="font-size: 3rem;"></i>
                                    {% elif 'rain' in latest_weather.condition|lower or 'shower' in latest_weather.condition|lower %}
                                    <i class="fas fa-cloud-rain text-primary" style="font-size: 3rem;"></i>
                                    {% elif 'storm' in latest_weather.condition|lower or 'thunder' in latest_weather.condition|lower %}
                                    <i class="fas fa-bolt text-warning" style="font-size: 3rem;"></i>
                                    {% else %}
                                    <i class="fas fa-cloud-sun text-primary" style="font-size: 3rem;"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <h2 class="mb-0">{{ latest_weather.temperature|floatformat:1 }}°C</h2>
                                    <p class="mb-0">{{ latest_weather.condition }}</p>
                                    <p class="text-muted small mb-0">Last updated: {{ latest_weather.timestamp|date:"M d, Y H:i" }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Humidity</p>
                                        <p class="mb-0">{{ latest_weather.humidity|floatformat:0 }}%</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Pressure</p>
                                        <p class="mb-0">{{ latest_weather.pressure|floatformat:0 }} hPa</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Wind</p>
                                        <p class="mb-0">{{ latest_weather.wind_speed|floatformat:1 }} km/h {{ latest_weather.wind_direction }}</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Precipitation</p>
                                        <p class="mb-0">{{ latest_weather.precipitation|floatformat:1 }} mm</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">Station Information</h6>
                                    <div class="mb-2">
                                        <p class="text-muted small mb-1">Location</p>
                                        <p class="mb-0">{{ selected_station.location }}</p>
                                    </div>
                                    <div class="mb-2">
                                        <p class="text-muted small mb-1">Coordinates</p>
                                        <p class="mb-0">{{ selected_station.latitude|floatformat:4 }}, {{ selected_station.longitude|floatformat:4 }}</p>
                                    </div>
                                    <div class="mb-2">
                                        <p class="text-muted small mb-1">Elevation</p>
                                        <p class="mb-0">{{ selected_station.elevation|floatformat:0 }} m</p>
                                    </div>
                                    <a href="{% url 'weather:station_detail' selected_station.id %}" class="btn btn-sm btn-outline-primary mt-2">
                                        View Station Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No weather data available for this station.</span>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'weather:data_create' %}?station_id={{ selected_station.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add First Reading
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Weather Forecast -->
            <div class="card mb-4" style="background: rgba(255, 255, 255, 0.95); border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);">
                <div class="card-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="mb-0">Weather Forecast</h5>
                    <a href="{% url 'weather:forecast_create' %}?station_id={{ selected_station.id }}" class="btn btn-sm btn-light">
                        <i class="fas fa-plus me-1"></i> Add Forecast
                    </a>
                </div>
                <div class="card-body">
                    {% if forecasts %}
                    <div class="row">
                        {% for forecast in forecasts|slice:":5" %}
                        <div class="col">
                            <div class="text-center">
                                <p class="mb-1 fw-bold">{{ forecast.forecast_date|date:"D, M d" }}</p>
                                <p class="small text-muted mb-2">{{ forecast.forecast_time|time:"H:i" }}</p>

                                {% if 'sunny' in forecast.condition|lower or 'clear' in forecast.condition|lower %}
                                <i class="fas fa-sun text-warning mb-2" style="font-size: 2rem;"></i>
                                {% elif 'cloud' in forecast.condition|lower %}
                                <i class="fas fa-cloud text-secondary mb-2" style="font-size: 2rem;"></i>
                                {% elif 'rain' in forecast.condition|lower or 'shower' in forecast.condition|lower %}
                                <i class="fas fa-cloud-rain text-primary mb-2" style="font-size: 2rem;"></i>
                                {% elif 'storm' in forecast.condition|lower or 'thunder' in forecast.condition|lower %}
                                <i class="fas fa-bolt text-warning mb-2" style="font-size: 2rem;"></i>
                                {% else %}
                                <i class="fas fa-cloud-sun text-primary mb-2" style="font-size: 2rem;"></i>
                                {% endif %}

                                <p class="mb-1">{{ forecast.temperature_max|floatformat:0 }}°C / {{ forecast.temperature_min|floatformat:0 }}°C</p>
                                <p class="small mb-1">{{ forecast.condition }}</p>
                                <p class="small mb-0">
                                    <i class="fas fa-tint text-primary me-1"></i> {{ forecast.precipitation_probability|floatformat:0 }}%
                                </p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No forecast data available for this station.</span>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'weather:forecast_create' %}?station_id={{ selected_station.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add Forecast
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:forecast_list' %}?station={{ selected_station.id }}" class="btn btn-sm btn-outline-primary w-100">
                        View Full Forecast
                    </a>
                </div>
            </div>

            <!-- Weather History Chart -->
            <div class="card" style="background: rgba(255, 255, 255, 0.95); border-radius: 20px; border: none; box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);">
                <div class="card-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 20px 20px 0 0; border: none;">
                    <h5 class="mb-0">Weather History</h5>
                </div>
                <div class="card-body">
                    {% if historical_data %}
                    <ul class="nav nav-tabs mb-3" id="weatherChartTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="temperature-tab" data-bs-toggle="tab" data-bs-target="#temperature" type="button" role="tab" aria-controls="temperature" aria-selected="true">Temperature</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="precipitation-tab" data-bs-toggle="tab" data-bs-target="#precipitation" type="button" role="tab" aria-controls="precipitation" aria-selected="false">Precipitation</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="humidity-tab" data-bs-toggle="tab" data-bs-target="#humidity" type="button" role="tab" aria-controls="humidity" aria-selected="false">Humidity</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="wind-tab" data-bs-toggle="tab" data-bs-target="#wind" type="button" role="tab" aria-controls="wind" aria-selected="false">Wind</button>
                        </li>
                    </ul>

                    <div class="tab-content" id="weatherChartTabsContent">
                        <div class="tab-pane fade show active" id="temperature" role="tabpanel" aria-labelledby="temperature-tab">
                            <div style="height: 300px;">
                                <canvas id="temperatureChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="precipitation" role="tabpanel" aria-labelledby="precipitation-tab">
                            <div style="height: 300px;">
                                <canvas id="precipitationChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="humidity" role="tabpanel" aria-labelledby="humidity-tab">
                            <div style="height: 300px;">
                                <canvas id="humidityChart"></canvas>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="wind" role="tabpanel" aria-labelledby="wind-tab">
                            <div style="height: 300px;">
                                <canvas id="windChart"></canvas>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No historical data available for this station.</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:weather_history' %}?station={{ selected_station.id }}" class="btn btn-sm btn-outline-primary w-100">
                        View Full History
                    </a>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-4">
            <!-- Active Weather Alerts -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Active Weather Alerts</h5>
                    <a href="{% url 'weather:alert_create' %}?station_id={{ selected_station.id }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i> Add Alert
                    </a>
                </div>
                <div class="card-body">
                    {% if active_alerts %}
                    {% for alert in active_alerts %}
                    <div class="alert {% if alert.severity == 'High' or alert.severity == 'Extreme' %}alert-danger{% elif alert.severity == 'Medium' %}alert-warning{% else %}alert-info{% endif %}">
                        <div class="d-flex">
                            <div class="me-3">
                                {% if alert.severity == 'High' or alert.severity == 'Extreme' %}
                                <i class="fas fa-exclamation-circle text-danger"></i>
                                {% elif alert.severity == 'Medium' %}
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% else %}
                                <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div>
                                <h6 class="alert-heading">{{ alert.title }}</h6>
                                <p class="mb-1">{{ alert.description }}</p>
                                <p class="small mb-0">
                                    <span class="badge {% if alert.severity == 'High' or alert.severity == 'Extreme' %}bg-danger{% elif alert.severity == 'Medium' %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                        {{ alert.severity }} - {{ alert.alert_type }}
                                    </span>
                                    <span class="ms-2">Until {{ alert.end_time|date:"M d, H:i" }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                        <h5>No Active Alerts</h5>
                        <p class="text-muted">There are no active weather alerts at this time.</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:alert_list' %}" class="btn btn-sm btn-outline-primary w-100">
                        View All Alerts
                    </a>
                </div>
            </div>

            <!-- Weather Impacts -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Weather Impacts on Farming</h5>
                </div>
                <div class="card-body">
                    {% if current_impacts %}
                    {% for impact in current_impacts %}
                    <div class="card mb-3 border-0 {% if impact.impact_level == 'Positive' %}bg-success bg-opacity-10{% elif impact.impact_level == 'Negative' %}bg-warning bg-opacity-10{% elif impact.impact_level == 'Severe' %}bg-danger bg-opacity-10{% else %}bg-light{% endif %}">
                        <div class="card-body">
                            <h6 class="card-title">
                                {{ impact.condition }}
                                <span class="badge {% if impact.impact_level == 'Positive' %}bg-success{% elif impact.impact_level == 'Negative' %}bg-warning text-dark{% elif impact.impact_level == 'Severe' %}bg-danger{% else %}bg-secondary{% endif %}">
                                    {{ impact.impact_level }}
                                </span>
                            </h6>
                            <p class="card-text small mb-2">{{ impact.description }}</p>
                            <p class="card-text small mb-0"><strong>Recommendations:</strong> {{ impact.recommendations }}</p>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No specific impacts for current weather conditions.</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:impact_list' %}" class="btn btn-sm btn-outline-primary w-100">
                        View All Weather Impacts
                    </a>
                </div>
            </div>

            <!-- Daily Summary -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Daily Summary</h5>
                </div>
                <div class="card-body">
                    {% if daily_data %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Avg Temp</th>
                                    <th>Min/Max</th>
                                    <th>Precip</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for day in daily_data %}
                                <tr>
                                    <td>{{ day.date|date:"M d" }}</td>
                                    <td>{{ day.avg_temp|floatformat:1 }}°C</td>
                                    <td>{{ day.min_temp|floatformat:1 }}/{{ day.max_temp|floatformat:1 }}°C</td>
                                    <td>{{ day.total_precip|floatformat:1 }} mm</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No daily summary data available.</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Station selector
    document.getElementById('station').addEventListener('change', function() {
        this.form.submit();
    });

    {% if historical_data %}
    // Temperature Chart
    const temperatureCtx = document.getElementById('temperatureChart').getContext('2d');
    const temperatureChart = new Chart(temperatureCtx, {
        type: 'line',
        data: {
            labels: [{% for data in historical_data %}'{{ data.timestamp|date:"M d, H:i" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Temperature (°C)',
                data: [{% for data in historical_data %}{{ data.temperature }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false
                }
            }
        }
    });

    // Precipitation Chart
    const precipitationCtx = document.getElementById('precipitationChart').getContext('2d');
    const precipitationChart = new Chart(precipitationCtx, {
        type: 'bar',
        data: {
            labels: [{% for data in historical_data %}'{{ data.timestamp|date:"M d, H:i" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Precipitation (mm)',
                data: [{% for data in historical_data %}{{ data.precipitation }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgb(54, 162, 235)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Humidity Chart
    const humidityCtx = document.getElementById('humidityChart').getContext('2d');
    const humidityChart = new Chart(humidityCtx, {
        type: 'line',
        data: {
            labels: [{% for data in historical_data %}'{{ data.timestamp|date:"M d, H:i" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Humidity (%)',
                data: [{% for data in historical_data %}{{ data.humidity }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false
                }
            }
        }
    });

    // Wind Chart
    const windCtx = document.getElementById('windChart').getContext('2d');
    const windChart = new Chart(windCtx, {
        type: 'line',
        data: {
            labels: [{% for data in historical_data %}'{{ data.timestamp|date:"M d, H:i" }}'{% if not forloop.last %}, {% endif %}{% endfor %}],
            datasets: [{
                label: 'Wind Speed (km/h)',
                data: [{% for data in historical_data %}{{ data.wind_speed }}{% if not forloop.last %}, {% endif %}{% endfor %}],
                borderColor: 'rgb(153, 102, 255)',
                backgroundColor: 'rgba(153, 102, 255, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    function refreshWeather() {
        // Show loading spinner
        const refreshBtn = document.querySelector('button[onclick="refreshWeather()"]');
        const originalContent = refreshBtn.innerHTML;
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Reload the page after a short delay to simulate API refresh
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
