import unittest
from unittest.mock import patch, MagicMock
import os
import sys
from io import String<PERSON>
from django.test import TestCase
from django.core.management import call_command

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Try to import the cache module
try:
    from core.api_key_cache import api_key_cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False


@unittest.skipIf(not CACHE_AVAILABLE, "API key cache module not available")
class TestClearAPIKeyCache(TestCase):
    """Test suite for the clear_api_key_cache command."""

    def setUp(self):
        """Set up test environment."""
        # Sample test keys and results
        self.test_key1 = "AIzaSyTestKey12345678901234567890"
        self.test_results1 = {
            'key': self.test_key1,
            'status': 'success',
            'message': 'API key is valid'
        }
        
        self.test_key2 = "AIzaSyAnotherKey12345678901234567"
        self.test_results2 = {
            'key': self.test_key2,
            'status': 'error',
            'message': 'API key is invalid'
        }
        
        # Clear the cache before tests
        api_key_cache.clear()
        
        # Add test entries to cache
        api_key_cache.set(self.test_key1, self.test_results1)
        api_key_cache.set(self.test_key2, self.test_results2)

    def tearDown(self):
        """Clean up after tests."""
        # Clear the cache after tests
        api_key_cache.clear()

    def test_clear_all_cache(self):
        """Test clearing all cache entries."""
        # Verify cache entries exist
        self.assertEqual(api_key_cache.get(self.test_key1), self.test_results1)
        self.assertEqual(api_key_cache.get(self.test_key2), self.test_results2)
        
        # Call the command to clear all cache
        out = StringIO()
        call_command('clear_api_key_cache', stdout=out)
        
        # Verify output
        output = out.getvalue()
        self.assertIn('Clearing all API key cache entries', output)
        self.assertIn('All API key cache entries cleared', output)
        
        # Verify cache is cleared
        self.assertIsNone(api_key_cache.get(self.test_key1))
        self.assertIsNone(api_key_cache.get(self.test_key2))

    def test_clear_specific_key(self):
        """Test clearing a specific key from cache."""
        # Verify cache entries exist
        self.assertEqual(api_key_cache.get(self.test_key1), self.test_results1)
        self.assertEqual(api_key_cache.get(self.test_key2), self.test_results2)
        
        # Call the command to clear specific key
        out = StringIO()
        call_command('clear_api_key_cache', key=self.test_key1, stdout=out)
        
        # Verify output
        output = out.getvalue()
        self.assertIn(f"Clearing cache for API key: {self.test_key1[:6]}...{self.test_key1[-4:]}", output)
        self.assertIn('Cache cleared for specified API key', output)
        
        # Verify only the specified key is cleared
        self.assertIsNone(api_key_cache.get(self.test_key1))
        self.assertEqual(api_key_cache.get(self.test_key2), self.test_results2)

    @patch('core.api_key_cache.api_key_cache.clear')
    def test_command_calls_api_key_cache_clear(self, mock_clear):
        """Test that the command calls the api_key_cache.clear method."""
        # Call the command without a key
        call_command('clear_api_key_cache')
        
        # Verify api_key_cache.clear() was called without args
        mock_clear.assert_called_once_with()
        
        # Reset mock
        mock_clear.reset_mock()
        
        # Call the command with a key
        call_command('clear_api_key_cache', key='test_key')
        
        # Verify api_key_cache.clear(key) was called with the key
        mock_clear.assert_called_once_with('test_key')


if __name__ == '__main__':
    unittest.main()