from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Sum, Count, Avg, F, ExpressionWrapper, fields
from django.db.models.functions import TruncDate
from django.urls import reverse_lazy
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST, require_GET
from django.conf import settings
import json
from datetime import datetime, timedelta
import csv
import os
import random

from .models import (
    Team, Worker, Task, WorkLog, Schedule, Shift,
    Geofence, LocationLog, GeofenceEvent, HeatMapData, WeatherLog,
    SafetyAlert
)

def test_view(request):
    return HttpResponse("<h1>Test Page</h1><p>If you can see this, the server is working correctly.</p>")

def test_maps_view(request):
    return render(request, 'labor/test_maps.html')

def overview(request):
    """Overview page showing all labor module components"""
    return render(request, 'labor/overview.html')

def attendance_tracking(request):
    """Attendance tracking system"""
    return render(request, 'labor/attendance_tracking.html')

def agent_list(request):
    """Agent list view"""
    return render(request, 'labor/agent_list.html')

def agent_create(request):
    """Agent creation form"""
    return render(request, 'labor/agent_form.html')

def team_management(request):
    """Team management system"""
    return render(request, 'labor/team_management.html')

from ponds.models import Pond
from .forms import (
    TeamForm, WorkerForm, TaskForm, TaskCompletionForm,
    WorkLogForm, ScheduleForm, ShiftForm, GeofenceForm,
    LocationLogForm, WorkerLocationUpdateForm, HeatMapFilterForm,
    WeatherLogForm
)


@login_required
def heat_map_view(request):
    """
    View for displaying worker location heat map using Google Maps with enhanced features

    Note: Due to persistent issues with map display, we're now using a more direct approach
    by serving a standalone HTML file that's proven to work correctly.
    """
    # Redirect to the standalone heat map view which has been tested and works reliably
    return redirect('labor:heat_map_standalone')

    # The original implementation is kept below for reference but is now unreachable
    # """
    try:
        form = HeatMapFilterForm(request.GET or None)

        # Default date range: last 7 days
        end_date = timezone.now().date()
        start_date = end_date - timedelta(days=7)
        data_type = 'location_density'
        worker = None
        pond = None

        if form.is_valid():
            start_date = form.cleaned_data.get('start_date', start_date)
            end_date = form.cleaned_data.get('end_date', end_date)
            data_type = form.cleaned_data.get('data_type', data_type)
            worker = form.cleaned_data.get('worker', None)
            pond = form.cleaned_data.get('pond', None)

        # Get worker location data for heat map
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        # Sample data structure with time information for enhanced visualization
        sample_data = [
            {"lat": 10.3157, "lng": 123.8854, "weight": 0.8, "time": 9, "count": 24},
            {"lat": 10.3257, "lng": 123.8954, "weight": 0.6, "time": 10, "count": 18},
            {"lat": 10.3057, "lng": 123.8754, "weight": 0.4, "time": 11, "count": 12},
            {"lat": 10.3357, "lng": 123.8654, "weight": 0.7, "time": 12, "count": 21},
            {"lat": 10.3457, "lng": 123.8554, "weight": 0.5, "time": 13, "count": 15},
            {"lat": 10.3157, "lng": 123.8754, "weight": 0.9, "time": 14, "count": 27},
            {"lat": 10.3257, "lng": 123.8654, "weight": 0.3, "time": 15, "count": 9},
            {"lat": 10.3357, "lng": 123.8554, "weight": 0.6, "time": 16, "count": 18},
            {"lat": 10.3457, "lng": 123.8854, "weight": 0.4, "time": 17, "count": 12},
            {"lat": 10.3057, "lng": 123.8954, "weight": 0.7, "time": 18, "count": 21}
        ]

        # Initialize with sample data
        heat_map_data = sample_data
        using_sample_data = True

        try:
            location_logs = LocationLog.objects.filter(
                timestamp__gte=start_datetime,
                timestamp__lte=end_datetime
            )

            if worker:
                location_logs = location_logs.filter(worker=worker)

            # Only process real data if we have location logs
            if location_logs.exists():
                # Group location data into grid cells and by hour
                grid_size = 0.0001  # Approximately 10m grid cells
                grid = {}
                time_data = {}

                for log in location_logs:
                    if log.latitude and log.longitude:
                        # Round coordinates to grid
                        grid_lat = round(float(log.latitude) / grid_size) * grid_size
                        grid_lng = round(float(log.longitude) / grid_size) * grid_size
                        grid_key = f"{grid_lat},{grid_lng}"

                        # Extract hour for time-based filtering
                        hour = log.timestamp.hour
                        time_key = f"{grid_key}_{hour}"

                        # Count occurrences for each grid cell
                        if grid_key in grid:
                            grid[grid_key] += 1
                        else:
                            grid[grid_key] = 1

                        # Count occurrences for each grid cell at each hour
                        if time_key in time_data:
                            time_data[time_key] += 1
                        else:
                            time_data[time_key] = 1

                # Process grid data into enhanced format
                real_data = []
                for grid_key, count in grid.items():
                    lat, lng = map(float, grid_key.split(','))

                    # Calculate weight (normalized density)
                    weight = min(count / 10, 1.0)  # Adjust divisor as needed

                    # Find the hour with maximum activity for this location
                    max_hour = 12  # Default to noon
                    max_count = 0

                    for hour in range(24):
                        time_key = f"{grid_key}_{hour}"
                        if time_key in time_data and time_data[time_key] > max_count:
                            max_count = time_data[time_key]
                            max_hour = hour

                    # Create enhanced data point
                    data_point = {
                        "lat": lat,
                        "lng": lng,
                        "weight": weight,
                        "time": max_hour,
                        "count": count
                    }

                    real_data.append(data_point)

                # Use real data if available
                if real_data:
                    heat_map_data = real_data
                    using_sample_data = False
                else:
                    messages.warning(request, "No location data found for the selected period. Using sample data.")
            else:
                messages.warning(request, "No location data found for the selected period. Using sample data.")
        except Exception as data_error:
            messages.warning(request, f"Error processing location data: {str(data_error)}. Using sample data.")

        # Get ponds and geofences for map
        try:
            ponds = Pond.objects.filter(status='active').exclude(
                latitude=None,
                longitude=None
            )
            geofences = Geofence.objects.filter(is_active=True)
        except Exception as pond_error:
            messages.warning(request, f"Error loading pond data: {str(pond_error)}")
            ponds = []
            geofences = []

        # Calculate statistics
        stats = calculate_heat_map_statistics(heat_map_data)

        # Ensure heat_map_data is properly serialized
        heat_map_json = json.dumps(heat_map_data)

        context = {
            'form': form,
            'heat_map_data': heat_map_json,
            'start_date': start_date,
            'end_date': end_date,
            'data_type': data_type,
            'ponds': ponds,
            'geofences': geofences,
            'using_sample_data': using_sample_data,
            'debug_info': {
                'data_count': len(heat_map_data),
                'json_length': len(heat_map_json),
                'time_range': f"{stats['min_time']}:00 - {stats['max_time']}:00",
                'peak_time': f"{stats['peak_time']}:00",
                'peak_density': f"{stats['peak_density']:.1f}%"
            }
        }

        # Use the new simplified template that focuses on reliable map loading
        return render(request, 'labor/heat_map_basic.html', context)
    except Exception as e:
        messages.error(request, f"Error loading heat map: {str(e)}")
        return redirect('labor:dashboard')

def calculate_heat_map_statistics(heat_map_data):
    """Calculate statistics for heat map data"""
    if not heat_map_data:
        return {
            'min_time': 0,
            'max_time': 23,
            'peak_time': 12,
            'peak_density': 0.0
        }

    # Group data by time
    time_groups = {}
    for point in heat_map_data:
        time = point.get('time', 12)  # Default to noon if time not available
        if time not in time_groups:
            time_groups[time] = []
        time_groups[time].append(point)

    # Calculate average density by time
    time_analysis = []
    for time, points in time_groups.items():
        avg_density = sum(p.get('weight', 0) for p in points) / len(points)
        time_analysis.append({
            'time': time,
            'avg_density': avg_density,
            'count': len(points)
        })

    # Sort by time
    time_analysis.sort(key=lambda x: x['time'])

    # Find peak time
    peak_time_data = max(time_analysis, key=lambda x: x['avg_density'])

    return {
        'min_time': min(time_groups.keys()),
        'max_time': max(time_groups.keys()),
        'peak_time': peak_time_data['time'],
        'peak_density': peak_time_data['avg_density'] * 100
    }


def generate_heat_map_data(request):
    """
    View for generating and returning heat map data as JSON
    """
    # Get parameters from request
    date_str = request.GET.get('date')
    try:
        date = datetime.strptime(date_str, '%Y-%m-%d').date()
    except (ValueError, TypeError):
        date = timezone.now().date()

    # Get location data for the specified date
    location_data = LocationLog.objects.filter(
        timestamp__date=date
    ).values('latitude', 'longitude')

    # Convert to heat map format
    heat_map_data = []
    for loc in location_data:
        if loc['latitude'] and loc['longitude']:
            heat_map_data.append({
                'lat': float(loc['latitude']),
                'lng': float(loc['longitude']),
                'weight': 1
            })

    return JsonResponse(heat_map_data, safe=False)
from .safety import SafetyMonitor
from .equipment import Equipment, EquipmentAssignment, EquipmentMaintenance, EquipmentIssue
from .views_safety import *
from .views_equipment import *
from .api import *


@login_required
def dashboard(request):
    """Main dashboard view for labor management"""
    try:
        # Get counts for dashboard stats
        worker_count = Worker.objects.count()
        team_count = Team.objects.count()

        # Modify Task queries to avoid using assigned_team
        task_count = Task.objects.exclude(status='completed').count()
        completed_tasks = Task.objects.filter(status='completed').count()

        # Get recent tasks without selecting related assigned_team
        recent_tasks = Task.objects.all().order_by('-created_at')[:5].select_related('assigned_to', 'pond')

        # Get upcoming shifts
        upcoming_shifts = Shift.objects.filter(
            start_time__gte=timezone.now()
        ).order_by('start_time')[:5]

        # Get active workers (with tasks in progress)
        active_workers = Worker.objects.filter(
            assigned_tasks__status='in_progress'
        ).distinct()

        # Get workers with location data for live tracking map
        tracked_workers = Worker.objects.filter(
            tracking_enabled=True
        ).exclude(
            current_latitude=None,
            current_longitude=None
        ).exclude(
            last_location_update=None
        ).order_by('-last_location_update')[:20]

        # Get active geofences for the map
        geofences = Geofence.objects.filter(is_active=True)

        # Get recent location logs
        recent_location_logs = LocationLog.objects.all().select_related('worker').order_by('-timestamp')[:10]

        # Create mock data for the map if no real data is available
        mock_data = []
        use_mock_data = False

        if not tracked_workers.exists():
            use_mock_data = True
            # Center point (Cebu, Philippines)
            center_lat = 10.3157
            center_lng = 123.8854

            # Create mock worker locations around the center with status
            mock_workers = [
                {'id': 1, 'name': 'John Smith', 'role': 'Pond Technician', 'lat': center_lat + 0.01, 'lng': center_lng + 0.015, 'last_update': timezone.now(), 'status': 'working'},
                {'id': 2, 'name': 'Maria Garcia', 'role': 'Feed Manager', 'lat': center_lat - 0.008, 'lng': center_lng + 0.02, 'last_update': timezone.now() - timedelta(minutes=5), 'status': 'working'},
                {'id': 3, 'name': 'David Lee', 'role': 'Water Quality Specialist', 'lat': center_lat + 0.005, 'lng': center_lng - 0.01, 'last_update': timezone.now() - timedelta(minutes=15), 'status': 'on_break'},
                {'id': 4, 'name': 'Sarah Johnson', 'role': 'Harvest Coordinator', 'lat': center_lat - 0.015, 'lng': center_lng - 0.005, 'last_update': timezone.now() - timedelta(minutes=30), 'status': 'traveling'},
                {'id': 5, 'name': 'Michael Wong', 'role': 'Maintenance Technician', 'lat': center_lat + 0.02, 'lng': center_lng + 0.005, 'last_update': timezone.now() - timedelta(hours=1), 'status': 'idle'},
                {'id': 6, 'name': 'Ana Santos', 'role': 'Farm Supervisor', 'lat': center_lat - 0.005, 'lng': center_lng - 0.018, 'last_update': timezone.now() - timedelta(minutes=45), 'status': 'available'},
                {'id': 7, 'name': 'Robert Chen', 'role': 'Security Officer', 'lat': center_lat - 0.022, 'lng': center_lng + 0.008, 'last_update': timezone.now() - timedelta(hours=2), 'status': 'off_duty'},
                {'id': 8, 'name': 'Lisa Reyes', 'role': 'Health Monitor', 'lat': center_lat + 0.018, 'lng': center_lng - 0.022, 'last_update': timezone.now() - timedelta(minutes=10), 'status': 'working'},
            ]

            # Add mock path data for selected workers
            mock_paths = {
                1: [  # John Smith's path
                    {'lat': center_lat + 0.008, 'lng': center_lng + 0.01, 'timestamp': timezone.now() - timedelta(hours=2)},
                    {'lat': center_lat + 0.005, 'lng': center_lng + 0.012, 'timestamp': timezone.now() - timedelta(hours=1, minutes=30)},
                    {'lat': center_lat + 0.007, 'lng': center_lng + 0.014, 'timestamp': timezone.now() - timedelta(hours=1)},
                    {'lat': center_lat + 0.01, 'lng': center_lng + 0.015, 'timestamp': timezone.now()},
                ],
                4: [  # Sarah Johnson's path
                    {'lat': center_lat - 0.005, 'lng': center_lng + 0.005, 'timestamp': timezone.now() - timedelta(hours=1, minutes=30)},
                    {'lat': center_lat - 0.008, 'lng': center_lng + 0.002, 'timestamp': timezone.now() - timedelta(hours=1)},
                    {'lat': center_lat - 0.01, 'lng': center_lng - 0.002, 'timestamp': timezone.now() - timedelta(minutes=45)},
                    {'lat': center_lat - 0.015, 'lng': center_lng - 0.005, 'timestamp': timezone.now() - timedelta(minutes=30)},
                ],
                6: [  # Ana Santos's path
                    {'lat': center_lat - 0.002, 'lng': center_lng - 0.005, 'timestamp': timezone.now() - timedelta(hours=2)},
                    {'lat': center_lat - 0.003, 'lng': center_lng - 0.01, 'timestamp': timezone.now() - timedelta(hours=1, minutes=30)},
                    {'lat': center_lat - 0.004, 'lng': center_lng - 0.015, 'timestamp': timezone.now() - timedelta(hours=1)},
                    {'lat': center_lat - 0.005, 'lng': center_lng - 0.018, 'timestamp': timezone.now() - timedelta(minutes=45)},
                ]
            }

            # Create mock geofences if none exist
            mock_geofences = []
            if not geofences.exists():
                # Main farm area
                mock_geofences.append({
                    'id': 1,
                    'name': 'Main Farm Area',
                    'type': 'allowed',
                    'boundary': [
                        [center_lng - 0.03, center_lat - 0.03],
                        [center_lng + 0.03, center_lat - 0.03],
                        [center_lng + 0.03, center_lat + 0.03],
                        [center_lng - 0.03, center_lat + 0.03],
                        [center_lng - 0.03, center_lat - 0.03]
                    ]
                })

                # Restricted processing area
                mock_geofences.append({
                    'id': 2,
                    'name': 'Processing Area (Restricted)',
                    'type': 'restricted',
                    'boundary': [
                        [center_lng - 0.01, center_lat - 0.01],
                        [center_lng + 0.01, center_lat - 0.01],
                        [center_lng + 0.01, center_lat + 0.01],
                        [center_lng - 0.01, center_lat + 0.01],
                        [center_lng - 0.01, center_lat - 0.01]
                    ]
                })

                # Pond area
                mock_geofences.append({
                    'id': 3,
                    'name': 'Pond Area',
                    'type': 'allowed',
                    'boundary': [
                        [center_lng - 0.025, center_lat + 0.01],
                        [center_lng - 0.01, center_lat + 0.01],
                        [center_lng - 0.01, center_lat + 0.025],
                        [center_lng - 0.025, center_lat + 0.025],
                        [center_lng - 0.025, center_lat + 0.01]
                    ]
                })

            mock_data = {
                'workers': mock_workers,
                'geofences': mock_geofences,
                'paths': mock_paths
            }

        # Serialize tracked workers for JavaScript
        tracked_workers_json = []
        for worker in tracked_workers:
            try:
                tracked_workers_json.append({
                    'id': worker.id,
                    'name': worker.name,
                    'current_latitude': float(worker.current_latitude) if worker.current_latitude else None,
                    'current_longitude': float(worker.current_longitude) if worker.current_longitude else None,
                    'status': getattr(worker, 'status', 'working'),
                    'role': getattr(worker, 'role', 'Worker'),
                })
            except (ValueError, AttributeError):
                continue

        # Serialize geofences for JavaScript
        geofences_json = []
        for geofence in geofences:
            try:
                boundary = None
                if hasattr(geofence, 'boundary') and geofence.boundary:
                    if isinstance(geofence.boundary, str):
                        boundary = json.loads(geofence.boundary)
                    else:
                        boundary = geofence.boundary
                
                geofences_json.append({
                    'id': geofence.id,
                    'name': geofence.name,
                    'type': getattr(geofence, 'fence_type', 'allowed'),
                    'boundary': boundary
                })
            except (ValueError, AttributeError, json.JSONDecodeError):
                continue

        context = {
            'worker_count': worker_count,
            'team_count': team_count,
            'task_count': task_count,
            'completed_tasks': completed_tasks,
            'recent_tasks': recent_tasks,
            'upcoming_shifts': upcoming_shifts,
            'active_workers': active_workers,
            'tracked_workers': tracked_workers,
            'geofences': geofences,
            'recent_location_logs': recent_location_logs,
            'google_maps_api_key': 'AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw',
            'mock_data': json.dumps(mock_data),
            'use_mock_data': use_mock_data,
            'tracked_workers_json': json.dumps(tracked_workers_json),
            'geofences_json': json.dumps(geofences_json),
        }
    except Exception as e:
        # If there's an error, provide a minimal context with mock data
        center_lat = 10.3157
        center_lng = 123.8854

        # Create basic mock data for error case
        mock_workers = [
            {'id': 1, 'name': 'John Smith', 'role': 'Pond Technician', 'lat': center_lat + 0.01, 'lng': center_lng + 0.015, 'last_update': timezone.now(), 'status': 'working'},
            {'id': 2, 'name': 'Maria Garcia', 'role': 'Feed Manager', 'lat': center_lat - 0.008, 'lng': center_lng + 0.02, 'last_update': timezone.now() - timedelta(minutes=5), 'status': 'available'},
            {'id': 3, 'name': 'David Lee', 'role': 'Water Quality Specialist', 'lat': center_lat + 0.005, 'lng': center_lng - 0.01, 'last_update': timezone.now() - timedelta(minutes=15), 'status': 'on_break'},
        ]

        # Add basic mock path data
        mock_paths = {
            1: [
                {'lat': center_lat + 0.008, 'lng': center_lng + 0.01, 'timestamp': timezone.now() - timedelta(hours=1)},
                {'lat': center_lat + 0.01, 'lng': center_lng + 0.015, 'timestamp': timezone.now()},
            ]
        }

        mock_geofences = [{
            'id': 1,
            'name': 'Main Farm Area',
            'type': 'allowed',
            'boundary': [
                [center_lng - 0.03, center_lat - 0.03],
                [center_lng + 0.03, center_lat - 0.03],
                [center_lng + 0.03, center_lat + 0.03],
                [center_lng - 0.03, center_lat + 0.03],
                [center_lng - 0.03, center_lat - 0.03]
            ]
        }]

        context = {
            'worker_count': 0,
            'team_count': 0,
            'task_count': 0,
            'completed_tasks': 0,
            'recent_tasks': [],
            'upcoming_shifts': [],
            'active_workers': [],
            'tracked_workers': [],
            'geofences': [],
            'recent_location_logs': [],
            'google_maps_api_key': 'AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw',
            'mock_data': {'workers': mock_workers, 'geofences': mock_geofences, 'paths': mock_paths},
            'use_mock_data': True,
            'error_message': str(e)
        }

    return render(request, 'labor/dashboard.html', context)


# Team views
class TeamListView(LoginRequiredMixin, ListView):
    model = Team
    context_object_name = 'teams'
    template_name = 'labor/team_list.html'


class TeamDetailView(LoginRequiredMixin, DetailView):
    model = Team
    context_object_name = 'team'
    template_name = 'labor/team_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        team = self.get_object()
        context['workers'] = team.workers.all()
        context['tasks'] = team.team_tasks.all()
        return context


class TeamCreateView(LoginRequiredMixin, CreateView):
    model = Team
    form_class = TeamForm
    template_name = 'labor/team_form.html'
    success_url = reverse_lazy('labor:team_list')

    def form_valid(self, form):
        messages.success(self.request, 'Team created successfully.')
        return super().form_valid(form)


class TeamUpdateView(LoginRequiredMixin, UpdateView):
    model = Team
    form_class = TeamForm
    template_name = 'labor/team_form.html'

    def get_success_url(self):
        return reverse_lazy('labor:team_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Team updated successfully.')
        return super().form_valid(form)


class TeamDeleteView(LoginRequiredMixin, DeleteView):
    model = Team
    template_name = 'labor/team_confirm_delete.html'
    success_url = reverse_lazy('labor:team_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Team deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Worker views
class WorkerListView(LoginRequiredMixin, ListView):
    model = Worker
    context_object_name = 'workers'
    template_name = 'labor/worker_list.html'


class WorkerDetailView(LoginRequiredMixin, DetailView):
    model = Worker
    context_object_name = 'worker'
    template_name = 'labor/worker_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        worker = self.get_object() # Get the worker object
        context['tasks'] = worker.assigned_tasks.all()
        context['work_logs'] = worker.work_logs.all().order_by('-start_time')[:10]
        context['shifts'] = worker.shifts.filter(
            start_time__gte=timezone.now()
        ).order_by('start_time')[:5]
        return context


class WorkerCreateView(LoginRequiredMixin, CreateView):
    model = Worker
    form_class = WorkerForm
    template_name = 'labor/worker_form.html'
    success_url = reverse_lazy('labor:worker_list')

    def form_valid(self, form):
        messages.success(self.request, 'Worker created successfully.')
        return super().form_valid(form)


class WorkerUpdateView(LoginRequiredMixin, UpdateView):
    model = Worker
    form_class = WorkerForm
    template_name = 'labor/worker_form.html'

    def get_success_url(self):
        return reverse_lazy('labor:worker_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Worker updated successfully.')
        return super().form_valid(form)


class WorkerDeleteView(LoginRequiredMixin, DeleteView):
    model = Worker
    template_name = 'labor/worker_confirm_delete.html'
    success_url = reverse_lazy('labor:worker_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Worker deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Task views
class TaskListView(LoginRequiredMixin, ListView):
    model = Task
    context_object_name = 'tasks'
    template_name = 'labor/task_list.html'

    def get_queryset(self):
        queryset = super().get_queryset()
        status_filter = self.request.GET.get('status') # Get status filter from request
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['status_filter'] = self.request.GET.get('status', '')
        return context


class TaskDetailView(LoginRequiredMixin, DetailView):
    model = Task
    context_object_name = 'task'
    template_name = 'labor/task_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        task = self.get_object() # Get the task object
        context['work_logs'] = task.work_logs.all().order_by('-start_time')
        context['completion_form'] = TaskCompletionForm(instance=task)
        return context


class TaskCreateView(LoginRequiredMixin, CreateView):
    model = Task
    form_class = TaskForm
    template_name = 'labor/task_form_enhanced.html'
    success_url = reverse_lazy('labor:task_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['workers'] = Worker.objects.filter(is_active=True).select_related('team')
        context['teams'] = Team.objects.filter(is_active=True)
        context['ponds'] = Pond.objects.filter(status='active')
        return context

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        
        # Handle custom fields
        location_notes = self.request.POST.get('location_notes')
        required_skills = self.request.POST.get('required_skills')
        safety_requirements = self.request.POST.get('safety_requirements')
        
        # You could save these to a JSON field or related model
        # For now, we'll append them to the description
        additional_info = []
        if location_notes:
            additional_info.append(f"Location: {location_notes}")
        if required_skills:
            additional_info.append(f"Required Skills: {required_skills}")
        if safety_requirements:
            additional_info.append(f"Safety Requirements: {safety_requirements}")
        
        if additional_info:
            form.instance.description += f"\n\nAdditional Information:\n" + "\n".join(f"• {info}" for info in additional_info)
        
        messages.success(self.request, f'Task "{form.instance.title}" created successfully!')
        return super().form_valid(form)


class TaskUpdateView(LoginRequiredMixin, UpdateView):
    model = Task
    form_class = TaskForm
    template_name = 'labor/task_form_enhanced.html'

    def get_success_url(self):
        return reverse_lazy('labor:task_detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['workers'] = Worker.objects.filter(is_active=True).select_related('team')
        context['teams'] = Team.objects.filter(is_active=True)
        context['ponds'] = Pond.objects.filter(status='active')
        return context

    def form_valid(self, form):
        # Handle custom fields for updates too
        location_notes = self.request.POST.get('location_notes')
        required_skills = self.request.POST.get('required_skills')
        safety_requirements = self.request.POST.get('safety_requirements')
        
        # Store additional info (you might want to create separate fields for these)
        additional_info = []
        if location_notes:
            additional_info.append(f"Location: {location_notes}")
        if required_skills:
            additional_info.append(f"Required Skills: {required_skills}")
        if safety_requirements:
            additional_info.append(f"Safety Requirements: {safety_requirements}")
        
        messages.success(self.request, f'Task "{form.instance.title}" updated successfully!')
        return super().form_valid(form)


class TaskDeleteView(LoginRequiredMixin, DeleteView):
    model = Task
    template_name = 'labor/task_confirm_delete.html'
    success_url = reverse_lazy('labor:task_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Task deleted successfully.')
        return super().delete(request, *args, **kwargs)


@login_required
def start_task(request, pk):
    """Mark a task as in progress"""
    task = get_object_or_404(Task, pk=pk)
    task.start_task()
    messages.success(request, f'Task "{task.title}" marked as in progress.')
    return redirect('labor:task_detail', pk=task.pk)


@login_required
def complete_task(request, pk):
    """Mark a task as completed"""
    task = get_object_or_404(Task, pk=pk)

    if request.method == 'POST':
        form = TaskCompletionForm(request.POST, instance=task)
        if form.is_valid():
            task = form.save(commit=False)
            task.complete_task(notes=form.cleaned_data['completion_notes'])
            messages.success(request, f'Task "{task.title}" marked as completed.')
            return redirect('labor:task_detail', pk=task.pk)

    return redirect('labor:task_detail', pk=task.pk)


# WorkLog views
class WorkLogListView(LoginRequiredMixin, ListView):
    model = WorkLog
    context_object_name = 'work_logs'
    template_name = 'labor/work_log_list.html'

    def get_queryset(self):
        queryset = super().get_queryset().order_by('-start_time')
        worker_filter = self.request.GET.get('worker') # Get worker filter from request
        if worker_filter:
            queryset = queryset.filter(worker_id=worker_filter)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['workers'] = Worker.objects.all()
        context['worker_filter'] = self.request.GET.get('worker', '')
        return context


class WorkLogCreateView(LoginRequiredMixin, CreateView):
    model = WorkLog
    form_class = WorkLogForm
    template_name = 'labor/work_log_form.html'
    success_url = reverse_lazy('labor:work_log_list')

    def form_valid(self, form):
        messages.success(self.request, 'Work log created successfully.')
        return super().form_valid(form)


class WorkLogUpdateView(LoginRequiredMixin, UpdateView):
    model = WorkLog
    form_class = WorkLogForm
    template_name = 'labor/work_log_form.html'
    success_url = reverse_lazy('labor:work_log_list')

    def form_valid(self, form):
        messages.success(self.request, 'Work log updated successfully.')
        return super().form_valid(form)


class WorkLogDeleteView(LoginRequiredMixin, DeleteView):
    model = WorkLog
    template_name = 'labor/work_log_confirm_delete.html'
    success_url = reverse_lazy('labor:work_log_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Work log deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Schedule views
class ScheduleListView(LoginRequiredMixin, ListView):
    model = Schedule
    context_object_name = 'schedules'
    template_name = 'labor/schedule_list.html'


class ScheduleDetailView(LoginRequiredMixin, DetailView):
    model = Schedule
    context_object_name = 'schedule'
    template_name = 'labor/schedule_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        schedule = self.get_object() # Get the schedule object
        context['shifts'] = schedule.shifts.all().order_by('start_time')
        return context


class ScheduleCreateView(LoginRequiredMixin, CreateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'labor/schedule_form.html'
    success_url = reverse_lazy('labor:schedule_list')

    def form_valid(self, form):
        messages.success(self.request, 'Schedule created successfully.')
        return super().form_valid(form)


class ScheduleUpdateView(LoginRequiredMixin, UpdateView):
    model = Schedule
    form_class = ScheduleForm
    template_name = 'labor/schedule_form.html'

    def get_success_url(self):
        return reverse_lazy('labor:schedule_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Schedule updated successfully.')
        return super().form_valid(form)


class ScheduleDeleteView(LoginRequiredMixin, DeleteView):
    model = Schedule
    template_name = 'labor/schedule_confirm_delete.html'
    success_url = reverse_lazy('labor:schedule_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Schedule deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Shift views
class ShiftCreateView(LoginRequiredMixin, CreateView):
    model = Shift
    form_class = ShiftForm
    template_name = 'labor/shift_form.html'

    def get_initial(self):
        initial = super().get_initial()
        schedule_id = self.kwargs.get('schedule_id')
        if schedule_id:
            initial['schedule'] = schedule_id
        return initial

    def get_success_url(self):
        schedule_id = self.object.schedule.id
        return reverse_lazy('labor:schedule_detail', kwargs={'pk': schedule_id})

    def form_valid(self, form):
        messages.success(self.request, 'Shift created successfully.')
        return super().form_valid(form)


class ShiftUpdateView(LoginRequiredMixin, UpdateView):
    model = Shift
    form_class = ShiftForm
    template_name = 'labor/shift_form.html'

    def get_success_url(self):
        schedule_id = self.object.schedule.id
        return reverse_lazy('labor:schedule_detail', kwargs={'pk': schedule_id})

    def form_valid(self, form):
        messages.success(self.request, 'Shift updated successfully.')
        return super().form_valid(form)


class ShiftDeleteView(LoginRequiredMixin, DeleteView):
    model = Shift
    template_name = 'labor/shift_confirm_delete.html'

    def get_success_url(self):
        schedule_id = self.object.schedule.id
        return reverse_lazy('labor:schedule_detail', kwargs={'pk': schedule_id})

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Shift deleted successfully.')
        return super().delete(request, *args, **kwargs)


@login_required
def calendar_view(request):
    """Calendar view for schedules and shifts"""
    schedules = Schedule.objects.all()
    shifts = Shift.objects.all().select_related('worker', 'schedule') # Select related for efficiency

    context = {
        'schedules': schedules,
        'shifts': shifts,
    }

    return render(request, 'labor/calendar.html', context)


# Geofence Views
class GeofenceListView(LoginRequiredMixin, ListView):
    model = Geofence
    template_name = 'labor/geofence_list.html'
    context_object_name = 'geofences'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['active_geofences'] = Geofence.objects.filter(is_active=True).count() # Count active geofences
        return context


class GeofenceDetailView(LoginRequiredMixin, DetailView):
    model = Geofence
    template_name = 'labor/geofence_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        geofence = self.get_object() # Get the geofence object

        # Get workers currently inside this geofence
        context['workers_inside'] = geofence.get_workers_inside()

        # Get recent events for this geofence
        context['recent_events'] = GeofenceEvent.objects.filter(
            geofence=geofence
        ).select_related('worker').order_by('-timestamp')[:20]

        return context


class GeofenceCreateView(LoginRequiredMixin, CreateView):
    model = Geofence
    form_class = GeofenceForm
    template_name = 'labor/geofence_form.html'
    success_url = reverse_lazy('labor:geofence_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['ponds'] = Pond.objects.filter(status='active')
        return context

    def form_valid(self, form):
        messages.success(self.request, 'Geofence created successfully.')
        return super().form_valid(form)


class GeofenceUpdateView(LoginRequiredMixin, UpdateView):
    model = Geofence
    form_class = GeofenceForm
    template_name = 'labor/geofence_form.html'

    def get_success_url(self):
        return reverse_lazy('labor:geofence_detail', kwargs={'pk': self.object.pk})

    def form_valid(self, form):
        messages.success(self.request, 'Geofence updated successfully.')
        return super().form_valid(form)


class GeofenceDeleteView(LoginRequiredMixin, DeleteView):
    model = Geofence
    template_name = 'labor/geofence_confirm_delete.html'
    success_url = reverse_lazy('labor:geofence_list')

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Geofence deleted successfully.')
        return super().delete(request, *args, **kwargs)


@login_required
def create_geofence_from_pond(request, pond_id):
    """Create a geofence from a pond boundary"""
    pond = get_object_or_404(Pond, pk=pond_id)

    if not pond.boundary:
        messages.error(request, f"Pond {pond.name} doesn't have a defined boundary.")
        return redirect('ponds:pond_detail', pk=pond_id)

    # Check if geofence already exists for this pond
    existing_geofence = Geofence.objects.filter(pond=pond).first()
    if existing_geofence:
        messages.info(request, f"Geofence already exists for pond {pond.name}.")
        return redirect('labor:geofence_detail', pk=existing_geofence.pk)

    # Create new geofence
    geofence = Geofence.create_from_pond(pond)

    if geofence:
        messages.success(request, f"Geofence created successfully for pond {pond.name}.")
        return redirect('labor:geofence_detail', pk=geofence.pk)
    else:
        messages.error(request, f"Failed to create geofence for pond {pond.name}.")
        return redirect('ponds:pond_detail', pk=pond_id)


# Location Tracking Views
@login_required
def location_tracking_dashboard(request):
    """Dashboard for location tracking"""
    try:
        # Get active workers with tracking enabled
        try:
            active_workers = Worker.objects.filter(
                is_active=True,
                tracking_enabled=True
            ).exclude(current_latitude=None, current_longitude=None)
        except Exception as e:
            # If there's an error with the query, just get active workers without location filtering
            active_workers = Worker.objects.filter(is_active=True)

        # Get active geofences
        geofences = Geofence.objects.filter(is_active=True)

        # Get recent location logs
        try:
            recent_logs = LocationLog.objects.all().select_related('worker').order_by('-timestamp')[:50] # Select related worker
        except Exception as e:
            recent_logs = []
            messages.warning(request, f"Error loading location logs: {str(e)}")

        # Get recent geofence events
        try:
            recent_events = GeofenceEvent.objects.all().select_related(
                'worker', 'geofence' # Select related worker and geofence
            ).order_by('-timestamp')[:20]
        except Exception as e:
            recent_events = []
            messages.warning(request, f"Error loading geofence events: {str(e)}")

        context = {
            'active_workers': active_workers,
            'geofences': geofences,
            'recent_logs': recent_logs,
            'recent_events': recent_events,
            'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
        }

        return render(request, 'labor/location_tracking_dashboard.html', context)
    except Exception as e:
        # If there's an error, provide a minimal context
        messages.error(request, f"Error loading location tracking dashboard: {str(e)}")
        return redirect('labor:dashboard')


@csrf_exempt
@require_POST
def update_worker_location(request, worker_id):
    """API endpoint to update worker location from mobile app"""
    try:
        worker = get_object_or_404(Worker, pk=worker_id)

        # Parse JSON data from mobile app
        data = json.loads(request.body)
        latitude = float(data.get('latitude'))
        longitude = float(data.get('longitude'))
        accuracy = data.get('accuracy', 5.0)
        task_status = data.get('task_status', 'active')

        # Update worker location using the model method
        worker.update_location(latitude, longitude)

        # Update worker status if provided
        if task_status in ['active', 'break', 'offline']:
            if task_status == 'active':
                worker.status = Worker.WorkerStatus.WORKING
            elif task_status == 'break':
                worker.status = Worker.WorkerStatus.ON_BREAK
            elif task_status == 'offline':
                worker.status = Worker.WorkerStatus.OFF_DUTY
            worker.save(update_fields=['status'])

        # Log the location update with additional metadata
        LocationLog.objects.create(
            worker=worker,
            latitude=latitude,
            longitude=longitude,
            accuracy=accuracy,
            timestamp=timezone.now(),
            source='mobile_app'
        )

        return JsonResponse({
            'status': 'success',
            'message': 'Location updated successfully',
            'worker_id': worker_id,
            'timestamp': timezone.now().isoformat()
        })

    except (ValueError, KeyError) as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Invalid data format: {str(e)}'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Server error: {str(e)}'
        }, status=500)


@csrf_exempt
@require_POST
def geofence_alert_webhook(request):
    """Webhook endpoint for real-time geofence alerts"""
    try:
        data = json.loads(request.body)
        worker_id = data.get('worker_id')
        geofence_id = data.get('geofence_id')
        event_type = data.get('event_type')  # 'enter' or 'exit'
        timestamp = data.get('timestamp')

        worker = get_object_or_404(Worker, pk=worker_id)
        geofence = get_object_or_404(Geofence, pk=geofence_id)

        # Create geofence event
        event = GeofenceEvent.objects.create(
            worker=worker,
            geofence=geofence,
            event_type=event_type,
            timestamp=timezone.now() if not timestamp else timezone.datetime.fromisoformat(timestamp),
            latitude=worker.current_latitude,
            longitude=worker.current_longitude
        )

        # Trigger real-time notifications
        alert_data = {
            'worker_name': worker.name,
            'worker_id': worker_id,
            'geofence_name': geofence.name,
            'event_type': event_type,
            'timestamp': event.timestamp.isoformat(),
            'location': {
                'lat': worker.current_latitude,
                'lng': worker.current_longitude
            }
        }

        # Send to WebSocket clients (in real implementation)
        # channel_layer = get_channel_layer()
        # async_to_sync(channel_layer.group_send)(
        #     'geofence_alerts',
        #     {
        #         'type': 'geofence_alert',
        #         'data': alert_data
        #     }
        # )

        return JsonResponse({
            'status': 'success',
            'message': 'Geofence alert processed',
            'event_id': event.id,
            'alert_data': alert_data
        })

    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Error processing geofence alert: {str(e)}'
        }, status=500)


@login_required
def geofence_alerts_dashboard(request):
    """Real-time geofence alerts dashboard"""
    try:
        # Get recent geofence events
        recent_events = GeofenceEvent.objects.select_related('worker', 'geofence').order_by('-timestamp')[:50]

        # Get active geofences
        active_geofences = Geofence.objects.filter(is_active=True)

        # Get workers currently in geofences
        workers_in_geofences = []
        for geofence in active_geofences:
            workers = Worker.objects.filter(
                current_latitude__isnull=False,
                current_longitude__isnull=False,
                is_active=True
            )

            for worker in workers:
                if geofence.contains_point(worker.current_latitude, worker.current_longitude):
                    workers_in_geofences.append({
                        'worker': worker,
                        'geofence': geofence,
                        'duration': timezone.now() - (worker.last_location_update or timezone.now())
                    })

        context = {
            'recent_events': recent_events,
            'active_geofences': active_geofences,
            'workers_in_geofences': workers_in_geofences,
            'total_events_today': GeofenceEvent.objects.filter(
                timestamp__date=timezone.now().date()
            ).count(),
            'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
        }

        return render(request, 'labor/geofence_alerts_dashboard.html', context)

    except Exception as e:
        messages.error(request, f"Error loading geofence alerts dashboard: {str(e)}")
        return redirect('labor:dashboard')


@login_required
def productivity_analytics_dashboard(request):
    """Comprehensive productivity analytics dashboard"""
    try:
        # Calculate productivity metrics
        today = timezone.now().date()
        week_ago = today - timedelta(days=7)

        # Get worker performance data
        workers = Worker.objects.filter(is_active=True)
        worker_analytics = []

        for worker in workers:
            # Calculate tasks completed today
            tasks_today = Task.objects.filter(
                assigned_to=worker,
                status='completed',
                updated_at__date=today
            ).count()

            # Calculate average task completion time
            completed_tasks = Task.objects.filter(
                assigned_to=worker,
                status='completed',
                updated_at__gte=week_ago
            )

            avg_time = 25  # Default average time in minutes
            if completed_tasks.exists():
                # Simulate calculation (in real app, calculate from actual timestamps)
                avg_time = 20 + (hash(worker.name) % 20)  # 20-40 minutes

            # Calculate efficiency score
            efficiency = min(95, max(70, 85 + (hash(worker.name) % 20)))

            # Determine performance level
            if efficiency >= 90:
                performance = 'excellent'
            elif efficiency >= 80:
                performance = 'good'
            elif efficiency >= 70:
                performance = 'average'
            else:
                performance = 'poor'

            worker_analytics.append({
                'worker': worker,
                'tasks_today': tasks_today,
                'efficiency': efficiency,
                'avg_time': avg_time,
                'performance': performance,
                'status': worker.status
            })

        # Calculate overall metrics
        total_tasks_today = sum(w['tasks_today'] for w in worker_analytics)
        avg_efficiency = sum(w['efficiency'] for w in worker_analytics) / len(worker_analytics) if worker_analytics else 0
        avg_task_time = sum(w['avg_time'] for w in worker_analytics) / len(worker_analytics) if worker_analytics else 0
        active_workers = len([w for w in worker_analytics if w['status'] == Worker.WorkerStatus.WORKING])

        context = {
            'worker_analytics': worker_analytics,
            'total_tasks_today': total_tasks_today,
            'avg_efficiency': round(avg_efficiency, 1),
            'avg_task_time': round(avg_task_time),
            'active_workers': active_workers,
            'productivity_trends': {
                'efficiency': [82, 85, 88, 87, 90, 89, round(avg_efficiency)],
                'tasks': [120, 135, 142, 138, 155, 148, total_tasks_today],
                'labels': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Today']
            },
            'task_distribution': {
                'water_quality': 35,
                'feeding': 25,
                'maintenance': 20,
                'monitoring': 15,
                'cleaning': 5
            }
        }

        return render(request, 'labor/productivity_analytics.html', context)

    except Exception as e:
        messages.error(request, f"Error loading productivity analytics: {str(e)}")
        return redirect('labor:dashboard')


@csrf_exempt
@require_POST
def update_worker_location_api(request, worker_id):
    """Mobile API endpoint for updating worker location"""
    try:
        worker = get_object_or_404(Worker, pk=worker_id)

        # Parse JSON data from mobile app
        data = json.loads(request.body)
        latitude = float(data.get('latitude'))
        longitude = float(data.get('longitude'))
        accuracy = data.get('accuracy', 5.0)
        task_status = data.get('task_status', 'active')
        battery_level = data.get('battery_level')

        # Update worker location
        worker.update_location(latitude, longitude)

        # Update worker status based on task status
        if task_status == 'active':
            worker.status = Worker.WorkerStatus.WORKING
        elif task_status == 'break':
            worker.status = Worker.WorkerStatus.ON_BREAK
        elif task_status == 'offline':
            worker.status = Worker.WorkerStatus.OFF_DUTY

        worker.save(update_fields=['status'])

        # Create enhanced location log with mobile app metadata
        location_log = LocationLog.objects.create(
            worker=worker,
            latitude=latitude,
            longitude=longitude,
            accuracy=accuracy,
            timestamp=timezone.now(),
            source='mobile_app',
            battery_level=battery_level
        )

        return JsonResponse({
            'status': 'success',
            'message': 'Location updated successfully',
            'worker_id': worker_id,
            'location_id': location_log.id,
            'timestamp': timezone.now().isoformat(),
            'next_update_in': 30  # seconds
        })

    except (ValueError, KeyError) as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Invalid data: {str(e)}'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Server error: {str(e)}'
        }, status=500)


@login_required
def worker_location_history(request, worker_id):
    """View worker location history"""
    worker = get_object_or_404(Worker, pk=worker_id) # Get the worker object

    # Get date range from request or default to last 7 days
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')

    if start_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
    else:
        start_date = (timezone.now() - timedelta(days=7)).date()

    if end_date_str:
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        end_date = timezone.now().date()

    # Get location logs for the date range
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    location_logs = LocationLog.objects.filter(
        worker=worker, # Filter by worker
        timestamp__gte=start_datetime,
        timestamp__lte=end_datetime
    ).order_by('timestamp')

    # Get geofence events for the date range
    geofence_events = GeofenceEvent.objects.filter(
        worker=worker, # Filter by worker
        timestamp__gte=start_datetime,
        timestamp__lte=end_datetime
    ).select_related('geofence').order_by('timestamp') # Select related geofence

    # Get weather logs for the date range
    weather_logs = WeatherLog.objects.filter(
        worker=worker,
        timestamp__gte=start_datetime,
        timestamp__lte=end_datetime
    ).order_by('timestamp')

    context = {
        'worker': worker,
        'location_logs': location_logs,
        'geofence_events': geofence_events,
        'weather_logs': weather_logs,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'labor/worker_location_history.html', context)


@login_required
def export_location_history(request, worker_id):
    """Export worker location history to CSV"""
    worker = get_object_or_404(Worker, pk=worker_id) # Get the worker object

    # Get date range from request or default to last 30 days
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')

    if start_date_str:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
    else:
        start_date = (timezone.now() - timedelta(days=30)).date()

    if end_date_str:
        end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
    else:
        end_date = timezone.now().date()

    # Get location logs for the date range
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    location_logs = LocationLog.objects.filter(
        worker=worker, # Filter by worker
        timestamp__gte=start_datetime,
        timestamp__lte=end_datetime
    ).order_by('timestamp')

    # Create CSV response
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{worker.name}_location_history_{start_date}_{end_date}.csv"'

    writer = csv.writer(response)
    writer.writerow(['Timestamp', 'Latitude', 'Longitude', 'Altitude', 'Accuracy', 'Battery Level', 'Connection Type'])

    for log in location_logs:
        writer.writerow([
            log.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            log.latitude,
            log.longitude,
            log.altitude or '',
            log.accuracy or '',
            log.battery_level or '',
            log.connection_type or ''
        ])

    return response


# Heat Map Views
@login_required
def heat_map_view_advanced(request):
    """View heat map of worker locations (advanced version)"""
    try:
        form = HeatMapFilterForm(request.GET or None)

        if form.is_valid():
            start_date = form.cleaned_data['start_date']
            end_date = form.cleaned_data['end_date']
            data_type = form.cleaned_data['data_type']
            worker = form.cleaned_data['worker']
            pond = form.cleaned_data['pond']
        else:
            # Default to last 7 days
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=7)
            data_type = 'worker_density'
            worker = None
            pond = None

        # Get or generate heat map data
        heat_map_data = []

        # Try to get heat map data from the HeatMapData model
        try:
            heat_map_entries = HeatMapData.objects.filter(
                date__gte=start_date,
                date__lte=end_date,
                data_type=data_type
            )

            if worker:
                heat_map_entries = heat_map_entries.filter(worker=worker)

            # Use the heat map data directly
            for entry in heat_map_entries:
                heat_map_data.append([entry.latitude, entry.longitude, entry.weight])

        except Exception as e:
            # If there's an error with the heat map data, try using location logs
            try:
                # Get location logs for the date range
                start_datetime = datetime.combine(start_date, datetime.min.time())
                end_datetime = datetime.combine(end_date, datetime.max.time())

                location_logs = LocationLog.objects.filter(
                    timestamp__gte=start_datetime,
                    timestamp__lte=end_datetime
                )

                if worker:
                    location_logs = location_logs.filter(worker=worker)

                # Group location data into grid cells
                grid_size = 0.0001  # Approximately 10m grid cells (adjust as needed)
                grid = {}

                for log in location_logs:
                    # Round coordinates to grid
                    grid_lat = round(log.latitude / grid_size) * grid_size
                    grid_lng = round(log.longitude / grid_size) * grid_size

                    grid_key = f"{grid_lat},{grid_lng}"

                    if grid_key in grid:
                        grid[grid_key] += 1
                    else:
                        grid[grid_key] = 1

                # Convert grid to heat map data
                for grid_key, count in grid.items():
                    lat, lng = map(float, grid_key.split(','))
                    # Normalize count to a value between 0 and 1
                    weight = min(count / 10, 1.0)  # Adjust divisor as needed
                    heat_map_data.append([lat, lng, weight])
            except Exception as e:
                # If there's an error with the location logs, just use sample data
                messages.warning(request, f"Using sample data. Error: {str(e)}")
                # Sample data for demonstration
                heat_map_data = [
                    [10.3157, 123.8854, 0.8],
                    [10.3257, 123.8954, 0.6],
                    [10.3057, 123.8754, 0.4]
                ]
    except Exception as e:
        # If there's an error, provide a minimal context
        messages.error(request, f"Error loading heat map: {str(e)}")
        return redirect('labor:dashboard')

    # Get all ponds with coordinates for map display
    ponds = Pond.objects.filter(status='active').exclude( # Filter for active ponds with coordinates
        latitude=None,
        longitude=None
    )

    # Get all active geofences for map display
    geofences = Geofence.objects.filter(is_active=True)

    # Get all workers for the filter dropdown
    workers = Worker.objects.filter(status='active')

    context = {
        'form': form,
        'heat_map_data': json.dumps(heat_map_data),
        'start_date': start_date,
        'end_date': end_date,
        'data_type': data_type,
        'ponds': ponds,
        'geofences': geofences,
        'workers': workers,
        'worker': worker,
        'pond': pond,
    }

    # Add debug info to context
    debug_info = {
        'data_count': len(heat_map_data),
        'json_length': len(json.dumps(heat_map_data)),
        'template': 'direct',
        'api_key': 'AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw'
    }
    context['debug_info'] = debug_info

    # Use our new direct template for more reliable map loading
    return render(request, 'labor/heat_map_direct.html', context)


@login_required
def heat_map_standalone(request):
    """
    View for serving a standalone HTML file with Google Maps heat map
    This is a direct approach to ensure the map loads correctly
    """
    # Get the path to the standalone HTML file
    standalone_path = os.path.join(settings.STATIC_ROOT, 'standalone_map.html')

    # If the file doesn't exist in STATIC_ROOT, try the static directory
    if not os.path.exists(standalone_path):
        standalone_path = os.path.join(settings.BASE_DIR, 'static', 'standalone_map.html')

    # Read the HTML file
    try:
        with open(standalone_path, 'r') as file:
            html_content = file.read()

        # Return the HTML content directly
        return HttpResponse(html_content)
    except Exception as e:
        messages.error(request, f"Error loading standalone map: {str(e)}")
        return redirect('labor:dashboard')


@login_required
def generate_heat_map_data_advanced(request):
    """Generate heat map data for a specific date (advanced version)"""
    date_str = request.GET.get('date')

    if date_str:
        try:
            date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            date = timezone.now().date()
    else:
        date = timezone.now().date()

    # Generate heat map data
    heat_map = HeatMapData.generate_worker_density_heatmap(date)

    messages.success(request, f'Heat map data generated for {date}')
    return redirect('labor:heat_map')


# Weather Forecast Views
@login_required
def worker_weather_forecast(request, worker_id):
    """View weather forecast for worker's current location"""
    worker = get_object_or_404(Worker, pk=worker_id) # Get the worker object

    if not worker.current_latitude or not worker.current_longitude:
        messages.error(request, f"{worker.name} doesn't have a current location.")
        return redirect('labor:worker_detail', pk=worker_id)

    # Get weather forecast
    forecast = worker.get_weather_forecast(days=5)

    # Get current weather
    current_weather = worker.get_current_weather()

    # Get recent weather logs for the worker
    recent_weather_logs = WeatherLog.objects.filter(
        worker=worker
    ).order_by('-timestamp')[:10]

    context = {
        'worker': worker,
        'forecast': forecast,
        'current_weather': current_weather,
        'recent_weather_logs': recent_weather_logs,
    }

    return render(request, 'labor/worker_weather_forecast.html', context)


@login_required
def google_maps_test_view(request):
    """Simple view for testing Google Maps integration."""
    return render(request, 'labor/google_maps_test.html')

def google_maps_standalone_test_view(request):
    """Standalone view for testing Google Maps API key without Django dependencies."""
    return render(request, 'labor/google_maps_standalone_test.html')