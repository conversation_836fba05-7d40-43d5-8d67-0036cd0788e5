<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 500px;
            width: 100%;
            border: 2px solid #007bff;
            border-radius: 10px;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps API Test</h1>
        <div id="status" class="status info">🔄 Initializing Google Maps...</div>
        <div id="api-info">
            <p><strong>API Key:</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}</p>
            <p><strong>Full API Key:</strong> {{ google_maps_api_key }}</p>
        </div>
        <div id="map"></div>
        <div id="debug-info" style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <h3>Debug Information:</h3>
            <ul id="debug-list"></ul>
        </div>
    </div>

    <script>
        // Debug logging function
        function addDebugInfo(message) {
            const debugList = document.getElementById('debug-list');
            const li = document.createElement('li');
            li.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            debugList.appendChild(li);
            console.log(message);
        }

        // Update status function
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        // Global initMap function
        window.initMap = function() {
            addDebugInfo('🗺️ initMap function called');
            
            try {
                // Check if Google Maps is available
                if (typeof google === 'undefined') {
                    throw new Error('Google object is undefined');
                }
                
                if (!google.maps) {
                    throw new Error('Google Maps API not loaded');
                }
                
                addDebugInfo('✅ Google Maps API is available');
                
                // Create map
                const mapElement = document.getElementById('map');
                if (!mapElement) {
                    throw new Error('Map container not found');
                }
                
                const map = new google.maps.Map(mapElement, {
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai, India
                    zoom: 10,
                    mapTypeId: google.maps.MapTypeId.ROADMAP
                });
                
                addDebugInfo('✅ Map created successfully');
                
                // Add a marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai'
                });
                
                addDebugInfo('✅ Marker added successfully');
                updateStatus('✅ Google Maps loaded successfully!', 'success');
                
            } catch (error) {
                addDebugInfo(`❌ Error: ${error.message}`);
                updateStatus(`❌ Error: ${error.message}`, 'error');
                
                // Show error in map container
                const mapElement = document.getElementById('map');
                if (mapElement) {
                    mapElement.innerHTML = `
                        <div style="padding: 40px; text-align: center; color: #dc3545; background: #f8f9fa; border-radius: 10px; height: 100%; display: flex; flex-direction: column; justify-content: center;">
                            <h3>❌ Map Loading Failed</h3>
                            <p>${error.message}</p>
                            <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                                🔄 Retry
                            </button>
                        </div>
                    `;
                }
            }
        };

        // Global error handler for Google Maps API authentication failures
        window.gm_authFailure = function() {
            addDebugInfo('❌ Google Maps API authentication failed');
            updateStatus('❌ Google Maps API authentication failed', 'error');
        };

        // Check if Google Maps script loads
        addDebugInfo('🔄 Starting Google Maps test...');
        addDebugInfo(`🔑 API Key: {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}`);
        
        // Fallback initialization
        document.addEventListener('DOMContentLoaded', function() {
            addDebugInfo('📄 DOM loaded');
            
            // Wait for Google Maps to load
            let attempts = 0;
            const maxAttempts = 10;
            
            const checkGoogleMaps = setInterval(function() {
                attempts++;
                addDebugInfo(`🔍 Checking Google Maps availability (attempt ${attempts}/${maxAttempts})`);
                
                if (typeof google !== 'undefined' && google.maps) {
                    addDebugInfo('✅ Google Maps detected, initializing...');
                    clearInterval(checkGoogleMaps);
                    if (!window.mapInitialized) {
                        window.initMap();
                    }
                } else if (attempts >= maxAttempts) {
                    addDebugInfo('❌ Google Maps failed to load after maximum attempts');
                    updateStatus('❌ Google Maps failed to load', 'error');
                    clearInterval(checkGoogleMaps);
                }
            }, 1000);
        });
    </script>

    <!-- Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onerror="addDebugInfo('❌ Failed to load Google Maps script'); updateStatus('❌ Failed to load Google Maps script', 'error');">
    </script>
</body>
</html>
