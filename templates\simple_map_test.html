<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Map Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 500px; width: 100%; border: 2px solid #ccc; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    </style>
</head>
<body>
    <h1>Simple Map Test for Cumulative Dashboard</h1>
    <div id="status"></div>
    <div id="map"></div>

    <script>
        const statusDiv = document.getElementById('status');
        
        function updateStatus(message, type = 'info') {
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            console.log(message);
        }

        // Global variables to match cumulative dashboard
        let map;
        let infoWindow;
        const centerLat = 13.0827;
        const centerLng = 80.2707;
        const currentMapType = 'roadmap';

        // Sample data to match the structure
        const farmsData = [
            {
                id: 1,
                name: "Test Farm 1",
                latitude: 13.0827,
                longitude: 80.2707,
                description: "Test farm for debugging",
                type: 'farm'
            }
        ];

        const pondsData = [
            {
                id: 1,
                name: "Test Pond 1",
                latitude: 13.0900,
                longitude: 80.2800,
                status: 'active',
                type: 'pond'
            }
        ];

        // Error handler
        window.gm_authFailure = function() {
            updateStatus('❌ Google Maps authentication failed! Check API key.', 'error');
        };

        // Main initialization function (same name as cumulative dashboard)
        function initMap() {
            updateStatus('✅ initMap() called successfully', 'success');
            
            try {
                // Initialize map with same settings as cumulative dashboard
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 10,
                    center: { lat: centerLat, lng: centerLng },
                    mapTypeId: currentMapType,
                    styles: [
                        {
                            featureType: 'poi',
                            elementType: 'labels',
                            stylers: [{ visibility: 'off' }]
                        }
                    ]
                });

                updateStatus('✅ Map created successfully', 'success');

                infoWindow = new google.maps.InfoWindow();

                // Add test markers
                addTestMarkers();
                
                updateStatus('✅ All components loaded successfully!', 'success');

            } catch (error) {
                updateStatus(`❌ Error in initMap(): ${error.message}`, 'error');
                console.error('Map initialization error:', error);
            }
        }

        function addTestMarkers() {
            // Add farm marker
            const farmMarker = new google.maps.Marker({
                position: { lat: farmsData[0].latitude, lng: farmsData[0].longitude },
                map: map,
                title: farmsData[0].name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#2563eb" stroke="white" stroke-width="3"/>
                            <text x="20" y="26" text-anchor="middle" fill="white" font-size="14" font-family="Arial">F</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40)
                }
            });

            // Add pond marker
            const pondMarker = new google.maps.Marker({
                position: { lat: pondsData[0].latitude, lng: pondsData[0].longitude },
                map: map,
                title: pondsData[0].name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="15" cy="15" r="12" fill="#10b981" stroke="white" stroke-width="2"/>
                            <text x="15" y="19" text-anchor="middle" fill="white" font-size="10" font-family="Arial">P</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(30, 30)
                }
            });

            updateStatus('✅ Test markers added successfully', 'success');
        }

        // Load Google Maps with the same API key
        updateStatus('Loading Google Maps API...', 'info');
    </script>

    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
    </script>
</body>
</html>
