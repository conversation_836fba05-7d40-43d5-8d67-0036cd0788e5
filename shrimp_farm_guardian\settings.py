"""
Django settings for shrimp_farm_guardian project.
"""

import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Initialize Sentry for error tracking in production
if os.environ.get('SENTRY_DSN'):
    try:
        import sentry_sdk
        from sentry_sdk.integrations.django import DjangoIntegration

        sentry_sdk.init(
            dsn=os.environ.get('SENTRY_DSN'),
            integrations=[DjangoIntegration()],
            # Set traces_sample_rate to 1.0 to capture 100% of transactions for performance monitoring
            # We recommend adjusting this value in production
            traces_sample_rate=float(os.environ.get('SENTRY_TRACES_SAMPLE_RATE', 0.1)),
            # If you wish to associate users to errors (assuming you are using
            # django.contrib.auth) you may enable sending PII data
            send_default_pii=True,
            # By default the SDK will try to use the SENTRY_RELEASE
            # environment variable, or infer a git commit
            # SHA as release, however you may want to set
            # something more human-readable.
            release=os.environ.get('SENTRY_RELEASE', 'shrimp-farm-guardian@1.0.0'),
            environment=os.environ.get('SENTRY_ENVIRONMENT', 'development'),
        )
    except ImportError:
        # Sentry SDK not installed, continue without error tracking
        pass

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Directory for AI models
MODELS_DIR = os.path.join(BASE_DIR, 'models')

# Application version
APP_VERSION = os.environ.get('SENTRY_RELEASE', '1.0.0')

# Use environment variable for SECRET_KEY, with a secure fallback for development
SECRET_KEY = os.environ.get('DJANGO_SECRET_KEY', 'n3=66hz*h^4619@q@2xx2dr6s1q7zd=0-ks38ai2+mmf8k87*^')

# SECURITY WARNING: don't run with debug turned on in production!
# Set DEBUG based on environment variable, default to False for safety
DEBUG = os.environ.get('DJANGO_DEBUG', 'False').lower() == 'true'

# Allow hosts from environment variable (comma-separated) or default to localhost
ALLOWED_HOSTS = os.environ.get('DJANGO_ALLOWED_HOSTS', '127.0.0.1,localhost,testserver').split(',')

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.sites',

    # Third-party apps
    'rest_framework',
    'rest_framework.authtoken',
    'django_filters',
    'drf_yasg',  # API documentation
    'corsheaders',
    'crispy_forms',
    'crispy_bootstrap5',
    'webpack_loader',
    'channels',
    'sslserver',

    # Project apps
    'core.apps.CoreConfig',
    'users.apps.UsersConfig',
    'ponds.apps.PondsConfig',
    'water_quality.apps.WaterQualityConfig',
    'feed.apps.FeedConfig',
    'medicine.apps.MedicineConfig',
    'lunar.apps.LunarConfig',  # Re-enable lunar app
    'weather.apps.WeatherConfig',
    'alerts.apps.AlertsConfig',  # Unified Alert Management System (includes AI alerts functionality)
    # 'ai_alerts.apps.AiAlertsConfig',  # AI Alerts module - MERGED into alerts app
    'harvest.apps.HarvestConfig',
    'electricity.apps.ElectricityConfig',  # Electricity monitoring module (new standalone app)
    'api.apps.ApiConfig',  # API module (consolidated API functionality)
    # 'api_advanced.apps.ApiAdvancedConfig',  # Advanced RESTful API System - CONSOLIDATED into api app
    # 'api_platform.apps.ApiPlatformConfig',  # API Platform - CONSOLIDATED into api app
    'disease.apps.DiseaseConfig',
    'labor.apps.LaborConfig',  # Labor Management - migration dependencies fixed
    # 'labor_management',  # Removed for rebuild
    'reports',
    'staticfiles_app',
    'chart_test',
    'digital_twin_api',
    'enhancements.apps.EnhancementsConfig',  # Enhanced features module
    'audit.apps.AuditConfig',  # Audit logging module
    'settings.apps.SettingsConfig',  # Settings module
    'ai_ml.apps.AiMlConfig',  # AI/ML module for predictive analytics (consolidated AI/ML & AI alerts functionality)
    # 'ai_ml_advanced.apps.AiMlAdvancedConfig',  # Advanced AI/ML Integration - CONSOLIDATED into ai_ml app
    # 'ai_optimization.apps.AiOptimizationConfig',  # AI Optimization & Quantum Computing - CONSOLIDATED into ai_ml app
    'technical_debt.apps.TechnicalDebtConfig',  # Technical debt monitoring
    'iot_integration.apps.IotIntegrationConfig',  # IoT device management (consolidated IoT functionality)
    # 'iot_advanced.apps.IotAdvancedConfig',  # Advanced IoT Device Management - CONSOLIDATED into iot_integration app
    # 'mobile_iot_expansion.apps.MobileIotExpansionConfig',  # Mobile & IoT Expansion - CONSOLIDATED into iot_integration app
    # 'advanced_automation.apps.AdvancedAutomationConfig',  # Advanced Automation - CONSOLIDATED into iot_integration app
    'mobile_app.apps.MobileAppConfig',  # Mobile application backend (consolidated mobile functionality)
    # 'mobile_ecosystem.apps.MobileEcosystemConfig',  # Mobile Ecosystem - CONSOLIDATED into mobile_app
    'geospatial.apps.GeospatialConfig',  # Geospatial features
    # 'security_enhancements.apps.SecurityEnhancementsConfig',  # Security enhancements - replaced by comprehensive security module
    'devops_infrastructure.apps.DevopsInfrastructureConfig',  # DevOps infrastructure
    # 'enhancements.modules.electricity.apps.ElectricityConfig',  # Electricity management - REPLACED by standalone electricity app
    'edge_computing.apps.EdgeComputingConfig',  # Edge Computing & Distributed AI
    # 'ai_optimization.apps.AiOptimizationConfig',  # AI Optimization & Quantum Computing - REMOVED (advanced tech)

    # PHASE 10: ADVANCED SHRIMP FARMING ECOSYSTEM (Consolidated features)
    # 'advanced_water_quality.apps.AdvancedWaterQualityConfig',  # Advanced Water Quality AI - CONSOLIDATED into water_quality app  
    # 'shrimp_behavior_analytics.apps.ShrimpBehaviorAnalyticsConfig',  # Shrimp Behavior Analytics - CONSOLIDATED into analytics app
    # 'blockchain_supply_chain.apps.BlockchainSupplyChainConfig',  # Blockchain & Supply Chain - CONSOLIDATED into financial app
    # 'arvr_digital_twin.apps.ArvrDigitalTwinConfig',  # AR/VR & Digital Twin - CONSOLIDATED into digital_twin_api app
    # 'advanced_automation.apps.AdvancedAutomationConfig',  # Advanced Automation - CONSOLIDATED into iot_integration app
    'research_development.apps.ResearchDevelopmentConfig',  # Research & Development Platform
    # 'multi_farm_enterprise.apps.MultiFarmEnterpriseConfig',  # Multi-Farm Enterprise - CONSOLIDATED into enterprise app
    # 'business_intelligence.apps.BusinessIntelligenceConfig',  # Business Intelligence & Analytics - CONSOLIDATED into enterprise app

    # PHASE 12: PRODUCTION-READY GLOBAL PLATFORM
    # 'production_deployment.apps.ProductionDeploymentConfig',  # Production Deployment - functionality moved to DevOps
    # 'global_expansion.apps.GlobalExpansionConfig',  # Global Expansion - functionality moved to enterprise app

    # COMPREHENSIVE IMPLEMENTATION - ALL FEATURES
    'analytics.apps.AnalyticsConfig',  # Advanced Analytics Dashboard (consolidated analytics & reporting functionality)
    # 'reporting.apps.ReportingConfig',  # Comprehensive Reporting System - CONSOLIDATED into analytics app
    'security.apps.SecurityConfig',  # Enhanced Security & User Management (consolidated security & alerts functionality)
    # 'security_advanced.apps.SecurityAdvancedConfig',  # Advanced Security & Compliance Framework - CONSOLIDATED into security app
    'notifications.apps.NotificationsConfig',  # Smart Notification System
    'financial.apps.FinancialConfig',  # Financial Management Module
    'enterprise.apps.EnterpriseConfig',  # Enterprise Multi-Tenant Module
    # 'realtime_advanced.apps.RealtimeAdvancedConfig',  # Advanced Real-time Data Streaming - CONSOLIDATED into core functionality
    # 'ui_advanced.apps.UiAdvancedConfig',  # Advanced UI/UX Enhancement System - CONSOLIDATED into enhancements app
    # 'performance_advanced.apps.PerformanceAdvancedConfig',  # Advanced Performance Optimization & Monitoring - CONSOLIDATED into technical_debt app
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'shrimp_farm_guardian.middleware.SecurityHeadersMiddleware',  # Enhanced security headers
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'shrimp_farm_guardian.middleware.RequestLoggingMiddleware',  # Enhanced logging
    'core.cache_monitoring.CacheMetricsMiddleware',  # Cache monitoring
]

ROOT_URLCONF = 'shrimp_farm_guardian.urls'

APPEND_SLASH = True

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'core.context_processors.map_settings',
            ],
        },
    },
]

WSGI_APPLICATION = 'shrimp_farm_guardian.wsgi.application'
ASGI_APPLICATION = 'shrimp_farm_guardian.asgi.application'

# Channel layers for WebSocket
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer',
    },
}

# Database
# Use environment variables to configure the database
# Default to SQLite for development, but use PostgreSQL in production
if os.environ.get('DATABASE_URL'):
    # Use dj-database-url for database URL configuration (install with pip install dj-database-url)
    import dj_database_url
    DATABASES = {
        'default': dj_database_url.config(default=os.environ.get('DATABASE_URL'))
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': os.environ.get('DB_ENGINE', 'django.db.backends.sqlite3'),
            'NAME': os.environ.get('DB_NAME', str(BASE_DIR / 'db.sqlite3')),
            'USER': os.environ.get('DB_USER', ''),
            'PASSWORD': os.environ.get('DB_PASSWORD', ''),
            'HOST': os.environ.get('DB_HOST', ''),
            'PORT': os.environ.get('DB_PORT', ''),
        }
    }

# Database connection pooling for improved performance (when using PostgreSQL)
if DATABASES['default']['ENGINE'] == 'django.db.backends.postgresql':
    DATABASES['default']['CONN_MAX_AGE'] = int(os.environ.get('DB_CONN_MAX_AGE', 600))

# Cache configuration
# Use Redis in production, local memory cache in development
if os.environ.get('REDIS_URL'):
    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': os.environ.get('REDIS_URL'),
            'OPTIONS': {
                'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                'PARSER_CLASS': 'redis.connection.HiredisParser',
                'CONNECTION_POOL_CLASS': 'redis.BlockingConnectionPool',
                'CONNECTION_POOL_CLASS_KWARGS': {
                    'max_connections': 50,
                    'timeout': 20,
                }
            }
        }
    }
    # Use Redis for session cache if available
    SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
    SESSION_CACHE_ALIAS = 'default'
else:
    CACHES = {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'shrimp-farm-guardian-cache',
        }
    }

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = '/static/'
STATICFILES_DIRS = [os.path.join(BASE_DIR, 'static')]
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

# Media files
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# AWS S3 configuration for static and media files in production
if os.environ.get('USE_S3', 'False').lower() == 'true':
    # AWS settings
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_STORAGE_BUCKET_NAME = os.environ.get('AWS_STORAGE_BUCKET_NAME')
    AWS_DEFAULT_ACL = 'public-read'
    AWS_S3_CUSTOM_DOMAIN = f'{AWS_STORAGE_BUCKET_NAME}.s3.amazonaws.com'
    AWS_S3_OBJECT_PARAMETERS = {'CacheControl': 'max-age=86400'}

    # S3 static settings
    STATIC_LOCATION = 'static'
    STATIC_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{STATIC_LOCATION}/'
    STATICFILES_STORAGE = 'shrimp_farm_guardian.storage_backends.StaticStorage'

    # S3 media settings
    MEDIA_LOCATION = 'media'
    MEDIA_URL = f'https://{AWS_S3_CUSTOM_DOMAIN}/{MEDIA_LOCATION}/'
    DEFAULT_FILE_STORAGE = 'shrimp_farm_guardian.storage_backends.MediaStorage'
else:
    # Use whitenoise for static files in production without S3
    if not DEBUG:
        MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
        STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Custom user model
AUTH_USER_MODEL = 'users.User'

# Authentication settings
LOGIN_URL = 'users:login'
LOGIN_REDIRECT_URL = '/'
LOGOUT_REDIRECT_URL = '/'

# Crispy forms
CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"
CRISPY_TEMPLATE_PACK = "bootstrap5"

# REST Framework
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.TokenAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
        'api_advanced.authentication.APIRateLimitThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': os.environ.get('API_THROTTLE_ANON', '100/hour'),
        'user': os.environ.get('API_THROTTLE_USER', '1000/hour'),
        'api_advanced': '1000/hour',
        'burst': '60/min',
        'sustained': '1000/day',
        'critical': os.environ.get('API_THROTTLE_CRITICAL', '5/minute'),
        'auth': '10/minute',
        'upload': '20/hour',
        'iot_data': '10000/hour',  # High rate for IoT data ingestion
    },
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': int(os.environ.get('API_PAGE_SIZE', 20)),
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer' if DEBUG else 'rest_framework.renderers.JSONRenderer',
    ],
    'EXCEPTION_HANDLER': 'api.utils.custom_exception_handler',
    # API Versioning
    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',
    'DEFAULT_VERSION': 'v1',
    'ALLOWED_VERSIONS': ['v1', 'v2'],
    'VERSION_PARAM': 'version',
    # Security Headers
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
}

# CORS settings
CORS_ALLOW_ALL_ORIGINS = DEBUG  # Only in development

# Map settings
MAP_PROVIDER = os.environ.get('MAP_PROVIDER', 'google')  # Options: 'mapbox', 'google'
# API keys should be set in environment variables with no default values for security
# For production, get your own Google Maps API key from Google Cloud Console
GOOGLE_MAPS_API_KEY = os.environ.get('GOOGLE_MAPS_API_KEY', '')  # Remove hardcoded key
MAPBOX_ACCESS_TOKEN = os.environ.get('MAPBOX_ACCESS_TOKEN', '')

# Weather API settings
OPENWEATHER_API_KEY = os.environ.get('OPENWEATHER_API_KEY', '')

# Django Sites Framework
SITE_ID = 1

# Webpack Loader
WEBPACK_LOADER = {
    'DEFAULT': {
        'CACHE': not DEBUG,
        'BUNDLE_DIR_NAME': 'webpack_bundles/',
        'STATS_FILE': os.path.join(BASE_DIR, 'webpack-stats.json'),
        'POLL_INTERVAL': 0.1,
        'TIMEOUT': None,
        'IGNORE': [r'.+\.hot-update.js', r'.+\.map'],
    }
}

# Logging
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# Web Push Notifications Settings
WEBPUSH_PRIVATE_KEY = os.environ.get('WEBPUSH_PRIVATE_KEY', '')
WEBPUSH_PUBLIC_KEY = os.environ.get('WEBPUSH_PUBLIC_KEY', '')
WEBPUSH_EMAIL = os.environ.get('WEBPUSH_EMAIL', '<EMAIL>')

# Firebase Configuration
FIREBASE_CONFIG = {
    'credentials_path': os.environ.get('FIREBASE_CREDENTIALS_PATH', ''),
    'project_id': os.environ.get('FIREBASE_PROJECT_ID', ''),
    'api_key': os.environ.get('FIREBASE_API_KEY', ''),
    'auth_domain': os.environ.get('FIREBASE_AUTH_DOMAIN', ''),
    'database_url': os.environ.get('FIREBASE_DATABASE_URL', ''),
    'storage_bucket': os.environ.get('FIREBASE_STORAGE_BUCKET', ''),
    'messaging_sender_id': os.environ.get('FIREBASE_MESSAGING_SENDER_ID', ''),
    'app_id': os.environ.get('FIREBASE_APP_ID', ''),
    'measurement_id': os.environ.get('FIREBASE_MEASUREMENT_ID', ''),
}

# Firebase Cloud Messaging (FCM) Settings
FCM_SERVER_KEY = os.environ.get('FCM_SERVER_KEY', '')
FCM_API_KEY = os.environ.get('FCM_API_KEY', '')

# Apple Push Notification Service (APNs) Settings
APNS_KEY_ID = os.environ.get('APNS_KEY_ID', '')
APNS_TEAM_ID = os.environ.get('APNS_TEAM_ID', '')
APNS_BUNDLE_ID = os.environ.get('APNS_BUNDLE_ID', 'com.shrimpfarmguardian.app')
APNS_KEY_PATH = os.environ.get('APNS_KEY_PATH', '')

# Email Configuration
EMAIL_BACKEND = os.environ.get('EMAIL_BACKEND', 'django.core.mail.backends.console.EmailBackend')
EMAIL_HOST = os.environ.get('EMAIL_HOST', '')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', 587))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Security Settings - only apply in production
if not DEBUG:
    # HTTPS settings
    SECURE_SSL_REDIRECT = os.environ.get('SECURE_SSL_REDIRECT', 'True').lower() == 'true'
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'True').lower() == 'true'
    CSRF_COOKIE_SECURE = os.environ.get('CSRF_COOKIE_SECURE', 'True').lower() == 'true'

    # HSTS settings
    SECURE_HSTS_SECONDS = int(os.environ.get('SECURE_HSTS_SECONDS', 31536000))  # 1 year
    SECURE_HSTS_INCLUDE_SUBDOMAINS = os.environ.get('SECURE_HSTS_INCLUDE_SUBDOMAINS', 'True').lower() == 'true'
    SECURE_HSTS_PRELOAD = os.environ.get('SECURE_HSTS_PRELOAD', 'True').lower() == 'true'

    # Content security policy
    SECURE_CONTENT_TYPE_NOSNIFF = True
    SECURE_BROWSER_XSS_FILTER = True
    X_FRAME_OPTIONS = 'DENY'

# Celery Configuration
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = True

# Weather monitoring settings
WEATHER_MONITORING_INTERVAL = int(os.environ.get('WEATHER_MONITORING_INTERVAL', 300))  # 5 minutes
WEATHER_DATA_RETENTION_DAYS = int(os.environ.get('WEATHER_DATA_RETENTION_DAYS', 30))

# SMS Configuration
SMS_API_KEY = os.environ.get('SMS_API_KEY', '')
SMS_PROVIDER = os.environ.get('SMS_PROVIDER', 'twilio')
