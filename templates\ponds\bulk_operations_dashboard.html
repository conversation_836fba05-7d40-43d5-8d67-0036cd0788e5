{% extends "base.html" %}
{% load static %}

{% block title %}Bulk Operations - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .bulk-container {
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        min-height: 100vh;
    }

    .bulk-header {
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .bulk-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 8s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .bulk-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .bulk-stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .bulk-stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }

    .bulk-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #fd7e14, #e55353);
    }

    .bulk-stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .bulk-stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .bulk-stat-label {
        color: #636e72;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
    }

    .bulk-operations {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 30px;
    }

    .bulk-operation-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
    }

    .bulk-operation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .operation-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .operation-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .operation-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: #2d3436;
        margin-bottom: 5px;
    }

    .operation-description {
        color: #636e72;
        font-size: 0.95rem;
    }

    .operation-actions {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .operation-btn {
        flex: 1;
        padding: 12px 20px;
        border: 2px solid #fd7e14;
        border-radius: 10px;
        background: white;
        color: #fd7e14;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        font-size: 0.9rem;
    }

    .operation-btn:hover {
        background: #fd7e14;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .operation-btn.primary {
        background: linear-gradient(135deg, #fd7e14 0%, #e55353 100%);
        color: white;
        border-color: transparent;
    }

    .operation-btn.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(253, 126, 20, 0.4);
    }

    .selection-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        margin-bottom: 30px;
        display: none;
    }

    .selection-panel.show {
        display: block;
    }

    .selection-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f3f4;
    }

    .selection-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2d3436;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .selection-controls {
        display: flex;
        gap: 10px;
    }

    .selection-btn {
        padding: 8px 16px;
        border: 2px solid #fd7e14;
        border-radius: 8px;
        background: white;
        color: #fd7e14;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .selection-btn:hover {
        background: #fd7e14;
        color: white;
    }

    .items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }

    .item-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        border: 2px solid #e9ecef;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
    }

    .item-card:hover {
        border-color: #fd7e14;
        box-shadow: 0 4px 15px rgba(253, 126, 20, 0.2);
    }

    .item-card.selected {
        border-color: #fd7e14;
        background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
    }

    .item-checkbox {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
        accent-color: #fd7e14;
    }

    .item-name {
        font-weight: 600;
        color: #2d3436;
        margin-bottom: 5px;
        padding-right: 30px;
    }

    .item-details {
        font-size: 0.85rem;
        color: #636e72;
    }

    .bulk-form {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        margin-bottom: 30px;
        display: none;
    }

    .bulk-form.show {
        display: block;
    }

    .form-section {
        margin-bottom: 25px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
        display: block;
        font-size: 0.95rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        border-color: #fd7e14;
        box-shadow: 0 0 0 0.2rem rgba(253, 126, 20, 0.25);
        outline: none;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
        margin: 20px 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #fd7e14, #e55353);
        width: 0%;
        transition: width 0.3s ease;
    }

    .status-message {
        padding: 15px 20px;
        border-radius: 10px;
        margin: 20px 0;
        display: none;
    }

    .status-message.success {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border: 1px solid #28a745;
        color: #155724;
    }

    .status-message.error {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border: 1px solid #dc3545;
        color: #721c24;
    }

    .status-message.show {
        display: block;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .bulk-container {
            padding: 15px;
        }

        .bulk-operations {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .bulk-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .items-grid {
            grid-template-columns: 1fr;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .operation-actions {
            flex-direction: column;
        }

        .selection-controls {
            flex-direction: column;
        }
    }

    @media (max-width: 480px) {
        .bulk-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="bulk-container">
    <!-- Header -->
    <div class="bulk-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">⚡ Bulk Operations</h1>
                <p class="mb-0" style="opacity: 0.9;">Efficiently manage multiple farms and ponds at once</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-2"></i> Refresh
                </button>
                <button class="btn btn-outline-light" onclick="showHelp()">
                    <i class="fas fa-question-circle me-2"></i> Help
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="bulk-stats">
        <div class="bulk-stat-card">
            <div class="bulk-stat-icon">🏢</div>
            <div class="bulk-stat-number">{{ total_farms }}</div>
            <div class="bulk-stat-label">Total Farms</div>
        </div>
        <div class="bulk-stat-card">
            <div class="bulk-stat-icon">🦐</div>
            <div class="bulk-stat-number">{{ total_ponds }}</div>
            <div class="bulk-stat-label">Total Ponds</div>
        </div>
        <div class="bulk-stat-card">
            <div class="bulk-stat-icon">✅</div>
            <div class="bulk-stat-number" id="selected-count">0</div>
            <div class="bulk-stat-label">Selected Items</div>
        </div>
        <div class="bulk-stat-card">
            <div class="bulk-stat-icon">📊</div>
            <div class="bulk-stat-number" id="operation-count">0</div>
            <div class="bulk-stat-label">Operations Today</div>
        </div>
    </div>

    <!-- Bulk Operations -->
    <div class="bulk-operations">
        <!-- Bulk Update Ponds -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div>
                    <div class="operation-title">Bulk Update Ponds</div>
                    <div class="operation-description">Update multiple ponds simultaneously</div>
                </div>
            </div>
            <div class="operation-actions">
                <button class="operation-btn primary" onclick="startBulkUpdate('ponds')">
                    <i class="fas fa-edit me-1"></i> Start Update
                </button>
                <button class="operation-btn" onclick="showPreview('update_ponds')">
                    <i class="fas fa-eye me-1"></i> Preview
                </button>
            </div>
        </div>

        <!-- Bulk Delete -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-trash"></i>
                </div>
                <div>
                    <div class="operation-title">Bulk Delete</div>
                    <div class="operation-description">Delete multiple items with confirmation</div>
                </div>
            </div>
            <div class="operation-actions">
                <button class="operation-btn primary" onclick="startBulkDelete()">
                    <i class="fas fa-trash me-1"></i> Start Delete
                </button>
                <button class="operation-btn" onclick="showPreview('delete')">
                    <i class="fas fa-eye me-1"></i> Preview
                </button>
            </div>
        </div>

        <!-- Export Data -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-download"></i>
                </div>
                <div>
                    <div class="operation-title">Export Data</div>
                    <div class="operation-description">Export farms and ponds to CSV/Excel</div>
                </div>
            </div>
            <div class="operation-actions">
                <a href="{% url 'ponds:export_ponds_csv' %}" class="operation-btn primary">
                    <i class="fas fa-file-csv me-1"></i> Export CSV
                </a>
                <a href="{% url 'ponds:export_complete_data' %}" class="operation-btn">
                    <i class="fas fa-file-archive me-1"></i> Export All
                </a>
            </div>
        </div>

        <!-- Import Data -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-upload"></i>
                </div>
                <div>
                    <div class="operation-title">Import Data</div>
                    <div class="operation-description">Import ponds from CSV files</div>
                </div>
            </div>
            <div class="operation-actions">
                <button class="operation-btn primary" onclick="startImport()">
                    <i class="fas fa-upload me-1"></i> Import CSV
                </button>
                <button class="operation-btn" onclick="downloadTemplate()">
                    <i class="fas fa-download me-1"></i> Template
                </button>
            </div>
        </div>

        <!-- Bulk Assign Workers -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div>
                    <div class="operation-title">Assign Workers</div>
                    <div class="operation-description">Assign workers to multiple ponds</div>
                </div>
            </div>
            <div class="operation-actions">
                <button class="operation-btn primary" onclick="startWorkerAssignment()">
                    <i class="fas fa-user-plus me-1"></i> Assign
                </button>
                <button class="operation-btn" onclick="showWorkerPreview()">
                    <i class="fas fa-eye me-1"></i> Preview
                </button>
            </div>
        </div>

        <!-- Generate Reports -->
        <div class="bulk-operation-card">
            <div class="operation-header">
                <div class="operation-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div>
                    <div class="operation-title">Generate Reports</div>
                    <div class="operation-description">Create comprehensive reports</div>
                </div>
            </div>
            <div class="operation-actions">
                <button class="operation-btn primary" onclick="generateReport()">
                    <i class="fas fa-file-pdf me-1"></i> PDF Report
                </button>
                <button class="operation-btn" onclick="generateAnalytics()">
                    <i class="fas fa-chart-line me-1"></i> Analytics
                </button>
            </div>
        </div>
    </div>

    <!-- Selection Panel -->
    <div class="selection-panel" id="selection-panel">
        <div class="selection-header">
            <h3 class="selection-title">
                <i class="fas fa-check-square"></i>
                <span id="selection-title-text">Select Items</span>
            </h3>
            <div class="selection-controls">
                <button class="selection-btn" onclick="selectAll()">Select All</button>
                <button class="selection-btn" onclick="selectNone()">Select None</button>
                <button class="selection-btn" onclick="closeSelection()">Close</button>
            </div>
        </div>
        <div class="items-grid" id="items-grid">
            <!-- Items will be populated here -->
        </div>
    </div>

    <!-- Bulk Form -->
    <div class="bulk-form" id="bulk-form">
        <div class="selection-header">
            <h3 class="selection-title">
                <i class="fas fa-edit"></i>
                <span id="form-title-text">Bulk Update</span>
            </h3>
            <div class="selection-controls">
                <button class="selection-btn" onclick="executeBulkOperation()">Execute</button>
                <button class="selection-btn" onclick="closeBulkForm()">Cancel</button>
            </div>
        </div>
        
        <div id="bulk-form-content">
            <!-- Form content will be populated here -->
        </div>
        
        <div class="progress-bar" id="progress-bar" style="display: none;">
            <div class="progress-fill" id="progress-fill"></div>
        </div>
        
        <div class="status-message" id="status-message">
            <!-- Status messages will appear here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let selectedItems = [];
    let currentOperation = null;

    // Initialize bulk operations
    document.addEventListener('DOMContentLoaded', function() {
        updateSelectedCount();
        loadOperationCount();
    });

    function startBulkUpdate(type) {
        currentOperation = 'update';
        showSelectionPanel(type, 'Select items to update');
    }

    function startBulkDelete() {
        currentOperation = 'delete';
        showSelectionPanel('ponds', 'Select items to delete');
    }

    function showSelectionPanel(type, title) {
        const panel = document.getElementById('selection-panel');
        const titleElement = document.getElementById('selection-title-text');

        titleElement.textContent = title;
        panel.classList.add('show');

        // Mock data for demonstration
        const mockPonds = [
            {id: 1, name: 'Pond Alpha-1', farm_name: 'Green Valley Farm', size: '2500'},
            {id: 2, name: 'Pond Beta-2', farm_name: 'Blue Ocean Farm', size: '3000'},
            {id: 3, name: 'Pond Gamma-3', farm_name: 'Green Valley Farm', size: '2000'}
        ];

        populateItemsGrid(mockPonds, type);
    }

    function populateItemsGrid(items, type) {
        const grid = document.getElementById('items-grid');

        const itemsHTML = items.map(item => `
            <div class="item-card" onclick="toggleItemSelection(${item.id}, '${type}')">
                <input type="checkbox" class="item-checkbox" id="item-${item.id}"
                       onchange="toggleItemSelection(${item.id}, '${type}')">
                <div class="item-name">${item.name}</div>
                <div class="item-details">
                    Farm: ${item.farm_name || 'Not assigned'}<br>
                    Size: ${item.size || 'Not specified'} m²
                </div>
            </div>
        `).join('');

        grid.innerHTML = itemsHTML;
    }

    function toggleItemSelection(id, type) {
        const checkbox = document.getElementById(`item-${id}`);
        const card = checkbox.closest('.item-card');

        if (checkbox.checked) {
            selectedItems.push({id, type});
            card.classList.add('selected');
        } else {
            selectedItems = selectedItems.filter(item => !(item.id === id && item.type === type));
            card.classList.remove('selected');
        }

        updateSelectedCount();
    }

    function selectAll() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                checkbox.checked = true;
                checkbox.onchange();
            }
        });
    }

    function selectNone() {
        const checkboxes = document.querySelectorAll('.item-checkbox');
        checkboxes.forEach(checkbox => {
            if (checkbox.checked) {
                checkbox.checked = false;
                checkbox.onchange();
            }
        });
    }

    function updateSelectedCount() {
        document.getElementById('selected-count').textContent = selectedItems.length;
    }

    function closeSelection() {
        document.getElementById('selection-panel').classList.remove('show');
        selectedItems = [];
        updateSelectedCount();
    }

    function executeBulkOperation() {
        if (selectedItems.length === 0) {
            showStatusMessage('Please select at least one item', 'error');
            return;
        }

        if (currentOperation === 'update') {
            showBulkUpdateForm();
        } else if (currentOperation === 'delete') {
            confirmBulkDelete();
        }
    }

    function showBulkUpdateForm() {
        closeSelection();

        const form = document.getElementById('bulk-form');
        const titleElement = document.getElementById('form-title-text');
        const content = document.getElementById('bulk-form-content');

        titleElement.textContent = `Update ${selectedItems.length} Items`;

        content.innerHTML = `
            <div class="form-row">
                <div class="form-section">
                    <label class="form-label">Species</label>
                    <select class="form-control" id="bulk-species">
                        <option value="">Keep current</option>
                        <option value="Litopenaeus vannamei">Litopenaeus vannamei</option>
                        <option value="Penaeus monodon">Penaeus monodon</option>
                    </select>
                </div>
                <div class="form-section">
                    <label class="form-label">Water Quality</label>
                    <select class="form-control" id="bulk-water-quality">
                        <option value="">Keep current</option>
                        <option value="Excellent">Excellent</option>
                        <option value="Good">Good</option>
                        <option value="Fair">Fair</option>
                        <option value="Poor">Poor</option>
                    </select>
                </div>
            </div>
            <div class="form-section">
                <label class="form-label">Additional Notes</label>
                <textarea class="form-control" id="bulk-notes" rows="3"
                          placeholder="Add notes to all selected items..."></textarea>
            </div>
        `;

        form.classList.add('show');
    }

    function confirmBulkDelete() {
        if (!confirm(`Are you sure you want to delete ${selectedItems.length} items? This action cannot be undone.`)) {
            return;
        }

        showProgress();

        setTimeout(() => {
            hideProgress();
            showStatusMessage(`Successfully deleted ${selectedItems.length} items`, 'success');
            setTimeout(() => {
                closeSelection();
                refreshData();
            }, 2000);
        }, 2000);
    }

    function closeBulkForm() {
        document.getElementById('bulk-form').classList.remove('show');
        selectedItems = [];
        updateSelectedCount();
    }

    function showProgress() {
        document.getElementById('progress-bar').style.display = 'block';
        animateProgress();
    }

    function hideProgress() {
        document.getElementById('progress-bar').style.display = 'none';
        document.getElementById('progress-fill').style.width = '0%';
    }

    function animateProgress() {
        const fill = document.getElementById('progress-fill');
        let width = 0;
        const interval = setInterval(() => {
            width += Math.random() * 10;
            if (width >= 90) {
                clearInterval(interval);
                width = 90;
            }
            fill.style.width = width + '%';
        }, 100);
    }

    function showStatusMessage(message, type) {
        const statusElement = document.getElementById('status-message');
        statusElement.textContent = message;
        statusElement.className = `status-message ${type} show`;

        setTimeout(() => {
            statusElement.classList.remove('show');
        }, 5000);
    }

    function refreshData() {
        location.reload();
    }

    function loadOperationCount() {
        document.getElementById('operation-count').textContent = Math.floor(Math.random() * 10);
    }

    function startImport() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv';
        input.onchange = handleFileImport;
        input.click();
    }

    function handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        showProgress();

        setTimeout(() => {
            hideProgress();
            showStatusMessage('Successfully imported 5 ponds from CSV', 'success');
            setTimeout(refreshData, 2000);
        }, 2000);
    }

    function downloadTemplate() {
        const csvContent = "Name,Farm,Size (m²),Species,Water Quality,Status,Latitude,Longitude,Notes\n" +
                          "Example Pond,Example Farm,2500,Litopenaeus vannamei,Good,active,13.0827,80.2707,Example notes";

        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'pond_import_template.csv';
        a.click();
        window.URL.revokeObjectURL(url);
    }

    function showHelp() {
        alert('Bulk Operations Help:\n\n' +
              '1. Select items using checkboxes\n' +
              '2. Choose an operation (Update, Delete, etc.)\n' +
              '3. Configure the operation settings\n' +
              '4. Execute the operation\n\n' +
              'Use Export/Import for data management');
    }

    function startWorkerAssignment() {
        alert('Worker Assignment - Coming Soon!');
    }

    function generateReport() {
        alert('Report Generation - Coming Soon!');
    }

    function generateAnalytics() {
        alert('Analytics Generation - Coming Soon!');
    }

    function showPreview(operation) {
        alert(`Preview for ${operation} - Coming Soon!`);
    }

    function showWorkerPreview() {
        alert('Worker Preview - Coming Soon!');
    }
</script>
{% endblock %}
