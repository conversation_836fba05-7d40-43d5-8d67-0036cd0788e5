{% extends 'base.html' %}
{% load static %}

{% block title %}Real-time Monitoring - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<link href="{% static 'css/unified-template.css' %}" rel="stylesheet">
<link href="{% static 'css/iot-realtime.css' %}" rel="stylesheet">
<style>
    .monitoring-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .monitoring-hero::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }
    
    .live-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 1rem;
    }
    
    .live-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse-live 1.5s infinite;
    }
    
    @keyframes pulse-live {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.2); }
    }
    
    .stats-overview {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: #e2e8f0;
    }
    
    .stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #e2e8f0;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }
    
    .stat-change {
        font-size: 0.8rem;
        font-weight: 600;
        margin-top: 0.25rem;
    }
    
    .stat-change.positive {
        color: #10b981;
    }
    
    .stat-change.negative {
        color: #ef4444;
    }
    
    .stat-change.neutral {
        color: #94a3b8;
    }
    
    /* Enhanced Responsive Design for Mobile & Web */
    @media (max-width: 1200px) {
        .monitoring-hero h1 {
            font-size: 2.2rem;
        }
        
        .electricity-stat-card {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 992px) {
        .monitoring-hero {
            padding: 2.5rem 1.5rem;
        }
        
        .stats-overview {
            padding: 1.25rem;
        }
        
        .stat-item {
            padding: 1rem 0.75rem;
        }
        
        .electricity-stat-card {
            margin-bottom: 20px;
        }
        
        .equipment-status-container {
            margin-top: 15px;
        }
    }

    @media (max-width: 768px) {
        .monitoring-hero {
            padding: 2rem 1rem;
        }
        
        .monitoring-hero h1 {
            font-size: 1.8rem;
            text-align: center;
        }
        
        .monitoring-hero p {
            font-size: 1rem;
            text-align: center;
        }
        
        .live-indicator {
            justify-content: center;
            margin-top: 1rem;
        }
        
        .monitoring-stats {
            text-align: center !important;
            margin-top: 1.5rem;
        }
        
        .stats-overview {
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .stat-item {
            padding: 0.75rem 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.5rem;
        }
        
        .stat-label {
            font-size: 0.8rem;
        }
        
        /* Mobile Electricity Monitoring */
        .electricity-stat-card {
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .stat-icon {
            font-size: 1.5rem;
        }
        
        .stat-value {
            font-size: 1.4rem;
        }
        
        .stat-change {
            font-size: 0.75rem;
        }
        
        /* Mobile Equipment Status */
        .equipment-status-container {
            padding: 15px;
            margin-top: 10px;
        }
        
        .equipment-item {
            margin-bottom: 0.75rem;
            font-size: 0.9rem;
        }
        
        /* Mobile Responsive Grid */
        .col-lg-3.col-md-6.col-12.mb-3 {
            margin-bottom: 1rem !important;
        }
        
        .col-lg-8.col-12 {
            margin-top: 1rem;
        }
    }

    @media (max-width: 576px) {
        .monitoring-hero {
            padding: 1.5rem 0.75rem;
        }
        
        .monitoring-hero h1 {
            font-size: 1.6rem;
        }
        
        .monitoring-hero p {
            font-size: 0.95rem;
        }
        
        .live-indicator span {
            font-size: 0.8rem;
        }
        
        .monitoring-stats {
            font-size: 0.85rem;
        }
        
        .stats-overview {
            padding: 0.75rem;
        }
        
        .stat-item {
            padding: 0.6rem 0.4rem;
        }
        
        .stat-value {
            font-size: 1.3rem;
        }
        
        .stat-label {
            font-size: 0.75rem;
        }
        
        /* Extra Small Mobile - Electricity Cards */
        .electricity-stat-card {
            padding: 12px;
            text-align: center;
        }
        
        .stat-icon {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .stat-label {
            font-size: 0.7rem;
            margin-bottom: 0.25rem;
        }
        
        .stat-change {
            font-size: 0.7rem;
        }
        
        /* Mobile Equipment Status */
        .equipment-status-container h6 {
            font-size: 1rem;
            margin-bottom: 0.75rem;
        }
        
        .equipment-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
        }
        
        .equipment-item:last-child {
            border-bottom: none;
        }
        
        .badge {
            align-self: flex-end;
        }
        
        /* Mobile 3-Phase Monitoring */
        .phase-monitoring-card {
            margin-bottom: 1rem;
        }
        
        .phase-card {
            margin-bottom: 0.75rem;
        }
        
        .phase-value {
            font-size: 1.5rem !important;
        }
        
        .power-monitoring-controls {
            width: 100%;
            margin-top: 0.75rem;
        }
        
        .power-monitoring-controls .btn {
            margin-bottom: 0.5rem;
            width: 100%;
        }
        
        .power-metrics-card,
        .power-quality-card {
            margin-bottom: 1rem;
        }
        
        .power-chart-mini {
            height: 60px !important;
        }
        
        /* Mobile Grid Adjustments */
        .row.mt-3 .col-md-3 {
            margin-bottom: 0.75rem;
        }
        
        /* Mobile Container */
        .container-fluid {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }
    }

    /* Touch-friendly interactions for mobile devices */
    @media (hover: none) and (pointer: coarse) {
        .electricity-stat-card:hover {
            transform: none;
            box-shadow: initial;
        }
        
        /* Larger touch targets */
        .btn {
            min-height: 44px;
            min-width: 44px;
            padding: 0.75rem 1rem;
        }
        
        .badge {
            min-height: 24px;
            padding: 0.4rem 0.75rem;
        }
    }

    /* Landscape mobile orientation */
    @media (max-width: 768px) and (orientation: landscape) {
        .monitoring-hero {
            padding: 1.5rem 1rem;
        }
        
        .monitoring-hero h1 {
            font-size: 1.6rem;
        }
        
        .stats-overview {
            padding: 0.75rem;
        }
        
        .stat-item {
            padding: 0.5rem;
        }
        
        .electricity-stat-card {
            padding: 12px;
        }
    }

    /* High DPI / Retina displays */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
        .live-dot {
            border-radius: 50%;
            -webkit-background-size: cover;
            background-size: cover;
        }
        
        .electricity-stat-card {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Monitoring Hero Section -->
    <div class="monitoring-hero">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-satellite-dish me-3"></i>
                    Real-time Monitoring Center
                </h1>
                <p class="mb-0 fs-5">
                    Live IoT sensor data, automated alerts, and intelligent monitoring
                </p>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE DATA STREAMING</span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="monitoring-stats">
                    <div class="mb-2">
                        <i class="fas fa-microchip me-2"></i>
                        <span id="activeSensors">8</span> Active Sensors
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-clock me-2"></i>
                        <span id="uptime">99.8%</span> Uptime
                    </div>
                    <div>
                        <i class="fas fa-wifi me-2"></i>
                        <span id="dataRate">2.4 MB/h</span> Data Rate
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
        <div class="row">
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <span class="stat-value" id="avgTemperature">29.5°C</span>
                    <span class="stat-label">Avg Temperature</span>
                    <div class="stat-change positive" id="tempChange">+0.3°C</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <span class="stat-value" id="avgOxygen">6.2 mg/L</span>
                    <span class="stat-label">Avg Dissolved O₂</span>
                    <div class="stat-change negative" id="oxygenChange">-0.1 mg/L</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <span class="stat-value" id="avgPH">8.1</span>
                    <span class="stat-label">Avg pH Level</span>
                    <div class="stat-change neutral" id="phChange">±0.0</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="stat-item">
                    <span class="stat-value" id="alertCount">2</span>
                    <span class="stat-label">Active Alerts</span>
                    <div class="stat-change positive" id="alertChange">-1 alert</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Comprehensive Electricity & 3-Phase Power Monitoring -->
    <div class="stats-overview mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-bolt me-2" style="color: #667eea;"></i>
                Electricity & 3-Phase Power Monitoring
                <span class="badge bg-success ms-2">
                    <div class="live-dot me-1"></div>
                    LIVE
                </span>
            </h5>
            <div class="power-monitoring-controls">
                <button class="btn btn-sm btn-outline-primary me-2" id="refreshPowerData">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <a href="{% url 'electricity:dashboard' %}" class="btn btn-sm btn-outline-success">
                    <i class="fas fa-external-link-alt me-1"></i> Full Dashboard
                </a>
            </div>
        </div>
        
        <!-- Power Overview Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="electricity-stat-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 12px; padding: 20px; text-align: center; color: white;">
                    <div class="stat-icon mb-2" style="font-size: 2rem;">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="stat-value" style="font-size: 1.8rem; font-weight: 700;" id="totalPowerUsage">12.5 kW</div>
                    <div class="stat-label" style="font-size: 0.9rem; opacity: 0.9;">Total Power Usage</div>
                    <div class="stat-change positive mt-2" style="font-size: 0.8rem;" id="powerChange">
                        <i class="fas fa-arrow-up"></i> +2.1 kW
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="electricity-stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 20px; text-align: center; color: white;">
                    <div class="stat-icon mb-2" style="font-size: 2rem;">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="stat-value" style="font-size: 1.8rem; font-weight: 700;" id="voltageReading">230V</div>
                    <div class="stat-label" style="font-size: 0.9rem; opacity: 0.9;">Avg Voltage</div>
                    <div class="stat-change neutral mt-2" style="font-size: 0.8rem;" id="voltageChange">
                        <i class="fas fa-minus"></i> Stable
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="electricity-stat-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 12px; padding: 20px; text-align: center; color: white;">
                    <div class="stat-icon mb-2" style="font-size: 2rem;">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="stat-value" style="font-size: 1.8rem; font-weight: 700;" id="powerFactor">0.95</div>
                    <div class="stat-label" style="font-size: 0.9rem; opacity: 0.9;">Power Factor</div>
                    <div class="stat-change positive mt-2" style="font-size: 0.8rem;" id="powerFactorChange">
                        <i class="fas fa-check"></i> Excellent
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 col-12 mb-3">
                <div class="electricity-stat-card" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 12px; padding: 20px; text-align: center; color: white;">
                    <div class="stat-icon mb-2" style="font-size: 2rem;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-value" style="font-size: 1.8rem; font-weight: 700;" id="dailyCost">$24.50</div>
                    <div class="stat-label" style="font-size: 0.9rem; opacity: 0.9;">Daily Cost</div>
                    <div class="stat-change negative mt-2" style="font-size: 0.8rem;" id="costChange">
                        <i class="fas fa-arrow-up"></i> +$2.10
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3-Phase Voltage Monitoring -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="phase-monitoring-card" style="background: rgba(30, 41, 59, 0.6); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(102, 126, 234, 0.3);">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        3-Phase Voltage Monitoring (V)
                    </h6>
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L1</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="voltage-l1-realtime">230.2</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Volts</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L2</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="voltage-l2-realtime">229.8</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Volts</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L3</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="voltage-l3-realtime">230.5</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Volts</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 3-Phase Current Monitoring -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="phase-monitoring-card" style="background: rgba(30, 41, 59, 0.6); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(102, 126, 234, 0.3);">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-bolt me-2"></i>
                        3-Phase Current Monitoring (A)
                    </h6>
                    <div class="row">
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L1</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="current-l1-realtime">18.4</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Amperes</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L2</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="current-l2-realtime">17.9</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Amperes</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-4 col-12 mb-3">
                            <div class="phase-card" style="background: linear-gradient(135deg, #ec4899 0%, #db2777 100%); border-radius: 12px; padding: 1.5rem; text-align: center; color: white;">
                                <div class="phase-label" style="font-size: 0.9rem; opacity: 0.9; margin-bottom: 0.5rem;">Phase L3</div>
                                <div class="phase-value" style="font-size: 2rem; font-weight: 700;" id="current-l3-realtime">18.7</div>
                                <div class="phase-unit" style="font-size: 0.8rem; opacity: 0.8;">Amperes</div>
                                <div class="phase-status" style="font-size: 0.75rem; margin-top: 0.5rem;">
                                    <i class="fas fa-check-circle"></i> Normal
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Power Consumption and Quality Metrics -->
        <div class="row">
            <div class="col-lg-6 col-12 mb-3">
                <div class="power-metrics-card" style="background: rgba(30, 41, 59, 0.6); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(102, 126, 234, 0.3);">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-chart-pie me-2"></i>
                        Power Consumption
                    </h6>
                    <div class="row">
                        <div class="col-6 text-center mb-2">
                            <div class="metric-value" style="font-size: 1.5rem; font-weight: 700; color: #667eea;" id="total-power-realtime">12.8</div>
                            <div class="metric-label" style="font-size: 0.8rem; color: #94a3b8;">Total kW</div>
                        </div>
                        <div class="col-6 text-center mb-2">
                            <div class="metric-value" style="font-size: 1.5rem; font-weight: 700; color: #10b981;" id="reactive-power-realtime">2.4</div>
                            <div class="metric-label" style="font-size: 0.8rem; color: #94a3b8;">Reactive kVAr</div>
                        </div>
                    </div>
                    <div class="power-chart-mini" style="height: 80px; background: rgba(15, 23, 42, 0.5); border-radius: 8px; margin-top: 1rem; display: flex; align-items: center; justify-content: center;">
                        <canvas id="mini-power-chart" width="200" height="60"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-12 mb-3">
                <div class="power-quality-card" style="background: rgba(30, 41, 59, 0.6); border-radius: 15px; padding: 1.5rem; border: 1px solid rgba(102, 126, 234, 0.3);">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-award me-2"></i>
                        Power Quality
                    </h6>
                    <div class="quality-metrics">
                        <div class="quality-item d-flex justify-content-between align-items-center mb-2">
                            <span style="color: #94a3b8;">Power Factor:</span>
                            <span class="badge bg-success" id="power-factor-realtime">0.95</span>
                        </div>
                        <div class="quality-item d-flex justify-content-between align-items-center mb-2">
                            <span style="color: #94a3b8;">THD Voltage:</span>
                            <span class="badge bg-success" id="thd-voltage-realtime">2.1%</span>
                        </div>
                        <div class="quality-item d-flex justify-content-between align-items-center mb-2">
                            <span style="color: #94a3b8;">THD Current:</span>
                            <span class="badge bg-warning" id="thd-current-realtime">4.8%</span>
                        </div>
                        <div class="quality-item d-flex justify-content-between align-items-center">
                            <span style="color: #94a3b8;">Frequency:</span>
                            <span class="badge bg-success" id="frequency-realtime">50.02 Hz</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Equipment Status and Power Alerts -->
        <div class="row mt-3">
            <div class="col-lg-8 col-12">
                <div class="equipment-status-container" style="background: rgba(30, 41, 59, 0.5); border-radius: 12px; padding: 20px;">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-cogs me-2"></i>
                        Equipment Status
                    </h6>
                    <div class="row">
                        <div class="col-md-3 col-6 mb-2">
                            <div class="equipment-item d-flex justify-content-between align-items-center">
                                <span style="color: #94a3b8;">Aerators:</span>
                                <span class="badge bg-success" id="aeratorStatus">8/8 Online</span>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <div class="equipment-item d-flex justify-content-between align-items-center">
                                <span style="color: #94a3b8;">Water Pumps:</span>
                                <span class="badge bg-success" id="pumpStatus">4/4 Online</span>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <div class="equipment-item d-flex justify-content-between align-items-center">
                                <span style="color: #94a3b8;">Blowers:</span>
                                <span class="badge bg-warning text-dark" id="blowerStatus">2/3 Online</span>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-2">
                            <div class="equipment-item d-flex justify-content-between align-items-center">
                                <span style="color: #94a3b8;">Feeders:</span>
                                <span class="badge bg-success" id="feederStatus">6/6 Online</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-12">
                <div class="power-alerts-container" style="background: rgba(30, 41, 59, 0.5); border-radius: 12px; padding: 20px;">
                    <h6 class="mb-3" style="color: #e2e8f0;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Power Alerts
                    </h6>
                    <div id="powerAlertsList">
                        <div class="alert-item mb-2" style="padding: 8px 12px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; border-left: 3px solid #ef4444;">
                            <small style="color: #fecaca;">High consumption detected in Pond 3</small>
                        </div>
                        <div class="alert-item mb-2" style="padding: 8px 12px; background: rgba(245, 158, 11, 0.2); border-radius: 8px; border-left: 3px solid #f59e0b;">
                            <small style="color: #fcd34d;">Power factor low in Circuit B</small>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{% url 'electricity:dashboard' %}" class="btn btn-sm btn-outline-warning">
                                <i class="fas fa-eye"></i> View All
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Dashboard Container -->
    <div id="realtime-dashboard-container">
        <!-- Real-time dashboard will be rendered here by JavaScript -->
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="quick-actions-container">
                <div class="stats-overview">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-primary w-100" id="exportDataBtn">
                                <i class="fas fa-download me-2"></i>
                                Export Data
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-success w-100" id="calibrateAllBtn">
                                <i class="fas fa-cogs me-2"></i>
                                Calibrate All
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-warning w-100" id="testAlertsBtn">
                                <i class="fas fa-bell me-2"></i>
                                Test Alerts
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-info w-100" id="systemReportBtn">
                                <i class="fas fa-chart-bar me-2"></i>
                                System Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js for real-time charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<!-- IoT Sensor Manager -->
<script src="{% static 'js/iot-sensor-manager.js' %}"></script>
<!-- Real-time Dashboard -->
<script src="{% static 'js/realtime-dashboard.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize monitoring dashboard
    initializeMonitoringDashboard();
    
    // Initialize electricity monitoring
    initializeElectricityMonitoring();
    
    // Setup quick actions
    setupQuickActions();
    
    // Start live data simulation
    startLiveDataSimulation();
});

// Monitoring Functions
function initializeMonitoringDashboard() {
    // Listen for IoT data updates to update overview stats
    window.addEventListener('iotDataUpdate', (event) => {
        updateOverviewStats(event.detail);
    });
    
    // Listen for system status updates
    window.addEventListener('iotSystemStatus', (event) => {
        updateSystemStats(event.detail);
    });
    
    // Listen for alerts
    window.addEventListener('iotAlert', (event) => {
        updateAlertStats(event.detail);
    });
}

function updateOverviewStats(data) {
    const { sensorId, reading } = data;
    
    // Update specific sensor averages (simplified for demo)
    switch (sensorId) {
        case 'temperature':
            document.getElementById('avgTemperature').textContent = reading.value.toFixed(1) + '°C';
            updateStatChange('tempChange', Math.random() * 0.6 - 0.3, '°C');
            break;
        case 'dissolved_oxygen':
            document.getElementById('avgOxygen').textContent = reading.value.toFixed(1) + ' mg/L';
            updateStatChange('oxygenChange', Math.random() * 0.4 - 0.2, ' mg/L');
            break;
        case 'ph_level':
            document.getElementById('avgPH').textContent = reading.value.toFixed(1);
            updateStatChange('phChange', Math.random() * 0.2 - 0.1, '');
            break;
    }
}

function updateSystemStats(status) {
    document.getElementById('activeSensors').textContent = status.online;
    
    // Update uptime (simulated)
    const uptime = (99.5 + Math.random() * 0.5).toFixed(1);
    document.getElementById('uptime').textContent = uptime + '%';
    
    // Update data rate (simulated)
    const dataRate = (2.0 + Math.random() * 1.0).toFixed(1);
    document.getElementById('dataRate').textContent = dataRate + ' MB/h';
}

function updateAlertStats(alert) {
    const alertCountElement = document.getElementById('alertCount');
    const currentCount = parseInt(alertCountElement.textContent);
    alertCountElement.textContent = currentCount + 1;
    
    updateStatChange('alertChange', 1, ' alert');
}

function updateStatChange(elementId, change, unit) {
    const element = document.getElementById(elementId);
    const changeText = change > 0 ? '+' + change.toFixed(1) : change.toFixed(1);
    element.textContent = changeText + unit;
    
    // Update color based on change
    element.className = 'stat-change ' + (change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral');
}

// Electricity Monitoring Functions
function initializeElectricityMonitoring() {
    // Start electricity data simulation
    startElectricityDataSimulation();
    
    // Listen for electricity data updates
    window.addEventListener('electricityDataUpdate', (event) => {
        updateElectricityStats(event.detail);
    });
}

function startElectricityDataSimulation() {
    // Simulate real-time electricity data updates
    setInterval(() => {
        const electricityData = {
            totalPower: (12.5 + (Math.random() - 0.5) * 2).toFixed(1),
            voltage: (230 + (Math.random() - 0.5) * 10).toFixed(0),
            powerFactor: (0.95 + (Math.random() - 0.5) * 0.1).toFixed(2),
            dailyCost: (24.50 + (Math.random() - 0.5) * 5).toFixed(2),
            powerChange: (Math.random() - 0.5) * 4,
            voltageStatus: Math.random() > 0.8 ? 'warning' : 'stable',
            powerFactorStatus: Math.random() > 0.9 ? 'excellent' : 'good'
        };
        
        // Dispatch electricity update event
        window.dispatchEvent(new CustomEvent('electricityDataUpdate', {
            detail: electricityData
        }));
    }, 3000); // Update every 3 seconds
}

function updateElectricityStats(data) {
    // Update power usage
    document.getElementById('totalPowerUsage').textContent = data.totalPower + ' kW';
    updateElectricityChange('powerChange', data.powerChange, ' kW');
    
    // Update voltage
    document.getElementById('voltageReading').textContent = data.voltage + 'V';
    updateElectricityStatus('voltageChange', data.voltageStatus);
    
    // Update power factor
    document.getElementById('powerFactor').textContent = data.powerFactor;
    updateElectricityStatus('powerFactorChange', data.powerFactorStatus);
    
    // Update daily cost
    document.getElementById('dailyCost').textContent = '$' + data.dailyCost;
    updateElectricityChange('costChange', data.dailyCost - 24.50, '');
    
    // Simulate equipment status changes
    updateEquipmentStatus();
}

function updateElectricityChange(elementId, change, unit) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const absChange = Math.abs(change);
    const isPositive = change > 0;
    const icon = isPositive ? 'fa-arrow-up' : 'fa-arrow-down';
    const colorClass = isPositive ? 'negative' : 'positive';
    
    element.className = `stat-change ${colorClass} mt-2`;
    element.innerHTML = `<i class="fas ${icon}"></i> ${isPositive ? '+' : ''}${change.toFixed(1)}${unit}`;
}

function updateElectricityStatus(elementId, status) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    let icon, colorClass, text;
    
    switch(status) {
        case 'excellent':
            icon = 'fa-check';
            colorClass = 'positive';
            text = 'Excellent';
            break;
        case 'good':
            icon = 'fa-check-circle';
            colorClass = 'positive';
            text = 'Good';
            break;
        case 'warning':
            icon = 'fa-exclamation-triangle';
            colorClass = 'negative';
            text = 'Warning';
            break;
        default:
            icon = 'fa-minus';
            colorClass = 'neutral';
            text = 'Stable';
    }
    
    element.className = `stat-change ${colorClass} mt-2`;
    element.innerHTML = `<i class="fas ${icon}"></i> ${text}`;
}

function updateEquipmentStatus() {
    // Simulate equipment status changes
    const equipmentStatuses = [
        { id: 'aeratorStatus', total: 8, name: 'Aerators' },
        { id: 'pumpStatus', total: 4, name: 'Water Pumps' },
        { id: 'blowerStatus', total: 3, name: 'Blowers' },
        { id: 'feederStatus', total: 6, name: 'Feeders' }
    ];
    
    equipmentStatuses.forEach(equipment => {
        // Randomly simulate equipment going online/offline
        const online = Math.random() > 0.1 ? equipment.total : equipment.total - Math.floor(Math.random() * 2);
        const element = document.getElementById(equipment.id);
        
        if (element) {
            const badgeClass = online === equipment.total ? 'bg-success' : 
                              online >= equipment.total * 0.8 ? 'bg-warning text-dark' : 'bg-danger';
            
            element.className = `badge ${badgeClass}`;
            element.textContent = `${online}/${equipment.total} Online`;
        }
    });
}

// Quick Actions
function setupQuickActions() {
    // Export Data
    document.getElementById('exportDataBtn').addEventListener('click', function() {
        showNotification('Exporting sensor data...', 'info');
        setTimeout(() => {
            showNotification('Data exported successfully!', 'success');
        }, 2000);
    });
    
    // Calibrate All Sensors
    document.getElementById('calibrateAllBtn').addEventListener('click', function() {
        showNotification('Starting sensor calibration...', 'info');
        setTimeout(() => {
            showNotification('All sensors calibrated successfully!', 'success');
        }, 3000);
    });
    
    // Test Alerts
    document.getElementById('testAlertsBtn').addEventListener('click', function() {
        // Trigger test alerts
        window.dispatchEvent(new CustomEvent('iotAlert', {
            detail: {
                sensorId: 'temperature',
                level: 'warning',
                message: 'Test alert: Temperature sensor check',
                value: 32.5,
                timestamp: new Date(),
                sensor: 'Water Temperature'
            }
        }));
        
        setTimeout(() => {
            window.dispatchEvent(new CustomEvent('iotAlert', {
                detail: {
                    sensorId: 'dissolved_oxygen',
                    level: 'critical',
                    message: 'Test alert: Low oxygen level detected',
                    value: 3.8,
                    timestamp: new Date(),
                    sensor: 'Dissolved Oxygen'
                }
            }));
        }, 1000);
        
        showNotification('Test alerts triggered', 'info');
    });
    
    // System Report
    document.getElementById('systemReportBtn').addEventListener('click', function() {
        showNotification('Generating system report...', 'info');
        setTimeout(() => {
            showNotification('System report generated successfully!', 'success');
        }, 2500);
    });
}

// Live Data Simulation
function startLiveDataSimulation() {
    // Update live stats periodically
    setInterval(() => {
        // Simulate data rate changes
        const dataRate = (2.0 + Math.random() * 1.0).toFixed(1);
        document.getElementById('dataRate').textContent = dataRate + ' MB/h';
        
        // Simulate uptime changes
        const uptime = (99.5 + Math.random() * 0.5).toFixed(1);
        document.getElementById('uptime').textContent = uptime + '%';
    }, 5000);
}

// Notification Function
function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}
</script>

<!-- Mobile Enhancements JavaScript -->
<script src="{% static 'js/electricity-mobile.js' %}"></script>
<script>
// Initialize mobile dashboard if on mobile device
document.addEventListener('DOMContentLoaded', function() {
    // Initialize mobile electricity dashboard for enhanced mobile experience
    if (typeof MobileElectricityDashboard !== 'undefined') {
        const mobileDashboard = new MobileElectricityDashboard();
        
        // Add mobile-specific features for real-time monitoring
        if (mobileDashboard.isMobile) {
            // Add mobile action bar
            const actionBar = document.createElement('div');
            actionBar.className = 'mobile-action-bar show';
            actionBar.innerHTML = `
                <button class="mobile-action-btn" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </button>
                <button class="mobile-action-btn" onclick="window.location.href='/electricity/dashboard/'">
                    <i class="fas fa-bolt"></i>
                    <span>Power</span>
                </button>
                <button class="mobile-action-btn active">
                    <i class="fas fa-satellite-dish"></i>
                    <span>Real-time</span>
                </button>
                <button class="mobile-action-btn" onclick="window.location.href='/iot_integration/power_analytics/'">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </button>
                <button class="mobile-action-btn" onclick="window.location.href='/core/'">
                    <i class="fas fa-home"></i>
                    <span>Home</span>
                </button>
            `;
            document.body.appendChild(actionBar);
            
            // Add mobile-specific notification for new features
            setTimeout(() => {
                mobileDashboard.showNotification('Mobile features enabled: Pull to refresh, touch gestures, and offline support', 'info', 3000);
            }, 1000);
        }
    }
});
</script>
{% endblock %}
