<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ treatment_plan.name }} - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .medicine-icon {
        font-size: 2.5rem;
        opacity: 0.9;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-action i {
        font-size: 1.1rem;
    }
    
    .card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 15px 15px 0 0 !important;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    
    .step-card {
        transition: all 0.3s ease;
        border-left: 4px solid #e9ecef;
    }
    
    .step-card.completed {
        border-left-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
    }
    
    .step-card.pending {
        border-left-color: #ffc107;
        background: rgba(255, 193, 7, 0.05);
    }
    
    .step-card.skipped {
        border-left-color: #dc3545;
        background: rgba(220, 53, 69, 0.05);
    }
    
    .progress-container {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-clipboard-check medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Treatment Plan</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">{{ treatment_plan.name }}</h2>
                    <p class="mb-0 opacity-75">{{ treatment_plan.pond.name }}</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="status-badge bg-{% if treatment_plan.status == 'planned' %}info{% elif treatment_plan.status == 'in_progress' %}primary{% elif treatment_plan.status == 'completed' %}success{% else %}secondary{% endif %}">
                            {{ treatment_plan.get_status_display }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="/medicine/treatment-plans/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list"></i> All Plans
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">Loading...</span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Add Step
            </a>
            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/edit/" class="quick-action">
                <i class="fas fa-edit"></i>
                Edit Plan
            </a>
            <a href="/medicine/treatment-plans/" class="quick-action">
                <i class="fas fa-arrow-left"></i>
                Back to Plans
            </a>
        </div>

        <!-- Content -->
        <div class="row">
            <!-- Left Column - Plan Details -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Plan Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <p class="text-muted mb-1 small">Pond</p>
                            <p class="mb-0">
                                <a href="/ponds/{{ treatment_plan.pond.id }}/" class="text-decoration-none">
                                    {{ treatment_plan.pond.name }}
                                </a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <p class="text-muted mb-1 small">Duration</p>
                            <p class="mb-0">{{ treatment_plan.start_date|date:"M d, Y" }} to {{ treatment_plan.end_date|date:"M d, Y" }}</p>
                            <p class="mb-0 small text-muted">{{ treatment_plan.duration_days }} days</p>
                        </div>
                        
                        <div class="mb-3">
                            <p class="text-muted mb-1 small">Description</p>
                            <p class="mb-0">{{ treatment_plan.description|default:"No description provided" }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <p class="text-muted mb-1 small">Created</p>
                            <p class="mb-0">{{ treatment_plan.created_at|date:"M d, Y H:i" }}</p>
                            {% if treatment_plan.created_by %}
                            <p class="mb-0 small text-muted">by {{ treatment_plan.created_by.get_full_name|default:treatment_plan.created_by.username }}</p>
                            {% endif %}
                        </div>
                        
                        {% if treatment_plan.is_active %}
                        <div class="alert alert-primary">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>This treatment plan is currently active.</span>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Progress Overview -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Progress Overview</h5>
                    </div>
                    <div class="card-body">
                        {% if treatment_plan.steps.count > 0 %}
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" 
                                 style="width: {{ progress_percentage }}%;" 
                                 aria-valuenow="{{ progress_percentage }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                                {{ progress_percentage }}%
                            </div>
                        </div>
                        <div class="row text-center">
                            <div class="col-4">
                                <h6 class="mb-0">{{ completed_steps }}</h6>
                                <small class="text-muted">Completed</small>
                            </div>
                            <div class="col-4">
                                <h6 class="mb-0">{{ total_steps }}</h6>
                                <small class="text-muted">Total</small>
                            </div>
                            <div class="col-4">
                                <h6 class="mb-0">{{ remaining_steps }}</h6>
                                <small class="text-muted">Remaining</small>
                            </div>
                        </div>
                        {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-clipboard-list text-muted mb-2" style="font-size: 2rem;"></i>
                            <p class="text-muted mb-0">No steps defined yet</p>
                            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/create/" class="btn btn-primary btn-sm mt-2">
                                <i class="fas fa-plus me-1"></i> Add First Step
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Right Column - Treatment Steps -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Treatment Steps</h5>
                        <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/create/" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i> Add Step
                        </a>
                    </div>
                    <div class="card-body">
                        {% if treatment_plan.steps.count > 0 %}
                        <div class="row">
                            {% for step in treatment_plan.steps.all %}
                            <div class="col-md-6 mb-3">
                                <div class="card step-card {{ step.status }}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0">Day {{ step.day_number }}</h6>
                                            <span class="badge bg-{% if step.status == 'completed' %}success{% elif step.status == 'pending' %}warning{% else %}danger{% endif %}">
                                                {{ step.get_status_display }}
                                            </span>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <p class="text-muted mb-1 small">Medicine</p>
                                            <p class="mb-0">{{ step.medicine.name }}</p>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <p class="text-muted mb-1 small">Dosage</p>
                                            <p class="mb-0">{{ step.dosage }} {{ step.medicine.unit }}</p>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <p class="text-muted mb-1 small">Scheduled Date</p>
                                            <p class="mb-0">{{ step.scheduled_date|date:"M d, Y" }}</p>
                                        </div>
                                        
                                        {% if step.instructions %}
                                        <div class="mb-2">
                                            <p class="text-muted mb-1 small">Instructions</p>
                                            <p class="mb-0 small">{{ step.instructions|truncatechars:100 }}</p>
                                        </div>
                                        {% endif %}
                                        
                                        {% if step.completed_date %}
                                        <div class="mb-2">
                                            <p class="text-muted mb-1 small">Completed</p>
                                            <p class="mb-0 small">{{ step.completed_date|date:"M d, Y" }}</p>
                                        </div>
                                        {% endif %}
                                        
                                        <div class="d-flex gap-2 mt-3">
                                            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/{{ step.id }}/" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                            {% if step.status == 'pending' %}
                                            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/{{ step.id }}/complete/" class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> Complete
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list text-muted mb-3" style="font-size: 3rem;"></i>
                            <h5>No Treatment Steps</h5>
                            <p class="text-muted">This treatment plan doesn't have any steps yet.</p>
                            <a href="/medicine/treatment-plans/{{ treatment_plan.id }}/step/create/" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i> Add First Step
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Update time immediately and then every minute
        updateTime();
        setInterval(updateTime, 60000);
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
                        <p class="mb-0">{{ treatment_plan.description|linebreaks }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Created</p>
                        <p class="mb-0">{{ treatment_plan.created_at|date:"M d, Y H:i" }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Last Updated</p>
                        <p class="mb-0">{{ treatment_plan.updated_at|date:"M d, Y H:i" }}</p>
                    </div>
                </div>
            </div>
            
            <!-- Progress -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Progress</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="progress mb-3" style="height: 20px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ progress_percentage }}%;" aria-valuenow="{{ progress_percentage }}" aria-valuemin="0" aria-valuemax="100">{{ progress_percentage|floatformat:0 }}%</div>
                        </div>
                        <p class="mb-0">{{ completed_steps }} of {{ total_steps }} steps completed</p>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h3 class="mb-0">{{ steps|filter_status:'Pending'|length }}</h3>
                                <p class="small text-muted mb-0">Pending</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h3 class="mb-0">{{ completed_steps }}</h3>
                                <p class="small text-muted mb-0">Completed</p>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="bg-light p-2 rounded">
                                <h3 class="mb-0">{{ steps|filter_status:'Skipped'|length }}</h3>
                                <p class="small text-muted mb-0">Skipped</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Update Status -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Update Status</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'medicine:update_treatment_plan_status' treatment_plan.id %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="status" class="form-label">Plan Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="Planned" {% if treatment_plan.status == 'Planned' %}selected{% endif %}>Planned</option>
                                <option value="In Progress" {% if treatment_plan.status == 'In Progress' %}selected{% endif %}>In Progress</option>
                                <option value="Completed" {% if treatment_plan.status == 'Completed' %}selected{% endif %}>Completed</option>
                                <option value="Cancelled" {% if treatment_plan.status == 'Cancelled' %}selected{% endif %}>Cancelled</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Update Status</button>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Right Column -->
        <div class="col-lg-8">
            <!-- Treatment Steps -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Treatment Steps</h5>
                    <a href="{% url 'medicine:treatment_step_create' treatment_plan.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i> Add Step
                    </a>
                </div>
                <div class="card-body">
                    {% if steps %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Date</th>
                                    <th>Medicine</th>
                                    <th>Dosage</th>
                                    <th>Instructions</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for step in steps %}
                                <tr class="{% if step.is_due_today %}table-warning{% endif %}">
                                    <td>Day {{ step.day_number }}</td>
                                    <td>
                                        {{ step.scheduled_date|date:"M d, Y" }}
                                        {% if step.is_due_today %}
                                        <span class="badge bg-warning text-dark">Today</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'medicine:medicine_detail' step.medicine.id %}">
                                            {{ step.medicine.name }}
                                        </a>
                                    </td>
                                    <td>{{ step.dosage }}</td>
                                    <td>{{ step.instructions|truncatechars:50 }}</td>
                                    <td>
                                        <span class="badge {% if step.status == 'Completed' %}bg-success{% elif step.status == 'Pending' %}bg-warning text-dark{% else %}bg-secondary{% endif %}">
                                            {{ step.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'medicine:treatment_step_update' step.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No treatment steps have been added to this plan yet.</span>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'medicine:treatment_step_create' treatment_plan.id %}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add First Step
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // Custom template filter for filtering steps by status
    function filterStatus(steps, status) {
        return steps.filter(step => step.status === status);
    }
</script>
{% endblock %}
{% endblock %}
