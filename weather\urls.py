from django.urls import path
from . import views
from . import views_api
from . import views_export

app_name = 'weather'

urlpatterns = [
    # Dashboard
    path('', views.weather_dashboard, name='weather_dashboard'),
    path('pond-integration/', views.pond_weather_dashboard, name='pond_weather_dashboard'),
    path('unified-map/', views.unified_weather_map, name='unified_weather_map'),
    path('pond-map/<int:pond_id>/', views.pond_weather_map, name='pond_weather_map'),
    path('test-gmaps/', views.test_gmaps, name='test_gmaps'),
    path('simple-gmaps-test/', views.simple_gmaps_test, name='simple_gmaps_test'),
    path('minimal-gmaps/', views.minimal_gmaps, name='minimal_gmaps'),
    path('working-gmaps/<int:pond_id>/', views.working_gmaps_weather, name='working_gmaps_weather'),
    path('debug-gmaps/', views.debug_gmaps, name='debug_gmaps'),
    path('fallback-map/<int:pond_id>/', views.fallback_weather_map, name='fallback_weather_map'),
    path('direct-gmaps/<int:pond_id>/', views.direct_gmaps, name='direct_gmaps'),
    path('history/', views.weather_history, name='weather_history'),

    # Weather Stations
    path('stations/', views.weather_station_list, name='station_list'),
    path('stations/<int:pk>/', views.weather_station_detail, name='station_detail'),
    path('stations/create/', views.weather_station_create, name='station_create'),
    path('stations/<int:pk>/update/', views.weather_station_update, name='station_update'),

    # Weather Data
    path('data/create/', views.weather_data_create, name='data_create'),

    # Weather Forecasts
    path('forecasts/', views.weather_forecast_list, name='forecast_list'),
    path('forecasts/create/', views.weather_forecast_create, name='forecast_create'),

    # Weather Alerts
    path('alerts/', views.weather_alert_list, name='alert_list'),
    path('alerts/create/', views.weather_alert_create, name='alert_create'),

    # Weather Impacts
    path('impacts/', views.weather_impact_list, name='impact_list'),
    path('impacts/create/', views.weather_impact_create, name='impact_create'),
    path('impacts/analysis/', views.weather_impact_analysis, name='impact_analysis'),

    # Weather API Configurations
    path('api/configs/', views_api.api_config_list, name='api_config_list'),
    path('api/configs/create/', views_api.api_config_create, name='api_config_create'),
    path('api/configs/<int:pk>/update/', views_api.api_config_update, name='api_config_update'),
    path('api/configs/<int:pk>/test/', views_api.api_config_test, name='api_config_test'),

    # Weather Maps
    path('maps/', views_api.weather_map_list, name='map_list'),
    path('maps/create/', views_api.weather_map_create, name='map_create'),
    path('maps/<int:pk>/update/', views_api.weather_map_update, name='map_update'),
    path('maps/<int:pk>/view/', views_api.weather_map_view, name='map_view'),
    path('maps/dashboard/', views_api.weather_maps_dashboard, name='maps_dashboard'),

    # Weather Data Update
    path('update-now/', views.update_weather_now, name='update_weather_now'),

    # API Actions
    path('api/fetch-data/', views_api.fetch_weather_data, name='fetch_weather_data'),

    # Data Export
    path('export/', views.weather_export, name='export'),
    path('export/csv/', views_export.export_weather_data_csv, name='export_csv'),
    path('export/json/', views_export.export_weather_data_json, name='export_json'),
    path('export/forecast/csv/', views_export.export_forecast_csv, name='export_forecast_csv'),
    path('export/forecast/csv/<int:station_id>/', views_export.export_forecast_csv, name='export_station_forecast_csv'),
    path('export/summary/csv/', views_export.export_daily_summary_csv, name='export_summary_csv'),
    path('export/summary/csv/<int:station_id>/', views_export.export_daily_summary_csv, name='export_station_summary_csv'),
]
