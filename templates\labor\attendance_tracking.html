<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attendance Tracking - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Header */
    .attendance-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .attendance-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: attendance-sweep 4s infinite;
    }
    
    @keyframes attendance-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-decoration: none;
        display: inline-block;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
        position: relative;
        z-index: 10;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .stats-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
    }

    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stats-label {
        color: #6c757d;
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .stats-present { color: #28a745; }
    .stats-absent { color: #dc3545; }
    .stats-late { color: #ffc107; }
    .stats-total { color: #667eea; }

    .attendance-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .attendance-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .worker-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        margin-right: 15px;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-present {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid rgba(40, 167, 69, 0.2);
    }

    .status-absent {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid rgba(220, 53, 69, 0.2);
    }

    .status-late {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 1px solid rgba(255, 193, 7, 0.2);
    }

    .status-checkout {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 1px solid rgba(108, 117, 125, 0.2);
    }

    .time-display {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #667eea;
    }

    .check-btn {
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
        border: none;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-check-in {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
    }

    .btn-check-in:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }

    .btn-check-out {
        background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        color: white;
    }

    .btn-check-out:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .filter-container {
        background: rgba(102, 126, 234, 0.02);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        padding: 10px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .live-clock {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 25px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
        font-family: 'Courier New', monospace;
        font-size: 1.2rem;
        font-weight: 600;
    }

    .team-section {
        margin-bottom: 30px;
    }

    .team-header {
        background: rgba(102, 126, 234, 0.1);
        padding: 15px 20px;
        border-radius: 10px;
        margin-bottom: 15px;
        border-left: 4px solid #667eea;
    }

    .team-name {
        color: #667eea;
        font-weight: 700;
        font-size: 1.1rem;
        margin: 0;
    }

    .team-stats {
        font-size: 0.9rem;
        color: #6c757d;
        margin: 0;
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Header -->
    <div class="attendance-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Attendance Tracking</h1>
                <p class="lead mb-0">Monitor workforce attendance and working hours</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <button class="control-btn" data-bs-toggle="modal" data-bs-target="#bulkActionModal">
                        <i class="fas fa-tasks me-2"></i>Bulk Actions
                    </button>
                    <a href="/labor/dashboard/" class="control-btn">
                        <i class="fas fa-arrow-left me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Live Clock -->
    <div class="live-clock">
        <div id="liveClock">Loading...</div>
        <small>Current Farm Time</small>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number stats-present">42</div>
                <div class="stats-label">Present Today</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number stats-absent">8</div>
                <div class="stats-label">Absent Today</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number stats-late">5</div>
                <div class="stats-label">Late Arrivals</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-number stats-total">50</div>
                <div class="stats-label">Total Workers</div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <button class="quick-action" onclick="markAllPresent()">
            <i class="fas fa-check-circle"></i>
            Mark All Present
        </button>
        <button class="quick-action" data-bs-toggle="modal" data-bs-target="#attendanceReportModal">
            <i class="fas fa-chart-bar"></i>
            Attendance Report
        </button>
        <a href="/labor/workers/" class="quick-action">
            <i class="fas fa-users"></i>
            Workers
        </a>
        <a href="/labor/teams/" class="quick-action">
            <i class="fas fa-users-cog"></i>
            Teams
        </a>
    </div>

    <!-- Filter Section -->
    <div class="filter-container">
        <div class="row">
            <div class="col-md-3 mb-3">
                <label class="form-label">Date</label>
                <input type="date" class="form-control" id="attendanceDate" value="">
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">Team</label>
                <select class="form-select" id="teamFilter">
                    <option value="">All Teams</option>
                    <option value="pond-maintenance">Pond Maintenance</option>
                    <option value="feeding">Feeding Team</option>
                    <option value="monitoring">Water Quality Monitoring</option>
                    <option value="harvesting">Harvesting Team</option>
                    <option value="security">Security Team</option>
                    <option value="general">General Labor</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">Status</label>
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="present">Present</option>
                    <option value="absent">Absent</option>
                    <option value="late">Late</option>
                    <option value="checkout">Checked Out</option>
                </select>
            </div>
            <div class="col-md-3 mb-3">
                <label class="form-label">Search Worker</label>
                <input type="text" class="form-control" id="searchWorker" placeholder="Search by name...">
            </div>
        </div>
    </div>

    <!-- Attendance by Teams -->
    <div class="enhanced-card">
        <h3 class="section-title">
            <i class="fas fa-clipboard-list me-2"></i>Today's Attendance
        </h3>

        <!-- Pond Maintenance Team -->
        <div class="team-section">
            <div class="team-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="team-name">🌊 Pond Maintenance Team</h5>
                    <span class="team-stats">10/12 Present • 2 Absent</span>
                </div>
            </div>

            <!-- Worker Attendance Cards -->
            <div class="row">
                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">RK</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Ravi Kumar</h6>
                                <small class="text-muted">Worker ID: WRK001</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">08:15 AM</div>
                                <small class="text-muted">Check-in</small>
                            </div>
                            <div>
                                <span class="status-badge status-present">Present</span>
                                <button class="btn check-btn btn-check-out ms-2" onclick="checkOut('WRK001')">
                                    <i class="fas fa-sign-out-alt"></i> Check Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">PS</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Priya Sharma</h6>
                                <small class="text-muted">Worker ID: WRK002</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">09:25 AM</div>
                                <small class="text-muted">Check-in</small>
                            </div>
                            <div>
                                <span class="status-badge status-late">Late</span>
                                <button class="btn check-btn btn-check-out ms-2" onclick="checkOut('WRK002')">
                                    <i class="fas fa-sign-out-alt"></i> Check Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">AP</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Arjun Patel</h6>
                                <small class="text-muted">Worker ID: WRK003</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">--:--</div>
                                <small class="text-muted">Not checked in</small>
                            </div>
                            <div>
                                <span class="status-badge status-absent">Absent</span>
                                <button class="btn check-btn btn-check-in ms-2" onclick="checkIn('WRK003')">
                                    <i class="fas fa-sign-in-alt"></i> Check In
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">MS</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Meera Singh</h6>
                                <small class="text-muted">Worker ID: WRK004</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">08:00 - 17:30</div>
                                <small class="text-muted">Completed</small>
                            </div>
                            <div>
                                <span class="status-badge status-checkout">Checked Out</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Feeding Team -->
        <div class="team-section">
            <div class="team-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="team-name">🍽️ Feeding Team</h5>
                    <span class="team-stats">8/10 Present • 2 Absent</span>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">SR</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Suresh Reddy</h6>
                                <small class="text-muted">Worker ID: WRK005</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">07:45 AM</div>
                                <small class="text-muted">Check-in</small>
                            </div>
                            <div>
                                <span class="status-badge status-present">Present</span>
                                <button class="btn check-btn btn-check-out ms-2" onclick="checkOut('WRK005')">
                                    <i class="fas fa-sign-out-alt"></i> Check Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">LN</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Lakshmi Nair</h6>
                                <small class="text-muted">Worker ID: WRK006</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">08:30 AM</div>
                                <small class="text-muted">Check-in</small>
                            </div>
                            <div>
                                <span class="status-badge status-present">Present</span>
                                <button class="btn check-btn btn-check-out ms-2" onclick="checkOut('WRK006')">
                                    <i class="fas fa-sign-out-alt"></i> Check Out
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Team -->
        <div class="team-section">
            <div class="team-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="team-name">🛡️ Security Team</h5>
                    <span class="team-stats">6/6 Present • 24/7 Coverage</span>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">VS</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Vinod Singh</h6>
                                <small class="text-muted">Worker ID: WRK007 • Night Shift</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">22:00 PM</div>
                                <small class="text-muted">Night duty</small>
                            </div>
                            <div>
                                <span class="status-badge status-present">On Duty</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="attendance-card">
                        <div class="d-flex align-items-center">
                            <div class="worker-avatar">SA</div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1 fw-bold">Sunil Ashok</h6>
                                <small class="text-muted">Worker ID: WRK008 • Day Shift</small>
                            </div>
                            <div class="text-end me-3">
                                <div class="time-display">06:00 - 18:00</div>
                                <small class="text-muted">Day duty</small>
                            </div>
                            <div>
                                <span class="status-badge status-present">On Duty</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Action Modal -->
<div class="modal fade" id="bulkActionModal" tabindex="-1" aria-labelledby="bulkActionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title" id="bulkActionModalLabel">
                    <i class="fas fa-tasks me-2"></i>Bulk Actions
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Select Action</label>
                    <select class="form-select" id="bulkAction">
                        <option value="">Choose action...</option>
                        <option value="mark-present">Mark Selected as Present</option>
                        <option value="mark-absent">Mark Selected as Absent</option>
                        <option value="check-out-all">Check Out All Present Workers</option>
                        <option value="export-attendance">Export Today's Attendance</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Team (Optional)</label>
                    <select class="form-select" id="bulkTeam">
                        <option value="">All Teams</option>
                        <option value="pond-maintenance">Pond Maintenance</option>
                        <option value="feeding">Feeding Team</option>
                        <option value="monitoring">Water Quality Monitoring</option>
                        <option value="harvesting">Harvesting Team</option>
                        <option value="security">Security Team</option>
                        <option value="general">General Labor</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkAction()">
                    <i class="fas fa-check me-2"></i>Execute Action
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Report Modal -->
<div class="modal fade" id="attendanceReportModal" tabindex="-1" aria-labelledby="attendanceReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title" id="attendanceReportModalLabel">
                    <i class="fas fa-chart-bar me-2"></i>Attendance Report
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">From Date</label>
                        <input type="date" class="form-control" id="reportFromDate">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">To Date</label>
                        <input type="date" class="form-control" id="reportToDate">
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Team</label>
                        <select class="form-select" id="reportTeam">
                            <option value="">All Teams</option>
                            <option value="pond-maintenance">Pond Maintenance</option>
                            <option value="feeding">Feeding Team</option>
                            <option value="monitoring">Water Quality Monitoring</option>
                            <option value="harvesting">Harvesting Team</option>
                            <option value="security">Security Team</option>
                            <option value="general">General Labor</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Report Type</label>
                        <select class="form-select" id="reportType">
                            <option value="summary">Summary Report</option>
                            <option value="detailed">Detailed Report</option>
                            <option value="overtime">Overtime Report</option>
                            <option value="absent">Absenteeism Report</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="generateReport()">
                    <i class="fas fa-download me-2"></i>Generate Report
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
    
    // Update live clock
    updateClock();
    setInterval(updateClock, 1000);
    
    // Set default date range for reports
    const today = new Date().toISOString().split('T')[0];
    const weekAgo = new Date(Date.now() - 7*24*60*60*1000).toISOString().split('T')[0];
    document.getElementById('reportFromDate').value = weekAgo;
    document.getElementById('reportToDate').value = today;
});

function updateClock() {
    const now = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };
    document.getElementById('liveClock').textContent = now.toLocaleString('en-US', options);
}

function checkIn(workerId) {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit', hour12: true});
    
    console.log(`Checking in worker ${workerId} at ${timeString}`);
    
    // Here you would make an API call to record the check-in
    // For demo purposes, we'll just show an alert
    alert(`Worker ${workerId} checked in successfully at ${timeString}`);
    
    // Refresh the page to show updated status (in real implementation, update UI dynamically)
    // location.reload();
}

function checkOut(workerId) {
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {hour: '2-digit', minute:'2-digit', hour12: true});
    
    console.log(`Checking out worker ${workerId} at ${timeString}`);
    
    // Here you would make an API call to record the check-out
    alert(`Worker ${workerId} checked out successfully at ${timeString}`);
    
    // Refresh the page to show updated status
    // location.reload();
}

function markAllPresent() {
    if (confirm('Are you sure you want to mark all workers as present for today?')) {
        console.log('Marking all workers present');
        alert('All workers marked as present for today!');
        // location.reload();
    }
}

function executeBulkAction() {
    const action = document.getElementById('bulkAction').value;
    const team = document.getElementById('bulkTeam').value;
    
    if (!action) {
        alert('Please select an action');
        return;
    }
    
    console.log(`Executing bulk action: ${action} for team: ${team || 'all teams'}`);
    
    const actionMessages = {
        'mark-present': 'All selected workers marked as present!',
        'mark-absent': 'All selected workers marked as absent!',
        'check-out-all': 'All present workers checked out!',
        'export-attendance': 'Attendance data exported successfully!'
    };
    
    alert(actionMessages[action] || 'Action completed successfully!');
    bootstrap.Modal.getInstance(document.getElementById('bulkActionModal')).hide();
}

function generateReport() {
    const fromDate = document.getElementById('reportFromDate').value;
    const toDate = document.getElementById('reportToDate').value;
    const team = document.getElementById('reportTeam').value;
    const reportType = document.getElementById('reportType').value;
    
    if (!fromDate || !toDate) {
        alert('Please select date range');
        return;
    }
    
    console.log(`Generating ${reportType} report for ${team || 'all teams'} from ${fromDate} to ${toDate}`);
    
    alert(`${reportType.charAt(0).toUpperCase() + reportType.slice(1)} report generated successfully!\nDate Range: ${fromDate} to ${toDate}\nTeam: ${team || 'All Teams'}`);
    
    bootstrap.Modal.getInstance(document.getElementById('attendanceReportModal')).hide();
}
</script>

</body>
</html>
