#!/usr/bin/env python
"""
Test script for the automated weather monitoring and alert system.
This script will test the weather monitoring functionality and verify
that alerts are properly generated and sent.
"""

import os
import sys
import django
from datetime import datetime, timedelta
import json

# Setup Django environment
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

from django.db import transaction
from django.contrib.auth import get_user_model
from ponds.models import Pond, Farm
from water_quality.models import WaterQualityReading
from weather.models import WeatherStation, WeatherData
from weather.tasks import monitor_pond_weather, update_weather_data, cleanup_old_weather_data
from alerts.models import Alert
from labor.models import Worker
from notifications.models import Notification

User = get_user_model()


def create_test_data():
    """Create test data for the weather monitoring system."""
    print("Creating test data...")
      # Create test user
    user, created = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'admin'
        }
    )
      # Create test farm
    farm, created = Farm.objects.get_or_create(
        name='Test Weather Farm',
        defaults={
            'location': 'Test Location',
            'size': 100,
            'description': 'Test farm for weather monitoring'
        }
    )
    # Add user to farm
    farm.users.add(user)
      # Create test pond with specific coordinates
    pond, created = Pond.objects.get_or_create(
        name='Weather Test Pond',
        defaults={
            'farm': farm,
            'latitude': 13.0827,  # Chennai coordinates for testing
            'longitude': 80.2707,
            'size': 1000,
            'depth': 2.0,
            'species': 'Tiger Shrimp',
            'density': '50 PL/m²'
        }
    )
      # Create test worker
    worker, created = Worker.objects.get_or_create(
        name='Test Worker',
        defaults={
            'user': user,
            'phone': '+1234567890',
            'email': '<EMAIL>',
            'is_active': True
        }
    )
      # Create recent water quality reading
    from django.utils import timezone
    reading, created = WaterQualityReading.objects.get_or_create(
        pond=pond,
        timestamp=timezone.now() - timedelta(hours=1),
        defaults={
            'temperature': 28.5,
            'ph': 7.8,
            'oxygen': 6.2,
            'salinity': 25.0,
            'ammonia': 0.5
        }
    )
    
    print(f"Test data created: User={user.email}, Farm={farm.name}, Pond={pond.name}")
    return user, farm, pond, worker


def test_weather_monitoring():
    """Test the weather monitoring task."""
    print("\n=== Testing Weather Monitoring Task ===")
    
    try:
        # Run the weather monitoring task
        result = monitor_pond_weather.delay()
        
        # Wait for the task to complete (for testing)
        print(f"Task ID: {result.id}")
        print("Task started... waiting for completion")
        
        # Check if alerts were created
        recent_alerts = Alert.objects.filter(
            created_at__gte=datetime.now() - timedelta(minutes=10)
        ).order_by('-created_at')
        
        print(f"Recent alerts created: {recent_alerts.count()}")
        for alert in recent_alerts[:5]:  # Show first 5
            print(f"  - {alert.alert_type}: {alert.message} (Severity: {alert.severity})")
        
        # Check weather data
        recent_weather = WeatherData.objects.filter(
            timestamp__gte=datetime.now() - timedelta(minutes=10)
        ).order_by('-timestamp')
        
        print(f"Recent weather data entries: {recent_weather.count()}")
        for weather in recent_weather[:3]:  # Show first 3
            print(f"  - Station: {weather.station.name}, Temp: {weather.temperature}°C, "
                  f"Humidity: {weather.humidity}%, Weather: {weather.weather_condition}")
        
        return True
        
    except Exception as e:
        print(f"Error testing weather monitoring: {e}")
        return False


def test_weather_api():
    """Test the weather API integration."""
    print("\n=== Testing Weather API Integration ===")
    
    try:
        from weather.utils import get_weather_data
        
        # Test coordinates (Chennai)
        lat, lon = 13.0827, 80.2707
        weather_data = get_weather_data(lat, lon)
        
        if weather_data:
            print("Weather API test successful!")
            print(f"Temperature: {weather_data.get('temperature', 'N/A')}°C")
            print(f"Humidity: {weather_data.get('humidity', 'N/A')}%")
            print(f"Weather: {weather_data.get('weather_condition', 'N/A')}")
            print(f"Wind Speed: {weather_data.get('wind_speed', 'N/A')} m/s")
            return True
        else:
            print("Weather API test failed - no data returned")
            return False
            
    except Exception as e:
        print(f"Error testing weather API: {e}")
        return False


def test_alert_generation():
    """Test alert generation with simulated critical weather."""
    print("\n=== Testing Alert Generation ===")
    
    try:
        from weather.utils import analyze_weather_for_alerts
        
        # Simulate critical weather conditions
        critical_weather = {
            'temperature': 40.0,  # High temperature
            'humidity': 90.0,     # High humidity
            'wind_speed': 15.0,   # High wind
            'weather_condition': 'thunderstorm',
            'pressure': 1005.0,
            'rainfall': 25.0,     # Heavy rain
            'timestamp': datetime.now()
        }
        
        pond = Pond.objects.first()
        if pond:
            alerts = analyze_weather_for_alerts(pond, critical_weather)
            print(f"Generated {len(alerts)} alerts for critical weather conditions:")
            for alert in alerts:
                print(f"  - {alert['type']}: {alert['message']} (Severity: {alert['severity']})")
            return len(alerts) > 0
        else:
            print("No pond found for testing")
            return False
            
    except Exception as e:
        print(f"Error testing alert generation: {e}")
        return False


def test_notification_system():
    """Test the notification system."""
    print("\n=== Testing Notification System ===")
    
    try:
        from weather.utils import send_weather_notification
        
        user = User.objects.first()
        pond = Pond.objects.first()
        
        if user and pond:
            # Test email notification
            success = send_weather_notification(
                user=user,
                pond=pond,
                alert_type='HIGH_TEMPERATURE',
                message='Test weather alert notification',
                severity='HIGH'
            )
            
            if success:
                print("Notification system test successful!")
                
                # Check if notification was created
                recent_notifications = Notification.objects.filter(
                    user=user,
                    created_at__gte=datetime.now() - timedelta(minutes=5)
                ).order_by('-created_at')
                
                print(f"Recent notifications: {recent_notifications.count()}")
                for notification in recent_notifications[:3]:
                    print(f"  - {notification.title}: {notification.message}")
                
                return True
            else:
                print("Notification system test failed")
                return False
        else:
            print("No user or pond found for testing")
            return False
            
    except Exception as e:
        print(f"Error testing notification system: {e}")
        return False


def test_cleanup_task():
    """Test the cleanup task."""
    print("\n=== Testing Cleanup Task ===")
    
    try:
        # Create old test data
        old_weather = WeatherData.objects.filter(
            timestamp__lt=datetime.now() - timedelta(days=35)
        ).count()
        
        print(f"Old weather data entries (>35 days): {old_weather}")
        
        # Run cleanup task
        result = cleanup_old_weather_data.delay()
        print(f"Cleanup task started: {result.id}")
        
        return True
        
    except Exception as e:
        print(f"Error testing cleanup task: {e}")
        return False


def main():
    """Main test function."""
    print("🌤️  Starting Weather Monitoring System Tests")
    print("=" * 50)
    
    # Create test data
    create_test_data()
    
    # Run tests
    tests = [
        ("Weather API Integration", test_weather_api),
        ("Alert Generation", test_alert_generation),
        ("Notification System", test_notification_system),
        ("Weather Monitoring Task", test_weather_monitoring),
        ("Cleanup Task", test_cleanup_task),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{status}: {test_name}")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ ERROR: {test_name} - {e}")
    
    # Summary
    print(f"\n{'='*50}")
    print("🎯 TEST SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Weather monitoring system is ready.")
    else:
        print("⚠️  Some tests failed. Please check the configuration.")
    
    # Show system status
    print(f"\n{'='*50}")
    print("📊 SYSTEM STATUS")
    print(f"{'='*50}")
    print(f"Total Ponds: {Pond.objects.count()}")
    print(f"Active Weather Stations: {WeatherStation.objects.count()}")
    print(f"Recent Weather Data: {WeatherData.objects.filter(timestamp__gte=datetime.now() - timedelta(hours=1)).count()}")
    print(f"Recent Alerts: {Alert.objects.filter(created_at__gte=datetime.now() - timedelta(hours=1)).count()}")
    print(f"Total Users: {User.objects.count()}")
    print(f"Active Workers: {Worker.objects.filter(is_active=True).count()}")


if __name__ == '__main__':
    main()
