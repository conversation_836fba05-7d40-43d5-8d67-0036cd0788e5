{% extends 'base.html' %}
{% load static %}

{% block title %}Blockchain & Supply Chain - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<link href="{% static 'css/unified-template.css' %}" rel="stylesheet">
<link href="{% static 'css/computer-vision.css' %}" rel="stylesheet">
<style>
    .blockchain-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .blockchain-hero::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }
    
    .blockchain-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 1rem;
    }
    
    .blockchain-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse-blockchain 2s infinite;
    }
    
    @keyframes pulse-blockchain {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.3); }
    }
    
    .blockchain-metrics {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: #e2e8f0;
    }
    
    .blockchain-metric-item {
        text-align: center;
        padding: 1rem;
    }
    
    .blockchain-metric-value {
        font-size: 2rem;
        font-weight: 700;
        color: #e2e8f0;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .blockchain-metric-label {
        font-size: 0.9rem;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }
    
    .blockchain-metric-change {
        font-size: 0.8rem;
        font-weight: 600;
        margin-top: 0.25rem;
    }
    
    .blockchain-metric-change.positive {
        color: #10b981;
    }
    
    .blockchain-metric-change.negative {
        color: #ef4444;
    }
    
    .blockchain-metric-change.neutral {
        color: #94a3b8;
    }
    
    .security-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(16, 185, 129, 0.2);
        color: #10b981;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .security-badge i {
        font-size: 1rem;
    }
    
    @media (max-width: 768px) {
        .blockchain-hero {
            padding: 2rem 1rem;
        }
        
        .blockchain-metrics {
            padding: 1rem;
        }
        
        .blockchain-metric-item {
            padding: 0.75rem 0.5rem;
        }
        
        .blockchain-metric-value {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Blockchain Hero Section -->
    <div class="blockchain-hero">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-link me-3"></i>
                    Blockchain & Supply Chain
                </h1>
                <p class="mb-0 fs-5">
                    Decentralized traceability, transparency, and smart contract automation
                </p>
                <div class="blockchain-indicator">
                    <div class="blockchain-dot"></div>
                    <span>BLOCKCHAIN ACTIVE</span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="blockchain-overview">
                    <div class="mb-2">
                        <i class="fas fa-cube me-2"></i>
                        <span id="totalBlocks">12</span> Blocks Mined
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-exchange-alt me-2"></i>
                        <span id="totalTransactions">247</span> Transactions
                    </div>
                    <div class="mb-3">
                        <i class="fas fa-network-wired me-2"></i>
                        <span id="networkNodes">5</span> Network Nodes
                    </div>
                    <div class="security-badge">
                        <i class="fas fa-shield-check"></i>
                        <span>CHAIN VERIFIED</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blockchain Metrics -->
    <div class="blockchain-metrics">
        <div class="row">
            <div class="col-md-3 col-6">
                <div class="blockchain-metric-item">
                    <span class="blockchain-metric-value" id="traceabilityRecords">47</span>
                    <span class="blockchain-metric-label">Traceability Records</span>
                    <div class="blockchain-metric-change positive" id="recordsChange">+5 today</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="blockchain-metric-item">
                    <span class="blockchain-metric-value" id="smartContracts">3</span>
                    <span class="blockchain-metric-label">Smart Contracts</span>
                    <div class="blockchain-metric-change neutral" id="contractsChange">Active</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="blockchain-metric-item">
                    <span class="blockchain-metric-value" id="verificationRate">99.8%</span>
                    <span class="blockchain-metric-label">Verification Rate</span>
                    <div class="blockchain-metric-change positive" id="verificationChange">+0.2%</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="blockchain-metric-item">
                    <span class="blockchain-metric-value" id="carbonFootprint">2.1 kg</span>
                    <span class="blockchain-metric-label">CO₂/kg Shrimp</span>
                    <div class="blockchain-metric-change positive" id="carbonChange">-0.3 kg</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Supply Chain Dashboard Container -->
    <div id="supply-chain-dashboard-container">
        <!-- Supply chain dashboard will be rendered here by JavaScript -->
    </div>

    <!-- Blockchain Features -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="blockchain-features-container">
                <div class="blockchain-metrics">
                    <h5 class="mb-3">
                        <i class="fas fa-cogs me-2"></i>
                        Blockchain Features
                    </h5>
                    <div class="row">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Immutable Records</h6>
                                    <p>Tamper-proof data storage with cryptographic security</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-eye"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Full Traceability</h6>
                                    <p>Complete supply chain visibility from farm to consumer</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-file-contract"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Smart Contracts</h6>
                                    <p>Automated compliance and quality assurance</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-qrcode"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>QR Code Verification</h6>
                                    <p>Instant product verification for consumers</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-leaf"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Sustainability Tracking</h6>
                                    <p>Environmental impact monitoring and reporting</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="fas fa-certificate"></i>
                                </div>
                                <div class="feature-content">
                                    <h6>Certification Management</h6>
                                    <p>Digital certificates and compliance tracking</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.feature-card {
    background: rgba(74, 85, 104, 0.2);
    border: 1px solid rgba(74, 85, 104, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.feature-card:hover {
    background: rgba(74, 85, 104, 0.3);
    border-color: rgba(102, 126, 234, 0.5);
    transform: translateY(-3px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.feature-content h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #e2e8f0;
    margin-bottom: 0.5rem;
}

.feature-content p {
    font-size: 0.9rem;
    color: #94a3b8;
    margin: 0;
    line-height: 1.4;
}
</style>
{% endblock %}

{% block extra_js %}
<!-- Blockchain Engine -->
<script src="{% static 'js/blockchain-engine.js' %}"></script>
<!-- Supply Chain Dashboard -->
<script src="{% static 'js/supply-chain-dashboard.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize blockchain dashboard
    initializeBlockchainDashboard();
    
    // Start live data simulation
    startBlockchainDataSimulation();
});

function initializeBlockchainDashboard() {
    // Listen for blockchain transaction updates
    window.addEventListener('blockchainTransaction', (event) => {
        updateBlockchainStats(event.detail);
    });
    
    // Listen for blockchain block updates
    window.addEventListener('blockchainBlock', (event) => {
        updateBlockchainBlocks(event.detail);
    });
}

function updateBlockchainStats(data) {
    // Update transaction count
    const transactionsElement = document.getElementById('totalTransactions');
    if (transactionsElement) {
        const current = parseInt(transactionsElement.textContent);
        transactionsElement.textContent = current + 1;
    }
    
    // Update traceability records if it's a traceability transaction
    if (data.transaction && data.transaction.type === 'traceability') {
        const recordsElement = document.getElementById('traceabilityRecords');
        if (recordsElement) {
            const current = parseInt(recordsElement.textContent);
            recordsElement.textContent = current + 1;
        }
    }
}

function updateBlockchainBlocks(data) {
    // Update block count
    const blocksElement = document.getElementById('totalBlocks');
    if (blocksElement) {
        const current = parseInt(blocksElement.textContent);
        blocksElement.textContent = current + 1;
    }
    
    showNotification(`New block mined: Block #${data.block.index}`, 'success');
}

function startBlockchainDataSimulation() {
    // Update live stats periodically
    setInterval(() => {
        // Simulate verification rate changes
        const verificationRate = (99.5 + Math.random() * 0.5).toFixed(1); // 99.5-100%
        document.getElementById('verificationRate').textContent = verificationRate + '%';
        
        // Simulate carbon footprint improvements
        const carbonFootprint = (2.0 + Math.random() * 0.3).toFixed(1); // 2.0-2.3 kg
        document.getElementById('carbonFootprint').textContent = carbonFootprint + ' kg';
        
        // Update change indicators
        updateMetricChange('recordsChange', Math.floor(Math.random() * 8) + 3, ' today');
        updateMetricChange('verificationChange', (Math.random() * 0.4 - 0.2).toFixed(1), '%');
        updateMetricChange('carbonChange', -(Math.random() * 0.5 + 0.1).toFixed(1), ' kg');
    }, 15000); // Update every 15 seconds
}

function updateMetricChange(elementId, change, unit) {
    const element = document.getElementById(elementId);
    if (element) {
        if (typeof change === 'number') {
            const changeText = change > 0 ? '+' + change : change.toString();
            element.textContent = changeText + unit;
            
            // Update color based on change
            element.className = 'blockchain-metric-change ' + (change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral');
        } else {
            element.textContent = change;
            element.className = 'blockchain-metric-change neutral';
        }
    }
}

function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}
</script>
{% endblock %}
