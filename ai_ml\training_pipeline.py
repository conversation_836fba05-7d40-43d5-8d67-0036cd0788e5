"""
Automated ML Training Pipeline
Handles automated model training, validation, and deployment
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import mean_squared_error, accuracy_score, classification_report, r2_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
import joblib
import logging
from typing import Dict, List, Any, Optional
import threading
from celery import shared_task

from .models import MLModel, TrainingJob, Prediction
from water_quality.models import WaterQualityReading
from ponds.models import Pond

logger = logging.getLogger(__name__)

class AutomatedTrainingPipeline:
    """
    Automated ML model training and deployment pipeline
    """
    
    def __init__(self):
        self.models_dir = os.path.join(settings.BASE_DIR, 'ai_ml', 'trained_models')
        os.makedirs(self.models_dir, exist_ok=True)
        self.training_lock = threading.Lock()
        self.model_configs = {
            'WATER_QUALITY_PREDICTION': {
                'model_class': RandomForestRegressor,
                'params': {'n_estimators': 100, 'random_state': 42},
                'scoring': 'r2',
                'threshold': 0.8
            },
            'DISEASE_DETECTION': {
                'model_class': RandomForestClassifier,
                'params': {'n_estimators': 100, 'random_state': 42},
                'scoring': 'accuracy',
                'threshold': 0.85
            },
            'GROWTH_PREDICTION': {
                'model_class': RandomForestRegressor,
                'params': {'n_estimators': 100, 'random_state': 42},
                'scoring': 'r2',
                'threshold': 0.75
            }
        }
    
    def should_retrain_model(self, model_type: str) -> bool:
        """Check if model needs retraining"""
        try:
            model = MLModel.objects.filter(model_type=model_type, status='active').first()
            
            if not model:
                logger.info(f"No active model found for {model_type}, training needed")
                return True
            
            # Check model age
            days_old = (timezone.now() - model.created_at).days
            if days_old > 7:
                logger.info(f"Model {model_type} is {days_old} days old, retraining needed")
                return True
            
            # Check model performance
            if hasattr(model, 'accuracy') and model.accuracy:
                threshold = self.model_configs.get(model_type, {}).get('threshold', 0.8)
                if model.accuracy < threshold:
                    logger.info(f"Model {model_type} accuracy {model.accuracy} below threshold {threshold}")
                    return True
            
            # Check for new data
            new_data_count = self._count_new_data_since(model_type, model.last_training_date or model.created_at)
            if new_data_count > 100:
                logger.info(f"Found {new_data_count} new data points for {model_type}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking retrain status for {model_type}: {e}")
            return False
    
    def _count_new_data_since(self, model_type: str, since_date: datetime) -> int:
        """Count new training data since last training"""
        try:
            if model_type == 'WATER_QUALITY_PREDICTION':
                return WaterQualityReading.objects.filter(timestamp__gte=since_date).count()
            elif model_type == 'DISEASE_DETECTION':
                # Count disease-related data (alerts, treatments, etc.)
                from alerts.models import Alert
                return Alert.objects.filter(created_at__gte=since_date, alert_type='disease').count()
            elif model_type == 'GROWTH_PREDICTION':
                from harvest.models import HarvestRecord
                return HarvestRecord.objects.filter(created_at__gte=since_date).count()
            return 0
        except Exception as e:
            logger.error(f"Error counting new data for {model_type}: {e}")
            return 0
    
    @shared_task
    def train_all_models_async(self):
        """Train all models asynchronously"""
        results = {}
        for model_type in self.model_configs.keys():
            if self.should_retrain_model(model_type):
                try:
                    result = self.train_model(model_type)
                    results[model_type] = result
                except Exception as e:
                    logger.error(f"Failed to train {model_type}: {e}")
                    results[model_type] = {'error': str(e)}
        return results
    
    def train_model(self, model_type: str) -> Dict[str, Any]:
        """Train a specific model type"""
        with self.training_lock:
            logger.info(f"Starting training for {model_type}")
            
            # Create training job record
            training_job = TrainingJob.objects.create(
                model_type=model_type,
                status='RUNNING',
                started_at=timezone.now()
            )
            
            try:
                if model_type == 'WATER_QUALITY_PREDICTION':
                    result = self._train_water_quality_model()
                elif model_type == 'DISEASE_DETECTION':
                    result = self._train_disease_model()
                elif model_type == 'GROWTH_PREDICTION':
                    result = self._train_growth_model()
                else:
                    raise ValueError(f"Unknown model type: {model_type}")
                
                # Update training job
                training_job.status = 'COMPLETED'
                training_job.completed_at = timezone.now()
                training_job.metrics = result.get('metrics', {})
                training_job.save()
                
                logger.info(f"Training completed for {model_type}")
                return result
                
            except Exception as e:
                training_job.status = 'FAILED'
                training_job.error_message = str(e)
                training_job.completed_at = timezone.now()
                training_job.save()
                
                logger.error(f"Training failed for {model_type}: {e}")
                raise
    
    def _train_water_quality_model(self) -> Dict[str, Any]:
        """Train water quality prediction model"""
        logger.info("Training water quality prediction model")
        
        # Get training data
        readings = WaterQualityReading.objects.select_related('pond').filter(
            timestamp__gte=timezone.now() - timedelta(days=90)
        ).order_by('timestamp')
        
        if len(readings) < 100:
            raise ValueError(f"Insufficient data for training (got {len(readings)}, need at least 100)")
        
        # Prepare dataset
        data = []
        for reading in readings:
            if reading.pond and all([reading.ph, reading.temperature, reading.dissolved_oxygen]):
                data.append({
                    'ph': reading.ph,
                    'temperature': reading.temperature,
                    'dissolved_oxygen': reading.dissolved_oxygen,
                    'salinity': reading.salinity or 0,
                    'turbidity': reading.turbidity or 0,
                    'pond_size': reading.pond.size,
                    'pond_depth': reading.pond.depth,
                    'hour': reading.timestamp.hour,
                    'day_of_week': reading.timestamp.weekday(),
                    'month': reading.timestamp.month,
                })
        
        if len(data) < 50:
            raise ValueError("Insufficient valid data after filtering")
        
        df = pd.DataFrame(data)
        
        # Prepare features and target
        feature_columns = ['temperature', 'dissolved_oxygen', 'salinity', 'turbidity', 
                          'pond_size', 'pond_depth', 'hour', 'day_of_week', 'month']
        target_column = 'ph'
        
        X = df[feature_columns].fillna(0)
        y = df[target_column]
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Hyperparameter tuning
        param_grid = {
            'n_estimators': [50, 100, 200],
            'max_depth': [10, 20, None],
            'min_samples_split': [2, 5, 10]
        }
        
        grid_search = GridSearchCV(
            RandomForestRegressor(random_state=42),
            param_grid,
            cv=5,
            scoring='r2',
            n_jobs=-1
        )
        
        grid_search.fit(X_train_scaled, y_train)
        best_model = grid_search.best_estimator_
        
        # Evaluate model
        y_pred = best_model.predict(X_test_scaled)
        mse = mean_squared_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        # Cross-validation
        cv_scores = cross_val_score(best_model, X_train_scaled, y_train, cv=5, scoring='r2')
        
        # Save model and scaler
        timestamp = int(timezone.now().timestamp())
        model_path = os.path.join(self.models_dir, f'water_quality_model_{timestamp}.joblib')
        scaler_path = os.path.join(self.models_dir, f'water_quality_scaler_{timestamp}.joblib')
        
        joblib.dump(best_model, model_path)
        joblib.dump(scaler, scaler_path)
        
        # Update database record
        # Deactivate old models
        MLModel.objects.filter(model_type='WATER_QUALITY_PREDICTION').update(status='inactive')
        
        ml_model = MLModel.objects.create(
            name=f'Water Quality Predictor v{timestamp}',
            model_type='WATER_QUALITY_PREDICTION',
            model_file_path=model_path,
            scaler_file_path=scaler_path,
            accuracy=r2,
            training_data_size=len(X_train),
            feature_columns=feature_columns,
            target_column=target_column,
            status='active',
            last_training_date=timezone.now(),
            hyperparameters=grid_search.best_params_,
            cross_validation_score=cv_scores.mean()
        )
        
        logger.info(f"Water quality model trained successfully with R² = {r2:.3f}")
        
        return {
            'model_type': 'WATER_QUALITY_PREDICTION',
            'model_id': ml_model.id,
            'metrics': {
                'mse': float(mse),
                'r2_score': float(r2),
                'cv_mean': float(cv_scores.mean()),
                'cv_std': float(cv_scores.std())
            },
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'best_params': grid_search.best_params_,
            'model_path': model_path
        }
    
    def _train_disease_model(self) -> Dict[str, Any]:
        """Train disease detection model (placeholder)"""
        # This would implement disease detection training
        # For now, return a mock result
        logger.info("Disease model training not yet implemented")
        return {
            'model_type': 'DISEASE_DETECTION',
            'status': 'not_implemented',
            'message': 'Disease detection model training will be implemented in next phase'
        }
    
    def _train_growth_model(self) -> Dict[str, Any]:
        """Train growth prediction model (placeholder)"""
        # This would implement growth prediction training
        # For now, return a mock result
        logger.info("Growth model training not yet implemented")
        return {
            'model_type': 'GROWTH_PREDICTION',
            'status': 'not_implemented',
            'message': 'Growth prediction model training will be implemented in next phase'
        }

# Global pipeline instance
training_pipeline = AutomatedTrainingPipeline()
