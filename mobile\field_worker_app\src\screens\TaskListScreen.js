/**
 * Task List Screen
 * Main screen showing all tasks with filtering and search capabilities
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  TextInput,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import TaskCard from '../components/TaskCard';
import TaskManager, { TaskStatus, TaskPriority, TaskType } from '../services/TaskManager';
import { useFocusEffect } from '@react-navigation/native';

const TaskListScreen = ({ navigation }) => {
  const [tasks, setTasks] = useState([]);
  const [filteredTasks, setFilteredTasks] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [syncStatus, setSyncStatus] = useState({});

  // Filter options
  const filterOptions = [
    { key: 'all', label: 'All Tasks', icon: 'list' },
    { key: 'today', label: 'Today', icon: 'today' },
    { key: 'pending', label: 'Pending', icon: 'pending' },
    { key: 'in_progress', label: 'In Progress', icon: 'play-circle' },
    { key: 'overdue', label: 'Overdue', icon: 'warning' },
    { key: 'completed', label: 'Completed', icon: 'check-circle' }
  ];

  // Load tasks when screen focuses
  useFocusEffect(
    useCallback(() => {
      loadTasks();
      loadSyncStatus();
    }, [])
  );

  // Filter tasks when search query or filter changes
  useEffect(() => {
    filterTasks();
  }, [tasks, searchQuery, selectedFilter]);

  const loadTasks = () => {
    try {
      const allTasks = TaskManager.getTasks();
      setTasks(allTasks);
    } catch (error) {
      console.error('Error loading tasks:', error);
      Alert.alert('Error', 'Failed to load tasks');
    }
  };

  const loadSyncStatus = () => {
    const status = TaskManager.getSyncStatus();
    setSyncStatus(status);
  };

  const filterTasks = () => {
    let filtered = [...tasks];

    // Apply status filter
    switch (selectedFilter) {
      case 'today':
        filtered = TaskManager.getTodaysTasks();
        break;
      case 'pending':
        filtered = filtered.filter(task => task.status === TaskStatus.PENDING);
        break;
      case 'in_progress':
        filtered = filtered.filter(task => task.status === TaskStatus.IN_PROGRESS);
        break;
      case 'overdue':
        filtered = TaskManager.getOverdueTasks();
        break;
      case 'completed':
        filtered = filtered.filter(task => task.status === TaskStatus.COMPLETED);
        break;
      default:
        // 'all' - no additional filtering
        break;
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(query) ||
        task.description?.toLowerCase().includes(query) ||
        task.pondName?.toLowerCase().includes(query)
      );
    }

    setFilteredTasks(filtered);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await TaskManager.forceSync();
      loadTasks();
      loadSyncStatus();
    } catch (error) {
      console.error('Error refreshing tasks:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleTaskPress = (task) => {
    navigation.navigate('TaskDetail', { taskId: task.id });
  };

  const handleStartTask = async (taskId) => {
    try {
      await TaskManager.startTask(taskId);
      loadTasks();
      loadSyncStatus();
    } catch (error) {
      console.error('Error starting task:', error);
      Alert.alert('Error', 'Failed to start task');
    }
  };

  const handleCompleteTask = (taskId) => {
    navigation.navigate('TaskCompletion', { taskId });
  };

  const handleCancelTask = async (taskId) => {
    try {
      await TaskManager.cancelTask(taskId, 'Cancelled by user');
      loadTasks();
      loadSyncStatus();
    } catch (error) {
      console.error('Error cancelling task:', error);
      Alert.alert('Error', 'Failed to cancel task');
    }
  };

  const renderTaskCard = ({ item }) => (
    <TaskCard
      task={item}
      onPress={handleTaskPress}
      onStart={handleStartTask}
      onComplete={handleCompleteTask}
      onCancel={handleCancelTask}
    />
  );

  const renderFilterButton = (option) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.filterButton,
        selectedFilter === option.key && styles.activeFilterButton
      ]}
      onPress={() => setSelectedFilter(option.key)}
    >
      <Icon 
        name={option.icon} 
        size={16} 
        color={selectedFilter === option.key ? '#fff' : '#666'} 
      />
      <Text style={[
        styles.filterButtonText,
        selectedFilter === option.key && styles.activeFilterButtonText
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search tasks..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => setSearchQuery('')}>
            <Icon name="clear" size={20} color="#666" />
          </TouchableOpacity>
        )}
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={filterOptions}
          renderItem={({ item }) => renderFilterButton(item)}
          keyExtractor={(item) => item.key}
          contentContainerStyle={styles.filterList}
        />
      </View>

      {/* Sync Status */}
      {!syncStatus.isOnline && (
        <View style={styles.offlineIndicator}>
          <Icon name="cloud-off" size={16} color="#fff" />
          <Text style={styles.offlineText}>Offline Mode</Text>
        </View>
      )}

      {syncStatus.pendingSync > 0 && (
        <View style={styles.syncIndicator}>
          <Icon name="sync" size={16} color="#fff" />
          <Text style={styles.syncText}>
            {syncStatus.pendingSync} tasks pending sync
          </Text>
        </View>
      )}
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon name="assignment" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>No Tasks Found</Text>
      <Text style={styles.emptyStateText}>
        {selectedFilter === 'all' 
          ? 'No tasks available at the moment'
          : `No ${selectedFilter} tasks found`
        }
      </Text>
      <TouchableOpacity 
        style={styles.refreshButton}
        onPress={handleRefresh}
      >
        <Icon name="refresh" size={20} color="#2196F3" />
        <Text style={styles.refreshButtonText}>Refresh</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={filteredTasks}
        renderItem={renderTaskCard}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#2196F3']}
          />
        }
        contentContainerStyle={filteredTasks.length === 0 ? styles.emptyContainer : null}
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('CreateTask')}
      >
        <Icon name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    paddingTop: 16,
    paddingBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
    borderRadius: 25,
    marginHorizontal: 16,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  filterContainer: {
    marginBottom: 8,
  },
  filterList: {
    paddingHorizontal: 16,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  activeFilterButton: {
    backgroundColor: '#2196F3',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    fontWeight: '500',
  },
  activeFilterButtonText: {
    color: '#fff',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF9800',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 4,
    marginBottom: 8,
  },
  offlineText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#2196F3',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginHorizontal: 16,
    borderRadius: 4,
    marginBottom: 8,
  },
  syncText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  emptyContainer: {
    flexGrow: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#2196F3',
  },
  refreshButtonText: {
    color: '#2196F3',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
});

export default TaskListScreen;
