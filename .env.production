# Production Environment Configuration
# Copy this file to .env and update values for production deployment

# Django Settings
DEBUG=False
SECRET_KEY=your-super-secret-production-key-here-change-this
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,your-server-ip

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/shrimp_farm_guardian
DB_PASSWORD=your-secure-database-password

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CACHE_URL=redis://localhost:6379/1

# API Rate Limiting
API_THROTTLE_ANON=100/hour
API_THROTTLE_USER=1000/hour
API_THROTTLE_CRITICAL=5/minute
API_PAGE_SIZE=20

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# AWS S3 Configuration (if using S3 for static/media files)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-cloudfront-domain.cloudfront.net

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=Shrimp Farm Guardian <<EMAIL>>

# Sentry Error Tracking
SENTRY_DSN=https://<EMAIL>/project-id

# Google Maps API
GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# IoT and External Services
MQTT_BROKER_HOST=localhost
MQTT_BROKER_PORT=1883
MQTT_USERNAME=mqtt-user
MQTT_PASSWORD=mqtt-password

# Monitoring and Logging
LOG_LEVEL=INFO
ENABLE_PROMETHEUS_METRICS=True
HEALTH_CHECK_ENABLED=True

# Performance Settings
CONN_MAX_AGE=60
DATABASE_POOL_SIZE=20
CACHE_TIMEOUT_DEFAULT=300

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
