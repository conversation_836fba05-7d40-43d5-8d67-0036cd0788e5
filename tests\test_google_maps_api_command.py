import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import json
from io import StringIO
from django.test import TestCase
from django.core.management import call_command

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the command to test
from core.management.commands.test_google_maps_api import Command


class TestGoogleMapsAPICommand(TestCase):
    """Test suite for the test_google_maps_api command."""

    def setUp(self):
        """Set up test environment."""
        # Sample API key results for mocking
        self.valid_key_result = {
            'key': 'AIzaSyValidTestKey12345678901234567890',
            'key_length': 39,
            'status': 'success',
            'message': 'API key is valid and working',
            'apis_enabled': ['Static Maps API', 'JavaScript Maps API'],
            'restrictions': {},
            'cached': False,
            'timestamp': **********
        }
        
        self.invalid_key_result = {
            'key': 'AIzaSyInvalidTestKey123456789012345678',
            'key_length': 39,
            'status': 'error',
            'message': 'API key is invalid',
            'apis_enabled': [],
            'restrictions': {},
            'cached': False,
            'timestamp': **********
        }
        
        # Sample environment check results
        self.env_match_result = {
            'settings_key': 'AIzaSyValidTestKey12345678901234567890',
            'env_key': 'AIzaSyValidTestKey12345678901234567890',
            'vite_key': 'AIzaSyValidTestKey12345678901234567890',
            'map_provider': 'google',
            'match': True,
            'message': 'All API keys match'
        }
        
        self.env_mismatch_result = {
            'settings_key': 'AIzaSyValidTestKey12345678901234567890',
            'env_key': 'AIzaSyDifferentKey12345678901234567890',
            'vite_key': 'AIzaSyValidTestKey12345678901234567890',
            'map_provider': 'google',
            'match': False,
            'message': 'API keys do not match'
        }

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_valid_key(self, mock_check_env, mock_check_key):
        """Test command with a valid API key."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command
        out = StringIO()
        call_command('test_google_maps_api', stdout=out)
        
        # Verify output contains success message
        output = out.getvalue()
        self.assertIn('✓ API key is valid', output)
        self.assertIn('APIs detected: Static Maps API, JavaScript Maps API', output)
        
        # Verify the check functions were called with default parameters
        mock_check_key.assert_called_once_with(api_key=None, use_cache=True, force_refresh=False)
        mock_check_env.assert_called_once()

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_invalid_key(self, mock_check_env, mock_check_key):
        """Test command with an invalid API key."""
        # Mock the API key check to return an error
        mock_check_key.return_value = self.invalid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command
        out = StringIO()
        # Use custom key option
        call_command('test_google_maps_api', key='invalid-key', stdout=out)
        
        # Verify output contains error message
        output = out.getvalue()
        self.assertIn('✗ Error: API key is invalid', output)
        
        # Verify the check functions were called with the custom key
        mock_check_key.assert_called_once_with(api_key='invalid-key', use_cache=True, force_refresh=False)

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_no_cache(self, mock_check_env, mock_check_key):
        """Test command with no_cache option."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with no_cache
        call_command('test_google_maps_api', no_cache=True)
        
        # Verify the check function was called with no_cache parameters
        mock_check_key.assert_called_once_with(api_key=None, use_cache=False, force_refresh=True)

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_cached_results(self, mock_check_env, mock_check_key):
        """Test command correctly shows when results are from cache."""
        # Mock the API key check to return cached results
        cached_result = self.valid_key_result.copy()
        cached_result['cached'] = True
        mock_check_key.return_value = cached_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command
        out = StringIO()
        call_command('test_google_maps_api', stdout=out)
        
        # Verify output indicates results are from cache
        output = out.getvalue()
        self.assertIn('(Results from cache)', output)

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_json_output(self, mock_check_env, mock_check_key):
        """Test command with JSON output format."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with json output
        out = StringIO()
        call_command('test_google_maps_api', json=True, stdout=out)
        
        # Verify output is valid JSON
        output = out.getvalue()
        result = json.loads(output)
        
        # Verify JSON structure
        self.assertIn('environment', result)
        self.assertIn('api_key_test', result)
        self.assertIn('timestamp', result)
        self.assertIn('cached', result)
        
        # Verify content
        self.assertEqual(result['api_key_test']['status'], 'success')
        self.assertEqual(result['environment']['match'], True)
        self.assertFalse(result['cached'])

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_verbose(self, mock_check_env, mock_check_key):
        """Test command with verbose output."""
        # Mock the API key check to return success
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with verbose
        out = StringIO()
        call_command('test_google_maps_api', verbose=True, stdout=out)
        
        # Verify verbose output includes additional details
        output = out.getvalue()
        self.assertIn('Additional Details:', output)
        self.assertIn('Key length: 39 characters', output)
        self.assertIn('Troubleshooting Tips:', output)
        self.assertIn('No issues detected. Your API key is working correctly.', output)

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    def test_command_with_mismatched_env(self, mock_check_env, mock_check_key):
        """Test command with mismatched environment settings."""
        # Mock the API key check to return success but environment mismatch
        mock_check_key.return_value = self.valid_key_result
        mock_check_env.return_value = self.env_mismatch_result
        
        # Call the command
        out = StringIO()
        call_command('test_google_maps_api', verbose=True, stdout=out)
        
        # Verify output includes environment mismatch warning
        output = out.getvalue()
        self.assertIn('✗ API keys do not match', output)
        self.assertIn('Django Settings:', output)  # Should show different keys in verbose mode

    @patch('core.management.commands.test_google_maps_api.check_api_key')
    @patch('core.management.commands.test_google_maps_api.check_environment')
    @patch('sys.exit')
    def test_command_exit_code_on_error(self, mock_exit, mock_check_env, mock_check_key):
        """Test command exits with non-zero code on error."""
        # Mock the API key check to return an error
        mock_check_key.return_value = self.invalid_key_result
        mock_check_env.return_value = self.env_match_result
        
        # Call the command with json to ensure the exit code is set
        call_command('test_google_maps_api', json=True)
        
        # Verify sys.exit was called with code 1
        mock_exit.assert_called_once_with(1)


if __name__ == '__main__':
    unittest.main()