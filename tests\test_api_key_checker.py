import unittest
from unittest.mock import patch, MagicMock
import os
import sys
import json
import requests
from django.conf import settings
from django.test import TestCase, override_settings

# Add project root to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import modules to test
from core.api_key_checker import check_api_key, check_environment
try:
    from core.api_key_cache import api_key_cache
    CACHE_AVAILABLE = True
except ImportError:
    CACHE_AVAILABLE = False


class MockResponse:
    """Mock response for requests to simulate API responses."""
    def __init__(self, status_code, text, headers=None):
        self.status_code = status_code
        self.text = text
        self.headers = headers or {}

    def json(self):
        return json.loads(self.text)


class TestAPIKeyChecker(TestCase):
    """Test suite for the Google Maps API key checker."""

    def setUp(self):
        """Set up test environment."""
        # Sample valid and invalid API keys for testing
        self.valid_api_key = "AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg"  # Public test key
        self.invalid_api_key = "AIzaInvalidKeyThatWillNotWork12345678"
        self.restricted_api_key = "AIzaSyRestrictedKeyWithDomainLimits123"

        # If cache is available, clear it before tests
        if CACHE_AVAILABLE:
            api_key_cache.clear()

    def tearDown(self):
        """Clean up after tests."""
        # If cache is available, clear it after tests
        if CACHE_AVAILABLE:
            api_key_cache.clear()

    @patch('requests.get')
    def test_valid_api_key(self, mock_get):
        """Test checking a valid API key."""
        # Mock successful response for Static Maps API
        mock_get.side_effect = [
            # Static Maps API response
            MockResponse(200, ''),
            # JavaScript Maps API response
            MockResponse(200, ''),
            # Places API response
            MockResponse(200, '{"candidates": [{"name": "Test Place"}]}'),
            # Geocoding API response
            MockResponse(200, '{"results": [{"formatted_address": "Test Address"}]}')
        ]

        # Test with cache disabled to ensure fresh check
        result = check_api_key(self.valid_api_key, use_cache=False)

        # Verify results
        self.assertEqual(result['status'], 'success')
        self.assertIn('API key is valid', result['message'])
        self.assertGreaterEqual(len(result['apis_enabled']), 2)  # At least two APIs should be detected
        self.assertIn('Static Maps API', result['apis_enabled'])
        self.assertIn('JavaScript Maps API', result['apis_enabled'])
        
        # Verify API calls were made
        self.assertEqual(mock_get.call_count, 4)  # Should call all 4 API endpoints

    @patch('requests.get')
    def test_invalid_api_key(self, mock_get):
        """Test checking an invalid API key."""
        # Mock error response for Static Maps API
        mock_get.return_value = MockResponse(400, 
            '{"error_message": "The provided API key is invalid.", "status": "REQUEST_DENIED"}')

        # Test with cache disabled to ensure fresh check
        result = check_api_key(self.invalid_api_key, use_cache=False)

        # Verify results
        self.assertEqual(result['status'], 'error')
        self.assertIn('invalid', result['message'].lower())
        self.assertEqual(len(result['apis_enabled']), 0)  # No APIs should be detected

    @patch('requests.get')
    def test_api_key_with_domain_restrictions(self, mock_get):
        """Test checking an API key with domain restrictions."""
        # Mock referer restriction response
        mock_get.return_value = MockResponse(400, 
            '{"error_message": "This API key is not authorized to use this service or API. '
            'This service requires the Google Maps JavaScript API. '
            'The provided API key is not authorized to use the Google Maps JavaScript API. '
            'Please check your API key configuration.", "status": "REQUEST_DENIED"}')

        # Test with cache disabled to ensure fresh check
        result = check_api_key(self.restricted_api_key, use_cache=False)

        # Verify results
        self.assertEqual(result['status'], 'error')
        self.assertIn('not authorized', result['message'])
        self.assertEqual(len(result['apis_enabled']), 0)  # No APIs should be detected
        
        # Restriction info should be detected
        self.assertIn('restrictions', result)

    @patch('requests.get')
    def test_api_key_with_billing_issues(self, mock_get):
        """Test checking an API key with billing issues."""
        # Mock billing issue response
        mock_get.return_value = MockResponse(400, 
            '{"error_message": "This API project is not authorized to use this API. '
            'Please ensure this API is activated in the Google Developers Console. '
            'This API requires billing to be enabled.", "status": "REQUEST_DENIED"}')

        # Test with cache disabled to ensure fresh check
        result = check_api_key(self.restricted_api_key, use_cache=False)

        # Verify results
        self.assertEqual(result['status'], 'error')
        self.assertIn('billing', result['message'].lower())
        self.assertEqual(len(result['apis_enabled']), 0)  # No APIs should be detected
        
        # Billing issue should be detected in restrictions
        self.assertIn('restrictions', result)
        self.assertIn('billing', str(result['restrictions']).lower())

    @patch('requests.get')
    def test_network_error(self, mock_get):
        """Test handling of network errors during API key check."""
        # Mock network error
        mock_get.side_effect = requests.exceptions.ConnectionError("Network error")

        # Test with cache disabled to ensure fresh check
        result = check_api_key(self.valid_api_key, use_cache=False)

        # Verify results
        self.assertEqual(result['status'], 'error')
        self.assertIn('network', result['message'].lower())
        self.assertEqual(len(result['apis_enabled']), 0)  # No APIs should be detected

    @unittest.skipIf(not CACHE_AVAILABLE, "Cache module not available")
    @patch('requests.get')
    def test_api_key_caching(self, mock_get):
        """Test that API key results are properly cached."""
        # Mock successful response for Static Maps API
        mock_get.side_effect = [
            # Static Maps API response
            MockResponse(200, ''),
            # JavaScript Maps API response
            MockResponse(200, ''),
            # Places API response
            MockResponse(200, '{"candidates": [{"name": "Test Place"}]}'),
            # Geocoding API response
            MockResponse(200, '{"results": [{"formatted_address": "Test Address"}]}')
        ]

        # First call should hit the API
        result1 = check_api_key(self.valid_api_key, use_cache=True)
        
        # Clear the mock to verify no more calls are made
        mock_get.reset_mock()
        
        # Second call should use cache
        result2 = check_api_key(self.valid_api_key, use_cache=True)
        
        # Verify results
        self.assertEqual(result1['status'], result2['status'])
        self.assertEqual(result1['message'], result2['message'])
        self.assertEqual(result1['apis_enabled'], result2['apis_enabled'])
        
        # Verify cache flag is set on second result
        self.assertTrue(result2.get('cached', False))
        
        # Verify no API calls were made on second check
        mock_get.assert_not_called()

    @unittest.skipIf(not CACHE_AVAILABLE, "Cache module not available")
    @patch('requests.get')
    def test_force_refresh_ignores_cache(self, mock_get):
        """Test that force_refresh option ignores cached results."""
        # Mock successful response for Static Maps API for both calls
        mock_get.side_effect = [
            # First call
            MockResponse(200, ''),
            MockResponse(200, ''),
            MockResponse(200, '{"candidates": [{"name": "Test Place"}]}'),
            MockResponse(200, '{"results": [{"formatted_address": "Test Address"}]}'),
            # Second call with force_refresh
            MockResponse(200, ''),
            MockResponse(200, ''),
            MockResponse(200, '{"candidates": [{"name": "Test Place"}]}'),
            MockResponse(200, '{"results": [{"formatted_address": "Test Address"}]}')
        ]

        # First call should hit the API and cache the result
        result1 = check_api_key(self.valid_api_key, use_cache=True)
        
        # Second call with force_refresh should hit the API again
        result2 = check_api_key(self.valid_api_key, use_cache=True, force_refresh=True)
        
        # Verify results
        self.assertEqual(result1['status'], result2['status'])
        self.assertEqual(result1['message'], result2['message'])
        
        # Verify cache flag is not set on second result
        self.assertFalse(result2.get('cached', False))
        
        # Verify API calls were made on both checks
        self.assertEqual(mock_get.call_count, 8)  # 4 calls for each check

    @override_settings(GOOGLE_MAPS_API_KEY='test_key_from_settings')
    def test_check_environment(self):
        """Test environment checking functionality."""
        # Mock environment variables
        with patch.dict(os.environ, {
            'GOOGLE_MAPS_API_KEY': 'test_key_from_env',
            'VITE_GOOGLE_MAPS_API_KEY': 'test_key_from_env',
            'MAP_PROVIDER': 'google'
        }):
            result = check_environment()
            
            # Verify results
            self.assertEqual(result['settings_key'], 'test_key_from_settings')
            self.assertEqual(result['env_key'], 'test_key_from_env')
            self.assertEqual(result['vite_key'], 'test_key_from_env')
            self.assertEqual(result['map_provider'], 'google')
            self.assertFalse(result['match'])  # Keys don't match between settings and env
            self.assertIn('mismatch', result['message'].lower())

    @override_settings(GOOGLE_MAPS_API_KEY='test_key_matching')
    def test_check_environment_matching_keys(self):
        """Test environment checking with matching keys."""
        # Mock environment variables with matching keys
        with patch.dict(os.environ, {
            'GOOGLE_MAPS_API_KEY': 'test_key_matching',
            'VITE_GOOGLE_MAPS_API_KEY': 'test_key_matching',
            'MAP_PROVIDER': 'google'
        }):
            result = check_environment()
            
            # Verify results
            self.assertEqual(result['settings_key'], 'test_key_matching')
            self.assertEqual(result['env_key'], 'test_key_matching')
            self.assertEqual(result['vite_key'], 'test_key_matching')
            self.assertEqual(result['map_provider'], 'google')
            self.assertTrue(result['match'])  # Keys match
            self.assertIn('match', result['message'].lower())

    def test_check_environment_missing_keys(self):
        """Test environment checking with missing keys."""
        # Mock environment variables with missing keys
        with patch.dict(os.environ, {}, clear=True):
            with override_settings(GOOGLE_MAPS_API_KEY=''):
                result = check_environment()
                
                # Verify results
                self.assertEqual(result['settings_key'], '')
                self.assertEqual(result['env_key'], '')
                self.assertEqual(result['vite_key'], '')
                self.assertEqual(result['map_provider'], '')
                self.assertTrue(result['match'])  # All keys are empty so they "match"
                self.assertIn('not set', result['message'].lower())


if __name__ == '__main__':
    unittest.main()