{% extends 'website/base.html' %}

{% block content %}
<!-- Blog Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Industry Insights
                </h1>
                <p class="lead mb-0" data-aos="fade-up" data-aos-delay="100">
                    Latest trends, research, and innovations in aquaculture technology
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 mb-5">
                <h2 class="display-5 fw-bold text-center" data-aos="fade-up">
                    Featured Articles
                </h2>
                <p class="lead text-center text-muted" data-aos="fade-up" data-aos-delay="100">
                    In-depth analysis and expert insights on aquaculture innovation
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            {% for article in featured_articles %}
            <div class="col-lg-6">
                <div class="feature-card h-100" data-aos="fade-up" data-aos-delay="{% if forloop.counter0 == 0 %}0{% elif forloop.counter0 == 1 %}100{% elif forloop.counter0 == 2 %}200{% elif forloop.counter0 == 3 %}300{% else %}400{% endif %}">
                    <div class="card-img-placeholder mb-3" style="height: 200px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-{{ article.icon }} text-white" style="font-size: 4rem;"></i>
                    </div>
                    
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-primary me-2">{{ article.category }}</span>
                        <small class="text-muted">{{ article.date }}</small>
                    </div>
                    
                    <h4 class="fw-bold mb-3">{{ article.title }}</h4>
                    <p class="text-muted mb-3">{{ article.excerpt }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="author-avatar me-2" style="width: 32px; height: 32px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user text-white" style="font-size: 0.8rem;"></i>
                            </div>
                            <small class="text-muted">{{ article.author }}</small>
                        </div>
                        <span class="text-muted">{{ article.read_time }} min read</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Latest Articles Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 mb-5">
                <h2 class="display-5 fw-bold text-center" data-aos="fade-up">
                    Latest Articles
                </h2>
            </div>
        </div>
        
        <div class="row g-4">
            {% for article in latest_articles %}
            <div class="col-lg-4 col-md-6">
                <div class="feature-card h-100" data-aos="fade-up" data-aos-delay="{% if forloop.counter0 == 0 %}0{% elif forloop.counter0 == 1 %}100{% elif forloop.counter0 == 2 %}200{% elif forloop.counter0 == 3 %}300{% elif forloop.counter0 == 4 %}400{% else %}500{% endif %}">
                    <div class="card-img-placeholder mb-3" style="height: 150px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-{{ article.icon }} text-white" style="font-size: 3rem;"></i>
                    </div>
                    
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-outline-primary me-2">{{ article.category }}</span>
                        <small class="text-muted">{{ article.date }}</small>
                    </div>
                    
                    <h5 class="fw-bold mb-2">{{ article.title }}</h5>
                    <p class="text-muted small mb-3">{{ article.excerpt }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center mt-auto">
                        <small class="text-muted">{{ article.author }}</small>
                        <span class="text-muted">{{ article.read_time }} min</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Categories Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 mb-5">
                <h2 class="display-5 fw-bold text-center" data-aos="fade-up">
                    Explore by Category
                </h2>
                <p class="lead text-center text-muted" data-aos="fade-up" data-aos-delay="100">
                    Discover content tailored to your interests
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            {% for category in categories %}
            <div class="col-lg-3 col-md-6">
                <div class="feature-card text-center h-100" data-aos="fade-up" data-aos-delay="{% if forloop.counter0 == 0 %}0{% elif forloop.counter0 == 1 %}100{% elif forloop.counter0 == 2 %}200{% else %}300{% endif %}">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-{{ category.icon }}"></i>
                    </div>
                    <h5 class="fw-bold mb-2">{{ category.name }}</h5>
                    <p class="text-muted small mb-3">{{ category.description }}</p>
                    <div class="mt-auto">
                        <span class="badge bg-light text-dark">{{ category.count }} articles</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div data-aos="fade-right">
                    <h2 class="display-5 fw-bold mb-3">
                        Stay Updated
                    </h2>
                    <p class="lead mb-0">
                        Get the latest insights and innovations delivered to your inbox
                    </p>
                </div>
            </div>
            <div class="col-lg-4">
                <div data-aos="fade-left">
                    <form class="d-flex gap-2">
                        <input type="email" class="form-control" placeholder="Enter your email" 
                               style="background: rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.2); color: white;">
                        <button type="submit" class="btn btn-light text-primary fw-bold px-4">
                            Subscribe
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Resources Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center">
                <h2 class="display-5 fw-bold mb-4" data-aos="fade-up">
                    Additional Resources
                </h2>
                
                <div class="row g-4 justify-content-center">
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up">
                            <a href="{% url 'website:docs' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-book me-2"></i>Documentation
                            </a>
                            <small class="text-muted d-block">Technical guides and API docs</small>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up" data-aos-delay="100">
                            <a href="{% url 'website:demo' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-play me-2"></i>Live Demo
                            </a>
                            <small class="text-muted d-block">Try our platform features</small>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div data-aos="fade-up" data-aos-delay="200">
                            <a href="{% url 'website:support' %}" class="btn btn-outline-gradient btn-lg w-100 mb-2">
                                <i class="fas fa-headset me-2"></i>Support
                            </a>
                            <small class="text-muted d-block">Get help from our experts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
