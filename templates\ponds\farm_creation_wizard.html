{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Farm - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
<style>
    .wizard-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .wizard-header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
    }

    .wizard-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .wizard-header p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .progress-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .progress-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        position: relative;
    }

    .progress-steps::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 3px;
        background: #e2e8f0;
        z-index: 1;
        transform: translateY(-50%);
    }

    .progress-step {
        background: #e2e8f0;
        color: #64748b;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.1rem;
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
        border: 3px solid #e2e8f0;
    }

    .progress-step.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .progress-step.completed {
        background: #10b981;
        color: white;
        border-color: #10b981;
    }

    .step-content {
        display: none;
        background: rgba(255, 255, 255, 0.98);
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .step-content.active {
        display: block;
        animation: fadeInUp 0.5s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .step-header {
        margin-bottom: 25px;
        text-align: center;
    }

    .step-header h2 {
        color: #2d3748;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .step-header p {
        color: #64748b;
        font-size: 1.1rem;
        margin-bottom: 0;
    }

    .address-search-block {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .search-block-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        color: #2d3748;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .search-input-container {
        display: flex;
        background: rgba(248, 250, 252, 0.8);
        border-radius: 12px;
        padding: 8px;
        border: 2px solid rgba(102, 126, 234, 0.1);
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .search-input-container:focus-within {
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .address-search-input {
        flex: 1;
        border: none;
        outline: none;
        padding: 14px 18px;
        font-size: 1rem;
        background: white;
        color: #2d3748;
        border-radius: 10px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }

    .address-search-input:focus {
        border-color: rgba(102, 126, 234, 0.5);
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .address-search-input::placeholder {
        color: #a0aec0;
        font-style: italic;
    }

    .search-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 14px 18px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        min-width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .my-location-search-btn {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        border: none;
        border-radius: 10px;
        padding: 14px 18px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
    }

    .my-location-search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(66, 133, 244, 0.4);
    }

    .my-location-search-btn:disabled,
    .search-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .map-container {
        position: relative;
        height: 500px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        margin-bottom: 25px;
    }

    .map-tools-top {
        position: absolute;
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        display: flex;
        gap: 10px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .map-tool-btn {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 10px;
        padding: 10px 16px;
        color: #4a5568;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 6px;
        white-space: nowrap;
    }

    .map-tool-btn:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    }

    .map-tool-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-color: #667eea;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .map-tool-btn i {
        font-size: 1rem;
    }

    .map-tool-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .map-tool-btn .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .measurement-display {
        position: absolute;
        top: 15px;
        right: 15px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.2);
        min-width: 200px;
        z-index: 1000;
    }

    .measurement-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .measurement-item:last-child {
        margin-bottom: 0;
    }

    .measurement-label {
        color: #64748b;
        font-weight: 500;
    }

    .measurement-value {
        color: #2d3748;
        font-weight: 600;
    }

    /* Pond Sub-Steps Styles */
    .pond-substeps {
        position: relative;
    }

    .pond-substep {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .pond-substep:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 20px;
        left: 100%;
        width: 80px;
        height: 3px;
        background: #e2e8f0;
        z-index: 1;
    }

    .pond-substep.completed::after {
        background: #10b981;
    }

    .pond-substep.active::after {
        background: linear-gradient(90deg, #10b981 50%, #e2e8f0 50%);
    }

    .substep-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #64748b;
        font-weight: bold;
        font-size: 1rem;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }

    .pond-substep.completed .substep-circle {
        background: #10b981;
        color: white;
    }

    .pond-substep.active .substep-circle {
        background: #667eea;
        color: white;
        transform: scale(1.1);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
    }

    .substep-title {
        color: #64748b;
        font-weight: 600;
        text-align: center;
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .pond-substep.active .substep-title,
    .pond-substep.completed .substep-title {
        opacity: 1;
        color: #2d3748;
    }

    .substep-content {
        display: none;
    }

    .substep-content.active {
        display: block;
        animation: fadeInUp 0.4s ease;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #2d3748;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .wizard-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30px;
        padding-top: 25px;
        border-top: 2px solid #e2e8f0;
    }

    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        text-decoration: none;
    }

    .btn-secondary {
        background: #e2e8f0;
        color: #64748b;
    }

    .btn-secondary:hover {
        background: #cbd5e0;
        transform: translateY(-2px);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }

    .btn-success {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .step-indicator {
        color: #64748b;
        font-weight: 500;
        font-size: 0.95rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .wizard-container {
            padding: 15px;
        }

        .wizard-header h1 {
            font-size: 2rem;
        }

        .progress-container {
            padding: 20px;
        }

        .step-content {
            padding: 20px;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .map-container {
            height: 400px;
        }

        .address-search-block {
            padding: 15px;
            margin-bottom: 15px;
        }

        .search-input-container {
            flex-direction: column;
            gap: 10px;
            padding: 10px;
        }

        .search-btn, .my-location-search-btn {
            width: 100%;
            justify-content: center;
        }

        .map-tools-top {
            left: 10px;
            right: 10px;
            top: 15px;
            transform: none;
            flex-wrap: wrap;
            justify-content: center;
        }

        .measurement-display {
            top: 80px;
            right: 10px;
            left: 10px;
            position: absolute;
            min-width: auto;
        }
    }
</style>
    </style>
</head>
<body>
<div class="wizard-container">
    <div class="wizard-header">
        <h1>🏢 Create New Farm</h1>
        <p>Advanced Farm Creation with Boundary Marking & Pond Management</p>
    </div>

    <!-- Progress Steps -->
    <div class="progress-container">
        <div class="progress-steps">
            <div class="progress-step active" data-step="1">1</div>
            <div class="progress-step" data-step="2">2</div>
        </div>
        <div class="step-indicator" id="step-indicator">Step 1 of 2</div>
    </div>

    <!-- Wizard Content -->
    <div class="wizard-content">
        <!-- Step 1: Farm Boundary Marking -->
        <div class="step-content active" id="step-1">
            <div class="step-header">
                <h2>🗺️ Step 1: Farm Boundary & Location</h2>
                <p>Search for your farm location and mark the farm boundaries on the map.</p>
                <div style="background: #f0f4ff; border: 1px solid #c7d2fe; border-radius: 8px; padding: 12px; margin: 15px 0; font-size: 0.9rem; color: #4338ca;">
                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                    <strong>Instructions:</strong> First search for your farm location, then click on the map to draw the farm boundary. The system will automatically calculate the total farm area.
                </div>

                <!-- Test Navigation Button -->
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 12px; margin: 15px 0; font-size: 0.9rem; color: #856404;">
                    <i class="fas fa-bug" style="margin-right: 8px;"></i>
                    <strong>Testing:</strong>
                    <button onclick="testNextStep()" style="background: #ffc107; color: #000; border: none; padding: 4px 8px; border-radius: 4px; margin-left: 8px; cursor: pointer;">
                        Test Next Step
                    </button>
                </div>
            </div>

            <!-- Address Search Block -->
            <div class="address-search-block">
                <div class="search-block-header">
                    <i class="fas fa-map-marker-alt" style="color: #667eea;"></i>
                    <span>Find Farm Location</span>
                </div>
                <div class="search-input-container">
                    <input type="text" id="address-search-input" placeholder="Search for farm address or location..." class="address-search-input">
                    <button class="search-btn" id="search-address-btn" onclick="event.preventDefault(); event.stopPropagation(); searchAddress();">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="my-location-search-btn" id="my-location-search-btn" onclick="event.preventDefault(); event.stopPropagation(); getMyLocationAddress();">
                        <i class="fas fa-crosshairs"></i> My Location
                    </button>
                </div>
            </div>

            <div class="map-container">
                <!-- Drawing Tools -->
                <div class="map-tools-top">
                    <button class="map-tool-btn active" id="draw-farm-btn" onclick="directDrawFarm()">
                        <i class="fas fa-draw-polygon"></i> Draw Farm
                    </button>
                    <button class="map-tool-btn" id="edit-farm-btn" onclick="directEditFarm()">
                        <i class="fas fa-edit"></i> Edit Shape
                    </button>
                    <button class="map-tool-btn" id="clear-farm-btn" onclick="directClearFarm()">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                    <button class="map-tool-btn" id="current-location-btn" onclick="getCurrentLocation()">
                        <i class="fas fa-crosshairs"></i> Center Map
                    </button>
                </div>

                <!-- Measurement Display -->
                <div class="measurement-display">
                    <div class="measurement-item">
                        <span class="measurement-label">Farm Area:</span>
                        <span class="measurement-value" id="farm-area-value">0 m²</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">Area (Hectares):</span>
                        <span class="measurement-value" id="farm-area-hectares" style="color: #667eea; font-weight: 700;">0 ha</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">Perimeter:</span>
                        <span class="measurement-value" id="farm-perimeter-value">0 m</span>
                    </div>
                </div>

                <!-- Map -->
                <div id="farm-map" style="width: 100%; height: 100%;"></div>
                <div id="map-loading" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1001; background: rgba(255,255,255,0.95); padding: 20px; border-radius: 10px; text-align: center; display: none;">
                    <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: #667eea; margin-bottom: 10px;"></i>
                    <p style="margin: 0; color: #64748b;">Loading Google Maps...</p>
                </div>
            </div>

            <!-- Farm Details Form -->
            <div class="form-grid">
                <div class="form-group">
                    <label for="farm-name">Farm Name *</label>
                    <input type="text" id="farm-name" class="form-control" placeholder="Enter farm name" required>
                </div>
                <div class="form-group">
                    <label for="farm-location">Location</label>
                    <input type="text" id="farm-location" class="form-control" placeholder="City, State, Country">
                </div>
                <div class="form-group">
                    <label for="contact-person">Contact Person</label>
                    <input type="text" id="contact-person" class="form-control" placeholder="Farm manager name">
                </div>
                <div class="form-group">
                    <label for="contact-phone">Contact Phone</label>
                    <input type="tel" id="contact-phone" class="form-control" placeholder="+****************">
                </div>
                <div class="form-group">
                    <label for="contact-email">Contact Email</label>
                    <input type="email" id="contact-email" class="form-control" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="farm-description">Description</label>
                    <textarea id="farm-description" class="form-control" rows="3" placeholder="Brief description of the farm"></textarea>
                </div>
            </div>
        </div>

        <!-- Step 2: Pond Creation -->
        <div class="step-content" id="step-2">
            <div class="step-header">
                <h2>🦐 Step 2: Create Ponds within Farm</h2>
                <p>Add ponds within your farm boundaries with automatic area calculations.</p>
                <div style="background: #f0fff4; border: 1px solid #86efac; border-radius: 8px; padding: 12px; margin: 15px 0; font-size: 0.9rem; color: #166534;">
                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                    <strong>Instructions:</strong> Click on the map to draw pond boundaries within your farm. Each pond will be automatically measured and can be configured individually.
                </div>
            </div>

            <!-- Map Area -->
            <div class="map-container" style="margin-bottom: 30px;">
                <!-- Pond Tools -->
                <div class="map-tools-top">
                    <button class="map-tool-btn active" id="draw-pond-btn">
                        <i class="fas fa-plus-circle"></i> Add Pond
                    </button>
                    <button class="map-tool-btn" id="edit-pond-btn">
                        <i class="fas fa-edit"></i> Edit Pond
                    </button>
                    <button class="map-tool-btn" id="delete-pond-btn">
                        <i class="fas fa-trash"></i> Delete Pond
                    </button>
                    <button class="map-tool-btn" id="clear-all-ponds-btn">
                        <i class="fas fa-broom"></i> Clear All
                    </button>
                </div>

                <!-- Farm & Pond Stats -->
                <div class="measurement-display">
                    <div class="measurement-item">
                        <span class="measurement-label">Farm Name:</span>
                        <span class="measurement-value" id="display-farm-name">-</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">Total Ponds:</span>
                        <span class="measurement-value" id="total-ponds-count" style="color: #10b981; font-weight: 700;">0</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">Total Pond Area:</span>
                        <span class="measurement-value" id="total-pond-area">0 m²</span>
                    </div>
                    <div class="measurement-item">
                        <span class="measurement-label">Farm Coverage:</span>
                        <span class="measurement-value" id="farm-coverage">0%</span>
                    </div>
                </div>

                <!-- Pond Map -->
                <div id="pond-map" style="width: 100%; height: 500px;"></div>
            </div>

            <!-- Comprehensive Pond Creation Section Below Map -->
            <div class="pond-creation-section" style="background: #f8fafc; border-radius: 15px; padding: 25px; margin-bottom: 20px;">
                <h3 style="color: #2d3748; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-cogs" style="color: #667eea;"></i>
                    Complete Pond Setup
                </h3>

                <!-- Pond Creation Sub-Steps -->
                <div class="pond-substeps" style="display: flex; justify-content: center; margin-bottom: 25px; gap: 15px;">
                    <div class="pond-substep active" data-substep="marking">
                        <div class="substep-circle">1</div>
                        <div class="substep-title">Pond Marking</div>
                    </div>
                    <div class="pond-substep" data-substep="aerators">
                        <div class="substep-circle">2</div>
                        <div class="substep-title">Aerator Setup</div>
                    </div>
                    <div class="pond-substep" data-substep="details">
                        <div class="substep-circle">3</div>
                        <div class="substep-title">Pond Details</div>
                    </div>
                </div>

                <!-- Sub-Step Content -->
                <div class="pond-substep-content">
                    <!-- Sub-Step 1: Pond Marking (Active by default) -->
                    <div class="substep-content active" id="substep-marking">
                        <div style="background: #e0f2fe; border: 1px solid #0284c7; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #0c4a6e;">
                                <i class="fas fa-map-marked-alt"></i> Step 1: Pond Boundary Marking
                            </h4>
                            <p style="margin: 0; color: #0c4a6e;">Draw pond boundaries within the farm area. Each pond will be automatically measured and numbered.</p>
                        </div>

                        <!-- Pond List -->
                        <div id="pond-list-detailed" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
                            <div class="empty-pond-state" style="text-align: center; color: #64748b; padding: 40px; grid-column: 1 / -1;">
                                <i class="fas fa-plus-circle" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                                <h4 style="margin: 0 0 10px 0;">No Ponds Created Yet</h4>
                                <p style="margin: 0;">Click "Add Pond" above the map to start drawing pond boundaries.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Sub-Step 2: Aerator Setup -->
                    <div class="substep-content" id="substep-aerators">
                        <div style="background: #f0fdf4; border: 1px solid #16a34a; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #166534;">
                                <i class="fas fa-cogs"></i> Step 2: Aerator Placement & Equipment
                            </h4>
                            <p style="margin: 0; color: #166534;">Add aerators to your ponds with specific equipment types and power specifications.</p>
                        </div>

                        <!-- Selected Pond for Aerator Setup -->
                        <div id="aerator-pond-selector" style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                Select Pond for Aerator Setup:
                            </label>
                            <select id="aerator-pond-select" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem;">
                                <option value="">No ponds available - Create ponds first</option>
                            </select>
                        </div>

                        <!-- Aerator Tools -->
                        <div style="display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">
                            <button class="btn btn-primary" id="add-aerator-btn" disabled>
                                <i class="fas fa-plus-circle"></i> Add Aerator
                            </button>
                            <button class="btn btn-secondary" id="auto-place-aerators-btn" disabled>
                                <i class="fas fa-magic"></i> Auto Place
                            </button>
                            <button class="btn btn-secondary" id="clear-aerators-btn" disabled>
                                <i class="fas fa-trash"></i> Clear All Aerators
                            </button>
                        </div>

                        <!-- Aerator Statistics -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-bottom: 20px;">
                            <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #e2e8f0;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #10b981;" id="aerator-count-display">0</div>
                                <div style="color: #64748b; font-size: 0.9rem;">Aerators</div>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #e2e8f0;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #3b82f6;" id="total-power-display">0 HP</div>
                                <div style="color: #64748b; font-size: 0.9rem;">Total Power</div>
                            </div>
                            <div style="background: white; padding: 15px; border-radius: 10px; text-align: center; border: 2px solid #e2e8f0;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #f59e0b;" id="coverage-display">0%</div>
                                <div style="color: #64748b; font-size: 0.9rem;">Coverage</div>
                            </div>
                        </div>

                        <!-- Aerator List -->
                        <div id="aerator-list-container">
                            <h5 style="margin-bottom: 15px; color: #2d3748;">
                                <i class="fas fa-list"></i> Aerators in Selected Pond
                            </h5>
                            <div id="aerator-list" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 15px;">
                                <div class="empty-aerator-state" style="text-align: center; color: #64748b; padding: 30px; grid-column: 1 / -1; background: white; border-radius: 10px; border: 2px dashed #e2e8f0;">
                                    <i class="fas fa-cog" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.3;"></i>
                                    <p style="margin: 0;">Select a pond and click "Add Aerator" to place equipment.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sub-Step 3: Pond Details -->
                    <div class="substep-content" id="substep-details">
                        <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 10px; padding: 15px; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0; color: #92400e;">
                                <i class="fas fa-clipboard-list"></i> Step 3: Pond Details & Specifications
                            </h4>
                            <p style="margin: 0; color: #92400e;">Configure species, stocking details, and operational parameters for each pond.</p>
                        </div>

                        <!-- Pond Details Form -->
                        <div id="pond-details-container">
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                    Select Pond for Configuration:
                                </label>
                                <select id="details-pond-select" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1rem;">
                                    <option value="">No ponds available - Create ponds first</option>
                                </select>
                            </div>

                            <div id="pond-details-form" style="display: none;">
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                    <!-- Species Selection -->
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                            Species *
                                        </label>
                                        <select id="pond-species" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                                            <option value="">Select species...</option>
                                            <option value="White Shrimp (L. vannamei)">White Shrimp (L. vannamei)</option>
                                            <option value="Black Tiger Shrimp (P. monodon)">Black Tiger Shrimp (P. monodon)</option>
                                            <option value="Blue Shrimp (L. stylirostris)">Blue Shrimp (L. stylirostris)</option>
                                            <option value="Indian White Shrimp (F. indicus)">Indian White Shrimp (F. indicus)</option>
                                        </select>
                                    </div>

                                    <!-- Stocking Density -->
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                            Stocking Density (per m²)
                                        </label>
                                        <input type="number" id="pond-density" placeholder="e.g., 15" min="1" max="50"
                                               style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                                    </div>

                                    <!-- Water Depth -->
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                            Water Depth (meters)
                                        </label>
                                        <input type="number" id="pond-depth" placeholder="e.g., 1.5" step="0.1" min="0.5" max="5"
                                               style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                                    </div>

                                    <!-- Pond Status -->
                                    <div>
                                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #2d3748;">
                                            Pond Status
                                        </label>
                                        <select id="pond-status" style="width: 100%; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px;">
                                            <option value="preparation">Preparation</option>
                                            <option value="ready">Ready for Stocking</option>
                                            <option value="active">Active</option>
                                            <option value="maintenance">Maintenance</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Save Pond Details Button -->
                                <div style="margin-top: 20px; text-align: center;">
                                    <button class="btn btn-primary" id="save-pond-details-btn">
                                        <i class="fas fa-save"></i> Save Pond Details
                                    </button>
                                </div>
                            </div>

                            <div id="no-pond-selected" style="text-align: center; color: #64748b; padding: 40px; background: white; border-radius: 10px; border: 2px dashed #e2e8f0;">
                                <i class="fas fa-clipboard-list" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.3;"></i>
                                <p style="margin: 0;">Select a pond to configure its details and specifications.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sub-Step Navigation -->
                <div style="display: flex; justify-content: center; gap: 10px; margin-top: 25px;">
                    <button class="btn btn-secondary" id="prev-substep-btn" style="display: none;">
                        <i class="fas fa-arrow-left"></i> Previous
                    </button>
                    <button class="btn btn-primary" id="next-substep-btn">
                        Next <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="wizard-navigation">
        <button class="btn btn-secondary" id="prev-btn" style="display: none;">
            <i class="fas fa-arrow-left"></i> Previous
        </button>
        <div class="step-indicator" id="step-indicator-bottom">Step 1 of 2</div>
        <button class="btn btn-primary" id="next-btn">
            Next <i class="fas fa-arrow-right"></i>
        </button>
    </div>
</div>

<script>
    // Farm Creation Wizard State
    let currentStep = 1;
    let farmPolygon = null;
    let pondPolygons = [];
    let farmMap = null;
    let pondMap = null;
    let farmDrawingManager = null;
    let pondDrawingManager = null;
    let geocoder = null;
    let placesService = null;

    // Direct onclick functions for debugging
    function directDrawFarm() {
        console.log('🖊️ DIRECT: Draw Farm button clicked');
        console.log('🔍 farmMap available:', !!farmMap);
        console.log('🔍 farmDrawingManager available:', !!farmDrawingManager);
        console.log('🔍 google.maps available:', !!google.maps);
        console.log('🔍 google.maps.drawing available:', !!google.maps.drawing);

        // Show status to user
        alert('Draw Farm clicked!\n' +
              'Map: ' + !!farmMap + '\n' +
              'Drawing Manager: ' + !!farmDrawingManager + '\n' +
              'Google Maps: ' + !!google.maps + '\n' +
              'Drawing Library: ' + !!google.maps.drawing);

        if (!farmMap) {
            alert('Map not ready. Please wait for the map to load.');
            return;
        }

        if (!google.maps.drawing) {
            alert('Drawing library not loaded. Please refresh the page.');
            return;
        }

        if (!farmDrawingManager) {
            console.log('🔄 Creating drawing manager...');
            try {
                farmDrawingManager = new google.maps.drawing.DrawingManager({
                    drawingMode: google.maps.drawing.OverlayType.POLYGON,
                    drawingControl: false,
                    polygonOptions: {
                        fillColor: '#667eea',
                        fillOpacity: 0.3,
                        strokeColor: '#667eea',
                        strokeWeight: 3,
                        clickable: true,
                        editable: true
                    }
                });
                farmDrawingManager.setMap(farmMap);
                console.log('✅ Drawing manager created');

                // Add polygon complete listener
                farmDrawingManager.addListener('polygoncomplete', function(polygon) {
                    console.log('🎯 Farm polygon completed');
                    if (farmPolygon) {
                        farmPolygon.setMap(null);
                    }
                    farmPolygon = polygon;
                    calculateFarmMeasurements();
                    farmDrawingManager.setDrawingMode(null);
                    setActiveMapTool('edit-farm-btn');
                    alert('Farm boundary created successfully!');
                });

            } catch (error) {
                console.error('❌ Failed to create drawing manager:', error);
                alert('Failed to create drawing manager: ' + error.message);
                return;
            }
        }

        console.log('✅ Setting drawing mode to POLYGON');
        farmDrawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
        setActiveMapTool('draw-farm-btn');
        alert('Draw mode activated! Click on the map to start drawing the farm boundary.');
    }

    function directEditFarm() {
        console.log('✏️ DIRECT: Edit Farm button clicked');
        if (farmDrawingManager) {
            farmDrawingManager.setDrawingMode(null);
            setActiveMapTool('edit-farm-btn');
            alert('Edit mode activated! You can now drag the corners of the farm boundary to modify it.');
        } else {
            alert('Drawing manager not available.');
        }
    }

    function directClearFarm() {
        console.log('🗑️ DIRECT: Clear Farm button clicked');
        clearFarmPolygon();
        alert('Farm boundary cleared!');
    }

    // Global callback function for Google Maps API
    window.initMap = function() {
        console.log('📍 Google Maps API loaded, initializing farm map...');

        // Hide loading message immediately
        const loadingElement = document.getElementById('map-loading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // Initialize the map
        initializeFarmMap();
    };

    // Initialize wizard
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🏢 Farm Creation Wizard initializing...');

        initializeWizard();
        setupEventListeners();

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            console.log('📍 Google Maps already loaded');
            initializeFarmMap();
        } else {
            console.log('📍 Waiting for Google Maps API callback...');
        }
    });

    function initializeWizard() {
        updateStepDisplay();
        updateButtons();
    }

    function initializeFarmMap() {
        try {
            console.log('🗺️ Initializing farm boundary map...');

            const mapElement = document.getElementById('farm-map');
            if (!mapElement) {
                console.error('❌ Farm map element not found');
                return;
            }

            // Create the map
            farmMap = new google.maps.Map(mapElement, {
                zoom: 15,
                center: { lat: 13.0827, lng: 80.2707 },
                mapTypeId: 'satellite',
                mapTypeControl: true,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true
            });

            console.log('✅ Farm map created successfully');

            // Initialize geocoder and places service
            geocoder = new google.maps.Geocoder();
            placesService = new google.maps.places.PlacesService(farmMap);
            console.log('✅ Geocoder and Places service initialized');

            // Check if drawing library is available
            if (!google.maps.drawing) {
                console.error('❌ Google Maps Drawing library not loaded');
                alert('Google Maps Drawing library not available. Please refresh the page.');
                return;
            }

            console.log('✅ Google Maps Drawing library available');

            // Setup drawing manager for farm boundary
            try {
                farmDrawingManager = new google.maps.drawing.DrawingManager({
                    drawingMode: google.maps.drawing.OverlayType.POLYGON,
                    drawingControl: false,
                    polygonOptions: {
                        fillColor: '#667eea',
                        fillOpacity: 0.3,
                        strokeColor: '#667eea',
                        strokeWeight: 3,
                        clickable: true,
                        editable: true
                    }
                });

                farmDrawingManager.setMap(farmMap);
                console.log('✅ Drawing manager created and set to map');
                console.log('🔍 farmDrawingManager type:', typeof farmDrawingManager);
                console.log('🔍 farmDrawingManager available:', !!farmDrawingManager);
            } catch (error) {
                console.error('❌ Error creating drawing manager:', error);
                alert('Failed to initialize drawing tools: ' + error.message);
                return;
            }

            // Handle farm polygon completion
            farmDrawingManager.addListener('polygoncomplete', function(polygon) {
                if (farmPolygon) {
                    farmPolygon.setMap(null);
                }
                farmPolygon = polygon;
                calculateFarmMeasurements();

                // Switch to edit mode
                farmDrawingManager.setDrawingMode(null);
                setActiveMapTool('edit-farm-btn');

                console.log('✅ Farm boundary created');
            });

            console.log('✅ Farm map initialization complete');

            // Setup event listeners now that drawing manager is ready
            setupMapEventListeners();

            // Hide loading message
            const loadingElement = document.getElementById('map-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            // Add map idle listener to ensure map is fully loaded
            google.maps.event.addListenerOnce(farmMap, 'idle', function() {
                console.log('✅ Farm map fully loaded and ready');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            });

        } catch (error) {
            console.error('❌ Error initializing farm map:', error);
        }
    }

    function calculateFarmMeasurements() {
        if (!farmPolygon) {
            document.getElementById('farm-area-value').textContent = '0 m²';
            document.getElementById('farm-area-hectares').textContent = '0 ha';
            document.getElementById('farm-perimeter-value').textContent = '0 m';
            return;
        }

        try {
            const path = farmPolygon.getPath();
            const area = google.maps.geometry.spherical.computeArea(path);
            const perimeter = google.maps.geometry.spherical.computeLength(path);

            document.getElementById('farm-area-value').textContent = Math.round(area).toLocaleString() + ' m²';
            document.getElementById('farm-area-hectares').textContent = (area / 10000).toFixed(2) + ' ha';
            document.getElementById('farm-perimeter-value').textContent = Math.round(perimeter).toLocaleString() + ' m';

            console.log(`📏 Farm measurements: ${Math.round(area)} m², ${Math.round(perimeter)} m perimeter`);
        } catch (error) {
            console.error('❌ Error calculating farm measurements:', error);
        }
    }

    function setupEventListeners() {
        console.log('🔧 Setting up basic event listeners...');

        // Navigation buttons
        document.getElementById('next-btn').addEventListener('click', nextStep);
        document.getElementById('prev-btn').addEventListener('click', prevStep);

        // Address search Enter key support
        document.getElementById('address-search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchAddress();
            }
        });

        console.log('✅ Basic event listeners set up');
    }

    function setupMapEventListeners() {
        console.log('🗺️ Setting up map event listeners...');

        // Farm map tools
        document.getElementById('draw-farm-btn').addEventListener('click', activateDrawMode);
        document.getElementById('edit-farm-btn').addEventListener('click', activateEditMode);
        document.getElementById('clear-farm-btn').addEventListener('click', clearFarmPolygon);

        console.log('✅ Map event listeners set up');
    }

    function setActiveMapTool(activeId) {
        document.querySelectorAll('.map-tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(activeId).classList.add('active');
    }

    function clearFarmPolygon() {
        console.log('🗑️ Clearing farm polygon...');
        if (farmPolygon) {
            farmPolygon.setMap(null);
            farmPolygon = null;
            calculateFarmMeasurements();
            console.log('✅ Farm polygon cleared');
        }

        // Reset drawing mode
        if (farmDrawingManager) {
            farmDrawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
            setActiveMapTool('draw-farm-btn');
        }
    }

    function getCurrentLocation() {
        console.log('📍 Getting current location...');

        if (!navigator.geolocation) {
            alert('❌ Geolocation is not supported by this browser.\n\nPlease use a modern browser that supports GPS location.');
            return;
        }

        // Show permission request info
        const permissionInfo = confirm(
            '📍 Location Access Required\n\n' +
            'This will help center the map on your current location for easier farm marking.\n\n' +
            '• Click "OK" to allow location access\n' +
            '• Click "Cancel" to skip\n\n' +
            'Your location data is only used locally and not stored.'
        );

        if (!permissionInfo) {
            return;
        }

        // Show loading state
        const btn = document.getElementById('current-location-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Locating...';
        btn.disabled = true;

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;

                console.log(`📍 Current location: ${lat}, ${lng} (accuracy: ${accuracy}m)`);

                if (farmMap) {
                    // Center map on current location
                    const currentLocation = new google.maps.LatLng(lat, lng);
                    farmMap.setCenter(currentLocation);
                    farmMap.setZoom(18);

                    // Add a marker for current location (using default marker)
                    const currentLocationMarker = new google.maps.Marker({
                        position: currentLocation,
                        map: farmMap,
                        title: 'Your Current Location'
                        // Using default red marker - no custom icon
                    });

                    // Remove marker after 5 seconds
                    setTimeout(() => {
                        currentLocationMarker.setMap(null);
                    }, 5000);

                    // Show info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px; text-align: center;">
                                <h4 style="margin: 0 0 8px 0; color: #4285f4;">📍 Your Location</h4>
                                <p style="margin: 0; font-size: 0.9rem;">
                                    <strong>Coordinates:</strong><br>
                                    ${lat.toFixed(6)}, ${lng.toFixed(6)}
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #666;">
                                    Accuracy: ±${Math.round(accuracy)}m
                                </p>
                            </div>
                        `
                    });

                    infoWindow.open(farmMap, currentLocationMarker);
                    setTimeout(() => {
                        infoWindow.close();
                    }, 4000);
                }

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
            },
            function(error) {
                console.error('❌ Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access and try again.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information is unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                alert(errorMessage);

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    function nextStep() {
        console.log(`🔄 Next button clicked - current step: ${currentStep}`);

        if (validateCurrentStep()) {
            if (currentStep < 2) {
                currentStep++;
                console.log(`✅ Advanced to step ${currentStep}`);
                updateStepDisplay();
                updateButtons();

                if (currentStep === 2) {
                    console.log('🦐 Initializing pond creation step...');
                    setTimeout(() => {
                        initializePondMap();
                    }, 100);
                }
            } else {
                console.log('🏁 Submitting farm...');
                submitFarm();
            }
        } else {
            console.log('❌ Step validation failed');
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
            updateButtons();
        }
    }

    function validateCurrentStep() {
        console.log(`🔍 Validating step ${currentStep}`);

        switch (currentStep) {
            case 1:
                const farmName = document.getElementById('farm-name').value.trim();
                console.log(`📝 Farm name: "${farmName}"`);
                console.log(`🗺️ Farm polygon exists: ${!!farmPolygon}`);

                if (!farmName) {
                    alert('❌ Please enter a farm name before proceeding to the next step.');
                    document.getElementById('farm-name').focus();
                    return false;
                }
                if (!farmPolygon) {
                    alert('❌ Please draw the farm boundary on the map before proceeding.\n\nClick "Draw Farm" and then click on the map to create the farm boundary.');
                    return false;
                }
                console.log('✅ Step 1 validation passed');
                return true;
            case 2:
                console.log('✅ Step 2 validation passed (ponds are optional)');
                return true; // Ponds are optional
        }
        return true;
    }

    function updateStepDisplay() {
        // Update progress steps
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNum = index + 1;
            step.classList.remove('active', 'completed');

            if (stepNum < currentStep) {
                step.classList.add('completed');
            } else if (stepNum === currentStep) {
                step.classList.add('active');
            }
        });

        // Update content
        document.querySelectorAll('.step-content').forEach((content, index) => {
            content.classList.remove('active');
            if (index + 1 === currentStep) {
                content.classList.add('active');
            }
        });

        // Update step indicators
        document.getElementById('step-indicator').textContent = `Step ${currentStep} of 2`;
        document.getElementById('step-indicator-bottom').textContent = `Step ${currentStep} of 2`;
    }

    function updateButtons() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');

        prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';

        if (currentStep === 2) {
            nextBtn.innerHTML = '<i class="fas fa-check"></i> Create Farm';
            nextBtn.classList.add('btn-success');
            nextBtn.classList.remove('btn-primary');
        } else {
            nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right"></i>';
            nextBtn.classList.remove('btn-success');
            nextBtn.classList.add('btn-primary');
        }
    }

    function initializePondMap() {
        console.log('🦐 Initializing pond creation map...');

        try {
            const pondMapElement = document.getElementById('pond-map');
            if (!pondMapElement) {
                console.error('❌ Pond map element not found');
                return;
            }

            // Copy farm boundary to pond map
            pondMap = new google.maps.Map(pondMapElement, {
                zoom: 16,
                center: farmMap.getCenter(),
                mapTypeId: 'satellite',
                mapTypeControl: true,
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true
            });

            console.log('✅ Pond map created successfully');

            if (farmPolygon) {
                const farmBounds = new google.maps.LatLngBounds();
                farmPolygon.getPath().forEach(point => farmBounds.extend(point));

                // Create farm outline on pond map
                const farmOutline = new google.maps.Polygon({
                    paths: farmPolygon.getPath().getArray(),
                    strokeColor: '#667eea',
                    strokeOpacity: 0.8,
                    strokeWeight: 3,
                    fillColor: '#667eea',
                    fillOpacity: 0.1,
                    map: pondMap
                });

                // Add farm name label
                const farmCenter = farmBounds.getCenter();
                const farmLabel = new google.maps.InfoWindow({
                    content: `<div style="text-align: center; padding: 5px;"><strong>🏢 ${document.getElementById('farm-name').value}</strong></div>`,
                    position: farmCenter
                });
                farmLabel.open(pondMap);

                pondMap.fitBounds(farmBounds);
                console.log('✅ Farm boundary displayed on pond map');
            }

            // Setup pond drawing manager
            pondDrawingManager = new google.maps.drawing.DrawingManager({
                drawingMode: google.maps.drawing.OverlayType.POLYGON,
                drawingControl: false,
                polygonOptions: {
                    fillColor: '#10b981',
                    fillOpacity: 0.4,
                    strokeColor: '#10b981',
                    strokeWeight: 2,
                    clickable: true,
                    editable: true
                }
            });

            pondDrawingManager.setMap(pondMap);
            console.log('✅ Pond drawing manager initialized');

            // Handle pond polygon completion
            pondDrawingManager.addListener('polygoncomplete', function(polygon) {
                console.log('🦐 New pond polygon completed');

                // Check if pond is within farm boundary
                if (farmPolygon && !isPondWithinFarm(polygon, farmPolygon)) {
                    alert('❌ Pond must be created within the farm boundary.\n\nPlease draw the pond inside the blue farm area.');
                    polygon.setMap(null);
                    return;
                }

                const pondNumber = pondPolygons.length + 1;
                const pondId = 'pond-' + pondNumber;
                const pondData = {
                    id: pondId,
                    polygon: polygon,
                    name: `Pond ${pondNumber}`,
                    species: '',
                    depth: 1.5,
                    density: '',
                    status: 'preparation'
                };

                pondPolygons.push(pondData);

                // Add pond label
                const pondCenter = getBounds(polygon).getCenter();
                const pondLabel = new google.maps.InfoWindow({
                    content: `<div style="text-align: center; padding: 3px; font-size: 0.9rem;"><strong>🦐 ${pondData.name}</strong></div>`,
                    position: pondCenter
                });
                pondLabel.open(pondMap);

                updatePondDisplay();
                addPondToList(pondData);

                // Switch to edit mode after creating pond
                pondDrawingManager.setDrawingMode(null);
                setActivePondTool('edit-pond-btn');

                console.log(`✅ Pond ${pondNumber} created successfully`);

                // Show success message
                showPondCreatedMessage(pondData);
            });

            // Update farm name display
            const farmName = document.getElementById('farm-name').value || 'Unnamed Farm';
            document.getElementById('display-farm-name').textContent = farmName;

            setupPondEventListeners();

        } catch (error) {
            console.error('❌ Error initializing pond map:', error);
        }
    }

    function isPondWithinFarm(pondPolygon, farmPolygon) {
        try {
            const pondPath = pondPolygon.getPath();
            const farmPath = farmPolygon.getPath();

            // Check if all pond vertices are within farm boundary
            for (let i = 0; i < pondPath.getLength(); i++) {
                const point = pondPath.getAt(i);
                if (!google.maps.geometry.poly.containsLocation(point, farmPolygon)) {
                    return false;
                }
            }
            return true;
        } catch (error) {
            console.error('Error checking pond boundary:', error);
            return true; // Allow if check fails
        }
    }

    function getBounds(polygon) {
        const bounds = new google.maps.LatLngBounds();
        polygon.getPath().forEach(point => bounds.extend(point));
        return bounds;
    }

    function showPondCreatedMessage(pondData) {
        const area = google.maps.geometry.spherical.computeArea(pondData.polygon.getPath());
        const message = `🎉 Pond Created Successfully!\n\n` +
                       `🦐 Name: ${pondData.name}\n` +
                       `📏 Area: ${Math.round(area).toLocaleString()} m² (${(area/10000).toFixed(2)} ha)\n\n` +
                       `You can now:\n` +
                       `• Create another pond\n` +
                       `• Edit pond details\n` +
                       `• Finish farm creation`;

        // Use a non-blocking notification
        setTimeout(() => {
            if (confirm(message + '\n\nWould you like to create another pond?')) {
                pondDrawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                setActivePondTool('draw-pond-btn');
            }
        }, 500);
    }

    function setupPondEventListeners() {
        document.getElementById('draw-pond-btn').addEventListener('click', () => {
            console.log('🖊️ Activating pond drawing mode');
            pondDrawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
            setActivePondTool('draw-pond-btn');
        });

        document.getElementById('edit-pond-btn').addEventListener('click', () => {
            console.log('✏️ Activating pond edit mode');
            pondDrawingManager.setDrawingMode(null);
            setActivePondTool('edit-pond-btn');
        });

        document.getElementById('delete-pond-btn').addEventListener('click', () => {
            console.log('🗑️ Activating pond delete mode');
            if (pondPolygons.length === 0) {
                alert('No ponds to delete.');
                return;
            }
            alert('Click on a pond to delete it.');
            // Add click listeners to ponds for deletion
            pondPolygons.forEach(pond => {
                google.maps.event.addListenerOnce(pond.polygon, 'click', () => {
                    deletePond(pond.id);
                });
            });
        });

        document.getElementById('clear-all-ponds-btn').addEventListener('click', () => {
            clearAllPonds();
        });
    }

    function setActivePondTool(activeId) {
        document.querySelectorAll('#step-2 .map-tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(activeId).classList.add('active');
    }

    function updatePondDisplay() {
        const totalPonds = pondPolygons.length;
        let totalPondArea = 0;

        pondPolygons.forEach(pond => {
            if (pond.polygon) {
                const area = google.maps.geometry.spherical.computeArea(pond.polygon.getPath());
                totalPondArea += area;
            }
        });

        document.getElementById('total-ponds-count').textContent = totalPonds;
        document.getElementById('total-pond-area').textContent = Math.round(totalPondArea).toLocaleString() + ' m²';

        // Calculate farm coverage
        if (farmPolygon) {
            const farmArea = google.maps.geometry.spherical.computeArea(farmPolygon.getPath());
            const coverage = (totalPondArea / farmArea * 100).toFixed(1);
            document.getElementById('farm-coverage').textContent = coverage + '%';
        }
    }

    function addPondToList(pondData) {
        const pondList = document.getElementById('pond-list');

        // Clear empty message if this is the first pond
        if (pondPolygons.length === 1) {
            pondList.innerHTML = '';
        }

        const area = google.maps.geometry.spherical.computeArea(pondData.polygon.getPath());
        const pondDiv = document.createElement('div');
        pondDiv.className = 'pond-item';
        pondDiv.id = `pond-item-${pondData.id}`;
        pondDiv.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        `;

        pondDiv.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                <h4 style="margin: 0; color: #2d3748; font-size: 1.1rem; font-weight: 600;">🦐 ${pondData.name}</h4>
                <button onclick="deletePond('${pondData.id}')" style="
                    background: #ef4444;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 6px 10px;
                    cursor: pointer;
                    font-size: 0.8rem;
                    font-weight: 600;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='#dc2626'" onmouseout="this.style.background='#ef4444'">
                    <i class="fas fa-trash"></i>
                </button>
            </div>

            <div style="background: #f8fafc; border-radius: 8px; padding: 12px; margin-bottom: 12px;">
                <div style="font-size: 0.9rem; color: #64748b; margin-bottom: 6px;">
                    📏 <strong>Area:</strong> ${Math.round(area).toLocaleString()} m² (${(area/10000).toFixed(2)} ha)
                </div>
                <div style="font-size: 0.9rem; color: #64748b;">
                    📍 <strong>Status:</strong> <span style="color: #10b981; font-weight: 600;">${pondData.status}</span>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 12px;">
                <div>
                    <label style="font-size: 0.8rem; color: #64748b; font-weight: 600;">Pond Name</label>
                    <input type="text" value="${pondData.name}"
                           onchange="updatePondName('${pondData.id}', this.value)"
                           style="width: 100%; padding: 6px 8px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 0.9rem; margin-top: 2px;">
                </div>
                <div>
                    <label style="font-size: 0.8rem; color: #64748b; font-weight: 600;">Depth (m)</label>
                    <input type="number" value="${pondData.depth}" step="0.1" min="0.5" max="5"
                           onchange="updatePondDepth('${pondData.id}', this.value)"
                           style="width: 100%; padding: 6px 8px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 0.9rem; margin-top: 2px;">
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                <div>
                    <label style="font-size: 0.8rem; color: #64748b; font-weight: 600;">Species</label>
                    <select onchange="updatePondSpecies('${pondData.id}', this.value)"
                            style="width: 100%; padding: 6px 8px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 0.9rem; margin-top: 2px;">
                        <option value="">Select species</option>
                        <option value="vannamei" ${pondData.species === 'vannamei' ? 'selected' : ''}>Vannamei</option>
                        <option value="tiger_prawn" ${pondData.species === 'tiger_prawn' ? 'selected' : ''}>Tiger Prawn</option>
                        <option value="black_tiger" ${pondData.species === 'black_tiger' ? 'selected' : ''}>Black Tiger</option>
                        <option value="other" ${pondData.species === 'other' ? 'selected' : ''}>Other</option>
                    </select>
                </div>
                <div>
                    <label style="font-size: 0.8rem; color: #64748b; font-weight: 600;">Density (per m²)</label>
                    <input type="number" value="${pondData.density}" min="1" max="100"
                           onchange="updatePondDensity('${pondData.id}', this.value)"
                           style="width: 100%; padding: 6px 8px; border: 1px solid #e2e8f0; border-radius: 6px; font-size: 0.9rem; margin-top: 2px;">
                </div>
            </div>
        `;

        pondList.appendChild(pondDiv);

        // Add hover effect
        pondDiv.addEventListener('mouseenter', () => {
            pondDiv.style.borderColor = '#10b981';
            pondDiv.style.transform = 'translateY(-2px)';
            pondDiv.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.2)';
        });

        pondDiv.addEventListener('mouseleave', () => {
            pondDiv.style.borderColor = '#e2e8f0';
            pondDiv.style.transform = 'translateY(0)';
            pondDiv.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });
    }

    function updatePondName(pondId, newName) {
        const pond = pondPolygons.find(p => p.id === pondId);
        if (pond) {
            pond.name = newName;
            console.log(`📝 Updated pond ${pondId} name to: ${newName}`);
        }
    }

    function updatePondDepth(pondId, newDepth) {
        const pond = pondPolygons.find(p => p.id === pondId);
        if (pond) {
            pond.depth = parseFloat(newDepth);
            console.log(`📏 Updated pond ${pondId} depth to: ${newDepth}m`);
        }
    }

    function updatePondSpecies(pondId, newSpecies) {
        const pond = pondPolygons.find(p => p.id === pondId);
        if (pond) {
            pond.species = newSpecies;
            console.log(`🦐 Updated pond ${pondId} species to: ${newSpecies}`);
        }
    }

    function updatePondDensity(pondId, newDensity) {
        const pond = pondPolygons.find(p => p.id === pondId);
        if (pond) {
            pond.density = newDensity;
            console.log(`📊 Updated pond ${pondId} density to: ${newDensity} per m²`);
        }
    }

    function deletePond(pondId) {
        console.log(`🗑️ Deleting pond: ${pondId}`);

        const pondIndex = pondPolygons.findIndex(p => p.id === pondId);
        if (pondIndex === -1) {
            console.error('Pond not found:', pondId);
            return;
        }

        const pond = pondPolygons[pondIndex];
        const confirmDelete = confirm(`🗑️ Delete ${pond.name}?\n\nThis action cannot be undone.`);

        if (confirmDelete) {
            // Remove from map
            pond.polygon.setMap(null);

            // Remove from array
            pondPolygons.splice(pondIndex, 1);

            // Remove from list
            const pondElement = document.getElementById(`pond-item-${pondId}`);
            if (pondElement) {
                pondElement.remove();
            }

            // Update display
            updatePondDisplay();

            // Show empty message if no ponds left
            if (pondPolygons.length === 0) {
                document.getElementById('pond-list').innerHTML = `
                    <div style="text-align: center; color: #64748b; padding: 20px;">
                        <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;"></i>
                        <p>No ponds created yet.<br>Start by drawing pond boundaries on the map.</p>
                    </div>
                `;
            }

            console.log(`✅ Pond ${pondId} deleted successfully`);
        }
    }

    function clearAllPonds() {
        if (pondPolygons.length === 0) return;

        if (confirm('Are you sure you want to remove all ponds?')) {
            pondPolygons.forEach(pond => {
                if (pond.polygon) {
                    pond.polygon.setMap(null);
                }
            });
            pondPolygons = [];

            document.getElementById('pond-list').innerHTML = `
                <div style="text-align: center; color: #64748b; padding: 20px;">
                    <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.5;"></i>
                    <p>No ponds created yet.<br>Start by drawing pond boundaries on the map.</p>
                </div>
            `;

            updatePondDisplay();
            console.log('🗑️ All ponds cleared');
        }
    }

    function submitFarm() {
        console.log('🏁 Starting farm submission...');

        // Show loading state
        const submitBtn = document.getElementById('next-btn');
        const originalContent = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Farm...';
        submitBtn.disabled = true;

        // Collect farm data
        const farmData = {
            name: document.getElementById('farm-name').value,
            location: document.getElementById('farm-location').value,
            contact_person: document.getElementById('contact-person').value,
            contact_phone: document.getElementById('contact-phone').value,
            contact_email: document.getElementById('contact-email').value,
            description: document.getElementById('farm-description').value,
            address: document.getElementById('address-search-input').value,
            polygon: farmPolygon ? farmPolygon.getPath().getArray().map(point => ({
                lat: point.lat(),
                lng: point.lng()
            })) : null,
            ponds: pondPolygons.map(pond => ({
                name: pond.name,
                species: pond.species,
                depth: pond.depth,
                density: pond.density,
                status: pond.status,
                polygon: pond.polygon ? pond.polygon.getPath().getArray().map(point => ({
                    lat: point.lat(),
                    lng: point.lng()
                })) : null
            }))
        };

        console.log('📊 Farm data prepared:', {
            farmName: farmData.name,
            pondsCount: farmData.ponds.length,
            hasBoundary: !!farmData.polygon
        });

        // Submit to server
        fetch('{% url "ponds:farm_create_wizard" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(farmData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('📨 Server response:', data);

            if (data.success) {
                console.log('✅ Farm created successfully');

                // Show detailed success message
                const successMessage = `🎉 Farm Created Successfully!\n\n` +
                    `🏢 Farm Name: ${data.farm_name}\n` +
                    `📏 Farm Area: ${(data.farm_area/10000).toFixed(2)} hectares\n` +
                    `🦐 Ponds Created: ${data.ponds_created}\n` +
                    `👤 Created By: ${data.created_by}\n` +
                    `📅 Created: ${new Date(data.created_at).toLocaleString()}\n\n` +
                    `What would you like to do next?`;

                // Show success with options
                setTimeout(() => {
                    const choice = confirm(
                        successMessage + '\n\n' +
                        '• Click "OK" to view farm details\n' +
                        '• Click "Cancel" to view all farms'
                    );

                    if (choice) {
                        // Redirect to farm details
                        console.log('🔄 Redirecting to farm details...');
                        window.location.href = data.farm_detail_url || `/ponds/farm/${data.farm_id}/`;
                    } else {
                        // Redirect to farms list
                        console.log('🔄 Redirecting to farms list...');
                        window.location.href = data.redirect_url || '/ponds/farms/';
                    }
                }, 500);

            } else {
                console.error('❌ Farm creation failed:', data.error);
                alert('❌ Error creating farm: ' + data.error);

                // Reset button
                submitBtn.innerHTML = originalContent;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            console.error('❌ Network error:', error);
            alert('❌ Network error creating farm. Please check your connection and try again.');

            // Reset button
            submitBtn.innerHTML = originalContent;
            submitBtn.disabled = false;
        });
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Address search functions
    function searchAddress() {
        const addressInput = document.getElementById('address-search-input');
        const address = addressInput.value.trim();

        if (!address) {
            alert('Please enter an address to search');
            return;
        }

        if (!geocoder) {
            alert('Geocoding service not available');
            return;
        }

        console.log(`🔍 Searching for address: ${address}`);

        // Show loading state
        const searchBtn = document.getElementById('search-address-btn');
        const originalContent = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchBtn.disabled = true;

        geocoder.geocode({ address: address }, function(results, status) {
            if (status === 'OK' && results[0]) {
                const location = results[0].geometry.location;
                const formattedAddress = results[0].formatted_address;

                console.log(`✅ Address found: ${formattedAddress}`);

                // Center map on the found location
                farmMap.setCenter(location);
                farmMap.setZoom(16);

                // Add a marker for the searched location (using default marker)
                const searchMarker = new google.maps.Marker({
                    position: location,
                    map: farmMap,
                    title: formattedAddress
                    // Using default red marker - no custom icon
                });

                // Show info window with address details
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 12px; max-width: 300px;">
                            <h4 style="margin: 0 0 8px 0; color: #ea4335;">📍 Found Location</h4>
                            <p style="margin: 0; font-size: 0.9rem; line-height: 1.4;">
                                <strong>${formattedAddress}</strong>
                            </p>
                            <p style="margin: 8px 0 0 0; font-size: 0.8rem; color: #666;">
                                Coordinates: ${location.lat().toFixed(6)}, ${location.lng().toFixed(6)}
                            </p>
                        </div>
                    `
                });

                infoWindow.open(farmMap, searchMarker);

                // Remove marker after 10 seconds
                setTimeout(() => {
                    searchMarker.setMap(null);
                    infoWindow.close();
                }, 10000);

                // Update the address input with formatted address
                addressInput.value = formattedAddress;

            } else {
                console.error('❌ Geocoding failed:', status);
                alert('Address not found. Please try a different address or be more specific.');
            }

            // Reset button
            searchBtn.innerHTML = originalContent;
            searchBtn.disabled = false;
        });
    }

    function getMyLocationAddress() {
        console.log('📍 User clicked My Location for address...');

        // Prevent rapid/automatic calls
        if (window.locationRequestInProgress) {
            console.log('📍 Location request already in progress, ignoring...');
            return;
        }

        if (!navigator.geolocation) {
            alert('❌ Geolocation is not supported by this browser.\n\nPlease use a modern browser that supports GPS location.');
            return;
        }

        if (!geocoder) {
            alert('❌ Geocoding service not available. Please refresh the page and try again.');
            return;
        }

        // Explicit user confirmation
        const userConfirm = confirm(
            '📍 Get Your Current Location?\n\n' +
            'This will:\n' +
            '• Access your GPS location\n' +
            '• Fill the address search bar\n' +
            '• Center the map on your position\n\n' +
            'Click "OK" to proceed or "Cancel" to skip.'
        );

        if (!userConfirm) {
            console.log('📍 User cancelled location request');
            return;
        }

        // Set flag to prevent multiple calls
        window.locationRequestInProgress = true;

        // Show loading state
        const btn = document.getElementById('my-location-search-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
        btn.disabled = true;

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const location = new google.maps.LatLng(lat, lng);

                console.log(`📍 Current location: ${lat}, ${lng}`);

                // Reverse geocode to get address
                geocoder.geocode({ location: location }, function(results, status) {
                    if (status === 'OK' && results[0]) {
                        const address = results[0].formatted_address;

                        console.log(`✅ Address found: ${address}`);

                        // Fill the address input
                        document.getElementById('address-search-input').value = address;

                        // Center map on current location
                        farmMap.setCenter(location);
                        farmMap.setZoom(18);

                        // Add current location marker (using default marker)
                        const locationMarker = new google.maps.Marker({
                            position: location,
                            map: farmMap,
                            title: 'Your Current Location'
                            // Using default red marker - no custom icon
                        });

                        // Show info window
                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 12px; max-width: 300px;">
                                    <h4 style="margin: 0 0 8px 0; color: #4285f4;">📍 Your Current Location</h4>
                                    <p style="margin: 0; font-size: 0.9rem; line-height: 1.4;">
                                        <strong>${address}</strong>
                                    </p>
                                    <p style="margin: 8px 0 0 0; font-size: 0.8rem; color: #666;">
                                        Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}
                                    </p>
                                </div>
                            `
                        });

                        infoWindow.open(farmMap, locationMarker);

                        // Remove marker after 8 seconds
                        setTimeout(() => {
                            locationMarker.setMap(null);
                            infoWindow.close();
                        }, 8000);

                    } else {
                        console.error('❌ Reverse geocoding failed:', status);
                        alert('Unable to get address for your location. Using coordinates only.');

                        // Fill with coordinates if address lookup fails
                        document.getElementById('address-search-input').value = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

                        // Still center the map
                        farmMap.setCenter(location);
                        farmMap.setZoom(18);
                    }

                    // Reset button and flag
                    btn.innerHTML = originalContent;
                    btn.disabled = false;
                    window.locationRequestInProgress = false;
                });
            },
            function(error) {
                console.error('❌ Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access and try again.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information is unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                alert(errorMessage);

                // Reset button and flag
                btn.innerHTML = originalContent;
                btn.disabled = false;
                window.locationRequestInProgress = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }
</script>

<!-- Load Google Maps API with Drawing Library -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=drawing,geometry&callback=initMap">
</script>

<script>
    function handleMapsError() {
        console.error('❌ Failed to load Google Maps API');
        const mapElement = document.getElementById('farm-map');
        const loadingElement = document.getElementById('map-loading');

        // Hide loading spinner
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (mapElement) {
            mapElement.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #e74c3c; background: #fff; border-radius: 15px;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px; color: #f56565;"></i>
                    <h3 style="color: #2d3748; margin-bottom: 15px;">Failed to Load Google Maps</h3>
                    <p style="color: #64748b; margin-bottom: 10px;">Please check the following:</p>
                    <ul style="text-align: left; display: inline-block; color: #64748b; margin-bottom: 20px;">
                        <li>Internet connection is stable</li>
                        <li>Google Maps API key is valid</li>
                        <li>Drawing and Geometry libraries are enabled</li>
                        <li>Browser allows JavaScript execution</li>
                    </ul>
                    <div style="background: #f7fafc; padding: 15px; border-radius: 8px; margin-top: 20px;">
                        <p style="font-size: 0.9rem; color: #4a5568; margin: 0;">
                            <strong>API Key:</strong> {{ google_maps_api_key|default:"Not configured" }}
                        </p>
                        <p style="font-size: 0.8rem; color: #718096; margin: 5px 0 0 0;">
                            If the issue persists, please contact the system administrator.
                        </p>
                    </div>
                    <button onclick="location.reload()" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        cursor: pointer;
                        margin-top: 20px;
                        font-weight: 600;
                    ">
                        <i class="fas fa-refresh"></i> Retry
                    </button>
                </div>
            `;
        }
    }
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>