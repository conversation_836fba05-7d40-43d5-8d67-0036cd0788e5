<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard - No Dynamic Updates</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5; 
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
        }
        .chart-container { 
            width: 100%; 
            height: 400px; 
            margin: 20px 0; 
        }
        .metrics { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin: 20px 0; 
        }
        .metric-card { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 8px; 
            text-align: center; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Electricity Monitoring Dashboard - Test Version</h1>
        <p><strong>Status:</strong> Static data only - no real-time updates</p>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>Total Consumption</h3>
                <div>12.8 kW</div>
            </div>
            <div class="metric-card">
                <h3>Active Devices</h3>
                <div>8</div>
            </div>
            <div class="metric-card">
                <h3>Daily Cost</h3>
                <div>$127.40</div>
            </div>
            <div class="metric-card">
                <h3>Efficiency</h3>
                <div>87.5%</div>
            </div>
        </div>
        
        <div class="chart-container">
            <canvas id="testChart"></canvas>
        </div>
    </div>

    <script>
        // Completely static chart - no updates, no intervals, no dynamic data
        const ctx = document.getElementById('testChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['6 AM', '7 AM', '8 AM', '9 AM', '10 AM', '11 AM'],
                datasets: [{
                    label: 'Power Consumption (kW)',
                    data: [14.2, 13.8, 12.5, 11.8, 12.1, 13.5],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        min: 10,
                        max: 16,
                        ticks: {
                            callback: function(value) {
                                return value + ' kW';
                            }
                        }
                    }
                }
            }
        });
        
        console.log('Static chart loaded successfully');
        console.log('Chart data:', chart.data.datasets[0].data);
    </script>
</body>
</html>
