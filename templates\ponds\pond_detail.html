{% extends 'base.html' %}
{% load static %}

{% block title %}{{ pond.name }} - Pond Details{% endblock %}

{% block extra_css %}
<style>
    .pond-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .pond-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 6s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .info-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }

    .info-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
    }

    .info-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e9ecef;
    }

    .card-icon {
        font-size: 2em;
        margin-right: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .card-title {
        font-size: 1.3em;
        font-weight: bold;
        color: #2d3436;
        margin: 0;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: #636e72;
    }

    .detail-value {
        font-weight: bold;
        color: #2d3436;
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .status-active {
        background: linear-gradient(45deg, #00b894, #00cec9);
        color: white;
    }

    .status-maintenance {
        background: linear-gradient(45deg, #fdcb6e, #e17055);
        color: white;
    }

    .weather-card {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        border-radius: 20px;
        padding: 25px;
        margin: 20px 0;
    }

    .weather-current {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
    }

    .weather-icon {
        font-size: 4em;
        opacity: 0.9;
    }

    .weather-temp {
        font-size: 3em;
        font-weight: bold;
    }

    .weather-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
    }

    .weather-metric {
        text-align: center;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
    }

    .metric-value {
        font-size: 1.5em;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .metric-label {
        font-size: 0.9em;
        opacity: 0.8;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
        margin: 30px 0;
    }

    .action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }

    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }

    .action-btn.secondary {
        background: linear-gradient(45deg, #74b9ff, #0984e3);
    }

    .action-btn.secondary:hover {
        background: linear-gradient(45deg, #0984e3, #74b9ff);
    }

    .weather-alerts {
        background: linear-gradient(135deg, #ff7675, #fd79a8);
        color: white;
        border-radius: 20px;
        padding: 25px;
        margin: 20px 0;
        animation: pulse-alert 2s infinite;
    }

    @keyframes pulse-alert {
        0%, 100% { transform: scale(1); opacity: 0.9; }
        50% { transform: scale(1.02); opacity: 1; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Pond Header -->
    <div class="pond-header">
        <div class="position-relative">
            <h1><i class="fas fa-fish me-3"></i>{{ pond.name }}</h1>
            <p class="lead mb-0">Comprehensive pond monitoring and management</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🐟 POND DETAILS</span>
                {% if pond.farm %}
                <span class="badge bg-light text-dark me-2">🏢 {{ pond.farm.name }}</span>
                {% endif %}
                {% if has_location %}
                <span class="badge bg-success text-white me-2">📍 LOCATION ENABLED</span>
                <span class="badge bg-info text-white">🌤️ WEATHER INTEGRATED</span>
                {% else %}
                <span class="badge bg-warning text-dark">📍 NO LOCATION DATA</span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Weather Alerts -->
    {% if weather_alerts %}
    <div class="weather-alerts">
        <h4><i class="fas fa-exclamation-triangle me-2"></i>Weather Alerts</h4>
        {% for alert in weather_alerts %}
        <div class="alert-item mb-2">
            <strong>{{ alert.alert_type|title }}:</strong> {{ alert.message }}
            <small class="d-block mt-1">{{ alert.created_at|timesince }} ago</small>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Information Cards -->
    <div class="info-cards">
        <!-- Basic Information -->
        <div class="info-card">
            <div class="card-header">
                <i class="fas fa-info-circle card-icon"></i>
                <h3 class="card-title">Basic Information</h3>
            </div>
            <div class="detail-item">
                <span class="detail-label">Pond Name:</span>
                <span class="detail-value">{{ pond.name }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Farm:</span>
                <span class="detail-value">{{ pond.farm.name|default:"Not assigned" }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Size:</span>
                <span class="detail-value">{{ pond.size }} m²</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="status-badge status-{{ pond.status }}">{{ pond.status|title }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Species:</span>
                <span class="detail-value">{{ pond.species|default:"Not specified" }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Density:</span>
                <span class="detail-value">{{ pond.density|default:"Not specified" }}</span>
            </div>
        </div>

        <!-- Location Information -->
        <div class="info-card">
            <div class="card-header">
                <i class="fas fa-map-marker-alt card-icon"></i>
                <h3 class="card-title">Location & Weather</h3>
            </div>
            {% if has_location %}
            <div class="detail-item">
                <span class="detail-label">Latitude:</span>
                <span class="detail-value">{{ pond.latitude|floatformat:6 }}°N</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Longitude:</span>
                <span class="detail-value">{{ pond.longitude|floatformat:6 }}°E</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Weather Station:</span>
                <span class="detail-value">{{ weather_station.name|default:"No station assigned" }}</span>
            </div>
            {% if weather_data %}
            <div class="detail-item">
                <span class="detail-label">Last Weather Update:</span>
                <span class="detail-value">{{ weather_data.timestamp|timesince }} ago</span>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5>No Location Data</h5>
                <p>Add GPS coordinates to enable weather monitoring and mapping features.</p>
            </div>
            {% endif %}
        </div>

        <!-- Operational Status -->
        <div class="info-card">
            <div class="card-header">
                <i class="fas fa-cogs card-icon"></i>
                <h3 class="card-title">Operational Status</h3>
            </div>
            <div class="detail-item">
                <span class="detail-label">Occupancy:</span>
                <span class="detail-value">{{ pond.occupancy }}%</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Water Quality:</span>
                <span class="detail-value">{{ pond.water_quality|default:"Not assessed" }}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Feeding Status:</span>
                <span class="detail-value">{{ pond.feeding_status|default:"Not specified" }}</span>
            </div>
            {% if pond.stocked_date %}
            <div class="detail-item">
                <span class="detail-label">Stocked Date:</span>
                <span class="detail-value">{{ pond.stocked_date }}</span>
            </div>
            {% endif %}
            {% if pond.harvest_date %}
            <div class="detail-item">
                <span class="detail-label">Harvest Date:</span>
                <span class="detail-value">{{ pond.harvest_date }}</span>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Current Weather -->
    {% if weather_data %}
    <div class="weather-card">
        <h3><i class="fas fa-cloud-sun me-2"></i>Current Weather Conditions</h3>
        <div class="weather-current">
            <div class="weather-icon">
                {% if weather_data.condition == 'Clear' %}
                    <i class="fas fa-sun"></i>
                {% elif weather_data.condition == 'Partly Cloudy' %}
                    <i class="fas fa-cloud-sun"></i>
                {% elif 'Rain' in weather_data.condition %}
                    <i class="fas fa-cloud-rain"></i>
                {% else %}
                    <i class="fas fa-cloud"></i>
                {% endif %}
            </div>
            <div class="text-center">
                <div class="weather-temp">{{ weather_data.temperature }}°C</div>
                <div>{{ weather_data.condition }}</div>
            </div>
        </div>

        <div class="weather-metrics">
            <div class="weather-metric">
                <div class="metric-value">{{ weather_data.humidity }}%</div>
                <div class="metric-label">Humidity</div>
            </div>
            <div class="weather-metric">
                <div class="metric-value">{{ weather_data.wind_speed }} km/h</div>
                <div class="metric-label">Wind Speed</div>
            </div>
            <div class="weather-metric">
                <div class="metric-value">{{ weather_data.pressure }} hPa</div>
                <div class="metric-label">Pressure</div>
            </div>
            <div class="weather-metric">
                <div class="metric-value">{{ weather_data.precipitation }} mm</div>
                <div class="metric-label">Precipitation</div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'ponds:simple_enhanced_aerator_map' pond.id %}" class="action-btn">
            <i class="fas fa-fan"></i>Enhanced Aerator Map
        </a>
        <a href="{% url 'ponds:aerator_management' pond.id %}" class="action-btn">
            <i class="fas fa-cogs"></i>Manage Aerators
        </a>
        {% if has_location %}
        <a href="{% url 'weather:pond_weather_map' pond.id %}" class="action-btn secondary">
            <i class="fas fa-map-marked-alt"></i>Weather Map
        </a>
        {% endif %}
        <a href="{% url 'weather:unified_weather_map' %}" class="action-btn secondary">
            <i class="fas fa-globe"></i>Unified Map
        </a>
        <a href="{% url 'weather:pond_weather_dashboard' %}" class="action-btn secondary">
            <i class="fas fa-tachometer-alt"></i>Weather Dashboard
        </a>
        <a href="{% url 'ponds:pond_list' %}" class="action-btn">
            <i class="fas fa-list"></i>All Ponds
        </a>
        {% if pond.farm %}
        <a href="{% url 'ponds:farm_detail' pond.farm.id %}" class="action-btn">
            <i class="fas fa-building"></i>Farm Details
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}