{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create New Pond - 3-Step Wizard - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
<style>
    .wizard-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }

    .wizard-header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
    }

    .wizard-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .wizard-header p {
        font-size: 1.1rem;
        opacity: 0.9;
    }

    .wizard-progress {
        display: flex;
        justify-content: center;
        margin-bottom: 40px;
        position: relative;
    }

    .progress-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 2;
    }

    .progress-step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 25px;
        left: 100%;
        width: 150px;
        height: 3px;
        background: rgba(255,255,255,0.3);
        z-index: 1;
    }

    .progress-step.completed::after {
        background: #10b981;
    }

    .progress-step.active::after {
        background: linear-gradient(90deg, #10b981 50%, rgba(255,255,255,0.3) 50%);
    }

    .step-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255,255,255,0.3);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 1.2rem;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .progress-step.completed .step-circle {
        background: #10b981;
    }

    .progress-step.active .step-circle {
        background: #3b82f6;
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
    }

    .step-title {
        color: white;
        font-weight: 600;
        text-align: center;
        opacity: 0.8;
    }

    .progress-step.active .step-title,
    .progress-step.completed .step-title {
        opacity: 1;
    }

    .wizard-content {
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .step-content {
        display: none;
    }

    .step-content.active {
        display: block;
        animation: fadeInUp 0.5s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .step-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .step-header h2 {
        color: #2d3748;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .step-header p {
        color: #718096;
        font-size: 1.1rem;
    }

    .map-container {
        height: 500px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        position: relative;
    }

    .address-search-block {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .search-block-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        color: #2d3748;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .search-input-container {
        display: flex;
        background: rgba(248, 250, 252, 0.8);
        border-radius: 12px;
        padding: 8px;
        border: 2px solid rgba(102, 126, 234, 0.1);
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .search-input-container:focus-within {
        border-color: rgba(102, 126, 234, 0.3);
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .address-search-input {
        flex: 1;
        border: none;
        outline: none;
        padding: 14px 18px;
        font-size: 1rem;
        background: white;
        color: #2d3748;
        border-radius: 10px;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }

    .address-search-input:focus {
        border-color: rgba(102, 126, 234, 0.5);
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
    }

    .address-search-input::placeholder {
        color: #a0aec0;
        font-style: italic;
    }

    .search-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 14px 18px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        min-width: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .my-location-search-btn {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        border: none;
        border-radius: 10px;
        padding: 14px 18px;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
    }

    .my-location-search-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(66, 133, 244, 0.4);
    }

    .my-location-search-btn:disabled,
    .search-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .map-tools-top {
        position: absolute;
        top: 15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        display: flex;
        gap: 10px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .map-tools-bottom {
        position: absolute;
        bottom: 15px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        display: flex;
        gap: 10px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 12px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .map-tool-btn {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 10px;
        padding: 10px 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #4a5568;
        font-weight: 600;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 100px;
        justify-content: center;
        backdrop-filter: blur(5px);
    }

    .map-tool-btn:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
    }

    .map-tool-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    }

    .map-tool-btn i {
        font-size: 1rem;
    }

    .map-tool-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .map-tool-btn .fa-spinner {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    #current-location-btn.locating,
    #current-location-aerator-btn.locating {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        color: white;
        border-color: #4285f4;
    }

    .measurement-display {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: 1px solid rgba(255,255,255,0.2);
        min-width: 220px;
    }

    .measurement-display h4 {
        margin: 0 0 12px 0;
        color: #2d3748;
        font-size: 1rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .measurement-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .measurement-item:last-child {
        margin-bottom: 0;
        font-weight: bold;
        color: #667eea;
        border-top: 1px solid #e2e8f0;
        padding-top: 8px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #2d3748;
    }

    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-select {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 1rem;
        background: white;
        cursor: pointer;
    }

    .wizard-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 40px;
    }

    .btn {
        padding: 12px 30px;
        border: none;
        border-radius: 10px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-secondary {
        background: #e2e8f0;
        color: #4a5568;
    }

    .btn-secondary:hover {
        background: #cbd5e0;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .aerator-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .aerator-item {
        background: #f7fafc;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 15px;
        transition: all 0.3s ease;
    }

    .aerator-item:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .aerator-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 10px;
    }

    .aerator-type {
        font-weight: 600;
        color: #2d3748;
    }

    .aerator-coords {
        font-size: 0.85rem;
        color: #718096;
    }

    .weather-station-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        margin-bottom: 20px;
    }

    .weather-station-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }

    .weather-icon {
        font-size: 2.5rem;
    }

    .weather-info h3 {
        margin: 0;
        font-size: 1.3rem;
    }

    .weather-info p {
        margin: 5px 0 0 0;
        opacity: 0.9;
    }

    .labor-assignment {
        background: #f7fafc;
        border-radius: 12px;
        padding: 20px;
        margin-top: 20px;
    }

    .labor-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 15px;
    }

    .worker-card {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .worker-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }

    .worker-card.selected {
        border-color: #667eea;
        background: #f0f4ff;
    }

    .worker-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #667eea;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        margin: 0 auto 10px;
    }

    .worker-name {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 5px;
    }

    .worker-role {
        font-size: 0.85rem;
        color: #718096;
    }

    /* Responsive Design for Map Controls */
    @media (max-width: 768px) {
        .address-search-block {
            padding: 15px;
            margin-bottom: 15px;
        }

        .search-block-header {
            font-size: 1rem;
            margin-bottom: 12px;
        }

        .search-input-container {
            flex-direction: column;
            gap: 10px;
            padding: 10px;
        }

        .address-search-input {
            padding: 12px 16px;
            font-size: 0.95rem;
        }

        .search-btn, .my-location-search-btn {
            padding: 12px 16px;
            font-size: 0.95rem;
            border-radius: 8px;
            width: 100%;
            justify-content: center;
        }

        .map-tools-top, .map-tools-bottom {
            left: 10px;
            right: 10px;
            top: 15px;
            transform: none;
            flex-wrap: wrap;
            justify-content: center;
        }

        .map-tool-btn {
            min-width: 80px;
            padding: 8px 12px;
            font-size: 0.8rem;
        }

        .measurement-display {
            top: 80px;
            right: 10px;
            left: 10px;
            position: absolute;
            margin-bottom: 10px;
            min-width: auto;
        }

        .map-container {
            position: relative;
        }
    }

    @media (max-width: 480px) {
        .map-tool-btn {
            min-width: 70px;
            padding: 6px 10px;
            font-size: 0.75rem;
        }

        .map-tool-btn i {
            font-size: 0.9rem;
        }
    }
</style>
    </style>
</head>
<body>
<div class="wizard-container">
    <div class="wizard-header">
        <h1>🦐 Create New Pond</h1>
        <p>Advanced 3-Step Wizard with GPS Mapping & Automation</p>
    </div>

    <!-- Progress Steps -->
    <div class="wizard-progress">
        <div class="progress-step active" data-step="1">
            <div class="step-circle">1</div>
            <div class="step-title">Pond Marking<br>& Measurement</div>
        </div>
        <div class="progress-step" data-step="2">
            <div class="step-circle">2</div>
            <div class="step-title">Aerator Placement<br>& Equipment</div>
        </div>
        <div class="progress-step" data-step="3">
            <div class="step-circle">3</div>
            <div class="step-title">Details & Labor<br>Assignment</div>
        </div>
    </div>

    <!-- Wizard Content -->
    <div class="wizard-content">
        <!-- Step 1: Pond Marking & GPS Measurement -->
        <div class="step-content active" id="step-1">
            <div class="step-header">
                <h2>📍 Step 1: Pond Marking & GPS Measurement</h2>
                <p>Search for a location or use your current position, then draw your pond boundaries on the map.</p>
                <div style="background: #f0f4ff; border: 1px solid #c7d2fe; border-radius: 8px; padding: 12px; margin: 15px 0; font-size: 0.9rem; color: #4338ca;">
                    <i class="fas fa-info-circle" style="margin-right: 8px;"></i>
                    <strong>Instructions:</strong> First search for your location, then click on the map to start drawing your pond boundary. The system will automatically calculate area and perimeter measurements.
                </div>
            </div>

            <!-- Address Search Block -->
            <div class="address-search-block">
                <div class="search-block-header">
                    <i class="fas fa-map-marker-alt" style="color: #667eea;"></i>
                    <span>Find Location</span>
                </div>
                <div class="search-input-container">
                    <input type="text" id="address-search-input" placeholder="Search for an address or location..." class="address-search-input">
                    <button class="search-btn" id="search-address-btn" onclick="searchAddress()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="my-location-search-btn" id="my-location-search-btn" onclick="event.preventDefault(); event.stopPropagation(); getMyLocationAddress();">
                        <i class="fas fa-crosshairs"></i> My Location
                    </button>
                </div>
            </div>

            <div class="map-container">
                <!-- Drawing Tools -->
                <div class="map-tools-top">
                    <button class="map-tool-btn active" id="draw-polygon-btn">
                        <i class="fas fa-draw-polygon"></i> Draw Pond
                    </button>
                    <button class="map-tool-btn" id="edit-polygon-btn">
                        <i class="fas fa-edit"></i> Edit Shape
                    </button>
                    <button class="map-tool-btn" id="clear-polygon-btn">
                        <i class="fas fa-trash"></i> Clear
                    </button>
                    <button class="map-tool-btn" id="debug-drawing-btn" style="background: #e74c3c;">
                        <i class="fas fa-bug"></i> Debug
                    </button>
                    <button class="map-tool-btn" id="alt-draw-btn" style="background: #f39c12;">
                        <i class="fas fa-hand-pointer"></i> Alt Draw
                    </button>
                    <button class="map-tool-btn" id="current-location-btn" onclick="getCurrentLocation()">
                        <i class="fas fa-crosshairs"></i> Center Map
                    </button>
                </div>

                <!-- Map Type Controls - Bottom Center -->
                <div class="map-tools-bottom">
                    <button class="map-tool-btn active" id="roadmap-btn" onclick="switchMapType('roadmap')">
                        <i class="fas fa-map"></i> Road View
                    </button>
                    <button class="map-tool-btn" id="satellite-btn" onclick="switchMapType('satellite')">
                        <i class="fas fa-satellite"></i> Satellite View
                    </button>
                </div>

                <div class="measurement-display" id="measurement-display">
                    <h4><i class="fas fa-ruler-combined"></i> Pond Measurements</h4>
                    <div class="measurement-item">
                        <span>Perimeter:</span>
                        <span id="perimeter-value">0 m</span>
                    </div>
                    <div class="measurement-item">
                        <span>Area:</span>
                        <span id="area-value">0 m²</span>
                    </div>
                    <div class="measurement-item">
                        <span>Area (Hectares):</span>
                        <span id="area-hectares">0 ha</span>
                    </div>
                </div>

                <div id="pond-map" style="height: 100%; width: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #666;">
                    <div id="map-loading">🗺️ Loading Google Maps...</div>
                </div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label">Pond Name *</label>
                    <input type="text" class="form-input" id="pond-name" placeholder="e.g., Pond A1" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Farm Selection (Optional)</label>
                    <select class="form-select" id="farm-select">
                        <option value="">No farm assignment (Independent pond)</option>
                        {% for farm in farms %}
                        <option value="{{ farm.id }}">{{ farm.name }} - {{ farm.location }}</option>
                        {% endfor %}
                    </select>
                    <small style="color: #718096; font-size: 0.85rem; margin-top: 5px; display: block;">
                        💡 You can create a pond without assigning it to a farm, or select an existing farm
                    </small>
                </div>
            </div>
        </div>

        <!-- Step 2: Aerator Placement -->
        <div class="step-content" id="step-2">
            <div class="step-header">
                <h2>⚙️ Step 2: Aerator Placement & Equipment Setup</h2>
                <p>Search for precise locations and place aerators on your pond with equipment specifications.</p>
            </div>

            <!-- Address Search Block for Aerator Map -->
            <div class="address-search-block">
                <div class="search-block-header">
                    <i class="fas fa-cog" style="color: #667eea;"></i>
                    <span>Find Aerator Location</span>
                </div>
                <div class="search-input-container">
                    <input type="text" id="address-search-input-aerator" placeholder="Search for an address or location..." class="address-search-input">
                    <button class="search-btn" id="search-address-aerator-btn" onclick="searchAddressAerator()">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="my-location-search-btn" id="my-location-search-aerator-btn" onclick="event.preventDefault(); event.stopPropagation(); getMyLocationAddressAerator();">
                        <i class="fas fa-crosshairs"></i> My Location
                    </button>
                </div>
            </div>

            <div class="map-container">
                <!-- Aerator Tools -->
                <div class="map-tools-top">
                    <button class="map-tool-btn active" id="place-aerator-btn">
                        <i class="fas fa-plus-circle"></i> Add Aerator
                    </button>
                    <button class="map-tool-btn" id="remove-aerator-btn">
                        <i class="fas fa-minus-circle"></i> Remove
                    </button>
                    <button class="map-tool-btn" id="auto-place-btn">
                        <i class="fas fa-magic"></i> Auto Place
                    </button>
                    <button class="map-tool-btn" id="current-location-aerator-btn" onclick="getCurrentLocationAerator()">
                        <i class="fas fa-crosshairs"></i> Center Map
                    </button>
                </div>

                <div class="measurement-display">
                    <h4><i class="fas fa-cogs"></i> Aerator Statistics</h4>
                    <div class="measurement-item">
                        <span>Aerators:</span>
                        <span id="aerator-count">0</span>
                    </div>
                    <div class="measurement-item">
                        <span>Total Power:</span>
                        <span id="total-power">0 HP</span>
                    </div>
                    <div class="measurement-item">
                        <span>Coverage:</span>
                        <span id="coverage-percent">0%</span>
                    </div>
                </div>

                <div id="aerator-map" style="height: 100%; width: 100%;"></div>
            </div>

            <div class="aerator-list" id="aerator-list">
                <!-- Aerators will be dynamically added here -->
            </div>
        </div>

        <!-- Step 3: Details & Labor Assignment -->
        <div class="step-content" id="step-3">
            <div class="step-header">
                <h2>📋 Step 3: Pond Details & Labor Assignment</h2>
                <p>Complete pond information and assign workers and weather monitoring</p>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label">Species *</label>
                    <select class="form-select" id="species-select" required>
                        <option value="">Select species...</option>
                        <option value="White Shrimp (L. vannamei)">White Shrimp (L. vannamei)</option>
                        <option value="Black Tiger Shrimp (P. monodon)">Black Tiger Shrimp (P. monodon)</option>
                        <option value="Blue Shrimp (L. stylirostris)">Blue Shrimp (L. stylirostris)</option>
                        <option value="Indian White Shrimp (F. indicus)">Indian White Shrimp (F. indicus)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">Stocking Density (per m²)</label>
                    <input type="number" class="form-input" id="density-input" placeholder="e.g., 15" min="1" max="50">
                </div>
                <div class="form-group">
                    <label class="form-label">Water Depth (meters)</label>
                    <input type="number" class="form-input" id="depth-input" placeholder="e.g., 1.5" step="0.1" min="0.5" max="5">
                </div>
                <div class="form-group">
                    <label class="form-label">Pond Status</label>
                    <select class="form-select" id="status-select">
                        <option value="preparation">Preparation</option>
                        <option value="ready">Ready for Stocking</option>
                        <option value="active">Active</option>
                        <option value="maintenance">Maintenance</option>
                    </select>
                </div>
            </div>

            <!-- Weather Station Setup -->
            <div class="weather-station-card">
                <div class="weather-station-header">
                    <div class="weather-icon">🌤️</div>
                    <div class="weather-info">
                        <h3>Weather Station Integration</h3>
                        <p>Automatic weather monitoring for your pond</p>
                    </div>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" style="color: white;">Weather Station</label>
                        <select class="form-select" id="weather-station-select">
                            <option value="">Auto-detect nearest station</option>
                            <option value="create-new">Create new weather station</option>
                            {% for station in weather_stations %}
                            <option value="{{ station.id }}">{{ station.name }} - {{ station.location }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" style="color: white;">Monitoring Frequency</label>
                        <select class="form-select" id="monitoring-frequency">
                            <option value="15">Every 15 minutes</option>
                            <option value="30" selected>Every 30 minutes</option>
                            <option value="60">Every hour</option>
                            <option value="180">Every 3 hours</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Labor Assignment -->
            <div class="labor-assignment">
                <h3>👥 Labor Assignment</h3>
                <p>Assign workers to manage this pond</p>

                <div class="labor-grid" id="worker-grid">
                    {% for worker in available_workers %}
                    <div class="worker-card" data-worker-id="{{ worker.id }}">
                        <div class="worker-avatar">{{ worker.name|first }}</div>
                        <div class="worker-name">{{ worker.name }}</div>
                        <div class="worker-role">{{ worker.role|default:"Worker" }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Wizard Actions -->
    <div class="wizard-actions">
        <div style="display: flex; gap: 10px;">
            <button class="btn btn-secondary" id="prev-btn" style="display: none;">
                <i class="fas fa-arrow-left"></i> Previous
            </button>
            <button class="btn btn-secondary" id="reset-btn" onclick="resetWizardState()" title="Reset all data and start over">
                <i class="fas fa-redo"></i> Reset
            </button>
        </div>
        <div>
            <span id="step-indicator">Step 1 of 3</span>
        </div>
        <button class="btn btn-primary" id="next-btn">
            Next <i class="fas fa-arrow-right"></i>
        </button>
    </div>
</div>

<script>
    // Wizard state
    let currentStep = 1;
    let pondPolygon = null;
    let aeratorMarkers = [];
    let selectedWorkers = [];
    let map = null;
    let drawingManager = null;
    let geocoder = null;
    let placesService = null;

    // Initialize wizard
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📋 DOM loaded, initializing wizard...');
        initializeWizard();
        
        // Set up basic event listeners (non-map related)
        setupBasicEventListeners();

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            console.log('🗺️ Google Maps already loaded, initializing map...');
            initializeMap();
            setupMapEventListeners();
        } else {
            console.log('⏳ Waiting for Google Maps API...');
            // Map will be initialized when Google Maps API loads via callback
        }
    });

    function initializeWizard() {
        updateStepDisplay();
        updateButtons();
    }

    function initializeMap() {
        console.log('🗺️ Initializing Google Maps for pond wizard...');

        const mapElement = document.getElementById('pond-map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }

        try {
            // Initialize Google Maps for Step 1 with reliable location
            const defaultCenter = { lat: 40.7128, lng: -74.0060 }; // New York City (reliable imagery)
            const userCenter = { lat: {{ center_lat|default:40.7128 }}, lng: {{ center_lng|default:-74.0060 }} };

            map = new google.maps.Map(mapElement, {
                zoom: 15,
                center: userCenter,
                mapTypeId: 'roadmap', // Start with roadmap for better compatibility
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
                    position: google.maps.ControlPosition.TOP_CENTER,
                    mapTypeIds: ['roadmap', 'satellite', 'hybrid', 'terrain']
                },
                streetViewControl: false,
                fullscreenControl: true,
                zoomControl: true,
                scaleControl: true,
                rotateControl: false,
                gestureHandling: 'cooperative'
            });

            console.log('✅ Map created successfully');

            // Initialize geocoder and places service
            geocoder = new google.maps.Geocoder();
            placesService = new google.maps.places.PlacesService(map);
            console.log('✅ Geocoder and Places service initialized');

            // Hide loading message
            const loadingElement = document.getElementById('map-loading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }

            // Initialize drawing manager
            drawingManager = new google.maps.drawing.DrawingManager({
                drawingMode: google.maps.drawing.OverlayType.POLYGON,
                drawingControl: false,
                polygonOptions: {
                    fillColor: '#667eea',
                    fillOpacity: 0.3,
                    strokeColor: '#667eea',
                    strokeWeight: 3,
                    editable: true,
                    draggable: false
                }
            });

            drawingManager.setMap(map);
            console.log('✅ Drawing manager initialized and set to polygon drawing mode');
            console.log('🖊️ Ready to draw! Click on the map to start drawing your pond boundary.');
            
            // Debug the drawing manager
            setTimeout(() => {
                debugDrawingManager();
                testDrawingMode();
            }, 1000);

            // Listen for polygon completion
            google.maps.event.addListener(drawingManager, 'polygoncomplete', function(polygon) {
                console.log('🎯 Polygon completed');
                if (pondPolygon) {
                    pondPolygon.setMap(null);
                }
                pondPolygon = polygon;
                calculateMeasurements();

                // Add listeners for polygon changes
                google.maps.event.addListener(polygon.getPath(), 'set_at', calculateMeasurements);
                google.maps.event.addListener(polygon.getPath(), 'insert_at', calculateMeasurements);
            });

            // Add map event listeners
            google.maps.event.addListener(map, 'tilesloaded', function() {
                console.log('✅ Map tiles loaded successfully');
                // Hide any loading indicators
                const loadingElement = document.getElementById('map-loading');
                if (loadingElement) {
                    loadingElement.style.display = 'none';
                }
            });

            google.maps.event.addListener(map, 'idle', function() {
                console.log('✅ Map is idle and ready');
            });

            // Add error handling for map tiles
            google.maps.event.addListener(map, 'tilesloaded', function() {
                // Check if we're seeing "no imagery" and suggest switching to satellite
                setTimeout(() => {
                    const mapDiv = document.getElementById('pond-map');
                    if (mapDiv && mapDiv.innerHTML.includes('Sorry, we have no imagery here')) {
                        console.log('⚠️ No imagery detected, suggesting satellite view');
                        // Auto-switch to satellite if roadmap has no imagery
                        map.setMapTypeId('satellite');
                    }
                }, 2000);
            });

            // Trigger resize to ensure map renders properly
            setTimeout(() => {
                google.maps.event.trigger(map, 'resize');

                // Try to set a better initial location if we have farm data
                const farmSelect = document.getElementById('farm-select');
                if (farmSelect && farmSelect.options.length > 1) {
                    // If we have farms, try to center on a more specific location
                    map.setCenter({ lat: 13.0827, lng: 80.2707 }); // Chennai, India
                    map.setZoom(12);
                }
            }, 500);

        } catch (error) {
            console.error('❌ Error initializing map:', error);
        }
    }

    // Global function for Google Maps API callback
    window.initMap = function() {
        console.log('🌍 Google Maps API loaded via initMap callback');
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            initializeMap();
        }
    };

    // Maps API callback function
    window.mapsApiReady = function() {
        console.log('🌍 Google Maps API loaded via mapsApiReady callback');
        // Always initialize the map and setup map-related event listeners
        initializeMap();
        setupMapEventListeners();
    };

    function calculateMeasurements() {
        if (!pondPolygon) {
            // Reset measurements when no polygon
            document.getElementById('area-value').textContent = '0 m²';
            document.getElementById('area-hectares').textContent = '0 ha';
            document.getElementById('perimeter-value').textContent = '0 m';
            return;
        }

        const path = pondPolygon.getPath();
        const area = google.maps.geometry.spherical.computeArea(path);
        const perimeter = google.maps.geometry.spherical.computeLength(path);

        document.getElementById('area-value').textContent = Math.round(area) + ' m²';
        document.getElementById('area-hectares').textContent = (area / 10000).toFixed(3) + ' ha';
        document.getElementById('perimeter-value').textContent = Math.round(perimeter) + ' m';

        // Auto-fill pond size in form
        const sizeInput = document.getElementById('pond-size');
        if (sizeInput) {
            sizeInput.value = Math.round(area);
        }

        console.log(`📏 Measurements updated: ${Math.round(area)} m², ${Math.round(perimeter)} m perimeter`);
    }

    function setupBasicEventListeners() {
        console.log('🔧 Setting up basic event listeners...');
        
        // Navigation buttons
        const nextBtn = document.getElementById('next-btn');
        const prevBtn = document.getElementById('prev-btn');
        
        if (nextBtn) nextBtn.addEventListener('click', nextStep);
        if (prevBtn) prevBtn.addEventListener('click', prevStep);

        // Worker selection
        document.addEventListener('click', function(e) {
            if (e.target.closest('.worker-card')) {
                toggleWorkerSelection(e.target.closest('.worker-card'));
            }
        });

        // Address search Enter key support
        const addressInput = document.getElementById('address-search-input');
        if (addressInput) {
            addressInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchAddress();
                }
            });
        }

        // Aerator address search Enter key support
        document.addEventListener('keypress', function(e) {
            if (e.target.id === 'address-search-input-aerator' && e.key === 'Enter') {
                e.preventDefault();
                searchAddressAerator();
            }
        });
        
        console.log('✅ Basic event listeners set up');
    }

    function setupMapEventListeners() {
        console.log('🗺️ Setting up map event listeners...');
        
        // Map tools - only set up if drawing manager is available
        const drawPolygonBtn = document.getElementById('draw-polygon-btn');
        const editPolygonBtn = document.getElementById('edit-polygon-btn');
        const clearPolygonBtn = document.getElementById('clear-polygon-btn');
        
        if (drawPolygonBtn) {
            drawPolygonBtn.addEventListener('click', () => {
                console.log('🖊️ Draw polygon button clicked');
                if (drawingManager && typeof google.maps.drawing !== 'undefined') {
                    // Try primary method with DrawingManager
                    try {
                        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                        setActiveMapTool('draw-polygon-btn');
                        console.log('✅ Drawing mode activated (Primary method)');
                    } catch (error) {
                        console.error('❌ DrawingManager error:', error);
                        console.log('🔄 Falling back to alternative drawing method...');
                        enableAlternativeDrawing();
                    }
                } else {
                    console.warn('⚠️ DrawingManager not available, using alternative method');
                    enableAlternativeDrawing();
                }
            });
        }

        if (editPolygonBtn) {
            editPolygonBtn.addEventListener('click', () => {
                console.log('✏️ Edit polygon button clicked');
                if (drawingManager) {
                    drawingManager.setDrawingMode(null);
                    setActiveMapTool('edit-polygon-btn');
                } else {
                    console.error('❌ Drawing manager not available');
                }
            });
        }

        if (clearPolygonBtn) {
            clearPolygonBtn.addEventListener('click', () => {
                console.log('🗑️ Clear polygon button clicked');
                clearPondPolygon();
            });
        }
        
        // Debug button
        const debugBtn = document.getElementById('debug-drawing-btn');
        if (debugBtn) {
            debugBtn.addEventListener('click', () => {
                console.log('🐛 Debug button clicked');
                debugDrawingManager();
                testDrawingMode();
            });
        }
        
        // Alternative draw button
        const altDrawBtn = document.getElementById('alt-draw-btn');
        if (altDrawBtn) {
            altDrawBtn.addEventListener('click', () => {
                console.log('🎨 Alternative draw button clicked');
                enableAlternativeDrawing();
                setActiveMapTool('alt-draw-btn');
            });
        }
        
        console.log('✅ Map event listeners set up');
    }

    function setActiveMapTool(activeId) {
        document.querySelectorAll('.map-tool-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(activeId).classList.add('active');
    }

    function switchMapType(mapType) {
        if (map) {
            map.setMapTypeId(mapType);
            console.log(`🗺️ Switched to ${mapType} view`);

            // Update button states
            document.getElementById('roadmap-btn').classList.remove('active');
            document.getElementById('satellite-btn').classList.remove('active');
            document.getElementById(mapType + '-btn').classList.add('active');
        }
    }

    function clearPondPolygon() {
        console.log('🗑️ Clearing pond polygon...');
        if (pondPolygon) {
            pondPolygon.setMap(null);
            pondPolygon = null;
            calculateMeasurements();
            console.log('✅ Pond polygon cleared');
        }

        // Reset drawing mode
        if (drawingManager) {
            drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
            setActiveMapTool('draw-polygon-btn');
        }
    }

    function getCurrentLocation() {
        console.log('📍 Getting current location...');

        if (!navigator.geolocation) {
            alert('❌ Geolocation is not supported by this browser.\n\nPlease use a modern browser that supports GPS location.');
            return;
        }

        // Show permission request info
        const permissionInfo = confirm(
            '📍 Location Access Required\n\n' +
            'This will help center the map on your current location for easier pond marking.\n\n' +
            '• Click "OK" to allow location access\n' +
            '• Click "Cancel" to skip\n\n' +
            'Your location data is only used locally and not stored.'
        );

        if (!permissionInfo) {
            return;
        }

        // Show loading state
        const btn = document.getElementById('current-location-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Locating...';
        btn.disabled = true;
        btn.classList.add('locating');

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const accuracy = position.coords.accuracy;

                console.log(`📍 Current location: ${lat}, ${lng} (accuracy: ${accuracy}m)`);

                if (map) {
                    // Center map on current location
                    const currentLocation = new google.maps.LatLng(lat, lng);
                    map.setCenter(currentLocation);
                    map.setZoom(18);

                    // Add a marker for current location
                    const currentLocationMarker = new google.maps.Marker({
                        position: currentLocation,
                        map: map,
                        title: 'Your Current Location',
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="15" cy="15" r="12" fill="#4285f4" stroke="white" stroke-width="3"/>
                                    <circle cx="15" cy="15" r="6" fill="white"/>
                                    <circle cx="15" cy="15" r="3" fill="#4285f4"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });

                    // Remove marker after 5 seconds
                    setTimeout(() => {
                        currentLocationMarker.setMap(null);
                    }, 5000);

                    // Show info window
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px; text-align: center;">
                                <h4 style="margin: 0 0 8px 0; color: #4285f4;">📍 Your Location</h4>
                                <p style="margin: 0; font-size: 0.9rem;">
                                    <strong>Coordinates:</strong><br>
                                    ${lat.toFixed(6)}, ${lng.toFixed(6)}
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 0.8rem; color: #666;">
                                    Accuracy: ±${Math.round(accuracy)}m
                                </p>
                            </div>
                        `
                    });

                    infoWindow.open(map, currentLocationMarker);
                    setTimeout(() => {
                        infoWindow.close();
                    }, 4000);
                }

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
                btn.classList.remove('locating');
            },
            function(error) {
                console.error('❌ Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access and try again.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information is unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                alert(errorMessage);

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
                btn.classList.remove('locating');
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    function getCurrentLocationAerator() {
        console.log('📍 Getting current location for aerator map...');

        if (!navigator.geolocation) {
            alert('Geolocation is not supported by this browser.');
            return;
        }

        // Show loading state
        const btn = document.getElementById('current-location-aerator-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Locating...';
        btn.disabled = true;
        btn.classList.add('locating');

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                console.log(`📍 Current location for aerator: ${lat}, ${lng}`);

                // Get the aerator map (created in step 2)
                const aeratorMapElement = document.getElementById('aerator-map');
                if (aeratorMapElement && aeratorMapElement.map) {
                    const currentLocation = new google.maps.LatLng(lat, lng);
                    aeratorMapElement.map.setCenter(currentLocation);
                    aeratorMapElement.map.setZoom(19);

                    // Add current location marker
                    const currentLocationMarker = new google.maps.Marker({
                        position: currentLocation,
                        map: aeratorMapElement.map,
                        title: 'Your Current Location',
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12.5" cy="12.5" r="10" fill="#4285f4" stroke="white" stroke-width="2"/>
                                    <circle cx="12.5" cy="12.5" r="5" fill="white"/>
                                    <circle cx="12.5" cy="12.5" r="2" fill="#4285f4"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });

                    // Remove marker after 5 seconds
                    setTimeout(() => {
                        currentLocationMarker.setMap(null);
                    }, 5000);
                }

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
                btn.classList.remove('locating');
            },
            function(error) {
                console.error('❌ Geolocation error:', error);
                alert('Unable to get your current location. Please check your location settings.');

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    function toggleWorkerSelection(workerCard) {
        const workerId = workerCard.dataset.workerId;

        if (workerCard.classList.contains('selected')) {
            workerCard.classList.remove('selected');
            selectedWorkers = selectedWorkers.filter(id => id !== workerId);
        } else {
            workerCard.classList.add('selected');
            selectedWorkers.push(workerId);
        }
    }

    function nextStep() {
        if (validateCurrentStep()) {
            if (currentStep < 3) {
                currentStep++;
                updateStepDisplay();
                updateButtons();

                if (currentStep === 2) {
                    initializeAeratorMap();
                }
            } else {
                submitPond();
            }
        }
    }

    function prevStep() {
        if (currentStep > 1) {
            currentStep--;
            updateStepDisplay();
            updateButtons();
        }
    }

    function validateCurrentStep() {
        switch (currentStep) {
            case 1:
                if (!document.getElementById('pond-name').value.trim()) {
                    alert('Please enter a pond name');
                    return false;
                }
                // Farm selection is now optional - no validation needed
                if (!pondPolygon) {
                    alert('Please draw the pond boundary on the map');
                    return false;
                }
                return true;
            case 2:
                return true; // Aerators are optional
            case 3:
                if (!document.getElementById('species-select').value) {
                    alert('Please select a species');
                    return false;
                }
                return true;
        }
        return true;
    }

    function updateStepDisplay() {
        // Update progress steps
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            const stepNum = index + 1;
            step.classList.remove('active', 'completed');

            if (stepNum < currentStep) {
                step.classList.add('completed');
            } else if (stepNum === currentStep) {
                step.classList.add('active');
            }
        });

        // Update content
        document.querySelectorAll('.step-content').forEach((content, index) => {
            content.classList.remove('active');
            if (index + 1 === currentStep) {
                content.classList.add('active');
            }
        });

        // Update step indicator
        document.getElementById('step-indicator').textContent = `Step ${currentStep} of 3`;
    }

    function updateButtons() {
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');

        prevBtn.style.display = currentStep === 1 ? 'none' : 'inline-flex';

        if (currentStep === 3) {
            nextBtn.innerHTML = '<i class="fas fa-check"></i> Create Pond';
            nextBtn.classList.add('btn-success');
        } else {
            nextBtn.innerHTML = 'Next <i class="fas fa-arrow-right"></i>';
            nextBtn.classList.remove('btn-success');
        }
    }

    function initializeAeratorMap() {
        // Copy pond polygon to aerator map
        const aeratorMap = new google.maps.Map(document.getElementById('aerator-map'), {
            zoom: 18,
            center: map.getCenter(),
            mapTypeId: 'satellite'
        });

        // Store reference to aerator map for location function
        document.getElementById('aerator-map').map = aeratorMap;

        if (pondPolygon) {
            const pondBounds = new google.maps.LatLngBounds();
            pondPolygon.getPath().forEach(point => pondBounds.extend(point));

            // Create pond outline on aerator map
            new google.maps.Polygon({
                paths: pondPolygon.getPath().getArray(),
                strokeColor: '#667eea',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: '#667eea',
                fillOpacity: 0.2,
                map: aeratorMap
            });

            aeratorMap.fitBounds(pondBounds);
        }

        // Setup aerator placement
        setupAeratorPlacement(aeratorMap);
    }

    function setupAeratorPlacement(aeratorMap) {
        aeratorMap.addListener('click', function(event) {
            if (document.getElementById('place-aerator-btn').classList.contains('active')) {
                addAerator(event.latLng, aeratorMap);
            }
        });

        document.getElementById('place-aerator-btn').addEventListener('click', () => {
            setActiveMapTool('place-aerator-btn');
        });

        document.getElementById('auto-place-btn').addEventListener('click', () => {
            autoPlaceAerators(aeratorMap);
        });
    }

    function addAerator(position, aeratorMap) {
        const aeratorId = 'aerator-' + (aeratorMarkers.length + 1);

        const marker = new google.maps.Marker({
            position: position,
            map: aeratorMap,
            title: `Aerator ${aeratorMarkers.length + 1}`,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12.5" cy="12.5" r="11" fill="#10b981" stroke="white" stroke-width="2"/>
                        <circle cx="12.5" cy="12.5" r="7" fill="none" stroke="white" stroke-width="1.5"/>
                        <path d="M12.5 5.5 L12.5 19.5 M5.5 12.5 L19.5 12.5" stroke="white" stroke-width="1.2"/>
                        <circle cx="12.5" cy="12.5" r="1.5" fill="white"/>
                    </svg>
                `),
                scaledSize: new google.maps.Size(25, 25)
            }
        });

        aeratorMarkers.push({
            id: aeratorId,
            marker: marker,
            position: position,
            type: 'paddle_wheel',
            power: 5
        });

        updateAeratorDisplay();
        addAeratorToList(aeratorId, position);
    }

    function updateAeratorDisplay() {
        document.getElementById('aerator-count').textContent = aeratorMarkers.length;
        const totalPower = aeratorMarkers.reduce((sum, aerator) => sum + aerator.power, 0);
        document.getElementById('total-power').textContent = totalPower + ' HP';

        // Calculate coverage (simplified)
        const coverage = Math.min(100, (aeratorMarkers.length * 20));
        document.getElementById('coverage-percent').textContent = coverage + '%';
    }

    function addAeratorToList(aeratorId, position) {
        const aeratorList = document.getElementById('aerator-list');
        const aeratorDiv = document.createElement('div');
        aeratorDiv.className = 'aerator-item';
        aeratorDiv.innerHTML = `
            <div class="aerator-header">
                <div class="aerator-type">Aerator ${aeratorMarkers.length}</div>
                <button onclick="removeAerator('${aeratorId}')" style="background: #ef4444; color: white; border: none; border-radius: 4px; padding: 4px 8px; cursor: pointer;">×</button>
            </div>
            <div class="aerator-coords">📍 ${position.lat().toFixed(6)}, ${position.lng().toFixed(6)}</div>
            <div style="margin-top: 10px;">
                <select onchange="updateAeratorType('${aeratorId}', this.value)" style="width: 100%; padding: 5px; margin-bottom: 5px;">
                    <option value="paddle_wheel">Paddle Wheel</option>
                    <option value="fountain">Fountain</option>
                    <option value="diffuser">Diffuser</option>
                    <option value="venturi">Venturi</option>
                </select>
                <input type="number" placeholder="Power (HP)" value="5" onchange="updateAeratorPower('${aeratorId}', this.value)" style="width: 100%; padding: 5px;">
            </div>
        `;
        aeratorList.appendChild(aeratorDiv);
    }

    function removeAerator(aeratorId) {
        const aeratorIndex = aeratorMarkers.findIndex(a => a.id === aeratorId);
        if (aeratorIndex !== -1) {
            aeratorMarkers[aeratorIndex].marker.setMap(null);
            aeratorMarkers.splice(aeratorIndex, 1);
            updateAeratorDisplay();

            // Remove from list
            const aeratorItem = document.querySelector(`[onclick*="${aeratorId}"]`).closest('.aerator-item');
            aeratorItem.remove();
        }
    }

    function updateAeratorType(aeratorId, type) {
        const aerator = aeratorMarkers.find(a => a.id === aeratorId);
        if (aerator) {
            aerator.type = type;
        }
    }

    function updateAeratorPower(aeratorId, power) {
        const aerator = aeratorMarkers.find(a => a.id === aeratorId);
        if (aerator) {
            aerator.power = parseFloat(power) || 0;
            updateAeratorDisplay();
        }
    }

    function autoPlaceAerators(aeratorMap) {
        if (!pondPolygon) return;

        // Clear existing aerators
        aeratorMarkers.forEach(aerator => aerator.marker.setMap(null));
        aeratorMarkers = [];
        document.getElementById('aerator-list').innerHTML = '';

        // Calculate optimal aerator positions
        const bounds = new google.maps.LatLngBounds();
        pondPolygon.getPath().forEach(point => bounds.extend(point));

        const center = bounds.getCenter();
        const area = google.maps.geometry.spherical.computeArea(pondPolygon.getPath());
        const recommendedAerators = Math.max(1, Math.ceil(area / 2000)); // 1 aerator per 2000 m²

        // Place aerators in a grid pattern
        for (let i = 0; i < recommendedAerators; i++) {
            const angle = (2 * Math.PI * i) / recommendedAerators;
            const radius = 0.0005; // Adjust based on pond size

            const lat = center.lat() + radius * Math.cos(angle);
            const lng = center.lng() + radius * Math.sin(angle);

            addAerator(new google.maps.LatLng(lat, lng), aeratorMap);
        }
    }

    function submitPond() {
        console.log('🚀 Starting pond submission...');
        
        // Show loading state
        const nextBtn = document.getElementById('next-btn');
        const originalBtnText = nextBtn.innerHTML;
        nextBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Pond...';
        nextBtn.disabled = true;

        // Collect all form data
        const farmId = document.getElementById('farm-select').value;
        const pondData = {
            name: document.getElementById('pond-name').value,
            farm_id: farmId || null, // Send null if no farm selected
            species: document.getElementById('species-select').value,
            density: document.getElementById('density-input').value,
            depth: document.getElementById('depth-input').value,
            status: document.getElementById('status-select').value,
            weather_station: document.getElementById('weather-station-select').value,
            monitoring_frequency: document.getElementById('monitoring-frequency').value,
            polygon: pondPolygon ? pondPolygon.getPath().getArray().map(point => ({
                lat: point.lat(),
                lng: point.lng()
            })) : null,
            aerators: aeratorMarkers.map(aerator => ({
                lat: aerator.position.lat(),
                lng: aerator.position.lng(),
                type: aerator.type,
                power: aerator.power
            })),
            assigned_workers: selectedWorkers
        };

        console.log('📋 Pond data to submit:', pondData);

        // Submit to server
        fetch('{% url "ponds:create_pond_wizard" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(pondData)
        })
        .then(response => {
            console.log('📡 Response received:', response);
            console.log('📡 Response status:', response.status);
            console.log('📡 Response OK:', response.ok);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return response.json();
        })
        .then(data => {
            console.log('📥 Response data:', data);
            
            // Reset button state
            nextBtn.innerHTML = originalBtnText;
            nextBtn.disabled = false;
            
            if (data.success) {
                // Format creation date
                const createdDate = new Date(data.created_at);
                const formattedDate = createdDate.toLocaleString();

                // Show success message with creation details
                showSuccessMessage(data, formattedDate);
                
            } else {
                showErrorMessage(data.error || 'Unknown error occurred');
            }
        })
        .catch(error => {
            console.error('❌ Error during submission:', error);
            
            // Reset button state
            nextBtn.innerHTML = originalBtnText;
            nextBtn.disabled = false;
            
            showErrorMessage('Network error: ' + error.message);
        });
    }

    function showSuccessMessage(data, formattedDate) {
        // Create success modal
        const successModal = document.createElement('div');
        successModal.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            ">
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                ">
                    <div style="
                        width: 80px;
                        height: 80px;
                        background: linear-gradient(135deg, #10b981, #059669);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 20px;
                        animation: bounce 0.6s ease;
                    ">
                        <i class="fas fa-check" style="color: white; font-size: 2rem;"></i>
                    </div>
                    
                    <h2 style="color: #065f46; margin-bottom: 20px; font-size: 1.8rem;">
                        🎉 Pond Created Successfully!
                    </h2>
                    
                    <div style="
                        background: #f0fdf4;
                        border-left: 4px solid #10b981;
                        padding: 20px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                        text-align: left;
                    ">
                        <div style="margin-bottom: 10px;"><strong>📋 Pond Name:</strong> ${data.pond_name}</div>
                        <div style="margin-bottom: 10px;"><strong>👤 Created By:</strong> ${data.created_by}</div>
                        <div style="margin-bottom: 10px;"><strong>📅 Created At:</strong> ${formattedDate}</div>
                        <div><strong>🆔 Pond ID:</strong> #${data.pond_id}</div>
                    </div>
                    
                    <div style="display: flex; gap: 15px; justify-content: center;">
                        <button onclick="createAnotherPond()" style="
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 10px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-plus"></i> Create Another
                        </button>
                        <button onclick="viewAllPonds('${data.redirect_url}')" style="
                            background: #e5e7eb;
                            color: #374151;
                            border: none;
                            padding: 12px 24px;
                            border-radius: 10px;
                            font-weight: 600;
                            cursor: pointer;
                            transition: all 0.3s ease;
                        ">
                            <i class="fas fa-list"></i> View All Ponds
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(successModal);
        
        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
                    transform: translate3d(0,0,0);
                }
                40%, 43% {
                    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                    transform: translate3d(0, -30px, 0);
                }
                70% {
                    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
                    transform: translate3d(0, -15px, 0);
                }
                90% {
                    transform: translate3d(0,-4px,0);
                }
            }
        `;
        document.head.appendChild(style);
    }

    function showErrorMessage(errorMessage) {
        // Create error modal
        const errorModal = document.createElement('div');
        errorModal.innerHTML = `
            <div style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            ">
                <div style="
                    background: white;
                    border-radius: 20px;
                    padding: 40px;
                    max-width: 500px;
                    width: 90%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
                ">
                    <div style="
                        width: 80px;
                        height: 80px;
                        background: linear-gradient(135deg, #ef4444, #dc2626);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin: 0 auto 20px;
                    ">
                        <i class="fas fa-exclamation-triangle" style="color: white; font-size: 2rem;"></i>
                    </div>
                    
                    <h2 style="color: #991b1b; margin-bottom: 20px; font-size: 1.8rem;">
                        ❌ Pond Creation Failed
                    </h2>
                    
                    <div style="
                        background: #fef2f2;
                        border-left: 4px solid #ef4444;
                        padding: 20px;
                        border-radius: 10px;
                        margin-bottom: 30px;
                        text-align: left;
                    ">
                        <strong>Error Details:</strong><br>
                        ${errorMessage}
                    </div>
                    
                    <button onclick="closeErrorModal()" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 10px;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    ">
                        <i class="fas fa-redo"></i> Try Again
                    </button>
                </div>
            </div>
        `;
        
        document.body.appendChild(errorModal);
    }

    function createAnotherPond() {
        // Remove success modal
        const modal = document.querySelector('div[style*="position: fixed"]');
        if (modal) modal.remove();
        
        // Reset wizard for another pond
        resetWizardState();
        console.log('🔄 Ready to create another pond');
    }

    function viewAllPonds(redirectUrl) {
        // Redirect to pond list
        window.location.href = redirectUrl || '{% url "ponds:pond_list" %}';
    }

    function closeErrorModal() {
        // Remove error modal
        const modal = document.querySelector('div[style*="position: fixed"]');
        if (modal) modal.remove();
    }
    }

    function resetWizardState() {
        console.log('🔄 Resetting wizard state...');

        // Reset step to 1
        currentStep = 1;

        // Clear pond polygon
        if (pondPolygon) {
            pondPolygon.setMap(null);
            pondPolygon = null;
        }

        // Clear aerator markers
        aeratorMarkers.forEach(aerator => {
            if (aerator.marker) {
                aerator.marker.setMap(null);
            }
        });
        aeratorMarkers = [];

        // Clear selected workers
        selectedWorkers = [];

        // Reset form fields
        document.getElementById('pond-name').value = '';
        document.getElementById('farm-select').value = '';
        document.getElementById('species-select').value = '';
        document.getElementById('density-input').value = '';
        document.getElementById('depth-input').value = '';
        document.getElementById('status-select').value = 'preparation';
        document.getElementById('weather-station-select').value = '';
        document.getElementById('monitoring-frequency').value = '30';

        // Reset measurements
        document.getElementById('area-value').textContent = '0 m²';
        document.getElementById('area-hectares').textContent = '0 ha';
        document.getElementById('perimeter-value').textContent = '0 m';

        // Reset aerator display
        document.getElementById('aerator-count').textContent = '0';
        document.getElementById('total-power').textContent = '0 HP';
        document.getElementById('coverage-percent').textContent = '0%';

        // Clear aerator list
        document.getElementById('aerator-list').innerHTML = '';

        // Reset worker selections
        document.querySelectorAll('.worker-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Reset drawing manager
        if (drawingManager) {
            drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
        }

        // Update display
        updateStepDisplay();
        updateButtons();

        console.log('✅ Wizard state reset complete');
    }

    function searchAddress() {
        const addressInput = document.getElementById('address-search-input');
        const address = addressInput.value.trim();

        if (!address) {
            alert('Please enter an address to search');
            return;
        }

        if (!geocoder) {
            alert('Geocoding service not available');
            return;
        }

        console.log(`🔍 Searching for address: ${address}`);

        // Show loading state
        const searchBtn = document.getElementById('search-address-btn');
        const originalContent = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchBtn.disabled = true;

        geocoder.geocode({ address: address }, function(results, status) {
            if (status === 'OK' && results[0]) {
                const location = results[0].geometry.location;
                const formattedAddress = results[0].formatted_address;

                console.log(`✅ Address found: ${formattedAddress}`);

                // Center map on the found location
                map.setCenter(location);
                map.setZoom(16);

                // Add a marker for the searched location
                const searchMarker = new google.maps.Marker({
                    position: location,
                    map: map,
                    title: formattedAddress,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="35" height="35" viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17.5 0C11.15 0 6 5.15 6 11.5C6 20.125 17.5 35 17.5 35S29 20.125 29 11.5C29 5.15 23.85 0 17.5 0Z" fill="#ea4335"/>
                                <circle cx="17.5" cy="11.5" r="6" fill="white"/>
                                <circle cx="17.5" cy="11.5" r="3" fill="#ea4335"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(35, 35)
                    }
                });

                // Show info window with address details
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 12px; max-width: 300px;">
                            <h4 style="margin: 0 0 8px 0; color: #ea4335;">📍 Found Location</h4>
                            <p style="margin: 0; font-size: 0.9rem; line-height: 1.4;">
                                <strong>${formattedAddress}</strong>
                            </p>
                            <p style="margin: 8px 0 0 0; font-size: 0.8rem; color: #666;">
                                Coordinates: ${location.lat().toFixed(6)}, ${location.lng().toFixed(6)}
                            </p>
                        </div>
                    `
                });

                infoWindow.open(map, searchMarker);

                // Remove marker after 10 seconds
                setTimeout(() => {
                    searchMarker.setMap(null);
                    infoWindow.close();
                }, 10000);

                // Update the address input with formatted address
                addressInput.value = formattedAddress;

            } else {
                console.error('❌ Geocoding failed:', status);
                let errorMessage = 'Address not found. ';

                switch(status) {
                    case 'ZERO_RESULTS':
                        errorMessage += 'Please try a different address or be more specific.';
                        break;
                    case 'OVER_QUERY_LIMIT':
                        errorMessage += 'Too many requests. Please try again later.';
                        break;
                    case 'REQUEST_DENIED':
                        errorMessage += 'Geocoding service denied.';
                        break;
                    default:
                        errorMessage += 'Please check your internet connection and try again.';
                        break;
                }

                alert(errorMessage);
            }

            // Reset button
            searchBtn.innerHTML = originalContent;
            searchBtn.disabled = false;
        });
    }

    function getMyLocationAddress() {
        console.log('📍 getMyLocationAddress() called - User clicked My Location for address...');
        console.trace('📍 Function call stack:');

        // Prevent rapid/automatic calls
        if (window.locationRequestInProgress) {
            console.log('📍 Location request already in progress, ignoring...');
            return;
        }

        if (!navigator.geolocation) {
            alert('❌ Geolocation is not supported by this browser.\n\nPlease use a modern browser that supports GPS location.');
            return;
        }

        if (!geocoder) {
            alert('❌ Geocoding service not available. Please refresh the page and try again.');
            return;
        }

        // Explicit user confirmation
        const userConfirm = confirm(
            '📍 Get Your Current Location?\n\n' +
            'This will:\n' +
            '• Access your GPS location\n' +
            '• Fill the address search bar\n' +
            '• Center the map on your position\n\n' +
            'Click "OK" to proceed or "Cancel" to skip.'
        );

        if (!userConfirm) {
            console.log('📍 User cancelled location request');
            return;
        }

        // Set flag to prevent multiple calls
        window.locationRequestInProgress = true;

        // Show loading state
        const btn = document.getElementById('my-location-search-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
        btn.disabled = true;

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const location = new google.maps.LatLng(lat, lng);

                console.log(`📍 Current location: ${lat}, ${lng}`);

                // Reverse geocode to get address
                geocoder.geocode({ location: location }, function(results, status) {
                    if (status === 'OK' && results[0]) {
                        const address = results[0].formatted_address;

                        console.log(`✅ Address found: ${address}`);

                        // Fill the address input
                        document.getElementById('address-search-input').value = address;

                        // Center map on current location
                        map.setCenter(location);
                        map.setZoom(18);

                        // Add current location marker
                        const locationMarker = new google.maps.Marker({
                            position: location,
                            map: map,
                            title: 'Your Current Location',
                            icon: {
                                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                    <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                        <circle cx="15" cy="15" r="12" fill="#4285f4" stroke="white" stroke-width="3"/>
                                        <circle cx="15" cy="15" r="6" fill="white"/>
                                        <circle cx="15" cy="15" r="3" fill="#4285f4"/>
                                    </svg>
                                `),
                                scaledSize: new google.maps.Size(30, 30)
                            }
                        });

                        // Show info window
                        const infoWindow = new google.maps.InfoWindow({
                            content: `
                                <div style="padding: 12px; max-width: 300px;">
                                    <h4 style="margin: 0 0 8px 0; color: #4285f4;">📍 Your Current Location</h4>
                                    <p style="margin: 0; font-size: 0.9rem; line-height: 1.4;">
                                        <strong>${address}</strong>
                                    </p>
                                    <p style="margin: 8px 0 0 0; font-size: 0.8rem; color: #666;">
                                        Coordinates: ${lat.toFixed(6)}, ${lng.toFixed(6)}
                                    </p>
                                </div>
                            `
                        });

                        infoWindow.open(map, locationMarker);

                        // Remove marker after 8 seconds
                        setTimeout(() => {
                            locationMarker.setMap(null);
                            infoWindow.close();
                        }, 8000);

                    } else {
                        console.error('❌ Reverse geocoding failed:', status);
                        alert('Unable to get address for your location. Using coordinates only.');

                        // Fill with coordinates if address lookup fails
                        document.getElementById('address-search-input').value = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;

                        // Still center the map
                        map.setCenter(location);
                        map.setZoom(18);
                    }

                    // Reset button and flag
                    btn.innerHTML = originalContent;
                    btn.disabled = false;
                    window.locationRequestInProgress = false;
                });
            },
            function(error) {
                console.error('❌ Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access and try again.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information is unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                alert(errorMessage);

                // Reset button and flag
                btn.innerHTML = originalContent;
                btn.disabled = false;
                window.locationRequestInProgress = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    function searchAddressAerator() {
        const addressInput = document.getElementById('address-search-input-aerator');
        const address = addressInput.value.trim();

        if (!address) {
            alert('Please enter an address to search');
            return;
        }

        if (!geocoder) {
            alert('Geocoding service not available');
            return;
        }

        console.log(`🔍 Searching for address on aerator map: ${address}`);

        // Show loading state
        const searchBtn = document.getElementById('search-address-aerator-btn');
        const originalContent = searchBtn.innerHTML;
        searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        searchBtn.disabled = true;

        geocoder.geocode({ address: address }, function(results, status) {
            if (status === 'OK' && results[0]) {
                const location = results[0].geometry.location;
                const formattedAddress = results[0].formatted_address;

                console.log(`✅ Address found for aerator map: ${formattedAddress}`);

                // Get the aerator map
                const aeratorMapElement = document.getElementById('aerator-map');
                if (aeratorMapElement && aeratorMapElement.map) {
                    // Center aerator map on the found location
                    aeratorMapElement.map.setCenter(location);
                    aeratorMapElement.map.setZoom(18);

                    // Add a marker for the searched location
                    const searchMarker = new google.maps.Marker({
                        position: location,
                        map: aeratorMapElement.map,
                        title: formattedAddress,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M15 0C10.85 0 7.5 3.35 7.5 7.5C7.5 13.125 15 30 15 30S22.5 13.125 22.5 7.5C22.5 3.35 19.15 0 15 0Z" fill="#ea4335"/>
                                    <circle cx="15" cy="7.5" r="4" fill="white"/>
                                    <circle cx="15" cy="7.5" r="2" fill="#ea4335"/>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(30, 30)
                        }
                    });

                    // Remove marker after 8 seconds
                    setTimeout(() => {
                        searchMarker.setMap(null);
                    }, 8000);
                }

                // Update the address input with formatted address
                addressInput.value = formattedAddress;

            } else {
                console.error('❌ Geocoding failed for aerator map:', status);
                alert('Address not found. Please try a different address or be more specific.');
            }

            // Reset button
            searchBtn.innerHTML = originalContent;
            searchBtn.disabled = false;
        });
    }

    function getMyLocationAddressAerator() {
        console.log('📍 User clicked My Location for aerator map address...');

        if (!navigator.geolocation) {
            alert('❌ Geolocation is not supported by this browser.\n\nPlease use a modern browser that supports GPS location.');
            return;
        }

        if (!geocoder) {
            alert('❌ Geocoding service not available. Please refresh the page and try again.');
            return;
        }

        // Explicit user confirmation
        const userConfirm = confirm(
            '📍 Get Your Current Location for Aerator Placement?\n\n' +
            'This will:\n' +
            '• Access your GPS location\n' +
            '• Fill the aerator address search bar\n' +
            '• Center the aerator map on your position\n\n' +
            'Click "OK" to proceed or "Cancel" to skip.'
        );

        if (!userConfirm) {
            console.log('📍 User cancelled aerator location request');
            return;
        }

        // Show loading state
        const btn = document.getElementById('my-location-search-aerator-btn');
        const originalContent = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
        btn.disabled = true;

        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;
                const location = new google.maps.LatLng(lat, lng);

                console.log(`📍 Current location for aerator map: ${lat}, ${lng}`);

                // Reverse geocode to get address
                geocoder.geocode({ location: location }, function(results, status) {
                    if (status === 'OK' && results[0]) {
                        const address = results[0].formatted_address;

                        console.log(`✅ Address found for aerator map: ${address}`);

                        // Fill the address input
                        document.getElementById('address-search-input-aerator').value = address;

                        // Get the aerator map and center it
                        const aeratorMapElement = document.getElementById('aerator-map');
                        if (aeratorMapElement && aeratorMapElement.map) {
                            aeratorMapElement.map.setCenter(location);
                            aeratorMapElement.map.setZoom(19);

                            // Add current location marker
                            const locationMarker = new google.maps.Marker({
                                position: location,
                                map: aeratorMapElement.map,
                                title: 'Your Current Location',
                                icon: {
                                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                        <svg width="25" height="25" viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="12.5" cy="12.5" r="10" fill="#4285f4" stroke="white" stroke-width="2"/>
                                            <circle cx="12.5" cy="12.5" r="5" fill="white"/>
                                            <circle cx="12.5" cy="12.5" r="2" fill="#4285f4"/>
                                        </svg>
                                    `),
                                    scaledSize: new google.maps.Size(25, 25)
                                }
                            });

                            // Remove marker after 6 seconds
                            setTimeout(() => {
                                locationMarker.setMap(null);
                            }, 6000);
                        }

                    } else {
                        console.error('❌ Reverse geocoding failed for aerator map:', status);
                        alert('Unable to get address for your location. Using coordinates only.');

                        // Fill with coordinates if address lookup fails
                        document.getElementById('address-search-input-aerator').value = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
                    }

                    // Reset button
                    btn.innerHTML = originalContent;
                    btn.disabled = false;
                });
            },
            function(error) {
                console.error('❌ Geolocation error for aerator map:', error);
                alert('Unable to get your current location. Please check your location settings.');

                // Reset button
                btn.innerHTML = originalContent;
                btn.disabled = false;
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Debug function to test drawing manager
    function debugDrawingManager() {
        console.log('=== DEBUGGING DRAWING MANAGER ===');
        console.log('Google Maps API available:', typeof google !== 'undefined');
        console.log('Drawing library available:', typeof google?.maps?.drawing !== 'undefined');
        console.log('Drawing Manager available:', typeof google?.maps?.drawing?.DrawingManager !== 'undefined');
        console.log('Map instance:', map);
        console.log('Drawing manager instance:', drawingManager);
        console.log('Drawing manager map:', drawingManager?.getMap());
        console.log('Current drawing mode:', drawingManager?.getDrawingMode());
        console.log('=== END DEBUG ===');
    }

    // Test drawing manager function
    function testDrawingMode() {
        console.log('🧪 Testing drawing mode...');
        if (drawingManager) {
            console.log('✅ Drawing manager exists');
            drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
            console.log('✅ Drawing mode set to POLYGON');
            console.log('Current mode:', drawingManager.getDrawingMode());
        } else {
            console.error('❌ Drawing manager not found');
        }
    }

    // Alternative drawing method using map clicks
    let isDrawing = false;
    let drawingPath = [];
    
    function enableAlternativeDrawing() {
        console.log('🎨 Enabling alternative drawing method...');
        isDrawing = true;
        drawingPath = [];
        
        if (map) {
            // Change cursor to crosshair
            map.setOptions({ draggableCursor: 'crosshair' });
            
            // Add click listener
            const clickListener = google.maps.event.addListener(map, 'click', function(event) {
                if (isDrawing) {
                    console.log('📍 Adding point:', event.latLng.lat(), event.latLng.lng());
                    drawingPath.push({
                        lat: event.latLng.lat(),
                        lng: event.latLng.lng()
                    });
                    
                    // Create/update polygon
                    if (pondPolygon) {
                        pondPolygon.setMap(null);
                    }
                    
                    if (drawingPath.length >= 3) {
                        pondPolygon = new google.maps.Polygon({
                            paths: drawingPath,
                            fillColor: '#667eea',
                            fillOpacity: 0.3,
                            strokeColor: '#667eea',
                            strokeWeight: 3,
                            editable: true,
                            draggable: false
                        });
                        
                        pondPolygon.setMap(map);
                        calculateMeasurements();
                        console.log('✅ Polygon created with', drawingPath.length, 'points');
                    }
                }
            });
            
            // Add double-click to finish
            const dblClickListener = google.maps.event.addListener(map, 'dblclick', function(event) {
                if (isDrawing) {
                    console.log('🏁 Finished drawing');
                    isDrawing = false;
                    map.setOptions({ draggableCursor: 'default' });
                    google.maps.event.removeListener(clickListener);
                    google.maps.event.removeListener(dblClickListener);
                    
                    if (pondPolygon) {
                        // Add listeners for polygon changes
                        google.maps.event.addListener(pondPolygon.getPath(), 'set_at', calculateMeasurements);
                        google.maps.event.addListener(pondPolygon.getPath(), 'insert_at', calculateMeasurements);
                    }
                }
            });
        }
    }
</script>

<!-- Load Google Maps API with Drawing Library -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=drawing,geometry&callback=mapsApiReady"
    onerror="handleMapsError()">
</script>

<script>
    function handleMapsError() {
        console.error('❌ Failed to load Google Maps API');
        const mapElement = document.getElementById('pond-map');
        if (mapElement) {
            mapElement.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #e74c3c;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                    <h3>Failed to Load Google Maps</h3>
                    <p>Please check your internet connection and Google Maps API key.</p>
                    <p style="font-size: 0.9rem; opacity: 0.8;">API Key: {{ google_maps_api_key|default:"Not configured" }}</p>
                </div>
            `;
        }
    }
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
