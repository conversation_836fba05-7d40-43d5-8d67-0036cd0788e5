<!DOCTYPE html>
<html>
<head>
    <title>Simple Maps Test</title>
    <style>
        #map { height: 400px; width: 100%; }
        .debug { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Simple Google Maps Test</h1>
    
    <div class="debug">
        <h3>Debug Info:</h3>
        <p>API Key: {{ google_maps_api_key|slice:":10" }}...</p>
        <p>Page loaded at: <span id="load-time"></span></p>
        <div id="status">Loading...</div>
    </div>
    
    <div id="map"></div>

    <script>
        document.getElementById('load-time').textContent = new Date().toLocaleTimeString();
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
            console.log(message);
        }
        
        window.gm_authFailure = function() {
            updateStatus('❌ Authentication failed - API key invalid');
        };
        
        function initMap() {
            updateStatus('✅ initMap callback triggered');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 10,
                    center: { lat: 13.0827, lng: 80.2707 }
                });
                
                new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location'
                });
                
                updateStatus('✅ Map loaded successfully!');
            } catch (error) {
                updateStatus('❌ Error: ' + error.message);
            }
        }
        
        // Timeout check
        setTimeout(() => {
            if (typeof google === 'undefined') {
                updateStatus('❌ Google Maps API failed to load');
            }
        }, 5000);
    </script>

    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
        onerror="updateStatus('❌ Script loading failed');">
    </script>
</body>
</html>
