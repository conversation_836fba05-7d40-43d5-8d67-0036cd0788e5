/**
 * Dashboard Screen
 * Main dashboard showing task statistics, quick actions, and recent activity
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  RefreshControl,
  Dimensions
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import LinearGradient from 'react-native-linear-gradient';
import moment from 'moment';
import TaskManager, { TaskStatus, TaskPriority } from '../services/TaskManager';
import { useFocusEffect } from '@react-navigation/native';

const { width } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [statistics, setStatistics] = useState({});
  const [todaysTasks, setTodaysTasks] = useState([]);
  const [overdueTasks, setOverdueTasks] = useState([]);
  const [recentTasks, setRecentTasks] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [syncStatus, setSyncStatus] = useState({});

  useFocusEffect(
    React.useCallback(() => {
      loadDashboardData();
    }, [])
  );

  const loadDashboardData = () => {
    try {
      // Load statistics
      const stats = TaskManager.getTaskStatistics();
      setStatistics(stats);

      // Load today's tasks
      const today = TaskManager.getTodaysTasks();
      setTodaysTasks(today);

      // Load overdue tasks
      const overdue = TaskManager.getOverdueTasks();
      setOverdueTasks(overdue);

      // Load recent completed tasks
      const recent = TaskManager.getTasks({ status: TaskStatus.COMPLETED })
        .slice(0, 5);
      setRecentTasks(recent);

      // Load sync status
      const sync = TaskManager.getSyncStatus();
      setSyncStatus(sync);

    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await TaskManager.forceSync();
      loadDashboardData();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const getGreeting = () => {
    const hour = moment().hour();
    if (hour < 12) return 'Good Morning';
    if (hour < 17) return 'Good Afternoon';
    return 'Good Evening';
  };

  const renderHeader = () => (
    <LinearGradient
      colors={['#2196F3', '#1976D2']}
      style={styles.header}
    >
      <View style={styles.headerContent}>
        <View>
          <Text style={styles.greeting}>{getGreeting()}</Text>
          <Text style={styles.userName}>Field Worker</Text>
          <Text style={styles.date}>{moment().format('dddd, MMMM DD')}</Text>
        </View>
        
        <TouchableOpacity 
          style={styles.profileButton}
          onPress={() => navigation.navigate('Profile')}
        >
          <Icon name="person" size={32} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Sync Status */}
      {!syncStatus.isOnline && (
        <View style={styles.offlineIndicator}>
          <Icon name="cloud-off" size={16} color="#fff" />
          <Text style={styles.offlineText}>Working Offline</Text>
        </View>
      )}

      {syncStatus.pendingSync > 0 && (
        <View style={styles.syncIndicator}>
          <Icon name="sync" size={16} color="#fff" />
          <Text style={styles.syncText}>
            {syncStatus.pendingSync} tasks pending sync
          </Text>
        </View>
      )}
    </LinearGradient>
  );

  const renderStatistics = () => (
    <View style={styles.statisticsContainer}>
      <Text style={styles.sectionTitle}>Today's Overview</Text>
      
      <View style={styles.statsGrid}>
        <TouchableOpacity 
          style={[styles.statCard, { backgroundColor: '#E3F2FD' }]}
          onPress={() => navigation.navigate('TaskList', { filter: 'today' })}
        >
          <Icon name="today" size={32} color="#1976D2" />
          <Text style={styles.statNumber}>{todaysTasks.length}</Text>
          <Text style={styles.statLabel}>Today's Tasks</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.statCard, { backgroundColor: '#FFF3E0' }]}
          onPress={() => navigation.navigate('TaskList', { filter: 'in_progress' })}
        >
          <Icon name="play-circle" size={32} color="#F57C00" />
          <Text style={styles.statNumber}>{statistics.inProgress || 0}</Text>
          <Text style={styles.statLabel}>In Progress</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.statCard, { backgroundColor: '#E8F5E8' }]}
          onPress={() => navigation.navigate('TaskList', { filter: 'completed' })}
        >
          <Icon name="check-circle" size={32} color="#388E3C" />
          <Text style={styles.statNumber}>{statistics.completed || 0}</Text>
          <Text style={styles.statLabel}>Completed</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.statCard, { backgroundColor: '#FFEBEE' }]}
          onPress={() => navigation.navigate('TaskList', { filter: 'overdue' })}
        >
          <Icon name="warning" size={32} color="#D32F2F" />
          <Text style={styles.statNumber}>{overdueTasks.length}</Text>
          <Text style={styles.statLabel}>Overdue</Text>
        </TouchableOpacity>
      </View>

      {/* Completion Rate */}
      <View style={styles.completionCard}>
        <View style={styles.completionHeader}>
          <Text style={styles.completionTitle}>Completion Rate</Text>
          <Text style={styles.completionRate}>{statistics.completionRate || 0}%</Text>
        </View>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${statistics.completionRate || 0}%` }
            ]} 
          />
        </View>
        <Text style={styles.completionText}>
          {statistics.completed || 0} of {statistics.total || 0} tasks completed
        </Text>
      </View>
    </View>
  );

  const renderQuickActions = () => (
    <View style={styles.quickActionsContainer}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      
      <View style={styles.actionsGrid}>
        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => navigation.navigate('CreateTask')}
        >
          <Icon name="add-task" size={24} color="#2196F3" />
          <Text style={styles.actionText}>Create Task</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => navigation.navigate('QRScanner')}
        >
          <Icon name="qr-code-scanner" size={24} color="#2196F3" />
          <Text style={styles.actionText}>Scan QR</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => navigation.navigate('WaterQuality')}
        >
          <Icon name="water-drop" size={24} color="#2196F3" />
          <Text style={styles.actionText}>Water Test</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => navigation.navigate('Reports')}
        >
          <Icon name="assessment" size={24} color="#2196F3" />
          <Text style={styles.actionText}>Reports</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderPriorityTasks = () => {
    const priorityTasks = [...todaysTasks, ...overdueTasks]
      .filter(task => task.priority === TaskPriority.URGENT || task.priority === TaskPriority.HIGH)
      .slice(0, 3);

    if (priorityTasks.length === 0) {
      return null;
    }

    return (
      <View style={styles.priorityTasksContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Priority Tasks</Text>
          <TouchableOpacity onPress={() => navigation.navigate('TaskList')}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        {priorityTasks.map((task) => (
          <TouchableOpacity
            key={task.id}
            style={styles.priorityTaskCard}
            onPress={() => navigation.navigate('TaskDetail', { taskId: task.id })}
          >
            <View style={styles.taskCardHeader}>
              <View style={styles.taskInfo}>
                <Text style={styles.taskTitle} numberOfLines={1}>
                  {task.title}
                </Text>
                <Text style={styles.taskTime}>
                  {moment(task.dueDate).format('HH:mm')}
                </Text>
              </View>
              <View style={[
                styles.priorityBadge,
                { backgroundColor: task.priority === TaskPriority.URGENT ? '#D32F2F' : '#F57C00' }
              ]}>
                <Text style={styles.priorityText}>
                  {task.priority.toUpperCase()}
                </Text>
              </View>
            </View>
            
            {task.pondName && (
              <Text style={styles.taskPond}>{task.pondName}</Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderRecentActivity = () => {
    if (recentTasks.length === 0) {
      return null;
    }

    return (
      <View style={styles.recentActivityContainer}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Activity</Text>
          <TouchableOpacity onPress={() => navigation.navigate('TaskList', { filter: 'completed' })}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        {recentTasks.map((task) => (
          <TouchableOpacity
            key={task.id}
            style={styles.activityItem}
            onPress={() => navigation.navigate('TaskDetail', { taskId: task.id })}
          >
            <Icon name="check-circle" size={20} color="#4CAF50" />
            <View style={styles.activityContent}>
              <Text style={styles.activityTitle} numberOfLines={1}>
                {task.title}
              </Text>
              <Text style={styles.activityTime}>
                Completed {moment(task.completedAt).fromNow()}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={['#2196F3']}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderHeader()}
        {renderStatistics()}
        {renderQuickActions()}
        {renderPriorityTasks()}
        {renderRecentActivity()}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  greeting: {
    fontSize: 16,
    color: '#E3F2FD',
    fontWeight: '500',
  },
  userName: {
    fontSize: 24,
    color: '#fff',
    fontWeight: '700',
    marginVertical: 4,
  },
  date: {
    fontSize: 14,
    color: '#E3F2FD',
  },
  profileButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  offlineIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,152,0,0.9)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  offlineText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  syncIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  syncText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  statisticsContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  viewAllText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    width: (width - 60) / 2,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 28,
    fontWeight: '700',
    color: '#333',
    marginVertical: 8,
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    fontWeight: '600',
    textAlign: 'center',
  },
  completionCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  completionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  completionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  completionRate: {
    fontSize: 24,
    fontWeight: '700',
    color: '#4CAF50',
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E0E0E0',
    borderRadius: 4,
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  completionText: {
    fontSize: 12,
    color: '#666',
  },
  quickActionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionCard: {
    width: (width - 60) / 2,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
    marginTop: 8,
  },
  priorityTasksContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  priorityTaskCard: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  taskCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  taskTime: {
    fontSize: 12,
    color: '#666',
  },
  taskPond: {
    fontSize: 12,
    color: '#666',
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '700',
    color: '#fff',
  },
  recentActivityContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  activityContent: {
    flex: 1,
    marginLeft: 12,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  activityTime: {
    fontSize: 12,
    color: '#666',
  },
});

export default DashboardScreen;
