<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicine Categories - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .medicine-icon {
        font-size: 2.5rem;
        opacity: 0.9;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .quick-action i {
        font-size: 1.1rem;
    }
    
    .category-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
    
    .category-card .card-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding: 20px;
    }
    
    .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin: 0 auto 15px;
    }
    
    .medicine-count {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        padding: 5px 12px;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.9rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-tags medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Categories</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Medicine Categories</h2>
                    <p class="mb-0 opacity-75">Organize and manage medicine types</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Total Categories</small>
                                <strong>{{ categories|length|default:"0" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chart-line"></i> Dashboard
                    </a>
                    <a href="/medicine/medicine/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills"></i> Medicines
                    </a>
                </div>
                <div class="d-flex gap-2">
                    <span class="text-muted small">
                        <i class="fas fa-clock"></i>
                        <span id="current-time">Loading...</span>
                    </span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/category/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Create Category
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                View Medicines
            </a>
            <a href="/medicine/dashboard/" class="quick-action">
                <i class="fas fa-chart-line"></i>
                Dashboard
            </a>
        </div>

        <!-- Categories List -->
        <div class="row">
            {% if categories %}
            {% for category in categories %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card category-card h-100">
                    <div class="card-header text-center">
                        <div class="category-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <h5 class="mb-2">{{ category.name }}</h5>
                        <span class="medicine-count">{{ category.medicine_count }} medicine{{ category.medicine_count|pluralize }}</span>
                    </div>
                    <div class="card-body">
                        {% if category.description %}
                        <p class="text-muted mb-3">{{ category.description|truncatechars:120 }}</p>
                        {% else %}
                        <p class="text-muted mb-3 fst-italic">No description provided</p>
                        {% endif %}
                        
                        <div class="mb-3">
                            <small class="text-muted">Created: {{ category.created_at|date:"M d, Y" }}</small>
                        </div>
                        
                        <div class="d-flex flex-wrap gap-2">
                            <a href="/medicine/medicine/?category={{ category.id }}" class="btn btn-primary btn-sm flex-fill">
                                <i class="fas fa-eye me-1"></i> View Medicines
                            </a>
                            <a href="/medicine/category/{{ category.id }}/edit/" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% else %}
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-tags text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5>No Categories Found</h5>
                        <p class="text-muted">You haven't created any medicine categories yet.</p>
                        <a href="/medicine/category/create/" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Create First Category
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }
        
        // Update time immediately and then every minute
        updateTime();
        setInterval(updateTime, 60000);
    </script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
