<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Complete Integration Test - Maps & Labor Management</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        .map-container {
            height: 500px;
            width: 100%;
            border: 3px solid #ddd;
            border-radius: 15px;
            margin: 20px 0;
            position: relative;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 10px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border-left: 5px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 5px solid #dc3545; }
        .warning { background: #fff3cd; color: #856404; border-left: 5px solid #ffc107; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 5px solid #17a2b8; }
        .debug-panel {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        .feature-test {
            margin: 15px 0;
            padding: 15px;
            background: white;
            border-radius: 10px;
            border: 1px solid #ddd;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Complete Integration Test</h1>
            <h2>Google Maps + Labor Management + Pond Mapping</h2>
            <p>Comprehensive validation of the integrated cumulative map dashboard</p>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>🗺️ Google Maps API Test</h3>
                <div id="maps-status">
                    <div class="status info">⏳ Initializing Google Maps API...</div>
                </div>
                <div class="feature-test">
                    <strong>Features to Test:</strong>
                    <ul>
                        <li>✅ API Key Validation</li>
                        <li>⏳ Map Initialization</li>
                        <li>⏳ Marker Placement</li>
                        <li>⏳ Custom Styling</li>
                        <li>⏳ Geofence Rendering</li>
                    </ul>
                </div>
            </div>

            <div class="test-section">
                <h3>👷 Labor Management Integration</h3>
                <div id="labor-status">
                    <div class="status info">⏳ Loading labor management data...</div>
                </div>
                <div class="feature-test">
                    <strong>Features to Test:</strong>
                    <ul>
                        <li>⏳ Worker Location Tracking</li>
                        <li>⏳ Geofence Management</li>
                        <li>⏳ Safety Alerts</li>
                        <li>⏳ Real-time Updates</li>
                        <li>⏳ Location History</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Integrated Map Dashboard</h3>
            <div id="map" class="map-container">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    <div class="loading"></div>
                    <span style="margin-left: 10px;">Loading Google Maps...</span>
                </div>
            </div>
            
            <div class="feature-test">
                <button class="btn" onclick="testFarmMarkers()">🏢 Test Farm Markers</button>
                <button class="btn" onclick="testPondMarkers()">🎣 Test Pond Markers</button>
                <button class="btn" onclick="testWorkerMarkers()">👷 Test Worker Markers</button>
                <button class="btn" onclick="testGeofences()">⭕ Test Geofences</button>
                <button class="btn" onclick="testRealTimeUpdates()">⚡ Test Real-time</button>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-section">
                <h3>📊 Test Results</h3>
                <div id="test-results">
                    <div class="status info">🧪 Tests will appear here as they complete...</div>
                </div>
            </div>

            <div class="test-section">
                <h3>🔧 Debug Console</h3>
                <div id="debug-console" class="debug-panel">
                    🔍 Debug information will appear here...<br>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 Performance Metrics</h3>
            <div id="performance-metrics">
                <div class="feature-test">
                    <strong>Loading Times:</strong>
                    <ul>
                        <li>Google Maps API: <span id="maps-load-time">⏳ Measuring...</span></li>
                        <li>Data Fetching: <span id="data-load-time">⏳ Measuring...</span></li>
                        <li>Marker Rendering: <span id="marker-render-time">⏳ Measuring...</span></li>
                        <li>Total Integration: <span id="total-load-time">⏳ Measuring...</span></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables for testing
        let map;
        let testResults = [];
        let debugConsole = document.getElementById('debug-console');
        let startTime = performance.now();
        let mapsLoadTime, dataLoadTime, markerRenderTime;

        // Debug logging function
        function debugLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#00ff00';
            debugConsole.innerHTML += `<span style="color: ${color};">[${timestamp}] ${message}</span><br>`;
            debugConsole.scrollTop = debugConsole.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Update status function
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="status ${type}">${message}</div>`;
            }
        }

        // Test result tracking
        function addTestResult(testName, status, message) {
            testResults.push({ testName, status, message, timestamp: new Date().toLocaleTimeString() });
            updateTestResults();
        }

        function updateTestResults() {
            const resultsContainer = document.getElementById('test-results');
            let html = '';
            testResults.forEach(result => {
                const statusClass = result.status === 'pass' ? 'success' : result.status === 'fail' ? 'error' : 'warning';
                html += `<div class="status ${statusClass}">[${result.timestamp}] ${result.testName}: ${result.message}</div>`;
            });
            resultsContainer.innerHTML = html;
        }

        // Performance tracking
        function updatePerformanceMetric(metricId, value) {
            const element = document.getElementById(metricId);
            if (element) {
                element.textContent = `${value}ms`;
                element.style.color = value < 1000 ? '#28a745' : value < 3000 ? '#ffc107' : '#dc3545';
            }
        }

        // Google Maps initialization
        function initMap() {
            debugLog('🗺️ Google Maps initMap() callback triggered');
            mapsLoadTime = performance.now() - startTime;
            updatePerformanceMetric('maps-load-time', Math.round(mapsLoadTime));

            try {
                // Check if Google Maps API is available
                if (typeof google === 'undefined' || !google.maps) {
                    throw new Error('Google Maps API not loaded');
                }

                debugLog('✅ Google Maps API available');
                updateStatus('maps-status', '✅ Google Maps API loaded successfully!', 'success');
                addTestResult('Google Maps API', 'pass', 'API loaded and available');

                // Initialize map
                const mapOptions = {
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai coordinates
                    zoom: 12,
                    mapTypeId: google.maps.MapTypeId.ROADMAP,
                    styles: [
                        {
                            featureType: 'water',
                            elementType: 'geometry',
                            stylers: [{ color: '#193d7e' }]
                        },
                        {
                            featureType: 'landscape',
                            elementType: 'geometry',
                            stylers: [{ color: '#f5f5f5' }]
                        }
                    ]
                };

                map = new google.maps.Map(document.getElementById('map'), mapOptions);
                debugLog('✅ Map initialized successfully');
                addTestResult('Map Initialization', 'pass', 'Map created and styled');

                // Test basic marker
                const testMarker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location',
                    icon: {
                        url: 'data:image/svg+xml,' + encodeURIComponent(`
                            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                <circle cx="15" cy="15" r="12" fill="#667eea" stroke="#fff" stroke-width="3"/>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="16">📍</text>
                            </svg>
                        `)
                    }
                });

                debugLog('✅ Test marker added successfully');
                addTestResult('Marker Creation', 'pass', 'Basic marker rendered');

                // Start data loading simulation
                simulateDataLoading();

            } catch (error) {
                debugLog(`❌ Map initialization error: ${error.message}`, 'error');
                updateStatus('maps-status', `❌ Map initialization failed: ${error.message}`, 'error');
                addTestResult('Google Maps API', 'fail', error.message);
            }
        }

        // Simulate loading labor management data
        function simulateDataLoading() {
            debugLog('🔄 Starting data loading simulation...');
            const dataStartTime = performance.now();

            setTimeout(() => {
                dataLoadTime = performance.now() - dataStartTime;
                updatePerformanceMetric('data-load-time', Math.round(dataLoadTime));
                
                debugLog('✅ Mock data loaded successfully');
                updateStatus('labor-status', '✅ Labor management data loaded!', 'success');
                addTestResult('Data Loading', 'pass', 'Mock worker and geofence data loaded');

                simulateMarkerRendering();
            }, 800);
        }

        // Simulate marker rendering
        function simulateMarkerRendering() {
            debugLog('🎯 Starting marker rendering simulation...');
            const markerStartTime = performance.now();

            setTimeout(() => {
                markerRenderTime = performance.now() - markerStartTime;
                updatePerformanceMetric('marker-render-time', Math.round(markerRenderTime));
                
                const totalTime = performance.now() - startTime;
                updatePerformanceMetric('total-load-time', Math.round(totalTime));

                debugLog('✅ All markers rendered successfully');
                addTestResult('Marker Rendering', 'pass', 'All farm, pond, and worker markers rendered');
                addTestResult('Complete Integration', 'pass', `Total load time: ${Math.round(totalTime)}ms`);
            }, 500);
        }

        // Test functions for manual testing
        function testFarmMarkers() {
            debugLog('🏢 Testing farm markers...');
            if (map) {
                // Simulate adding farm markers
                const farms = [
                    { lat: 13.0827, lng: 80.2707, name: 'Main Farm' },
                    { lat: 13.0927, lng: 80.2807, name: 'North Farm' }
                ];

                farms.forEach((farm, index) => {
                    setTimeout(() => {
                        const marker = new google.maps.Marker({
                            position: { lat: farm.lat, lng: farm.lng },
                            map: map,
                            title: farm.name,
                            icon: {
                                url: 'data:image/svg+xml,' + encodeURIComponent(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">
                                        <rect x="5" y="5" width="30" height="30" fill="#28a745" stroke="#fff" stroke-width="2" rx="5"/>
                                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="20">🏢</text>
                                    </svg>
                                `)
                            }
                        });
                        debugLog(`✅ Farm marker added: ${farm.name}`);
                    }, index * 300);
                });

                addTestResult('Farm Markers', 'pass', `${farms.length} farm markers added`);
            } else {
                addTestResult('Farm Markers', 'fail', 'Map not initialized');
            }
        }

        function testPondMarkers() {
            debugLog('🎣 Testing pond markers...');
            if (map) {
                const ponds = [
                    { lat: 13.0827, lng: 80.2707, name: 'Pond A1' },
                    { lat: 13.0847, lng: 80.2727, name: 'Pond A2' },
                    { lat: 13.0867, lng: 80.2747, name: 'Pond B1' }
                ];

                ponds.forEach((pond, index) => {
                    setTimeout(() => {
                        const marker = new google.maps.Marker({
                            position: { lat: pond.lat, lng: pond.lng },
                            map: map,
                            title: pond.name,
                            icon: {
                                url: 'data:image/svg+xml,' + encodeURIComponent(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 35 35">
                                        <circle cx="17.5" cy="17.5" r="15" fill="#17a2b8" stroke="#fff" stroke-width="2"/>
                                        <text x="17.5" y="22" text-anchor="middle" fill="white" font-size="16">🎣</text>
                                    </svg>
                                `)
                            }
                        });
                        debugLog(`✅ Pond marker added: ${pond.name}`);
                    }, index * 200);
                });

                addTestResult('Pond Markers', 'pass', `${ponds.length} pond markers added`);
            } else {
                addTestResult('Pond Markers', 'fail', 'Map not initialized');
            }
        }

        function testWorkerMarkers() {
            debugLog('👷 Testing worker markers...');
            if (map) {
                const workers = [
                    { lat: 13.0837, lng: 80.2717, name: 'Worker John', status: 'active' },
                    { lat: 13.0857, lng: 80.2737, name: 'Worker Mary', status: 'break' },
                    { lat: 13.0877, lng: 80.2757, name: 'Worker Bob', status: 'active' }
                ];

                workers.forEach((worker, index) => {
                    setTimeout(() => {
                        const color = worker.status === 'active' ? '#28a745' : '#ffc107';
                        const marker = new google.maps.Marker({
                            position: { lat: worker.lat, lng: worker.lng },
                            map: map,
                            title: `${worker.name} (${worker.status})`,
                            icon: {
                                url: 'data:image/svg+xml,' + encodeURIComponent(`
                                    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30">
                                        <circle cx="15" cy="15" r="12" fill="${color}" stroke="#fff" stroke-width="2"/>
                                        <text x="15" y="20" text-anchor="middle" fill="white" font-size="14">👷</text>
                                    </svg>
                                `)
                            }
                        });
                        debugLog(`✅ Worker marker added: ${worker.name} (${worker.status})`);
                    }, index * 250);
                });

                addTestResult('Worker Markers', 'pass', `${workers.length} worker markers added`);
            } else {
                addTestResult('Worker Markers', 'fail', 'Map not initialized');
            }
        }

        function testGeofences() {
            debugLog('⭕ Testing geofences...');
            if (map) {
                const geofences = [
                    { lat: 13.0827, lng: 80.2707, radius: 100, name: 'Safety Zone A' },
                    { lat: 13.0867, lng: 80.2747, radius: 150, name: 'Restricted Area B' }
                ];

                geofences.forEach((fence, index) => {
                    setTimeout(() => {
                        const circle = new google.maps.Circle({
                            strokeColor: '#dc3545',
                            strokeOpacity: 0.8,
                            strokeWeight: 2,
                            fillColor: '#dc3545',
                            fillOpacity: 0.15,
                            map: map,
                            center: { lat: fence.lat, lng: fence.lng },
                            radius: fence.radius
                        });
                        debugLog(`✅ Geofence added: ${fence.name} (${fence.radius}m radius)`);
                    }, index * 400);
                });

                addTestResult('Geofences', 'pass', `${geofences.length} geofences rendered`);
            } else {
                addTestResult('Geofences', 'fail', 'Map not initialized');
            }
        }

        function testRealTimeUpdates() {
            debugLog('⚡ Testing real-time updates...');
            let updateCount = 0;
            const maxUpdates = 5;

            const updateInterval = setInterval(() => {
                updateCount++;
                debugLog(`📡 Real-time update ${updateCount}/${maxUpdates}`);

                if (updateCount >= maxUpdates) {
                    clearInterval(updateInterval);
                    debugLog('✅ Real-time update simulation complete');
                    addTestResult('Real-time Updates', 'pass', `${maxUpdates} updates simulated`);
                }
            }, 1000);
        }

        // Global error handlers
        window.onerror = function(message, source, lineno, colno, error) {
            debugLog(`🚨 JavaScript Error: ${message} at ${source}:${lineno}`, 'error');
            return false;
        };

        window.gm_authFailure = function() {
            debugLog('🔑 Google Maps Authentication Failed!', 'error');
            updateStatus('maps-status', '🔑 Google Maps Authentication Failed!', 'error');
            addTestResult('Google Maps API', 'fail', 'Authentication failure - check API key');
        };

        // Initialize test
        debugLog('🧪 Complete Integration Test Started');
        debugLog(`🔍 API Key: {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}`);
        debugLog(`🌐 Current URL: ${window.location.href}`);
        debugLog(`⏰ Test started at: ${new Date().toLocaleString()}`);
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onload="debugLog('✅ Google Maps script loaded successfully')"
        onerror="debugLog('❌ Failed to load Google Maps script', 'error'); updateStatus('maps-status', '❌ Failed to load Google Maps script', 'error');">
    </script>
</body>
</html>
