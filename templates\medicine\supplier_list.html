<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suppliers - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-truck medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Suppliers</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Medicine Suppliers</h2>
                    <p class="mb-0 opacity-75">Manage medicine suppliers and vendors</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Total Suppliers</small>
                                <strong>{{ total_suppliers|default:"0" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/medicine/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to Medicines
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/supplier/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Add Supplier
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                Medicines
            </a>
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-clipboard-list"></i>
                Applications
            </a>
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-tags"></i>
                Categories
            </a>
        </div>

        <div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-truck-loading text-primary me-2" style="font-size: 1.5rem;"></i>
            <h1 class="h3 mb-0">Suppliers</h1>
        </div>
        <div>
            <a href="/medicine/supplier/create/" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Supplier
            </a>
            <a href="/medicine/dashboard/" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">Total Suppliers</h5>
                    <h2 class="mb-0">{{ total_suppliers }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-light">
                <div class="card-body text-center">
                    <h5 class="card-title">Active Suppliers</h5>
                    <h2 class="mb-0">{{ active_suppliers }}</h2>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="d-flex">
                        <div class="input-group me-2">
                            <input type="text" name="search" class="form-control" placeholder="Search suppliers..." value="{{ search_query }}">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <select name="is_active" class="form-select" style="max-width: 150px;" onchange="this.form.submit()">
                            <option value="">All Suppliers</option>
                            <option value="true" {% if is_active == 'true' %}selected{% endif %}>Active Only</option>
                            <option value="false" {% if is_active == 'false' %}selected{% endif %}>Inactive Only</option>
                        </select>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            {% if suppliers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Contact Person</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Rating</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for supplier in suppliers %}
                                <tr>
                                    <td>
                                        <a href="/medicine/supplier/{{ supplier.id }}/" class="fw-bold text-decoration-none">
                                            {{ supplier.name }}
                                        </a>
                                    </td>
                                    <td>{{ supplier.contact_person|default:"-" }}</td>
                                    <td>
                                        {% if supplier.email %}
                                            <a href="mailto:{{ supplier.email }}" class="text-decoration-none">
                                                {{ supplier.email }}
                                            </a>
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>{{ supplier.phone|default:"-" }}</td>
                                    <td>
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= supplier.reliability_rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% if supplier.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="/medicine/supplier/{{ supplier.id }}/" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/medicine/supplier/{{ supplier.id }}/edit/" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/medicine/order/create/?supplier_id={{ supplier.id }}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-shopping-cart"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-truck-loading text-muted mb-3" style="font-size: 3rem;"></i>
                    <p class="mb-0">No suppliers found.</p>
                    {% if search_query %}
                        <p class="text-muted">Try a different search term or <a href="/medicine/supplier/">view all suppliers</a>.</p>
                    {% else %}
                        <p class="text-muted">Get started by <a href="/medicine/supplier/create/">adding a supplier</a>.</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
