<!DOCTYPE html>
<html>
<head>
    <title>Test Enhanced Map</title>
    <style>
        #map {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 15px;
        }
        
        .loading {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 500px;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-radius: 15px;
            color: #6b7280;
            font-size: 1.2rem;
        }
        
        .error {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 500px;
            background: #fee;
            border-radius: 15px;
            color: #c53030;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <h1>Enhanced Cumulative Map Test</h1>
    
    <div id="map" class="loading">
        <div>
            <i class="fas fa-spinner fa-spin"></i>
            <div>Loading Enhanced Map...</div>
            <div style="font-size: 0.9rem; color: #9ca3af; margin-top: 10px;">
                Initializing labor tracking and weather integration...
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 Starting enhanced map test...');
        
        // Test data
        const centerLat = 13.0827;
        const centerLng = 80.2707;
        let map;
        
        function initMap() {
            console.log('🗺️ Google Maps API loaded, initializing enhanced map...');
            
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                console.error('❌ Map container not found');
                return;
            }
            
            // Force map container dimensions
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            mapElement.style.minHeight = '500px';
            mapElement.style.display = 'block';
            console.log('📏 Map container size set to:', mapElement.style.height);

            // Check if we have valid coordinates
            if (isNaN(centerLat) || isNaN(centerLng)) {
                console.error('❌ Invalid center coordinates');
                mapElement.innerHTML = '<div class="error"><div><i class="fas fa-exclamation-triangle"></i><br>Invalid map coordinates</div></div>';
                return;
            }

            try {
                // Create the enhanced map
                map = new google.maps.Map(mapElement, {
                    zoom: 12,
                    center: { lat: centerLat, lng: centerLng },
                    mapTypeId: 'roadmap',
                    styles: [
                        {
                            featureType: 'poi',
                            elementType: 'labels',
                            stylers: [{ visibility: 'off' }]
                        },
                        {
                            featureType: 'water',
                            elementType: 'geometry',
                            stylers: [{ color: '#76c7c0' }]
                        }
                    ]
                });

                console.log('✅ Enhanced map created successfully');
                
                // Remove loading class and clear loading content
                mapElement.classList.remove('loading');
                mapElement.innerHTML = '';
                
                // Add a test marker
                const marker = new google.maps.Marker({
                    position: { lat: centerLat, lng: centerLng },
                    map: map,
                    title: 'Test Location'
                });
                
                // Force Google Maps to recognize the container size
                setTimeout(() => {
                    google.maps.event.trigger(map, 'resize');
                    console.log('🔄 Map resize triggered');
                }, 500);
                
                console.log('✅ Enhanced map initialization complete');
                
            } catch (error) {
                console.error('❌ Error initializing enhanced map:', error);
                mapElement.classList.remove('loading');
                mapElement.innerHTML = '<div class="error"><div><i class="fas fa-exclamation-triangle"></i><br>Failed to load enhanced map: ' + error.message + '</div></div>';
            }
        }
        
        function handleMapError() {
            console.error('❌ Failed to load Google Maps');
            const mapElement = document.getElementById('map');
            if (mapElement) {
                mapElement.classList.remove('loading');
                mapElement.innerHTML = `
                    <div class="error">
                        <div>
                            <i class="fas fa-exclamation-triangle"></i><br>
                            Failed to load Enhanced Google Maps<br>
                            <small>Please check your internet connection and refresh the page</small>
                        </div>
                    </div>
                `;
            }
        }
        
        // Timeout fallback if Google Maps API doesn't load within 10 seconds
        setTimeout(() => {
            const mapElement = document.getElementById('map');
            if (mapElement && mapElement.classList.contains('loading')) {
                console.warn('⏰ Google Maps API loading timeout');
                handleMapError();
            }
        }, 10000);
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
