{% extends 'base.html' %}
{% load static %}

{% block title %}Theme System Test - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
.demo-section {
    margin-bottom: var(--spacing-xl);
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.color-swatch {
    width: 100%;
    height: 60px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.component-showcase {
    padding: var(--spacing-lg);
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}
</style>
{% endblock %}

{% block content %}
<div class="main-content">
    <div class="page-header">
        <div>
            <h1 class="page-title">Unified Theme System Test</h1>
            <p class="page-subtitle">Testing all theme components and functionality</p>
        </div>
        {% include 'components/unified_theme_toggle.html' %}
    </div>

    <!-- Color Palette Demo -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Color Palette</h2>
        <div class="demo-grid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Primary Colors</h3>
                </div>
                <div class="card-body">
                    <div class="color-swatch" style="background: var(--gradient-primary);">Primary Gradient</div>
                    <div class="color-swatch" style="background: var(--primary-color);">Primary Color</div>
                    <div class="color-swatch" style="background: var(--secondary-color);">Secondary Color</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Status Colors</h3>
                </div>
                <div class="card-body">
                    <div class="color-swatch" style="background: var(--status-success);">Success</div>
                    <div class="color-swatch" style="background: var(--status-warning);">Warning</div>
                    <div class="color-swatch" style="background: var(--status-danger);">Danger</div>
                    <div class="color-swatch" style="background: var(--status-info);">Info</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Button Components -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Button Components</h2>
        <div class="card">
            <div class="card-body">
                <div class="d-flex flex-wrap gap-3 mb-3">
                    <button class="btn btn-primary">Primary Button</button>
                    <button class="btn btn-success">Success Button</button>
                    <button class="btn btn-warning">Warning Button</button>
                    <button class="btn btn-danger">Danger Button</button>
                </div>
                <div class="d-flex flex-wrap gap-3">
                    <button class="btn btn-outline-primary">Outline Primary</button>
                    <button class="btn btn-outline-primary" disabled>Disabled Button</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Form Components -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Form Components</h2>
        <div class="demo-grid">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Input Fields</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Text Input</label>
                        <input type="text" class="form-control" placeholder="Enter text here">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Dropdown</label>
                        <select class="form-select">
                            <option>Option 1</option>
                            <option>Option 2</option>
                            <option>Option 3</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Textarea</label>
                        <textarea class="form-control" rows="3" placeholder="Enter description"></textarea>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Status Indicators</h3>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <span class="status-indicator status-active"></span>
                        <span class="text-success">Active Status</span>
                    </div>
                    <div class="mb-3">
                        <span class="status-indicator status-warning"></span>
                        <span class="text-warning">Warning Status</span>
                    </div>
                    <div class="mb-3">
                        <span class="status-indicator status-inactive"></span>
                        <span class="text-danger">Inactive Status</span>
                    </div>
                    <div class="d-flex flex-wrap gap-2">
                        <span class="badge badge-success">Success</span>
                        <span class="badge badge-warning">Warning</span>
                        <span class="badge badge-danger">Danger</span>
                        <span class="badge badge-info">Info</span>
                        <span class="badge badge-primary">Primary</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Components -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Alert Components</h2>
        <div class="alert alert-success">
            <strong>Success!</strong> This is a success alert message.
        </div>
        <div class="alert alert-warning">
            <strong>Warning!</strong> This is a warning alert message.
        </div>
        <div class="alert alert-danger">
            <strong>Error!</strong> This is an error alert message.
        </div>
        <div class="alert alert-info">
            <strong>Info!</strong> This is an info alert message.
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Statistics Cards</h2>
        <div class="demo-grid">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-water"></i>
                </div>
                <div class="stats-value">24</div>
                <div class="stats-label">Active Ponds</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-fish"></i>
                </div>
                <div class="stats-value">1,250</div>
                <div class="stats-label">Total Shrimp</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="stats-value">28°C</div>
                <div class="stats-label">Avg Temperature</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stats-value">95%</div>
                <div class="stats-label">Health Score</div>
            </div>
        </div>
    </div>

    <!-- Table Component -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Table Component</h2>
        <div class="table-container">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Pond ID</th>
                        <th>Status</th>
                        <th>Temperature</th>
                        <th>pH Level</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>POND-001</td>
                        <td><span class="badge badge-success">Active</span></td>
                        <td>28.5°C</td>
                        <td>7.2</td>
                        <td>
                            <button class="btn btn-primary btn-sm">View</button>
                            <button class="btn btn-outline-primary btn-sm">Edit</button>
                        </td>
                    </tr>
                    <tr>
                        <td>POND-002</td>
                        <td><span class="badge badge-warning">Maintenance</span></td>
                        <td>26.8°C</td>
                        <td>7.0</td>
                        <td>
                            <button class="btn btn-primary btn-sm">View</button>
                            <button class="btn btn-outline-primary btn-sm">Edit</button>
                        </td>
                    </tr>
                    <tr>
                        <td>POND-003</td>
                        <td><span class="badge badge-danger">Alert</span></td>
                        <td>31.2°C</td>
                        <td>6.8</td>
                        <td>
                            <button class="btn btn-primary btn-sm">View</button>
                            <button class="btn btn-outline-primary btn-sm">Edit</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Test Theme Switching -->
    <div class="demo-section">
        <h2 class="text-primary mb-3">Theme Testing</h2>
        <div class="card">
            <div class="card-body">
                <p>Use the theme toggle in the header to switch between light and dark themes. All components should adapt seamlessly.</p>
                <div class="d-flex gap-3">
                    <button class="btn btn-primary" onclick="themeManager.setTheme('dark')">Force Dark Theme</button>
                    <button class="btn btn-outline-primary" onclick="themeManager.setTheme('light')">Force Light Theme</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
