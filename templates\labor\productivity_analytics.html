<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 Productivity Analytics - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .analytics-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .analytics-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            color: white;
            text-align: center;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 1rem;
            color: #666;
            margin-bottom: 15px;
        }

        .metric-change {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .metric-change.positive { color: #4CAF50; }
        .metric-change.negative { color: #f44336; }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #333;
        }

        .worker-performance-table {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .performance-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .badge-excellent { background: #E8F5E8; color: #4CAF50; }
        .badge-good { background: #FFF3E0; color: #FF9800; }
        .badge-average { background: #E3F2FD; color: #2196F3; }
        .badge-poor { background: #FFEBEE; color: #f44336; }

        .filter-controls {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .btn-filter {
            margin: 5px;
            border-radius: 20px;
            padding: 8px 16px;
            border: 2px solid #e0e0e0;
            background: white;
            color: #666;
            transition: all 0.3s ease;
        }

        .btn-filter.active {
            background: #2196F3;
            color: white;
            border-color: #2196F3;
        }

        .productivity-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .indicator-bar {
            flex: 1;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
        }

        .indicator-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .real-time-updates {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            font-size: 0.9rem;
            z-index: 1000;
            display: none;
        }

        .update-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <div class="analytics-container">
        <div class="analytics-header">
            <h1 class="display-4 fw-bold mb-3">📊 Productivity Analytics Dashboard</h1>
            <p class="lead mb-0">Real-time worker performance and efficiency metrics</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">Live Data</span>
                <span class="badge bg-light text-dark me-2">Last Updated: <span id="lastUpdate">just now</span></span>
                <span class="badge bg-light text-dark">Auto-refresh: ON</span>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="filter-controls">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-2">📅 Time Period</h6>
                    <button class="btn-filter active" onclick="setTimePeriod('today')">Today</button>
                    <button class="btn-filter" onclick="setTimePeriod('week')">This Week</button>
                    <button class="btn-filter" onclick="setTimePeriod('month')">This Month</button>
                </div>
                <div class="col-md-6">
                    <h6 class="mb-2">🎯 Metrics View</h6>
                    <button class="btn-filter active" onclick="setMetricsView('overview')">Overview</button>
                    <button class="btn-filter" onclick="setMetricsView('detailed')">Detailed</button>
                    <button class="btn-filter" onclick="setMetricsView('comparison')">Comparison</button>
                </div>
            </div>
        </div>

        <!-- Key Metrics Row -->
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-primary" id="avgEfficiency">87%</div>
                    <div class="metric-label">Average Efficiency</div>
                    <div class="metric-change positive">↗ +5.2% from yesterday</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-success" id="tasksCompleted">142</div>
                    <div class="metric-label">Tasks Completed</div>
                    <div class="metric-change positive">↗ +12 from yesterday</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-warning" id="avgTaskTime">23m</div>
                    <div class="metric-label">Avg Task Time</div>
                    <div class="metric-change negative">↘ +2m from yesterday</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value text-info" id="activeWorkers">12</div>
                    <div class="metric-label">Active Workers</div>
                    <div class="metric-change positive">↗ +2 from yesterday</div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row">
            <div class="col-md-8">
                <div class="chart-container">
                    <h3 class="chart-title">📈 Productivity Trends (Last 7 Days)</h3>
                    <canvas id="productivityChart" height="100"></canvas>
                </div>
            </div>
            <div class="col-md-4">
                <div class="chart-container">
                    <h3 class="chart-title">🎯 Task Distribution</h3>
                    <canvas id="taskDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Worker Performance Table -->
        <div class="worker-performance-table">
            <h3 class="chart-title">👥 Individual Worker Performance</h3>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Worker</th>
                            <th>Tasks Today</th>
                            <th>Efficiency</th>
                            <th>Avg Time/Task</th>
                            <th>Performance</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="workerPerformanceTable">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Real-time Updates Indicator -->
    <div class="real-time-updates" id="realTimeIndicator">
        <span class="update-dot"></span>
        <span id="updateMessage">Updating productivity metrics...</span>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Productivity Analytics System
        let currentTimePeriod = 'today';
        let currentMetricsView = 'overview';
        let productivityChart, taskChart;
        
        // Sample worker data
        const workerData = [
            { id: 'FW001', name: 'Rajesh Kumar', tasksToday: 18, efficiency: 92, avgTime: 21, performance: 'excellent', status: 'active' },
            { id: 'FW002', name: 'Priya Sharma', tasksToday: 15, efficiency: 88, avgTime: 25, performance: 'good', status: 'active' },
            { id: 'FW003', name: 'Amit Singh', tasksToday: 12, efficiency: 85, avgTime: 28, performance: 'good', status: 'break' },
            { id: 'FW004', name: 'Sunita Devi', tasksToday: 20, efficiency: 95, avgTime: 19, performance: 'excellent', status: 'active' },
            { id: 'FW005', name: 'Ravi Patel', tasksToday: 14, efficiency: 82, avgTime: 30, performance: 'average', status: 'active' },
            { id: 'FW006', name: 'Meera Joshi', tasksToday: 16, efficiency: 90, avgTime: 22, performance: 'excellent', status: 'active' }
        ];

        function initializeCharts() {
            // Productivity Trends Chart
            const ctx1 = document.getElementById('productivityChart').getContext('2d');
            productivityChart = new Chart(ctx1, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Efficiency %',
                        data: [82, 85, 88, 87, 90, 89, 87],
                        borderColor: '#2196F3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Tasks Completed',
                        data: [120, 135, 142, 138, 155, 148, 142],
                        borderColor: '#4CAF50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        fill: true,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: { display: true, text: 'Efficiency %' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: 'Tasks' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });

            // Task Distribution Chart
            const ctx2 = document.getElementById('taskDistributionChart').getContext('2d');
            taskChart = new Chart(ctx2, {
                type: 'doughnut',
                data: {
                    labels: ['Water Quality', 'Feeding', 'Maintenance', 'Monitoring', 'Cleaning'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: ['#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336']
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        function updateWorkerTable() {
            const tbody = document.getElementById('workerPerformanceTable');
            tbody.innerHTML = workerData.map(worker => `
                <tr>
                    <td>
                        <strong>${worker.name}</strong><br>
                        <small class="text-muted">${worker.id}</small>
                    </td>
                    <td>${worker.tasksToday}</td>
                    <td>
                        <div class="productivity-indicator">
                            <span>${worker.efficiency}%</span>
                            <div class="indicator-bar">
                                <div class="indicator-fill" style="width: ${worker.efficiency}%; background: ${getEfficiencyColor(worker.efficiency)};"></div>
                            </div>
                        </div>
                    </td>
                    <td>${worker.avgTime}m</td>
                    <td><span class="performance-badge badge-${worker.performance}">${worker.performance.toUpperCase()}</span></td>
                    <td><span class="badge ${worker.status === 'active' ? 'bg-success' : 'bg-warning'}">${worker.status.toUpperCase()}</span></td>
                </tr>
            `).join('');
        }

        function getEfficiencyColor(efficiency) {
            if (efficiency >= 90) return '#4CAF50';
            if (efficiency >= 80) return '#FF9800';
            if (efficiency >= 70) return '#2196F3';
            return '#f44336';
        }

        function setTimePeriod(period) {
            currentTimePeriod = period;
            document.querySelectorAll('.btn-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            showRealTimeUpdate(`Updating data for ${period}...`);
            setTimeout(() => {
                updateMetrics();
                hideRealTimeUpdate();
            }, 1500);
        }

        function setMetricsView(view) {
            currentMetricsView = view;
            document.querySelectorAll('.btn-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            showRealTimeUpdate(`Switching to ${view} view...`);
            setTimeout(hideRealTimeUpdate, 1000);
        }

        function updateMetrics() {
            // Simulate real-time metric updates
            const metrics = {
                efficiency: Math.floor(Math.random() * 10) + 85,
                tasks: Math.floor(Math.random() * 20) + 130,
                avgTime: Math.floor(Math.random() * 5) + 20,
                workers: Math.floor(Math.random() * 3) + 10
            };

            document.getElementById('avgEfficiency').textContent = `${metrics.efficiency}%`;
            document.getElementById('tasksCompleted').textContent = metrics.tasks;
            document.getElementById('avgTaskTime').textContent = `${metrics.avgTime}m`;
            document.getElementById('activeWorkers').textContent = metrics.workers;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function showRealTimeUpdate(message) {
            const indicator = document.getElementById('realTimeIndicator');
            document.getElementById('updateMessage').textContent = message;
            indicator.style.display = 'block';
        }

        function hideRealTimeUpdate() {
            document.getElementById('realTimeIndicator').style.display = 'none';
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            updateWorkerTable();
            
            // Auto-update every 30 seconds
            setInterval(() => {
                showRealTimeUpdate('Refreshing productivity data...');
                setTimeout(() => {
                    updateMetrics();
                    hideRealTimeUpdate();
                }, 2000);
            }, 30000);
        });
    </script>
</body>
</html>
