<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Design System - Component Showcase</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/css/design-system.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Inter Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family:Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-8);
        }
        
        .showcase-header {
            text-align: center;
            margin-bottom: var(--space-16);
            padding: var(--space-12);
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            color: white;
            border-radius: var(--radius-3xl);
            position: relative;
            overflow: hidden;
        }
        
        .showcase-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            animation: float 20s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .showcase-title {
            font-size: var(--font-size-5xl);
            font-weight: var(--font-weight-extrabold);
            margin-bottom: var(--space-4);
            position: relative;
            z-index: 2;
        }
        
        .showcase-subtitle {
            font-size: var(--font-size-xl);
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .section {
            margin-bottom: var(--space-16);
        }
        
        .section-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin-bottom: var(--space-8);
            color: var(--on-background);
            border-bottom: 2px solid var(--primary-200);
            padding-bottom: var(--space-4);
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-8);
            margin-bottom: var(--space-12);
        }
        
        .component-demo {
            background: var(--surface);
            border: 1px solid var(--secondary-200);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-base);
        }
        
        .component-demo:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .demo-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            margin-bottom: var(--space-4);
            color: var(--primary-700);
        }
        
        .demo-content {
            margin-bottom: var(--space-4);
        }
        
        .demo-code {
            background: var(--secondary-100);
            border: 1px solid var(--secondary-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            font-family: var(--font-family-mono);
            font-size: var(--font-size-sm);
            color: var(--secondary-700);
            overflow-x: auto;
        }
        
        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-4);
        }
        
        .color-swatch {
            text-align: center;
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }
        
        .color-preview {
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-weight-semibold);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        
        .color-info {
            padding: var(--space-3);
            background: var(--surface);
            font-size: var(--font-size-sm);
        }
        
        .typography-demo {
            margin-bottom: var(--space-6);
        }
        
        .spacing-demo {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            margin-bottom: var(--space-4);
        }
        
        .spacing-box {
            background: var(--primary-100);
            border: 2px dashed var(--primary-300);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--primary-700);
        }
        
        .animation-demo {
            display: flex;
            gap: var(--space-4);
            flex-wrap: wrap;
        }
        
        .animation-box {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-700));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: var(--font-weight-bold);
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .theme-controls {
            position: fixed;
            top: var(--space-6);
            right: var(--space-6);
            z-index: 1000;
            display: flex;
            gap: var(--space-2);
        }
        
        @media (max-width: 768px) {
            .showcase-container {
                padding: var(--space-4);
            }
            
            .component-grid {
                grid-template-columns: 1fr;
            }
            
            .theme-controls {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: var(--space-8);
            }
        }
    </style>
</head>
<body>

<!-- Theme Controls -->
<div class="theme-controls">
    <div id="theme-toggle-container"></div>
    <button class="btn btn-secondary" onclick="toggleAnimations()">
        <i class="fas fa-magic" id="animation-icon"></i>
    </button>
</div>

<div class="showcase-container">
    <!-- Header -->
    <div class="showcase-header animate-fade-in-up">
        <h1 class="showcase-title">🎨 Design System</h1>
        <p class="showcase-subtitle">Modern UI/UX components with accessibility and dark mode support</p>
    </div>

    <!-- Color System -->
    <section class="section" data-animate="fade-in-up" data-delay="100">
        <h2 class="section-title">Color Palette</h2>
        
        <div class="color-palette">
            <div class="color-swatch">
                <div class="color-preview" style="background: var(--primary-600);">Primary</div>
                <div class="color-info">
                    <div class="font-semibold">Primary 600</div>
                    <div class="text-sm text-secondary">#0284c7</div>
                </div>
            </div>
            
            <div class="color-swatch">
                <div class="color-preview" style="background: var(--success-600);">Success</div>
                <div class="color-info">
                    <div class="font-semibold">Success 600</div>
                    <div class="text-sm text-secondary">#16a34a</div>
                </div>
            </div>
            
            <div class="color-swatch">
                <div class="color-preview" style="background: var(--warning-500);">Warning</div>
                <div class="color-info">
                    <div class="font-semibold">Warning 500</div>
                    <div class="text-sm text-secondary">#f59e0b</div>
                </div>
            </div>
            
            <div class="color-swatch">
                <div class="color-preview" style="background: var(--danger-600);">Danger</div>
                <div class="color-info">
                    <div class="font-semibold">Danger 600</div>
                    <div class="text-sm text-secondary">#dc2626</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Typography -->
    <section class="section" data-animate="fade-in-up" data-delay="200">
        <h2 class="section-title">Typography</h2>
        
        <div class="component-grid">
            <div class="component-demo">
                <div class="demo-title">Headings</div>
                <div class="demo-content">
                    <h1 class="text-5xl font-extrabold mb-2">Heading 1</h1>
                    <h2 class="text-4xl font-bold mb-2">Heading 2</h2>
                    <h3 class="text-3xl font-semibold mb-2">Heading 3</h3>
                    <h4 class="text-2xl font-medium mb-2">Heading 4</h4>
                    <h5 class="text-xl font-medium mb-2">Heading 5</h5>
                    <h6 class="text-lg font-medium">Heading 6</h6>
                </div>
            </div>
            
            <div class="component-demo">
                <div class="demo-title">Body Text</div>
                <div class="demo-content">
                    <p class="text-lg mb-4">Large body text for important content and introductions.</p>
                    <p class="text-base mb-4">Regular body text for most content. This is the default size.</p>
                    <p class="text-sm mb-4">Small text for captions, labels, and secondary information.</p>
                    <p class="text-xs">Extra small text for fine print and metadata.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Buttons -->
    <section class="section" data-animate="fade-in-up" data-delay="300">
        <h2 class="section-title">Buttons</h2>
        
        <div class="component-grid">
            <div class="component-demo">
                <div class="demo-title">Button Variants</div>
                <div class="demo-content">
                    <div class="flex flex-wrap gap-3 mb-4">
                        <button class="btn btn-primary" data-ripple>Primary</button>
                        <button class="btn btn-secondary" data-ripple>Secondary</button>
                        <button class="btn btn-success" data-ripple>Success</button>
                        <button class="btn btn-warning" data-ripple>Warning</button>
                        <button class="btn btn-danger" data-ripple>Danger</button>
                    </div>
                </div>
                <div class="demo-code">
&lt;button class="btn btn-primary"&gt;Primary&lt;/button&gt;
&lt;button class="btn btn-secondary"&gt;Secondary&lt;/button&gt;
                </div>
            </div>
            
            <div class="component-demo">
                <div class="demo-title">Button Sizes</div>
                <div class="demo-content">
                    <div class="flex flex-wrap items-center gap-3 mb-4">
                        <button class="btn btn-primary btn-xs">Extra Small</button>
                        <button class="btn btn-primary btn-sm">Small</button>
                        <button class="btn btn-primary">Default</button>
                        <button class="btn btn-primary btn-lg">Large</button>
                        <button class="btn btn-primary btn-xl">Extra Large</button>
                    </div>
                </div>
                <div class="demo-code">
&lt;button class="btn btn-primary btn-sm"&gt;Small&lt;/button&gt;
&lt;button class="btn btn-primary btn-lg"&gt;Large&lt;/button&gt;
                </div>
            </div>
        </div>
    </section>

    <!-- Form Controls -->
    <section class="section" data-animate="fade-in-up" data-delay="400">
        <h2 class="section-title">Form Controls</h2>
        
        <div class="component-grid">
            <div class="component-demo">
                <div class="demo-title">Input Fields</div>
                <div class="demo-content">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Email Address</label>
                        <input type="email" class="form-control" placeholder="Enter your email">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Password</label>
                        <input type="password" class="form-control" placeholder="Enter your password">
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Message</label>
                        <textarea class="form-control" rows="3" placeholder="Enter your message"></textarea>
                    </div>
                </div>
            </div>
            
            <div class="component-demo">
                <div class="demo-title">Select & Checkboxes</div>
                <div class="demo-content">
                    <div class="mb-4">
                        <label class="block text-sm font-medium mb-2">Country</label>
                        <select class="form-control">
                            <option>Select a country</option>
                            <option>United States</option>
                            <option>Canada</option>
                            <option>United Kingdom</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-sm">I agree to the terms and conditions</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Cards -->
    <section class="section" data-animate="fade-in-up" data-delay="500">
        <h2 class="section-title">Cards</h2>
        
        <div class="component-grid">
            <div class="component-demo">
                <div class="demo-title">Basic Card</div>
                <div class="demo-content">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="text-lg font-semibold">Card Title</h3>
                        </div>
                        <div class="card-body">
                            <p class="text-sm text-secondary mb-4">This is a basic card component with header, body, and footer sections.</p>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-primary btn-sm">Action</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="component-demo">
                <div class="demo-title">Simple Card</div>
                <div class="demo-content">
                    <div class="card">
                        <div class="card-body">
                            <h3 class="text-lg font-semibold mb-2">Simple Card</h3>
                            <p class="text-sm text-secondary mb-4">A simple card with just a body section for minimal content.</p>
                            <div class="flex gap-2">
                                <span class="badge badge-primary">Tag 1</span>
                                <span class="badge badge-success">Tag 2</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Alerts & Badges -->
    <section class="section" data-animate="fade-in-up" data-delay="600">
        <h2 class="section-title">Alerts & Badges</h2>
        
        <div class="component-grid">
            <div class="component-demo">
                <div class="demo-title">Alert Messages</div>
                <div class="demo-content">
                    <div class="alert alert-primary">
                        <strong>Info:</strong> This is an informational alert message.
                    </div>
                    <div class="alert alert-success">
                        <strong>Success:</strong> Your action was completed successfully.
                    </div>
                    <div class="alert alert-warning">
                        <strong>Warning:</strong> Please review your input before proceeding.
                    </div>
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Something went wrong. Please try again.
                    </div>
                </div>
            </div>
            
            <div class="component-demo">
                <div class="demo-title">Badges</div>
                <div class="demo-content">
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="badge badge-primary">Primary</span>
                        <span class="badge badge-success">Success</span>
                        <span class="badge badge-warning">Warning</span>
                        <span class="badge badge-danger">Danger</span>
                    </div>
                    <p class="text-sm text-secondary">
                        Badges are perfect for status indicators, counts, and labels.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Animations -->
    <section class="section" data-animate="fade-in-up" data-delay="700">
        <h2 class="section-title">Animations</h2>
        
        <div class="component-demo">
            <div class="demo-title">Interactive Animations</div>
            <div class="demo-content">
                <div class="animation-demo">
                    <div class="animation-box animate-pulse" data-tooltip="Pulse Animation">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="animation-box animate-bounce" data-tooltip="Bounce Animation">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="animation-box animate-spin" data-tooltip="Spin Animation">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="animation-box" data-tooltip="Hover Effect" data-hover-lift>
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                </div>
                <p class="text-sm text-secondary mt-4">
                    Hover over the boxes to see different animation effects. Animations respect user preferences for reduced motion.
                </p>
            </div>
        </div>
    </section>
</div>

<!-- UI Advanced JavaScript -->
<script src="/static/js/ui-advanced.js"></script>

<script>
// Additional showcase functionality
function toggleAnimations() {
    const icon = document.getElementById('animation-icon');
    if (window.uiAdvanced.animations) {
        window.uiAdvanced.disableAnimations();
        icon.className = 'fas fa-magic-wand-sparkles';
        window.uiAdvanced.showNotification('Animations disabled', 'info');
    } else {
        window.uiAdvanced.enableAnimations();
        icon.className = 'fas fa-magic';
        window.uiAdvanced.showNotification('Animations enabled', 'success');
    }
}

// Demo interactions
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for animation demos
    document.querySelectorAll('.animation-box').forEach(box => {
        box.addEventListener('click', function() {
            this.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 200);
        });
    });
    
    // Show welcome message
    setTimeout(() => {
        window.uiAdvanced.showNotification('Welcome to the Design System showcase!', 'success', 4000);
    }, 1000);
});
</script>

</body>
</html>
