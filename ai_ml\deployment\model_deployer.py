"""
Advanced ML Model Deployment Pipeline
Handles automated model deployment, versioning, and A/B testing
"""

import os
import json
import time
import docker
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
import boto3
import joblib
import numpy as np
from sklearn.metrics import accuracy_score, mean_squared_error

from ..models import MLModel, Prediction
from ..training_pipeline import training_pipeline

logger = logging.getLogger(__name__)

@dataclass
class DeploymentConfig:
    """Configuration for model deployment"""
    model_id: str
    deployment_name: str
    environment: str = "production"  # staging, production
    replicas: int = 2
    cpu_limit: str = "1000m"
    memory_limit: str = "2Gi"
    auto_scaling: bool = True
    health_check_path: str = "/health"
    metrics_enabled: bool = True
    a_b_testing: bool = False
    traffic_split: float = 1.0  # Percentage of traffic (0.0-1.0)

class ModelDeployer:
    """
    Advanced ML model deployment and management system
    """
    
    def __init__(self):
        self.docker_client = docker.from_env()
        self.deployments: Dict[str, DeploymentConfig] = {}
        self.model_versions: Dict[str, List[str]] = {}
        self.performance_metrics: Dict[str, Dict] = {}
        
        # AWS/Cloud configuration
        self.s3_client = boto3.client('s3') if self._aws_configured() else None
        self.model_bucket = getattr(settings, 'ML_MODEL_BUCKET', 'shrimp-farm-ml-models')
        
        # Initialize deployment tracking
        self._load_existing_deployments()
    
    def _aws_configured(self) -> bool:
        """Check if AWS is configured"""
        return all([
            os.getenv('AWS_ACCESS_KEY_ID'),
            os.getenv('AWS_SECRET_ACCESS_KEY'),
            os.getenv('AWS_DEFAULT_REGION')
        ])
    
    def _load_existing_deployments(self):
        """Load existing deployments from database"""
        try:
            active_models = MLModel.objects.filter(status='ACTIVE')
            for model in active_models:
                if hasattr(model, 'deployment_config'):
                    config_data = json.loads(model.deployment_config)
                    self.deployments[model.name] = DeploymentConfig(**config_data)
        except Exception as e:
            logger.error(f"Failed to load existing deployments: {e}")
    
    def deploy_model(self, model_id: str, config: DeploymentConfig) -> Dict[str, Any]:
        """Deploy ML model with specified configuration"""
        logger.info(f"Starting deployment of model {model_id}")
        
        try:
            # Get model from database
            model = MLModel.objects.get(id=model_id)
            
            # Validate model
            if not self._validate_model(model):
                return {"success": False, "error": "Model validation failed"}
            
            # Upload model to cloud storage
            model_url = self._upload_model_to_cloud(model)
            if not model_url:
                return {"success": False, "error": "Failed to upload model"}
            
            # Create deployment container
            container_info = self._create_deployment_container(model, config, model_url)
            if not container_info:
                return {"success": False, "error": "Failed to create container"}
            
            # Deploy to orchestration platform
            deployment_result = self._deploy_to_kubernetes(model, config, container_info)
            if not deployment_result["success"]:
                return deployment_result
            
            # Setup monitoring and health checks
            self._setup_monitoring(model, config)
            
            # Update deployment tracking
            self.deployments[config.deployment_name] = config
            self._update_model_deployment_status(model, config, "deployed")
            
            logger.info(f"Successfully deployed model {model_id}")
            
            return {
                "success": True,
                "deployment_name": config.deployment_name,
                "model_url": model_url,
                "container_id": container_info["container_id"],
                "deployment_id": deployment_result["deployment_id"]
            }
            
        except Exception as e:
            logger.error(f"Model deployment failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _validate_model(self, model: MLModel) -> bool:
        """Validate model before deployment"""
        try:
            # Check if model file exists
            if not model.model_file_path or not os.path.exists(model.model_file_path):
                logger.error("Model file not found")
                return False
            
            # Load and test model
            loaded_model = joblib.load(model.model_file_path)
            
            # Test with dummy data
            if model.model_type == 'WATER_QUALITY_PREDICTION':
                test_features = np.array([[25.0, 7.5, 6.0, 30.0, 5.0, 1000, 2, 14, 6]])
                prediction = loaded_model.predict(test_features)
                
                if len(prediction) == 0 or np.isnan(prediction[0]):
                    logger.error("Model prediction test failed")
                    return False
            
            # Check model performance metrics
            if model.accuracy and model.accuracy < 0.7:
                logger.warning(f"Model accuracy {model.accuracy} below recommended threshold")
                return False
            
            logger.info("Model validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Model validation failed: {e}")
            return False
    
    def _upload_model_to_cloud(self, model: MLModel) -> Optional[str]:
        """Upload model to cloud storage"""
        if not self.s3_client:
            logger.warning("AWS not configured, using local storage")
            return f"file://{model.model_file_path}"
        
        try:
            # Generate S3 key
            timestamp = int(time.time())
            s3_key = f"models/{model.model_type}/{model.id}/{timestamp}/model.joblib"
            
            # Upload model file
            self.s3_client.upload_file(
                model.model_file_path,
                self.model_bucket,
                s3_key
            )
            
            # Upload scaler if exists
            if hasattr(model, 'scaler_file_path') and model.scaler_file_path:
                scaler_key = f"models/{model.model_type}/{model.id}/{timestamp}/scaler.joblib"
                self.s3_client.upload_file(
                    model.scaler_file_path,
                    self.model_bucket,
                    scaler_key
                )
            
            model_url = f"s3://{self.model_bucket}/{s3_key}"
            logger.info(f"Model uploaded to {model_url}")
            
            return model_url
            
        except Exception as e:
            logger.error(f"Failed to upload model to cloud: {e}")
            return None
    
    def _create_deployment_container(self, model: MLModel, config: DeploymentConfig, 
                                   model_url: str) -> Optional[Dict[str, Any]]:
        """Create Docker container for model deployment"""
        try:
            # Create Dockerfile content
            dockerfile_content = self._generate_dockerfile(model, model_url)
            
            # Build container image
            image_tag = f"shrimp-farm-ml/{model.model_type.lower()}:{model.id}"
            
            # Create temporary directory for build context
            import tempfile
            with tempfile.TemporaryDirectory() as build_dir:
                dockerfile_path = os.path.join(build_dir, 'Dockerfile')
                with open(dockerfile_path, 'w') as f:
                    f.write(dockerfile_content)
                
                # Copy requirements
                requirements_content = self._generate_requirements()
                requirements_path = os.path.join(build_dir, 'requirements.txt')
                with open(requirements_path, 'w') as f:
                    f.write(requirements_content)
                
                # Build image
                image, build_logs = self.docker_client.images.build(
                    path=build_dir,
                    tag=image_tag,
                    rm=True
                )
                
                logger.info(f"Built container image: {image_tag}")
                
                return {
                    "image_tag": image_tag,
                    "image_id": image.id,
                    "container_id": image_tag
                }
                
        except Exception as e:
            logger.error(f"Failed to create deployment container: {e}")
            return None
    
    def _generate_dockerfile(self, model: MLModel, model_url: str) -> str:
        """Generate Dockerfile for model deployment"""
        return f"""
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy model serving code
COPY model_server.py .

# Set environment variables
ENV MODEL_TYPE={model.model_type}
ENV MODEL_URL={model_url}
ENV MODEL_ID={model.id}

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:8080/health || exit 1

# Run model server
CMD ["python", "model_server.py"]
"""
    
    def _generate_requirements(self) -> str:
        """Generate requirements.txt for model container"""
        return """
fastapi==0.68.0
uvicorn==0.15.0
scikit-learn==1.0.2
numpy==1.21.0
pandas==1.3.0
joblib==1.0.1
boto3==1.18.0
pydantic==1.8.2
"""
    
    def _deploy_to_kubernetes(self, model: MLModel, config: DeploymentConfig, 
                            container_info: Dict) -> Dict[str, Any]:
        """Deploy container to Kubernetes"""
        try:
            # Generate Kubernetes manifests
            deployment_manifest = self._generate_k8s_deployment(model, config, container_info)
            service_manifest = self._generate_k8s_service(model, config)
            
            # Apply manifests (simplified - in production use kubectl or K8s API)
            deployment_id = f"{config.deployment_name}-{int(time.time())}"
            
            # Store manifests for tracking
            manifests_dir = os.path.join(settings.BASE_DIR, 'k8s_manifests')
            os.makedirs(manifests_dir, exist_ok=True)
            
            with open(os.path.join(manifests_dir, f"{deployment_id}-deployment.yaml"), 'w') as f:
                f.write(deployment_manifest)
            
            with open(os.path.join(manifests_dir, f"{deployment_id}-service.yaml"), 'w') as f:
                f.write(service_manifest)
            
            logger.info(f"Generated Kubernetes manifests for {deployment_id}")
            
            return {
                "success": True,
                "deployment_id": deployment_id,
                "manifests_path": manifests_dir
            }
            
        except Exception as e:
            logger.error(f"Kubernetes deployment failed: {e}")
            return {"success": False, "error": str(e)}
    
    def _generate_k8s_deployment(self, model: MLModel, config: DeploymentConfig, 
                                container_info: Dict) -> str:
        """Generate Kubernetes deployment manifest"""
        return f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {config.deployment_name}
  labels:
    app: {config.deployment_name}
    model-type: {model.model_type.lower()}
    environment: {config.environment}
spec:
  replicas: {config.replicas}
  selector:
    matchLabels:
      app: {config.deployment_name}
  template:
    metadata:
      labels:
        app: {config.deployment_name}
        model-type: {model.model_type.lower()}
    spec:
      containers:
      - name: model-server
        image: {container_info['image_tag']}
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: {config.cpu_limit}
            memory: {config.memory_limit}
          requests:
            cpu: "500m"
            memory: "1Gi"
        env:
        - name: MODEL_TYPE
          value: "{model.model_type}"
        - name: MODEL_ID
          value: "{model.id}"
        - name: ENVIRONMENT
          value: "{config.environment}"
        livenessProbe:
          httpGet:
            path: {config.health_check_path}
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: {config.health_check_path}
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
"""
    
    def _generate_k8s_service(self, model: MLModel, config: DeploymentConfig) -> str:
        """Generate Kubernetes service manifest"""
        return f"""
apiVersion: v1
kind: Service
metadata:
  name: {config.deployment_name}-service
  labels:
    app: {config.deployment_name}
spec:
  selector:
    app: {config.deployment_name}
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
"""
    
    def _setup_monitoring(self, model: MLModel, config: DeploymentConfig):
        """Setup monitoring for deployed model"""
        if not config.metrics_enabled:
            return
        
        # Initialize performance tracking
        self.performance_metrics[config.deployment_name] = {
            "requests_total": 0,
            "requests_success": 0,
            "requests_error": 0,
            "response_times": [],
            "prediction_accuracy": [],
            "last_updated": time.time()
        }
        
        logger.info(f"Monitoring setup completed for {config.deployment_name}")
    
    def _update_model_deployment_status(self, model: MLModel, config: DeploymentConfig, 
                                      status: str):
        """Update model deployment status in database"""
        try:
            model.deployment_config = json.dumps(asdict(config))
            model.deployment_status = status
            model.last_deployed_at = timezone.now()
            model.save()
            
            logger.info(f"Updated deployment status for model {model.id}: {status}")
            
        except Exception as e:
            logger.error(f"Failed to update deployment status: {e}")
    
    def rollback_deployment(self, deployment_name: str, target_version: str) -> Dict[str, Any]:
        """Rollback deployment to previous version"""
        try:
            if deployment_name not in self.deployments:
                return {"success": False, "error": "Deployment not found"}
            
            # Implementation would involve K8s rollback commands
            logger.info(f"Rolling back {deployment_name} to version {target_version}")
            
            return {
                "success": True,
                "deployment_name": deployment_name,
                "target_version": target_version,
                "rollback_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Rollback failed: {e}")
            return {"success": False, "error": str(e)}
    
    def get_deployment_status(self, deployment_name: str) -> Dict[str, Any]:
        """Get current deployment status and metrics"""
        if deployment_name not in self.deployments:
            return {"error": "Deployment not found"}
        
        config = self.deployments[deployment_name]
        metrics = self.performance_metrics.get(deployment_name, {})
        
        return {
            "deployment_name": deployment_name,
            "config": asdict(config),
            "metrics": metrics,
            "status": "running",  # Would check actual K8s status
            "last_updated": datetime.now().isoformat()
        }

# Global deployer instance
model_deployer = ModelDeployer()
