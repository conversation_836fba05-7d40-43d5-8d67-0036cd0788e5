<!DOCTYPE html>
<html>
<head>
    <title>Minimal Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            background: #f0f0f0;
            border: 2px solid #ccc;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <h1>Minimal Google Maps Test</h1>
    <div id="map" class="loading">Loading Google Maps...</div>

    <script>
        // Simple initMap function
        function initMap() {
            console.log('initMap called!');
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 }
            });
            
            // Add a simple marker
            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Location'
            });
            
            document.getElementById('map').classList.remove('loading');
        }

        // Error handler
        function handleMapError() {
            console.error('Map loading failed');
            document.getElementById('map').innerHTML = 'Failed to load Google Maps';
        }
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap"
        onerror="handleMapError()">
    </script>
</body>
</html>
