"""
Scheduled tasks for the weather app
"""

import logging
import re
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q
from celery import shared_task
import requests
from django.conf import settings
from django.contrib.auth.models import User
from django.core.mail import send_mail

from weather.models import (
    WeatherStation, WeatherData, WeatherForecast, 
    WeatherAlert, WeatherImpact
)
from weather.services.factory import WeatherServiceFactory
from alerts.models import Alert
from ponds.models import Pond

logger = logging.getLogger(__name__)

# Security and validation functions
def validate_coordinates(latitude, longitude):
    """
    Validate latitude and longitude coordinates for security
    """
    try:
        lat = float(latitude)
        lon = float(longitude)
        
        # Valid coordinate ranges
        if -90 <= lat <= 90 and -180 <= lon <= 180:
            return True
        return False
    except (ValueError, TypeError):
        return False

def validate_api_key(api_key):
    """
    Validate API key format for security
    """
    if not isinstance(api_key, str):
        return False
    
    # Basic validation - API keys should be alphanumeric
    if not re.match(r'^[a-zA-Z0-9]{16,64}$', api_key):
        return False
    
    return True

def sanitize_pond_name(name):
    """
    Sanitize pond name to prevent injection attacks
    """
    if not isinstance(name, str):
        return "Unknown Pond"
    
    # Remove potential dangerous characters
    sanitized = re.sub(r'[<>"\'\\\0\n\r\t]', '', name)
    
    # Limit length
    if len(sanitized) > 100:
        sanitized = sanitized[:100] + "..."
    
    return sanitized or "Unknown Pond"

def sanitize_alert_message(message):
    """
    Sanitize alert messages to prevent XSS and injection attacks
    """
    if not isinstance(message, str):
        return "Alert message unavailable"
    
    # Remove potential dangerous characters
    sanitized = re.sub(r'[<>"\'\\\0]', '', message)
    
    # Limit length
    if len(sanitized) > 1000:
        sanitized = sanitized[:1000] + "..."
    
    return sanitized or "Alert message unavailable"

def sanitize_weather_description(description):
    """
    Sanitize weather descriptions from API responses
    """
    if not isinstance(description, str):
        return "Unknown weather condition"
    
    # Remove potential dangerous characters
    sanitized = re.sub(r'[<>"\'\\\0\n\r\t]', '', description)
    
    # Limit length
    if len(sanitized) > 200:
        sanitized = sanitized[:200] + "..."
    
    return sanitized or "Unknown weather condition"

def safe_numeric_value(value, min_val=None, max_val=None, default=0.0):
    """
    Safely convert and validate numeric values with range checking
    """
    try:
        num_val = float(value)
        
        # Check for NaN or infinity
        if not (num_val == num_val and abs(num_val) != float('inf')):
            return default
        
        # Range validation
        if min_val is not None and num_val < min_val:
            return default
        if max_val is not None and num_val > max_val:
            return default
            
        return num_val
    except (ValueError, TypeError, OverflowError):
        return default

def get_safe_api_timeout():
    """
    Get secure API timeout values from settings
    """
    connect_timeout = getattr(settings, 'WEATHER_API_CONNECT_TIMEOUT', 5)
    read_timeout = getattr(settings, 'WEATHER_API_READ_TIMEOUT', 30)
    
    # Enforce reasonable limits
    connect_timeout = max(1, min(connect_timeout, 10))
    read_timeout = max(5, min(read_timeout, 60))
    
    return (connect_timeout, read_timeout)

def log_security_event(event_type, details=None):
    """
    Log security-related events without exposing sensitive data
    """
    # Create a security logger if not exists
    security_logger = logging.getLogger('security')
    
    # Sanitize details to prevent log injection
    safe_details = ""
    if details and isinstance(details, str):
        safe_details = re.sub(r'[\n\r\t]', ' ', details)[:500]
    
    security_logger.warning(f"Security event: {event_type} - {safe_details}")

def validate_weather_response(data):
    """
    Validate weather API response structure for security
    """
    if not isinstance(data, dict):
        return False
    
    # Check required fields
    required_fields = ['main', 'weather', 'wind']
    if not all(field in data for field in required_fields):
        return False
    
    # Check main data structure
    if not isinstance(data['main'], dict):
        return False
    
    main_required = ['temp', 'humidity', 'pressure']
    if not all(field in data['main'] for field in main_required):
        return False
    
    # Check weather data structure
    if not isinstance(data['weather'], list) or len(data['weather']) == 0:
        return False
    
    weather_required = ['main', 'description']
    if not all(field in data['weather'][0] for field in weather_required):
        return False
    
    return True

def validate_forecast_response(data):
    """
    Validate forecast API response structure for security
    """
    if not isinstance(data, dict):
        return False
    
    # Check required fields
    if 'list' not in data:
        return False
    
    if not isinstance(data['list'], list) or len(data['list']) == 0:
        return False
    
    # Check first forecast item structure
    first_item = data['list'][0]
    if not isinstance(first_item, dict):
        return False
    
    required_fields = ['main', 'weather', 'dt']
    if not all(field in first_item for field in required_fields):
        return False
    
    return True


def update_weather_data():
    """
    Update weather data for all active stations
    
    This task should be scheduled to run periodically (e.g., every hour)
    """
    logger.info("Starting weather data update task")
    
    # Get all active weather stations
    stations = WeatherStation.objects.filter(is_active=True)
    
    if not stations:
        logger.info("No active weather stations found")
        return
    
    # Create weather service
    weather_service = WeatherServiceFactory.create_service()
    
    if not weather_service:
        logger.error("Failed to create weather service")
        return
    
    # Update data for each station
    for station in stations:
        try:
            logger.info(f"Updating weather data for station: {station.name}")
            results = weather_service.update_station_data(station)
            
            if results['current_weather']:
                logger.info(f"Updated current weather for station: {station.name}")
            
            if results['forecasts']:
                logger.info(f"Updated {len(results['forecasts'])} forecasts for station: {station.name}")
            
            if results['alerts']:
                logger.info(f"Created {len(results['alerts'])} weather alerts for station: {station.name}")
                
                # Create system alerts for weather alerts
                for weather_alert in results['alerts']:
                    create_system_alert_for_weather(weather_alert)
            
        except Exception as e:
            logger.error(f"Error updating weather data for station {station.name}: {e}")
    
    logger.info("Weather data update task completed")


def update_weather_impacts():
    """
    Update weather impacts based on current conditions and forecasts
    
    This task should be scheduled to run daily
    """
    logger.info("Starting weather impacts update task")
    
    # Get all active weather stations
    stations = WeatherStation.objects.filter(is_active=True)
    
    if not stations:
        logger.info("No active weather stations found")
        return
    
    # Update impacts for each station
    for station in stations:
        try:
            logger.info(f"Updating weather impacts for station: {station.name}")
            
            # Get latest weather data
            latest_data = WeatherData.objects.filter(station=station).order_by('-timestamp').first()
            
            if not latest_data:
                logger.info(f"No weather data available for station: {station.name}")
                continue
            
            # Get forecasts for the next 3 days
            today = timezone.now().date()
            forecasts = WeatherForecast.objects.filter(
                station=station,
                forecast_date__gte=today,
                forecast_date__lte=today + timedelta(days=3)
            ).order_by('forecast_date', 'forecast_time')
            
            # Check for potential impacts
            check_temperature_impacts(station, latest_data, forecasts)
            check_precipitation_impacts(station, latest_data, forecasts)
            check_wind_impacts(station, latest_data, forecasts)
            
        except Exception as e:
            logger.error(f"Error updating weather impacts for station {station.name}: {e}")
    
    logger.info("Weather impacts update task completed")


def check_temperature_impacts(station, latest_data, forecasts):
    """Check for temperature-related impacts"""
    # Temperature thresholds for shrimp farming
    low_temp_threshold = 22  # °C
    high_temp_threshold = 32  # °C
    
    # Check current temperature
    if latest_data.temperature < low_temp_threshold:
        create_weather_impact(
            station=station,
            impact_type='Temperature',
            severity='Medium',
            title='Low Temperature Alert',
            description=f'Current temperature ({latest_data.temperature:.1f}°C) is below the recommended minimum ({low_temp_threshold}°C) for optimal shrimp growth.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=1)
        )
    
    elif latest_data.temperature > high_temp_threshold:
        create_weather_impact(
            station=station,
            impact_type='Temperature',
            severity='High',
            title='High Temperature Alert',
            description=f'Current temperature ({latest_data.temperature:.1f}°C) is above the recommended maximum ({high_temp_threshold}°C) for optimal shrimp health.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=1)
        )
    
    # Check forecasted temperatures
    if forecasts:
        # Group forecasts by date
        forecast_by_date = {}
        for forecast in forecasts:
            if forecast.forecast_date not in forecast_by_date:
                forecast_by_date[forecast.forecast_date] = []
            forecast_by_date[forecast.forecast_date].append(forecast)
        
        # Check each day's forecast
        for date, day_forecasts in forecast_by_date.items():
            # Calculate min and max temperatures for the day
            min_temp = min(f.temperature_min for f in day_forecasts)
            max_temp = max(f.temperature_max for f in day_forecasts)
            
            if min_temp < low_temp_threshold:
                create_weather_impact(
                    station=station,
                    impact_type='Temperature',
                    severity='Medium',
                    title=f'Forecasted Low Temperature for {date.strftime("%b %d")}',
                    description=f'Forecasted minimum temperature ({min_temp:.1f}°C) is below the recommended minimum ({low_temp_threshold}°C) for optimal shrimp growth.',
                    start_date=date,
                    end_date=date
                )
            
            if max_temp > high_temp_threshold:
                create_weather_impact(
                    station=station,
                    impact_type='Temperature',
                    severity='High',
                    title=f'Forecasted High Temperature for {date.strftime("%b %d")}',
                    description=f'Forecasted maximum temperature ({max_temp:.1f}°C) is above the recommended maximum ({high_temp_threshold}°C) for optimal shrimp health.',
                    start_date=date,
                    end_date=date
                )


def check_precipitation_impacts(station, latest_data, forecasts):
    """Check for precipitation-related impacts"""
    # Precipitation thresholds
    heavy_rain_threshold = 20  # mm/day
    
    # Check current precipitation
    if latest_data.precipitation > 5:  # Significant current precipitation
        create_weather_impact(
            station=station,
            impact_type='Precipitation',
            severity='Medium',
            title='Current Heavy Rainfall',
            description=f'Current rainfall ({latest_data.precipitation:.1f} mm) may affect water quality parameters. Monitor oxygen levels and turbidity.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date()
        )
    
    # Check forecasted precipitation
    if forecasts:
        # Group forecasts by date
        forecast_by_date = {}
        for forecast in forecasts:
            if forecast.forecast_date not in forecast_by_date:
                forecast_by_date[forecast.forecast_date] = []
            forecast_by_date[forecast.forecast_date].append(forecast)
        
        # Check each day's forecast
        for date, day_forecasts in forecast_by_date.items():
            # Calculate total precipitation for the day
            total_precip = sum(f.precipitation_amount for f in day_forecasts)
            max_prob = max(f.precipitation_probability for f in day_forecasts)
            
            if total_precip > heavy_rain_threshold and max_prob > 50:
                create_weather_impact(
                    station=station,
                    impact_type='Precipitation',
                    severity='High',
                    title=f'Heavy Rainfall Forecast for {date.strftime("%b %d")}',
                    description=f'Forecasted heavy rainfall ({total_precip:.1f} mm) may significantly affect water quality. Prepare for potential runoff and water exchange.',
                    start_date=date,
                    end_date=date
                )
            elif total_precip > 5 and max_prob > 70:
                create_weather_impact(
                    station=station,
                    impact_type='Precipitation',
                    severity='Medium',
                    title=f'Rainfall Forecast for {date.strftime("%b %d")}',
                    description=f'Forecasted rainfall ({total_precip:.1f} mm) may affect water quality parameters. Monitor oxygen levels and turbidity.',
                    start_date=date,
                    end_date=date
                )


def check_wind_impacts(station, latest_data, forecasts):
    """Check for wind-related impacts"""
    # Wind thresholds
    strong_wind_threshold = 10  # m/s
    
    # Check current wind
    if latest_data.wind_speed > strong_wind_threshold:
        create_weather_impact(
            station=station,
            impact_type='Wind',
            severity='Medium',
            title='Strong Winds Alert',
            description=f'Current wind speed ({latest_data.wind_speed:.1f} m/s) may cause water turbulence and affect feeding patterns.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date()
        )
    
    # Check forecasted wind
    if forecasts:
        # Group forecasts by date
        forecast_by_date = {}
        for forecast in forecasts:
            if forecast.forecast_date not in forecast_by_date:
                forecast_by_date[forecast.forecast_date] = []
            forecast_by_date[forecast.forecast_date].append(forecast)
        
        # Check each day's forecast
        for date, day_forecasts in forecast_by_date.items():
            # Find maximum wind speed for the day
            max_wind = max(f.wind_speed for f in day_forecasts)
            
            if max_wind > strong_wind_threshold:
                create_weather_impact(
                    station=station,
                    impact_type='Wind',
                    severity='Medium',
                    title=f'Strong Winds Forecast for {date.strftime("%b %d")}',
                    description=f'Forecasted strong winds ({max_wind:.1f} m/s) may cause water turbulence and affect feeding patterns.',
                    start_date=date,
                    end_date=date
                )


def create_weather_impact(station, impact_type, severity, title, description, start_date, end_date):
    """Create a weather impact record if a similar one doesn't exist"""
    # Check if a similar impact already exists
    existing_impact = WeatherImpact.objects.filter(
        station=station,
        impact_type=impact_type,
        title=title,
        start_date=start_date,
        end_date=end_date
    ).first()
    
    if not existing_impact:
        impact = WeatherImpact.objects.create(
            station=station,
            impact_type=impact_type,
            severity=severity,
            title=title,
            description=description,
            start_date=start_date,
            end_date=end_date,
            is_active=True
        )
        
        # Create a system alert for this impact
        create_system_alert_for_impact(impact)
        
        return impact
    
    return existing_impact


def create_system_alert_for_weather(weather_alert):
    """Create a system alert for a weather alert"""
    alert_type = 'warning'
    if weather_alert.severity == 'Critical':
        alert_type = 'critical'
    elif weather_alert.severity == 'High':
        alert_type = 'warning'
    else:
        alert_type = 'info'
    
    # Check if a similar alert already exists
    existing_alert = Alert.objects.filter(
        title=f"[Weather] {weather_alert.title}",
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).first()
    
    if not existing_alert:
        Alert.objects.create(
            type=alert_type,
            title=f"[Weather] {weather_alert.title}",
            message=weather_alert.description,
            scheduled_date=weather_alert.start_time.date() if weather_alert.start_time else None
        )


def create_system_alert_for_impact(impact):
    """Create a system alert for a weather impact"""
    alert_type = 'warning'
    if impact.severity == 'Critical':
        alert_type = 'critical'
    elif impact.severity == 'High':
        alert_type = 'warning'
    else:
        alert_type = 'info'
    
    # Check if a similar alert already exists
    existing_alert = Alert.objects.filter(
        title=f"[Weather Impact] {impact.title}",
        created_at__gte=timezone.now() - timedelta(hours=24)
    ).first()
    
    if not existing_alert:
        Alert.objects.create(
            type=alert_type,
            title=f"[Weather Impact] {impact.title}",
            message=impact.description,
            scheduled_date=impact.start_date
        )


def cleanup_old_weather_data():
    """
    Clean up old weather data to prevent database bloat
    
    This task should be scheduled to run weekly
    """
    logger.info("Starting weather data cleanup task")
    
    # Delete weather data older than 90 days
    cutoff_date = timezone.now() - timedelta(days=90)
    old_data = WeatherData.objects.filter(timestamp__lt=cutoff_date)
    data_count = old_data.count()
    old_data.delete()
    
    # Delete forecasts older than 7 days
    cutoff_date = timezone.now().date() - timedelta(days=7)
    old_forecasts = WeatherForecast.objects.filter(forecast_date__lt=cutoff_date)
    forecast_count = old_forecasts.count()
    old_forecasts.delete()
    
    # Delete expired alerts
    expired_alerts = WeatherAlert.objects.filter(
        end_time__lt=timezone.now(),
        is_active=True
    )
    alert_count = expired_alerts.count()
    expired_alerts.update(is_active=False)
    
    # Delete expired impacts
    expired_impacts = WeatherImpact.objects.filter(
        end_date__lt=timezone.now().date(),
        is_active=True
    )
    impact_count = expired_impacts.count()
    expired_impacts.update(is_active=False)
    
    logger.info(f"Deleted {data_count} old weather data records")
    logger.info(f"Deleted {forecast_count} old forecast records")
    logger.info(f"Marked {alert_count} weather alerts as inactive")
    logger.info(f"Marked {impact_count} weather impacts as inactive")
    
    logger.info("Weather data cleanup task completed")

@shared_task
def monitor_pond_weather():
    """
    Automated task to monitor weather conditions for all ponds every 5 minutes
    """
    logger.info("Starting automated weather monitoring for all ponds")
    
    # Rate limiting check - prevent too frequent calls
    from django.core.cache import cache
    cache_key = 'weather_monitoring_lock'
    if cache.get(cache_key):
        logger.warning("Weather monitoring task already running, skipping")
        return {'status': 'skipped', 'reason': 'already_running'}
    
    # Set lock for 10 minutes
    cache.set(cache_key, True, 600)
    
    try:
        # Get all active ponds with coordinates
        ponds = Pond.objects.filter(
            status='active',
            latitude__isnull=False,
            longitude__isnull=False
        )
        
        # Limit the number of ponds processed per run for security
        max_ponds_per_run = getattr(settings, 'MAX_PONDS_PER_WEATHER_RUN', 100)
        if ponds.count() > max_ponds_per_run:
            ponds = ponds[:max_ponds_per_run]
            logger.warning(f"Limited weather monitoring to {max_ponds_per_run} ponds")
        
        notifications_sent = 0
        weather_alerts = []
        
        for pond in ponds:
            try:
                # Additional rate limiting per pond
                pond_cache_key = f'weather_pond_{pond.id}'
                if cache.get(pond_cache_key):
                    continue  # Skip if recently processed
                
                # Set pond-specific lock for 5 minutes
                cache.set(pond_cache_key, True, 300)
                
                # Get current weather data for pond location
                weather_data = get_weather_for_location(pond.latitude, pond.longitude)
                
                if weather_data:
                    # Analyze weather conditions and create alerts
                    alerts = analyze_weather_conditions(pond, weather_data)
                    
                    for alert in alerts:
                        # Create notification
                        create_weather_notification(pond, alert, weather_data)
                        weather_alerts.append(alert)
                        notifications_sent += 1
                    
                    # Store weather data
                    store_weather_data(pond, weather_data)
                    # Analyze weather conditions and create alerts
                    alerts = analyze_weather_conditions(pond, weather_data)
                    
                    for alert in alerts:
                        # Create notification
                        create_weather_notification(pond, alert, weather_data)
                        weather_alerts.append(alert)
                        notifications_sent += 1
                    
                    # Store weather data
                    store_weather_data(pond, weather_data)
                    
            except Exception as e:
                logger.error(f"Error monitoring weather for pond {pond.id}")
                # Don't log pond name or detailed error to prevent information disclosure
        
        logger.info(f"Weather monitoring completed. {notifications_sent} notifications sent.")
        return {
            'ponds_monitored': ponds.count(),
            'notifications_sent': notifications_sent,
            'alerts': len(weather_alerts)
        }
        
    finally:
        # Always release the lock
        cache.delete(cache_key)

def get_weather_for_location(latitude, longitude):
    """
    Get weather data from OpenWeatherMap API for specific coordinates
    """
    # Input validation
    if not validate_coordinates(latitude, longitude):
        logger.warning("Invalid coordinates provided for weather API call")
        return None
    
    api_key = getattr(settings, 'OPENWEATHER_API_KEY', None)
    if not api_key:
        log_security_event("missing_weather_api_key", "OpenWeatherMap API key not configured")
        return None
    
    if not validate_api_key(api_key):
        log_security_event("invalid_weather_api_key", "Invalid API key format")
        return None
    
    try:
        # Use HTTPS URLs for security
        current_url = "https://api.openweathermap.org/data/2.5/weather"
        current_params = {
            'lat': safe_numeric_value(latitude, -90, 90),
            'lon': safe_numeric_value(longitude, -180, 180),
            'appid': api_key,
            'units': 'metric'
        }
        
        # Enhanced timeout and security headers
        headers = {
            'User-Agent': 'ShrimpFarmGuardian/1.0',
            'Accept': 'application/json'
        }
        
        timeout = get_safe_api_timeout()
        
        current_response = requests.get(
            current_url, 
            params=current_params, 
            headers=headers,
            timeout=timeout,
            verify=True  # Verify SSL certificates
        )
        current_response.raise_for_status()
        
        # Validate response content type
        if not current_response.headers.get('content-type', '').startswith('application/json'):
            logger.error("Invalid content type received from weather API")
            return None
            
        current_data = current_response.json()
        
        # Validate API response structure
        if not validate_weather_response(current_data):
            logger.error("Invalid weather API response structure")
            return None
        
        # Forecast data (for storm detection)
        forecast_url = "https://api.openweathermap.org/data/2.5/forecast"
        forecast_params = {
            'lat': safe_numeric_value(latitude, -90, 90),
            'lon': safe_numeric_value(longitude, -180, 180),
            'appid': api_key,
            'units': 'metric',
            'cnt': 8  # Next 24 hours (3-hour intervals)
        }
        
        forecast_response = requests.get(
            forecast_url, 
            params=forecast_params, 
            headers=headers,
            timeout=timeout,
            verify=True
        )
        forecast_response.raise_for_status()
        
        # Validate response content type
        if not forecast_response.headers.get('content-type', '').startswith('application/json'):
            logger.error("Invalid content type received from forecast API")
            return None
            
        forecast_data = forecast_response.json()
        
        # Validate forecast response structure
        if not validate_forecast_response(forecast_data):
            logger.error("Invalid forecast API response structure")
            return None
        
        return {
            'current': current_data,
            'forecast': forecast_data,
            'timestamp': timezone.now()
        }
        
    except requests.Timeout:
        log_security_event("weather_api_timeout", f"Weather API request timed out for coordinates")
        return None
    except requests.ConnectionError:
        log_security_event("weather_api_connection_error", "Weather API connection failed")
        return None
    except requests.HTTPError as e:
        log_security_event("weather_api_http_error", f"Weather API HTTP error: {e.response.status_code}")
        return None
    except ValueError as e:
        log_security_event("weather_api_json_error", "Invalid JSON response from weather API")
        return None
    except Exception as e:
        log_security_event("weather_api_unexpected_error", "Unexpected error during weather API call")
        return None

def analyze_weather_conditions(pond, weather_data):
    """
    Analyze weather conditions and determine if alerts are needed
    """
    alerts = []
    current = weather_data['current']
    forecast = weather_data['forecast']
    
    # Get previous weather data for comparison
    previous_data = get_previous_weather_data(pond)
    
    # Current weather analysis with safe numeric conversion
    temperature = safe_numeric_value(current['main']['temp'], -50, 60, 25)
    humidity = safe_numeric_value(current['main']['humidity'], 0, 100, 50)
    wind_speed = safe_numeric_value(current['wind']['speed'], 0, 100, 0)
    weather_condition = sanitize_weather_description(current['weather'][0]['main'])
    weather_description = sanitize_weather_description(current['weather'][0]['description'])
    
    # Temperature alerts
    if temperature > 35:
        alerts.append({
            'type': 'high_temperature',
            'severity': 'critical',
            'message': f"Extreme high temperature: {temperature}°C",
            'recommendation': "Consider emergency aeration and shading measures"
        })
    elif temperature < 18:
        alerts.append({
            'type': 'low_temperature',
            'severity': 'warning',
            'message': f"Low temperature detected: {temperature}°C",
            'recommendation': "Monitor shrimp activity and consider heating"
        })
    
    # Sudden temperature change with safe comparison
    if previous_data:
        prev_temp = safe_numeric_value(previous_data.get('temperature', temperature), -50, 60, temperature)
        temp_change = abs(temperature - prev_temp)
        if temp_change > 5:
            alerts.append({
                'type': 'temperature_change',
                'severity': 'warning',
                'message': f"Rapid temperature change: {temperature:.1f}°C (was {prev_temp:.1f}°C)",
                'recommendation': "Monitor water temperature and shrimp behavior"
            })
    
    # Humidity alerts
    if humidity > 85:
        alerts.append({
            'type': 'high_humidity',
            'severity': 'warning',
            'message': f"High humidity: {humidity}%",
            'recommendation': "Increase ventilation and monitor for algae growth"
        })
    elif humidity < 40:
        alerts.append({
            'type': 'low_humidity',
            'severity': 'warning',
            'message': f"Low humidity: {humidity}%",
            'recommendation': "Monitor water evaporation rates"
        })
    
    # Wind speed alerts
    if wind_speed > 10:  # m/s
        alerts.append({
            'type': 'high_wind',
            'severity': 'warning',
            'message': f"High wind speed: {wind_speed} m/s",
            'recommendation': "Secure equipment and check pond covers"
        })
    
    # Weather condition alerts
    if weather_condition in ['Thunderstorm', 'Squall', 'Tornado']:
        alerts.append({
            'type': 'severe_weather',
            'severity': 'critical',
            'message': f"Severe weather alert: {weather_description}",
            'recommendation': "Take immediate protective measures, secure all equipment"
        })
    elif weather_condition == 'Rain':
        rain_volume = safe_numeric_value(current.get('rain', {}).get('1h', 0), 0, 500, 0)
        if rain_volume > 10:  # Heavy rain
            alerts.append({
                'type': 'heavy_rain',
                'severity': 'warning',
                'message': f"Heavy rain detected: {rain_volume:.1f}mm/h",
                'recommendation': "Monitor water levels and quality, check drainage"
            })
    
    # Forecast analysis for incoming storms
    for forecast_item in forecast['list'][:4]:  # Next 12 hours
        forecast_weather = forecast_item['weather'][0]['main']
        if forecast_weather in ['Thunderstorm', 'Squall']:
            forecast_time = datetime.fromtimestamp(forecast_item['dt'])
            alerts.append({
                'type': 'incoming_storm',
                'severity': 'warning',
                'message': f"Storm approaching at {forecast_time.strftime('%H:%M')}",
                'recommendation': "Prepare protective measures and secure equipment"
            })
            break
    
    return alerts

def get_previous_weather_data(pond):
    """
    Get previous weather data for comparison
    """
    try:
        # Get weather data from 30 minutes ago
        cutoff_time = timezone.now() - timedelta(minutes=30)
        weather_station = pond.weather_station
        
        if weather_station:
            previous_data = WeatherData.objects.filter(
                weather_station=weather_station,
                timestamp__lt=cutoff_time
            ).order_by('-timestamp').first()
            
            if previous_data:
                return {
                    'temperature': previous_data.temperature,
                    'humidity': previous_data.humidity,
                    'pressure': previous_data.pressure,
                    'wind_speed': previous_data.wind_speed,
                    'timestamp': previous_data.timestamp
                }
            
    except Exception as e:
        logger.error(f"Error getting previous weather data: {str(e)}")
    
    return None

def create_weather_notification(pond, alert, weather_data):
    """
    Create and send weather notifications to users and workers
    """
    try:
        # Get users to notify (pond owner, farm managers, assigned workers)
        users_to_notify = get_notification_recipients(pond)
        
        current = weather_data['current']
        
        # Sanitize inputs to prevent injection attacks
        pond_name = sanitize_pond_name(pond.name)
        alert_message = sanitize_alert_message(alert['message'])
        alert_recommendation = sanitize_alert_message(alert['recommendation'])
        
        # Create notification message with sanitized data
        message = f"""
        Weather Alert for {pond_name}
        
        Alert Type: {alert_message}
        Severity: {alert['severity'].upper()}
        
        Current Conditions:
        • Temperature: {safe_numeric_value(current['main']['temp']):.1f}°C
        • Humidity: {safe_numeric_value(current['main']['humidity']):.0f}%
        • Wind Speed: {safe_numeric_value(current['wind']['speed']):.1f} m/s
        • Condition: {sanitize_weather_description(current['weather'][0]['description'])}
        
        Recommendation: {alert_recommendation}
        
        Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        # Create database alert record
        Alert.objects.create(
            type=alert['severity'],
            title=f"Weather Alert - {pond_name}",
            message=message.strip(),
            scheduled_date=timezone.now().date()
        )
        
        # Send email notifications for critical alerts
        if alert['severity'] == 'critical':
            email_recipients = [user.email for user in users_to_notify if user.email]
            if email_recipients:
                send_critical_weather_email(pond, alert, weather_data, email_recipients)
        
        # Send SMS notifications for critical and warning alerts
        if alert['severity'] in ['critical', 'warning']:
            sms_recipients = get_sms_recipients(pond)
            if sms_recipients:
                send_weather_sms(pond, alert, sms_recipients)
                
        logger.info(f"Weather notification sent for pond {pond.id}: {alert['type']}")
            
    except Exception as e:
        logger.error(f"Error creating weather notification: {str(e)}")

def get_notification_recipients(pond):
    """
    Get list of users who should receive notifications for this pond
    """
    recipients = []
    
    try:
        # Get pond owner/farm owner
        if hasattr(pond, 'farm') and pond.farm:
            if hasattr(pond.farm, 'owner') and pond.farm.owner:
                recipients.append(pond.farm.owner)
        
        # Get farm managers
        from django.contrib.auth.models import Group
        try:
            managers_group = Group.objects.get(name='Farm Managers')
            recipients.extend(managers_group.user_set.filter(is_active=True))
        except Group.DoesNotExist:
            pass
        
        # Get workers assigned to this pond
        if hasattr(pond, 'assigned_workers'):
            recipients.extend(pond.assigned_workers.filter(is_active=True))
        
        # Get superusers as fallback
        if not recipients:
            recipients.extend(User.objects.filter(is_superuser=True, is_active=True)[:3])
        
        # Remove duplicates
        recipients = list(set(recipients))
        
    except Exception as e:
        logger.error(f"Error getting notification recipients: {str(e)}")
        # Fallback to superusers
        recipients = list(User.objects.filter(is_superuser=True, is_active=True)[:3])
    
    return recipients

def send_critical_weather_email(pond, alert, weather_data, recipients):
    """
    Send critical weather alert emails
    """
    try:
        current = weather_data['current']
        pond_name = sanitize_pond_name(pond.name)
        
        subject = f"🚨 Critical Weather Alert - {pond_name}"
        
        # Create HTML email content
        html_message = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Critical Weather Alert</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
                .header {{ background-color: #dc3545; color: white; padding: 15px; border-radius: 5px; text-align: center; }}
                .content {{ padding: 20px 0; }}
                .weather-info {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                .alert-box {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }}
                .footer {{ color: #666; font-size: 12px; text-align: center; margin-top: 20px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚨 Critical Weather Alert</h1>
                </div>
                <div class="content">
                    <h2>Pond: {pond_name}</h2>
                    <div class="alert-box">
                        <strong>Alert:</strong> {sanitize_alert_message(alert['message'])}<br>
                        <strong>Severity:</strong> {alert['severity'].upper()}
                    </div>
                    <div class="weather-info">
                        <h3>Current Weather Conditions:</h3>
                        <ul>
                            <li><strong>Temperature:</strong> {safe_numeric_value(current['main']['temp']):.1f}°C</li>
                            <li><strong>Humidity:</strong> {safe_numeric_value(current['main']['humidity']):.0f}%</li>
                            <li><strong>Wind Speed:</strong> {safe_numeric_value(current['wind']['speed']):.1f} m/s</li>
                            <li><strong>Condition:</strong> {sanitize_weather_description(current['weather'][0]['description'])}</li>
                            <li><strong>Pressure:</strong> {safe_numeric_value(current['main']['pressure']):.0f} hPa</li>
                        </ul>
                    </div>
                    <div class="alert-box">
                        <strong>Recommended Action:</strong><br>
                        {sanitize_alert_message(alert['recommendation'])}
                    </div>
                    <p><strong>Time:</strong> {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                <div class="footer">
                    <p>This is an automated alert from the Shrimp Farm Guardian system.</p>
                    <p>Please take immediate action to protect your shrimp farm.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Plain text version
        plain_message = f"""
        CRITICAL WEATHER ALERT - {pond_name}
        
        Alert: {sanitize_alert_message(alert['message'])}
        Severity: {alert['severity'].upper()}
        
        Current Weather Conditions:
        - Temperature: {safe_numeric_value(current['main']['temp']):.1f}°C
        - Humidity: {safe_numeric_value(current['main']['humidity']):.0f}%
        - Wind Speed: {safe_numeric_value(current['wind']['speed']):.1f} m/s
        - Condition: {sanitize_weather_description(current['weather'][0]['description'])}
        - Pressure: {safe_numeric_value(current['main']['pressure']):.0f} hPa
        
        Recommended Action:
        {sanitize_alert_message(alert['recommendation'])}
        
        Time: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}
        
        This is an automated alert from the Shrimp Farm Guardian system.
        Please take immediate action to protect your shrimp farm.
        """
        
        # Send email using Django's send_mail
        from django.core.mail import EmailMultiAlternatives
        
        email = EmailMultiAlternatives(
            subject=subject,
            body=plain_message,
            from_email=getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>'),
            to=recipients
        )
        email.attach_alternative(html_message, "text/html")
        email.send()
        
        logger.info(f"Critical weather email sent to {len(recipients)} recipients")
        
    except Exception as e:
        logger.error(f"Error sending critical weather email: {str(e)}")

def get_sms_recipients(pond):
    """
    Get phone numbers for SMS notifications
    """
    recipients = []
    
    try:
        # Get users who should receive SMS notifications
        users = get_notification_recipients(pond)
        
        for user in users:
            # Check for phone number in user profile
            if hasattr(user, 'profile') and hasattr(user.profile, 'phone_number'):
                if user.profile.phone_number:
                    recipients.append(user.profile.phone_number)
            # Check for phone number in user model (if extended)
            elif hasattr(user, 'phone_number') and user.phone_number:
                recipients.append(user.phone_number)
        
        # Remove duplicates and validate phone numbers
        recipients = list(set(recipients))
        valid_recipients = []
        
        for phone in recipients:
            # Basic phone number validation
            if re.match(r'^\+?[1-9]\d{1,14}$', str(phone)):
                valid_recipients.append(str(phone))
        
        return valid_recipients
        
    except Exception as e:
        logger.error(f"Error getting SMS recipients: {str(e)}")
        return []

def send_weather_sms(pond, alert, recipients):
    """
    Send SMS notifications for weather alerts
    """
    try:
        pond_name = sanitize_pond_name(pond.name)
        
        # Create SMS message (limited to 160 characters for compatibility)
        message = f"Weather Alert - {pond_name}: {sanitize_alert_message(alert['message'])[:80]}. Severity: {alert['severity'].upper()}. Check email for details."
        
        # SMS provider configuration from settings
        sms_provider = getattr(settings, 'SMS_PROVIDER', None)
        sms_api_key = getattr(settings, 'SMS_API_KEY', None)
        
        if not sms_provider or not sms_api_key:
            logger.warning("SMS provider not configured, skipping SMS notifications")
            return
        
        # Send SMS based on provider
        if sms_provider.lower() == 'twilio':
            send_twilio_sms(message, recipients)
        elif sms_provider.lower() == 'aws_sns':
            send_aws_sns_sms(message, recipients)
        else:
            logger.warning(f"Unsupported SMS provider: {sms_provider}")
            
    except Exception as e:
        logger.error(f"Error sending weather SMS: {str(e)}")

def send_twilio_sms(message, recipients):
    """
    Send SMS using Twilio API
    """
    try:
        # This would require twilio library: pip install twilio
        # from twilio.rest import Client
        
        # account_sid = getattr(settings, 'TWILIO_ACCOUNT_SID', None)
        # auth_token = getattr(settings, 'SMS_API_KEY', None)
        # from_number = getattr(settings, 'TWILIO_FROM_NUMBER', None)
        
        # if not all([account_sid, auth_token, from_number]):
        #     logger.error("Twilio configuration incomplete")
        #     return
        
        # client = Client(account_sid, auth_token)
        
        # for recipient in recipients:
        #     try:
        #         message = client.messages.create(
        #             body=message,
        #             from_=from_number,
        #             to=recipient
        #         )
        #         logger.info(f"SMS sent to {recipient}: {message.sid}")
        #     except Exception as e:
        #         logger.error(f"Failed to send SMS to {recipient}: {str(e)}")
        
        # For now, just log the SMS (implement when Twilio is configured)
        logger.info(f"SMS notification (Twilio): {message} to {len(recipients)} recipients")
        
    except Exception as e:
        logger.error(f"Error with Twilio SMS: {str(e)}")

def send_aws_sns_sms(message, recipients):
    """
    Send SMS using AWS SNS
    """
    try:
        # This would require boto3 library: pip install boto3
        # import boto3
        
        # sns_client = boto3.client('sns',
        #     aws_access_key_id=getattr(settings, 'AWS_ACCESS_KEY_ID', None),
        #     aws_secret_access_key=getattr(settings, 'AWS_SECRET_ACCESS_KEY', None),
        #     region_name=getattr(settings, 'AWS_REGION', 'us-east-1')
        # )
        
        # for recipient in recipients:
        #     try:
        #         response = sns_client.publish(
        #             PhoneNumber=recipient,
        #             Message=message
        #         )
        #         logger.info(f"SMS sent to {recipient}: {response['MessageId']}")
        #     except Exception as e:
        #         logger.error(f"Failed to send SMS to {recipient}: {str(e)}")
        
        # For now, just log the SMS (implement when AWS is configured)
        logger.info(f"SMS notification (AWS SNS): {message} to {len(recipients)} recipients")
        
    except Exception as e:
        logger.error(f"Error with AWS SNS SMS: {str(e)}")

def store_weather_data(pond, weather_data):
    """
    Store weather data in the database with comprehensive validation
    """
    try:
        current = weather_data['current']
        
        # Get or create weather station for this pond with secure validation
        pond_lat = safe_numeric_value(pond.latitude, -90, 90, 0)
        pond_lon = safe_numeric_value(pond.longitude, -180, 180, 0)
        
        weather_station, created = WeatherStation.objects.get_or_create(
            latitude=pond_lat,
            longitude=pond_lon,
            defaults={
                'name': f"Weather Station - {sanitize_pond_name(pond.name)}",
                'is_active': True
            }
        )
        
        # Associate weather station with pond if not already associated
        if not pond.weather_station:
            pond.weather_station = weather_station
            pond.save()
        
        # Validate and sanitize numeric values with enhanced security
        temperature = safe_numeric_value(current['main']['temp'], -50, 60, 25)
        humidity = safe_numeric_value(current['main']['humidity'], 0, 100, 50)
        pressure = safe_numeric_value(current['main']['pressure'], 800, 1200, 1013)
        wind_speed = safe_numeric_value(current['wind']['speed'], 0, 100, 0)
        wind_direction = safe_numeric_value(current['wind'].get('deg', 0), 0, 360, 0)
        visibility = safe_numeric_value(current.get('visibility', 0), 0, 50000, 10000)  
        uv_index = safe_numeric_value(current.get('uvi', 0), 0, 15, 0)
        
        # Sanitize string fields with length limits
        weather_condition = sanitize_weather_description(current['weather'][0]['main'])[:50]
        weather_description = sanitize_weather_description(current['weather'][0]['description'])[:100]
        
        # Handle precipitation data safely
        precipitation = 0.0
        if 'rain' in current:
            precipitation += safe_numeric_value(current['rain'].get('1h', 0), 0, 1000, 0)
        if 'snow' in current:
            precipitation += safe_numeric_value(current['snow'].get('1h', 0), 0, 1000, 0)
        
        # Create weather data record
        weather_data_record = WeatherData.objects.create(
            weather_station=weather_station,
            timestamp=timezone.now(),
            temperature=temperature,
            humidity=humidity,
            pressure=pressure,
            wind_speed=wind_speed,
            wind_direction=wind_direction,
            precipitation=precipitation,
            visibility=visibility,
            uv_index=uv_index,
            weather_condition=weather_condition,
            weather_description=weather_description,
            data_source='openweather'
        )
        
        logger.debug(f"Weather data stored for pond {pond.id}: T={temperature}°C, H={humidity}%")
        return weather_data_record
        
    except Exception as e:
        logger.error(f"Error storing weather data for pond {pond.id}: {str(e)}")
        return None

# Import security functions at the end to complete the file
import logging
