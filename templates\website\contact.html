{% extends 'website/base.html' %}

{% block content %}
<!-- Contact Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Get In Touch
                </h1>
                <p class="lead mb-0" data-aos="fade-up" data-aos-delay="100">
                    We're here to help transform your aquaculture operations
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <div class="row g-5">
            <!-- Contact Form -->
            <div class="col-lg-8">
                <div class="feature-card" data-aos="fade-right">
                    <h3 class="mb-4">Send us a message</h3>
                    
                    <form id="contactForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6">
                                <label for="company" class="form-label">Company</label>
                                <input type="text" class="form-control" id="company" name="company">
                            </div>
                            <div class="col-md-6">
                                <label for="subject" class="form-label">Subject *</label>
                                <select class="form-control" id="subject" name="subject" required>
                                    <option value="">Select a subject</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Product Demo">Product Demo</option>
                                    <option value="Pricing Information">Pricing Information</option>
                                    <option value="Technical Support">Technical Support</option>
                                    <option value="Partnership">Partnership</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label for="message" class="form-label">Message *</label>
                                <textarea class="form-control" id="message" name="message" rows="6" required></textarea>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-gradient">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <div id="contactResult" class="mt-3" style="display: none;"></div>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="feature-card" data-aos="fade-left">
                    <h4 class="mb-4">Contact Information</h4>
                    
                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #667eea, #764ba2);">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Email</h6>
                                <span class="text-muted"><EMAIL></span>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #10b981, #059669);">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Phone</h6>
                                <span class="text-muted">+****************</span>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #f59e0b, #d97706);">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Office</h6>
                                <span class="text-muted">123 Innovation Drive<br>Tech City, TC 12345</span>
                            </div>
                        </div>
                        
                        <div class="d-flex align-items-center mb-3">
                            <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">Business Hours</h6>
                                <span class="text-muted">Mon - Fri: 9:00 AM - 6:00 PM<br>Sat: 10:00 AM - 4:00 PM</span>
                            </div>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <h5 class="mb-3">Follow Us</h5>
                    <div class="d-flex gap-3">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <a href="#" class="btn btn-outline-danger btn-sm">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="feature-card" data-aos="fade-up">
                    <h3 class="mb-4 text-center">Visit Our Office</h3>
                    <div class="map-placeholder" style="height: 400px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: white;">
                        <div class="text-center">
                            <i class="fas fa-map-marked-alt" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                            <h4>Interactive Map</h4>
                            <p class="mb-0">123 Innovation Drive, Tech City, TC 12345</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Support Options Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-4 fw-bold" data-aos="fade-up">
                    Need Help?
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Multiple ways to get the support you need
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4">
                <div class="feature-card text-center" data-aos="fade-up">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-book"></i>
                    </div>
                    <h5>Documentation</h5>
                    <p class="text-muted mb-4">
                        Comprehensive guides and tutorials to help you get the most out of our platform.
                    </p>
                    <a href="{% url 'website:documentation' %}" class="btn btn-outline-gradient">
                        <i class="fas fa-external-link-alt me-2"></i>View Docs
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h5>Support Center</h5>
                    <p class="text-muted mb-4">
                        FAQs, troubleshooting guides, and step-by-step solutions to common issues.
                    </p>
                    <a href="{% url 'website:support' %}" class="btn btn-outline-gradient">
                        <i class="fas fa-external-link-alt me-2"></i>Get Help
                    </a>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5>Community</h5>
                    <p class="text-muted mb-4">
                        Connect with other users, share experiences, and learn from the community.
                    </p>
                    <a href="#" class="btn btn-outline-gradient">
                        <i class="fas fa-external-link-alt me-2"></i>Join Community
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        company: formData.get('company'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;
    
    fetch('{% url "website:contact" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('contactResult');
        resultDiv.style.display = 'block';
        
        if (data.status === 'success') {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                </div>
            `;
            this.reset();
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    ${data.message}
                </div>
            `;
        }
    })
    .catch(error => {
        const resultDiv = document.getElementById('contactResult');
        resultDiv.style.display = 'block';
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>
                There was an error sending your message. Please try again.
            </div>
        `;
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
{% endblock %}
