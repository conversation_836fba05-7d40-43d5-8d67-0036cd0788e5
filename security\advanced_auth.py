"""
Advanced Authentication System
Implements multi-factor authentication, biometric authentication,
and adaptive authentication mechanisms
"""

import pyotp
import qrcode
import io
import base64
import hashlib
import secrets
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
import jwt
from cryptography.fernet import Fernet
import requests
import json

logger = logging.getLogger(__name__)

@dataclass
class AuthenticationResult:
    """Authentication result with detailed information"""
    success: bool
    user_id: Optional[str] = None
    session_token: Optional[str] = None
    mfa_required: bool = False
    mfa_methods: List[str] = None
    risk_score: float = 0.0
    trust_level: str = "unknown"
    expires_at: Optional[datetime] = None
    error_message: Optional[str] = None

    def __post_init__(self):
        if self.mfa_methods is None:
            self.mfa_methods = []

@dataclass
class BiometricTemplate:
    """Biometric authentication template"""
    user_id: str
    template_type: str  # fingerprint, face, voice
    template_data: str  # Encrypted biometric template
    created_at: datetime
    last_used: Optional[datetime] = None
    active: bool = True

class AdvancedAuthenticationSystem:
    """
    Advanced Authentication System with MFA, Biometrics, and Adaptive Auth
    """

    def __init__(self):
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.mfa_methods = {
            "totp": self._verify_totp,
            "sms": self._verify_sms,
            "email": self._verify_email,
            "push": self._verify_push_notification,
            "biometric": self._verify_biometric
        }
        self.adaptive_rules = self._setup_adaptive_rules()

    def _get_encryption_key(self) -> bytes:
        """Get encryption key for sensitive data"""
        key = getattr(settings, 'BIOMETRIC_ENCRYPTION_KEY', None)
        if not key:
            key = Fernet.generate_key()
            logger.warning("Using generated encryption key. Set BIOMETRIC_ENCRYPTION_KEY in production.")
        return key if isinstance(key, bytes) else key.encode()

    def _setup_adaptive_rules(self) -> Dict[str, Any]:
        """Setup adaptive authentication rules"""
        return {
            "high_risk_threshold": 70.0,
            "medium_risk_threshold": 40.0,
            "require_mfa_for_high_risk": True,
            "require_additional_factor_for_admin": True,
            "max_failed_attempts": 5,
            "lockout_duration": 900,  # 15 minutes
            "session_timeout": 3600,  # 1 hour
            "remember_device_duration": 86400 * 30  # 30 days
        }

    def authenticate(self, username: str, password: str,
                    request_context: Dict[str, Any]) -> AuthenticationResult:
        """Primary authentication with adaptive security"""
        try:
            # Basic credential validation
            user = self._validate_credentials(username, password)
            if not user:
                self._log_failed_attempt(username, request_context.get('ip_address'))
                return AuthenticationResult(
                    success=False,
                    error_message="Invalid credentials"
                )

            # Check account lockout
            if self._is_account_locked(user.id):
                return AuthenticationResult(
                    success=False,
                    error_message="Account temporarily locked"
                )

            # Calculate risk score
            from .zero_trust import zero_trust_engine
            security_context = zero_trust_engine.evaluate_security_context({
                'user_id': str(user.id),
                'ip_address': request_context.get('ip_address', ''),
                'user_agent': request_context.get('user_agent', ''),
                'device_id': request_context.get('device_id', ''),
                'session_id': request_context.get('session_id', '')
            })

            # Determine MFA requirements
            mfa_required, mfa_methods = self._determine_mfa_requirements(
                user, security_context, request_context
            )

            if mfa_required:
                # Store partial authentication state
                partial_token = self._create_partial_auth_token(user.id, security_context)

                return AuthenticationResult(
                    success=False,
                    user_id=str(user.id),
                    mfa_required=True,
                    mfa_methods=mfa_methods,
                    session_token=partial_token,
                    risk_score=security_context.risk_score,
                    trust_level=security_context.trust_level
                )

            # Create full session
            session_token = self._create_session_token(user.id, security_context)
            expires_at = datetime.now() + timedelta(seconds=self.adaptive_rules['session_timeout'])

            # Log successful authentication
            self._log_successful_auth(user.id, request_context)

            return AuthenticationResult(
                success=True,
                user_id=str(user.id),
                session_token=session_token,
                risk_score=security_context.risk_score,
                trust_level=security_context.trust_level,
                expires_at=expires_at
            )

        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return AuthenticationResult(
                success=False,
                error_message="Authentication error"
            )

    def _validate_credentials(self, username: str, password: str) -> Optional[User]:
        """Validate user credentials"""
        try:
            from django.contrib.auth import authenticate
            user = authenticate(username=username, password=password)
            return user
        except Exception as e:
            logger.error(f"Credential validation failed: {e}")
            return None

    def _is_account_locked(self, user_id: int) -> bool:
        """Check if account is locked due to failed attempts"""
        cache_key = f"account_locked:{user_id}"
        return cache.get(cache_key, False)

    def _determine_mfa_requirements(self, user: User, security_context,
                                  request_context: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Determine MFA requirements based on risk and policy"""
        available_methods = []

        # Check user's enrolled MFA methods
        if self._has_totp_enrolled(user.id):
            available_methods.append("totp")

        if self._has_sms_enrolled(user.id):
            available_methods.append("sms")

        if self._has_biometric_enrolled(user.id):
            available_methods.append("biometric")

        # Always add email as fallback
        available_methods.append("email")

        # Determine if MFA is required
        mfa_required = False

        # High risk always requires MFA
        if security_context.risk_score >= self.adaptive_rules['high_risk_threshold']:
            mfa_required = True

        # Admin users always require MFA
        if user.is_staff or user.is_superuser:
            mfa_required = True

        # New device requires MFA
        if not security_context.device_trusted:
            mfa_required = True

        # Check if device is remembered
        device_id = request_context.get('device_id', '')
        if device_id and self._is_device_remembered(user.id, device_id):
            mfa_required = False

        return mfa_required, available_methods

    def verify_mfa(self, partial_token: str, method: str,
                  code: str, request_context: Dict[str, Any]) -> AuthenticationResult:
        """Verify multi-factor authentication"""
        try:
            # Validate partial token
            partial_data = self._validate_partial_token(partial_token)
            if not partial_data:
                return AuthenticationResult(
                    success=False,
                    error_message="Invalid or expired token"
                )

            user_id = partial_data['user_id']

            # Verify MFA code
            if method not in self.mfa_methods:
                return AuthenticationResult(
                    success=False,
                    error_message="Invalid MFA method"
                )

            verification_result = self.mfa_methods[method](user_id, code, request_context)

            if not verification_result:
                self._log_failed_mfa(user_id, method, request_context.get('ip_address'))
                return AuthenticationResult(
                    success=False,
                    error_message="Invalid MFA code"
                )

            # Create full session
            security_context = partial_data['security_context']
            session_token = self._create_session_token(user_id, security_context)
            expires_at = datetime.now() + timedelta(seconds=self.adaptive_rules['session_timeout'])

            # Remember device if requested
            if request_context.get('remember_device', False):
                self._remember_device(user_id, request_context.get('device_id', ''))

            # Log successful MFA
            self._log_successful_mfa(user_id, method, request_context)

            return AuthenticationResult(
                success=True,
                user_id=str(user_id),
                session_token=session_token,
                expires_at=expires_at
            )

        except Exception as e:
            logger.error(f"MFA verification failed: {e}")
            return AuthenticationResult(
                success=False,
                error_message="MFA verification error"
            )

    def _verify_totp(self, user_id: int, code: str, context: Dict[str, Any]) -> bool:
        """Verify TOTP (Time-based One-Time Password)"""
        try:
            # Get user's TOTP secret
            secret = self._get_totp_secret(user_id)
            if not secret:
                return False

            # Verify TOTP code
            totp = pyotp.TOTP(secret)
            return totp.verify(code, valid_window=1)  # Allow 30-second window

        except Exception as e:
            logger.error(f"TOTP verification failed: {e}")
            return False

    def _verify_sms(self, user_id: int, code: str, context: Dict[str, Any]) -> bool:
        """Verify SMS code"""
        try:
            # Check stored SMS code
            cache_key = f"sms_code:{user_id}"
            stored_code = cache.get(cache_key)

            if stored_code and stored_code == code:
                cache.delete(cache_key)  # One-time use
                return True

            return False

        except Exception as e:
            logger.error(f"SMS verification failed: {e}")
            return False

    def _verify_email(self, user_id: int, code: str, context: Dict[str, Any]) -> bool:
        """Verify email code"""
        try:
            # Check stored email code
            cache_key = f"email_code:{user_id}"
            stored_code = cache.get(cache_key)

            if stored_code and stored_code == code:
                cache.delete(cache_key)  # One-time use
                return True

            return False

        except Exception as e:
            logger.error(f"Email verification failed: {e}")
            return False

    def _verify_push_notification(self, user_id: int, code: str, context: Dict[str, Any]) -> bool:
        """Verify push notification response"""
        try:
            # Check push notification response
            cache_key = f"push_response:{user_id}"
            response = cache.get(cache_key)

            if response and response.get('approved', False):
                cache.delete(cache_key)  # One-time use
                return True

            return False

        except Exception as e:
            logger.error(f"Push notification verification failed: {e}")
            return False

    def _verify_biometric(self, user_id: int, biometric_data: str, context: Dict[str, Any]) -> bool:
        """Verify biometric authentication"""
        try:
            # Get stored biometric templates
            templates = self._get_biometric_templates(user_id)

            for template in templates:
                if self._match_biometric_template(biometric_data, template):
                    # Update last used timestamp
                    self._update_biometric_usage(template.user_id, template.template_type)
                    return True

            return False

        except Exception as e:
            logger.error(f"Biometric verification failed: {e}")
            return False