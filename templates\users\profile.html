{% extends 'base.html' %}
{% load static %}

{% block title %}User Profile{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">User Profile</h1>
                    <p class="text-muted">Manage your account information and preferences</p>
                </div>
                <div>
                    <a href="{% url 'core:dashboard' %}" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Profile Content -->
            <div class="row">
                <!-- Profile Photo Section -->
                <div class="col-lg-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0"><i class="fas fa-camera me-2"></i>Profile Photo</h5>
                        </div>
                        <div class="card-body text-center">
                            <div class="mb-3">
                                {% if user.profile_photo %}
                                    <img src="{{ user.profile_photo.url }}" alt="Profile Photo" 
                                         class="rounded-circle shadow" style="width: 150px; height: 150px; object-fit: cover;">
                                {% else %}
                                    <div class="rounded-circle shadow d-inline-flex align-items-center justify-content-center"
                                         style="width: 150px; height: 150px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; font-size: 3rem;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                            </div>
                            
                            <form method="post" enctype="multipart/form-data" id="photoForm">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <input type="file" class="form-control" id="profile_photo" name="profile_photo" 
                                           accept="image/*" onchange="previewImage(this)">
                                </div>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-upload me-2"></i>Update Photo
                                </button>
                            </form>
                            
                            {% if user.profile_photo %}
                            <form method="post" class="mt-2" id="removePhotoForm">
                                {% csrf_token %}
                                <input type="hidden" name="remove_photo" value="1">
                                <button type="submit" class="btn btn-outline-danger btn-sm"
                                        onclick="return confirm('Are you sure you want to remove your profile photo?');">
                                    <i class="fas fa-trash me-2"></i>Remove Photo
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Profile Information -->
                <div class="col-lg-8">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profile Information</h5>
                        </div>
                        <div class="card-body">
                            <form method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="first_name" class="form-label">First Name</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="{{ user.first_name|default:'' }}" placeholder="Enter first name">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="last_name" class="form-label">Last Name</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="{{ user.last_name|default:'' }}" placeholder="Enter last name">
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" class="form-control" id="email" 
                                               value="{{ user.email }}" readonly>
                                        <small class="text-muted">Email cannot be changed</small>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control" id="username" 
                                               value="{{ user.username }}" readonly>
                                        <small class="text-muted">Username cannot be changed</small>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="role" class="form-label">Role</label>
                                        <input type="text" class="form-control" id="role" 
                                               value="{{ user.role|default:'User' }}" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="date_joined" class="form-label">Member Since</label>
                                        <input type="text" class="form-control" id="date_joined" 
                                               value="{{ user.date_joined|date:'F d, Y' }}" readonly>
                                    </div>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="col-lg-4 mt-4">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Account Summary</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Account Status</span>
                                <span class="badge bg-success">Active</span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Last Login</span>
                                <small class="text-muted">{{ user.last_login|date:'M d, Y H:i' }}</small>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <span>Profile Completion</span>
                                <div class="progress flex-grow-1 ms-3" style="height: 10px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {% if user.first_name and user.last_name %}100{% else %}60{% endif %}%" 
                                         aria-valuenow="{% if user.first_name and user.last_name %}100{% else %}60{% endif %}" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <span class="ms-2">{% if user.first_name and user.last_name %}100{% else %}60{% endif %}%</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card shadow-sm">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{% url 'core:dashboard' %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                                <a href="/app/ponds/" class="btn btn-outline-info btn-sm">
                                    <i class="fas fa-water me-2"></i>View Ponds
                                </a>
                                <a href="/app/reports/" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-chart-line me-2"></i>Reports
                                </a>
                                <a href="{% url 'users:logout' %}" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-sign-out-alt me-2"></i>Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Security</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Change Password</h6>
                                    <p class="text-muted">Update your account password for better security.</p>
                                    <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                                        <i class="fas fa-key me-2"></i>Change Password
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <h6>Two-Factor Authentication</h6>
                                    <p class="text-muted">Add an extra layer of security to your account.</p>
                                    <button class="btn btn-outline-warning btn-sm" disabled>
                                        <i class="fas fa-mobile-alt me-2"></i>Enable 2FA (Coming Soon)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger">Update Password</button>
            </div>
        </div>
    </div>
</div>

<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Find the image element or create preview
            const imgContainer = input.closest('.card-body').querySelector('.mb-3');
            let img = imgContainer.querySelector('img');
            
            if (!img) {
                // Replace the icon div with an image
                const iconDiv = imgContainer.querySelector('div');
                if (iconDiv) {
                    img = document.createElement('img');
                    img.className = 'rounded-circle shadow';
                    img.style.width = '150px';
                    img.style.height = '150px';
                    img.style.objectFit = 'cover';
                    img.alt = 'Profile Photo Preview';
                    iconDiv.parentNode.replaceChild(img, iconDiv);
                }
            }
            
            if (img) {
                img.src = e.target.result;
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>
{% endblock %}
