<!-- Feed Management Section -->
<div class="feed-management-section fade-in widget-container" data-widget-id="feed-management">
    <div class="widget-header">
        <h2 class="section-title">
            <i class="fas fa-utensils"></i>
            Feed Management
        </h2>
        <div class="widget-actions">
            <button class="widget-action-button" title="Refresh" id="refreshFeedData">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="widget-action-button" title="Settings" id="feedSettings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="widget-action-button widget-move-handle" title="Move" style="display: none;">
                <i class="fas fa-grip-vertical"></i>
            </button>
        </div>
    </div>
    
    <div class="feed-management-container">
        <div class="feed-tabs">
            <button class="feed-tab active" data-tab="schedule">
                <i class="fas fa-calendar-alt"></i>
                Feeding Schedule
            </button>
            <button class="feed-tab" data-tab="inventory">
                <i class="fas fa-warehouse"></i>
                Feed Inventory
            </button>
            <button class="feed-tab" data-tab="analytics">
                <i class="fas fa-chart-line"></i>
                Feed Analytics
            </button>
            <button class="feed-tab" data-tab="calculator">
                <i class="fas fa-calculator"></i>
                Feed Calculator
            </button>
        </div>
        
        <div class="feed-content">
            <!-- Feeding Schedule Tab -->
            <div class="feed-tab-content active" data-tab="schedule">
                <div class="schedule-header">
                    <div class="schedule-date-selector">
                        <button class="date-nav-btn" id="prevDate">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="current-date" id="currentDate">Today, June 15, 2023</div>
                        <button class="date-nav-btn" id="nextDate">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="schedule-actions">
                        <button class="schedule-action-btn" id="addFeedingBtn">
                            <i class="fas fa-plus"></i>
                            Add Feeding
                        </button>
                        <button class="schedule-action-btn secondary" id="viewCalendarBtn">
                            <i class="fas fa-calendar-week"></i>
                            Calendar View
                        </button>
                    </div>
                </div>
                
                <div class="feeding-schedule">
                    <div class="schedule-timeline">
                        <div class="timeline-hour">6:00 AM</div>
                        <div class="timeline-hour">9:00 AM</div>
                        <div class="timeline-hour">12:00 PM</div>
                        <div class="timeline-hour">3:00 PM</div>
                        <div class="timeline-hour">6:00 PM</div>
                        <div class="timeline-hour">9:00 PM</div>
                    </div>
                    
                    <div class="schedule-ponds">
                        <div class="schedule-pond" data-pond="1">
                            <div class="pond-label">Pond 1</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 10%; width: 10%;" data-time="06:30 AM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:30 AM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 30%; width: 10%;" data-time="12:00 PM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">12:00 PM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 70%; width: 10%;" data-time="06:00 PM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:00 PM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="2">
                            <div class="pond-label">Pond 2</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 12%; width: 10%;" data-time="07:00 AM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:00 AM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 32%; width: 10%;" data-time="12:30 PM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">12:30 PM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 72%; width: 10%;" data-time="06:30 PM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:30 PM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="3">
                            <div class="pond-label">Pond 3</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 14%; width: 10%;" data-time="07:30 AM" data-amount="4 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:30 AM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 34%; width: 10%;" data-time="01:00 PM" data-amount="4.5 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">1:00 PM</div>
                                        <div class="tooltip-amount">4.5 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 74%; width: 10%;" data-time="07:00 PM" data-amount="4.5 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:00 PM</div>
                                        <div class="tooltip-amount">4.5 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="4">
                            <div class="pond-label">Pond 4</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 16%; width: 10%;" data-time="08:00 AM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:00 AM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 36%; width: 10%;" data-time="01:30 PM" data-amount="4 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">1:30 PM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 76%; width: 10%;" data-time="07:30 PM" data-amount="4 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:30 PM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="5">
                            <div class="pond-label">Pond 5</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 18%; width: 10%;" data-time="08:30 AM" data-amount="2 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:30 AM</div>
                                        <div class="tooltip-amount">2 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 38%; width: 10%;" data-time="02:00 PM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">2:00 PM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 78%; width: 10%;" data-time="08:00 PM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:00 PM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="schedule-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Today's Feedings</div>
                            <div class="summary-value">15</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-weight"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Total Feed Amount</div>
                            <div class="summary-value">45 kg</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Feed Cost</div>
                            <div class="summary-value">$135.00</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">FCR (Avg)</div>
                            <div class="summary-value">1.35</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Inventory Tab -->
            <div class="feed-tab-content" data-tab="inventory">
                <div class="inventory-header">
                    <div class="inventory-search">
                        <input type="text" class="inventory-search-input" placeholder="Search feed inventory...">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    
                    <div class="inventory-actions">
                        <button class="inventory-action-btn" id="addInventoryBtn">
                            <i class="fas fa-plus"></i>
                            Add Inventory
                        </button>
                        <button class="inventory-action-btn secondary" id="exportInventoryBtn">
                            <i class="fas fa-file-export"></i>
                            Export
                        </button>
                    </div>
                </div>
                
                <div class="inventory-table-container">
                    <table class="inventory-table">
                        <thead>
                            <tr>
                                <th>Feed Type</th>
                                <th>Brand</th>
                                <th>Protein %</th>
                                <th>Current Stock</th>
                                <th>Unit Price</th>
                                <th>Total Value</th>
                                <th>Reorder Level</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Starter Feed</td>
                                <td>AquaPrime</td>
                                <td>40%</td>
                                <td>120 kg</td>
                                <td>$3.20/kg</td>
                                <td>$384.00</td>
                                <td>50 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Growth Feed</td>
                                <td>AquaGrow</td>
                                <td>35%</td>
                                <td>85 kg</td>
                                <td>$2.90/kg</td>
                                <td>$246.50</td>
                                <td>100 kg</td>
                                <td><span class="status-badge warning">Low</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Finisher Feed</td>
                                <td>AquaFinish</td>
                                <td>32%</td>
                                <td>210 kg</td>
                                <td>$2.75/kg</td>
                                <td>$577.50</td>
                                <td>100 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Special Feed</td>
                                <td>AquaBoost</td>
                                <td>38%</td>
                                <td>25 kg</td>
                                <td>$4.50/kg</td>
                                <td>$112.50</td>
                                <td>30 kg</td>
                                <td><span class="status-badge critical">Critical</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Medicinal Feed</td>
                                <td>AquaHealth</td>
                                <td>36%</td>
                                <td>50 kg</td>
                                <td>$5.20/kg</td>
                                <td>$260.00</td>
                                <td>40 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="inventory-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Total Inventory</div>
                            <div class="summary-value">490 kg</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Inventory Value</div>
                            <div class="summary-value">$1,580.50</div>
                        </div>
                    </div>
                    
                    <div class="summary-card warning">
                        <div class="summary-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Low Stock Items</div>
                            <div class="summary-value">2</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Pending Orders</div>
                            <div class="summary-value">1</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Analytics Tab -->
            <div class="feed-tab-content" data-tab="analytics">
                <div class="analytics-header">
                    <div class="analytics-filters">
                        <div class="filter-group">
                            <label class="filter-label">Date Range</label>
                            <select class="filter-select" id="feedDateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="180">Last 6 Months</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">Pond</label>
                            <select class="filter-select" id="feedPondFilter">
                                <option value="all">All Ponds</option>
                                <option value="1">Pond 1</option>
                                <option value="2">Pond 2</option>
                                <option value="3">Pond 3</option>
                                <option value="4">Pond 4</option>
                                <option value="5">Pond 5</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">Feed Type</label>
                            <select class="filter-select" id="feedTypeFilter">
                                <option value="all">All Types</option>
                                <option value="starter">Starter</option>
                                <option value="growth">Growth</option>
                                <option value="finisher">Finisher</option>
                                <option value="special">Special</option>
                                <option value="medicinal">Medicinal</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="analytics-actions">
                        <button class="analytics-action-btn" id="refreshAnalyticsBtn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <button class="analytics-action-btn secondary" id="exportAnalyticsBtn">
                            <i class="fas fa-file-export"></i>
                            Export
                        </button>
                    </div>
                </div>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Usage Over Time</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedUsageChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Conversion Ratio (FCR)</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="fcrChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Type Distribution</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedTypeChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Cost Analysis</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedCostChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="analytics-insights">
                    <h3 class="insights-title">Key Insights</h3>
                    <div class="insights-grid">
                        <div class="insight-card">
                            <div class="insight-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Feed Usage Trend</div>
                                <div class="insight-value">+12% <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed usage has increased by 12% compared to the previous period, indicating growth in biomass.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card positive">
                            <div class="insight-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">FCR Improvement</div>
                                <div class="insight-value">-0.15 <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed conversion ratio has improved by 0.15 points, indicating better feed efficiency.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card negative">
                            <div class="insight-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Feed Cost per kg</div>
                                <div class="insight-value">+$0.25 <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed cost per kg has increased due to higher prices of raw materials.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card">
                            <div class="insight-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Optimal Feed Type</div>
                                <div class="insight-value">Growth Feed <span class="trend-period">best performance</span></div>
                                <div class="insight-description">Growth feed shows the best FCR performance across all ponds in the current cycle.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Calculator Tab -->
            <div class="feed-tab-content" data-tab="calculator">
                <div class="calculator-container">
                    <div class="calculator-sidebar">
                        <div class="calculator-form">
                            <h3 class="calculator-form-title">Feed Rate Calculator</h3>
                            
                            <div class="form-group">
                                <label class="form-label">Pond</label>
                                <select class="form-select" id="calcPond">
                                    <option value="1">Pond 1</option>
                                    <option value="2">Pond 2</option>
                                    <option value="3">Pond 3</option>
                                    <option value="4">Pond 4</option>
                                    <option value="5">Pond 5</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Shrimp Count (estimated)</label>
                                <input type="number" class="form-input" id="shrimpCount" value="100000">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Average Weight (g)</label>
                                <input type="number" class="form-input" id="avgWeight" value="12.5" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Survival Rate (%)</label>
                                <input type="number" class="form-input" id="survivalRate" value="85" min="0" max="100">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Feeding Rate (% of biomass)</label>
                                <input type="number" class="form-input" id="feedingRate" value="3.5" step="0.1" min="0" max="10">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Number of Feedings per Day</label>
                                <select class="form-select" id="feedingsPerDay">
                                    <option value="2">2 times</option>
                                    <option value="3" selected>3 times</option>
                                    <option value="4">4 times</option>
                                    <option value="5">5 times</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Feed Type</label>
                                <select class="form-select" id="calcFeedType">
                                    <option value="starter">Starter Feed</option>
                                    <option value="growth" selected>Growth Feed</option>
                                    <option value="finisher">Finisher Feed</option>
                                </select>
                            </div>
                            
                            <div class="calculator-actions">
                                <button class="calculator-action-btn" id="calculateFeedBtn">
                                    <i class="fas fa-calculator"></i>
                                    Calculate
                                </button>
                                <button class="calculator-action-btn secondary" id="resetCalculatorBtn">
                                    <i class="fas fa-undo"></i>
                                    Reset
                                </button>
                            </div>
                        </div>
                        
                        <div class="calculator-presets">
                            <h3 class="presets-title">Quick Presets</h3>
                            <div class="preset-buttons">
                                <button class="preset-btn" data-weight="5" data-rate="4.5">
                                    Early Stage
                                </button>
                                <button class="preset-btn" data-weight="12" data-rate="3.5">
                                    Mid Stage
                                </button>
                                <button class="preset-btn" data-weight="20" data-rate="2.8">
                                    Late Stage
                                </button>
                                <button class="preset-btn" data-weight="25" data-rate="2.2">
                                    Pre-Harvest
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="calculator-results">
                        <div class="results-header">
                            <h3 class="results-title">Calculation Results</h3>
                            <div class="results-actions">
                                <button class="results-action-btn" id="saveCalculationBtn">
                                    <i class="fas fa-save"></i>
                                    Save
                                </button>
                                <button class="results-action-btn" id="printCalculationBtn">
                                    <i class="fas fa-print"></i>
                                    Print
                                </button>
                            </div>
                        </div>
                        
                        <div class="results-cards">
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-weight"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Estimated Biomass</div>
                                    <div class="result-value" id="biomassResult">1,062.5 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-utensils"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Daily Feed Amount</div>
                                    <div class="result-value" id="dailyFeedResult">37.2 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Per Feeding</div>
                                    <div class="result-value" id="perFeedingResult">12.4 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Weekly Feed Amount</div>
                                    <div class="result-value" id="weeklyFeedResult">260.4 kg</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="feeding-schedule-result">
                            <h3 class="schedule-title">Recommended Feeding Schedule</h3>
                            <div class="schedule-table-container">
                                <table class="schedule-table">
                                    <thead>
                                        <tr>
                                            <th>Feeding #</th>
                                            <th>Time</th>
                                            <th>Amount</th>
                                            <th>Feed Type</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>7:00 AM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Morning feeding when oxygen levels are rising</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>1:00 PM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Midday feeding during active period</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>7:00 PM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Evening feeding before oxygen levels decrease</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="calculation-notes">
                            <h3 class="notes-title">Notes & Recommendations</h3>
                            <div class="notes-content">
                                <p><strong>Feeding Adjustments:</strong> Monitor feed consumption after each feeding. If feed is not consumed within 2 hours, reduce the amount by 10% for the next feeding.</p>
                                <p><strong>Water Quality:</strong> Check oxygen levels before each feeding. If below 4 mg/L, delay feeding until levels improve.</p>
                                <p><strong>Weather Considerations:</strong> During cloudy days, reduce feeding amount by 20% as metabolism slows down.</p>
                                <p><strong>Feed Conversion:</strong> With current settings, expected FCR is approximately 1.35-1.45.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!-- Feed Management Section -->
<div class="feed-management-section fade-in widget-container" data-widget-id="feed-management">
    <div class="widget-header">
        <h2 class="section-title">
            <i class="fas fa-utensils"></i>
            Feed Management
        </h2>
        <div class="widget-actions">
            <button class="widget-action-button" title="Refresh" id="refreshFeedData">
                <i class="fas fa-sync-alt"></i>
            </button>
            <button class="widget-action-button" title="Settings" id="feedSettings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="widget-action-button widget-move-handle" title="Move" style="display: none;">
                <i class="fas fa-grip-vertical"></i>
            </button>
        </div>
    </div>
    
    <div class="feed-management-container">
        <div class="feed-tabs">
            <button class="feed-tab active" data-tab="schedule">
                <i class="fas fa-calendar-alt"></i>
                Feeding Schedule
            </button>
            <button class="feed-tab" data-tab="inventory">
                <i class="fas fa-warehouse"></i>
                Feed Inventory
            </button>
            <button class="feed-tab" data-tab="analytics">
                <i class="fas fa-chart-line"></i>
                Feed Analytics
            </button>
            <button class="feed-tab" data-tab="calculator">
                <i class="fas fa-calculator"></i>
                Feed Calculator
            </button>
        </div>
        
        <div class="feed-content">
            <!-- Feeding Schedule Tab -->
            <div class="feed-tab-content active" data-tab="schedule">
                <div class="schedule-header">
                    <div class="schedule-date-selector">
                        <button class="date-nav-btn" id="prevDate">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="current-date" id="currentDate">Today, June 15, 2023</div>
                        <button class="date-nav-btn" id="nextDate">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    
                    <div class="schedule-actions">
                        <button class="schedule-action-btn" id="addFeedingBtn">
                            <i class="fas fa-plus"></i>
                            Add Feeding
                        </button>
                        <button class="schedule-action-btn secondary" id="viewCalendarBtn">
                            <i class="fas fa-calendar-week"></i>
                            Calendar View
                        </button>
                    </div>
                </div>
                
                <div class="feeding-schedule">
                    <div class="schedule-timeline">
                        <div class="timeline-hour">6:00 AM</div>
                        <div class="timeline-hour">9:00 AM</div>
                        <div class="timeline-hour">12:00 PM</div>
                        <div class="timeline-hour">3:00 PM</div>
                        <div class="timeline-hour">6:00 PM</div>
                        <div class="timeline-hour">9:00 PM</div>
                    </div>
                    
                    <div class="schedule-ponds">
                        <div class="schedule-pond" data-pond="1">
                            <div class="pond-label">Pond 1</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 10%; width: 10%;" data-time="06:30 AM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:30 AM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 30%; width: 10%;" data-time="12:00 PM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">12:00 PM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 70%; width: 10%;" data-time="06:00 PM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:00 PM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="2">
                            <div class="pond-label">Pond 2</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 12%; width: 10%;" data-time="07:00 AM" data-amount="3 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:00 AM</div>
                                        <div class="tooltip-amount">3 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 32%; width: 10%;" data-time="12:30 PM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">12:30 PM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 72%; width: 10%;" data-time="06:30 PM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">6:30 PM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="3">
                            <div class="pond-label">Pond 3</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 14%; width: 10%;" data-time="07:30 AM" data-amount="4 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:30 AM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 34%; width: 10%;" data-time="01:00 PM" data-amount="4.5 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">1:00 PM</div>
                                        <div class="tooltip-amount">4.5 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 74%; width: 10%;" data-time="07:00 PM" data-amount="4.5 kg" data-type="Finisher">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:00 PM</div>
                                        <div class="tooltip-amount">4.5 kg</div>
                                        <div class="tooltip-type">Finisher Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="4">
                            <div class="pond-label">Pond 4</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 16%; width: 10%;" data-time="08:00 AM" data-amount="3.5 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:00 AM</div>
                                        <div class="tooltip-amount">3.5 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 36%; width: 10%;" data-time="01:30 PM" data-amount="4 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">1:30 PM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 76%; width: 10%;" data-time="07:30 PM" data-amount="4 kg" data-type="Growth">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">7:30 PM</div>
                                        <div class="tooltip-amount">4 kg</div>
                                        <div class="tooltip-type">Growth Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="schedule-pond" data-pond="5">
                            <div class="pond-label">Pond 5</div>
                            <div class="pond-timeline">
                                <div class="feeding-event" style="left: 18%; width: 10%;" data-time="08:30 AM" data-amount="2 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:30 AM</div>
                                        <div class="tooltip-amount">2 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 38%; width: 10%;" data-time="02:00 PM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">2:00 PM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                                <div class="feeding-event" style="left: 78%; width: 10%;" data-time="08:00 PM" data-amount="2.5 kg" data-type="Starter">
                                    <div class="event-tooltip">
                                        <div class="tooltip-time">8:00 PM</div>
                                        <div class="tooltip-amount">2.5 kg</div>
                                        <div class="tooltip-type">Starter Feed</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="schedule-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Today's Feedings</div>
                            <div class="summary-value">15</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-weight"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Total Feed Amount</div>
                            <div class="summary-value">45 kg</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Feed Cost</div>
                            <div class="summary-value">$135.00</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">FCR (Avg)</div>
                            <div class="summary-value">1.35</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Inventory Tab -->
            <div class="feed-tab-content" data-tab="inventory">
                <div class="inventory-header">
                    <div class="inventory-search">
                        <input type="text" class="inventory-search-input" placeholder="Search feed inventory...">
                        <i class="fas fa-search search-icon"></i>
                    </div>
                    
                    <div class="inventory-actions">
                        <button class="inventory-action-btn" id="addInventoryBtn">
                            <i class="fas fa-plus"></i>
                            Add Inventory
                        </button>
                        <button class="inventory-action-btn secondary" id="exportInventoryBtn">
                            <i class="fas fa-file-export"></i>
                            Export
                        </button>
                    </div>
                </div>
                
                <div class="inventory-table-container">
                    <table class="inventory-table">
                        <thead>
                            <tr>
                                <th>Feed Type</th>
                                <th>Brand</th>
                                <th>Protein %</th>
                                <th>Current Stock</th>
                                <th>Unit Price</th>
                                <th>Total Value</th>
                                <th>Reorder Level</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Starter Feed</td>
                                <td>AquaPrime</td>
                                <td>40%</td>
                                <td>120 kg</td>
                                <td>$3.20/kg</td>
                                <td>$384.00</td>
                                <td>50 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Growth Feed</td>
                                <td>AquaGrow</td>
                                <td>35%</td>
                                <td>85 kg</td>
                                <td>$2.90/kg</td>
                                <td>$246.50</td>
                                <td>100 kg</td>
                                <td><span class="status-badge warning">Low</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Finisher Feed</td>
                                <td>AquaFinish</td>
                                <td>32%</td>
                                <td>210 kg</td>
                                <td>$2.75/kg</td>
                                <td>$577.50</td>
                                <td>100 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Special Feed</td>
                                <td>AquaBoost</td>
                                <td>38%</td>
                                <td>25 kg</td>
                                <td>$4.50/kg</td>
                                <td>$112.50</td>
                                <td>30 kg</td>
                                <td><span class="status-badge critical">Critical</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>Medicinal Feed</td>
                                <td>AquaHealth</td>
                                <td>36%</td>
                                <td>50 kg</td>
                                <td>$5.20/kg</td>
                                <td>$260.00</td>
                                <td>40 kg</td>
                                <td><span class="status-badge good">Good</span></td>
                                <td>
                                    <div class="table-actions">
                                        <button class="table-action-btn" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="table-action-btn" title="Add Stock">
                                            <i class="fas fa-plus-circle"></i>
                                        </button>
                                        <button class="table-action-btn" title="History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="inventory-summary">
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Total Inventory</div>
                            <div class="summary-value">490 kg</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Inventory Value</div>
                            <div class="summary-value">$1,580.50</div>
                        </div>
                    </div>
                    
                    <div class="summary-card warning">
                        <div class="summary-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Low Stock Items</div>
                            <div class="summary-value">2</div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <div class="summary-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="summary-content">
                            <div class="summary-title">Pending Orders</div>
                            <div class="summary-value">1</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Analytics Tab -->
            <div class="feed-tab-content" data-tab="analytics">
                <div class="analytics-header">
                    <div class="analytics-filters">
                        <div class="filter-group">
                            <label class="filter-label">Date Range</label>
                            <select class="filter-select" id="feedDateRange">
                                <option value="7">Last 7 Days</option>
                                <option value="30" selected>Last 30 Days</option>
                                <option value="90">Last 90 Days</option>
                                <option value="180">Last 6 Months</option>
                                <option value="365">Last Year</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">Pond</label>
                            <select class="filter-select" id="feedPondFilter">
                                <option value="all">All Ponds</option>
                                <option value="1">Pond 1</option>
                                <option value="2">Pond 2</option>
                                <option value="3">Pond 3</option>
                                <option value="4">Pond 4</option>
                                <option value="5">Pond 5</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label class="filter-label">Feed Type</label>
                            <select class="filter-select" id="feedTypeFilter">
                                <option value="all">All Types</option>
                                <option value="starter">Starter</option>
                                <option value="growth">Growth</option>
                                <option value="finisher">Finisher</option>
                                <option value="special">Special</option>
                                <option value="medicinal">Medicinal</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="analytics-actions">
                        <button class="analytics-action-btn" id="refreshAnalyticsBtn">
                            <i class="fas fa-sync-alt"></i>
                            Refresh
                        </button>
                        <button class="analytics-action-btn secondary" id="exportAnalyticsBtn">
                            <i class="fas fa-file-export"></i>
                            Export
                        </button>
                    </div>
                </div>
                
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Usage Over Time</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedUsageChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Conversion Ratio (FCR)</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="fcrChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Type Distribution</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedTypeChart" height="250"></canvas>
                        </div>
                    </div>
                    
                    <div class="analytics-card">
                        <div class="analytics-card-header">
                            <h3 class="analytics-card-title">Feed Cost Analysis</h3>
                            <div class="analytics-card-actions">
                                <button class="card-action-btn" title="View Full Screen">
                                    <i class="fas fa-expand"></i>
                                </button>
                                <button class="card-action-btn" title="Download">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>
                        </div>
                        <div class="analytics-chart-container">
                            <canvas id="feedCostChart" height="250"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="analytics-insights">
                    <h3 class="insights-title">Key Insights</h3>
                    <div class="insights-grid">
                        <div class="insight-card">
                            <div class="insight-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Feed Usage Trend</div>
                                <div class="insight-value">+12% <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed usage has increased by 12% compared to the previous period, indicating growth in biomass.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card positive">
                            <div class="insight-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">FCR Improvement</div>
                                <div class="insight-value">-0.15 <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed conversion ratio has improved by 0.15 points, indicating better feed efficiency.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card negative">
                            <div class="insight-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Feed Cost per kg</div>
                                <div class="insight-value">+$0.25 <span class="trend-period">vs. previous period</span></div>
                                <div class="insight-description">Feed cost per kg has increased due to higher prices of raw materials.</div>
                            </div>
                        </div>
                        
                        <div class="insight-card">
                            <div class="insight-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="insight-content">
                                <div class="insight-title">Optimal Feed Type</div>
                                <div class="insight-value">Growth Feed <span class="trend-period">best performance</span></div>
                                <div class="insight-description">Growth feed shows the best FCR performance across all ponds in the current cycle.</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Feed Calculator Tab -->
            <div class="feed-tab-content" data-tab="calculator">
                <div class="calculator-container">
                    <div class="calculator-sidebar">
                        <div class="calculator-form">
                            <h3 class="calculator-form-title">Feed Rate Calculator</h3>
                            
                            <div class="form-group">
                                <label class="form-label">Pond</label>
                                <select class="form-select" id="calcPond">
                                    <option value="1">Pond 1</option>
                                    <option value="2">Pond 2</option>
                                    <option value="3">Pond 3</option>
                                    <option value="4">Pond 4</option>
                                    <option value="5">Pond 5</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Shrimp Count (estimated)</label>
                                <input type="number" class="form-input" id="shrimpCount" value="100000">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Average Weight (g)</label>
                                <input type="number" class="form-input" id="avgWeight" value="12.5" step="0.1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Survival Rate (%)</label>
                                <input type="number" class="form-input" id="survivalRate" value="85" min="0" max="100">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Feeding Rate (% of biomass)</label>
                                <input type="number" class="form-input" id="feedingRate" value="3.5" step="0.1" min="0" max="10">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Number of Feedings per Day</label>
                                <select class="form-select" id="feedingsPerDay">
                                    <option value="2">2 times</option>
                                    <option value="3" selected>3 times</option>
                                    <option value="4">4 times</option>
                                    <option value="5">5 times</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Feed Type</label>
                                <select class="form-select" id="calcFeedType">
                                    <option value="starter">Starter Feed</option>
                                    <option value="growth" selected>Growth Feed</option>
                                    <option value="finisher">Finisher Feed</option>
                                </select>
                            </div>
                            
                            <div class="calculator-actions">
                                <button class="calculator-action-btn" id="calculateFeedBtn">
                                    <i class="fas fa-calculator"></i>
                                    Calculate
                                </button>
                                <button class="calculator-action-btn secondary" id="resetCalculatorBtn">
                                    <i class="fas fa-undo"></i>
                                    Reset
                                </button>
                            </div>
                        </div>
                        
                        <div class="calculator-presets">
                            <h3 class="presets-title">Quick Presets</h3>
                            <div class="preset-buttons">
                                <button class="preset-btn" data-weight="5" data-rate="4.5">
                                    Early Stage
                                </button>
                                <button class="preset-btn" data-weight="12" data-rate="3.5">
                                    Mid Stage
                                </button>
                                <button class="preset-btn" data-weight="20" data-rate="2.8">
                                    Late Stage
                                </button>
                                <button class="preset-btn" data-weight="25" data-rate="2.2">
                                    Pre-Harvest
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="calculator-results">
                        <div class="results-header">
                            <h3 class="results-title">Calculation Results</h3>
                            <div class="results-actions">
                                <button class="results-action-btn" id="saveCalculationBtn">
                                    <i class="fas fa-save"></i>
                                    Save
                                </button>
                                <button class="results-action-btn" id="printCalculationBtn">
                                    <i class="fas fa-print"></i>
                                    Print
                                </button>
                            </div>
                        </div>
                        
                        <div class="results-cards">
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-weight"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Estimated Biomass</div>
                                    <div class="result-value" id="biomassResult">1,062.5 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-utensils"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Daily Feed Amount</div>
                                    <div class="result-value" id="dailyFeedResult">37.2 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Per Feeding</div>
                                    <div class="result-value" id="perFeedingResult">12.4 kg</div>
                                </div>
                            </div>
                            
                            <div class="result-card">
                                <div class="result-icon">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                                <div class="result-content">
                                    <div class="result-label">Weekly Feed Amount</div>
                                    <div class="result-value" id="weeklyFeedResult">260.4 kg</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="feeding-schedule-result">
                            <h3 class="schedule-title">Recommended Feeding Schedule</h3>
                            <div class="schedule-table-container">
                                <table class="schedule-table">
                                    <thead>
                                        <tr>
                                            <th>Feeding #</th>
                                            <th>Time</th>
                                            <th>Amount</th>
                                            <th>Feed Type</th>
                                            <th>Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>7:00 AM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Morning feeding when oxygen levels are rising</td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>1:00 PM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Midday feeding during active period</td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>7:00 PM</td>
                                            <td>12.4 kg</td>
                                            <td>Growth Feed</td>
                                            <td>Evening feeding before oxygen levels decrease</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <div class="calculation-notes">
                            <h3 class="notes-title">Notes & Recommendations</h3>
                            <div class="notes-content">
                                <p><strong>Feeding Adjustments:</strong> Monitor feed consumption after each feeding. If feed is not consumed within 2 hours, reduce the amount by 10% for the next feeding.</p>
                                <p><strong>Water Quality:</strong> Check oxygen levels before each feeding. If below 4 mg/L, delay feeding until levels improve.</p>
                                <p><strong>Weather Considerations:</strong> During cloudy days, reduce feeding amount by 20% as metabolism slows down.</p>
                                <p><strong>Feed Conversion:</strong> With current settings, expected FCR is approximately 1.35-1.45.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>