{% extends "base.html" %}
{% load static %}

{% block title %}Enhanced Map Debug{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: 'Segoe UI', sans-serif;
        background: #f5f5f5;
        margin: 0;
        padding: 20px;
    }
    .debug-container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    #map {
        height: 500px;
        width: 100%;
        border: 2px solid #ccc;
        border-radius: 8px;
        background: #f0f0f0;
    }
    .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
    }
    .loading { background: #fff3cd; color: #856404; }
    .success { background: #d1edcc; color: #155724; }
    .error { background: #f8d7da; color: #721c24; }
    .debug-log {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin: 15px 0;
        max-height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
    }
    .debug-log div {
        margin: 2px 0;
    }
    .log-error { color: #dc3545; }
    .log-success { color: #28a745; }
    .log-info { color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="debug-container">
    <h1>🗺️ Enhanced Map Debug</h1>
    
    <div id="status" class="status loading">
        ⏳ Initializing enhanced map debugging...
    </div>
    
    <div class="debug-log" id="debugLog">
        <div class="log-info">Debug log initialized...</div>
    </div>
    
    <div id="map">
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
            <div style="text-align: center;">
                <div style="font-size: 24px; margin-bottom: 10px;">🗺️</div>
                <div>Loading Enhanced Map...</div>
                <div style="font-size: 0.9rem; margin-top: 5px;">Debugging Google Maps integration...</div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>Debug Information</h3>
        <p><strong>API Key:</strong> {{ google_maps_api_key|slice:":15" }}...</p>
        <p><strong>Center:</strong> {{ center_lat|floatformat:4 }}, {{ center_lng|floatformat:4 }}</p>
        <p><strong>Farms:</strong> {{ farms_data|length }}</p>
        <p><strong>Ponds:</strong> {{ ponds_data|length }}</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log('🔧 Enhanced Map Debug Starting...');
    
    // Debug logging function
    function debugLog(message, type = 'info') {
        const logDiv = document.getElementById('debugLog');
        const now = new Date().toLocaleTimeString();
        const logClass = type === 'error' ? 'log-error' : type === 'success' ? 'log-success' : 'log-info';
        logDiv.innerHTML += `<div class="${logClass}">[${now}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    function updateStatus(message, type = 'loading') {
        const statusEl = document.getElementById('status');
        statusEl.textContent = message;
        statusEl.className = `status ${type}`;
        debugLog(message, type);
    }
    
    // Global variables
    let map;
    let markers = [];
    
    // Map data from Django
    const apiKey = '{{ google_maps_api_key }}';
    const centerLat = {{ center_lat|default:13.0827 }};
    const centerLng = {{ center_lng|default:80.2707 }};
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    
    debugLog(`API Key loaded: ${apiKey.substring(0, 15)}...`);
    debugLog(`Center coordinates: ${centerLat}, ${centerLng}`);
    debugLog(`Farms data: ${farmsData.length} items`);
    debugLog(`Ponds data: ${pondsData.length} items`);
    
    // Google Maps initialization
    function initMap() {
        debugLog('🎯 initMap() called by Google Maps API', 'success');
        updateStatus('🗺️ Google Maps API loaded successfully', 'success');
        
        try {
            // Verify Google Maps is available
            if (typeof google === 'undefined') {
                throw new Error('Google object not found');
            }
            
            if (!google.maps) {
                throw new Error('google.maps not available');
            }
            
            debugLog('✅ Google Maps objects verified', 'success');
            
            // Get map container
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                throw new Error('Map container not found');
            }
            
            debugLog('✅ Map container found', 'success');
            
            // Set container size
            mapElement.style.height = '500px';
            mapElement.style.width = '100%';
            debugLog('📏 Map container size set to 500px', 'info');
            
            // Validate coordinates
            if (isNaN(centerLat) || isNaN(centerLng)) {
                throw new Error(`Invalid coordinates: ${centerLat}, ${centerLng}`);
            }
            
            debugLog(`📍 Using center coordinates: ${centerLat}, ${centerLng}`, 'info');
            
            // Create the map
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: 'roadmap'
            });
            
            debugLog('✅ Map object created successfully', 'success');
            updateStatus('🎉 Enhanced map loaded successfully!', 'success');
            
            // Add markers
            addMarkers();
            
            // Trigger resize to ensure proper display
            setTimeout(() => {
                google.maps.event.trigger(map, 'resize');
                debugLog('🔄 Map resize triggered', 'info');
            }, 500);
            
        } catch (error) {
            debugLog(`❌ Error in initMap: ${error.message}`, 'error');
            updateStatus(`❌ Map initialization failed: ${error.message}`, 'error');
            
            const mapElement = document.getElementById('map');
            mapElement.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #dc3545;">
                    <div style="text-align: center;">
                        <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                        <div><strong>Map Error</strong></div>
                        <div style="font-size: 0.9rem; margin-top: 5px;">${error.message}</div>
                    </div>
                </div>
            `;
        }
    }
    
    function addMarkers() {
        let markerCount = 0;
        
        // Add farm markers
        if (farmsData && farmsData.length > 0) {
            farmsData.forEach(farm => {
                if (farm.latitude && farm.longitude) {
                    const marker = new google.maps.Marker({
                        position: { lat: farm.latitude, lng: farm.longitude },
                        map: map,
                        title: farm.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="12.5" cy="12.5" r="10" fill="#3b82f6" stroke="white" stroke-width="2"/>
                                    <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">F</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });
                    
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4 style="margin: 0 0 10px 0; color: #1f2937;">🏢 ${farm.name}</h4>
                                <p style="margin: 0;"><strong>Total Ponds:</strong> ${farm.total_ponds || 0}</p>
                                <p style="margin: 0;"><strong>Active Ponds:</strong> ${farm.active_ponds || 0}</p>
                                <p style="margin: 0;"><strong>Location:</strong> ${farm.location || 'N/A'}</p>
                            </div>
                        `
                    });
                    
                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                    
                    markers.push(marker);
                    markerCount++;
                }
            });
        }
        
        // Add pond markers
        if (pondsData && pondsData.length > 0) {
            pondsData.forEach(pond => {
                if (pond.latitude && pond.longitude) {
                    let color = '#10b981'; // Default green
                    if (pond.status === 'maintenance') color = '#f59e0b';
                    else if (pond.status === 'inactive') color = '#ef4444';
                    
                    const marker = new google.maps.Marker({
                        position: { lat: pond.latitude, lng: pond.longitude },
                        map: map,
                        title: pond.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="10" cy="10" r="8" fill="${color}" stroke="white" stroke-width="2"/>
                                    <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">P</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(20, 20)
                        }
                    });
                    
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px;">
                                <h4 style="margin: 0 0 10px 0; color: #1f2937;">🏊 ${pond.name}</h4>
                                <p style="margin: 0;"><strong>Status:</strong> <span style="color: ${color};">${pond.status || 'Unknown'}</span></p>
                                <p style="margin: 0;"><strong>Size:</strong> ${pond.size || 'N/A'} acres</p>
                                <p style="margin: 0;"><strong>Farm:</strong> ${pond.farm_name || 'N/A'}</p>
                            </div>
                        `
                    });
                    
                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                    
                    markers.push(marker);
                    markerCount++;
                }
            });
        }
        
        debugLog(`✅ Added ${markerCount} markers to map`, 'success');
    }
    
    // Error handling
    function handleMapError() {
        debugLog('❌ Google Maps script failed to load', 'error');
        updateStatus('❌ Google Maps API failed to load', 'error');
    }
    
    // Global error handler for authentication issues
    window.gm_authFailure = function() {
        debugLog('❌ Google Maps authentication failed', 'error');
        updateStatus('❌ Google Maps authentication failed', 'error');
    };
    
    // Initial setup
    debugLog('🚀 Script loaded, waiting for Google Maps API...', 'info');
    updateStatus('⏳ Loading Google Maps API...', 'loading');
    
    // Timeout check
    setTimeout(() => {
        if (typeof google === 'undefined') {
            debugLog('⏰ Timeout: Google Maps API not loaded after 10 seconds', 'error');
            updateStatus('⏰ Timeout: Google Maps API failed to load', 'error');
        }
    }, 10000);
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
