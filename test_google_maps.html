<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
        .debug-panel {
            background: white;
            padding: 15px;
            margin: 10px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    
    <div class="debug-panel">
        <h3>Debug Information</h3>
        <div id="debug-content">
            <div>⏳ Initializing test...</div>
        </div>
    </div>
    
    <div id="map"></div>

    <script>
        function updateDebug(message, type = 'info') {
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
            debugContent.innerHTML += `<div style="color: ${color}; margin: 2px 0;">${timestamp}: ${message}</div>`;
            console.log(message);
        }

        // Test API key
        const API_KEY = 'AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw';
        updateDebug(`🔑 Using API Key: ${API_KEY.substring(0, 10)}...`);

        // Global error handler
        window.gm_authFailure = function() {
            updateDebug('❌ Google Maps authentication failed!', 'error');
        };

        function initMap() {
            updateDebug('🗺️ initMap() callback triggered', 'success');
            
            if (typeof google === 'undefined' || !google.maps) {
                updateDebug('❌ Google Maps API not loaded', 'error');
                return;
            }
            
            updateDebug('✅ Google Maps API loaded successfully', 'success');
            
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 }
            });
            
            updateDebug('✅ Map initialized successfully', 'success');
            
            // Add a test marker
            const marker = new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Location'
            });
            
            updateDebug('✅ Test marker added', 'success');
        }

        updateDebug('📍 Starting Google Maps test...');
    </script>

    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="updateDebug('❌ Failed to load Google Maps script', 'error'); document.getElementById('map').innerHTML='<div style=&quot;padding: 20px; text-align: center; color: red;&quot;>Failed to load Google Maps script</div>';">
    </script>
</body>
</html>
