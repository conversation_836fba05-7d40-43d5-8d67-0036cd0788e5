{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Shrimp Farm GPS - Final Test{% endblock %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    #map {
        height: 70vh;
        width: 100%;
        border-radius: 16px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        border: 2px solid rgba(74, 85, 104, 0.3);
    }
    
    .debug-panel {
        background: rgba(30, 41, 59, 0.9);
        border-radius: 16px;
        padding: 20px;
        margin: 20px 0;
        color: #e2e8f0;
        border: 1px solid rgba(74, 85, 104, 0.3);
    }
    
    .status-demo {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin: 15px 0;
    }
    
    .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .status-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid white;
    }
    
    h1 {
        color: #e2e8f0;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: 700;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
    
    .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 5px;
    }
    
    .btn-primary { background: linear-gradient(135deg, #3b82f6, #1e40af); color: white; }
    .btn-success { background: linear-gradient(135deg, #10b981, #059669); color: white; }
    .btn-info { background: linear-gradient(135deg, #06b6d4, #0891b2); color: white; }
    .btn-warning { background: linear-gradient(135deg, #f59e0b, #d97706); color: white; }
    .btn-danger { background: linear-gradient(135deg, #ef4444, #dc2626); color: white; }
    
    .btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>
        <i class="fas fa-map-marked-alt"></i>
        Enhanced Shrimp Farm GPS - Final Test
    </h1>
    
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h3><i class="fas fa-bug"></i> System Status</h3>
        <p><strong>Farms:</strong> {{ total_farms }} | <strong>Ponds:</strong> {{ total_ponds }} | <strong>Workers:</strong> {{ total_workers }} | <strong>Geofences:</strong> {{ total_geofences }}</p>
        
        <h4><i class="fas fa-palette"></i> Worker Status Color Coding</h4>
        <div class="status-demo">
            <div class="status-item">
                <div class="status-color" style="background: #ef4444;"></div>
                <span>WORKING</span>
            </div>
            <div class="status-item">
                <div class="status-color" style="background: #10b981;"></div>
                <span>AVAILABLE</span>
            </div>
            <div class="status-item">
                <div class="status-color" style="background: #f59e0b;"></div>
                <span>ON BREAK</span>
            </div>
            <div class="status-item">
                <div class="status-color" style="background: #8b5cf6;"></div>
                <span>TRAVELING</span>
            </div>
            <div class="status-item">
                <div class="status-color" style="background: #6b7280;"></div>
                <span>OFFLINE</span>
            </div>
        </div>
        
        <h4><i class="fas fa-chart-bar"></i> Current Status Distribution</h4>
        <div style="font-family: monospace;">
            {% for status, count in debug_info.status_distribution.items %}
                <div>{{ status|upper }}: {{ count }} workers</div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Map Controls -->
    <div class="debug-panel">
        <h4><i class="fas fa-layer-group"></i> Map Controls</h4>
        <button class="btn btn-primary" onclick="toggleFarms()">🏭 Toggle Farms</button>
        <button class="btn btn-success" onclick="togglePonds()">🌊 Toggle Ponds</button>
        <button class="btn btn-info" onclick="toggleWorkers()">👥 Toggle Workers</button>
        <button class="btn btn-warning" onclick="toggleGeofences()">🛡️ Toggle Geofences</button>
        <button class="btn btn-danger" onclick="centerMap()">🎯 Center Map</button>
    </div>
    
    <!-- Map Container -->
    <div id="map"></div>
    
    <!-- Debug Info -->
    <div class="debug-panel">
        <h4><i class="fas fa-code"></i> Debug Console</h4>
        <div id="jsDebugInfo" style="font-family: monospace; color: #94a3b8;">
            🔄 Initializing system...
        </div>
    </div>
</div>

<script>
// Global variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let workerMarkers = [];
let geofenceOverlays = [];
let layerVisibility = { farms: true, ponds: true, workers: true, geofences: true };

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const workersData = {{ workers_data|safe }};
const geofencesData = {{ geofences_data|safe }};

// Center coordinates
const centerLat = 13.0827;
const centerLng = 80.2707;

console.log('🎯 FINAL TEST - Data loaded:');
console.log('🏭 Farms:', farmsData.length);
console.log('🌊 Ponds:', pondsData.length);
console.log('👥 Workers:', workersData.length);
console.log('🛡️ Geofences:', geofencesData.length);
console.log('📊 Worker Status Distribution:', {{ debug_info.status_distribution|safe }});

function initMap() {
    try {
        console.log('🚀 Initializing Enhanced Map - Final Test...');
        
        // Initialize Map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 12,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: google.maps.MapTypeId.ROADMAP,
            styles: [
                {
                    "featureType": "all",
                    "elementType": "geometry.fill",
                    "stylers": [{"saturation": -40}, {"lightness": -10}]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [{"color": "#2563eb"}, {"lightness": -10}]
                }
            ]
        });
        
        console.log('✅ Map initialized successfully');
        
        // Add all markers
        addFarmMarkers();
        addPondMarkers();
        addWorkerMarkers();
        addGeofenceOverlays();
        
        // Update debug info
        document.getElementById('jsDebugInfo').innerHTML = 
            `✅ System Online - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Geofences: ${geofencesData.length}`;
        
        console.log('🎉 Enhanced Map System Ready - Final Test!');
        
    } catch (error) {
        console.error('❌ Map initialization failed:', error);
        handleMapError(error);
    }
}

function addWorkerMarkers() {
    console.log('👥 Adding worker markers with status colors...');
    console.log('📊 Worker data received:', workersData);
    
    const statusColors = {
        'working': '#ef4444',      // Red
        'available': '#10b981',    // Green
        'on_break': '#f59e0b',     // Yellow/Orange
        'traveling': '#8b5cf6',    // Purple
        'offline': '#6b7280'       // Gray
    };
    
    workersData.forEach((worker, index) => {
        const color = statusColors[worker.status] || '#6b7280';
        console.log(`👤 Worker ${index + 1}: ${worker.name} - Status: ${worker.status} - Color: ${color}`);
        
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(worker.latitude), lng: parseFloat(worker.longitude) },
            map: map,
            title: worker.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
                        <circle cx="20" cy="20" r="18" fill="${color}" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="20" cy="20" r="14" fill="#f8f9fa"/>
                        <circle cx="20" cy="16" r="5" fill="#fdbcb4"/>
                        <circle cx="18" cy="15" r="0.8" fill="#333"/>
                        <circle cx="22" cy="15" r="0.8" fill="#333"/>
                        <ellipse cx="20" cy="17" rx="0.5" ry="0.3" fill="#f4a261"/>
                        <path d="M 17.5 18.5 Q 20 20 22.5 18.5" stroke="#333" stroke-width="0.8" fill="none"/>
                        <rect x="16" y="22" width="8" height="7" fill="#4a90e2" rx="2"/>
                        <text x="20" y="37" text-anchor="middle" fill="white" font-size="6" font-family="Arial" font-weight="bold">${worker.status.toUpperCase()}</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(40, 40)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5 style="margin: 0 0 10px 0; color: #1f2937;">
                        <i class="fas fa-user" style="color: ${color}; margin-right: 8px;"></i>
                        ${worker.name}
                    </h5>
                    <p><strong>Employee ID:</strong> ${worker.employee_id}</p>
                    <p><strong>Team:</strong> ${worker.team}</p>
                    <p><strong>Status:</strong> <span style="color: ${color}; font-weight: bold; text-transform: uppercase;">${worker.status}</span></p>
                    <p><strong>Location:</strong> ${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</p>
                    <div style="margin-top: 10px; padding: 8px; background: ${color}; color: white; border-radius: 4px; text-align: center; font-weight: bold;">
                        ${worker.status.replace('_', ' ').toUpperCase()}
                    </div>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        workerMarkers.push(marker);
    });
    
    console.log(`✅ Added ${workerMarkers.length} worker markers with status colors`);
}

function addFarmMarkers() {
    console.log('🏭 Adding farm markers...');
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 40 40" width="40" height="40">
                        <circle cx="20" cy="20" r="18" fill="#3b82f6" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="20" cy="20" r="12" fill="#ffffff" opacity="0.2"/>
                        <text x="20" y="25" text-anchor="middle" fill="white" font-size="8" font-family="Arial" font-weight="bold">FARM</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(40, 40)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5>${farm.name}</h5>
                    <p><strong>Description:</strong> ${farm.description}</p>
                    <p><strong>Location:</strong> ${farm.latitude.toFixed(6)}, ${farm.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        farmMarkers.push(marker);
    });
    console.log(`✅ Added ${farmMarkers.length} farm markers`);
}

function addPondMarkers() {
    console.log('🌊 Adding pond markers...');
    pondsData.forEach(pond => {
        const marker = new google.maps.Marker({
            position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;base64,' + btoa(`
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="36" height="36">
                        <circle cx="18" cy="18" r="16" fill="#10b981" stroke="#ffffff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="10" fill="#ffffff" opacity="0.3"/>
                        <text x="18" y="22" text-anchor="middle" fill="white" font-size="7" font-family="Arial" font-weight="bold">POND</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36)
            }
        });
        
        const infoWindow = new google.maps.InfoWindow({
            content: `
                <div style="max-width: 300px;">
                    <h5>${pond.name}</h5>
                    <p><strong>Farm:</strong> ${pond.farm_name}</p>
                    <p><strong>Location:</strong> ${pond.latitude.toFixed(6)}, ${pond.longitude.toFixed(6)}</p>
                </div>
            `
        });
        
        marker.addListener('click', () => {
            infoWindow.open(map, marker);
        });
        
        pondMarkers.push(marker);
    });
    console.log(`✅ Added ${pondMarkers.length} pond markers`);
}

function addGeofenceOverlays() {
    console.log('🛡️ Adding geofence overlays...');
    geofencesData.forEach(geofence => {
        if (geofence.center_latitude && geofence.center_longitude && geofence.radius) {
            const circle = new google.maps.Circle({
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.1,
                map: map,
                center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                radius: geofence.radius
            });
            
            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h5>${geofence.name}</h5>
                        <p><strong>Type:</strong> ${geofence.geofence_type}</p>
                        <p><strong>Shape:</strong> ${geofence.shape_type}</p>
                        <p><strong>Radius:</strong> ${geofence.radius}m</p>
                    </div>
                `
            });
            
            circle.addListener('click', (event) => {
                infoWindow.setPosition(event.latLng);
                infoWindow.open(map);
            });
            
            geofenceOverlays.push(circle);
        }
    });
    console.log(`✅ Added ${geofenceOverlays.length} geofence overlays`);
}

// Toggle functions
function toggleFarms() {
    layerVisibility.farms = !layerVisibility.farms;
    farmMarkers.forEach(marker => marker.setVisible(layerVisibility.farms));
    console.log(`🏭 Farms ${layerVisibility.farms ? 'shown' : 'hidden'}`);
}

function togglePonds() {
    layerVisibility.ponds = !layerVisibility.ponds;
    pondMarkers.forEach(marker => marker.setVisible(layerVisibility.ponds));
    console.log(`🌊 Ponds ${layerVisibility.ponds ? 'shown' : 'hidden'}`);
}

function toggleWorkers() {
    layerVisibility.workers = !layerVisibility.workers;
    workerMarkers.forEach(marker => marker.setVisible(layerVisibility.workers));
    console.log(`👥 Workers ${layerVisibility.workers ? 'shown' : 'hidden'}`);
}

function toggleGeofences() {
    layerVisibility.geofences = !layerVisibility.geofences;
    geofenceOverlays.forEach(overlay => overlay.setVisible(layerVisibility.geofences));
    console.log(`🛡️ Geofences ${layerVisibility.geofences ? 'shown' : 'hidden'}`);
}

function centerMap() {
    map.setCenter({ lat: centerLat, lng: centerLng });
    map.setZoom(12);
    console.log('🎯 Map centered');
}

function handleMapError(error) {
    console.error('❌ Map error:', error);
    document.getElementById('map').innerHTML = `
        <div style="padding: 50px; text-align: center; color: #ef4444; background: rgba(30, 41, 59, 0.9); border-radius: 12px;">
            <h3><i class="fas fa-exclamation-triangle"></i> Map Loading Error</h3>
            <p>Failed to load Google Maps. Please check your internet connection and API key.</p>
            <button onclick="location.reload()" style="padding: 10px 20px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer; margin-top: 15px;">
                <i class="fas fa-redo"></i> Retry
            </button>
        </div>
    `;
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 DOM Ready - Waiting for Google Maps...');
});
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
    onerror="handleMapError('Failed to load Google Maps API')">
</script>
{% endblock %}
