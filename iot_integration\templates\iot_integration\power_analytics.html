<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#1a202c">
    <meta name="description" content="Real-time power analytics and monitoring for Shrimp Farm Guardian IoT system">
    <title>Power Analytics - Shrimp Farm Guardian</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
            color: #e2e8f0;
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(74, 85, 104, 0.3);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header-info h1 {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .header-info p {
            color: #94a3b8;
            font-size: 1rem;
        }

        .time-range-selector {
            display: flex;
            gap: 0.5rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 0.5rem;
        }

        .time-btn {
            background: transparent;
            border: none;
            color: #94a3b8;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }

        .time-btn.active, .time-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .analytics-widget {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(74, 85, 104, 0.3);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .analytics-widget:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .widget-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(74, 85, 104, 0.3);
        }

        .widget-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #e2e8f0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .chart-placeholder {
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #64748b;
            font-size: 0.875rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #94a3b8;
        }

        .stat-change {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .stat-increase { color: #10b981; }
        .stat-decrease { color: #ef4444; }

        .efficiency-meter {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .meter-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(
                #10b981 0deg,
                #10b981 var(--efficiency-angle, 270deg),
                rgba(107, 114, 128, 0.3) var(--efficiency-angle, 270deg),
                rgba(107, 114, 128, 0.3) 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .meter-inner {
            width: 80%;
            height: 80%;
            background: rgba(30, 41, 59, 0.9);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .meter-value {
            font-size: 2rem;
            font-weight: 700;
            color: #10b981;
        }

        .meter-label {
            font-size: 0.875rem;
            color: #94a3b8;
        }

        .cost-breakdown {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .cost-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .cost-category {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .cost-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
        }

        .cost-aerator { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
        .cost-lighting { background: rgba(245, 158, 11, 0.2); color: #f59e0b; }
        .cost-pumps { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .cost-other { background: rgba(139, 92, 246, 0.2); color: #8b5cf6; }

        .cost-details {
            text-align: right;
        }

        .cost-amount {
            font-size: 1.125rem;
            font-weight: 600;
            color: #e2e8f0;
        }

        .cost-percentage {
            font-size: 0.75rem;
            color: #94a3b8;
        }

        .recommendations {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .recommendations h4 {
            color: #10b981;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: #94a3b8;
        }

        .recommendation-item:last-child {
            margin-bottom: 0;
        }

        @media (max-width: 1200px) {
            .container {
                max-width: 100%;
                padding: 1.5rem;
            }
            
            .header h1 {
                font-size: 1.8rem;
            }
            
            .analytics-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 1.5rem;
            }
        }

        @media (max-width: 992px) {
            body {
                padding: 1.5rem;
            }
            
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 1.25rem;
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
            
            .header h1 {
                font-size: 1.6rem;
                margin-bottom: 0.25rem;
            }
            
            .analytics-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.25rem;
            }
            
            .efficiency-meter {
                width: 180px;
                height: 180px;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .container {
                padding: 0.75rem;
            }
            
            .header {
                padding: 1rem;
                margin-bottom: 1.5rem;
            }
            
            .header h1 {
                font-size: 1.4rem;
            }
            
            .header p {
                font-size: 0.9rem;
            }
            
            /* Mobile Analytics Grid */
            .analytics-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .analytics-widget {
                padding: 1rem;
            }
            
            .widget-title {
                font-size: 1.1rem;
                margin-bottom: 1rem;
            }
            
            /* Mobile Stats Grid */
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 0.75rem;
            }
            
            .stat-item {
                padding: 0.75rem;
                text-align: center;
            }
            
            .stat-value {
                font-size: 1.3rem;
            }
            
            .stat-label {
                font-size: 0.8rem;
            }
            
            .stat-change {
                font-size: 0.7rem;
            }
            
            /* Mobile Efficiency Meter */
            .efficiency-meter {
                width: 160px;
                height: 160px;
            }
            
            .meter-value {
                font-size: 1.8rem;
            }
            
            .meter-label {
                font-size: 0.8rem;
            }
        }

        @media (max-width: 576px) {
            body {
                padding: 0.75rem;
            }
            
            .container {
                padding: 0.5rem;
            }
            
            .header {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }
            
            .header h1 {
                font-size: 1.25rem;
            }
            
            .header p {
                font-size: 0.85rem;
            }
            
            /* Extra Small Mobile */
            .analytics-widget {
                padding: 0.75rem;
                border-radius: 12px;
            }
            
            .widget-title {
                font-size: 1rem;
                margin-bottom: 0.75rem;
            }
            
            /* Mobile Stats - Single Column on Very Small Screens */
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .stat-item {
                padding: 0.6rem;
            }
            
            .stat-value {
                font-size: 1.2rem;
            }
            
            .stat-label {
                font-size: 0.75rem;
            }
            
            .stat-change {
                font-size: 0.65rem;
                margin-top: 0.2rem;
            }
            
            /* Smaller Efficiency Meter */
            .efficiency-meter {
                width: 140px;
                height: 140px;
            }
            
            .meter-value {
                font-size: 1.6rem;
            }
            
            .meter-label {
                font-size: 0.75rem;
            }
        }

        /* Touch-friendly interactions for mobile devices */
        @media (hover: none) and (pointer: coarse) {
            .analytics-widget:hover {
                transform: none;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            }
            
            /* Larger touch targets for buttons */
            button, .clickable {
                min-height: 44px;
                min-width: 44px;
            }
        }

        /* Landscape mobile orientation */
        @media (max-width: 768px) and (orientation: landscape) {
            .header {
                padding: 0.75rem;
                margin-bottom: 1rem;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .analytics-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }
            
            .efficiency-meter {
                width: 150px;
                height: 150px;
            }
        }

        /* High DPI / Retina displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .analytics-widget {
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            
            .meter-circle {
                -webkit-background-size: cover;
                background-size: cover;
            }
        }

        /* Print styles for web */
        @media print {
            body {
                background: white !important;
                color: black !important;
            }
            
            .analytics-widget {
                background: white !important;
                color: black !important;
                border: 1px solid #ddd !important;
                box-shadow: none !important;
                break-inside: avoid;
            }
            
            .header {
                background: white !important;
                color: black !important;
                border-bottom: 2px solid #333 !important;
            }
        }

        /* Existing meter styles */
        .efficiency-meter {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .meter-circle {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: conic-gradient(
                #10b981 0deg,
                #10b981 var(--efficiency-angle, 270deg),
                rgba(107, 114, 128, 0.3) var(--efficiency-angle, 270deg),
                rgba(107, 114, 128, 0.3) 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .meter-inner {
            width: 80%;
            height: 80%;
            background: rgba(30, 41, 59, 0.9);
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .meter-value {
            font-size: 2rem;
            font-weight: 700;
            color: #10b981;
        }

        .meter-label {
            font-size: 0.875rem;
            color: #94a3b8;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-info">
                <h1><i class="fas fa-chart-line"></i> Power Analytics</h1>
                <p>Comprehensive power consumption analysis and optimization insights</p>
            </div>
            <div class="time-range-selector">
                <button class="time-btn active" onclick="changeTimeRange('24h')">24H</button>
                <button class="time-btn" onclick="changeTimeRange('7d')">7D</button>
                <button class="time-btn" onclick="changeTimeRange('30d')">30D</button>
                <button class="time-btn" onclick="changeTimeRange('90d')">90D</button>
            </div>
        </div>

        <!-- Analytics Grid -->
        <div class="analytics-grid">
            <!-- Power Consumption Chart -->
            <div class="analytics-widget" style="grid-column: span 2;">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-bolt"></i> Power Consumption Trends
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="powerConsumptionChart"></canvas>
                </div>
            </div>

            <!-- Energy Statistics -->
            <div class="analytics-widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-tachometer-alt"></i> Energy Statistics
                    </h3>
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" style="color: #3b82f6;">1,245</div>
                        <div class="stat-label">kWh Today</div>
                        <div class="stat-change stat-increase">+12% vs yesterday</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" style="color: #10b981;">8,567</div>
                        <div class="stat-label">kWh This Week</div>
                        <div class="stat-change stat-decrease">-5% vs last week</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" style="color: #f59e0b;">₹12,450</div>
                        <div class="stat-label">Cost Today</div>
                        <div class="stat-change stat-increase">+8% vs yesterday</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" style="color: #ef4444;">₹85,670</div>
                        <div class="stat-label">Cost This Week</div>
                        <div class="stat-change stat-decrease">-3% vs last week</div>
                    </div>
                </div>
            </div>

            <!-- System Efficiency -->
            <div class="analytics-widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-gauge-high"></i> System Efficiency
                    </h3>
                </div>
                <div class="efficiency-meter" style="--efficiency-angle: 270deg;">
                    <div class="meter-circle">
                        <div class="meter-inner">
                            <div class="meter-value">85%</div>
                            <div class="meter-label">Efficiency</div>
                        </div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 1rem; font-size: 0.875rem; color: #94a3b8;">
                    Power Factor: 0.92 | Load Balance: Good
                </div>
            </div>

            <!-- Cost Breakdown -->
            <div class="analytics-widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-pie-chart"></i> Cost Breakdown
                    </h3>
                </div>
                <div class="cost-breakdown">
                    <div class="cost-item">
                        <div class="cost-category">
                            <div class="cost-icon cost-aerator">
                                <i class="fas fa-fan"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">Aerators</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">4 devices</div>
                            </div>
                        </div>
                        <div class="cost-details">
                            <div class="cost-amount">₹8,450</div>
                            <div class="cost-percentage">68%</div>
                        </div>
                    </div>
                    <div class="cost-item">
                        <div class="cost-category">
                            <div class="cost-icon cost-lighting">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">Lighting</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">12 fixtures</div>
                            </div>
                        </div>
                        <div class="cost-details">
                            <div class="cost-amount">₹2,100</div>
                            <div class="cost-percentage">17%</div>
                        </div>
                    </div>
                    <div class="cost-item">
                        <div class="cost-category">
                            <div class="cost-icon cost-pumps">
                                <i class="fas fa-tint"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">Water Pumps</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">3 pumps</div>
                            </div>
                        </div>
                        <div class="cost-details">
                            <div class="cost-amount">₹1,200</div>
                            <div class="cost-percentage">10%</div>
                        </div>
                    </div>
                    <div class="cost-item">
                        <div class="cost-category">
                            <div class="cost-icon cost-other">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div>
                                <div style="font-weight: 600;">Other</div>
                                <div style="font-size: 0.75rem; color: #94a3b8;">Misc equipment</div>
                            </div>
                        </div>
                        <div class="cost-details">
                            <div class="cost-amount">₹700</div>
                            <div class="cost-percentage">5%</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Power Quality Chart -->
            <div class="analytics-widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-wave-square"></i> Power Quality
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="powerQualityChart"></canvas>
                </div>
            </div>

            <!-- Load Distribution -->
            <div class="analytics-widget">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-balance-scale"></i> Load Distribution
                    </h3>
                </div>
                <div class="chart-container">
                    <canvas id="loadDistributionChart"></canvas>
                </div>
            </div>

            <!-- Optimization Recommendations -->
            <div class="analytics-widget" style="grid-column: span 2;">
                <div class="widget-header">
                    <h3 class="widget-title">
                        <i class="fas fa-lightbulb"></i> Optimization Recommendations
                    </h3>
                </div>
                <div class="recommendations">
                    <h4><i class="fas fa-leaf"></i> Energy Saving Opportunities</h4>
                    <div class="recommendation-item">
                        <i class="fas fa-arrow-right" style="color: #10b981; margin-top: 0.125rem;"></i>
                        <span>Reduce aerator runtime during peak hours (2-6 PM) to save ₹1,200/month</span>
                    </div>
                    <div class="recommendation-item">
                        <i class="fas fa-arrow-right" style="color: #10b981; margin-top: 0.125rem;"></i>
                        <span>Install power factor correction to improve efficiency by 8%</span>
                    </div>
                    <div class="recommendation-item">
                        <i class="fas fa-arrow-right" style="color: #10b981; margin-top: 0.125rem;"></i>
                        <span>Schedule maintenance for Aerator 3 - showing 15% higher consumption</span>
                    </div>
                    <div class="recommendation-item">
                        <i class="fas fa-arrow-right" style="color: #10b981; margin-top: 0.125rem;"></i>
                        <span>Consider solar panels for daytime operations - potential 40% cost reduction</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let powerConsumptionChart;
        let powerQualityChart;
        let loadDistributionChart;
        let currentTimeRange = '24h';

        // Initialize analytics dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadAnalyticsData();
        });

        function initializeCharts() {
            // Power Consumption Chart
            const powerCtx = document.getElementById('powerConsumptionChart').getContext('2d');
            powerConsumptionChart = new Chart(powerCtx, {
                type: 'line',
                data: {
                    labels: generateTimeLabels(),
                    datasets: [{
                        label: 'Total Power (kW)',
                        data: generatePowerData(),
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        fill: true,
                        tension: 0.4
                    }, {
                        label: 'Aerators (kW)',
                        data: generateAeratorData(),
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: false,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#e2e8f0' }
                        }
                    },
                    scales: {
                        x: {
                            ticks: { color: '#94a3b8' },
                            grid: { color: 'rgba(148, 163, 184, 0.1)' }
                        },
                        y: {
                            ticks: { color: '#94a3b8' },
                            grid: { color: 'rgba(148, 163, 184, 0.1)' }
                        }
                    }
                }
            });

            // Power Quality Chart
            const qualityCtx = document.getElementById('powerQualityChart').getContext('2d');
            powerQualityChart = new Chart(qualityCtx, {
                type: 'radar',
                data: {
                    labels: ['Voltage Stability', 'Frequency', 'Power Factor', 'Harmonics', 'Load Balance'],
                    datasets: [{
                        label: 'Current',
                        data: [85, 92, 88, 78, 90],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        pointBackgroundColor: '#667eea'
                    }, {
                        label: 'Target',
                        data: [95, 95, 95, 90, 95],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        pointBackgroundColor: '#10b981'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            labels: { color: '#e2e8f0' }
                        }
                    },
                    scales: {
                        r: {
                            ticks: { color: '#94a3b8' },
                            grid: { color: 'rgba(148, 163, 184, 0.2)' },
                            pointLabels: { color: '#94a3b8' }
                        }
                    }
                }
            });

            // Load Distribution Chart
            const loadCtx = document.getElementById('loadDistributionChart').getContext('2d');
            loadDistributionChart = new Chart(loadCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Aerator 1', 'Aerator 2', 'Aerator 3', 'Aerator 4', 'Other'],
                    datasets: [{
                        data: [25, 23, 28, 20, 4],
                        backgroundColor: [
                            '#667eea',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444',
                            '#8b5cf6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                color: '#e2e8f0',
                                padding: 20
                            }
                        }
                    }
                }
            });
        }

        function generateTimeLabels() {
            const labels = [];
            const now = new Date();

            if (currentTimeRange === '24h') {
                for (let i = 23; i >= 0; i--) {
                    const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                    labels.push(time.getHours() + ':00');
                }
            } else if (currentTimeRange === '7d') {
                for (let i = 6; i >= 0; i--) {
                    const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
                    labels.push(date.toLocaleDateString('en-US', { weekday: 'short' }));
                }
            }

            return labels;
        }

        function generatePowerData() {
            const data = [];
            const baseLoad = 12;

            for (let i = 0; i < 24; i++) {
                // Simulate daily power pattern
                const hourlyVariation = Math.sin((i - 6) * Math.PI / 12) * 3;
                const randomVariation = (Math.random() - 0.5) * 2;
                data.push(Math.max(0, baseLoad + hourlyVariation + randomVariation));
            }

            return data;
        }

        function generateAeratorData() {
            const data = [];
            const baseAeratorLoad = 8;

            for (let i = 0; i < 24; i++) {
                // Aerators typically run more during day
                const dayFactor = i >= 6 && i <= 18 ? 1.2 : 0.8;
                const randomVariation = (Math.random() - 0.5) * 1;
                data.push(Math.max(0, baseAeratorLoad * dayFactor + randomVariation));
            }

            return data;
        }

        function loadAnalyticsData() {
            // Simulate loading analytics data
            console.log('Loading analytics data for:', currentTimeRange);

            // Update efficiency meter
            updateEfficiencyMeter(85);
        }

        function updateEfficiencyMeter(efficiency) {
            const angle = (efficiency / 100) * 270; // 270 degrees for 100%
            document.querySelector('.efficiency-meter').style.setProperty('--efficiency-angle', angle + 'deg');
            document.querySelector('.meter-value').textContent = efficiency + '%';
        }

        function changeTimeRange(range) {
            currentTimeRange = range;

            // Update active button
            document.querySelectorAll('.time-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Update charts
            powerConsumptionChart.data.labels = generateTimeLabels();
            powerConsumptionChart.data.datasets[0].data = generatePowerData();
            powerConsumptionChart.data.datasets[1].data = generateAeratorData();
            powerConsumptionChart.update();

            loadAnalyticsData();
        }

        console.log('📊 Power Analytics Dashboard Ready!');
    </script>
</body>
</html>
