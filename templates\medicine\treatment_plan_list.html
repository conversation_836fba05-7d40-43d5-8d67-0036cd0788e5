<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Treatment Plans - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    
    .plan-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .plan-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-clipboard-check medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Treatment Plans</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">Treatment Plans</h2>
                    <p class="mb-0 opacity-75">Manage treatment schedules and protocols</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Total Plans</small>
                                <strong>{{ treatment_plans|length|default:"0" }}</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/medicine/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to Medicines
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/treatment-plans/create/" class="quick-action">
                <i class="fas fa-plus"></i>
                Create Plan
            </a>
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-pills"></i>
                Medicines
            </a>
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-syringe"></i>
                Applications
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
    
    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <label for="pond" class="form-label">Pond</label>
                    <select name="pond" id="pond" class="form-select">
                        <option value="">All Ponds</option>
                        {% for pond in ponds %}
                        <option value="{{ pond.id }}" {% if selected_pond == pond.id|stringformat:"i" %}selected{% endif %}>{{ pond.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="status" class="form-label">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="planned" {% if selected_status == 'planned' %}selected{% endif %}>Planned</option>
                        <option value="in_progress" {% if selected_status == 'in_progress' %}selected{% endif %}>In Progress</option>
                        <option value="completed" {% if selected_status == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>
                <div class="col-12 d-flex justify-content-end">
                    <button type="submit" class="btn btn-primary me-2">Filter</button>
                    <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-outline-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Treatment Plans List -->
    <div class="row">
        {% if treatment_plans %}
        {% for plan in treatment_plans %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ plan.name }}</h5>
                    <span class="badge {% if plan.status == 'planned' %}bg-info{% elif plan.status == 'in_progress' %}bg-primary{% elif plan.status == 'completed' %}bg-success{% else %}bg-secondary{% endif %}">
                        {{ plan.get_status_display }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Pond</p>
                        <p class="mb-0">{{ plan.pond.name }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Duration</p>
                        <p class="mb-0">{{ plan.start_date|date:"M d, Y" }} to {{ plan.end_date|date:"M d, Y" }} ({{ plan.duration_days }} days)</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Description</p>
                        <p class="mb-0">{{ plan.description|truncatechars:100 }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Progress</p>
                        {% if plan.progress_data.total_steps > 0 %}
                        <div class="progress mb-2" style="height: 10px;">
                            <div class="progress-bar" role="progressbar" style="width: {{ plan.progress_data.progress_percentage }}%;" aria-valuenow="{{ plan.progress_data.progress_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <p class="small text-muted mb-0">{{ plan.progress_data.completed_steps }} of {{ plan.progress_data.total_steps }} steps completed ({{ plan.progress_data.progress_percentage }}%)</p>
                        {% else %}
                        <p class="small text-muted mb-0">No steps defined yet</p>
                        {% endif %}
                    </div>
                    
                    {% if plan.is_active %}
                    <div class="alert alert-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>This treatment plan is currently active.</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{% url 'medicine:treatment_plan_detail' plan.id %}" class="btn btn-primary w-100">
                        <i class="fas fa-eye me-1"></i> View Details
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-clipboard-check text-muted mb-3" style="font-size: 3rem;"></i>
                    <h5>No Treatment Plans Found</h5>
                    <p class="text-muted">
                        {% if selected_pond or selected_status %}
                        No treatment plans match your filter criteria.
                        <a href="{% url 'medicine:treatment_plan_list' %}">Clear filters</a>
                        {% else %}
                        You haven't created any treatment plans yet.
                        {% endif %}
                    </p>
                    <a href="{% url 'medicine:treatment_plan_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i> Create Treatment Plan
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
    // Update current time
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    // Update time immediately and then every minute
    updateTime();
    setInterval(updateTime, 60000);
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
