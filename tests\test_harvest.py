import json
from datetime import datetime, timedelta
from django.test import TestCase
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APIClient

from harvest.models import HarvestPlan, HarvestResult, QualityAssessment
from ponds.models import Pond
from users.models import User
# Note: Farm model is not implemented yet - creating simple farm placeholder


class HarvestModelTests(TestCase):
    """Test suite for the harvest models."""

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Note: Farm model not properly implemented, creating pond without farm reference
        self.pond = Pond.objects.create(
            name='Test Pond',
            size=1000,
            occupancy=80,
            water_quality='Good',
            feeding_status='On Schedule',
            status='active',
            species='White Shrimp (L. vannamei)',
            density='120 PL/m²',
            latitude=10.0,
            longitude=20.0
        )

    def test_harvest_plan_creation(self):
        """Test creating a harvest plan."""
        plan = HarvestPlan.objects.create(
            pond=self.pond,
            planned_date=(datetime.now() + timedelta(days=30)).date(),
            estimated_biomass=500.0,
            estimated_size=25.0,
            estimated_count=20,
            status='Planned',
            notes='Planned harvest for next month',
            created_by=self.user
        )
        
        self.assertEqual(plan.pond, self.pond)
        self.assertEqual(plan.estimated_biomass, 500.0)
        self.assertEqual(plan.estimated_size, 25.0)
        self.assertEqual(plan.estimated_count, 20)
        self.assertEqual(plan.status, 'Planned')
        self.assertEqual(plan.created_by, self.user)
        
        self.assertEqual(str(plan), f'Harvest Plan for {self.pond.name} - {plan.planned_date}')

    def test_harvest_result_creation(self):
        """Test creating a harvest result."""
        plan = HarvestPlan.objects.create(
            pond=self.pond,
            planned_date=(datetime.now() + timedelta(days=30)).date(),
            estimated_biomass=500.0,
            estimated_size=25.0,
            estimated_count=20,
            status='Planned',
            notes='Planned harvest for next month'
        )
        
        result = HarvestResult.objects.create(
            pond=self.pond,
            harvest_plan=plan,
            harvest_date=datetime.now().date(),
            actual_biomass=480.0,  # kg
            average_size=23.5,  # grams
            count_per_kg=20,
            survival_rate=85.0,  # %
            notes='Successful harvest with good quality'
        )
        
        self.assertEqual(result.pond, self.pond)
        self.assertEqual(result.harvest_plan, plan)
        self.assertEqual(result.actual_biomass, 480.0)
        self.assertEqual(result.average_size, 23.5)
        self.assertEqual(result.count_per_kg, 20)
        self.assertEqual(result.survival_rate, 85.0)
        
        self.assertEqual(str(result), f'Harvest Result: {self.pond.name} on {result.harvest_date}')

    def test_quality_assessment_creation(self):
        """Test creating a quality assessment."""
        # This replaces the growth sample test since GrowthSample doesn't exist
        plan = HarvestPlan.objects.create(
            pond=self.pond,
            planned_date=(datetime.now() + timedelta(days=30)).date(),
            estimated_biomass=500.0,
            estimated_size=25.0,
            estimated_count=20,
            status='Planned'
        )
        
        result = HarvestResult.objects.create(
            pond=self.pond,
            harvest_plan=plan,
            harvest_date=datetime.now().date(),
            actual_biomass=480.0,
            average_size=23.5,
            count_per_kg=20,
            survival_rate=85.0,
            notes='Successful harvest with good quality'
        )
        
        assessment = QualityAssessment.objects.create(
            harvest_result=result,
            assessment_date=datetime.now().date(),
            uniformity_score=8,
            color_score=9,
            texture_score=8,
            defect_rate=2.5,
            overall_quality='Good',
            notes='Good quality shrimp with minimal defects',
            assessed_by='Test Assessor'
        )
        
        self.assertEqual(assessment.harvest_result, result)
        self.assertEqual(assessment.uniformity_score, 8)
        self.assertEqual(assessment.color_score, 9)
        self.assertEqual(assessment.texture_score, 8)
        self.assertEqual(assessment.defect_rate, 2.5)
        self.assertEqual(assessment.overall_quality, 'Good')
        self.assertEqual(assessment.assessed_by, 'Test Assessor')
        
        self.assertEqual(str(assessment), f'Quality Assessment for {result} - {assessment.assessment_date}')


class HarvestAPITests(TestCase):
    """Test suite for the harvest API endpoints."""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            first_name='Test',
            last_name='User'
        )
        # Create a simple pond without farm reference for now
        self.pond = Pond.objects.create(
            name='Test Pond',
            area=1000.0,
            depth=2.5,
            shape='rectangular',
            coordinates=[
                {'latitude': 10.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 21.0},
                {'latitude': 11.0, 'longitude': 20.0},
                {'latitude': 10.0, 'longitude': 20.0},
            ],
            status='active',
            water_source='river',
            bottom_type='mud',
            created_by=self.user
        )
        
        # Create harvest plan
        self.plan = HarvestPlan.objects.create(
            pond=self.pond,
            planned_date=(datetime.now() + timedelta(days=30)).date(),
            estimated_biomass=500.0,
            estimated_size=25.0,
            estimated_count=20,
            status='Planned',
            notes='Planned harvest for next month',
            created_by=self.user
        )
        
        # Create harvest result
        self.result = HarvestResult.objects.create(
            pond=self.pond,
            harvest_plan=self.plan,
            harvest_date=datetime.now().date(),
            actual_biomass=480.0,
            average_size=23.5,
            count_per_kg=20,
            survival_rate=85.0,
            notes='Successful harvest with good quality'
        )
        
        # Create quality assessments
        self.assessment1 = QualityAssessment.objects.create(
            harvest_result=self.result,
            assessment_date=(datetime.now() - timedelta(days=14)).date(),
            uniformity_score=8,
            color_score=7,
            texture_score=8,
            defect_rate=3.0,
            overall_quality='Good',
            assessed_by='Test Assessor'
        )
        
        self.assessment2 = QualityAssessment.objects.create(
            harvest_result=self.result,
            assessment_date=datetime.now().date(),
            uniformity_score=9,
            color_score=8,
            texture_score=9,
            defect_rate=2.0,
            overall_quality='Excellent',
            assessed_by='Test Assessor'
        )
        
        # Authenticate the client
        self.client.force_authenticate(user=self.user)

    def test_get_harvest_plans(self):
        """Test retrieving harvest plans."""
        url = reverse('harvestplan-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['estimated_biomass'], 500.0)
        self.assertEqual(response.data[0]['status'], 'Planned')

    def test_create_harvest_plan(self):
        """Test creating a new harvest plan."""
        url = reverse('harvestplan-list')
        data = {
            'pond': self.pond.id,
            'planned_date': (datetime.now() + timedelta(days=60)).date().isoformat(),
            'estimated_biomass': 600.0,
            'estimated_size': 30.0,
            'estimated_count': 22,
            'status': 'Planned',
            'notes': 'Second planned harvest'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['estimated_biomass'], 600.0)
        self.assertEqual(response.data['estimated_size'], 30.0)
        self.assertEqual(HarvestPlan.objects.count(), 2)

    def test_update_harvest_plan(self):
        """Test updating a harvest plan."""
        url = reverse('harvestplan-detail', args=[self.plan.id])
        data = {
            'status': 'In Progress',
            'notes': 'Updated harvest plan'
        }
        
        response = self.client.patch(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['status'], 'In Progress')
        self.assertEqual(response.data['notes'], 'Updated harvest plan')
        
        # Verify the update in the database
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.status, 'In Progress')
        self.assertEqual(self.plan.notes, 'Updated harvest plan')

    def test_get_harvest_results(self):
        """Test retrieving harvest results."""
        url = reverse('harvestresult-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['actual_biomass'], 480.0)
        self.assertEqual(response.data[0]['average_size'], 23.5)

    def test_create_harvest_result(self):
        """Test creating a new harvest result."""
        url = reverse('harvestresult-list')
        data = {
            'pond': self.pond.id,
            'harvest_plan': self.plan.id,
            'harvest_date': datetime.now().date().isoformat(),
            'actual_biomass': 520.0,
            'average_size': 26.0,
            'count_per_kg': 18,
            'survival_rate': 88.0,
            'notes': 'Second successful harvest'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['actual_biomass'], 520.0)
        self.assertEqual(response.data['average_size'], 26.0)
        self.assertEqual(HarvestResult.objects.count(), 2)

    def test_get_quality_assessments(self):
        """Test retrieving quality assessments."""
        url = reverse('qualityassessment-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        self.assertEqual(response.data[0]['uniformity_score'], 8)
        self.assertEqual(response.data[1]['uniformity_score'], 9)

    def test_create_quality_assessment(self):
        """Test creating a new quality assessment."""
        url = reverse('qualityassessment-list')
        data = {
            'harvest_result': self.result.id,
            'assessment_date': datetime.now().date().isoformat(),
            'uniformity_score': 8,
            'color_score': 9,
            'texture_score': 8,
            'defect_rate': 1.5,
            'overall_quality': 'Excellent',
            'notes': 'Latest quality assessment'
        }
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['uniformity_score'], 8)
        self.assertEqual(response.data['overall_quality'], 'Excellent')
        self.assertEqual(QualityAssessment.objects.count(), 3)

    def test_filter_harvest_results_by_pond(self):
        """Test filtering harvest results by pond."""
        # Create another pond and result
        pond2 = Pond.objects.create(
            name='Second Pond',
            area=800.0,
            depth=2.0,
            shape='rectangular',
            coordinates=[
                {'latitude': 12.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 23.0},
                {'latitude': 13.0, 'longitude': 22.0},
                {'latitude': 12.0, 'longitude': 22.0},
            ],
            status='active',
            water_source='well',
            bottom_type='sand',
            created_by=self.user
        )
        
        plan2 = HarvestPlan.objects.create(
            pond=pond2,
            planned_date=(datetime.now() + timedelta(days=15)).date(),
            estimated_biomass=400.0,
            estimated_size=22.0,
            estimated_count=18,
            status='Planned',
            created_by=self.user
        )
        
        result2 = HarvestResult.objects.create(
            pond=pond2,
            harvest_plan=plan2,
            harvest_date=datetime.now().date(),
            actual_biomass=420.0,
            average_size=21.0,
            count_per_kg=19,
            survival_rate=80.0,
            notes='Second pond harvest'
        )
        
        # Filter by the first pond
        url = f"{reverse('harvestresult-list')}?pond={self.pond.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['actual_biomass'], 480.0)
        
        # Filter by the second pond
        url = f"{reverse('harvestresult-list')}?pond={pond2.id}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['actual_biomass'], 420.0)

    def test_get_quality_assessment_trend(self):
        """Test retrieving quality assessment trend for a pond."""
        url = f"{reverse('pond-quality-trend', args=[self.pond.id])}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)
        
        # Verify that assessments are ordered by date
        self.assertEqual(response.data[0]['uniformity_score'], 8)
        self.assertEqual(response.data[1]['uniformity_score'], 9)

    def test_get_harvest_summary(self):
        """Test retrieving harvest summary for a pond."""
        url = f"{reverse('pond-harvest-summary', args=[self.pond.id])}"
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('total_harvests', response.data)
        self.assertIn('total_biomass', response.data)
        self.assertIn('average_size', response.data)
        self.assertIn('average_survival', response.data)
        
        self.assertEqual(response.data['total_harvests'], 1)
        self.assertEqual(response.data['total_biomass'], 480.0)
        self.assertEqual(response.data['average_size'], 23.5)
        self.assertEqual(response.data['average_survival'], 85.0)
