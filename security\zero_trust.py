"""
Zero-Trust Security Architecture
Implements comprehensive zero-trust security model with advanced authentication,
authorization, and continuous security monitoring
"""

import jwt
import hashlib
import secrets
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
import requests
import json
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

logger = logging.getLogger(__name__)

@dataclass
class SecurityContext:
    """Security context for zero-trust evaluation"""
    user_id: str
    session_id: str
    device_id: str
    ip_address: str
    user_agent: str
    location: Optional[Dict[str, Any]] = None
    risk_score: float = 0.0
    trust_level: str = "unknown"  # unknown, low, medium, high
    last_verified: Optional[datetime] = None
    mfa_verified: bool = False
    device_trusted: bool = False

@dataclass
class AccessPolicy:
    """Access control policy definition"""
    resource: str
    action: str
    conditions: Dict[str, Any]
    required_trust_level: str
    require_mfa: bool = False
    time_restrictions: Optional[Dict[str, Any]] = None
    ip_restrictions: Optional[List[str]] = None
    device_restrictions: Optional[List[str]] = None

class ZeroTrustEngine:
    """
    Zero-Trust Security Engine
    Implements continuous verification and adaptive access control
    """
    
    def __init__(self):
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        self.security_policies: Dict[str, AccessPolicy] = {}
        self.risk_factors: Dict[str, float] = {}
        self.threat_intelligence: Dict[str, Any] = {}
        
        # Initialize security policies
        self._setup_security_policies()
        
        # Initialize risk factors
        self._setup_risk_factors()
        
        # Start threat intelligence updates
        self._update_threat_intelligence()
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for sensitive data"""
        key_file = os.path.join(settings.BASE_DIR, '.security_key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            os.chmod(key_file, 0o600)  # Restrict permissions
            return key
    
    def _setup_security_policies(self):
        """Setup zero-trust access policies"""
        policies = {
            "admin_access": AccessPolicy(
                resource="admin/*",
                action="*",
                conditions={"role": "admin"},
                required_trust_level="high",
                require_mfa=True,
                time_restrictions={"business_hours_only": True},
                ip_restrictions=["10.0.0.0/8", "***********/16"]
            ),
            "pond_management": AccessPolicy(
                resource="ponds/*",
                action="write",
                conditions={"role": ["farm_manager", "admin"]},
                required_trust_level="medium",
                require_mfa=True
            ),
            "water_quality_data": AccessPolicy(
                resource="water-quality/*",
                action="read",
                conditions={"role": ["farm_manager", "technician", "admin"]},
                required_trust_level="low",
                require_mfa=False
            ),
            "ml_models": AccessPolicy(
                resource="ai-ml/models/*",
                action="deploy",
                conditions={"role": ["data_scientist", "admin"]},
                required_trust_level="high",
                require_mfa=True,
                device_restrictions=["trusted_workstation"]
            ),
            "analytics_access": AccessPolicy(
                resource="analytics/*",
                action="read",
                conditions={"role": ["analyst", "farm_manager", "admin"]},
                required_trust_level="medium",
                require_mfa=False
            )
        }
        
        self.security_policies.update(policies)
        logger.info(f"Configured {len(policies)} security policies")
    
    def _setup_risk_factors(self):
        """Setup risk scoring factors"""
        self.risk_factors = {
            "unknown_device": 30.0,
            "new_location": 20.0,
            "unusual_time": 15.0,
            "suspicious_ip": 40.0,
            "failed_attempts": 25.0,
            "outdated_browser": 10.0,
            "no_mfa": 20.0,
            "privileged_access": 15.0,
            "bulk_operations": 10.0,
            "api_access": 5.0
        }
    
    def _update_threat_intelligence(self):
        """Update threat intelligence data"""
        try:
            # In production, this would fetch from threat intelligence feeds
            self.threat_intelligence = {
                "malicious_ips": [
                    "*********",  # Example malicious IP
                    "************"
                ],
                "suspicious_user_agents": [
                    "sqlmap",
                    "nikto",
                    "nmap"
                ],
                "known_attack_patterns": [
                    "sql_injection",
                    "xss_attempt",
                    "brute_force"
                ],
                "last_updated": datetime.now().isoformat()
            }
            
            logger.info("Threat intelligence updated")
            
        except Exception as e:
            logger.error(f"Failed to update threat intelligence: {e}")
    
    def evaluate_security_context(self, request_data: Dict[str, Any]) -> SecurityContext:
        """Evaluate security context for incoming request"""
        try:
            # Extract request information
            user_id = request_data.get('user_id', 'anonymous')
            session_id = request_data.get('session_id', '')
            device_id = request_data.get('device_id', '')
            ip_address = request_data.get('ip_address', '')
            user_agent = request_data.get('user_agent', '')
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(request_data)
            
            # Determine trust level
            trust_level = self._determine_trust_level(risk_score, request_data)
            
            # Check device trust
            device_trusted = self._is_device_trusted(device_id, user_id)
            
            # Check MFA status
            mfa_verified = self._check_mfa_status(user_id, session_id)
            
            # Get location information
            location = self._get_location_info(ip_address)
            
            context = SecurityContext(
                user_id=user_id,
                session_id=session_id,
                device_id=device_id,
                ip_address=ip_address,
                user_agent=user_agent,
                location=location,
                risk_score=risk_score,
                trust_level=trust_level,
                last_verified=datetime.now(),
                mfa_verified=mfa_verified,
                device_trusted=device_trusted
            )
            
            # Store context for continuous monitoring
            self._store_security_context(context)
            
            return context
            
        except Exception as e:
            logger.error(f"Security context evaluation failed: {e}")
            return SecurityContext(
                user_id=user_id,
                session_id=session_id,
                device_id=device_id,
                ip_address=ip_address,
                user_agent=user_agent,
                risk_score=100.0,  # Maximum risk on error
                trust_level="unknown"
            )
    
    def _calculate_risk_score(self, request_data: Dict[str, Any]) -> float:
        """Calculate risk score based on multiple factors"""
        risk_score = 0.0
        
        # Check IP reputation
        ip_address = request_data.get('ip_address', '')
        if ip_address in self.threat_intelligence.get('malicious_ips', []):
            risk_score += self.risk_factors['suspicious_ip']
        
        # Check user agent
        user_agent = request_data.get('user_agent', '').lower()
        for suspicious_agent in self.threat_intelligence.get('suspicious_user_agents', []):
            if suspicious_agent in user_agent:
                risk_score += self.risk_factors['suspicious_ip']
                break
        
        # Check device familiarity
        device_id = request_data.get('device_id', '')
        user_id = request_data.get('user_id', '')
        if not self._is_device_known(device_id, user_id):
            risk_score += self.risk_factors['unknown_device']
        
        # Check location
        if self._is_new_location(ip_address, user_id):
            risk_score += self.risk_factors['new_location']
        
        # Check time patterns
        if self._is_unusual_time(user_id):
            risk_score += self.risk_factors['unusual_time']
        
        # Check recent failed attempts
        if self._has_recent_failed_attempts(user_id, ip_address):
            risk_score += self.risk_factors['failed_attempts']
        
        # Check MFA status
        if not request_data.get('mfa_verified', False):
            risk_score += self.risk_factors['no_mfa']
        
        return min(risk_score, 100.0)  # Cap at 100
    
    def _determine_trust_level(self, risk_score: float, request_data: Dict[str, Any]) -> str:
        """Determine trust level based on risk score and other factors"""
        if risk_score >= 70:
            return "unknown"
        elif risk_score >= 40:
            return "low"
        elif risk_score >= 20:
            return "medium"
        else:
            return "high"
    
    def _is_device_trusted(self, device_id: str, user_id: str) -> bool:
        """Check if device is trusted for user"""
        cache_key = f"trusted_device:{user_id}:{device_id}"
        return cache.get(cache_key, False)
    
    def _is_device_known(self, device_id: str, user_id: str) -> bool:
        """Check if device is known for user"""
        cache_key = f"known_device:{user_id}:{device_id}"
        return cache.get(cache_key, False)
    
    def _check_mfa_status(self, user_id: str, session_id: str) -> bool:
        """Check if MFA is verified for session"""
        cache_key = f"mfa_verified:{user_id}:{session_id}"
        return cache.get(cache_key, False)
    
    def _get_location_info(self, ip_address: str) -> Optional[Dict[str, Any]]:
        """Get location information for IP address"""
        try:
            # In production, use a geolocation service
            # For now, return mock data
            return {
                "country": "US",
                "region": "California",
                "city": "San Francisco",
                "latitude": 37.7749,
                "longitude": -122.4194
            }
        except Exception as e:
            logger.warning(f"Failed to get location info: {e}")
            return None
    
    def _is_new_location(self, ip_address: str, user_id: str) -> bool:
        """Check if location is new for user"""
        cache_key = f"user_locations:{user_id}"
        known_locations = cache.get(cache_key, set())
        
        location = self._get_location_info(ip_address)
        if location:
            location_key = f"{location['country']}:{location['region']}"
            if location_key not in known_locations:
                known_locations.add(location_key)
                cache.set(cache_key, known_locations, 86400 * 30)  # 30 days
                return True
        
        return False
    
    def _is_unusual_time(self, user_id: str) -> bool:
        """Check if access time is unusual for user"""
        current_hour = datetime.now().hour
        
        # Business hours: 6 AM to 10 PM
        if 6 <= current_hour <= 22:
            return False
        
        # Check user's typical access patterns
        cache_key = f"access_patterns:{user_id}"
        patterns = cache.get(cache_key, {})
        
        hour_count = patterns.get(str(current_hour), 0)
        total_accesses = sum(patterns.values())
        
        if total_accesses > 10:  # Enough data to make judgment
            hour_percentage = (hour_count / total_accesses) * 100
            return hour_percentage < 5  # Less than 5% of accesses at this hour
        
        return current_hour < 6 or current_hour > 22  # Default to business hours
    
    def _has_recent_failed_attempts(self, user_id: str, ip_address: str) -> bool:
        """Check for recent failed authentication attempts"""
        cache_key = f"failed_attempts:{user_id}:{ip_address}"
        failed_count = cache.get(cache_key, 0)
        return failed_count >= 3
    
    def _store_security_context(self, context: SecurityContext):
        """Store security context for monitoring"""
        cache_key = f"security_context:{context.user_id}:{context.session_id}"
        cache.set(cache_key, asdict(context), 3600)  # 1 hour
        
        # Update access patterns
        self._update_access_patterns(context)
    
    def _update_access_patterns(self, context: SecurityContext):
        """Update user access patterns for behavioral analysis"""
        cache_key = f"access_patterns:{context.user_id}"
        patterns = cache.get(cache_key, {})
        
        current_hour = str(datetime.now().hour)
        patterns[current_hour] = patterns.get(current_hour, 0) + 1
        
        cache.set(cache_key, patterns, 86400 * 30)  # 30 days
    
    def authorize_access(self, context: SecurityContext, resource: str, 
                        action: str) -> Tuple[bool, str]:
        """Authorize access based on zero-trust evaluation"""
        try:
            # Find matching policy
            policy = self._find_matching_policy(resource, action)
            if not policy:
                return False, "No matching policy found"
            
            # Check trust level requirement
            trust_levels = {"unknown": 0, "low": 1, "medium": 2, "high": 3}
            required_level = trust_levels.get(policy.required_trust_level, 3)
            current_level = trust_levels.get(context.trust_level, 0)
            
            if current_level < required_level:
                return False, f"Insufficient trust level. Required: {policy.required_trust_level}, Current: {context.trust_level}"
            
            # Check MFA requirement
            if policy.require_mfa and not context.mfa_verified:
                return False, "Multi-factor authentication required"
            
            # Check IP restrictions
            if policy.ip_restrictions and not self._check_ip_restrictions(context.ip_address, policy.ip_restrictions):
                return False, "IP address not allowed"
            
            # Check time restrictions
            if policy.time_restrictions and not self._check_time_restrictions(policy.time_restrictions):
                return False, "Access not allowed at this time"
            
            # Check device restrictions
            if policy.device_restrictions and not self._check_device_restrictions(context.device_id, policy.device_restrictions):
                return False, "Device not authorized"
            
            # Log successful authorization
            self._log_access_decision(context, resource, action, True, "Access granted")
            
            return True, "Access granted"
            
        except Exception as e:
            logger.error(f"Authorization failed: {e}")
            self._log_access_decision(context, resource, action, False, f"Authorization error: {e}")
            return False, "Authorization error"
    
    def _find_matching_policy(self, resource: str, action: str) -> Optional[AccessPolicy]:
        """Find matching access policy for resource and action"""
        for policy_name, policy in self.security_policies.items():
            if self._resource_matches(resource, policy.resource) and self._action_matches(action, policy.action):
                return policy
        return None
    
    def _resource_matches(self, resource: str, pattern: str) -> bool:
        """Check if resource matches pattern"""
        if pattern == "*":
            return True
        if pattern.endswith("/*"):
            return resource.startswith(pattern[:-2])
        return resource == pattern
    
    def _action_matches(self, action: str, pattern: str) -> bool:
        """Check if action matches pattern"""
        return pattern == "*" or action == pattern
    
    def _check_ip_restrictions(self, ip_address: str, allowed_ranges: List[str]) -> bool:
        """Check if IP address is within allowed ranges"""
        import ipaddress
        
        try:
            ip = ipaddress.ip_address(ip_address)
            for range_str in allowed_ranges:
                if ip in ipaddress.ip_network(range_str, strict=False):
                    return True
            return False
        except Exception as e:
            logger.warning(f"IP restriction check failed: {e}")
            return False
    
    def _check_time_restrictions(self, restrictions: Dict[str, Any]) -> bool:
        """Check time-based access restrictions"""
        if restrictions.get("business_hours_only", False):
            current_hour = datetime.now().hour
            return 6 <= current_hour <= 22
        
        return True
    
    def _check_device_restrictions(self, device_id: str, allowed_devices: List[str]) -> bool:
        """Check device restrictions"""
        return device_id in allowed_devices or "trusted_workstation" in allowed_devices
    
    def _log_access_decision(self, context: SecurityContext, resource: str, 
                           action: str, granted: bool, reason: str):
        """Log access decision for audit trail"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "user_id": context.user_id,
            "session_id": context.session_id,
            "device_id": context.device_id,
            "ip_address": context.ip_address,
            "resource": resource,
            "action": action,
            "granted": granted,
            "reason": reason,
            "risk_score": context.risk_score,
            "trust_level": context.trust_level
        }
        
        # In production, send to SIEM or audit log system
        logger.info(f"Access decision: {json.dumps(log_entry)}")
    
    def continuous_monitoring(self):
        """Continuous security monitoring and threat detection"""
        try:
            # Monitor active sessions
            self._monitor_active_sessions()
            
            # Detect anomalous behavior
            self._detect_anomalies()
            
            # Update threat intelligence
            self._update_threat_intelligence()
            
            logger.info("Continuous monitoring cycle completed")
            
        except Exception as e:
            logger.error(f"Continuous monitoring failed: {e}")
    
    def _monitor_active_sessions(self):
        """Monitor active user sessions for security threats"""
        # Implementation would monitor active sessions
        # Check for session hijacking, concurrent sessions, etc.
        pass
    
    def _detect_anomalies(self):
        """Detect anomalous user behavior patterns"""
        # Implementation would use ML to detect anomalies
        # Unusual access patterns, data exfiltration, etc.
        pass
    
    def get_security_dashboard(self) -> Dict[str, Any]:
        """Get security dashboard data"""
        return {
            "active_policies": len(self.security_policies),
            "threat_intelligence_updated": self.threat_intelligence.get("last_updated"),
            "risk_factors": len(self.risk_factors),
            "security_events": self._get_recent_security_events(),
            "trust_level_distribution": self._get_trust_level_distribution()
        }
    
    def _get_recent_security_events(self) -> List[Dict[str, Any]]:
        """Get recent security events"""
        # In production, query from security event log
        return [
            {
                "timestamp": datetime.now().isoformat(),
                "event_type": "failed_authentication",
                "severity": "medium",
                "user_id": "user123",
                "ip_address": "*************"
            }
        ]
    
    def _get_trust_level_distribution(self) -> Dict[str, int]:
        """Get distribution of trust levels"""
        # In production, query from active sessions
        return {
            "high": 45,
            "medium": 32,
            "low": 18,
            "unknown": 5
        }

# Global zero-trust engine instance
zero_trust_engine = ZeroTrustEngine()
