<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Quality Dashboard - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #64748b;
            font-weight: 500;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .parameter-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s ease;
            border-left: 4px solid;
        }

        .parameter-card:hover {
            transform: translateY(-2px);
        }

        .parameter-optimal { border-left-color: #28a745; }
        .parameter-warning { border-left-color: #ffc107; }
        .parameter-critical { border-left-color: #dc3545; }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
        }

        .badge-optimal {
            background: #d4edda;
            color: #155724;
        }

        .badge-warning {
            background: #fff3cd;
            color: #856404;
        }

        .badge-critical {
            background: #f8d7da;
            color: #721c24;
        }

        .reading-trend {
            font-size: 0.9rem;
            font-weight: 600;
        }

        .trend-up { color: #28a745; }
        .trend-down { color: #dc3545; }
        .trend-stable { color: #6c757d; }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.2rem;
            font-weight: 500;
        }

        .pond-selector {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .select-enhanced {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px;
            transition: all 0.3s ease;
        }

        .select-enhanced:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>💧 Water Quality Dashboard</h1>
        <p>Monitor and analyze water parameters for optimal shrimp health</p>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="#" class="quick-action">
                <i class="fas fa-plus"></i>
                <span>Add Reading</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-chart-line"></i>
                <span>Historical Data</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Alerts</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-download"></i>
                <span>Export Report</span>
            </a>
            <a href="#" class="quick-action">
                <i class="fas fa-cog"></i>
                <span>Settings</span>
            </a>
        </div>
    </div>

    <!-- Pond Selection -->
    <div class="pond-selector">
        <div class="row align-items-center">
            <div class="col-md-6">
                <label for="pond-select" class="form-label">
                    <i class="fas fa-water me-2"></i>
                    <strong>Select Pond</strong>
                </label>
                <select id="pond-select" class="form-select select-enhanced">
                    <option value="pond-01">Pond A-01 - Active</option>
                    <option value="pond-02">Pond A-02 - Active</option>
                    <option value="pond-03" selected>Pond B-03 - Active</option>
                    <option value="pond-04">Pond B-04 - Maintenance</option>
                    <option value="pond-05">Pond C-05 - Active</option>
                </select>
            </div>
            <div class="col-md-6 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button class="btn btn-outline-primary">
                        <i class="fas fa-sync-alt me-2"></i>
                        Refresh Data
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-bell me-2"></i>
                        Alert Settings
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Water Quality Status -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-danger">
                    <i class="fas fa-thermometer-half"></i>
                </div>
                <div class="stats-value">28.5°C</div>
                <div class="stats-label">Temperature</div>
                <div class="reading-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +0.3°
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-primary">
                    <i class="fas fa-flask"></i>
                </div>
                <div class="stats-value">7.2</div>
                <div class="stats-label">pH Level</div>
                <div class="reading-trend trend-stable">
                    <i class="fas fa-minus"></i> Stable
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-info">
                    <i class="fas fa-wind"></i>
                </div>
                <div class="stats-value">6.8</div>
                <div class="stats-label">Dissolved O₂</div>
                <div class="reading-trend trend-down">
                    <i class="fas fa-arrow-down"></i> -0.2
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-warning">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="stats-value">25.3</div>
                <div class="stats-label">Salinity (ppt)</div>
                <div class="reading-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +0.1
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-success">
                    <i class="fas fa-atom"></i>
                </div>
                <div class="stats-value">0.02</div>
                <div class="stats-label">Ammonia</div>
                <div class="reading-trend trend-stable">
                    <i class="fas fa-minus"></i> Stable
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card">
                <div class="stats-icon text-secondary">
                    <i class="fas fa-eye"></i>
                </div>
                <div class="stats-value">35</div>
                <div class="stats-label">Turbidity</div>
                <div class="reading-trend trend-up">
                    <i class="fas fa-arrow-up"></i> +2
                </div>
            </div>
        </div>
    </div>

    <!-- Parameter Details -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                Detailed Parameter Analysis
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="parameter-card parameter-optimal">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-thermometer-half text-danger me-2"></i>
                                    Temperature
                                </h6>
                                <div class="h4 mb-0">28.5°C</div>
                                <small class="text-muted">Optimal Range: 26-30°C</small>
                            </div>
                            <span class="status-badge badge-optimal">Optimal</span>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="parameter-card parameter-optimal">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-flask text-primary me-2"></i>
                                    pH Level
                                </h6>
                                <div class="h4 mb-0">7.2</div>
                                <small class="text-muted">Optimal Range: 6.8-7.5</small>
                            </div>
                            <span class="status-badge badge-optimal">Optimal</span>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 82%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="parameter-card parameter-warning">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-wind text-info me-2"></i>
                                    Dissolved Oxygen
                                </h6>
                                <div class="h4 mb-0">6.8 mg/L</div>
                                <small class="text-muted">Optimal Range: >5.0 mg/L</small>
                            </div>
                            <span class="status-badge badge-warning">Low</span>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="parameter-card parameter-optimal">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="fas fa-tint text-warning me-2"></i>
                                    Salinity
                                </h6>
                                <div class="h4 mb-0">25.3 ppt</div>
                                <small class="text-muted">Optimal Range: 20-30 ppt</small>
                            </div>
                            <span class="status-badge badge-optimal">Good</span>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 78%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="chart-container">
                <h6 class="mb-3">
                    <i class="fas fa-chart-line me-2"></i>
                    24-Hour Trend Analysis
                </h6>
                <div class="chart-placeholder">
                    <div class="text-center">
                        <i class="fas fa-chart-line fa-3x mb-3"></i>
                        <div>24-hour parameter trend chart</div>
                        <small class="text-muted">Temperature, pH, and DO levels over time</small>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="chart-container">
                <h6 class="mb-3">
                    <i class="fas fa-chart-pie me-2"></i>
                    Quality Score
                </h6>
                <div class="chart-placeholder" style="height: 250px;">
                    <div class="text-center">
                        <i class="fas fa-chart-pie fa-3x mb-3"></i>
                        <div>Overall Quality: 87%</div>
                        <small class="text-muted">Good condition</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Alerts & Actions -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Recent Alerts
                    </h6>
                </div>
                <div class="card-body">
                    <div class="parameter-card parameter-warning">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-wind text-warning fa-lg me-3 mt-1"></i>
                            <div>
                                <h6 class="mb-1">Low Dissolved Oxygen</h6>
                                <p class="mb-1 small">DO level dropped to 6.8 mg/L in Pond B-03</p>
                                <small class="text-muted">15 minutes ago</small>
                            </div>
                        </div>
                    </div>
                    <div class="parameter-card parameter-optimal">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success fa-lg me-3 mt-1"></i>
                            <div>
                                <h6 class="mb-1">pH Normalized</h6>
                                <p class="mb-1 small">pH level returned to optimal range</p>
                                <small class="text-muted">2 hours ago</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Recommended Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="parameter-card parameter-warning">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">Increase Aeration</h6>
                                <p class="mb-1 small">Turn on aerators to boost oxygen levels</p>
                                <small class="text-muted">Priority: High</small>
                            </div>
                            <button class="btn btn-sm btn-warning">Execute</button>
                        </div>
                    </div>
                    <div class="parameter-card parameter-optimal">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">Monitor Temperature</h6>
                                <p class="mb-1 small">Continue monitoring temperature trend</p>
                                <small class="text-muted">Priority: Medium</small>
                            </div>
                            <button class="btn btn-sm btn-outline-primary">Monitor</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Water Quality Tools -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-tools me-2"></i>
                Water Quality Management Tools
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        Add Reading
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100">
                        <i class="fas fa-calendar me-2"></i>
                        Schedule Tests
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100">
                        <i class="fas fa-bell me-2"></i>
                        Set Alerts
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100">
                        <i class="fas fa-file-export me-2"></i>
                        Export Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Readings Table -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-history me-2"></i>
                Recent Readings - Pond B-03
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Timestamp</th>
                            <th>Temperature</th>
                            <th>pH</th>
                            <th>Dissolved O₂</th>
                            <th>Salinity</th>
                            <th>Ammonia</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>15:30 Today</td>
                            <td>28.5°C</td>
                            <td>7.2</td>
                            <td>6.8 mg/L</td>
                            <td>25.3 ppt</td>
                            <td>0.02 mg/L</td>
                            <td><span class="status-badge badge-warning">Warning</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                                <button class="btn btn-sm btn-outline-success">Edit</button>
                            </td>
                        </tr>
                        <tr>
                            <td>14:30 Today</td>
                            <td>28.2°C</td>
                            <td>7.1</td>
                            <td>7.2 mg/L</td>
                            <td>25.1 ppt</td>
                            <td>0.01 mg/L</td>
                            <td><span class="status-badge badge-optimal">Optimal</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                                <button class="btn btn-sm btn-outline-success">Edit</button>
                            </td>
                        </tr>
                        <tr>
                            <td>13:30 Today</td>
                            <td>27.9°C</td>
                            <td>7.0</td>
                            <td>7.5 mg/L</td>
                            <td>24.9 ppt</td>
                            <td>0.01 mg/L</td>
                            <td><span class="status-badge badge-optimal">Optimal</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                                <button class="btn btn-sm btn-outline-success">Edit</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Development Notice -->
    <div class="alert alert-info mt-4">
        <h6><i class="fas fa-info-circle me-2"></i>Water Quality Management System</h6>
        <p class="mb-0">
            Advanced water quality monitoring with real-time alerts, trend analysis, and automated recommendations.
            Integration with IoT sensors and laboratory equipment for comprehensive water quality management.
        </p>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Pond selection change handler
    document.getElementById('pond-select').addEventListener('change', function() {
        console.log('Pond selected:', this.value);
        // Add logic to update data based on selected pond
    });

    // Auto-refresh data every 5 minutes
    setInterval(function() {
        console.log('Auto-refreshing water quality data...');
        // Add logic to refresh data
    }, 300000);

    // Simulate real-time updates
    function simulateRealTimeUpdates() {
        // Simulate parameter changes
        setInterval(function() {
            // Update dissolved oxygen with slight variation
            const doElement = document.querySelector('.stats-card .stats-value');
            if (doElement && doElement.textContent.includes('6.8')) {
                const currentValue = parseFloat(doElement.textContent);
                const newValue = (currentValue + (Math.random() - 0.5) * 0.2).toFixed(1);
                doElement.textContent = newValue;
                
                // Update trend indicator
                const trendElement = doElement.parentNode.querySelector('.reading-trend');
                if (newValue > currentValue) {
                    trendElement.innerHTML = '<i class="fas fa-arrow-up"></i> +' + (newValue - currentValue).toFixed(1);
                    trendElement.className = 'reading-trend trend-up';
                } else if (newValue < currentValue) {
                    trendElement.innerHTML = '<i class="fas fa-arrow-down"></i> ' + (newValue - currentValue).toFixed(1);
                    trendElement.className = 'reading-trend trend-down';
                } else {
                    trendElement.innerHTML = '<i class="fas fa-minus"></i> Stable';
                    trendElement.className = 'reading-trend trend-stable';
                }
            }
        }, 10000); // Update every 10 seconds for demo
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Water Quality Dashboard loaded');
        simulateRealTimeUpdates();
    });
</script>

</body>
</html>
