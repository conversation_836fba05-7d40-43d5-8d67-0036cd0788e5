<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if form.instance.pk %}Edit Task{% else %}Create New Task{% endif %} | Labor Management - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;
            --border-radius: 12px;
            --border-radius-lg: 16px;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }
        
        * {
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        /* Enhanced Header matching task list */
        .tasks-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 40px;
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .tasks-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: tasks-sweep 4s infinite;
        }
        
        @keyframes tasks-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .task-header .content {
            position: relative;
            z-index: 2;
        }
        
        .task-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .task-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 400;
        }
        
        /* Navigation */
        .nav-container {
            background: rgba(102, 126, 234, 0.05);
            padding: 20px 30px;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 0.875rem;
        }
        
        .breadcrumb-item a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s ease;
        }
        
        .breadcrumb-item a:hover {
            color: #764ba2;
        }
        
        .breadcrumb-item.active {
            color: #6c757d;
        }
        
        /* Form Container */
        .form-container {
            padding: 30px;
        }
        
        /* Progress Bar */
        .progress-container {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            margin-bottom: 30px;
        }
        
        .progress-bar-custom {
            height: 4px;
            background: var(--gray-200);
            border-radius: 2px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 2px;
        }
        
        .progress-text {
            font-size: 0.875rem;
            color: var(--gray-600);
            margin-top: 0.5rem;
            text-align: center;
        }
        
        /* Form Sections - Enhanced Card Style */
        .enhanced-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .enhanced-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            border-color: rgba(102, 126, 234, 0.2);
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            margin-bottom: 25px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }
        
        .form-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
            border-color: rgba(102, 126, 234, 0.2);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .section-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
            color: var(--gray-800);
        }
        
        .section-header i {
            color: var(--primary-color);
            font-size: 1.125rem;
        }
        
        /* Form Fields */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .form-label i {
            color: var(--primary-color);
            font-size: 0.875rem;
        }
        
        .required::after {
            content: ' *';
            color: var(--danger-color);
            font-weight: 600;
        }
        
        .form-select, .form-control {
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            transition: all 0.3s ease;
            background: white;
            width: 100%;
        }

        .form-select:focus, .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .form-control:hover,
        .form-select:hover {
            border-color: #667eea;
        }
        
        /* Textarea */
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
        }
        
        /* Help Text */
        .form-text {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
            line-height: 1.4;
        }
        
        /* Priority Indicators */
        .priority-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .priority-low {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }
        
        .priority-medium {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }
        
        .priority-high {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }
        
        .priority-urgent {
            background: rgba(147, 51, 234, 0.1);
            color: #9333ea;
        }
        
        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            padding-top: 2rem;
            border-top: 1px solid var(--gray-200);
            margin-top: 2rem;
        }
        
        .btn-modern {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .btn-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-modern:hover::before {
            left: 100%;
        }
        
        /* Control Buttons matching task list */
        .control-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            border: 2px solid rgba(102, 126, 234, 0.3);
            color: #667eea;
            padding: 10px 22px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-secondary:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
            transform: translateY(-2px);
            color: #667eea;
        }
        
        /* Validation States */
        .is-invalid {
            border-color: var(--danger-color);
            background: rgba(239, 68, 68, 0.05);
        }
        
        .invalid-feedback {
            display: block;
            color: var(--danger-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .is-valid {
            border-color: var(--success-color);
            background: rgba(16, 185, 129, 0.05);
        }
        
        /* Loading States */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .btn-loading {
            position: relative;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 0.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Character Counter */
        .char-counter {
            text-align: right;
            font-size: 0.75rem;
            color: var(--gray-400);
            margin-top: 0.25rem;
        }
        
        .char-counter.warning {
            color: var(--warning-color);
        }
        
        .char-counter.danger {
            color: var(--danger-color);
        }
        
        /* Auto-save Indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 0.5rem 1rem;
            background: var(--success-color);
            color: var(--white);
            border-radius: var(--border-radius);
            font-size: 0.75rem;
            transform: translateY(-100px);
            transition: transform 0.3s ease;
            z-index: 1000;
        }
        
        .auto-save-indicator.show {
            transform: translateY(0);
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .task-header,
            .nav-container,
            .form-container {
                padding: 1rem;
            }
            
            .task-title {
                font-size: 1.5rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
            
            .btn-modern {
                justify-content: center;
            }
        }
        
        /* Field Specific Styling */
        .datetime-field {
            position: relative;
        }
        
        .datetime-field::after {
            content: '\f073';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            pointer-events: none;
        }
        
        /* Advanced Features */
        .field-tooltip {
            position: relative;
            display: inline-block;
        }
        
        .field-tooltip .tooltip-content {
            visibility: hidden;
            width: 250px;
            background: var(--gray-900);
            color: var(--white);
            text-align: left;
            border-radius: var(--border-radius);
            padding: 0.75rem;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            margin-left: -125px;
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 0.75rem;
            line-height: 1.4;
        }
        
        .field-tooltip:hover .tooltip-content {
            visibility: visible;
            opacity: 1;
        }
        
        .quick-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        
        .quick-action-btn {
            padding: 0.25rem 0.75rem;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 9999px;
            font-size: 0.75rem;
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-action-btn:hover {
            background: var(--gray-200);
            color: var(--gray-800);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="tasks-header">
            <div class="content">
                <h1 class="task-title">
                    <i class="fas fa-plus-circle"></i>
                    {% if form.instance.pk %}Edit Task{% else %}Create New Task{% endif %}
                </h1>
                <p class="task-subtitle">
                    {% if form.instance.pk %}
                        Update task details and assignments
                    {% else %}
                        Define a new task for your labor management system
                    {% endif %}
                </p>
            </div>
        </div>
        
        <!-- Navigation -->
        <div class="nav-container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'labor:dashboard' %}">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'labor:task_list' %}">
                            <i class="fas fa-tasks"></i> Tasks
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        {% if form.instance.pk %}Edit Task{% else %}New Task{% endif %}
                    </li>
                </ol>
            </nav>
        </div>
        
        <!-- Form Container -->
        <div class="form-container">
            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="progress-bar-custom">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="progress-text" id="progressText">Complete the form to create your task</div>
            </div>
            
            <!-- Auto-save Indicator -->
            <div class="auto-save-indicator" id="autoSaveIndicator">
                <i class="fas fa-check-circle"></i> Changes saved
            </div>
            
            <form method="post" id="taskForm" novalidate>
                {% csrf_token %}
                
                <!-- Basic Information Section -->
                <div class="enhanced-card" data-section="basic">
                    <div class="section-header">
                        <i class="fas fa-info-circle"></i>
                        <h3>Basic Information</h3>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="{{ form.title.id_for_label }}" class="form-label required">
                                    <i class="fas fa-heading"></i> Task Title
                                    <span class="field-tooltip">
                                        <i class="fas fa-question-circle" style="color: var(--gray-400);"></i>
                                        <div class="tooltip-content">
                                            Enter a clear, action-oriented title that describes what needs to be done. Good examples: "Clean Pond A filters", "Test water quality in Pond B", "Feed shrimp in Section C"
                                        </div>
                                    </span>
                                </label>
                                {{ form.title }}
                                <div class="char-counter" data-field="title" data-max="200">0/200</div>
                                {% if form.title.errors %}
                                    <div class="invalid-feedback">{{ form.title.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.title.help_text }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="{{ form.description.id_for_label }}" class="form-label required">
                                    <i class="fas fa-file-alt"></i> Description
                                </label>
                                {{ form.description }}
                                <div class="char-counter" data-field="description" data-max="1000">0/1000</div>
                                {% if form.description.errors %}
                                    <div class="invalid-feedback">{{ form.description.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.description.help_text }}</div>
                                
                                <!-- Quick Actions for Description -->
                                <div class="quick-actions">
                                    <button type="button" class="quick-action-btn" onclick="insertText('description', 'Materials needed: ')">
                                        <i class="fas fa-tools"></i> Add Materials
                                    </button>
                                    <button type="button" class="quick-action-btn" onclick="insertText('description', 'Safety requirements: ')">
                                        <i class="fas fa-shield-alt"></i> Add Safety
                                    </button>
                                    <button type="button" class="quick-action-btn" onclick="insertText('description', 'Expected outcome: ')">
                                        <i class="fas fa-bullseye"></i> Add Outcome
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Assignment & Location Section -->
                <div class="enhanced-card" data-section="assignment">
                    <div class="section-header">
                        <i class="fas fa-map-marker-alt"></i>
                        <h3>Assignment & Location</h3>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.pond.id_for_label }}" class="form-label">
                                    <i class="fas fa-water"></i> Pond
                                </label>
                                {{ form.pond }}
                                {% if form.pond.errors %}
                                    <div class="invalid-feedback">{{ form.pond.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.pond.help_text }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.assigned_to.id_for_label }}" class="form-label">
                                    <i class="fas fa-user"></i> Assigned Worker
                                </label>
                                {{ form.assigned_to }}
                                {% if form.assigned_to.errors %}
                                    <div class="invalid-feedback">{{ form.assigned_to.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.assigned_to.help_text }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="{{ form.assigned_team.id_for_label }}" class="form-label">
                                    <i class="fas fa-users"></i> Assigned Team
                                </label>
                                {{ form.assigned_team }}
                                {% if form.assigned_team.errors %}
                                    <div class="invalid-feedback">{{ form.assigned_team.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.assigned_team.help_text }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Location Notes (Custom Field) -->
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="id_location_notes" class="form-label">
                                    <i class="fas fa-map-pin"></i> Location Notes
                                </label>
                                {{ form.location_notes }}
                                <div class="form-text">{{ form.location_notes.help_text }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Task Details Section -->
                <div class="enhanced-card" data-section="details">
                    <div class="section-header">
                        <i class="fas fa-cogs"></i>
                        <h3>Task Details</h3>
                    </div>
                      <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.task_type.id_for_label }}" class="form-label">
                                    <i class="fas fa-tags"></i> Task Type
                                </label>
                                {{ form.task_type }}
                                {% if form.task_type.errors %}
                                    <div class="invalid-feedback">{{ form.task_type.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.task_type.help_text }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.priority.id_for_label }}" class="form-label required">
                                    <i class="fas fa-exclamation-triangle"></i> Priority
                                </label>
                                {{ form.priority }}
                                {% if form.priority.errors %}
                                    <div class="invalid-feedback">{{ form.priority.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.priority.help_text }}</div>
                                
                                <!-- Priority Guide -->
                                <div style="margin-top: 0.5rem;">
                                    <div class="priority-indicator priority-low">
                                        <i class="fas fa-circle"></i> Low - Routine maintenance
                                    </div>
                                    <div class="priority-indicator priority-medium" style="margin-top: 0.25rem;">
                                        <i class="fas fa-circle"></i> Medium - Standard operations
                                    </div>
                                    <div class="priority-indicator priority-high" style="margin-top: 0.25rem;">
                                        <i class="fas fa-circle"></i> High - Important tasks
                                    </div>
                                    <div class="priority-indicator priority-urgent" style="margin-top: 0.25rem;">
                                        <i class="fas fa-circle"></i> Urgent - Critical issues
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    <i class="fas fa-tasks"></i> Status
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">{{ form.status.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">Current status of the task</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.estimated_hours.id_for_label }}" class="form-label">
                                    <i class="fas fa-clock"></i> Estimated Hours
                                </label>
                                {{ form.estimated_hours }}
                                {% if form.estimated_hours.errors %}
                                    <div class="invalid-feedback">{{ form.estimated_hours.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.estimated_hours.help_text }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.due_date.id_for_label }}" class="form-label">
                                    <i class="fas fa-calendar-alt"></i> Due Date
                                </label>
                                <div class="datetime-field">
                                    {{ form.due_date }}
                                </div>
                                {% if form.due_date.errors %}
                                    <div class="invalid-feedback">{{ form.due_date.errors.0 }}</div>
                                {% endif %}
                                <div class="form-text">{{ form.due_date.help_text }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Additional Requirements Section -->
                <div class="enhanced-card" data-section="requirements">
                    <div class="section-header">
                        <i class="fas fa-list-check"></i>
                        <h3>Additional Requirements</h3>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id_required_skills" class="form-label">
                                    <i class="fas fa-graduation-cap"></i> Required Skills
                                </label>
                                {{ form.required_skills }}
                                <div class="form-text">{{ form.required_skills.help_text }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="id_safety_requirements" class="form-label">
                                    <i class="fas fa-hard-hat"></i> Safety Requirements
                                </label>
                                {{ form.safety_requirements }}
                                <div class="form-text">{{ form.safety_requirements.help_text }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button type="submit" class="control-btn" id="submitBtn">
                        <i class="fas fa-save"></i>
                        {% if form.instance.pk %}Update Task{% else %}Create Task{% endif %}
                    </button>
                    <a href="{% url 'labor:task_list' %}" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    {% if form.instance.pk %}
                    <button type="button" class="btn-secondary" onclick="duplicateTask()">
                        <i class="fas fa-copy"></i> Duplicate
                    </button>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form Enhancement JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('taskForm');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const submitBtn = document.getElementById('submitBtn');
            const autoSaveIndicator = document.getElementById('autoSaveIndicator');
            
            // Required fields for progress calculation
            const requiredFields = ['title', 'description', 'priority'];
            
            // Character counters
            setupCharacterCounters();
            
            // Form validation
            setupFormValidation();
            
            // Progress tracking
            updateProgress();
            
            // Auto-save (simulated)
            let autoSaveTimeout;
            
            // Field change listeners
            form.addEventListener('input', function(e) {
                updateProgress();
                validateField(e.target);
                
                // Simulate auto-save
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(showAutoSave, 2000);
            });
            
            form.addEventListener('change', function(e) {
                updateProgress();
                validateField(e.target);
            });
            
            // Form submission
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }
                
                submitBtn.classList.add('btn-loading');
                submitBtn.disabled = true;
            });
            
            function setupCharacterCounters() {
                document.querySelectorAll('[data-field]').forEach(counter => {
                    const fieldName = counter.dataset.field;
                    const maxLength = parseInt(counter.dataset.max);
                    const field = document.getElementById(`id_${fieldName}`);
                    
                    if (field) {
                        function updateCounter() {
                            const length = field.value.length;
                            counter.textContent = `${length}/${maxLength}`;
                            
                            if (length > maxLength * 0.9) {
                                counter.classList.add('danger');
                                counter.classList.remove('warning');
                            } else if (length > maxLength * 0.7) {
                                counter.classList.add('warning');
                                counter.classList.remove('danger');
                            } else {
                                counter.classList.remove('warning', 'danger');
                            }
                        }
                        
                        field.addEventListener('input', updateCounter);
                        updateCounter(); // Initial update
                    }
                });
            }
            
            function setupFormValidation() {
                // Add real-time validation
                form.querySelectorAll('input, select, textarea').forEach(field => {
                    field.addEventListener('blur', () => validateField(field));
                });
            }
            
            function validateField(field) {
                const value = field.value.trim();
                const isRequired = field.hasAttribute('required') || 
                                 field.closest('.form-group').querySelector('.required');
                
                // Remove previous validation classes
                field.classList.remove('is-valid', 'is-invalid');
                
                if (isRequired && !value) {
                    field.classList.add('is-invalid');
                    return false;
                } else if (value) {
                    field.classList.add('is-valid');
                    return true;
                }
                
                return true;
            }
            
            function validateForm() {
                let isValid = true;
                
                requiredFields.forEach(fieldName => {
                    const field = document.getElementById(`id_${fieldName}`);
                    if (field && !validateField(field)) {
                        isValid = false;
                    }
                });
                
                return isValid;
            }
            
            function updateProgress() {
                let completedFields = 0;
                
                requiredFields.forEach(fieldName => {
                    const field = document.getElementById(`id_${fieldName}`);
                    if (field && field.value.trim()) {
                        completedFields++;
                    }
                });
                
                const progress = (completedFields / requiredFields.length) * 100;
                progressFill.style.width = `${progress}%`;
                
                if (progress === 100) {
                    progressText.textContent = 'Ready to submit!';
                    progressText.style.color = 'var(--success-color)';
                } else {
                    const remaining = requiredFields.length - completedFields;
                    progressText.textContent = `${remaining} required field${remaining !== 1 ? 's' : ''} remaining`;
                    progressText.style.color = 'var(--gray-600)';
                }
            }
            
            function showAutoSave() {
                autoSaveIndicator.classList.add('show');
                setTimeout(() => {
                    autoSaveIndicator.classList.remove('show');
                }, 2000);
            }
        });
        
        // Utility functions
        function insertText(fieldId, text) {
            const field = document.getElementById(`id_${fieldId}`);
            if (field) {
                const cursorPos = field.selectionStart;
                const textBefore = field.value.substring(0, cursorPos);
                const textAfter = field.value.substring(cursorPos);
                
                field.value = textBefore + text + textAfter;
                field.focus();
                field.setSelectionRange(cursorPos + text.length, cursorPos + text.length);
                
                // Trigger input event for character counter
                field.dispatchEvent(new Event('input'));
            }
        }
        
        function duplicateTask() {
            if (confirm('This will create a copy of the current task. Continue?')) {
                // Clear the ID to create a new task
                const form = document.getElementById('taskForm');
                const actionUrl = form.action.replace(/\/\d+\/update\/$/, '/create/');
                form.action = actionUrl;
                
                // Update the title to indicate it's a copy
                const titleField = document.getElementById('id_title');
                if (titleField && !titleField.value.includes('(Copy)')) {
                    titleField.value = titleField.value + ' (Copy)';
                }
                
                form.submit();
            }
        }
        
        // Set default due date to tomorrow
        document.addEventListener('DOMContentLoaded', function() {
            const dueDateField = document.getElementById('id_due_date');
            if (dueDateField && !dueDateField.value) {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(9, 0, 0, 0); // Set to 9 AM
                
                const isoString = tomorrow.toISOString().slice(0, 16);
                dueDateField.value = isoString;
            }
        });
    </script>
</body>
</html>
