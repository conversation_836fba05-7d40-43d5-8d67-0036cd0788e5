{% extends "base.html" %}
{% load static %}

{% block title %}Enhanced Cumulative Map with Labor & Weather{% endblock %}

{% block extra_css %}
<style>
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        margin: 0;
        padding: 0;
    }

    .dashboard-container {
        min-height: 100vh;
        padding: 20px;
    }

    .header {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .map-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        margin-bottom: 20px;
        min-height: 580px;
    }

    #map {
        width: 100% !important;
        height: 500px !important;
        min-height: 500px !important;
        border-radius: 15px;
        border: 2px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }

    .loading {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 500px !important;
        color: #6b7280;
        font-size: 1.2rem;
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-radius: 15px;
    }

    .loading i {
        font-size: 3rem;
        margin-bottom: 20px;
        animation: spin 2s linear infinite;
        color: #3b82f6;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 500px;
        color: #ef4444;
        background: linear-gradient(135deg, #fef2f2, #fee2e2);
        border: 2px solid #fecaca;
        border-radius: 15px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="header">
        <h1 style="margin: 0; color: #1f2937; font-size: 2.2rem;">
            <i class="fas fa-globe-americas"></i>
            Enhanced Cumulative Map Dashboard
        </h1>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <h3 style="margin: 0 0 20px 0; color: #1f2937;">
            <i class="fas fa-map-marked-alt"></i>
            Live Tracking Map
        </h3>

        <div id="map" class="loading">
            <div>
                <i class="fas fa-spinner fa-spin"></i>
                <div>Loading Enhanced Map...</div>
                <div style="font-size: 0.9rem; color: #9ca3af; margin-top: 10px;">
                    Initializing Google Maps...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log('🗺️ Starting Enhanced Map Dashboard...');
    
    // CRITICAL: Define callback function FIRST before any other code
    window.initMap = function() {
        console.log('✅ Google Maps API loaded, creating enhanced map...');
        
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            console.error('❌ Map container not found');
            return;
        }
        
        // Set container size
        mapElement.style.height = '500px';
        mapElement.style.width = '100%';
        mapElement.style.display = 'block';
        
        try {
            // Create map with basic settings
            const map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: 13.0827, lng: 80.2707 },
                mapTypeId: 'roadmap'
            });
            
            console.log('✅ Enhanced map created successfully');
            
            // Remove loading state
            mapElement.classList.remove('loading');
            
            // Add a test marker
            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Enhanced Map Test - Chennai'
            });
            
        } catch (error) {
            console.error('❌ Error creating map:', error);
            mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error loading map: ' + error.message + '</div>';
        }
    };
    
    // Error handler
    window.handleMapError = function() {
        console.error('❌ Google Maps API failed to load');
        const mapElement = document.getElementById('map');
        if (mapElement) {
            mapElement.classList.remove('loading');
            mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Failed to load Google Maps</div>';
        }
    };
    
    // Verify functions are defined
    console.log('🔍 initMap function type:', typeof window.initMap);
    console.log('🔍 handleMapError function type:', typeof window.handleMapError);
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %>
