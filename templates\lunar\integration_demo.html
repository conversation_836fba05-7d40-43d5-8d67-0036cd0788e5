<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lunar-Labor-Alerts Integration Demo - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    .integration-demo-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .integration-demo-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: demo-sweep 4s infinite;
    }
    
    @keyframes demo-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .integration-flow {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 40px 0;
        gap: 30px;
        flex-wrap: wrap;
    }
    
    .flow-module {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        min-width: 200px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .flow-module:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
    }
    
    .flow-module::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }
    
    .flow-module:hover::before {
        left: 100%;
    }
    
    .flow-arrow {
        font-size: 3em;
        color: #667eea;
        animation: pulse-arrow 2s infinite;
    }
    
    @keyframes pulse-arrow {
        0%, 100% { transform: scale(1); opacity: 0.7; }
        50% { transform: scale(1.2); opacity: 1; }
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin: 40px 0;
    }
    
    .feature-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
    }
    
    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }
    
    .feature-icon {
        font-size: 4em;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .integration-example {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 20px;
        padding: 30px;
        margin: 30px 0;
        border-left: 5px solid #667eea;
    }
    
    .example-scenario {
        background: rgba(255,255,255,0.8);
        border-radius: 15px;
        padding: 25px;
        margin: 20px 0;
        border: 1px solid rgba(102, 126, 234, 0.2);
    }
    
    .scenario-step {
        display: flex;
        align-items: center;
        margin: 15px 0;
        padding: 15px;
        background: rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        transition: all 0.3s ease;
    }
    
    .scenario-step:hover {
        background: rgba(102, 126, 234, 0.2);
        transform: translateX(10px);
    }
    
    .step-number {
        background: #667eea;
        color: white;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 20px;
        flex-shrink: 0;
    }
    
    .benefits-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .benefit-item {
        background: linear-gradient(45deg, #28a745, #20c997);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .benefit-item:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(40, 167, 69, 0.4);
    }
    
    .demo-button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        font-size: 1.1em;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        text-decoration: none;
        display: inline-block;
    }
    
    .demo-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.6);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/" class="quick-action">
                    <i class="fas fa-calendar"></i>
                    Lunar Dashboard
                </a>
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/full-calendar/" class="quick-action">
                    <i class="fas fa-calendar-alt"></i>
                    Full Calendar
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid">
    <!-- Integration Demo Header -->
    <div class="integration-demo-header">
        <div class="position-relative">
            <h1><i class="fas fa-link me-3"></i>Lunar-Labor-Alerts Integration</h1>
            <p class="lead mb-0">Comprehensive module integration for intelligent shrimp farm management</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🌙 LUNAR CALENDAR</span>
                <span class="badge bg-light text-dark me-2">👥 LABOR MANAGEMENT</span>
                <span class="badge bg-light text-dark me-2">🚨 SMART ALERTS</span>
                <span class="badge bg-success text-white ms-3">🔗 FULLY INTEGRATED</span>
            </div>
        </div>
    </div>

    <!-- Integration Flow -->
    <div class="integration-flow">
        <div class="flow-module">
            <div class="position-relative">
                <i class="fas fa-moon fa-3x mb-3"></i>
                <h4>LUNAR MODULE</h4>
                <p>Astronomical calculations and lunar phase tracking</p>
            </div>
        </div>
        <div class="flow-arrow">⟷</div>
        <div class="flow-module">
            <div class="position-relative">
                <i class="fas fa-users fa-3x mb-3"></i>
                <h4>LABOR MODULE</h4>
                <p>Workforce management and task scheduling</p>
            </div>
        </div>
        <div class="flow-arrow">⟷</div>
        <div class="flow-module">
            <div class="position-relative">
                <i class="fas fa-bell fa-3x mb-3"></i>
                <h4>ALERTS MODULE</h4>
                <p>Intelligent notifications and alerts</p>
            </div>
        </div>
    </div>

    <!-- Integration Features -->
    <div class="feature-grid">
        <div class="feature-card">
            <div class="feature-icon"><i class="fas fa-calendar-alt"></i></div>
            <h3>Lunar-Based Scheduling</h3>
            <p>Automatically create work schedules based on lunar phases for optimal shrimp farming operations.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Feeding schedules during Astami/Amavasya</li>
                <li><i class="fas fa-check text-success me-2"></i>Harvesting optimization during Pournami</li>
                <li><i class="fas fa-check text-success me-2"></i>Treatment timing based on lunar cycles</li>
                <li><i class="fas fa-check text-success me-2"></i>Maintenance scheduling during favorable phases</li>
            </ul>
        </div>

        <div class="feature-card">
            <div class="feature-icon"><i class="fas fa-users-cog"></i></div>
            <h3>Intelligent Worker Assignment</h3>
            <p>Automatically assign workers to lunar-optimized tasks based on skill levels and availability.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Skill-based task assignment</li>
                <li><i class="fas fa-check text-success me-2"></i>Workload optimization</li>
                <li><i class="fas fa-check text-success me-2"></i>Priority-based scheduling</li>
                <li><i class="fas fa-check text-success me-2"></i>Real-time availability tracking</li>
            </ul>
        </div>

        <div class="feature-card">
            <div class="feature-icon"><i class="fas fa-bell-exclamation"></i></div>
            <h3>Smart Alert System</h3>
            <p>Generate intelligent alerts based on lunar phases and operational requirements.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Phase-specific notifications</li>
                <li><i class="fas fa-check text-success me-2"></i>Treatment reminders</li>
                <li><i class="fas fa-check text-success me-2"></i>Worker assignment alerts</li>
                <li><i class="fas fa-check text-success me-2"></i>Schedule conflict detection</li>
            </ul>
        </div>

        <div class="feature-card">
            <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
            <h3>Performance Analytics</h3>
            <p>Track and analyze the effectiveness of lunar-based operations and worker performance.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Lunar correlation analysis</li>
                <li><i class="fas fa-check text-success me-2"></i>Worker efficiency metrics</li>
                <li><i class="fas fa-check text-success me-2"></i>Task completion tracking</li>
                <li><i class="fas fa-check text-success me-2"></i>ROI optimization reports</li>
            </ul>
        </div>
    </div>

    <!-- Integration Example -->
    <div class="integration-example">
        <h3><i class="fas fa-lightbulb me-2"></i>Integration Example: Amavasya Operations</h3>
        <p class="lead">How the integrated system handles a critical lunar phase</p>

        <div class="example-scenario">
            <h5>Scenario: Amavasya (New Moon) - June 15, 2025</h5>
            
            <div class="scenario-step">
                <div class="step-number">1</div>
                <div>
                    <strong>Lunar Module Detection:</strong> System identifies Amavasya phase 24 hours in advance
                </div>
            </div>
            
            <div class="scenario-step">
                <div class="step-number">2</div>
                <div>
                    <strong>Schedule Generation:</strong> Automatically creates high-priority feeding and treatment schedules
                </div>
            </div>
            
            <div class="scenario-step">
                <div class="step-number">3</div>
                <div>
                    <strong>Worker Assignment:</strong> Assigns experienced workers to critical tasks based on skill requirements
                </div>
            </div>
            
            <div class="scenario-step">
                <div class="step-number">4</div>
                <div>
                    <strong>Alert Generation:</strong> Sends notifications to supervisors and farm managers about critical phase
                </div>
            </div>
            
            <div class="scenario-step">
                <div class="step-number">5</div>
                <div>
                    <strong>Monitoring:</strong> Tracks task completion and worker performance during the lunar phase
                </div>
            </div>
        </div>
    </div>

    <!-- Benefits -->
    <h3 class="text-center mb-4">Integration Benefits</h3>
    <div class="benefits-grid">
        <div class="benefit-item">
            <i class="fas fa-clock fa-2x mb-3"></i>
            <h5>Time Optimization</h5>
            <p>Reduce manual scheduling by 80%</p>
        </div>
        <div class="benefit-item">
            <i class="fas fa-chart-up fa-2x mb-3"></i>
            <h5>Productivity Increase</h5>
            <p>25% improvement in task efficiency</p>
        </div>
        <div class="benefit-item">
            <i class="fas fa-bell fa-2x mb-3"></i>
            <h5>Proactive Alerts</h5>
            <p>90% reduction in missed operations</p>
        </div>
        <div class="benefit-item">
            <i class="fas fa-users fa-2x mb-3"></i>
            <h5>Worker Satisfaction</h5>
            <p>Better work-life balance</p>
        </div>
        <div class="benefit-item">
            <i class="fas fa-fish fa-2x mb-3"></i>
            <h5>Shrimp Health</h5>
            <p>Improved survival rates</p>
        </div>
        <div class="benefit-item">
            <i class="fas fa-dollar-sign fa-2x mb-3"></i>
            <h5>Cost Reduction</h5>
            <p>15% operational cost savings</p>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mt-5">
        <h2>Experience the Integration</h2>
        <p class="lead">Explore the connected lunar-labor-alerts system</p>
        <div class="mt-4">
            <a href="{% url 'lunar:lunar_calendar' %}" class="demo-button me-3">
                <i class="fas fa-moon me-2"></i>Lunar Calendar
            </a>
            <a href="{% url 'lunar:full_lunar_calendar' %}" class="demo-button me-3">
                <i class="fas fa-calendar-alt me-2"></i>Full Calendar
            </a>
            <a href="{% url 'lunar:enhanced_lunar_dashboard' %}" class="demo-button">
                <i class="fas fa-rocket me-2"></i>Enhanced Dashboard
            </a>
        </div>
        
        <div class="mt-4">
            <h5>API Endpoints for Integration:</h5>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="bg-dark text-light p-3 rounded">
                        <code>
                            GET /lunar/api/calendar/ - Lunar calendar events<br>
                            GET /lunar/api/worker-assignments/ - Worker assignments<br>
                            GET /lunar/api/alerts/ - Lunar alerts<br>
                            POST /lunar/sync/ - Synchronize systems
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate feature cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.feature-card, .benefit-item').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Add click effects to demo buttons
    document.querySelectorAll('.demo-button').forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
});

// Add CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .demo-button {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
</script>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
