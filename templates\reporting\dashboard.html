{% extends "base.html" %}
{% load static %}

{% block title %}Reporting Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .reporting-header {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }
    
    .report-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border-left: 4px solid #059669;
    }
    
    .report-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        color: #6b7280;
        font-size: 0.9rem;
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .report-status {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-completed {
        background: #dcfce7;
        color: #166534;
    }
    
    .status-generating {
        background: #fef3c7;
        color: #92400e;
    }
    
    .status-scheduled {
        background: #dbeafe;
        color: #1e40af;
    }
    
    .report-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
        transition: background-color 0.2s ease;
    }
    
    .report-item:hover {
        background-color: #f9fafb;
    }
    
    .report-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.2rem;
        background: #dcfce7;
        color: #166534;
    }
    
    .btn-reporting {
        background: linear-gradient(135deg, #059669 0%, #047857 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-reporting:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(5, 150, 105, 0.4);
        color: white;
    }
    
    .reporting-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    @media (max-width: 768px) {
        .reporting-grid {
            grid-template-columns: 1fr;
        }
        
        .metric-value {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="reporting-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 mb-2">📊 Reporting Dashboard</h1>
                <p class="lead mb-0">Comprehensive reporting system with automated generation and scheduling</p>
                <div class="mt-3">
                    <span class="report-status status-completed">
                        <i class="fas fa-check-circle me-1"></i>
                        System Active
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <button class="btn btn-light" onclick="refreshReportData()">
                        <i class="fas fa-sync me-2"></i>Refresh
                    </button>
                    <a href="{% url 'reporting:create_template' %}" class="btn btn-outline-light">
                        <i class="fas fa-plus me-2"></i>New Report
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Reporting Metrics Grid -->
    <div class="reporting-grid">
        <div class="report-card">
            <div class="metric-value">{{ stats.total_templates|default:0 }}</div>
            <div class="metric-label">Report Templates</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-file-contract me-1"></i>
                    Available templates
                </small>
            </div>
        </div>
        
        <div class="report-card">
            <div class="metric-value">{{ stats.total_reports|default:0 }}</div>
            <div class="metric-label">Generated Reports</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-download me-1"></i>
                    Total generated
                </small>
            </div>
        </div>
        
        <div class="report-card">
            <div class="metric-value">{{ stats.scheduled_reports|default:0 }}</div>
            <div class="metric-label">Scheduled Reports</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-calendar-alt me-1"></i>
                    Active schedules
                </small>
            </div>
        </div>
        
        <div class="report-card">
            <div class="metric-value">{{ stats.reports_this_month|default:0 }}</div>
            <div class="metric-label">Reports This Month</div>
            <div class="mt-2">
                <small class="text-muted">
                    <i class="fas fa-chart-bar me-1"></i>
                    Monthly activity
                </small>
            </div>
        </div>
    </div>

    <!-- Recent Reports and Scheduled Reports -->
    <div class="row">
        <div class="col-lg-6">
            <div class="report-card">
                <h5 class="mb-3">
                    <i class="fas fa-file-alt me-2"></i>
                    Recent Reports
                </h5>
                {% if recent_reports %}
                    {% for report in recent_reports %}
                    <div class="report-item">
                        <div class="report-icon">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ report.title }}</h6>
                            <p class="mb-1 text-muted">{{ report.format|upper }} • {{ report.file_size|filesizeformat|default:'N/A' }}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ report.generated_at|timesince }} ago
                            </small>
                        </div>
                        <div>
                            <span class="report-status status-{{ report.status }}">
                                {{ report.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent reports</p>
                    </div>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{% url 'reporting:generated_reports' %}" class="btn btn-reporting btn-sm">
                        View All Reports
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="report-card">
                <h5 class="mb-3">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Scheduled Reports
                </h5>
                {% if scheduled_reports %}
                    {% for schedule in scheduled_reports %}
                    <div class="report-item">
                        <div class="report-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">{{ schedule.name }}</h6>
                            <p class="mb-1 text-muted">{{ schedule.get_frequency_display }}</p>
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                Next run: {{ schedule.next_run|date:'M d, Y H:i' }}
                            </small>
                        </div>
                        <div>
                            <span class="report-status status-{{ schedule.status }}">
                                {{ schedule.get_status_display }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No scheduled reports</p>
                    </div>
                {% endif %}
                
                <div class="text-center mt-3">
                    <a href="{% url 'reporting:scheduled_reports' %}" class="btn btn-reporting btn-sm">
                        View All Schedules
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="report-card">
                <h5 class="mb-3">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Reporting Actions
                </h5>
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reporting:templates' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-file-contract d-block mb-2"></i>
                            Report Templates
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reporting:create_template' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus d-block mb-2"></i>
                            Create Template
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reporting:widgets' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-puzzle-piece d-block mb-2"></i>
                            Report Widgets
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'reporting:dashboards' %}" class="btn btn-outline-danger w-100">
                            <i class="fas fa-th-large d-block mb-2"></i>
                            Custom Dashboards
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshReportData() {
        fetch('/reporting/api/data/')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error refreshing report data:', error);
        });
    }
</script>
{% endblock %}
