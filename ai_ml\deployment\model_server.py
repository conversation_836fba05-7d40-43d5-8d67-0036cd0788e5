"""
ML Model Serving API
FastAPI server for serving deployed ML models
"""

import os
import time
import logging
import joblib
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
import boto3
from datetime import datetime
import asyncio
import aioredis

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
MODEL_TYPE = os.getenv("MODEL_TYPE", "WATER_QUALITY_PREDICTION")
MODEL_URL = os.getenv("MODEL_URL", "")
MODEL_ID = os.getenv("MODEL_ID", "")
ENVIRONMENT = os.getenv("ENVIRONMENT", "production")
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379")

# Pydantic models for API
class PredictionRequest(BaseModel):
    features: List[float] = Field(..., description="Input features for prediction")
    model_version: Optional[str] = Field(None, description="Specific model version to use")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class PredictionResponse(BaseModel):
    prediction: List[float] = Field(..., description="Model prediction")
    confidence: float = Field(..., description="Prediction confidence score")
    model_version: str = Field(..., description="Model version used")
    processing_time: float = Field(..., description="Processing time in seconds")
    timestamp: str = Field(..., description="Prediction timestamp")

class HealthResponse(BaseModel):
    status: str = Field(..., description="Service health status")
    model_loaded: bool = Field(..., description="Whether model is loaded")
    model_type: str = Field(..., description="Type of loaded model")
    uptime: float = Field(..., description="Service uptime in seconds")
    version: str = Field(..., description="Service version")

class ModelServer:
    """
    ML Model serving server with caching and monitoring
    """
    
    def __init__(self):
        self.model = None
        self.scaler = None
        self.model_metadata = {}
        self.start_time = time.time()
        self.prediction_count = 0
        self.error_count = 0
        self.redis_client = None
        
        # Initialize FastAPI app
        self.app = FastAPI(
            title="Shrimp Farm ML Model Server",
            description="High-performance ML model serving API",
            version="1.0.0"
        )
        
        # Add CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Setup routes
        self._setup_routes()
        
        # Load model on startup
        asyncio.create_task(self._startup())
    
    async def _startup(self):
        """Initialize server on startup"""
        try:
            # Initialize Redis connection
            self.redis_client = await aioredis.from_url(REDIS_URL)
            logger.info("Connected to Redis")
            
            # Load model
            await self._load_model()
            
            logger.info("Model server startup completed")
            
        except Exception as e:
            logger.error(f"Startup failed: {e}")
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/health", response_model=HealthResponse)
        async def health_check():
            """Health check endpoint"""
            return HealthResponse(
                status="healthy" if self.model is not None else "unhealthy",
                model_loaded=self.model is not None,
                model_type=MODEL_TYPE,
                uptime=time.time() - self.start_time,
                version="1.0.0"
            )
        
        @self.app.post("/predict", response_model=PredictionResponse)
        async def predict(request: PredictionRequest, background_tasks: BackgroundTasks):
            """Make prediction using loaded model"""
            start_time = time.time()
            
            try:
                if self.model is None:
                    raise HTTPException(status_code=503, detail="Model not loaded")
                
                # Validate input features
                if not request.features:
                    raise HTTPException(status_code=400, detail="Features cannot be empty")
                
                # Check cache first
                cache_key = f"prediction:{MODEL_TYPE}:{hash(tuple(request.features))}"
                cached_result = await self._get_cached_prediction(cache_key)
                
                if cached_result:
                    logger.info("Returning cached prediction")
                    return cached_result
                
                # Prepare features
                features = np.array([request.features])
                
                # Apply scaling if available
                if self.scaler:
                    features = self.scaler.transform(features)
                
                # Make prediction
                prediction = self.model.predict(features)
                
                # Calculate confidence (simplified)
                confidence = self._calculate_confidence(features, prediction)
                
                # Prepare response
                response = PredictionResponse(
                    prediction=prediction.tolist(),
                    confidence=confidence,
                    model_version=self.model_metadata.get("version", "1.0.0"),
                    processing_time=time.time() - start_time,
                    timestamp=datetime.now().isoformat()
                )
                
                # Cache result
                background_tasks.add_task(self._cache_prediction, cache_key, response)
                
                # Update metrics
                self.prediction_count += 1
                
                # Log prediction
                background_tasks.add_task(self._log_prediction, request, response)
                
                return response
                
            except Exception as e:
                self.error_count += 1
                logger.error(f"Prediction failed: {e}")
                raise HTTPException(status_code=500, detail=str(e))
        
        @self.app.get("/metrics")
        async def get_metrics():
            """Get server metrics"""
            uptime = time.time() - self.start_time
            
            return {
                "uptime": uptime,
                "predictions_total": self.prediction_count,
                "errors_total": self.error_count,
                "success_rate": (self.prediction_count / (self.prediction_count + self.error_count)) * 100 
                               if (self.prediction_count + self.error_count) > 0 else 0,
                "model_type": MODEL_TYPE,
                "model_loaded": self.model is not None,
                "environment": ENVIRONMENT
            }
        
        @self.app.post("/reload")
        async def reload_model():
            """Reload model (for updates)"""
            try:
                await self._load_model()
                return {"status": "success", "message": "Model reloaded successfully"}
            except Exception as e:
                logger.error(f"Model reload failed: {e}")
                raise HTTPException(status_code=500, detail=str(e))
    
    async def _load_model(self):
        """Load ML model from storage"""
        try:
            logger.info(f"Loading model from {MODEL_URL}")
            
            if MODEL_URL.startswith("s3://"):
                # Load from S3
                await self._load_model_from_s3()
            elif MODEL_URL.startswith("file://"):
                # Load from local file
                model_path = MODEL_URL.replace("file://", "")
                self.model = joblib.load(model_path)
                
                # Try to load scaler
                scaler_path = model_path.replace("model.joblib", "scaler.joblib")
                if os.path.exists(scaler_path):
                    self.scaler = joblib.load(scaler_path)
                    logger.info("Scaler loaded successfully")
            else:
                raise ValueError(f"Unsupported model URL format: {MODEL_URL}")
            
            # Set model metadata
            self.model_metadata = {
                "type": MODEL_TYPE,
                "id": MODEL_ID,
                "loaded_at": datetime.now().isoformat(),
                "version": "1.0.0"
            }
            
            logger.info("Model loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    async def _load_model_from_s3(self):
        """Load model from S3 storage"""
        try:
            # Parse S3 URL
            s3_parts = MODEL_URL.replace("s3://", "").split("/", 1)
            bucket = s3_parts[0]
            key = s3_parts[1]
            
            # Download model
            s3_client = boto3.client('s3')
            
            # Create temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                s3_client.download_fileobj(bucket, key, tmp_file)
                model_path = tmp_file.name
            
            # Load model
            self.model = joblib.load(model_path)
            
            # Try to load scaler
            scaler_key = key.replace("model.joblib", "scaler.joblib")
            try:
                with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                    s3_client.download_fileobj(bucket, scaler_key, tmp_file)
                    scaler_path = tmp_file.name
                
                self.scaler = joblib.load(scaler_path)
                logger.info("Scaler loaded from S3")
                
                # Cleanup
                os.unlink(scaler_path)
            except:
                logger.info("No scaler found in S3")
            
            # Cleanup
            os.unlink(model_path)
            
            logger.info("Model loaded from S3 successfully")
            
        except Exception as e:
            logger.error(f"Failed to load model from S3: {e}")
            raise
    
    def _calculate_confidence(self, features: np.ndarray, prediction: np.ndarray) -> float:
        """Calculate prediction confidence score"""
        try:
            # For regression models, use prediction variance
            if hasattr(self.model, 'predict_proba'):
                # Classification model
                probabilities = self.model.predict_proba(features)
                confidence = float(np.max(probabilities))
            else:
                # Regression model - simplified confidence based on feature similarity
                # In production, use more sophisticated methods
                confidence = 0.85  # Default confidence
            
            return min(max(confidence, 0.0), 1.0)  # Clamp between 0 and 1
            
        except Exception as e:
            logger.warning(f"Confidence calculation failed: {e}")
            return 0.5  # Default confidence
    
    async def _get_cached_prediction(self, cache_key: str) -> Optional[PredictionResponse]:
        """Get cached prediction result"""
        try:
            if self.redis_client:
                cached_data = await self.redis_client.get(cache_key)
                if cached_data:
                    import json
                    data = json.loads(cached_data)
                    return PredictionResponse(**data)
            return None
        except Exception as e:
            logger.warning(f"Cache retrieval failed: {e}")
            return None
    
    async def _cache_prediction(self, cache_key: str, response: PredictionResponse):
        """Cache prediction result"""
        try:
            if self.redis_client:
                import json
                data = response.dict()
                await self.redis_client.setex(
                    cache_key, 
                    300,  # 5 minutes TTL
                    json.dumps(data)
                )
        except Exception as e:
            logger.warning(f"Cache storage failed: {e}")
    
    async def _log_prediction(self, request: PredictionRequest, response: PredictionResponse):
        """Log prediction for monitoring and analytics"""
        try:
            log_data = {
                "timestamp": response.timestamp,
                "model_type": MODEL_TYPE,
                "model_id": MODEL_ID,
                "features": request.features,
                "prediction": response.prediction,
                "confidence": response.confidence,
                "processing_time": response.processing_time,
                "environment": ENVIRONMENT
            }
            
            # In production, send to logging service or analytics platform
            logger.info(f"Prediction logged: {log_data}")
            
        except Exception as e:
            logger.warning(f"Prediction logging failed: {e}")

# Create server instance
model_server = ModelServer()
app = model_server.app

if __name__ == "__main__":
    # Run server
    uvicorn.run(
        "model_server:app",
        host="0.0.0.0",
        port=8080,
        log_level="info",
        access_log=True
    )
