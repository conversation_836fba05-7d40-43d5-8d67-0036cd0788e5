<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ lunar_day.phase }} on {{ lunar_day.formatted_date }} - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/" class="quick-action">
                    <i class="fas fa-calendar"></i>
                    Lunar Calendar
                </a>
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/full-calendar/" class="quick-action">
                    <i class="fas fa-calendar-alt"></i>
                    Full Calendar
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid px-4">
    <h1 class="mt-4">
        {{ lunar_day.phase }} {{ lunar_day.moon_phase_emoji }} on {{ lunar_day.formatted_date }}
    </h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'lunar:lunar_calendar' %}">Lunar Calendar</a></li>
        <li class="breadcrumb-item active">{{ lunar_day.phase }} Details</li>
    </ol>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-info-circle me-1"></i>
                    Lunar Day Information
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h5>{{ lunar_day.phase }} {{ lunar_day.moon_phase_emoji }}</h5>
                        <p class="text-muted">{{ lunar_day.description }}</p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Date:</strong> {{ lunar_day.formatted_date }}</p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <strong>Importance:</strong> 
                                <span class="badge {% if lunar_day.importance == 'High' %}bg-danger{% elif lunar_day.importance == 'Medium' %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ lunar_day.importance }}
                                </span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="mt-3">
                        <h6>Recommendations:</h6>
                        <ul class="list-group">
                            {% for rec in lunar_day.recommendations.all %}
                            <li class="list-group-item">{{ rec.recommendation }}</li>
                            {% empty %}
                            <li class="list-group-item">No specific recommendations for this lunar day.</li>
                            {% endfor %}
                            {% if lunar_day.phase == 'Astami' or lunar_day.phase == 'Amavasya' %}
                            <li class="list-group-item list-group-item-success">
                                <i class="fas fa-flask me-2"></i> Apply <strong>probiotics</strong> to all ponds
                            </li>
                            {% elif lunar_day.phase == 'Navami' %}
                            <li class="list-group-item list-group-item-warning">
                                <i class="fas fa-vial me-2"></i> Apply <strong>minerals</strong> to all ponds
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-plus-circle me-1"></i>
                            Add New Treatment
                        </div>
                        <div>
                            <a href="{% url 'lunar:generate_treatments' pk=lunar_day.pk %}" class="btn btn-sm btn-success">
                                <i class="fas fa-magic me-1"></i> Auto-Generate for All Ponds
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.pond.id_for_label }}" class="form-label">Pond</label>
                            {{ form.pond }}
                            {% if form.pond.errors %}
                            <div class="invalid-feedback d-block">{{ form.pond.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.treatment_type.id_for_label }}" class="form-label">Treatment Type</label>
                            {{ form.treatment_type }}
                            {% if form.treatment_type.errors %}
                            <div class="invalid-feedback d-block">{{ form.treatment_type.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.quantity.id_for_label }}" class="form-label">Quantity</label>
                                    {{ form.quantity }}
                                    {% if form.quantity.errors %}
                                    <div class="invalid-feedback d-block">{{ form.quantity.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="{{ form.unit.id_for_label }}" class="form-label">Unit</label>
                                    {{ form.unit }}
                                    {% if form.unit.errors %}
                                    <div class="invalid-feedback d-block">{{ form.unit.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            {{ form.notes }}
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Schedule Treatment
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-list me-1"></i>
            Scheduled Treatments
        </div>
        <div class="card-body">
            {% if treatments %}
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Pond</th>
                            <th>Treatment Type</th>
                            <th>Quantity</th>
                            <th>Status</th>
                            <th>Applied By</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for treatment in treatments %}
                        <tr>
                            <td>
                                <a href="{% url 'ponds:pond_detail' pk=treatment.pond.id %}">
                                    {{ treatment.pond.name }}
                                </a>
                            </td>
                            <td>
                                <span class="badge {% if treatment.treatment_type == 'PROBIOTIC' %}bg-success{% elif treatment.treatment_type == 'MINERAL' %}bg-warning{% else %}bg-info{% endif %}">
                                    {{ treatment.get_treatment_type_display }}
                                </span>
                            </td>
                            <td>{{ treatment.quantity }} {{ treatment.unit }}</td>
                            <td>
                                {% if treatment.applied %}
                                <span class="badge bg-success">Applied on {{ treatment.applied_date|date:"M d, Y H:i" }}</span>
                                {% else %}
                                <span class="badge bg-secondary">Pending</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if treatment.applied_by %}
                                {{ treatment.applied_by.full_name }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ treatment.notes|linebreaksbr }}</td>
                            <td>
                                {% if not treatment.applied %}
                                <a href="{% url 'lunar:treatment_apply' pk=treatment.pk %}" class="btn btn-sm btn-success">
                                    <i class="fas fa-check me-1"></i> Apply
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>            </div>
            {% else %}
            <div class="alert alert-info">
                No treatments scheduled for this lunar day yet.
            </div>
            {% endif %}
        </div>
    </div>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>