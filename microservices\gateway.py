"""
API Gateway for Microservices
Handles routing, load balancing, authentication, and rate limiting
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from django.http import JsonResponse, HttpResponse
from django.views import View
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.cache import cache
import jwt
from datetime import datetime, timedelta

from .config import service_registry, ServiceConfig

logger = logging.getLogger(__name__)

@dataclass
class RouteConfig:
    """Configuration for API route"""
    path_pattern: str
    service_name: str
    target_path: str = None
    methods: List[str] = None
    auth_required: bool = True
    rate_limit: int = 100  # requests per minute
    timeout: int = 30
    
    def __post_init__(self):
        if self.methods is None:
            self.methods = ["GET", "POST", "PUT", "DELETE", "PATCH"]
        if self.target_path is None:
            self.target_path = self.path_pattern

class APIGateway:
    """
    API Gateway for microservices routing and management
    """
    
    def __init__(self):
        self.routes: Dict[str, RouteConfig] = {}
        self.session: Optional[aiohttp.ClientSession] = None
        self.circuit_breakers: Dict[str, Dict] = {}
        
        # Initialize routes
        self._setup_routes()
        
        # Initialize HTTP session
        self._init_session()
    
    def _setup_routes(self):
        """Setup API routes to microservices"""
        routes = {
            # User service routes
            "/api/v1/auth/": RouteConfig(
                path_pattern="/api/v1/auth/",
                service_name="user-service",
                auth_required=False,
                rate_limit=20  # Lower rate limit for auth
            ),
            "/api/v1/users/": RouteConfig(
                path_pattern="/api/v1/users/",
                service_name="user-service",
                rate_limit=50
            ),
            
            # Pond service routes
            "/api/v1/ponds/": RouteConfig(
                path_pattern="/api/v1/ponds/",
                service_name="pond-service",
                rate_limit=100
            ),
            "/api/v1/farms/": RouteConfig(
                path_pattern="/api/v1/farms/",
                service_name="pond-service",
                rate_limit=100
            ),
            
            # Water quality service routes
            "/api/v1/water-quality/": RouteConfig(
                path_pattern="/api/v1/water-quality/",
                service_name="water-quality-service",
                rate_limit=200  # Higher for sensor data
            ),
            "/api/v1/sensors/": RouteConfig(
                path_pattern="/api/v1/sensors/",
                service_name="water-quality-service",
                rate_limit=500  # Very high for IoT data
            ),
            
            # AI/ML service routes
            "/api/v1/predictions/": RouteConfig(
                path_pattern="/api/v1/predictions/",
                service_name="ai-ml-service",
                rate_limit=50,
                timeout=60  # Longer timeout for ML operations
            ),
            "/api/v1/models/": RouteConfig(
                path_pattern="/api/v1/models/",
                service_name="ai-ml-service",
                rate_limit=20
            ),
            
            # Notification service routes
            "/api/v1/notifications/": RouteConfig(
                path_pattern="/api/v1/notifications/",
                service_name="notification-service",
                rate_limit=100
            ),
            "/api/v1/alerts/": RouteConfig(
                path_pattern="/api/v1/alerts/",
                service_name="notification-service",
                rate_limit=100
            ),
            
            # Analytics service routes
            "/api/v1/analytics/": RouteConfig(
                path_pattern="/api/v1/analytics/",
                service_name="analytics-service",
                rate_limit=50,
                timeout=45  # Longer for analytics queries
            ),
            "/api/v1/reports/": RouteConfig(
                path_pattern="/api/v1/reports/",
                service_name="analytics-service",
                rate_limit=20,
                timeout=60
            )
        }
        
        self.routes.update(routes)
        logger.info(f"Configured {len(routes)} API routes")
    
    def _init_session(self):
        """Initialize HTTP session for service communication"""
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=20,
            keepalive_timeout=30
        )
        
        timeout = aiohttp.ClientTimeout(total=60)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
    
    async def route_request(self, request_path: str, method: str, headers: Dict, 
                          body: bytes = None, query_params: Dict = None) -> Dict[str, Any]:
        """Route request to appropriate microservice"""
        
        # Find matching route
        route = self._find_route(request_path)
        if not route:
            return {
                "status": 404,
                "error": "Route not found",
                "path": request_path
            }
        
        # Check method allowed
        if method not in route.methods:
            return {
                "status": 405,
                "error": f"Method {method} not allowed",
                "allowed_methods": route.methods
            }
        
        # Check rate limiting
        if not self._check_rate_limit(request_path, route.rate_limit, headers):
            return {
                "status": 429,
                "error": "Rate limit exceeded",
                "retry_after": 60
            }
        
        # Check authentication
        if route.auth_required and not self._check_authentication(headers):
            return {
                "status": 401,
                "error": "Authentication required"
            }
        
        # Check circuit breaker
        if self._is_circuit_open(route.service_name):
            return {
                "status": 503,
                "error": "Service temporarily unavailable",
                "service": route.service_name
            }
        
        # Get service instance
        service = service_registry.get_load_balanced_service(route.service_name)
        if not service:
            return {
                "status": 503,
                "error": "Service unavailable",
                "service": route.service_name
            }
        
        # Forward request
        return await self._forward_request(service, route, method, headers, body, query_params)
    
    def _find_route(self, request_path: str) -> Optional[RouteConfig]:
        """Find matching route configuration"""
        # Simple prefix matching (in production, use more sophisticated routing)
        for pattern, route in self.routes.items():
            if request_path.startswith(pattern):
                return route
        return None
    
    def _check_rate_limit(self, path: str, limit: int, headers: Dict) -> bool:
        """Check rate limiting for request"""
        # Get client identifier (IP or user ID)
        client_id = headers.get('X-Forwarded-For', 'unknown')
        if 'Authorization' in headers:
            try:
                # Extract user ID from JWT token
                token = headers['Authorization'].replace('Bearer ', '')
                payload = jwt.decode(token, options={"verify_signature": False})
                client_id = f"user_{payload.get('user_id', client_id)}"
            except:
                pass
        
        # Check rate limit using Redis/cache
        cache_key = f"rate_limit:{path}:{client_id}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit:
            return False
        
        # Increment counter
        cache.set(cache_key, current_count + 1, 60)  # 1 minute window
        return True
    
    def _check_authentication(self, headers: Dict) -> bool:
        """Check JWT authentication"""
        auth_header = headers.get('Authorization', '')
        
        if not auth_header.startswith('Bearer '):
            return False
        
        try:
            token = auth_header.replace('Bearer ', '')
            # In production, verify with proper secret and issuer
            payload = jwt.decode(token, options={"verify_signature": False})
            
            # Check token expiration
            exp = payload.get('exp', 0)
            if exp < time.time():
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Authentication failed: {e}")
            return False
    
    def _is_circuit_open(self, service_name: str) -> bool:
        """Check if circuit breaker is open for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = {
                "failure_count": 0,
                "last_failure": 0,
                "state": "closed"  # closed, open, half-open
            }
        
        breaker = self.circuit_breakers[service_name]
        
        # If circuit is open, check if we should try again
        if breaker["state"] == "open":
            if time.time() - breaker["last_failure"] > 60:  # 1 minute timeout
                breaker["state"] = "half-open"
                return False
            return True
        
        return False
    
    async def _forward_request(self, service: ServiceConfig, route: RouteConfig, 
                             method: str, headers: Dict, body: bytes = None, 
                             query_params: Dict = None) -> Dict[str, Any]:
        """Forward request to microservice"""
        
        # Build target URL
        target_path = route.target_path
        if query_params:
            query_string = "&".join([f"{k}={v}" for k, v in query_params.items()])
            target_path += f"?{query_string}"
        
        target_url = f"{service.url}{target_path}"
        
        # Prepare headers (remove hop-by-hop headers)
        forward_headers = {k: v for k, v in headers.items() 
                          if k.lower() not in ['host', 'connection', 'upgrade']}
        forward_headers['X-Forwarded-Host'] = headers.get('Host', 'unknown')
        forward_headers['X-Gateway-Service'] = route.service_name
        
        start_time = time.time()
        
        try:
            async with self.session.request(
                method=method,
                url=target_url,
                headers=forward_headers,
                data=body,
                timeout=aiohttp.ClientTimeout(total=route.timeout)
            ) as response:
                
                response_body = await response.read()
                response_time = time.time() - start_time
                
                # Update circuit breaker on success
                self._record_success(route.service_name)
                
                # Log request
                logger.info(f"Forwarded {method} {target_path} to {service.name} "
                           f"({response.status}, {response_time:.3f}s)")
                
                return {
                    "status": response.status,
                    "headers": dict(response.headers),
                    "body": response_body,
                    "response_time": response_time,
                    "service": service.name
                }
                
        except asyncio.TimeoutError:
            self._record_failure(route.service_name)
            return {
                "status": 504,
                "error": "Gateway timeout",
                "service": service.name,
                "timeout": route.timeout
            }
            
        except Exception as e:
            self._record_failure(route.service_name)
            logger.error(f"Request forwarding failed: {e}")
            return {
                "status": 502,
                "error": "Bad gateway",
                "service": service.name,
                "details": str(e)
            }
    
    def _record_success(self, service_name: str):
        """Record successful request for circuit breaker"""
        if service_name in self.circuit_breakers:
            breaker = self.circuit_breakers[service_name]
            breaker["failure_count"] = 0
            breaker["state"] = "closed"
    
    def _record_failure(self, service_name: str):
        """Record failed request for circuit breaker"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = {
                "failure_count": 0,
                "last_failure": 0,
                "state": "closed"
            }
        
        breaker = self.circuit_breakers[service_name]
        breaker["failure_count"] += 1
        breaker["last_failure"] = time.time()
        
        # Open circuit if too many failures
        if breaker["failure_count"] >= 5:
            breaker["state"] = "open"
            logger.warning(f"Circuit breaker opened for {service_name}")
    
    def get_gateway_status(self) -> Dict[str, Any]:
        """Get gateway status and metrics"""
        return {
            "routes": len(self.routes),
            "circuit_breakers": {
                name: {
                    "state": breaker["state"],
                    "failure_count": breaker["failure_count"]
                }
                for name, breaker in self.circuit_breakers.items()
            },
            "services": {
                service.name: service_registry.get_service_status(service.name).value
                for service in service_registry.services.values()
            }
        }

# Global gateway instance
api_gateway = APIGateway()

@method_decorator(csrf_exempt, name='dispatch')
class GatewayView(View):
    """
    Django view for API Gateway
    """
    
    async def dispatch(self, request, *args, **kwargs):
        """Handle all HTTP methods through gateway"""
        
        # Extract request data
        headers = dict(request.META)
        body = request.body if hasattr(request, 'body') else None
        query_params = dict(request.GET)
        
        # Route through gateway
        result = await api_gateway.route_request(
            request_path=request.path,
            method=request.method,
            headers=headers,
            body=body,
            query_params=query_params
        )
        
        # Return response
        if result["status"] >= 400:
            return JsonResponse(
                {"error": result.get("error", "Unknown error")},
                status=result["status"]
            )
        
        # Forward successful response
        response = HttpResponse(
            result.get("body", ""),
            status=result["status"],
            content_type=result.get("headers", {}).get("content-type", "application/json")
        )
        
        # Add response headers
        for key, value in result.get("headers", {}).items():
            if key.lower() not in ['content-length', 'transfer-encoding']:
                response[key] = value
        
        return response
