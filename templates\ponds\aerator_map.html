{% extends "base.html" %}
{% load static %}

{% block title %}{{ pond.name }} - Aerator Map{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 500px;
        width: 100%;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .pond-info-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .aerator-list {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-top: 20px;
    }
    
    .aerator-item {
        padding: 10px;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        justify-content: between;
        align-items: center;
    }
    
    .aerator-item:last-child {
        border-bottom: none;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    
    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #991b1b; }
    .status-maintenance { background-color: #fef3c7; color: #92400e; }
    
    .legend {
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
    }
    
    .pond-marker { background-color: #3b82f6; }
    .aerator-active { background-color: #10b981; }
    .aerator-inactive { background-color: #ef4444; }
    .aerator-maintenance { background-color: #f59e0b; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">{{ pond.name }} - Aerator Map</h1>
                <div>
                    <a href="{% url 'ponds:pond_detail' pk=pond.id %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Pond
                    </a>
                    <a href="{% url 'ponds:aerator_management' pond_id=pond.id %}" class="btn btn-primary">
                        <i class="fas fa-cogs"></i> Manage Aerators
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Pond Info & Legend -->
        <div class="col-md-4">
            <!-- Pond Information -->
            <div class="pond-info-card">
                <h5 class="mb-3">
                    <i class="fas fa-water"></i> Pond Information
                </h5>
                <div class="row">
                    <div class="col-12">
                        <p><strong>Name:</strong> {{ pond.name }}</p>
                        <p><strong>Farm:</strong> {{ pond.farm.name|default:"No Farm" }}</p>
                        <p><strong>Status:</strong> 
                            <span class="status-badge status-{{ pond.status }}">{{ pond.status|title }}</span>
                        </p>
                        <p><strong>Size:</strong> {{ pond.size|default:"N/A" }} m²</p>
                        <p><strong>Species:</strong> {{ pond.species|default:"N/A" }}</p>
                        {% if pond.latitude and pond.longitude %}
                        <p><strong>Location:</strong> {{ pond.latitude|floatformat:6 }}, {{ pond.longitude|floatformat:6 }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="legend">
                <h6 class="mb-3">Map Legend</h6>
                <div class="legend-item">
                    <div class="legend-color pond-marker"></div>
                    <span>Pond Center</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-active"></div>
                    <span>Active Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-inactive"></div>
                    <span>Inactive Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-maintenance"></div>
                    <span>Maintenance Aerator</span>
                </div>
            </div>

            <!-- Aerator List -->
            <div class="aerator-list">
                <h6 class="mb-3">
                    <i class="fas fa-list"></i> Aerators ({{ aerators.count }})
                </h6>
                {% if aerators %}
                    {% for aerator in aerators %}
                    <div class="aerator-item">
                        <div>
                            <strong>{{ aerator.name }}</strong><br>
                            <small class="text-muted">{{ aerator.aerator_type|title }}</small>
                            {% if aerator.power_rating %}
                            <br><small class="text-muted">{{ aerator.power_rating }} HP</small>
                            {% endif %}
                        </div>
                        <div>
                            <span class="status-badge status-{{ aerator.status }}">{{ aerator.status|title }}</span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No aerators found for this pond.</p>
                    <a href="{% url 'ponds:add_aerator' pond_id=pond.id %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus"></i> Add Aerator
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Right Column - Map -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-map-marked-alt"></i> Aerator Locations
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div id="map"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let map;
    let infoWindow;
    
    // Data from Django
    const pondData = {
        id: {{ pond.id }},
        name: "{{ pond.name|escapejs }}",
        latitude: {{ pond.latitude|default:0 }},
        longitude: {{ pond.longitude|default:0 }},
        status: "{{ pond.status|escapejs }}"
    };
    
    const aeratorsData = [
        {% for aerator in aerators %}
        {
            id: {{ aerator.id }},
            name: "{{ aerator.name|escapejs }}",
            type: "{{ aerator.aerator_type|escapejs }}",
            status: "{{ aerator.status|escapejs }}",
            power_rating: {{ aerator.power_rating|default:0 }},
            latitude: {{ aerator.latitude|default:0 }},
            longitude: {{ aerator.longitude|default:0 }},
            operating_hours: {{ aerator.operating_hours|default:0 }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    
    function initMap() {
        // Initialize map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 18,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: 'hybrid'
        });
        
        infoWindow = new google.maps.InfoWindow();
        
        // Add pond marker if coordinates exist
        if (pondData.latitude && pondData.longitude) {
            const pondMarker = new google.maps.Marker({
                position: { lat: pondData.latitude, lng: pondData.longitude },
                map: map,
                title: pondData.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="35" height="35" viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="17.5" cy="17.5" r="15" fill="#3b82f6" stroke="white" stroke-width="3"/>
                            <text x="17.5" y="23" text-anchor="middle" fill="white" font-size="14" font-family="Arial">P</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(35, 35)
                }
            });
            
            const pondInfoContent = `
                <div style="max-width: 250px;">
                    <h6><strong>${pondData.name}</strong></h6>
                    <p><strong>Status:</strong> <span class="badge badge-${pondData.status === 'active' ? 'success' : 'secondary'}">${pondData.status}</span></p>
                    <p><strong>Aerators:</strong> ${aeratorsData.length}</p>
                </div>
            `;
            
            pondMarker.addListener('click', function() {
                infoWindow.setContent(pondInfoContent);
                infoWindow.open(map, pondMarker);
            });
        }
        
        // Add aerator markers
        aeratorsData.forEach(function(aerator, index) {
            if (aerator.latitude && aerator.longitude) {
                let aeratorColor = '#ef4444'; // inactive - red
                if (aerator.status === 'active') {
                    aeratorColor = '#10b981'; // active - green
                } else if (aerator.status === 'maintenance') {
                    aeratorColor = '#f59e0b'; // maintenance - yellow
                }
                
                const aeratorMarker = new google.maps.Marker({
                    position: { lat: aerator.latitude, lng: aerator.longitude },
                    map: map,
                    title: aerator.name,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="28" height="28" viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="14" cy="14" r="12" fill="${aeratorColor}" stroke="white" stroke-width="2"/>
                                <text x="14" y="18" text-anchor="middle" fill="white" font-size="10" font-family="Arial">A${index + 1}</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(28, 28)
                    }
                });
                
                const aeratorInfoContent = `
                    <div style="max-width: 200px;">
                        <h6><strong>${aerator.name}</strong></h6>
                        <p><strong>Type:</strong> ${aerator.type}</p>
                        <p><strong>Status:</strong> <span class="badge badge-${aerator.status === 'active' ? 'success' : aerator.status === 'maintenance' ? 'warning' : 'danger'}">${aerator.status}</span></p>
                        ${aerator.power_rating ? `<p><strong>Power:</strong> ${aerator.power_rating} HP</p>` : ''}
                        ${aerator.operating_hours ? `<p><strong>Operating Hours:</strong> ${aerator.operating_hours}</p>` : ''}
                        <p><strong>Location:</strong> ${aerator.latitude.toFixed(6)}, ${aerator.longitude.toFixed(6)}</p>
                    </div>
                `;
                
                aeratorMarker.addListener('click', function() {
                    infoWindow.setContent(aeratorInfoContent);
                    infoWindow.open(map, aeratorMarker);
                });
            }
        });
        
        // Fit map to show all markers
        if (aeratorsData.length > 0 || (pondData.latitude && pondData.longitude)) {
            const bounds = new google.maps.LatLngBounds();
            
            // Include pond location
            if (pondData.latitude && pondData.longitude) {
                bounds.extend(new google.maps.LatLng(pondData.latitude, pondData.longitude));
            }
            
            // Include aerator locations
            aeratorsData.forEach(function(aerator) {
                if (aerator.latitude && aerator.longitude) {
                    bounds.extend(new google.maps.LatLng(aerator.latitude, aerator.longitude));
                }
            });
            
            map.fitBounds(bounds);
            
            // Ensure minimum zoom level for single pond view
            const minZoom = 16;
            if (map.getZoom() > minZoom) {
                map.setZoom(minZoom);
            }
        }
    }
    
    // Error handling for Google Maps
    window.gm_authFailure = function() {
        document.getElementById('map').innerHTML = 
            '<div class="alert alert-danger m-3">Google Maps failed to load. Please check your API key.</div>';
    };
</script>

<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap">
</script>
{% endblock %}
