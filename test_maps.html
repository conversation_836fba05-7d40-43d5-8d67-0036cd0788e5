<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
        }
    </style>
</head>
<body>
    <h1>Google Maps Test</h1>
    <div id="map"></div>
    
    <script>
        function initMap() {
            console.log('initMap function called');
            
            const map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: 13.0827, lng: 80.2707 }, // Chennai, India
                zoom: 10,
                mapTypeId: 'satellite'
            });
            
            const marker = new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Marker'
            });
            
            console.log('Map initialized successfully');
        }
    </script>
    
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap" async defer></script>
</body>
</html>