{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Pond Dashboard - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
<style>
        .dashboard-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: dashboard-sweep 6s infinite;
        }

        @keyframes dashboard-sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 25px;
            color: white;
            font-weight: bold;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
    
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.4s ease;
            border: 2px solid transparent;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
        font-size: 1.5rem;
        color: white;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            text-align: center;
            transition: all 0.4s ease;
            border: 2px solid transparent;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #636e72;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .feature-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #1f2937;
    }
    
    .feature-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .feature-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .btn-feature {
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .btn-primary-feature {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
    }
    
    .btn-secondary-feature {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        color: white;
    }
    
    .btn-success-feature {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }
    
    .btn-warning-feature {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        color: white;
    }
    
    .btn-info-feature {
        background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
        color: white;
    }
    
    .btn-feature:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        color: white;
    }
    
    .category-section {
        margin-bottom: 3rem;
    }
    
    .category-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 3px solid #3b82f6;
        display: inline-block;
    }
    
        .quick-stats {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            transition: all 0.4s ease;
        }

        .quick-stats:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border-color: #667eea;
        }
    
    .coming-soon {
        opacity: 0.7;
        position: relative;
    }
    
    .coming-soon::after {
        content: "Coming Soon";
        position: absolute;
        top: 10px;
        right: 10px;
        background: #f59e0b;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.7rem;
        font-weight: 600;
    }
    
    .status-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }
    
    .status-active { background-color: #10b981; }
    .status-inactive { background-color: #ef4444; }
    .status-maintenance { background-color: #f59e0b; }
    .status-warning { background-color: #f97316; }
</style>
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="dashboard-header">
        <div class="position-relative">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">🐟 Comprehensive Pond Dashboard</h1>
                    <p class="mb-0" style="opacity: 0.9;">Complete shrimp farm management system with advanced features</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{% url 'ponds:pond_create' %}" class="action-btn">
                        <i class="fas fa-plus"></i> Add Pond
                    </a>
                    <a href="{% url 'ponds:pond_list' %}" class="action-btn" style="background: linear-gradient(45deg, #74b9ff, #0984e3);">
                        <i class="fas fa-list"></i> All Ponds
                    </a>
                </div>
            </div>
        </div>
    </div>

<div class="container-fluid">
    <!-- Quick Statistics -->
    <div class="quick-stats">
        <h3 class="mb-3">📊 System Overview</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number text-primary">{{ total_ponds }}</div>
                <div class="stat-label">Total Ponds</div>
                <div class="mt-2">
                    <span class="status-indicator status-active"></span>{{ active_ponds }} Active
                    <span class="status-indicator status-maintenance ms-2"></span>{{ maintenance_ponds }} Maintenance
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success">{{ total_farms }}</div>
                <div class="stat-label">Total Farms</div>
                <div class="mt-2">
                    <span class="status-indicator status-active"></span>{{ farms_with_ponds }} With Ponds
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-info">{{ total_aerators }}</div>
                <div class="stat-label">Aerators</div>
                <div class="mt-2">
                    <span class="status-indicator status-active"></span>{{ active_aerators }} Active
                    <span class="status-indicator status-maintenance ms-2"></span>{{ maintenance_aerators }} Maintenance
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-warning">{{ total_iot_devices }}</div>
                <div class="stat-label">IoT Devices</div>
                <div class="mt-2">
                    <span class="status-indicator status-active"></span>{{ online_iot_devices }} Online
                    <span class="status-indicator status-inactive ms-2"></span>{{ offline_iot_devices }} Offline
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-danger">{{ critical_alerts }}</div>
                <div class="stat-label">Critical Alerts</div>
                <div class="mt-2">
                    <span class="status-indicator status-warning"></span>{{ warning_alerts }} Warnings
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-number text-success">{{ energy_efficiency }}%</div>
                <div class="stat-label">Energy Efficiency</div>
                <div class="mt-2">
                    <small class="text-muted">₹{{ monthly_energy_cost|floatformat:0 }} Monthly</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Core Pond Management -->
    <div class="category-section">
        <h2 class="category-title">🏊 Core Pond Management</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon bg-primary">
                    <i class="fas fa-water"></i>
                </div>
                <div class="feature-title">Pond Operations</div>
                <div class="feature-description">Complete pond lifecycle management, monitoring, and operations control</div>
                <div class="feature-actions">
                    <a href="{% url 'ponds:pond_list' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-list me-1"></i>View All
                    </a>
                    <a href="{% url 'ponds:pond_create' %}" class="btn-feature btn-success-feature">
                        <i class="fas fa-plus me-1"></i>Add New
                    </a>
                    <a href="{% url 'ponds:pond_dashboard' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-chart-line me-1"></i>Dashboard
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-success">
                    <i class="fas fa-building"></i>
                </div>
                <div class="feature-title">Farm Management</div>
                <div class="feature-description">Multi-farm operations, resource allocation, and centralized management</div>
                <div class="feature-actions">
                    <a href="{% url 'ponds:farm_list' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-building me-1"></i>Farms
                    </a>
                    <a href="{% url 'ponds:farm_create' %}" class="btn-feature btn-success-feature">
                        <i class="fas fa-plus me-1"></i>Add Farm
                    </a>
                    <a href="{% url 'ponds:farm_dashboard' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-info">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
                <div class="feature-title">Mapping & Location</div>
                <div class="feature-description">Interactive maps, GPS tracking, and geographical pond management</div>
                <div class="feature-actions">
                    <a href="{% url 'ponds:cumulative_map_dashboard' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-globe me-1"></i>Unified Map
                    </a>
                    <a href="{% url 'weather:unified_weather_map' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-cloud me-1"></i>Weather Map
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- IoT & Automation -->
    <div class="category-section">
        <h2 class="category-title">🤖 IoT & Automation</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon bg-success">
                    <i class="fas fa-fan"></i>
                </div>
                <div class="feature-title">Aerator Control</div>
                <div class="feature-description">Smart aerator management with IoT control, GPS tracking, and power monitoring</div>
                <div class="feature-actions">
                    {% for pond in recent_ponds_list|slice:":3" %}
                    <a href="{% url 'ponds:simple_enhanced_aerator_map' pond.id %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-fan me-1"></i>{{ pond.name }}
                    </a>
                    {% endfor %}
                    <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-globe me-1"></i>All Aerators
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-warning">
                    <i class="fas fa-satellite-dish"></i>
                </div>
                <div class="feature-title">IoT Device Management</div>
                <div class="feature-description">Real-time device monitoring, GPS tracking, and remote control capabilities</div>
                <div class="feature-actions">
                    <a href="/iot/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-microchip me-1"></i>Devices
                    </a>
                    <a href="/iot/realtime/" class="btn-feature btn-info-feature">
                        <i class="fas fa-chart-line me-1"></i>Real-time
                    </a>
                    <a href="/iot/alerts/" class="btn-feature btn-warning-feature">
                        <i class="fas fa-bell me-1"></i>Alerts
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-info">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="feature-title">Smart Automation</div>
                <div class="feature-description">AI-powered automation, predictive maintenance, and intelligent scheduling</div>
                <div class="feature-actions">
                    <a href="/ai-ml/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-brain me-1"></i>AI Dashboard
                    </a>
                    <a href="/iot/automation/" class="btn-feature btn-success-feature">
                        <i class="fas fa-cogs me-1"></i>Automation
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Monitoring & Analytics -->
    <div class="category-section">
        <h2 class="category-title">📊 Monitoring & Analytics</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon bg-primary">
                    <i class="fas fa-cloud-sun"></i>
                </div>
                <div class="feature-title">Weather Integration</div>
                <div class="feature-description">Real-time weather monitoring, forecasting, and pond-specific weather data</div>
                <div class="feature-actions">
                    <a href="{% url 'weather:pond_weather_dashboard' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-cloud me-1"></i>Weather Dashboard
                    </a>
                    <a href="{% url 'weather:unified_weather_map' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-map me-1"></i>Weather Map
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-success">
                    <i class="fas fa-tint"></i>
                </div>
                <div class="feature-title">Water Quality Monitoring</div>
                <div class="feature-description">Real-time water quality sensors, pH monitoring, and quality alerts</div>
                <div class="feature-actions">
                    <a href="/water-quality/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-flask me-1"></i>Quality Dashboard
                    </a>
                    <a href="/sensors/" class="btn-feature btn-info-feature">
                        <i class="fas fa-thermometer me-1"></i>Sensors
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-warning">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-title">Advanced Analytics</div>
                <div class="feature-description">Predictive analytics, trend analysis, and performance optimization</div>
                <div class="feature-actions">
                    <a href="/iot/analytics/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-chart-bar me-1"></i>Analytics
                    </a>
                    <a href="/reports/" class="btn-feature btn-info-feature">
                        <i class="fas fa-file-alt me-1"></i>Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Operations Management -->
    <div class="category-section">
        <h2 class="category-title">👥 Operations Management</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon bg-info">
                    <i class="fas fa-users"></i>
                </div>
                <div class="feature-title">Labor Management</div>
                <div class="feature-description">Worker scheduling, task management, and productivity tracking</div>
                <div class="feature-actions">
                    <a href="/labor/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-users me-1"></i>Labor Dashboard
                    </a>
                    <a href="/direct-labor-dashboard/" class="btn-feature btn-info-feature">
                        <i class="fas fa-user-tie me-1"></i>Direct Labor
                    </a>
                    <a href="/standalone-labor-dashboard/" class="btn-feature btn-secondary-feature">
                        <i class="fas fa-user-cog me-1"></i>Standalone
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-success">
                    <i class="fas fa-pills"></i>
                </div>
                <div class="feature-title">Medicine & Health</div>
                <div class="feature-description">Disease management, medication tracking, and health monitoring</div>
                <div class="feature-actions">
                    <a href="/medicine/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-pills me-1"></i>Medicine Dashboard
                    </a>
                    <a href="/disease/" class="btn-feature btn-warning-feature">
                        <i class="fas fa-virus me-1"></i>Disease Detection
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-warning">
                    <i class="fas fa-fish"></i>
                </div>
                <div class="feature-title">Feed Management</div>
                <div class="feature-description">Feeding schedules, nutrition tracking, and feed optimization</div>
                <div class="feature-actions">
                    <a href="/feed/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-fish me-1"></i>Feed Dashboard
                    </a>
                    <a href="/nutrition/" class="btn-feature btn-success-feature">
                        <i class="fas fa-leaf me-1"></i>Nutrition
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Panel -->
    <div class="category-section">
        <h2 class="category-title">⚡ Quick Actions</h2>
        <div class="feature-grid">
            <div class="feature-card">
                <div class="feature-icon bg-primary">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="feature-title">Create New</div>
                <div class="feature-description">Quickly create new ponds, farms, or add equipment</div>
                <div class="feature-actions">
                    <a href="{% url 'ponds:pond_create' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-water me-1"></i>New Pond
                    </a>
                    <a href="{% url 'ponds:farm_create' %}" class="btn-feature btn-success-feature">
                        <i class="fas fa-building me-1"></i>New Farm
                    </a>
                    <a href="/iot/devices/register/" class="btn-feature btn-info-feature">
                        <i class="fas fa-microchip me-1"></i>Add Device
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-info">
                    <i class="fas fa-search"></i>
                </div>
                <div class="feature-title">Search & Filter</div>
                <div class="feature-description">Advanced search and filtering across all system data</div>
                <div class="feature-actions">
                    <a href="{% url 'ponds:pond_list' %}" class="btn-feature btn-primary-feature">
                        <i class="fas fa-list me-1"></i>Browse Ponds
                    </a>
                    <a href="{% url 'ponds:farm_list' %}" class="btn-feature btn-info-feature">
                        <i class="fas fa-building me-1"></i>Browse Farms
                    </a>
                    <a href="/search/" class="btn-feature btn-secondary-feature">
                        <i class="fas fa-search me-1"></i>Global Search
                    </a>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon bg-success">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="feature-title">System Settings</div>
                <div class="feature-description">Configure system preferences, user settings, and integrations</div>
                <div class="feature-actions">
                    <a href="/settings/" class="btn-feature btn-primary-feature">
                        <i class="fas fa-cog me-1"></i>Settings
                    </a>
                    <a href="/profile/" class="btn-feature btn-info-feature">
                        <i class="fas fa-user me-1"></i>Profile
                    </a>
                    <a href="/help/" class="btn-feature btn-secondary-feature">
                        <i class="fas fa-question-circle me-1"></i>Help
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add smooth scrolling to category sections
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add loading states to feature buttons
        document.querySelectorAll('.btn-feature').forEach(button => {
            button.addEventListener('click', function() {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Loading...';
                this.disabled = true;

                // Re-enable after 2 seconds (in case navigation fails)
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // Initialize tooltips if Bootstrap is available
        if (typeof bootstrap !== 'undefined') {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    });
</script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
