{% extends "base.html" %}
{% load static %}

{% block title %}Water Quality Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .water-quality-container {
        padding: 20px;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        min-height: 100vh;
    }

    .wq-header {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .wq-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: header-sweep 8s infinite;
    }

    @keyframes header-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .wq-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
        margin-bottom: 30px;
    }

    .wq-stat-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }

    .wq-stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }

    .wq-stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(45deg, #1976d2, #42a5f5);
    }

    .wq-stat-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .wq-stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1565c0;
        margin-bottom: 5px;
    }

    .wq-stat-label {
        color: #424242;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
        font-size: 0.9rem;
    }

    .wq-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
    }

    .wq-main-content {
        display: flex;
        flex-direction: column;
        gap: 30px;
    }

    .wq-sidebar {
        display: flex;
        flex-direction: column;
        gap: 20px;
    }

    .wq-widget {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    }

    .wq-widget-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f1f3f4;
    }

    .wq-widget-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #1565c0;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .wq-reading-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-left: 4px solid #1976d2;
        transition: all 0.3s ease;
    }

    .wq-reading-item:hover {
        background: #e3f2fd;
        transform: translateX(5px);
    }

    .wq-reading-info {
        flex: 1;
    }

    .wq-reading-pond {
        font-weight: 600;
        color: #1565c0;
        margin-bottom: 2px;
    }

    .wq-reading-parameter {
        font-size: 0.9rem;
        color: #424242;
    }

    .wq-reading-value {
        font-weight: 700;
        font-size: 1.1rem;
        color: #2e7d32;
    }

    .wq-reading-time {
        font-size: 0.8rem;
        color: #757575;
        margin-top: 5px;
    }

    .wq-status-optimal { border-left-color: #4caf50; }
    .wq-status-acceptable { border-left-color: #ff9800; }
    .wq-status-warning { border-left-color: #ff5722; }
    .wq-status-critical { border-left-color: #f44336; }

    .wq-alert-item {
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
        border: 1px solid #ef5350;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .wq-alert-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f44336 0%, #e53935 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .wq-alert-content {
        flex: 1;
    }

    .wq-alert-title {
        font-weight: 600;
        color: #c62828;
        margin-bottom: 2px;
    }

    .wq-alert-message {
        font-size: 0.9rem;
        color: #424242;
    }

    .wq-alert-time {
        font-size: 0.8rem;
        color: #757575;
        margin-top: 5px;
    }

    .wq-pond-status {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .wq-pond-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        border: 2px solid #e0e0e0;
        transition: all 0.3s ease;
    }

    .wq-pond-card:hover {
        border-color: #1976d2;
        box-shadow: 0 8px 25px rgba(25, 118, 210, 0.2);
    }

    .wq-pond-card.status-good {
        border-color: #4caf50;
        background: linear-gradient(135deg, #f1f8e9 0%, #e8f5e8 100%);
    }

    .wq-pond-card.status-warning {
        border-color: #ff9800;
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    }

    .wq-pond-card.status-critical {
        border-color: #f44336;
        background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    }

    .wq-pond-name {
        font-weight: 700;
        color: #1565c0;
        margin-bottom: 10px;
    }

    .wq-pond-stats {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
    }

    .wq-action-btn {
        background: linear-gradient(45deg, #1976d2, #42a5f5);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 10px 20px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .wq-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(25, 118, 210, 0.4);
        color: white;
        text-decoration: none;
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .wq-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .wq-stats {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .wq-pond-status {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 480px) {
        .wq-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="water-quality-container">
    <!-- Header -->
    <div class="wq-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2">💧 Water Quality Dashboard</h1>
                <p class="mb-0" style="opacity: 0.9;">Monitor and manage water quality across all ponds</p>
            </div>
            <div class="d-flex gap-2">
                <a href="{% url 'ponds:water_quality_alerts' %}" class="btn btn-light">
                    <i class="fas fa-exclamation-triangle me-2"></i> View Alerts
                </a>
                <button class="btn btn-outline-light" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-2"></i> Refresh
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="wq-stats">
        <div class="wq-stat-card">
            <div class="wq-stat-icon">🏊</div>
            <div class="wq-stat-number">{{ total_ponds }}</div>
            <div class="wq-stat-label">Monitored Ponds</div>
        </div>
        <div class="wq-stat-card">
            <div class="wq-stat-icon">📊</div>
            <div class="wq-stat-number">{{ total_readings }}</div>
            <div class="wq-stat-label">Total Readings</div>
        </div>
        <div class="wq-stat-card">
            <div class="wq-stat-icon">🚨</div>
            <div class="wq-stat-number">{{ critical_alerts }}</div>
            <div class="wq-stat-label">Critical Alerts</div>
        </div>
        <div class="wq-stat-card">
            <div class="wq-stat-icon">⚠️</div>
            <div class="wq-stat-number">{{ high_alerts }}</div>
            <div class="wq-stat-label">High Priority</div>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="wq-grid">
        <!-- Main Content -->
        <div class="wq-main-content">
            <!-- Recent Readings -->
            <div class="wq-widget">
                <div class="wq-widget-header">
                    <h3 class="wq-widget-title">
                        <i class="fas fa-thermometer-half"></i>
                        Recent Readings (24h)
                    </h3>
                </div>
                <div class="wq-readings-list">
                    {% for reading in recent_readings %}
                    <div class="wq-reading-item wq-status-{{ reading.status }}">
                        <div class="wq-reading-info">
                            <div class="wq-reading-pond">{{ reading.pond.name }}</div>
                            <div class="wq-reading-parameter">{{ reading.parameter.name }}</div>
                            <div class="wq-reading-time">{{ reading.measured_at|timesince }} ago</div>
                        </div>
                        <div class="wq-reading-value">
                            {{ reading.value }} {{ reading.parameter.unit }}
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-line" style="font-size: 2rem; color: #dee2e6; margin-bottom: 10px;"></i>
                        <p class="text-muted">No recent readings</p>
                        <a href="#" class="wq-action-btn">
                            <i class="fas fa-plus"></i> Add Reading
                        </a>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Pond Status Overview -->
            <div class="wq-widget">
                <div class="wq-widget-header">
                    <h3 class="wq-widget-title">
                        <i class="fas fa-water"></i>
                        Pond Status Overview
                    </h3>
                </div>
                <div class="wq-pond-status">
                    {% for pond_info in pond_status %}
                    <div class="wq-pond-card status-{{ pond_info.status }}">
                        <div class="wq-pond-name">{{ pond_info.pond.name }}</div>
                        <div class="wq-pond-stats">
                            <span>📊 {{ pond_info.reading_count }} readings</span>
                            {% if pond_info.critical_count > 0 %}
                            <span style="color: #f44336;">🚨 {{ pond_info.critical_count }} critical</span>
                            {% elif pond_info.warning_count > 0 %}
                            <span style="color: #ff9800;">⚠️ {{ pond_info.warning_count }} warnings</span>
                            {% else %}
                            <span style="color: #4caf50;">✅ Good</span>
                            {% endif %}
                        </div>
                        <div class="mt-2">
                            <a href="{% url 'ponds:pond_water_quality' pond_info.pond.id %}" class="wq-action-btn" style="font-size: 0.8rem; padding: 6px 12px;">
                                <i class="fas fa-eye"></i> View Details
                            </a>
                        </div>
                    </div>
                    {% empty %}
                    <div class="text-center py-4">
                        <i class="fas fa-water" style="font-size: 2rem; color: #dee2e6; margin-bottom: 10px;"></i>
                        <p class="text-muted">No ponds found</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="wq-sidebar">
            <!-- Active Alerts -->
            <div class="wq-widget">
                <div class="wq-widget-header">
                    <h3 class="wq-widget-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        Active Alerts
                    </h3>
                </div>
                {% for alert in active_alerts %}
                <div class="wq-alert-item">
                    <div class="wq-alert-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="wq-alert-content">
                        <div class="wq-alert-title">{{ alert.title }}</div>
                        <div class="wq-alert-message">{{ alert.message }}</div>
                        <div class="wq-alert-time">{{ alert.created_at|timesince }} ago</div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-3">
                    <i class="fas fa-check-circle" style="font-size: 2rem; color: #4caf50; margin-bottom: 10px;"></i>
                    <p style="margin: 0; color: #4caf50;">No active alerts</p>
                </div>
                {% endfor %}
                
                {% if active_alerts %}
                <div class="text-center mt-3">
                    <a href="{% url 'ponds:water_quality_alerts' %}" class="wq-action-btn">
                        <i class="fas fa-list"></i> View All Alerts
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- Quick Actions -->
            <div class="wq-widget">
                <div class="wq-widget-header">
                    <h3 class="wq-widget-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                </div>
                <div class="d-grid gap-2">
                    <a href="#" class="wq-action-btn">
                        <i class="fas fa-plus"></i> Add Reading
                    </a>
                    <a href="{% url 'ponds:water_quality_alerts' %}" class="wq-action-btn">
                        <i class="fas fa-bell"></i> Manage Alerts
                    </a>
                    <a href="#" class="wq-action-btn">
                        <i class="fas fa-chart-bar"></i> Generate Report
                    </a>
                    <a href="#" class="wq-action-btn">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function refreshDashboard() {
        location.reload();
    }
</script>
{% endblock %}
