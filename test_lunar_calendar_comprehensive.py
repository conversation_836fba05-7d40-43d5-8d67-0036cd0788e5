#!/usr/bin/env python3
"""
Comprehensive test to validate the lunar calendar fixes.
This script tests:
1. API endpoint accessibility
2. Lunar phase data accuracy
3. Event formatting for calendar display
4. Date range coverage
"""

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def test_lunar_calendar_api():
    """Test the public lunar calendar API"""
    print("🌙 Testing Lunar Calendar API Integration")
    print("=" * 50)
    
    api_url = "http://127.0.0.1:8000/lunar/api/calendar/public/"
    
    try:
        # Test API accessibility
        print("1. Testing API accessibility...")
        response = requests.get(api_url)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code != 200:
            print("   ❌ API not accessible")
            return False
            
        print("   ✅ API accessible")
        
        # Test JSON response format
        print("\n2. Testing JSON response format...")
        try:
            data = response.json()
            print(f"   Response type: {type(data)}")
            print(f"   Has 'events' key: {'events' in data}")
            
            if 'events' not in data:
                print("   ❌ Invalid response format")
                return False
                
            events = data['events']
            print(f"   Events count: {len(events)}")
            print("   ✅ Valid JSON format")
            
        except json.JSONDecodeError:
            print("   ❌ Invalid JSON response")
            return False
        
        # Test lunar phase coverage
        print("\n3. Testing lunar phase coverage...")
        required_phases = ['astami', 'navami', 'pournami', 'amavasya']
        phase_counts = {}
        
        for phase in required_phases:
            phase_events = [e for e in events if e.get('type') == phase]
            phase_counts[phase] = len(phase_events)
            print(f"   {phase.capitalize()}: {len(phase_events)} events")
        
        # Check if all required phases are present
        missing_phases = [phase for phase, count in phase_counts.items() if count == 0]
        if missing_phases:
            print(f"   ⚠️  Missing phases: {missing_phases}")
        else:
            print("   ✅ All required lunar phases present")
        
        # Test event structure
        print("\n4. Testing event data structure...")
        if events:
            sample_event = events[0]
            required_fields = ['id', 'title', 'date', 'type', 'tithi']
            
            print(f"   Sample event: {sample_event['title']} on {sample_event['date']}")
            
            missing_fields = [field for field in required_fields if field not in sample_event]
            if missing_fields:
                print(f"   ⚠️  Missing fields: {missing_fields}")
            else:
                print("   ✅ All required event fields present")
        
        # Test date formatting
        print("\n5. Testing date formatting...")
        date_formats_valid = True
        for event in events[:5]:  # Test first 5 events
            try:
                # Test if date is in YYYY-MM-DD format
                datetime.strptime(event['date'], '%Y-%m-%d')
            except ValueError:
                print(f"   ❌ Invalid date format in event: {event['date']}")
                date_formats_valid = False
                break
        
        if date_formats_valid:
            print("   ✅ Date formats are valid")
        
        # Display upcoming events
        print("\n6. Upcoming lunar events (next 30 days)...")
        today = datetime.now().date()
        upcoming_limit = today + timedelta(days=30)
        
        upcoming_events = [
            e for e in events 
            if today <= datetime.strptime(e['date'], '%Y-%m-%d').date() <= upcoming_limit
        ]
        
        upcoming_events.sort(key=lambda x: x['date'])
        
        print(f"   Found {len(upcoming_events)} upcoming events:")
        for event in upcoming_events[:10]:  # Show first 10
            print(f"   • {event['date']}: {event['title']} ({event['type']})")
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 LUNAR CALENDAR TEST SUMMARY")
        print("=" * 50)
        
        total_events = len(events)
        important_events = len([e for e in events if e['type'] in required_phases])
        
        print(f"✅ API Status: Working")
        print(f"✅ Total Events: {total_events}")
        print(f"✅ Important Phase Events: {important_events}")
        print(f"✅ Astami Events: {phase_counts['astami']}")
        print(f"✅ Navami Events: {phase_counts['navami']}")
        print(f"✅ Pournami Events: {phase_counts['pournami']}")
        print(f"✅ Amavasya Events: {phase_counts['amavasya']}")
        print(f"✅ Upcoming Events (30 days): {len(upcoming_events)}")
        
        if important_events > 0:
            print("\n🎉 SUCCESS: Lunar calendar is now displaying important lunar phases!")
            print("   The calendar should now show Astami, Navami, Pournami, and Amavasya events.")
            return True
        else:
            print("\n❌ ISSUE: No important lunar phases found in the data.")
            return False
            
    except requests.RequestException as e:
        print(f"❌ Error connecting to API: {e}")
        return False

def test_calendar_page_integration():
    """Test if calendar pages can load (basic check)"""
    print("\n🌐 Testing Calendar Page Integration")
    print("=" * 50)
    
    pages_to_test = [
        ("Main Lunar Calendar", "http://127.0.0.1:8000/lunar/"),
        ("Full Lunar Calendar", "http://127.0.0.1:8000/lunar/full-calendar/")
    ]
    
    for page_name, url in pages_to_test:
        try:
            response = requests.get(url)
            print(f"{page_name}: Status {response.status_code}")
            
            # Check if the page contains calendar-related content
            has_fullcalendar = 'fullcalendar' in response.text.lower()
            has_lunar_api = 'lunar_calendar_api_public' in response.text
            
            if response.status_code == 200:
                if has_fullcalendar or has_lunar_api:
                    print(f"   ✅ Calendar content detected")
                else:
                    print(f"   ⚠️  May require authentication to view calendar")
            else:
                print(f"   ❌ Page not accessible")
                
        except requests.RequestException as e:
            print(f"{page_name}: ❌ Error - {e}")

if __name__ == "__main__":
    print("🌙 LUNAR CALENDAR INTEGRATION TEST")
    print("🐚 Shrimp Farm Guardian - Lunar Phase Display Fix")
    print("=" * 60)
    
    # Run API tests
    api_success = test_lunar_calendar_api()
    
    # Run page integration tests
    test_calendar_page_integration()
    
    print("\n" + "=" * 60)
    if api_success:
        print("🎊 OVERALL RESULT: LUNAR CALENDAR FIXES SUCCESSFUL!")
        print("   The important lunar phases (Astami, Navami, Pournami, Amavasya)")
        print("   are now available via the API and should display on the calendar.")
        print("\n📝 TO VERIFY IN BROWSER:")
        print("   1. Visit: http://127.0.0.1:8000/lunar/")
        print("   2. Log in if required")
        print("   3. Check that lunar phases appear on the calendar")
        print("   4. Verify colors: Astami=🌓, Navami=🌓, Pournami=🌕, Amavasya=🌑")
    else:
        print("❌ OVERALL RESULT: ISSUES DETECTED")
        print("   Please check the API configuration and try again.")
