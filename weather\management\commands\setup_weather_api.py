from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random

from weather.models import WeatherAPIConfig, WeatherStation, WeatherData
from ponds.models import Pond


class Command(BaseCommand):
    help = 'Setup weather API configuration and generate sample weather data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--create-config',
            action='store_true',
            help='Create weather API configuration'
        )
        parser.add_argument(
            '--generate-data',
            action='store_true',
            help='Generate sample weather data'
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days of weather data to generate'
        )

    def handle(self, *args, **options):
        self.stdout.write('🌤️ SETTING UP WEATHER API AND DATA...\n')
        
        if options['create_config']:
            self.create_api_config()
        
        if options['generate_data']:
            self.generate_weather_data(options['days'])
        
        self.stdout.write(self.style.SUCCESS('\n🎉 WEATHER SETUP COMPLETED!'))

    def create_api_config(self):
        """Create weather API configuration"""
        self.stdout.write('⚙️ Creating Weather API Configuration...')
        
        # Create OpenWeatherMap configuration (demo)
        config, created = WeatherAPIConfig.objects.get_or_create(
            api_provider='OpenWeatherMap',
            defaults={
                'name': 'OpenWeatherMap API',
                'api_key': 'demo_api_key_replace_with_real_key',
                'base_url': 'https://api.openweathermap.org',
                'is_active': True
            }
        )
        
        if created:
            self.stdout.write('  ✅ Created OpenWeatherMap API configuration')
        else:
            self.stdout.write('  ✅ OpenWeatherMap API configuration already exists')
        
        # Create WeatherAPI configuration (demo)
        config2, created2 = WeatherAPIConfig.objects.get_or_create(
            api_provider='WeatherAPI',
            defaults={
                'name': 'WeatherAPI Service',
                'api_key': 'demo_api_key_replace_with_real_key',
                'base_url': 'https://api.weatherapi.com',
                'is_active': False  # Keep inactive for demo
            }
        )
        
        if created2:
            self.stdout.write('  ✅ Created WeatherAPI configuration')
        else:
            self.stdout.write('  ✅ WeatherAPI configuration already exists')

    def generate_weather_data(self, days=7):
        """Generate sample weather data for all weather stations"""
        self.stdout.write(f'📊 Generating {days} days of weather data...')
        
        stations = WeatherStation.objects.filter(is_active=True)
        if not stations.exists():
            self.stdout.write('  ⚠️ No active weather stations found')
            return
        
        total_records = 0
        
        for station in stations:
            self.stdout.write(f'  📍 Generating data for station: {station.name}')
            
            # Generate data for the past few days and future forecast
            start_date = timezone.now() - timedelta(days=days//2)
            
            for day in range(days):
                current_time = start_date + timedelta(days=day)
                
                # Generate 4 records per day (every 6 hours)
                for hour in [0, 6, 12, 18]:
                    timestamp = current_time.replace(hour=hour, minute=0, second=0, microsecond=0)
                    
                    # Check if data already exists
                    if WeatherData.objects.filter(station=station, timestamp=timestamp).exists():
                        continue
                    
                    # Generate realistic weather data based on location
                    weather_data = self.generate_realistic_weather_data(station, timestamp)
                    
                    WeatherData.objects.create(
                        station=station,
                        timestamp=timestamp,
                        **weather_data
                    )
                    
                    total_records += 1
        
        self.stdout.write(f'  ✅ Generated {total_records} weather records')

    def generate_realistic_weather_data(self, station, timestamp):
        """Generate realistic weather data based on station location and time"""
        # Base weather on location (assuming tropical/subtropical aquaculture regions)
        base_temp = 28  # Base temperature for aquaculture regions
        
        # Add seasonal variation (simplified)
        month = timestamp.month
        if month in [12, 1, 2]:  # Winter
            temp_modifier = -3
        elif month in [3, 4, 5]:  # Spring
            temp_modifier = 0
        elif month in [6, 7, 8]:  # Summer
            temp_modifier = 3
        else:  # Fall
            temp_modifier = 1
        
        # Add daily variation
        hour = timestamp.hour
        if hour in [0, 6]:  # Night/early morning
            daily_modifier = -2
        elif hour == 12:  # Noon
            daily_modifier = 4
        else:  # Evening
            daily_modifier = 1
        
        # Add random variation
        random_modifier = random.uniform(-2, 2)
        
        temperature = base_temp + temp_modifier + daily_modifier + random_modifier
        
        # Generate other weather parameters
        humidity = random.uniform(60, 90)  # High humidity for coastal areas
        pressure = random.uniform(1008, 1018)  # Typical sea level pressure
        wind_speed = random.uniform(0, 15)  # Light to moderate winds
        wind_direction = random.choice(['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'])
        
        # Precipitation (more likely in certain seasons)
        if month in [6, 7, 8, 9]:  # Monsoon season
            precipitation = random.uniform(0, 10) if random.random() < 0.4 else 0
        else:
            precipitation = random.uniform(0, 5) if random.random() < 0.2 else 0
        
        # Weather condition based on precipitation and other factors
        if precipitation > 5:
            condition = 'Heavy Rain'
        elif precipitation > 1:
            condition = 'Light Rain'
        elif humidity > 85 and temperature > 30:
            condition = 'Hot and Humid'
        elif wind_speed > 10:
            condition = 'Windy'
        elif random.random() < 0.3:
            condition = 'Partly Cloudy'
        else:
            condition = 'Clear'
        
        return {
            'temperature': round(temperature, 1),
            'humidity': round(humidity, 1),
            'pressure': round(pressure, 1),
            'wind_speed': round(wind_speed, 1),
            'wind_direction': wind_direction,
            'precipitation': round(precipitation, 1),
            'condition': condition
        }
