<!DOCTYPE html>
<html>
<head>
    <title>Test Unified Map</title>
    <style>
        #map { height: 400px; width: 100%; }
        .debug { padding: 20px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="debug">
        <h2>Google Maps API Test</h2>
        <div id="status">Loading...</div>
        <div id="map"></div>
    </div>

    <script>
        const API_KEY = 'AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw';
        
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
            console.log(message);
        }

        function initMap() {
            updateStatus('Google Maps API loaded successfully!');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 13.7563, lng: 100.5018 },
                    zoom: 10
                });
                
                // Add a test marker
                new google.maps.Marker({
                    position: { lat: 13.7563, lng: 100.5018 },
                    map: map,
                    title: 'Test Marker'
                });
                
                updateStatus('Map created successfully! API key is valid.');
            } catch (error) {
                updateStatus('Error creating map: ' + error.message);
            }
        }

        function handleMapError() {
            updateStatus('Error loading Google Maps API. Check API key and network connection.');
        }

        // Check if Google Maps API loads
        updateStatus('Attempting to load Google Maps API...');
    </script>

    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&onerror=handleMapError">
    </script>
</body>
</html>
