from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
import json

User = get_user_model()


class WeatherStation(models.Model):
    """Weather monitoring station"""
    
    STATION_TYPE_CHOICES = [
        ('api', 'API Service'),
        ('local', 'Local Weather Station'),
        ('satellite', 'Satellite Data'),
        ('hybrid', 'Hybrid Sources'),
    ]
    
    name = models.CharField(max_length=200, help_text="Weather station name")
    station_type = models.CharField(max_length=20, choices=STATION_TYPE_CHOICES, default='api')
    
    # Location
    latitude = models.FloatField(validators=[MinValueValidator(-90), MaxValueValidator(90)])
    longitude = models.FloatField(validators=[MinValueValidator(-180), MaxValueValidator(180)])
    elevation = models.FloatField(default=0, help_text="Elevation in meters")
    
    # API Configuration
    api_provider = models.CharField(max_length=100, blank=True, help_text="Weather API provider")
    api_key = models.Char<PERSON>ield(max_length=200, blank=True, help_text="API key")
    api_endpoint = models.URLField(blank=True, help_text="API endpoint URL")
    
    # Station settings
    update_interval = models.IntegerField(default=300, help_text="Update interval in seconds")
    is_active = models.BooleanField(default=True)
    last_update = models.DateTimeField(null=True, blank=True)
    
    # Data quality
    reliability_score = models.FloatField(default=1.0, validators=[MinValueValidator(0), MaxValueValidator(1)])
    data_accuracy = models.FloatField(default=0.95, validators=[MinValueValidator(0), MaxValueValidator(1)])
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Weather Station'
        verbose_name_plural = 'Weather Stations'
        indexes = [
            models.Index(fields=['latitude', 'longitude']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} ({self.get_station_type_display()})"


class WeatherData(models.Model):
    """Current weather data"""
    
    station = models.ForeignKey(WeatherStation, on_delete=models.CASCADE, related_name='weather_data')
    
    # Basic weather parameters
    temperature = models.FloatField(help_text="Temperature in Celsius")
    humidity = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], help_text="Humidity percentage")
    pressure = models.FloatField(help_text="Atmospheric pressure in hPa")
    wind_speed = models.FloatField(validators=[MinValueValidator(0)], help_text="Wind speed in m/s")
    wind_direction = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(360)], help_text="Wind direction in degrees")
    
    # Precipitation
    rainfall = models.FloatField(default=0, validators=[MinValueValidator(0)], help_text="Rainfall in mm")
    rainfall_rate = models.FloatField(default=0, validators=[MinValueValidator(0)], help_text="Rainfall rate in mm/h")
    
    # Solar and UV
    solar_radiation = models.FloatField(null=True, blank=True, help_text="Solar radiation in W/m²")
    uv_index = models.FloatField(null=True, blank=True, validators=[MinValueValidator(0)], help_text="UV index")
    
    # Water-related parameters
    dew_point = models.FloatField(null=True, blank=True, help_text="Dew point in Celsius")
    evaporation_rate = models.FloatField(null=True, blank=True, help_text="Evaporation rate in mm/day")
    
    # Weather conditions
    weather_condition = models.CharField(max_length=100, blank=True, help_text="Weather condition description")
    visibility = models.FloatField(null=True, blank=True, help_text="Visibility in km")
    cloud_cover = models.FloatField(null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)], help_text="Cloud cover percentage")
    
    # Data quality and source
    data_source = models.CharField(max_length=100, help_text="Data source identifier")
    quality_score = models.FloatField(default=1.0, validators=[MinValueValidator(0), MaxValueValidator(1)])
    
    timestamp = models.DateTimeField(default=timezone.now)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Weather Data'
        verbose_name_plural = 'Weather Data'
        indexes = [
            models.Index(fields=['station', 'timestamp']),
            models.Index(fields=['timestamp']),
        ]
        unique_together = ['station', 'timestamp']
    
    def __str__(self):
        return f"{self.station.name} - {self.timestamp.strftime('%Y-%m-%d %H:%M')} - {self.temperature}°C"


class WeatherForecast(models.Model):
    """Weather forecast data"""
    
    FORECAST_TYPE_CHOICES = [
        ('hourly', 'Hourly Forecast'),
        ('daily', 'Daily Forecast'),
        ('weekly', 'Weekly Forecast'),
        ('extended', 'Extended Forecast'),
    ]
    
    station = models.ForeignKey(WeatherStation, on_delete=models.CASCADE, related_name='forecasts')
    forecast_type = models.CharField(max_length=20, choices=FORECAST_TYPE_CHOICES)
    
    # Forecast period
    forecast_time = models.DateTimeField(help_text="Time this forecast is for")
    forecast_horizon = models.DurationField(help_text="How far ahead this forecast is")
    
    # Forecasted weather parameters
    temperature_min = models.FloatField(help_text="Minimum temperature in Celsius")
    temperature_max = models.FloatField(help_text="Maximum temperature in Celsius")
    temperature_avg = models.FloatField(help_text="Average temperature in Celsius")
    
    humidity_min = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    humidity_max = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    humidity_avg = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    wind_speed_avg = models.FloatField(validators=[MinValueValidator(0)], help_text="Average wind speed in m/s")
    wind_speed_max = models.FloatField(validators=[MinValueValidator(0)], help_text="Maximum wind speed in m/s")
    wind_direction_avg = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(360)])
    
    # Precipitation forecast
    precipitation_probability = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)], help_text="Precipitation probability %")
    precipitation_amount = models.FloatField(default=0, validators=[MinValueValidator(0)], help_text="Expected precipitation in mm")
    
    # Weather conditions
    weather_condition = models.CharField(max_length=100, help_text="Forecasted weather condition")
    cloud_cover_avg = models.FloatField(null=True, blank=True, validators=[MinValueValidator(0), MaxValueValidator(100)])
    
    # Forecast confidence and accuracy
    confidence_score = models.FloatField(default=0.8, validators=[MinValueValidator(0), MaxValueValidator(1)])
    model_used = models.CharField(max_length=100, help_text="Weather model used for forecast")
    
    # Metadata
    issued_at = models.DateTimeField(default=timezone.now, help_text="When this forecast was issued")
    data_source = models.CharField(max_length=100, help_text="Forecast data source")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Weather Forecast'
        verbose_name_plural = 'Weather Forecasts'
        indexes = [
            models.Index(fields=['station', 'forecast_time']),
            models.Index(fields=['forecast_type', 'forecast_time']),
        ]
        unique_together = ['station', 'forecast_type', 'forecast_time']
    
    def __str__(self):
        return f"{self.station.name} - {self.forecast_type} - {self.forecast_time.strftime('%Y-%m-%d %H:%M')}"


class WeatherAlert(models.Model):
    """Weather-based alerts and warnings"""
    
    ALERT_TYPE_CHOICES = [
        ('storm', 'Storm Warning'),
        ('heavy_rain', 'Heavy Rain'),
        ('high_wind', 'High Wind'),
        ('temperature_extreme', 'Extreme Temperature'),
        ('drought', 'Drought Conditions'),
        ('flood', 'Flood Warning'),
        ('typhoon', 'Typhoon/Hurricane'),
        ('frost', 'Frost Warning'),
    ]
    
    SEVERITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
        ('emergency', 'Emergency'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('expired', 'Expired'),
        ('cancelled', 'Cancelled'),
        ('acknowledged', 'Acknowledged'),
    ]
    
    station = models.ForeignKey(WeatherStation, on_delete=models.CASCADE, related_name='alerts')
    
    alert_type = models.CharField(max_length=50, choices=ALERT_TYPE_CHOICES)
    severity = models.CharField(max_length=20, choices=SEVERITY_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    
    # Alert details
    title = models.CharField(max_length=200, help_text="Alert title")
    description = models.TextField(help_text="Detailed alert description")
    recommendations = models.TextField(blank=True, help_text="Recommended actions")
    
    # Alert timing
    start_time = models.DateTimeField(help_text="When alert becomes active")
    end_time = models.DateTimeField(help_text="When alert expires")
    issued_at = models.DateTimeField(default=timezone.now)
    
    # Alert parameters
    threshold_values = models.JSONField(default=dict, help_text="Threshold values that triggered alert")
    current_values = models.JSONField(default=dict, help_text="Current weather values")
    
    # Impact assessment
    impact_level = models.CharField(max_length=20, choices=SEVERITY_CHOICES, default='medium')
    affected_areas = models.JSONField(default=list, help_text="List of affected areas")
    
    # Response tracking
    acknowledged_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    response_actions = models.TextField(blank=True, help_text="Actions taken in response")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Weather Alert'
        verbose_name_plural = 'Weather Alerts'
        indexes = [
            models.Index(fields=['station', 'status']),
            models.Index(fields=['severity', 'status']),
            models.Index(fields=['start_time', 'end_time']),
        ]
    
    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.severity} - {self.station.name}"


class WeatherAutomationRule(models.Model):
    """Weather-based automation rules"""
    
    CONDITION_TYPE_CHOICES = [
        ('temperature', 'Temperature'),
        ('humidity', 'Humidity'),
        ('wind_speed', 'Wind Speed'),
        ('rainfall', 'Rainfall'),
        ('pressure', 'Atmospheric Pressure'),
        ('weather_condition', 'Weather Condition'),
        ('forecast', 'Weather Forecast'),
    ]
    
    OPERATOR_CHOICES = [
        ('gt', 'Greater Than'),
        ('gte', 'Greater Than or Equal'),
        ('lt', 'Less Than'),
        ('lte', 'Less Than or Equal'),
        ('eq', 'Equal To'),
        ('ne', 'Not Equal To'),
        ('between', 'Between'),
        ('contains', 'Contains'),
    ]
    
    ACTION_TYPE_CHOICES = [
        ('device_control', 'Device Control'),
        ('alert', 'Send Alert'),
        ('notification', 'Send Notification'),
        ('schedule_change', 'Change Schedule'),
        ('emergency_action', 'Emergency Action'),
    ]
    
    name = models.CharField(max_length=200, help_text="Rule name")
    description = models.TextField(blank=True, help_text="Rule description")
    is_active = models.BooleanField(default=True)
    
    # Weather condition
    condition_type = models.CharField(max_length=50, choices=CONDITION_TYPE_CHOICES)
    operator = models.CharField(max_length=20, choices=OPERATOR_CHOICES)
    threshold_value = models.FloatField(help_text="Threshold value for condition")
    threshold_value_max = models.FloatField(null=True, blank=True, help_text="Max value for 'between' operator")
    
    # Action to take
    action_type = models.CharField(max_length=50, choices=ACTION_TYPE_CHOICES)
    action_parameters = models.JSONField(default=dict, help_text="Action parameters")
    
    # Rule settings
    priority = models.IntegerField(default=5, validators=[MinValueValidator(1), MaxValueValidator(10)])
    cooldown_period = models.DurationField(default=timezone.timedelta(minutes=30), help_text="Minimum time between rule executions")
    
    # Execution tracking
    last_executed = models.DateTimeField(null=True, blank=True)
    execution_count = models.IntegerField(default=0)
    success_count = models.IntegerField(default=0)
    
    # Associated devices/farms
    target_devices = models.ManyToManyField('iot_integration.IoTDevice', blank=True, related_name='weather_rules')
    target_farms = models.ManyToManyField('iot_integration.Farm', blank=True, related_name='weather_rules')
    
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Weather Automation Rule'
        verbose_name_plural = 'Weather Automation Rules'
        indexes = [
            models.Index(fields=['is_active', 'condition_type']),
            models.Index(fields=['priority']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.get_condition_type_display()} {self.get_operator_display()} {self.threshold_value}"


class WeatherAutomationLog(models.Model):
    """Log of weather automation rule executions"""
    
    EXECUTION_STATUS_CHOICES = [
        ('success', 'Success'),
        ('failed', 'Failed'),
        ('skipped', 'Skipped'),
        ('error', 'Error'),
    ]
    
    rule = models.ForeignKey(WeatherAutomationRule, on_delete=models.CASCADE, related_name='execution_logs')
    
    # Execution details
    executed_at = models.DateTimeField(default=timezone.now)
    execution_status = models.CharField(max_length=20, choices=EXECUTION_STATUS_CHOICES)
    
    # Weather conditions at execution
    weather_data = models.JSONField(help_text="Weather data at time of execution")
    condition_met = models.BooleanField(help_text="Whether the condition was met")
    condition_value = models.FloatField(help_text="Actual value that triggered the rule")
    
    # Action taken
    action_taken = models.JSONField(help_text="Details of action taken")
    action_result = models.JSONField(default=dict, help_text="Result of the action")
    
    # Error handling
    error_message = models.TextField(blank=True, help_text="Error message if execution failed")
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Weather Automation Log'
        verbose_name_plural = 'Weather Automation Logs'
        indexes = [
            models.Index(fields=['rule', 'executed_at']),
            models.Index(fields=['execution_status']),
        ]
    
    def __str__(self):
        return f"{self.rule.name} - {self.executed_at.strftime('%Y-%m-%d %H:%M')} - {self.execution_status}"
