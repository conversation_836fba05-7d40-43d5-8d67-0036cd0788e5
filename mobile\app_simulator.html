<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Shrimp Farm Field Worker App - Simulator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
        }

        .phone-screen {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: #1976D2;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }

        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }

        .screen-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            padding: 20px;
            color: white;
        }

        .greeting {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .user-name {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .date {
            font-size: 14px;
            opacity: 0.8;
        }

        .offline-indicator {
            background: rgba(255, 152, 0, 0.9);
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-top: 12px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .search-container {
            padding: 16px;
            background: white;
        }

        .search-box {
            background: #f8f8f8;
            border-radius: 25px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            border: none;
            font-size: 16px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            padding: 0 16px 16px;
        }

        .stat-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .stat-number {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            font-weight: 600;
        }

        .progress-card {
            background: white;
            margin: 0 16px 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .progress-title {
            font-size: 16px;
            font-weight: 600;
        }

        .progress-rate {
            font-size: 24px;
            font-weight: 700;
            color: #4CAF50;
        }

        .progress-bar {
            height: 8px;
            background: #E0E0E0;
            border-radius: 4px;
            margin-bottom: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 85%;
            border-radius: 4px;
        }

        .quick-actions {
            padding: 0 16px 16px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            padding: 0 4px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .action-card {
            background: white;
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .action-card:active {
            transform: scale(0.95);
        }

        .action-icon {
            font-size: 24px;
            color: #2196F3;
            margin-bottom: 8px;
        }

        .action-text {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }

        .priority-tasks {
            padding: 0 16px 16px;
        }

        .task-card {
            background: white;
            margin-bottom: 8px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #F44336;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .task-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            flex: 1;
        }

        .priority-badge {
            background: #F44336;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }

        .task-details {
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }

        .task-actions {
            display: flex;
            gap: 8px;
        }

        .task-btn {
            padding: 8px 12px;
            border-radius: 20px;
            border: none;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .btn-start {
            background: #2196F3;
            color: white;
        }

        .btn-cancel {
            background: #F44336;
            color: white;
        }

        .bottom-nav {
            height: 80px;
            background: white;
            display: flex;
            border-top: 1px solid #e0e0e0;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .nav-item.active {
            color: #2196F3;
        }

        .nav-item:not(.active) {
            color: #757575;
        }

        .nav-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .nav-label {
            font-size: 12px;
            font-weight: 600;
        }

        .screen {
            display: none;
        }

        .screen.active {
            display: block;
        }

        .task-list-screen {
            padding: 16px;
        }

        .filter-chips {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .filter-chip {
            background: #f0f0f0;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            cursor: pointer;
            border: none;
        }

        .filter-chip.active {
            background: #2196F3;
            color: white;
        }

        .task-list-card {
            background: white;
            margin-bottom: 12px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .urgent { border-left: 4px solid #F44336; }
        .high { border-left: 4px solid #FF9800; }
        .medium { border-left: 4px solid #2196F3; }
        .completed { border-left: 4px solid #4CAF50; }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .screen.active {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="notch"></div>
        <div class="phone-screen">
            <!-- Status Bar -->
            <div class="status-bar">
                <div>🔋100% 📶</div>
                <div>2:30 PM</div>
            </div>

            <!-- Dashboard Screen -->
            <div class="screen active" id="dashboard">
                <div class="screen-content">
                    <div class="header">
                        <div class="greeting">Good Afternoon</div>
                        <div class="user-name">Field Worker</div>
                        <div class="date">Wednesday, January 17, 2025</div>
                        <div class="offline-indicator">
                            🔄 Working Offline - 3 pending sync
                        </div>
                    </div>

                    <div class="search-container">
                        <div class="search-box">
                            🔍 <input type="text" placeholder="Search tasks..." style="border: none; background: none; outline: none; flex: 1;">
                        </div>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-card" style="background: #E3F2FD;">
                            <div class="stat-icon">📅</div>
                            <div class="stat-number">12</div>
                            <div class="stat-label">Today's Tasks</div>
                        </div>
                        <div class="stat-card" style="background: #FFF3E0;">
                            <div class="stat-icon">▶️</div>
                            <div class="stat-number">3</div>
                            <div class="stat-label">In Progress</div>
                        </div>
                        <div class="stat-card" style="background: #E8F5E8;">
                            <div class="stat-icon">✅</div>
                            <div class="stat-number">8</div>
                            <div class="stat-label">Completed</div>
                        </div>
                        <div class="stat-card" style="background: #FFEBEE;">
                            <div class="stat-icon">⚠️</div>
                            <div class="stat-number">2</div>
                            <div class="stat-label">Overdue</div>
                        </div>
                    </div>

                    <div class="progress-card">
                        <div class="progress-header">
                            <div class="progress-title">Completion Rate</div>
                            <div class="progress-rate">85%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <div style="font-size: 12px; color: #666;">8 of 12 tasks completed</div>
                    </div>

                    <div class="quick-actions">
                        <div class="section-title">Quick Actions</div>
                        <div class="actions-grid">
                            <div class="action-card" onclick="showNotification('Creating new task...')">
                                <div class="action-icon">➕</div>
                                <div class="action-text">Create Task</div>
                            </div>
                            <div class="action-card" onclick="showNotification('Opening QR scanner...')">
                                <div class="action-icon">📷</div>
                                <div class="action-text">Scan QR</div>
                            </div>
                            <div class="action-card" onclick="showNotification('Starting water quality test...')">
                                <div class="action-icon">💧</div>
                                <div class="action-text">Water Test</div>
                            </div>
                            <div class="action-card" onclick="showNotification('Opening reports...')">
                                <div class="action-icon">📊</div>
                                <div class="action-text">Reports</div>
                            </div>
                        </div>
                    </div>

                    <div class="priority-tasks">
                        <div class="section-title">Priority Tasks</div>
                        <div class="task-card">
                            <div class="task-header">
                                <div class="task-title">💧 Water Quality Check</div>
                                <div class="priority-badge">URGENT</div>
                            </div>
                            <div class="task-details">📍 Pond A-1 • ⏰ Due: 14:00 • ⏱️ 30 min</div>
                            <div class="task-actions">
                                <button class="task-btn btn-start" onclick="startTask('Water Quality Check')">
                                    ▶️ Start
                                </button>
                                <button class="task-btn btn-cancel" onclick="cancelTask('Water Quality Check')">
                                    ❌ Cancel
                                </button>
                            </div>
                        </div>

                        <div class="task-card" style="border-left-color: #FF9800;">
                            <div class="task-header">
                                <div class="task-title">🔧 Equipment Maintenance</div>
                                <div class="priority-badge" style="background: #FF9800;">HIGH</div>
                            </div>
                            <div class="task-details">📍 Pump Station • ⏰ Due: 16:30 • ⏱️ 1 hour</div>
                            <div class="task-actions">
                                <button class="task-btn btn-start" onclick="startTask('Equipment Maintenance')">
                                    ▶️ Start
                                </button>
                                <button class="task-btn btn-cancel" onclick="cancelTask('Equipment Maintenance')">
                                    ❌ Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Task List Screen -->
            <div class="screen" id="tasks">
                <div class="screen-content">
                    <div class="header" style="padding: 16px 20px;">
                        <div style="display: flex; align-items: center; gap: 16px;">
                            <div style="font-size: 20px; font-weight: 700;">Tasks</div>
                            <div style="margin-left: auto;">🔍</div>
                        </div>
                    </div>

                    <div class="task-list-screen">
                        <div class="search-container" style="padding: 0; margin-bottom: 16px;">
                            <div class="search-box">
                                🔍 <input type="text" placeholder="Search tasks..." style="border: none; background: none; outline: none; flex: 1;">
                            </div>
                        </div>

                        <div class="filter-chips">
                            <button class="filter-chip active">All</button>
                            <button class="filter-chip">Today</button>
                            <button class="filter-chip">Pending</button>
                            <button class="filter-chip">Active</button>
                            <button class="filter-chip">Overdue</button>
                            <button class="filter-chip">Completed</button>
                        </div>

                        <div class="task-list-card urgent">
                            <div class="task-header">
                                <div class="task-title">💧 Water Quality Check</div>
                                <div class="priority-badge">URGENT</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                                Check pH and oxygen levels in main pond
                            </div>
                            <div class="task-details">📍 Pond A-1 • ⏰ Today 14:00 • ⏱️ 30 min</div>
                            <div style="font-size: 12px; color: #FF9800; margin-bottom: 12px;">🔄 Pending Sync</div>
                            <div class="task-actions">
                                <button class="task-btn btn-start">▶️ Start</button>
                                <button class="task-btn btn-cancel">❌ Cancel</button>
                            </div>
                        </div>

                        <div class="task-list-card high">
                            <div class="task-header">
                                <div class="task-title">🍤 Feeding Schedule</div>
                                <div class="priority-badge" style="background: #FF9800;">HIGH</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                                Morning feeding - 50kg premium pellets
                            </div>
                            <div class="task-details">📍 Pond B-2 • ⏰ Today 08:00 • ⏱️ 45 min</div>
                            <div style="font-size: 12px; color: #4CAF50; margin-bottom: 12px;">✅ Ready to Complete</div>
                            <div class="task-actions">
                                <button class="task-btn" style="background: #4CAF50; color: white;">✅ Complete</button>
                            </div>
                        </div>

                        <div class="task-list-card medium">
                            <div class="task-header">
                                <div class="task-title">🧹 Pond Cleaning</div>
                                <div class="priority-badge" style="background: #2196F3;">MEDIUM</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                                Weekly cleaning and debris removal
                            </div>
                            <div class="task-details">📍 Pond C-1 • ⏰ Tomorrow 09:00 • ⏱️ 2 hours</div>
                            <div class="task-actions">
                                <button class="task-btn btn-start">▶️ Start</button>
                                <button class="task-btn" style="background: #666; color: white;">📝 Edit</button>
                            </div>
                        </div>

                        <div class="task-list-card completed">
                            <div class="task-header">
                                <div class="task-title">🔍 Disease Inspection</div>
                                <div class="priority-badge" style="background: #4CAF50;">COMPLETED</div>
                            </div>
                            <div style="font-size: 14px; color: #666; margin-bottom: 8px;">
                                Weekly health check - all clear
                            </div>
                            <div class="task-details">📍 All Ponds • ✓ Yesterday 16:30</div>
                            <div style="font-size: 12px; color: #4CAF50; margin-bottom: 12px;">📸 3 photos • 📊 All measurements recorded</div>
                            <div class="task-actions">
                                <button class="task-btn" style="background: #2196F3; color: white;">👁️ View Report</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Navigation -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="switchScreen('dashboard', this)">
                    <div class="nav-icon">📊</div>
                    <div class="nav-label">Dashboard</div>
                </div>
                <div class="nav-item" onclick="switchScreen('tasks', this)">
                    <div class="nav-icon">📋</div>
                    <div class="nav-label">Tasks</div>
                </div>
                <div class="nav-item" onclick="showNotification('QR Scanner opening...')">
                    <div class="nav-icon">📷</div>
                    <div class="nav-label">Scanner</div>
                </div>
                <div class="nav-item" onclick="showNotification('Reports loading...')">
                    <div class="nav-icon">📈</div>
                    <div class="nav-label">Reports</div>
                </div>
                <div class="nav-item" onclick="showNotification('Profile opening...')">
                    <div class="nav-icon">👤</div>
                    <div class="nav-label">Profile</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchScreen(screenId, navElement) {
            // Hide all screens
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            
            // Show selected screen
            document.getElementById(screenId).classList.add('active');
            
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            navElement.classList.add('active');
        }

        function startTask(taskName) {
            showNotification(`Starting task: ${taskName}`);
        }

        function cancelTask(taskName) {
            if (confirm(`Are you sure you want to cancel "${taskName}"?`)) {
                showNotification(`Task cancelled: ${taskName}`);
            }
        }

        function showNotification(message) {
            // Create notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 100px;
                left: 50%;
                transform: translateX(-50%);
                background: #333;
                color: white;
                padding: 12px 20px;
                border-radius: 25px;
                font-size: 14px;
                z-index: 1000;
                animation: slideDown 0.3s ease-out;
            `;
            notification.textContent = message;
            
            // Add animation keyframes
            if (!document.querySelector('#notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    @keyframes slideDown {
                        from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
                        to { opacity: 1; transform: translateX(-50%) translateY(0); }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Add touch feedback
        document.addEventListener('touchstart', function(e) {
            if (e.target.classList.contains('action-card') || 
                e.target.classList.contains('task-btn') ||
                e.target.classList.contains('nav-item')) {
                e.target.style.transform = 'scale(0.95)';
            }
        });

        document.addEventListener('touchend', function(e) {
            if (e.target.classList.contains('action-card') || 
                e.target.classList.contains('task-btn') ||
                e.target.classList.contains('nav-item')) {
                setTimeout(() => {
                    e.target.style.transform = 'scale(1)';
                }, 100);
            }
        });

        // Filter chips functionality
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', function() {
                document.querySelectorAll('.filter-chip').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                showNotification(`Filtering by: ${this.textContent}`);
            });
        });
    </script>
</body>
</html>
