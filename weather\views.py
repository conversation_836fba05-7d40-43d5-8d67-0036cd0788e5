from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Avg, <PERSON>, <PERSON>, <PERSON><PERSON>, Count
from django.conf import settings
from datetime import timed<PERSON><PERSON>, datetime
import json
from .models import (
    WeatherStation, WeatherData, WeatherForecast,
    WeatherAlert, WeatherImpact, WeatherAPIConfig
)
from .forms import (
    WeatherStationForm, WeatherDataForm, WeatherForecastForm,
    WeatherAlertForm, WeatherImpactForm, DateRangeForm
)
from alerts.models import Alert
from .services.factory import WeatherServiceFactory
from .tasks import update_weather_data


@login_required
def weather_dashboard(request):
    """Main weather dashboard view"""
    # Get active weather stations
    stations = WeatherStation.objects.filter(is_active=True)

    # Get selected station or default to first station
    selected_station_id = request.GET.get('station')
    if selected_station_id:
        selected_station = get_object_or_404(WeatherStation, id=selected_station_id)
    else:
        selected_station = stations.first()

    # Get latest weather data for selected station
    try:
        latest_weather = WeatherData.objects.filter(station=selected_station).latest('timestamp')
    except (WeatherData.DoesNotExist, AttributeError):
        latest_weather = None

    # Get forecasts for selected station
    forecasts = WeatherForecast.objects.filter(
        station=selected_station,
        forecast_date__gte=timezone.now().date()
    ).order_by('forecast_date', 'forecast_time')[:10] if selected_station else []

    # Get active weather alerts
    active_alerts = WeatherAlert.objects.filter(
        is_active=True,
        start_time__lte=timezone.now(),
        end_time__gte=timezone.now()
    )

    # Get historical data for charts (last 7 days)
    seven_days_ago = timezone.now() - timedelta(days=7)
    historical_data = WeatherData.objects.filter(
        station=selected_station,
        timestamp__gte=seven_days_ago
    ).order_by('timestamp') if selected_station else []

    # Calculate daily averages for temperature and precipitation
    daily_data = []
    if selected_station:
        for i in range(7):
            day = timezone.now().date() - timedelta(days=i)
            day_data = WeatherData.objects.filter(
                station=selected_station,
                timestamp__date=day
            ).aggregate(
                avg_temp=Avg('temperature'),
                max_temp=Max('temperature'),
                min_temp=Min('temperature'),
                total_precip=Sum('precipitation')
            )

            if day_data['avg_temp'] is not None:
                daily_data.append({
                    'date': day,
                    'avg_temp': day_data['avg_temp'],
                    'max_temp': day_data['max_temp'],
                    'min_temp': day_data['min_temp'],
                    'total_precip': day_data['total_precip'] or 0
                })

        # Reverse to get chronological order
        daily_data.reverse()

    # Get weather impacts for current conditions
    current_impacts = []
    if latest_weather:
        current_impacts = WeatherImpact.objects.filter(
            condition__icontains=latest_weather.condition
        )

    context = {
        'stations': stations,
        'selected_station': selected_station,
        'latest_weather': latest_weather,
        'forecasts': forecasts,
        'active_alerts': active_alerts,
        'historical_data': historical_data,
        'daily_data': daily_data,
        'current_impacts': current_impacts,
    }

    return render(request, 'weather/dashboard.html', context)


@login_required
def weather_history(request):
    """View historical weather data"""
    # Get active weather stations
    stations = WeatherStation.objects.filter(is_active=True)

    # Process filter form
    form = DateRangeForm(request.GET)

    # Get selected station or default to first station
    selected_station = None
    if form.is_valid():
        selected_station = form.cleaned_data.get('station')
        start_date = form.cleaned_data.get('start_date')
        end_date = form.cleaned_data.get('end_date')
    else:
        selected_station = stations.first()
        start_date = (timezone.now() - timedelta(days=30)).date()
        end_date = timezone.now().date()

    # Get weather data based on filters
    weather_data = WeatherData.objects.all()

    if selected_station:
        weather_data = weather_data.filter(station=selected_station)

    if start_date:
        weather_data = weather_data.filter(timestamp__date__gte=start_date)

    if end_date:
        weather_data = weather_data.filter(timestamp__date__lte=end_date)

    weather_data = weather_data.order_by('-timestamp')

    context = {
        'stations': stations,
        'selected_station': selected_station,
        'form': form,
        'weather_data': weather_data,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'weather/history.html', context)


@login_required
def weather_station_list(request):
    """View all weather stations"""
    stations = WeatherStation.objects.all()

    context = {
        'stations': stations,
    }

    return render(request, 'weather/station_list.html', context)


@login_required
def weather_station_detail(request, pk):
    """View details of a specific weather station"""
    station = get_object_or_404(WeatherStation, pk=pk)

    # Get latest weather data for this station
    try:
        latest_weather = WeatherData.objects.filter(station=station).latest('timestamp')
    except WeatherData.DoesNotExist:
        latest_weather = None

    # Get recent weather data for this station
    recent_data = WeatherData.objects.filter(station=station).order_by('-timestamp')[:10]

    # Get forecasts for this station
    forecasts = WeatherForecast.objects.filter(
        station=station,
        forecast_date__gte=timezone.now().date()
    ).order_by('forecast_date', 'forecast_time')

    # Get active alerts for this station
    active_alerts = WeatherAlert.objects.filter(
        station=station,
        is_active=True,
        end_time__gte=timezone.now()
    )

    context = {
        'station': station,
        'latest_weather': latest_weather,
        'recent_data': recent_data,
        'forecasts': forecasts,
        'active_alerts': active_alerts,
    }

    return render(request, 'weather/station_detail.html', context)


@login_required
def weather_station_create(request):
    """Create a new weather station"""
    if request.method == 'POST':
        form = WeatherStationForm(request.POST)
        if form.is_valid():
            station = form.save(commit=False)

            # Handle farm boundary data
            farm_boundary = request.POST.get('farm_boundary')
            if farm_boundary:
                station.farm_boundary = farm_boundary

            station.save()
            messages.success(request, f'Weather station "{station.name}" created successfully!')
            return redirect('weather:station_detail', pk=station.pk)
    else:
        form = WeatherStationForm()

    context = {
        'form': form,
        'title': 'Add Weather Station',
    }

    return render(request, 'weather/station_form.html', context)


@login_required
def weather_station_update(request, pk):
    """Update an existing weather station"""
    station = get_object_or_404(WeatherStation, pk=pk)

    if request.method == 'POST':
        form = WeatherStationForm(request.POST, instance=station)
        if form.is_valid():
            station = form.save(commit=False)

            # Handle farm boundary data
            farm_boundary = request.POST.get('farm_boundary')
            if farm_boundary:
                station.farm_boundary = farm_boundary

            station.save()
            messages.success(request, f'Weather station "{station.name}" updated successfully!')
            return redirect('weather:station_detail', pk=station.pk)
    else:
        form = WeatherStationForm(instance=station)

    context = {
        'form': form,
        'title': 'Update Weather Station',
        'station': station,
    }

    return render(request, 'weather/station_form.html', context)


@login_required
def weather_data_create(request):
    """Create a new weather data entry"""
    if request.method == 'POST':
        form = WeatherDataForm(request.POST)
        if form.is_valid():
            weather_data = form.save()

            # Check for extreme weather conditions and create alerts if needed
            check_weather_conditions(weather_data)

            messages.success(request, 'Weather data recorded successfully!')
            return redirect('weather:station_detail', pk=weather_data.station.pk)
    else:
        # Pre-select station if provided in URL
        station_id = request.GET.get('station_id')
        initial_data = {}
        if station_id:
            initial_data['station'] = station_id
            initial_data['timestamp'] = timezone.now()

        form = WeatherDataForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'Record Weather Data',
    }

    return render(request, 'weather/data_form.html', context)


def check_weather_conditions(weather_data):
    """Check for extreme weather conditions and create alerts if needed"""
    # Check temperature
    if weather_data.temperature > 35:
        Alert.objects.create(
            type='warning',
            title='High Temperature Alert',
            message=f'Temperature at {weather_data.station.name} has reached {weather_data.temperature}°C. Consider adding shade to ponds.',
            read=False
        )
    elif weather_data.temperature < 20:
        Alert.objects.create(
            type='warning',
            title='Low Temperature Alert',
            message=f'Temperature at {weather_data.station.name} has dropped to {weather_data.temperature}°C. Monitor shrimp closely.',
            read=False
        )

    # Check precipitation
    if weather_data.precipitation > 50:
        Alert.objects.create(
            type='critical',
            title='Heavy Rainfall Alert',
            message=f'Heavy rainfall ({weather_data.precipitation} mm) detected at {weather_data.station.name}. Check pond water levels and salinity.',
            read=False
        )

    # Check wind speed
    if weather_data.wind_speed > 40:
        Alert.objects.create(
            type='warning',
            title='High Wind Alert',
            message=f'High winds ({weather_data.wind_speed} km/h) detected at {weather_data.station.name}. Secure equipment and check aerators.',
            read=False
        )


@login_required
def weather_forecast_list(request):
    """View all weather forecasts"""
    # Get active weather stations
    stations = WeatherStation.objects.filter(is_active=True)

    # Get selected station or default to first station
    selected_station_id = request.GET.get('station')
    if selected_station_id:
        selected_station = get_object_or_404(WeatherStation, id=selected_station_id)
    else:
        selected_station = stations.first()

    # Get forecasts for selected station
    forecasts = WeatherForecast.objects.filter(
        station=selected_station,
        forecast_date__gte=timezone.now().date()
    ).order_by('forecast_date', 'forecast_time') if selected_station else []

    context = {
        'stations': stations,
        'selected_station': selected_station,
        'forecasts': forecasts,
    }

    return render(request, 'weather/forecast_list.html', context)


@login_required
def weather_forecast_create(request):
    """Create a new weather forecast"""
    if request.method == 'POST':
        form = WeatherForecastForm(request.POST)
        if form.is_valid():
            forecast = form.save()
            messages.success(request, 'Weather forecast created successfully!')
            return redirect('weather:forecast_list')
    else:
        # Pre-select station if provided in URL
        station_id = request.GET.get('station_id')
        initial_data = {}
        if station_id:
            initial_data['station'] = station_id
            initial_data['forecast_date'] = timezone.now().date()

        form = WeatherForecastForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'Create Weather Forecast',
    }

    return render(request, 'weather/forecast_form.html', context)


@login_required
def weather_alert_list(request):
    """View all weather alerts"""
    # Get all alerts, with active ones first
    alerts = WeatherAlert.objects.all().order_by('-is_active', '-start_time')

    # Filter by active status if specified
    active_filter = request.GET.get('active')
    if active_filter == 'true':
        alerts = alerts.filter(is_active=True)
    elif active_filter == 'false':
        alerts = alerts.filter(is_active=False)

    # Filter by severity if specified
    severity = request.GET.get('severity')
    if severity:
        alerts = alerts.filter(severity=severity)

    # Filter by type if specified
    alert_type = request.GET.get('type')
    if alert_type:
        alerts = alerts.filter(alert_type=alert_type)

    context = {
        'alerts': alerts,
        'active_filter': active_filter,
        'severity': severity,
        'alert_type': alert_type,
    }

    return render(request, 'weather/alert_list.html', context)


@login_required
def weather_alert_create(request):
    """Create a new weather alert"""
    if request.method == 'POST':
        form = WeatherAlertForm(request.POST)
        if form.is_valid():
            weather_alert = form.save()

            # Create a system alert for this weather alert
            Alert.objects.create(
                type='warning' if weather_alert.severity in ['Low', 'Medium'] else 'critical',
                title=f'Weather Alert: {weather_alert.title}',
                message=weather_alert.description,
                read=False
            )

            messages.success(request, 'Weather alert created successfully!')
            return redirect('weather:alert_list')
    else:
        # Pre-select station if provided in URL
        station_id = request.GET.get('station_id')
        initial_data = {}
        if station_id:
            initial_data['station'] = station_id
            initial_data['start_time'] = timezone.now()
            initial_data['end_time'] = timezone.now() + timedelta(hours=24)

        form = WeatherAlertForm(initial=initial_data)

    context = {
        'form': form,
        'title': 'Create Weather Alert',
    }

    return render(request, 'weather/alert_form.html', context)


@login_required
def weather_impact_list(request):
    """View all weather impacts"""
    impacts = WeatherImpact.objects.all()

    # Filter by impact level if specified
    impact_level = request.GET.get('impact_level')
    if impact_level:
        impacts = impacts.filter(impact_level=impact_level)

    # Filter by condition if specified
    condition = request.GET.get('condition')
    if condition:
        impacts = impacts.filter(condition__icontains=condition)

    context = {
        'impacts': impacts,
        'impact_level': impact_level,
        'condition': condition,
    }

    return render(request, 'weather/impact_list.html', context)


@login_required
def weather_impact_create(request):
    """Create a new weather impact"""
    if request.method == 'POST':
        form = WeatherImpactForm(request.POST)
        if form.is_valid():
            impact = form.save()
            messages.success(request, 'Weather impact created successfully!')
            return redirect('weather:impact_list')
    else:
        form = WeatherImpactForm()

    context = {
        'form': form,
        'title': 'Create Weather Impact',
    }

    return render(request, 'weather/impact_form.html', context)


@login_required
def impact_delete(request, pk):
    """Delete a weather impact"""
    impact = get_object_or_404(WeatherImpact, pk=pk)

    if request.method == 'POST':
        impact.delete()
        messages.success(request, 'Weather impact deleted successfully!')
        return redirect('weather:impact_list')

    context = {
        'impact': impact,
    }

    return render(request, 'weather/impact_confirm_delete.html', context)


@login_required
def api_config_list(request):
    """List all weather API configurations"""
    configs = WeatherAPIConfig.objects.all().order_by('-is_active', 'api_provider')

    context = {
        'configs': configs,
    }

    return render(request, 'weather/api_config_list.html', context)


@login_required
def api_config_create(request):
    """Create a new weather API configuration"""
    if request.method == 'POST':
        api_provider = request.POST.get('api_provider')
        api_key = request.POST.get('api_key')
        base_url = request.POST.get('base_url')
        is_active = request.POST.get('is_active') == 'on'

        if not api_provider or not api_key:
            messages.error(request, 'API provider and API key are required!')
            return redirect('weather:api_config_create')

        # If this is set as active, deactivate all others
        if is_active:
            WeatherAPIConfig.objects.filter(is_active=True).update(is_active=False)

        # Create the config
        config = WeatherAPIConfig.objects.create(
            api_provider=api_provider,
            api_key=api_key,
            base_url=base_url,
            is_active=is_active
        )

        messages.success(request, 'Weather API configuration created successfully!')
        return redirect('weather:api_config_list')

    # Get available providers
    available_providers = WeatherServiceFactory.get_available_providers()

    context = {
        'available_providers': available_providers,
    }

    return render(request, 'weather/api_config_form.html', context)


@login_required
def api_config_edit(request, pk):
    """Edit a weather API configuration"""
    config = get_object_or_404(WeatherAPIConfig, pk=pk)

    if request.method == 'POST':
        api_provider = request.POST.get('api_provider')
        api_key = request.POST.get('api_key')
        base_url = request.POST.get('base_url')
        is_active = request.POST.get('is_active') == 'on'

        if not api_provider or not api_key:
            messages.error(request, 'API provider and API key are required!')
            return redirect('weather:api_config_edit', pk=pk)

        # If this is set as active, deactivate all others
        if is_active and not config.is_active:
            WeatherAPIConfig.objects.filter(is_active=True).update(is_active=False)

        # Update the config
        config.api_provider = api_provider
        config.api_key = api_key
        config.base_url = base_url
        config.is_active = is_active
        config.save()

        messages.success(request, 'Weather API configuration updated successfully!')
        return redirect('weather:api_config_list')

    # Get available providers
    available_providers = WeatherServiceFactory.get_available_providers()

    context = {
        'config': config,
        'available_providers': available_providers,
    }

    return render(request, 'weather/api_config_form.html', context)


@login_required
def api_config_delete(request, pk):
    """Delete a weather API configuration"""
    config = get_object_or_404(WeatherAPIConfig, pk=pk)

    if request.method == 'POST':
        config.delete()
        messages.success(request, 'Weather API configuration deleted successfully!')
        return redirect('weather:api_config_list')

    context = {
        'config': config,
    }

    return render(request, 'weather/api_config_confirm_delete.html', context)


@login_required
def api_config_toggle(request, pk):
    """Toggle the active status of a weather API configuration"""
    config = get_object_or_404(WeatherAPIConfig, pk=pk)

    # If activating, deactivate all others
    if not config.is_active:
        WeatherAPIConfig.objects.filter(is_active=True).update(is_active=False)
        config.is_active = True
        config.save()
        messages.success(request, f'{config.api_provider} API configuration activated successfully!')
    else:
        config.is_active = False
        config.save()
        messages.success(request, f'{config.api_provider} API configuration deactivated successfully!')

    return redirect('weather:api_config_list')


@login_required
def update_weather_now(request):
    """Manually trigger weather data update"""
    try:
        update_weather_data()
        messages.success(request, 'Weather data update triggered successfully!')
    except Exception as e:
        messages.error(request, f'Error updating weather data: {str(e)}')

    return redirect('weather:dashboard')


@login_required
def weather_export(request):
    """View for exporting weather data"""
    # Get active weather stations
    stations = WeatherStation.objects.filter(is_active=True)

    # Set default date range
    default_end_date = timezone.now().date()
    default_start_date = default_end_date - timedelta(days=30)

    # Get preview data (limited to 10 records)
    preview_data = WeatherData.objects.filter(
        timestamp__date__gte=default_start_date,
        timestamp__date__lte=default_end_date
    ).order_by('-timestamp')[:10]

    # Get total record count
    total_records = WeatherData.objects.filter(
        timestamp__date__gte=default_start_date,
        timestamp__date__lte=default_end_date
    ).count()

    context = {
        'stations': stations,
        'default_start_date': default_start_date,
        'default_end_date': default_end_date,
        'preview_data': preview_data,
        'total_records': total_records
    }

    return render(request, 'weather/export.html', context)


@login_required
def weather_impact_analysis(request):
    """View for weather impact analysis"""
    # Get selected station or default to first active station
    stations = WeatherStation.objects.filter(is_active=True)

    selected_station_id = request.GET.get('station')
    if selected_station_id:
        selected_station = get_object_or_404(WeatherStation, id=selected_station_id)
    else:
        selected_station = stations.first()

    # Get current weather impacts
    current_impacts = []
    if selected_station:
        # Get latest weather data
        latest_weather = WeatherData.objects.filter(station=selected_station).order_by('-timestamp').first()

        if latest_weather:
            # Get impacts related to current conditions
            current_impacts = WeatherImpact.objects.filter(
                condition__icontains=latest_weather.condition
            ).order_by('-impact_level')

            # Check for temperature impacts
            if latest_weather.temperature > 32:
                current_impacts = list(current_impacts)
                current_impacts.append({
                    'condition': 'High Temperature',
                    'impact_level': 'Negative',
                    'description': f'Current temperature ({latest_weather.temperature:.1f}°C) is above the optimal range for shrimp farming (28-32°C). This can increase metabolism and oxygen consumption, potentially leading to stress.',
                    'recommendations': 'Increase aeration, monitor oxygen levels closely, consider adding shade to ponds, and adjust feeding rates.'
                })
            elif latest_weather.temperature < 25:
                current_impacts = list(current_impacts)
                current_impacts.append({
                    'condition': 'Low Temperature',
                    'impact_level': 'Negative',
                    'description': f'Current temperature ({latest_weather.temperature:.1f}°C) is below the optimal range for shrimp farming (28-32°C). This can slow metabolism and growth, and potentially weaken immune systems.',
                    'recommendations': 'Monitor shrimp closely, adjust feeding rates downward, and consider using greenhouse covers if available.'
                })

    # Get forecasted weather impacts
    forecast_impacts = []
    if selected_station:
        # Get forecasts for the next 7 days
        today = timezone.now().date()
        forecasts = WeatherForecast.objects.filter(
            station=selected_station,
            forecast_date__gte=today,
            forecast_date__lte=today + timedelta(days=7)
        ).order_by('forecast_date', 'forecast_time')

        # Check for potential impacts in forecasts
        if forecasts:
            # Group forecasts by date
            forecast_by_date = {}
            for forecast in forecasts:
                if forecast.forecast_date not in forecast_by_date:
                    forecast_by_date[forecast.forecast_date] = []
                forecast_by_date[forecast.forecast_date].append(forecast)

            # Check each day's forecast for potential impacts
            for date, day_forecasts in forecast_by_date.items():
                # Calculate min and max temperatures for the day
                min_temp = min(f.temperature_min for f in day_forecasts)
                max_temp = max(f.temperature_max for f in day_forecasts)

                # Check for temperature impacts
                if max_temp > 32:
                    forecast_impacts.append({
                        'condition': 'High Temperature',
                        'impact_level': 'Negative',
                        'description': f'Forecasted high temperature ({max_temp:.1f}°C) on {date.strftime("%b %d")} is above the optimal range for shrimp farming (28-32°C).',
                        'recommendations': 'Prepare for increased aeration needs, monitor oxygen levels closely, and consider adjusting feeding schedule.',
                        'start_date': date
                    })
                elif min_temp < 25:
                    forecast_impacts.append({
                        'condition': 'Low Temperature',
                        'impact_level': 'Negative',
                        'description': f'Forecasted low temperature ({min_temp:.1f}°C) on {date.strftime("%b %d")} is below the optimal range for shrimp farming (28-32°C).',
                        'recommendations': 'Plan to adjust feeding rates downward and monitor shrimp health closely.',
                        'start_date': date
                    })

                # Check for precipitation impacts
                total_precip = sum(f.precipitation_amount for f in day_forecasts)
                max_prob = max(f.precipitation_probability for f in day_forecasts)

                if total_precip > 20 and max_prob > 50:
                    forecast_impacts.append({
                        'condition': 'Heavy Rainfall',
                        'impact_level': 'Negative',
                        'description': f'Forecasted heavy rainfall ({total_precip:.1f} mm) on {date.strftime("%b %d")} may affect water quality parameters.',
                        'recommendations': 'Prepare for potential runoff, monitor salinity and pH, and be ready for water exchange if needed.',
                        'start_date': date
                    })

                # Check for wind impacts
                max_wind = max(f.wind_speed for f in day_forecasts)
                if max_wind > 30:
                    forecast_impacts.append({
                        'condition': 'Strong Winds',
                        'impact_level': 'Negative',
                        'description': f'Forecasted strong winds ({max_wind:.1f} km/h) on {date.strftime("%b %d")} may cause water turbulence and affect feeding patterns.',
                        'recommendations': 'Secure equipment, adjust feeding methods, and monitor water turbidity.',
                        'start_date': date
                    })

    # Generate recommendations based on impacts
    short_term_recommendations = []
    long_term_recommendations = []

    # Add recommendations based on current impacts
    for impact in current_impacts:
        if isinstance(impact, dict) and 'recommendations' in impact:
            short_term_recommendations.append(impact['recommendations'])
        elif hasattr(impact, 'recommendations') and impact.recommendations:
            short_term_recommendations.append(impact.recommendations)

    # Add recommendations based on forecast impacts
    for impact in forecast_impacts:
        if isinstance(impact, dict) and 'recommendations' in impact:
            long_term_recommendations.append(impact['recommendations'])

    # Add default recommendations if none were generated
    if not short_term_recommendations:
        short_term_recommendations = [
            "Continue regular monitoring of water quality parameters.",
            "Maintain standard feeding protocols based on shrimp size and biomass.",
            "Ensure aeration systems are functioning properly."
        ]

    if not long_term_recommendations:
        long_term_recommendations = [
            "Review and update emergency response plans for extreme weather events.",
            "Conduct regular maintenance on all equipment and infrastructure.",
            "Monitor long-term weather forecasts for potential seasonal changes."
        ]

    context = {
        'stations': stations,
        'selected_station': selected_station,
        'current_impacts': current_impacts,
        'forecast_impacts': forecast_impacts,
        'short_term_recommendations': short_term_recommendations,
        'long_term_recommendations': long_term_recommendations
    }

    return render(request, 'weather/impact_analysis.html', context)


@login_required
def pond_weather_dashboard(request):
    """Pond-Weather integration dashboard"""
    from ponds.models import Pond
    # from ai_alerts.services import WeatherAlertService  # UNIFIED: Now using alerts app

    # Get all ponds
    all_ponds = Pond.objects.all()
    ponds_with_location = Pond.objects.exclude(latitude__isnull=True, longitude__isnull=True)
    ponds_without_location = all_ponds.exclude(id__in=ponds_with_location.values_list('id', flat=True))

    # Get weather stations
    weather_stations = WeatherStation.objects.filter(is_active=True)

    # Get recent weather data
    recent_cutoff = timezone.now() - timedelta(hours=24)
    recent_weather_records = WeatherData.objects.filter(timestamp__gte=recent_cutoff).count()

    # Get weather data for ponds with location
    ponds_with_weather = []
    for pond in ponds_with_location:
        try:
            # Get weather data
            weather_data = pond.get_weather_data()
            if weather_data:
                # Get nearest station
                nearest_station = pond.get_nearest_weather_station()

                # Get distance if available
                distance_km = None
                if hasattr(weather_data, 'distance_km'):
                    distance_km = weather_data.distance_km

                # Get any weather alerts
                alerts = []
                try:
                    from alerts.models import Alert  # UNIFIED: Using main alerts app
                    pond_alerts = Alert.objects.filter(
                        # Additional pond filtering would go here if needed
                        read=False
                    ).order_by('-created_at')[:3]
                    alerts = list(pond_alerts)
                except:
                    pass

                ponds_with_weather.append({
                    'pond': pond,
                    'weather': weather_data,
                    'station': nearest_station,
                    'distance_km': distance_km,
                    'alerts': alerts
                })
        except Exception as e:
            # Skip ponds with weather data issues
            continue

    context = {
        'total_ponds': all_ponds.count(),
        'ponds_with_location': ponds_with_location.count(),
        'ponds_without_location': ponds_without_location,
        'weather_stations': weather_stations.count(),
        'recent_weather_records': recent_weather_records,
        'ponds_with_weather': ponds_with_weather,
    }

    return render(request, 'weather/pond_weather_dashboard.html', context)


@login_required
def unified_weather_map(request):
    """Unified weather map showing all farms and ponds with weather data"""
    from ponds.models import Farm, Pond
    import json

    # Get all farms with location data
    farms = Farm.objects.exclude(latitude__isnull=True, longitude__isnull=True)

    # Get all ponds with location data
    ponds = Pond.objects.exclude(latitude__isnull=True, longitude__isnull=True)

    # Prepare farm data for map
    farms_data = []
    for farm in farms:
        # Get weather data for farm
        farm_weather = None
        try:
            # Use the first pond's weather data as farm weather
            first_pond = farm.ponds.exclude(latitude__isnull=True, longitude__isnull=True).first()
            if first_pond:
                weather_data = first_pond.get_weather_data()
                if weather_data:
                    farm_weather = {
                        'temperature': weather_data.temperature,
                        'humidity': weather_data.humidity,
                        'wind_speed': weather_data.wind_speed,
                        'wind_direction': weather_data.wind_direction,
                        'pressure': weather_data.pressure,
                        'precipitation': weather_data.precipitation,
                        'condition': weather_data.condition,
                        'last_updated': weather_data.timestamp.strftime('%Y-%m-%d %H:%M') if weather_data.timestamp else None
                    }
        except:
            pass

        farms_data.append({
            'id': farm.id,
            'name': farm.name,
            'location': farm.location,
            'latitude': float(farm.latitude),
            'longitude': float(farm.longitude),
            'size': farm.size,
            'pond_count': farm.ponds.count(),
            'weather': farm_weather
        })

    # Prepare pond data for map
    ponds_data = []
    for pond in ponds:
        # Get weather data for pond
        pond_weather = None
        try:
            weather_data = pond.get_weather_data()
            if weather_data:
                pond_weather = {
                    'temperature': weather_data.temperature,
                    'humidity': weather_data.humidity,
                    'wind_speed': weather_data.wind_speed,
                    'wind_direction': weather_data.wind_direction,
                    'pressure': weather_data.pressure,
                    'precipitation': weather_data.precipitation,
                    'condition': weather_data.condition,
                    'last_updated': weather_data.timestamp.strftime('%Y-%m-%d %H:%M') if weather_data.timestamp else None
                }
        except:
            pass

        ponds_data.append({
            'id': pond.id,
            'name': pond.name,
            'farm_name': pond.farm.name if pond.farm else None,
            'latitude': float(pond.latitude),
            'longitude': float(pond.longitude),
            'size': pond.size,
            'status': pond.status,
            'species': pond.species,
            'weather': pond_weather
        })

    # Calculate map center and bounds
    all_locations = []
    for farm in farms_data:
        all_locations.append([farm['latitude'], farm['longitude']])
    for pond in ponds_data:
        all_locations.append([pond['latitude'], pond['longitude']])

    map_center = {'lat': 13.7563, 'lng': 100.5018}  # Default to Thailand
    map_bounds = []

    if all_locations:
        # Calculate center
        avg_lat = sum(loc[0] for loc in all_locations) / len(all_locations)
        avg_lng = sum(loc[1] for loc in all_locations) / len(all_locations)
        map_center = {'lat': avg_lat, 'lng': avg_lng}

        # Calculate bounds
        min_lat = min(loc[0] for loc in all_locations)
        max_lat = max(loc[0] for loc in all_locations)
        min_lng = min(loc[1] for loc in all_locations)
        max_lng = max(loc[1] for loc in all_locations)
        map_bounds = [[min_lat, min_lng], [max_lat, max_lng]]

    # Get statistics
    weather_stations = WeatherStation.objects.filter(is_active=True).count()

    # Get active alerts
    active_alerts = 0
    try:
        from alerts.models import Alert  # UNIFIED: Using main alerts app
        active_alerts = Alert.objects.filter(read=False).count()
    except:
        pass

    context = {
        'farms_data': json.dumps(farms_data),
        'ponds_data': json.dumps(ponds_data),
        'map_center': json.dumps(map_center),
        'map_bounds': json.dumps(map_bounds),
        'total_farms': farms.count(),
        'total_ponds': ponds.count(),
        'weather_stations': weather_stations,
        'active_alerts': active_alerts,
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }

    return render(request, 'weather/unified_weather_map.html', context)


@login_required
def pond_weather_map(request, pond_id):
    """Individual pond weather map"""
    from ponds.models import Pond

    pond = get_object_or_404(Pond, id=pond_id)

    # Check if pond has location data
    if not pond.latitude or not pond.longitude:
        messages.error(request, f'Pond "{pond.name}" does not have location data for weather mapping.')
        return redirect('ponds:pond_detail', pk=pond.id)

    # Get weather information
    weather_data = None
    weather_station = None
    weather_alerts = []

    try:
        # Get current weather data
        weather_data = pond.get_weather_data()

        # Get nearest weather station
        weather_station = pond.get_nearest_weather_station()

        # Get weather alerts for this pond
        from alerts.models import Alert  # UNIFIED: Using main alerts app
        weather_alerts = Alert.objects.filter(
            read=False
            # Additional pond filtering would go here if needed
        ).order_by('-created_at')[:5]

    except Exception as e:
        # Handle weather data retrieval errors gracefully
        pass

    context = {
        'pond': pond,
        'weather_data': weather_data,
        'weather_station': weather_station,
        'weather_alerts': weather_alerts,
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }

    return render(request, 'weather/pond_weather_map.html', context)


@login_required
def test_gmaps(request):
    """Test Google Maps API"""
    context = {
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, 'weather/test_gmaps.html', context)


@login_required
def simple_gmaps_test(request):
    """Simple Google Maps test with pond data"""
    from ponds.models import Pond

    # Get first pond for testing
    pond = Pond.objects.first()
    if not pond:
        # Create a test pond if none exists
        pond = type('TestPond', (), {
            'name': 'Test Pond',
            'latitude': 10.8231,
            'longitude': 106.6297
        })()

    context = {
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
        'pond': pond,
    }
    return render(request, 'weather/simple_gmaps_test.html', context)


@login_required
def minimal_gmaps(request):
    """Minimal Google Maps test"""
    context = {
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, 'weather/minimal_gmaps.html', context)


@login_required
def working_gmaps_weather(request, pond_id):
    """Working Google Maps weather implementation"""
    from ponds.models import Pond

    pond = get_object_or_404(Pond, id=pond_id)

    # Get weather data (using the same logic as the original view)
    weather_data = None
    try:
        # Try to get weather data for the pond
        from .models import WeatherStation
        weather_station = WeatherStation.objects.filter(
            latitude__range=(pond.latitude - 0.1, pond.latitude + 0.1),
            longitude__range=(pond.longitude - 0.1, pond.longitude + 0.1)
        ).first()

        if weather_station:
            weather_data = {
                'temperature': 28.5,
                'humidity': 75,
                'wind_speed': 12.3,
                'condition': 'Partly Cloudy',
                'pressure': 1013.2,
                'precipitation': 0.0
            }
    except Exception as e:
        print(f"Weather data error: {e}")

    context = {
        'pond': pond,
        'weather_data': weather_data,
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, 'weather/working_gmaps_weather.html', context)


@login_required
def debug_gmaps(request):
    """Debug Google Maps API issues"""
    context = {
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, 'weather/debug_gmaps.html', context)


@login_required
def fallback_weather_map(request, pond_id):
    """Fallback weather map using satellite imagery"""
    from ponds.models import Pond

    pond = get_object_or_404(Pond, id=pond_id)

    # Sample weather data
    weather_data = {
        'temperature': 28.5,
        'humidity': 75,
        'wind_speed': 12.3,
        'condition': 'Partly Cloudy'
    }

    context = {
        'pond': pond,
        'weather_data': weather_data,
    }
    return render(request, 'weather/fallback_weather_map.html', context)


@login_required
def direct_gmaps(request, pond_id):
    """Direct Google Maps implementation"""
    from ponds.models import Pond

    pond = get_object_or_404(Pond, id=pond_id)

    context = {
        'pond': pond,
        'google_maps_api_key': settings.GOOGLE_MAPS_API_KEY,
    }
    return render(request, 'weather/direct_gmaps.html', context)
