<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Medicine Header -->
        <div class="medicine-header text-center">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <div class="d-flex align-items-center justify-content-center justify-content-md-start">
                        <i class="fas fa-pills medicine-icon me-3"></i>
                        <div class="text-start">
                            <h4 class="mb-0">Medicine</h4>
                            <p class="mb-0 opacity-75">Form Management</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h2 class="mb-0">{{ title }}</h2>
                    <p class="mb-0 opacity-75">Add or edit medicine information</p>
                </div>
                <div class="col-md-3">
                    <div class="text-center text-md-end">
                        <div class="d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="me-3">
                                <small class="d-block opacity-75">Form Entry</small>
                                <strong>Medicine</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex gap-2">
                    <a href="/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-home me-1"></i> Main Dashboard
                    </a>
                    <a href="/medicine/dashboard/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-pills me-1"></i> Medicine Dashboard
                    </a>
                    <a href="/medicine/medicine/" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i> Back to List
                    </a>
                </div>
                <div class="text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </div>
            </div>
        </nav>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="/medicine/medicine/" class="quick-action">
                <i class="fas fa-list"></i>
                Medicine List
            </a>
            <a href="/medicine/category/" class="quick-action">
                <i class="fas fa-tags"></i>
                Categories
            </a>
            <a href="/medicine/application/" class="quick-action">
                <i class="fas fa-clipboard-list"></i>
                Applications
            </a>
            <a href="/medicine/supplier/" class="quick-action">
                <i class="fas fa-truck"></i>
                Suppliers
            </a>
        </div>

        <div class="container-fluid">
    
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card">
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Medicine Name</label>
                                {{ form.name.errors }}
                                <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
                                {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6">
                                <label for="{{ form.category.id_for_label }}" class="form-label">Category</label>
                                {{ form.category.errors }}
                                <select name="{{ form.category.name }}" id="{{ form.category.id_for_label }}" class="form-select {% if form.category.errors %}is-invalid{% endif %}">
                                    {% for value, text in form.category.field.choices %}
                                    <option value="{{ value }}" {% if form.category.value|stringformat:"i" == value|stringformat:"i" %}selected{% endif %}>{{ text }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.category.help_text %}
                                <div class="form-text">{{ form.category.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="{{ form.stock_quantity.id_for_label }}" class="form-label">Stock Quantity</label>
                                {{ form.stock_quantity.errors }}
                                <input type="number" step="0.01" name="{{ form.stock_quantity.name }}" id="{{ form.stock_quantity.id_for_label }}" class="form-control {% if form.stock_quantity.errors %}is-invalid{% endif %}" value="{{ form.stock_quantity.value|default:'' }}" required>
                                {% if form.stock_quantity.help_text %}
                                <div class="form-text">{{ form.stock_quantity.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <label for="{{ form.unit.id_for_label }}" class="form-label">Unit</label>
                                {{ form.unit.errors }}
                                <input type="text" name="{{ form.unit.name }}" id="{{ form.unit.id_for_label }}" class="form-control {% if form.unit.errors %}is-invalid{% endif %}" value="{{ form.unit.value|default:'' }}" placeholder="e.g., kg, L, tablets" required>
                                {% if form.unit.help_text %}
                                <div class="form-text">{{ form.unit.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status.errors }}
                                <select name="{{ form.status.name }}" id="{{ form.status.id_for_label }}" class="form-select {% if form.status.errors %}is-invalid{% endif %}">
                                    {% for value, text in form.status.field.choices %}
                                    <option value="{{ value }}" {% if form.status.value == value %}selected{% endif %}>{{ text }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.status.help_text %}
                                <div class="form-text">{{ form.status.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="{{ form.expiry_date.id_for_label }}" class="form-label">Expiry Date</label>
                                {{ form.expiry_date.errors }}
                                <input type="date" name="{{ form.expiry_date.name }}" id="{{ form.expiry_date.id_for_label }}" class="form-control {% if form.expiry_date.errors %}is-invalid{% endif %}" value="{{ form.expiry_date.value|date:'Y-m-d'|default:'' }}">
                                {% if form.expiry_date.help_text %}
                                <div class="form-text">{{ form.expiry_date.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <label for="{{ form.purchase_date.id_for_label }}" class="form-label">Purchase Date</label>
                                {{ form.purchase_date.errors }}
                                <input type="date" name="{{ form.purchase_date.name }}" id="{{ form.purchase_date.id_for_label }}" class="form-control {% if form.purchase_date.errors %}is-invalid{% endif %}" value="{{ form.purchase_date.value|date:'Y-m-d'|default:'' }}">
                                {% if form.purchase_date.help_text %}
                                <div class="form-text">{{ form.purchase_date.help_text }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-4">
                                <label for="{{ form.purchase_price.id_for_label }}" class="form-label">Purchase Price</label>
                                {{ form.purchase_price.errors }}
                                <input type="number" step="0.01" name="{{ form.purchase_price.name }}" id="{{ form.purchase_price.id_for_label }}" class="form-control {% if form.purchase_price.errors %}is-invalid{% endif %}" value="{{ form.purchase_price.value|default:'' }}">
                                {% if form.purchase_price.help_text %}
                                <div class="form-text">{{ form.purchase_price.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.supplier.id_for_label }}" class="form-label">Supplier</label>
                            {{ form.supplier.errors }}
                            <input type="text" name="{{ form.supplier.name }}" id="{{ form.supplier.id_for_label }}" class="form-control {% if form.supplier.errors %}is-invalid{% endif %}" value="{{ form.supplier.value|default:'' }}">
                            {% if form.supplier.help_text %}
                            <div class="form-text">{{ form.supplier.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                            {{ form.description.errors }}
                            <textarea name="{{ form.description.name }}" id="{{ form.description.id_for_label }}" class="form-control {% if form.description.errors %}is-invalid{% endif %}" rows="3">{{ form.description.value|default:'' }}</textarea>
                            {% if form.description.help_text %}
                            <div class="form-text">{{ form.description.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.dosage_instructions.id_for_label }}" class="form-label">Dosage Instructions</label>
                            {{ form.dosage_instructions.errors }}
                            <textarea name="{{ form.dosage_instructions.name }}" id="{{ form.dosage_instructions.id_for_label }}" class="form-control {% if form.dosage_instructions.errors %}is-invalid{% endif %}" rows="3">{{ form.dosage_instructions.value|default:'' }}</textarea>
                            {% if form.dosage_instructions.help_text %}
                            <div class="form-text">{{ form.dosage_instructions.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Notes</label>
                            {{ form.notes.errors }}
                            <textarea name="{{ form.notes.name }}" id="{{ form.notes.id_for_label }}" class="form-control {% if form.notes.errors %}is-invalid{% endif %}" rows="3">{{ form.notes.value|default:'' }}</textarea>
                            {% if form.notes.help_text %}
                            <div class="form-text">{{ form.notes.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="/medicine/medicine/" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Medicine</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
        </div>
    </div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            document.getElementById('current-time').textContent = timeString;
        }
        
        updateTime();
        setInterval(updateTime, 1000);
    </script>
</body>
</html>
