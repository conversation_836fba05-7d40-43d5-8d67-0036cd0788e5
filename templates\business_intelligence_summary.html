<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Intelligence Summary - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .module-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        }
        
        .module-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
        }
        
        .module-card.pond-management::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .module-card.feed-management::before {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .module-card.water-quality::before {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .module-card.iot-integration::before {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .module-card.financial::before {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .module-card.reporting::before {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        }
        
        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .module-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 15px;
        }
        
        .module-icon.pond-management {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .module-icon.feed-management {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .module-icon.water-quality {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .module-icon.iot-integration {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }
        
        .module-icon.financial {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .module-icon.reporting {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
        }
        
        .module-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2d3748;
            margin: 0;
        }
        
        .module-description {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .module-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
        }
        
        .stat-label {
            color: #64748b;
            font-size: 0.8rem;
        }
        
        .module-actions {
            display: flex;
            gap: 10px;
        }
        
        .module-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            font-size: 0.8rem;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-outline {
            background: #f8f9fa;
            color: #64748b;
            border: 1px solid #e9ecef;
        }
        
        .btn-outline:hover {
            background: #e9ecef;
            color: #495057;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-3px);
        }
        
        .summary-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .summary-label {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                margin: 10px;
                padding: 20px;
            }
            
            .module-grid {
                grid-template-columns: 1fr;
            }
            
            .summary-stats {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>🏆 Shrimp Farm Guardian - Complete System Overview</h1>
        <p>Comprehensive aquaculture management platform with advanced analytics and automation</p>
    </div>

    <!-- Summary Statistics -->
    <div class="summary-stats">
        <div class="summary-card">
            <div class="summary-value">6</div>
            <div class="summary-label">Core Modules</div>
        </div>
        <div class="summary-card">
            <div class="summary-value">100%</div>
            <div class="summary-label">Implementation Complete</div>
        </div>
        <div class="summary-card">
            <div class="summary-value">∞</div>
            <div class="summary-label">Scalability</div>
        </div>
        <div class="summary-card">
            <div class="summary-value">24/7</div>
            <div class="summary-label">Monitoring</div>
        </div>
    </div>

    <!-- Module Grid -->
    <div class="module-grid">
        <!-- Pond Management -->
        <div class="module-card pond-management">
            <div class="module-header">
                <div class="module-icon pond-management">
                    <i class="fas fa-water"></i>
                </div>
                <div>
                    <h3 class="module-title">Pond Management</h3>
                </div>
            </div>
            <div class="module-description">
                Comprehensive pond lifecycle management with Google Maps integration, creation wizards, and real-time monitoring.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">∞</div>
                    <div class="stat-label">Ponds Supported</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">100%</div>
                    <div class="stat-label">Automation</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/ponds/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/ponds/create-wizard/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>Create Pond
                </a>
            </div>
        </div>

        <!-- Feed Management -->
        <div class="module-card feed-management">
            <div class="module-header">
                <div class="module-icon feed-management">
                    <i class="fas fa-seedling"></i>
                </div>
                <div>
                    <h3 class="module-title">Feed Management</h3>
                </div>
            </div>
            <div class="module-description">
                Advanced feed optimization with AI-powered recommendations, automated scheduling, and nutrition tracking.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">AI</div>
                    <div class="stat-label">Optimization</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">Smart</div>
                    <div class="stat-label">Scheduling</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/feed/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/feed/batches/create/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>New Batch
                </a>
            </div>
        </div>

        <!-- Water Quality Monitoring -->
        <div class="module-card water-quality">
            <div class="module-header">
                <div class="module-icon water-quality">
                    <i class="fas fa-tint"></i>
                </div>
                <div>
                    <h3 class="module-title">Water Quality</h3>
                </div>
            </div>
            <div class="module-description">
                Real-time water parameter monitoring with automated alerts, trend analysis, and predictive maintenance.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">Real-time</div>
                    <div class="stat-label">Monitoring</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">Smart</div>
                    <div class="stat-label">Alerts</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/water-quality/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/water-quality/records/add/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>Add Record
                </a>
            </div>
        </div>

        <!-- IoT Device Integration -->
        <div class="module-card iot-integration">
            <div class="module-header">
                <div class="module-icon iot-integration">
                    <i class="fas fa-microchip"></i>
                </div>
                <div>
                    <h3 class="module-title">IoT Integration</h3>
                </div>
            </div>
            <div class="module-description">
                Complete IoT ecosystem with device management, real-time data collection, and automated control systems.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">24/7</div>
                    <div class="stat-label">Monitoring</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">Auto</div>
                    <div class="stat-label">Control</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/iot/enhanced/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/iot/devices/add/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>Add Device
                </a>
            </div>
        </div>

        <!-- Financial Management -->
        <div class="module-card financial">
            <div class="module-header">
                <div class="module-icon financial">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div>
                    <h3 class="module-title">Financial Management</h3>
                </div>
            </div>
            <div class="module-description">
                Complete financial tracking with P&L analysis, ROI calculations, budget management, and profitability insights.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">ROI</div>
                    <div class="stat-label">Analysis</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">P&L</div>
                    <div class="stat-label">Tracking</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/financial/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/financial/transactions/add/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>Add Transaction
                </a>
            </div>
        </div>

        <!-- Advanced Reporting -->
        <div class="module-card reporting">
            <div class="module-header">
                <div class="module-icon reporting">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div>
                    <h3 class="module-title">Business Intelligence</h3>
                </div>
            </div>
            <div class="module-description">
                Advanced reporting system with custom templates, automated generation, and comprehensive business analytics.
            </div>
            <div class="module-stats">
                <div class="stat-item">
                    <div class="stat-value">Custom</div>
                    <div class="stat-label">Reports</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">Auto</div>
                    <div class="stat-label">Generation</div>
                </div>
            </div>
            <div class="module-actions">
                <a href="/reporting/" class="module-btn btn-primary">
                    <i class="fas fa-eye me-1"></i>View Dashboard
                </a>
                <a href="/reporting/create-template/" class="module-btn btn-outline">
                    <i class="fas fa-plus me-1"></i>Create Report
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Access -->
    <div class="text-center">
        <h3 class="mb-3">🚀 Quick Access</h3>
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="/" class="btn btn-primary btn-lg">
                <i class="fas fa-home me-2"></i>Main Dashboard
            </a>
            <a href="/admin/" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-cog me-2"></i>Admin Panel
            </a>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>
