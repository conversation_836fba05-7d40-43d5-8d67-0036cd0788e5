{% extends "base.html" %}
{% load static %}

{% block title %}{{ pond.name }} - Enhanced Aerator Map{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 85vh;
        min-height: 800px;
        width: 100%;
        border-radius: 8px;
        border: 2px solid #ddd;
        margin: 20px 0;
        position: relative;
        z-index: 1;
    }
    
    .map-container {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .control-panel {
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    
    .aerator-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        color: #333;
    }
    
    .stat-number {
        font-size: 1.8rem;
        font-weight: bold;
        color: #1e40af;
    }
    
    .stat-label {
        font-size: 0.8rem;
        color: #6b7280;
        text-transform: uppercase;
        font-weight: 600;
    }
    
    .btn-add-mode {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-add-mode:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
    }
    
    .btn-add-mode.active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    }
    
    .add-mode-indicator {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        display: none;
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .aerator-list {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        max-height: 400px;
        overflow-y: auto;
    }
    
    .aerator-item {
        background: #f8fafc;
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid #e5e7eb;
        transition: all 0.3s ease;
    }
    
    .aerator-item:hover {
        border-left-color: #3b82f6;
        transform: translateX(5px);
    }
    
    .aerator-item.status-active {
        border-left-color: #10b981;
    }
    
    .aerator-item.status-maintenance {
        border-left-color: #f59e0b;
    }
    
    .aerator-item.status-inactive {
        border-left-color: #ef4444;
    }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-active { background-color: #d1fae5; color: #065f46; }
    .status-inactive { background-color: #fee2e2; color: #991b1b; }
    .status-maintenance { background-color: #fef3c7; color: #92400e; }
    .status-faulty { background-color: #fde2e8; color: #9f1239; }
    
    .legend {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }
    
    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 10px;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .pond-marker { background-color: #3b82f6; }
    .aerator-active { background-color: #10b981; }
    .aerator-inactive { background-color: #ef4444; }
    .aerator-maintenance { background-color: #f59e0b; }
    .aerator-faulty { background-color: #9f1239; }

    /* Full screen map styles */
    .map-fullscreen {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100vw !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: white !important;
        border-radius: 0 !important;
        margin: 0 !important;
    }

    .map-fullscreen #map {
        height: calc(100vh - 80px) !important;
        border-radius: 0 !important;
        margin: 0 !important;
    }

    .fullscreen-controls {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10000;
        display: flex;
        gap: 10px;
    }

    .btn-fullscreen {
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #ddd;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-fullscreen:hover {
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    /* IoT Control Panel Styles */
    .iot-control-panel {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }

    .gps-tracking-panel {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border-left: 4px solid #3b82f6;
    }

    .device-status {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .device-online { background-color: #d1fae5; color: #065f46; }
    .device-offline { background-color: #fee2e2; color: #991b1b; }
    .device-warning { background-color: #fef3c7; color: #92400e; }

    .control-buttons {
        display: flex;
        gap: 8px;
        margin-top: 10px;
    }

    .btn-control {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-start {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
    }

    .btn-stop {
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        color: white;
    }

    .btn-emergency {
        background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
        color: white;
        animation: pulse-red 2s infinite;
    }

    @keyframes pulse-red {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); box-shadow: 0 0 20px rgba(220, 38, 38, 0.5); }
        100% { transform: scale(1); }
    }

    .btn-control:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }

    .gps-indicator {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        padding: 4px 8px;
        background: rgba(59, 130, 246, 0.1);
        border-radius: 6px;
        font-size: 0.8rem;
    }

    .gps-signal {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #10b981;
        animation: pulse-gps 2s infinite;
    }

    @keyframes pulse-gps {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .power-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 10px;
        margin-top: 15px;
    }

    .power-metric {
        background: rgba(255, 255, 255, 0.1);
        padding: 10px;
        border-radius: 8px;
        text-align: center;
    }

    .power-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: white;
    }

    .power-label {
        font-size: 0.7rem;
        opacity: 0.8;
        text-transform: uppercase;
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        #map {
            height: 70vh;
            min-height: 600px;
        }

        .map-fullscreen #map {
            height: calc(100vh - 60px) !important;
        }

        .col-lg-3, .col-md-4 {
            margin-bottom: 20px;
        }

        .control-panel {
            padding: 15px;
        }

        .aerator-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .stat-card {
            padding: 10px;
        }

        .stat-number {
            font-size: 1.4rem;
        }

        .aerator-list {
            max-height: 300px;
        }

        .fullscreen-controls {
            top: 10px;
            right: 10px;
            flex-direction: column;
        }

        .btn-fullscreen {
            padding: 6px 10px;
            font-size: 12px;
            margin-bottom: 5px;
        }
    }

    @media (max-width: 576px) {
        .aerator-stats {
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .control-panel h1 {
            font-size: 1.5rem;
        }

        .control-panel .d-flex {
            flex-direction: column;
            gap: 15px;
        }

        .btn-add-mode {
            width: 100%;
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Add Mode Indicator -->
    <div id="addModeIndicator" class="add-mode-indicator">
        <i class="fas fa-mouse-pointer me-2"></i>
        <strong>ADD MODE ACTIVE</strong><br>
        <small>Click on the map to add aerators</small>
    </div>

    <!-- Header -->
    <div class="control-panel">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2">
                    <i class="fas fa-fan me-2"></i>
                    {{ pond.name }} - Enhanced Aerator Map
                </h1>
                <p class="mb-0 opacity-75">Interactive aerator management with click-to-add functionality</p>
            </div>
            <div class="d-flex gap-2">
                <button id="toggleAddMode" class="btn-add-mode">
                    <i class="fas fa-plus me-1"></i> Add Mode
                </button>
                <a href="{% url 'ponds:aerator_management' pond_id=pond.id %}" class="btn btn-light">
                    <i class="fas fa-cogs me-1"></i> Manage
                </a>
                <a href="{% url 'ponds:pond_detail' pk=pond.id %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-1"></i> Back
                </a>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="aerator-stats">
            <div class="stat-card">
                <div class="stat-number">{{ aerators.count }}</div>
                <div class="stat-label">Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #10b981;">{{ active_count }}</div>
                <div class="stat-label">Active</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #f59e0b;">{{ maintenance_count }}</div>
                <div class="stat-label">Maintenance</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" style="color: #ef4444;">{{ inactive_count }}</div>
                <div class="stat-label">Inactive</div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Controls & Info -->
        <div class="col-lg-3 col-md-4">
            <!-- IoT Control Panel -->
            <div class="iot-control-panel">
                <h6 class="mb-3">
                    <i class="fas fa-satellite-dish me-2"></i>
                    IoT Control Center
                </h6>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>GPS Tracking:</span>
                    <button id="toggleGpsTracking" class="btn btn-sm btn-light" onclick="toggleGpsTracking()">
                        <i class="fas fa-map-marker-alt me-1"></i> Enable
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Auto Refresh:</span>
                    <button id="toggleAutoRefresh" class="btn btn-sm btn-light" onclick="toggleAutoRefresh()">
                        <i class="fas fa-sync me-1"></i> Start
                    </button>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Emergency Stop:</span>
                    <button class="btn btn-sm btn-danger" onclick="emergencyStopAll()">
                        <i class="fas fa-stop me-1"></i> STOP ALL
                    </button>
                </div>
            </div>

            <!-- GPS Tracking Panel -->
            <div class="gps-tracking-panel">
                <h6 class="mb-3">
                    <i class="fas fa-satellite me-2"></i>
                    GPS Tracking Status
                </h6>
                <div id="gpsTrackingStatus">
                    <div class="gps-indicator">
                        <div class="gps-signal"></div>
                        <span>GPS Active</span>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Last Update: <span id="lastGpsUpdate">--</span>
                        </small>
                    </div>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-wifi me-1"></i>
                            Connected Devices: <span id="connectedDevices">{{ aerators.count }}</span>
                        </small>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="legend">
                <h6 class="mb-3">
                    <i class="fas fa-map-signs text-primary me-2"></i>
                    Map Legend
                </h6>
                <div class="legend-item">
                    <div class="legend-color pond-marker"></div>
                    <span><strong>Pond Center</strong></span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-active"></div>
                    <span>Active Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-inactive"></div>
                    <span>Inactive Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-maintenance"></div>
                    <span>Maintenance Aerator</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color aerator-faulty"></div>
                    <span>Faulty Aerator</span>
                </div>
            </div>

            <!-- Aerator List -->
            <div class="aerator-list">
                <h6 class="mb-3">
                    <i class="fas fa-list text-primary me-2"></i>
                    Aerators ({{ aerators.count }})
                </h6>
                {% if aerators %}
                    {% for aerator in aerators %}
                    <div class="aerator-item status-{{ aerator.status }}" data-aerator-id="{{ aerator.id }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex align-items-center mb-2">
                                    <strong>{{ aerator.name }}</strong>
                                    <span class="status-badge status-{{ aerator.status }} ms-2">
                                        {{ aerator.status }}
                                    </span>
                                </div>
                                <div class="small text-muted">
                                    <div><i class="fas fa-cog me-1"></i> {{ aerator.get_aerator_type_display }}</div>
                                    {% if aerator.power_rating %}
                                    <div><i class="fas fa-bolt me-1"></i> {{ aerator.power_rating }} HP</div>
                                    {% endif %}
                                    <div><i class="fas fa-map-marker-alt me-1"></i> {{ aerator.latitude|floatformat:4 }}, {{ aerator.longitude|floatformat:4 }}</div>
                                    {% if aerator.device_id %}
                                    <div class="mt-2">
                                        <span class="device-status device-online">
                                            <i class="fas fa-wifi me-1"></i> Online
                                        </span>
                                        <div class="gps-indicator mt-1">
                                            <div class="gps-signal"></div>
                                            <span>GPS Active</span>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                                {% if aerator.device_id %}
                                <div class="control-buttons">
                                    <button class="btn-control btn-start" onclick="controlAerator('{{ aerator.device_id }}', 'start')"
                                            {% if aerator.status == 'active' %}disabled{% endif %}>
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <button class="btn-control btn-stop" onclick="controlAerator('{{ aerator.device_id }}', 'stop')"
                                            {% if aerator.status == 'inactive' %}disabled{% endif %}>
                                        <i class="fas fa-stop"></i>
                                    </button>
                                    <button class="btn-control btn-emergency" onclick="emergencyStop('{{ aerator.device_id }}')">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </button>
                                </div>
                                {% endif %}
                            </div>
                            <div class="text-end">
                                <button class="btn btn-sm btn-outline-primary" onclick="focusAerator({{ aerator.id }})">
                                    <i class="fas fa-search"></i>
                                </button>
                                {% if aerator.device_id %}
                                <button class="btn btn-sm btn-outline-success" onclick="showPowerMetrics('{{ aerator.device_id }}')">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-info-circle text-muted fa-2x mb-3"></i>
                        <p class="text-muted">No aerators found for this pond.</p>
                        <button class="btn btn-primary btn-sm" onclick="enableAddMode()">
                            <i class="fas fa-plus me-1"></i> Add First Aerator
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Right Column - Map -->
        <div class="col-lg-9 col-md-8">
            <div class="map-container">
                <h5 class="mb-3">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    Interactive Aerator Map
                </h5>
                <div id="map"></div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Click aerator markers for details. Use Add Mode to place new aerators.
                    </small>
                    <div class="float-end">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="toggleFullscreen()">
                            <i class="fas fa-expand me-1"></i> Fullscreen
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="resetMapView()">
                            <i class="fas fa-expand-arrows-alt me-1"></i> Reset View
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Aerator Modal -->
<div class="modal fade" id="addAeratorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle me-2"></i>
                    Add New Aerator
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickAddAeratorForm">
                    {% csrf_token %}
                    <input type="hidden" id="aeratorLatitude" name="latitude">
                    <input type="hidden" id="aeratorLongitude" name="longitude">
                    
                    <div class="mb-3">
                        <label class="form-label">Aerator Name</label>
                        <input type="text" class="form-control" id="aeratorName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Aerator Type</label>
                        <select class="form-control" id="aeratorType" name="aerator_type" required>
                            <option value="paddle_wheel">Paddle Wheel</option>
                            <option value="fountain">Fountain</option>
                            <option value="diffuser">Diffuser</option>
                            <option value="venturi">Venturi</option>
                            <option value="surface">Surface Aerator</option>
                            <option value="submersible">Submersible</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Power Rating (HP)</label>
                        <input type="number" class="form-control" id="aeratorPower" name="power_rating" step="0.1" min="0">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select class="form-control" id="aeratorStatus" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="maintenance">Maintenance</option>
                            <option value="faulty">Faulty</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAerator()">
                    <i class="fas fa-save me-1"></i> Add Aerator
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Power Metrics Modal -->
<div class="modal fade" id="powerMetricsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-chart-line me-2"></i>
                    Real-time Power Metrics
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="powerMetricsContent">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading power metrics...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="refreshPowerMetrics()">
                    <i class="fas fa-sync me-1"></i> Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- GPS Tracking Modal -->
<div class="modal fade" id="gpsTrackingModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-satellite me-2"></i>
                    Live GPS Tracking
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <div id="gpsTrackingMap" style="height: 500px; width: 100%;"></div>
            </div>
            <div class="modal-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <span class="badge bg-success me-2">
                            <i class="fas fa-satellite-dish me-1"></i>
                            <span id="activeDevicesCount">0</span> Active Devices
                        </span>
                        <span class="badge bg-info">
                            <i class="fas fa-clock me-1"></i>
                            Last Update: <span id="lastTrackingUpdate">--</span>
                        </span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="button" class="btn btn-primary" onclick="refreshGpsTracking()">
                            <i class="fas fa-sync me-1"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let map;
    let infoWindow;
    let addMode = false;
    let aeratorMarkers = [];
    let pondMarker;

    // Data from Django
    const pondData = {
        id: {{ pond.id }},
        name: "{{ pond.name|escapejs }}",
        latitude: {{ pond.latitude|default:0 }},
        longitude: {{ pond.longitude|default:0 }},
        status: "{{ pond.status|escapejs }}"
    };

    const aeratorsData = [
        {% for aerator in aerators %}
        {
            id: {{ aerator.id }},
            name: "{{ aerator.name|escapejs }}",
            type: "{{ aerator.aerator_type|escapejs }}",
            status: "{{ aerator.status|escapejs }}",
            power_rating: {{ aerator.power_rating|default:0 }},
            latitude: {{ aerator.latitude|default:0 }},
            longitude: {{ aerator.longitude|default:0 }},
            operating_hours: {{ aerator.operating_hours|default:0 }},
            device_id: "{{ aerator.device_id|escapejs }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};

    function initMap() {
        console.log('Initializing map...');

        // Initialize map
        map = new google.maps.Map(document.getElementById('map'), {
            zoom: 18,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: 'hybrid'
        });

        infoWindow = new google.maps.InfoWindow();

        // Add click listener for adding aerators
        map.addListener('click', function(event) {
            if (addMode) {
                showAddAeratorModal(event.latLng.lat(), event.latLng.lng());
            }
        });

        // Add pond marker
        addPondMarker();

        // Add aerator markers
        addAeratorMarkers();

        // Fit map to show all markers
        fitMapToMarkers();

        console.log('Map initialized successfully');
    }

    function addPondMarker() {
        if (pondData.latitude && pondData.longitude) {
            pondMarker = new google.maps.Marker({
                position: { lat: pondData.latitude, lng: pondData.longitude },
                map: map,
                title: pondData.name,
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 15,
                    fillColor: '#3b82f6',
                    fillOpacity: 1,
                    strokeColor: 'white',
                    strokeWeight: 3
                },
                zIndex: 1000
            });

            const pondInfoContent = `
                <div style="max-width: 300px; padding: 10px;">
                    <h5 style="color: #1e40af; margin-bottom: 15px;">
                        <i class="fas fa-water"></i> ${pondData.name}
                    </h5>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div><strong>Status:</strong> ${pondData.status}</div>
                        <div><strong>Aerators:</strong> ${aeratorsData.length}</div>
                        <div><strong>Active:</strong> ${aeratorsData.filter(a => a.status === 'active').length}</div>
                        <div><strong>Total Power:</strong> ${aeratorsData.reduce((sum, a) => sum + (a.power_rating || 0), 0)} HP</div>
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <button class="btn btn-sm btn-primary" onclick="enableAddMode()">
                            <i class="fas fa-plus"></i> Add Aerator
                        </button>
                    </div>
                </div>
            `;

            pondMarker.addListener('click', function() {
                infoWindow.setContent(pondInfoContent);
                infoWindow.open(map, pondMarker);
            });
        }
    }

    function addAeratorMarkers() {
        aeratorMarkers = [];

        aeratorsData.forEach(function(aerator, index) {
            if (aerator.latitude && aerator.longitude) {
                const marker = createAeratorMarker(aerator, index);
                aeratorMarkers.push(marker);
            }
        });
    }

    function createAeratorMarker(aerator, index) {
        let aeratorColor = '#ef4444'; // inactive - red

        if (aerator.status === 'active') {
            aeratorColor = '#10b981'; // active - green
        } else if (aerator.status === 'maintenance') {
            aeratorColor = '#f59e0b'; // maintenance - yellow
        } else if (aerator.status === 'faulty') {
            aeratorColor = '#9f1239'; // faulty - dark red
        }

        const aeratorMarker = new google.maps.Marker({
            position: { lat: aerator.latitude, lng: aerator.longitude },
            map: map,
            title: aerator.name,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 10,
                fillColor: aeratorColor,
                fillOpacity: 1,
                strokeColor: 'white',
                strokeWeight: 2
            },
            zIndex: 500
        });

        const aeratorInfoContent = `
            <div style="max-width: 280px; padding: 10px;">
                <h6 style="color: #1e40af; margin-bottom: 15px;">
                    <i class="fas fa-fan"></i> ${aerator.name}
                </h6>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 15px;">
                    <div><strong>Type:</strong> ${aerator.type.replace('_', ' ')}</div>
                    <div><strong>Status:</strong> ${aerator.status}</div>
                    ${aerator.power_rating ? `<div><strong>Power:</strong> ${aerator.power_rating} HP</div>` : ''}
                    ${aerator.operating_hours ? `<div><strong>Hours:</strong> ${aerator.operating_hours}</div>` : ''}
                    ${aerator.device_id ? `<div><strong>Device ID:</strong> ${aerator.device_id}</div>` : ''}
                    <div><strong>Location:</strong> ${aerator.latitude.toFixed(4)}, ${aerator.longitude.toFixed(4)}</div>
                </div>
                <div style="text-align: center;">
                    <a href="/ponds/{{ pond.id }}/aerators/${aerator.id}/edit/" class="btn btn-sm btn-primary">
                        <i class="fas fa-edit"></i> Edit
                    </a>
                </div>
            </div>
        `;

        aeratorMarker.addListener('click', function() {
            infoWindow.setContent(aeratorInfoContent);
            infoWindow.open(map, aeratorMarker);
        });

        return aeratorMarker;
    }

    // Add Mode Functions
    function toggleAddMode() {
        addMode = !addMode;
        const button = document.getElementById('toggleAddMode');
        const indicator = document.getElementById('addModeIndicator');

        if (addMode) {
            button.innerHTML = '<i class="fas fa-times me-1"></i> Exit Add Mode';
            button.classList.add('active');
            indicator.style.display = 'block';
            map.setOptions({ cursor: 'crosshair' });
        } else {
            button.innerHTML = '<i class="fas fa-plus me-1"></i> Add Mode';
            button.classList.remove('active');
            indicator.style.display = 'none';
            map.setOptions({ cursor: 'default' });
        }
    }

    function enableAddMode() {
        if (!addMode) {
            toggleAddMode();
        }
    }

    function showAddAeratorModal(lat, lng) {
        document.getElementById('aeratorLatitude').value = lat;
        document.getElementById('aeratorLongitude').value = lng;

        // Generate default name
        const aeratorCount = aeratorsData.length + 1;
        document.getElementById('aeratorName').value = `${pondData.name} - Aerator ${aeratorCount}`;

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addAeratorModal'));
        modal.show();
    }

    function saveAerator() {
        const form = document.getElementById('quickAddAeratorForm');
        const formData = new FormData(form);

        // Add pond ID
        formData.append('pond_id', pondData.id);

        fetch(`/ponds/{{ pond.id }}/aerators/add/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addAeratorModal'));
                modal.hide();

                // Show success message
                showAlert('success', 'Aerator added successfully!');

                // Reload page to show new aerator
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showAlert('error', data.error || 'Failed to add aerator');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Failed to add aerator');
        });
    }

    // Utility Functions
    function fitMapToMarkers() {
        if (aeratorMarkers.length > 0 || pondMarker) {
            const bounds = new google.maps.LatLngBounds();

            // Include pond location
            if (pondMarker) {
                bounds.extend(pondMarker.getPosition());
            }

            // Include aerator locations
            aeratorMarkers.forEach(function(marker) {
                bounds.extend(marker.getPosition());
            });

            map.fitBounds(bounds);

            // Ensure minimum zoom level
            const minZoom = 16;
            google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
                if (map.getZoom() > minZoom) {
                    map.setZoom(minZoom);
                }
            });
        }
    }

    function resetMapView() {
        fitMapToMarkers();
    }

    // Fullscreen functionality
    function toggleFullscreen() {
        const mapContainer = document.querySelector('.map-container');
        const isFullscreen = mapContainer.classList.contains('map-fullscreen');

        if (isFullscreen) {
            exitFullscreen();
        } else {
            enterFullscreen();
        }
    }

    function enterFullscreen() {
        const mapContainer = document.querySelector('.map-container');
        mapContainer.classList.add('map-fullscreen');

        // Add fullscreen controls
        const fullscreenControls = document.createElement('div');
        fullscreenControls.className = 'fullscreen-controls';
        fullscreenControls.innerHTML = `
            <button class="btn-fullscreen" onclick="resetMapView()">
                <i class="fas fa-expand-arrows-alt me-1"></i> Reset View
            </button>
            <button class="btn-fullscreen" onclick="toggleAddMode()">
                <i class="fas fa-plus me-1"></i> Add Mode
            </button>
            <button class="btn-fullscreen" onclick="exitFullscreen()">
                <i class="fas fa-compress me-1"></i> Exit Fullscreen
            </button>
        `;
        mapContainer.appendChild(fullscreenControls);

        // Trigger map resize
        setTimeout(() => {
            google.maps.event.trigger(map, 'resize');
            fitMapToMarkers();
        }, 100);
    }

    function exitFullscreen() {
        const mapContainer = document.querySelector('.map-container');
        mapContainer.classList.remove('map-fullscreen');

        // Remove fullscreen controls
        const fullscreenControls = mapContainer.querySelector('.fullscreen-controls');
        if (fullscreenControls) {
            fullscreenControls.remove();
        }

        // Trigger map resize
        setTimeout(() => {
            google.maps.event.trigger(map, 'resize');
            fitMapToMarkers();
        }, 100);
    }

    function focusAerator(aeratorId) {
        const aerator = aeratorsData.find(a => a.id === aeratorId);
        if (aerator && aerator.latitude && aerator.longitude) {
            map.setCenter({ lat: aerator.latitude, lng: aerator.longitude });
            map.setZoom(20);

            // Find and click the marker
            const marker = aeratorMarkers.find(m => m.getTitle() === aerator.name);
            if (marker) {
                google.maps.event.trigger(marker, 'click');
            }
        }
    }

    // Utility function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Alert function
    function showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // IoT Control Functions
    let gpsTrackingEnabled = false;
    let autoRefreshEnabled = false;
    let refreshInterval;
    let currentDeviceId = null;

    function controlAerator(deviceId, action) {
        if (!deviceId) {
            showAlert('error', 'Device ID not found');
            return;
        }

        console.log(`Controlling aerator ${deviceId}: ${action}`);

        // Show loading state
        const buttons = document.querySelectorAll(`[onclick*="${deviceId}"]`);
        buttons.forEach(btn => {
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });

        fetch(`/iot/api/aerator/${deviceId}/${action}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                pond_id: pondData.id,
                timestamp: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', `Aerator ${deviceId} ${action} command sent successfully`);

                // Update marker color based on action
                updateAeratorMarkerStatus(deviceId, action === 'start' ? 'active' : 'inactive');

                // Refresh aerator list
                setTimeout(() => {
                    refreshAeratorData();
                }, 1000);
            } else {
                showAlert('error', data.error || `Failed to ${action} aerator ${deviceId}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Failed to communicate with device');
        })
        .finally(() => {
            // Restore button states
            setTimeout(() => {
                location.reload();
            }, 2000);
        });
    }

    function emergencyStop(deviceId) {
        if (!confirm(`Are you sure you want to emergency stop aerator ${deviceId}?`)) {
            return;
        }

        fetch(`/iot/api/aerator/${deviceId}/emergency_stop/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                reason: 'Manual emergency stop from map interface',
                timestamp: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('warning', `Emergency stop activated for aerator ${deviceId}`);
                updateAeratorMarkerStatus(deviceId, 'emergency');
            } else {
                showAlert('error', 'Failed to send emergency stop command');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Emergency stop command failed');
        });
    }

    function emergencyStopAll() {
        if (!confirm('Are you sure you want to emergency stop ALL aerators? This action cannot be undone.')) {
            return;
        }

        const deviceIds = aeratorsData.filter(a => a.device_id).map(a => a.device_id);

        if (deviceIds.length === 0) {
            showAlert('warning', 'No IoT-enabled aerators found');
            return;
        }

        fetch(`/iot/api/aerators/emergency_stop_all/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                pond_id: pondData.id,
                device_ids: deviceIds,
                reason: 'Emergency stop all from map interface',
                timestamp: new Date().toISOString()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('warning', `Emergency stop activated for ${data.stopped_count} aerators`);
                // Update all markers to emergency status
                deviceIds.forEach(deviceId => {
                    updateAeratorMarkerStatus(deviceId, 'emergency');
                });
            } else {
                showAlert('error', 'Failed to send emergency stop command to all devices');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('error', 'Emergency stop all command failed');
        });
    }

    function toggleGpsTracking() {
        gpsTrackingEnabled = !gpsTrackingEnabled;
        const button = document.getElementById('toggleGpsTracking');

        if (gpsTrackingEnabled) {
            button.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i> Disable';
            button.classList.remove('btn-light');
            button.classList.add('btn-warning');
            startGpsTracking();
            showAlert('info', 'GPS tracking enabled');
        } else {
            button.innerHTML = '<i class="fas fa-map-marker-alt me-1"></i> Enable';
            button.classList.remove('btn-warning');
            button.classList.add('btn-light');
            stopGpsTracking();
            showAlert('info', 'GPS tracking disabled');
        }
    }

    function toggleAutoRefresh() {
        autoRefreshEnabled = !autoRefreshEnabled;
        const button = document.getElementById('toggleAutoRefresh');

        if (autoRefreshEnabled) {
            button.innerHTML = '<i class="fas fa-sync me-1"></i> Stop';
            button.classList.remove('btn-light');
            button.classList.add('btn-warning');
            startAutoRefresh();
            showAlert('info', 'Auto refresh enabled (30s interval)');
        } else {
            button.innerHTML = '<i class="fas fa-sync me-1"></i> Start';
            button.classList.remove('btn-warning');
            button.classList.add('btn-light');
            stopAutoRefresh();
            showAlert('info', 'Auto refresh disabled');
        }
    }

    function startGpsTracking() {
        // Start GPS tracking for all IoT devices
        fetch(`/iot/api/gps/start_tracking/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                pond_id: pondData.id
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateGpsStatus('active');
            }
        })
        .catch(error => console.error('GPS tracking error:', error));
    }

    function stopGpsTracking() {
        // Stop GPS tracking
        updateGpsStatus('inactive');
    }

    function startAutoRefresh() {
        refreshInterval = setInterval(() => {
            refreshAeratorData();
            updateGpsPositions();
        }, 30000); // 30 seconds
    }

    function stopAutoRefresh() {
        if (refreshInterval) {
            clearInterval(refreshInterval);
            refreshInterval = null;
        }
    }

    function updateAeratorMarkerStatus(deviceId, status) {
        // Find and update marker color based on status
        const aerator = aeratorsData.find(a => a.device_id === deviceId);
        if (aerator) {
            const marker = aeratorMarkers.find(m => m.getTitle() === aerator.name);
            if (marker) {
                let color = '#ef4444'; // default inactive
                if (status === 'active') color = '#10b981';
                else if (status === 'emergency') color = '#dc2626';

                marker.setIcon({
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 10,
                    fillColor: color,
                    fillOpacity: 1,
                    strokeColor: 'white',
                    strokeWeight: 2
                });
            }
        }
    }

    function refreshAeratorData() {
        // Refresh aerator data from server
        fetch(`/iot/api/aerators/status/?pond_id=${pondData.id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update aerator statuses
                data.aerators.forEach(aerator => {
                    updateAeratorMarkerStatus(aerator.device_id, aerator.status);
                });

                // Update last update time
                document.getElementById('lastGpsUpdate').textContent = new Date().toLocaleTimeString();
            }
        })
        .catch(error => console.error('Refresh error:', error));
    }

    function updateGpsPositions() {
        // Update GPS positions for all devices
        fetch(`/iot/api/gps/positions/?pond_id=${pondData.id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.positions.forEach(position => {
                    updateDevicePosition(position.device_id, position.latitude, position.longitude);
                });
            }
        })
        .catch(error => console.error('GPS update error:', error));
    }

    function updateDevicePosition(deviceId, lat, lng) {
        // Update device position on map
        const aerator = aeratorsData.find(a => a.device_id === deviceId);
        if (aerator) {
            const marker = aeratorMarkers.find(m => m.getTitle() === aerator.name);
            if (marker) {
                marker.setPosition({ lat: lat, lng: lng });
            }
        }
    }

    function updateGpsStatus(status) {
        const statusElement = document.getElementById('gpsTrackingStatus');
        const indicator = statusElement.querySelector('.gps-signal');

        if (status === 'active') {
            indicator.style.background = '#10b981';
            statusElement.querySelector('span').textContent = 'GPS Active';
        } else {
            indicator.style.background = '#ef4444';
            statusElement.querySelector('span').textContent = 'GPS Inactive';
        }
    }

    function showPowerMetrics(deviceId) {
        currentDeviceId = deviceId;
        const modal = new bootstrap.Modal(document.getElementById('powerMetricsModal'));
        modal.show();

        // Load power metrics
        fetch(`/iot/api/power_metrics/${deviceId}/`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPowerMetrics(data.metrics);
            } else {
                document.getElementById('powerMetricsContent').innerHTML =
                    '<div class="alert alert-warning">No power metrics available for this device.</div>';
            }
        })
        .catch(error => {
            console.error('Power metrics error:', error);
            document.getElementById('powerMetricsContent').innerHTML =
                '<div class="alert alert-danger">Failed to load power metrics.</div>';
        });
    }

    function displayPowerMetrics(metrics) {
        const content = `
            <div class="power-metrics">
                <div class="power-metric">
                    <div class="power-value">${metrics.voltage || 0}V</div>
                    <div class="power-label">Voltage</div>
                </div>
                <div class="power-metric">
                    <div class="power-value">${metrics.current || 0}A</div>
                    <div class="power-label">Current</div>
                </div>
                <div class="power-metric">
                    <div class="power-value">${metrics.power || 0}kW</div>
                    <div class="power-label">Power</div>
                </div>
                <div class="power-metric">
                    <div class="power-value">${metrics.power_factor || 0}</div>
                    <div class="power-label">Power Factor</div>
                </div>
                <div class="power-metric">
                    <div class="power-value">${metrics.frequency || 0}Hz</div>
                    <div class="power-label">Frequency</div>
                </div>
                <div class="power-metric">
                    <div class="power-value">${metrics.efficiency || 0}%</div>
                    <div class="power-label">Efficiency</div>
                </div>
            </div>
            <div class="mt-3">
                <small class="text-muted">Last updated: ${new Date().toLocaleString()}</small>
            </div>
        `;
        document.getElementById('powerMetricsContent').innerHTML = content;
    }

    function refreshPowerMetrics() {
        if (currentDeviceId) {
            showPowerMetrics(currentDeviceId);
        }
    }

    function refreshGpsTracking() {
        updateGpsPositions();
        showAlert('info', 'GPS positions refreshed');
    }

    // Event Listeners
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('toggleAddMode').addEventListener('click', toggleAddMode);

        // Initialize GPS status
        updateGpsStatus('inactive');

        // Update connected devices count
        const iotDevices = aeratorsData.filter(a => a.device_id).length;
        document.getElementById('connectedDevices').textContent = iotDevices;

        // Trigger map resize after a short delay to ensure proper rendering
        setTimeout(() => {
            if (map) {
                google.maps.event.trigger(map, 'resize');
                fitMapToMarkers();
            }
        }, 500);
    });

    // Handle window resize to ensure map stays properly sized
    window.addEventListener('resize', function() {
        if (map) {
            google.maps.event.trigger(map, 'resize');
        }
    });

    // Error handling for Google Maps
    window.gm_authFailure = function() {
        document.getElementById('map').innerHTML =
            '<div class="alert alert-danger m-3">Google Maps failed to load. Please check your API key.</div>';
    };
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap">
</script>
{% endblock %}
