<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Lunar Dashboard - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    .enhanced-lunar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .enhanced-lunar-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: enhanced-sweep 4s infinite;
    }
    
    @keyframes enhanced-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 25px;
        margin: 30px 0;
    }
    
    .feature-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        backdrop-filter: blur(15px);
        border-radius: 20px;
        padding: 30px;
        border: 2px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }
    
    .feature-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 25px 50px rgba(0,0,0,0.3);
        border-color: rgba(255,255,255,0.4);
    }
    
    .feature-icon {
        font-size: 3.5em;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(116, 185, 255, 0.5);
    }
    
    .enhancement-badge {
        position: absolute;
        top: 20px;
        right: 20px;
        padding: 8px 16px;
        border-radius: 25px;
        font-size: 0.9em;
        font-weight: bold;
        animation: pulse-enhanced 2s infinite;
    }
    
    @keyframes pulse-enhanced {
        0%, 100% { 
            opacity: 1; 
            box-shadow: 0 0 20px rgba(255,255,255,0.5);
        }
        50% { 
            opacity: 0.8; 
            box-shadow: 0 0 30px rgba(255,255,255,0.8);
        }
    }
    
    .calendar-badge { background: linear-gradient(45deg, #667eea, #764ba2); }
    .analytics-badge { background: linear-gradient(45deg, #f093fb, #f5576c); }
    .api-badge { background: linear-gradient(45deg, #4facfe, #00f2fe); }
    .mobile-badge { background: linear-gradient(45deg, #43e97b, #38f9d7); }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .stat-card {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 25px;
        text-align: center;
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        background: rgba(255,255,255,0.15);
        transform: translateY(-5px);
    }
    
    .stat-number {
        font-size: 2.8em;
        font-weight: bold;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .enhanced-button {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        font-size: 1.1em;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        text-transform: uppercase;
        letter-spacing: 1px;
        text-decoration: none;
        display: inline-block;
    }
    
    .enhanced-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.6);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }
    
    .timeline-container {
        background: linear-gradient(45deg, #1e3c72, #2a5298);
        border-radius: 20px;
        padding: 30px;
        margin-top: 40px;
    }
    
    .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        border-left: 4px solid #74b9ff;
        transition: all 0.3s ease;
    }
    
    .timeline-item:hover {
        background: rgba(255,255,255,0.15);
        transform: translateX(10px);
    }
    
    .timeline-icon {
        font-size: 1.8em;
        margin-right: 20px;
        color: #74b9ff;
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
    <!-- Enhanced Lunar Header -->
    <div class="enhanced-lunar-header">
        <div class="position-relative">
            <h1><i class="fas fa-calendar-alt me-3"></i>Enhanced Lunar Module</h1>
            <p class="lead mb-0">Complete lunar calendar integration with advanced features</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">📅 FULL CALENDAR</span>
                <span class="badge bg-light text-dark me-2">📊 ANALYTICS</span>
                <span class="badge bg-light text-dark me-2">🔗 API INTEGRATION</span>
                <span class="badge bg-light text-dark me-2">📱 MOBILE READY</span>
                <span class="badge bg-success text-white ms-3">✅ ENHANCED</span>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="/lunar/full-calendar/" class="quick-action">
            <i class="fas fa-calendar-alt"></i>
            Full Calendar
        </a>
        <a href="/lunar/" class="quick-action">
            <i class="fas fa-tachometer-alt"></i>
            Lunar Dashboard
        </a>
        <a href="/lunar/api/" class="quick-action">
            <i class="fas fa-code"></i>
            API Access
        </a>
        <a href="/" class="quick-action">
            <i class="fas fa-home"></i>
            Back to Home
        </a>
    </div>

    <!-- Enhanced Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">365</div>
            <h6>Days Tracked</h6>
            <p class="mb-0">Full year coverage</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">4</div>
            <h6>Key Phases</h6>
            <p class="mb-0">Astami, Navami, Pournami, Amavasya</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">12</div>
            <h6>Monthly Views</h6>
            <p class="mb-0">Detailed month analysis</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">∞</div>
            <h6>API Access</h6>
            <p class="mb-0">Unlimited integration</p>
        </div>
    </div>

    <!-- Enhanced Features Grid -->
    <div class="feature-grid">
        <!-- Full Calendar Integration -->
        <div class="feature-card">
            <span class="enhancement-badge calendar-badge">CALENDAR</span>
            <div class="feature-icon"><i class="fas fa-calendar-alt"></i></div>
            <h3>Full Calendar Integration</h3>
            <p>Interactive calendar with FullCalendar.js integration, showing all lunar phases, events, and shrimp farming recommendations in a beautiful interface.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Interactive Calendar View</li>
                <li><i class="fas fa-check text-success me-2"></i>Month/Year Navigation</li>
                <li><i class="fas fa-check text-success me-2"></i>Event Details Modal</li>
                <li><i class="fas fa-check text-success me-2"></i>Responsive Design</li>
            </ul>
            <a href="/lunar/full-calendar/" class="enhanced-button mt-3">Open Full Calendar</a>
        </div>

        <!-- Advanced Analytics -->
        <div class="feature-card">
            <span class="enhancement-badge analytics-badge">ANALYTICS</span>
            <div class="feature-icon"><i class="fas fa-chart-line"></i></div>
            <h3>Lunar Analytics</h3>
            <p>Comprehensive analytics and statistics for lunar patterns, including phase distribution, favorable days analysis, and shrimp farming impact metrics.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Phase Distribution Charts</li>
                <li><i class="fas fa-check text-success me-2"></i>Favorable Days Analysis</li>
                <li><i class="fas fa-check text-success me-2"></i>Monthly Breakdowns</li>
                <li><i class="fas fa-check text-success me-2"></i>Trend Analysis</li>
            </ul>
            <a href="/lunar/api/" class="enhanced-button mt-3">View Analytics</a>
        </div>

        <!-- API Integration -->
        <div class="feature-card">
            <span class="enhancement-badge api-badge">API</span>
            <div class="feature-icon"><i class="fas fa-code"></i></div>
            <h3>API Integration</h3>
            <p>RESTful API endpoints for lunar data integration with external systems, mobile apps, and third-party services with JSON responses.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>RESTful API Endpoints</li>
                <li><i class="fas fa-check text-success me-2"></i>JSON Data Format</li>
                <li><i class="fas fa-check text-success me-2"></i>Date Range Queries</li>
                <li><i class="fas fa-check text-success me-2"></i>Real-time Data</li>
            </ul>
            <a href="/lunar/api/calendar/?start=2024-01-01&end=2024-12-31" class="enhanced-button mt-3">Test API</a>
        </div>

        <!-- Mobile Optimization -->
        <div class="feature-card">
            <span class="enhancement-badge mobile-badge">MOBILE</span>
            <div class="feature-icon"><i class="fas fa-mobile-alt"></i></div>
            <h3>Mobile Optimization</h3>
            <p>Fully responsive design optimized for mobile devices with touch-friendly interfaces, offline capabilities, and progressive web app features.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Responsive Design</li>
                <li><i class="fas fa-check text-success me-2"></i>Touch-friendly Interface</li>
                <li><i class="fas fa-check text-success me-2"></i>Offline Capabilities</li>
                <li><i class="fas fa-check text-success me-2"></i>PWA Ready</li>
            </ul>
            <a href="/lunar/" class="enhanced-button mt-3">Mobile View</a>
        </div>

        <!-- Astronomical Accuracy -->
        <div class="feature-card">
            <span class="enhancement-badge calendar-badge">ACCURATE</span>
            <div class="feature-icon"><i class="fas fa-telescope"></i></div>
            <h3>Astronomical Accuracy</h3>
            <p>Precise astronomical calculations using Julian Day Numbers, lunar phase algorithms, and real-time lunar position data for maximum accuracy.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Julian Day Calculations</li>
                <li><i class="fas fa-check text-success me-2"></i>Lunar Phase Algorithms</li>
                <li><i class="fas fa-check text-success me-2"></i>Position Tracking</li>
                <li><i class="fas fa-check text-success me-2"></i>Scientific Precision</li>
            </ul>
            <a href="/lunar/test/" class="enhanced-button mt-3">Verify Accuracy</a>
        </div>

        <!-- Shrimp Farm Integration -->
        <div class="feature-card">
            <span class="enhancement-badge analytics-badge">AQUA</span>
            <div class="feature-icon"><i class="fas fa-fish"></i></div>
            <h3>Shrimp Farm Integration</h3>
            <p>Specialized recommendations for shrimp farming based on lunar phases, including feeding schedules, molting predictions, and optimal timing for operations.</p>
            <ul class="list-unstyled mt-3">
                <li><i class="fas fa-check text-success me-2"></i>Feeding Recommendations</li>
                <li><i class="fas fa-check text-success me-2"></i>Molting Predictions</li>
                <li><i class="fas fa-check text-success me-2"></i>Operation Timing</li>
                <li><i class="fas fa-check text-success me-2"></i>Activity Monitoring</li>
            </ul>
            <a href="/lunar/" class="enhanced-button mt-3">View Recommendations</a>
        </div>
    </div>

    <!-- Enhancement Timeline -->
    <div class="timeline-container">
        <h3><i class="fas fa-rocket me-2"></i>Lunar Module Enhancement Timeline</h3>
        <p class="mb-4">Complete transformation from basic to advanced lunar calendar system</p>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-bug"></i></div>
            <div>
                <h5>Issue Resolution</h5>
                <p>Fixed property setter issues and parameter passing errors</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-calculator"></i></div>
            <div>
                <h5>Astronomical Calculations</h5>
                <p>Implemented precise Julian Day and lunar phase calculations</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-calendar-plus"></i></div>
            <div>
                <h5>Full Calendar Integration</h5>
                <p>Added interactive calendar with FullCalendar.js</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-code"></i></div>
            <div>
                <h5>API Development</h5>
                <p>Created comprehensive API endpoints for data access</p>
            </div>
        </div>
        
        <div class="timeline-item">
            <div class="timeline-icon"><i class="fas fa-mobile"></i></div>
            <div>
                <h5>Mobile Optimization</h5>
                <p>Enhanced responsive design and mobile experience</p>
            </div>
        </div>
    </div>

    <!-- Call to Action -->
    <div class="text-center mt-5">
        <h2>Enhanced Lunar Module Ready</h2>
        <p class="lead">Complete lunar calendar system with advanced features</p>
        <div class="mt-4">
            <a href="/lunar/full-calendar/" class="enhanced-button me-3">Explore Full Calendar</a>
            <a href="/lunar/" class="enhanced-button me-3">View Dashboard</a>
            <a href="/lunar/api/" class="enhanced-button">Access API</a>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate feature cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.feature-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Enhanced button effects
    document.querySelectorAll('.enhanced-button').forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
</body>
</html>
