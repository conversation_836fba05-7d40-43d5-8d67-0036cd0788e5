# Unified Theme System for Shrimp Farm Guardian

## Overview

The Unified Theme System provides a consistent, professional theming solution across the entire Shrimp Farm Guardian application. It supports both light and dark themes with seamless switching, accessibility features, and responsive design.

## Features

### 🎨 **Consistent Design Language**
- Unified color palette across all components
- Professional glassmorphism effects
- Consistent spacing, typography, and shadows
- Smooth transitions and animations

### 🌓 **Dual Theme Support**
- **Dark Theme (Default)**: Professional dark interface with blue/purple gradients
- **Light Theme**: Clean, bright interface for daytime use
- Automatic system preference detection
- User preference persistence

### ♿ **Accessibility First**
- WCAG 2.1 AA compliant color contrasts
- Keyboard navigation support
- Screen reader friendly
- High contrast mode support
- Reduced motion support

### 📱 **Responsive Design**
- Mobile-first approach
- Tablet and desktop optimizations
- Touch-friendly controls
- Adaptive layouts

## File Structure

```
static/css/
├── unified-theme.css          # Main theme system
├── style.css                  # Legacy styles (compatibility)
├── unified-template.css       # Legacy unified styles
└── dark-theme.css            # Legacy dark theme

static/js/
├── unified-theme.js          # Theme management system
└── theme-toggle.js           # Legacy theme toggle

templates/components/
├── unified_theme_toggle.html # New theme toggle component
└── theme_toggle.html         # Legacy theme toggle
```

## Implementation

### 1. CSS Custom Properties

The system uses CSS custom properties (variables) for consistent theming:

```css
:root {
  /* Primary Colors */
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  
  /* Background Colors */
  --bg-primary: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
  --bg-secondary: rgba(30, 41, 59, 0.95);
  --bg-card: rgba(30, 41, 59, 0.8);
  
  /* Text Colors */
  --text-primary: #e2e8f0;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
}
```

### 2. Theme Switching

The `ThemeManager` class handles all theme operations:

```javascript
// Get current theme
const currentTheme = themeManager.getTheme();

// Set theme programmatically
themeManager.setTheme('light'); // or 'dark'

// Listen for theme changes
window.addEventListener('themeChanged', (event) => {
  console.log('Theme changed to:', event.detail.theme);
});
```

### 3. Component Usage

Use the unified theme toggle component:

```html
{% include 'components/unified_theme_toggle.html' %}
```

## Color Palette

### Dark Theme (Default)
- **Primary**: #667eea (Blue)
- **Secondary**: #764ba2 (Purple)
- **Background**: #1a202c → #2d3748 (Gradient)
- **Text**: #e2e8f0 (Light gray)
- **Accent**: #90cdf4 (Light blue)

### Light Theme
- **Primary**: #667eea (Blue)
- **Secondary**: #764ba2 (Purple)
- **Background**: #f8fafc → #e2e8f0 (Gradient)
- **Text**: #1e293b (Dark gray)
- **Accent**: #667eea (Blue)

### Status Colors
- **Success**: #10b981 (Green)
- **Warning**: #f59e0b (Orange)
- **Danger**: #ef4444 (Red)
- **Info**: #3b82f6 (Blue)

## Component Classes

### Layout Components
- `.layout-container` - Main layout wrapper
- `.sidebar` - Navigation sidebar
- `.main-content` - Main content area
- `.page-header` - Page header section

### UI Components
- `.card` - Content cards
- `.stats-card` - Statistics cards
- `.btn` - Buttons (with variants)
- `.form-control` - Form inputs
- `.table-container` - Table wrapper

### Status & Feedback
- `.badge` - Status badges
- `.alert` - Alert messages
- `.status-indicator` - Status dots
- `.theme-notification` - Theme change notifications

## Usage Examples

### Creating a Card
```html
<div class="card">
  <div class="card-header">
    <h3 class="card-title">Card Title</h3>
  </div>
  <div class="card-body">
    <p>Card content goes here.</p>
  </div>
</div>
```

### Status Indicators
```html
<span class="status-indicator status-active"></span> Active
<span class="badge badge-success">Online</span>
```

### Buttons
```html
<button class="btn btn-primary">Primary Action</button>
<button class="btn btn-outline-primary">Secondary Action</button>
```

## Customization

### Adding New Colors
Add new color variables to the `:root` selector:

```css
:root {
  --custom-color: #your-color;
  --custom-bg: rgba(your, color, values, 0.8);
}
```

### Creating New Components
Follow the established patterns:

```css
.your-component {
  background: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  transition: all var(--transition-base);
}
```

## Migration Guide

### From Legacy Themes
1. Replace old CSS imports with unified theme
2. Update component classes to use new system
3. Test theme switching functionality
4. Verify accessibility compliance

### Updating Existing Components
1. Replace hardcoded colors with CSS variables
2. Use standardized spacing and radius values
3. Add proper hover and focus states
4. Ensure responsive behavior

## Browser Support

- **Modern Browsers**: Full support (Chrome 88+, Firefox 85+, Safari 14+)
- **CSS Custom Properties**: Required
- **Backdrop Filter**: Graceful degradation
- **Grid/Flexbox**: Full support

## Performance

- **CSS Variables**: Minimal performance impact
- **Transitions**: Hardware accelerated
- **File Size**: ~15KB gzipped
- **Load Time**: <50ms on modern connections

## Accessibility Features

- **Color Contrast**: WCAG AA compliant
- **Keyboard Navigation**: Full support
- **Screen Readers**: Proper ARIA labels
- **High Contrast**: System preference support
- **Reduced Motion**: Respects user preferences

## Future Enhancements

- [ ] Additional color themes (blue, green, purple)
- [ ] Advanced customization panel
- [ ] Theme scheduling (auto dark/light)
- [ ] Component-level theme overrides
- [ ] CSS-in-JS integration
- [ ] Theme export/import functionality

## Support

For issues or questions about the unified theme system:
1. Check this documentation
2. Review the CSS custom properties
3. Test in different browsers
4. Verify accessibility compliance
5. Submit bug reports with theme details

---

**Last Updated**: December 2024  
**Version**: 1.0.0  
**Compatibility**: All modern browsers
