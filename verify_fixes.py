#!/usr/bin/env python
"""
Simple test to verify dashboard fixes
"""
import sys
import os

# Add project to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test basic imports"""
    try:
        print("🧪 Testing custom template filters...")
        
        # Test template filters
        from core.templatetags.custom_filters import to_json, percentage, status_badge_class
        
        # Test basic functionality
        test_data = {"status": "active", "count": 42}
        json_result = to_json(test_data)
        print(f"  ✅ to_json: {json_result}")
        
        percentage_result = percentage(75, 100)
        print(f"  ✅ percentage: {percentage_result}%")
        
        badge_class = status_badge_class("active")
        print(f"  ✅ status_badge_class: {badge_class}")
        
        print("🎉 Template filters working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def test_django_setup():
    """Test Django setup"""
    try:
        print("\n🔧 Testing Django setup...")
        
        # Set up Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
        import django
        django.setup()
        
        print("  ✅ Django configured successfully")
        
        # Test URL reverse
        from django.urls import reverse
        dashboard_url = reverse('core:enhanced_dashboard')
        print(f"  ✅ Enhanced dashboard URL: {dashboard_url}")
        
        logout_url = reverse('users:logout')
        print(f"  ✅ Logout URL: {logout_url}")
        
        # Test view import
        from core.views_enhanced_dashboard import enhanced_dashboard_view
        print("  ✅ Enhanced dashboard view imported")
        
        # Test template loading
        from django.template.loader import get_template
        template = get_template('core/enhanced_dashboard.html')
        print("  ✅ Enhanced dashboard template loaded")
        
        print("🎉 Django setup working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Django test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Dashboard Fixes Verification")
    print("=" * 40)
    
    success = True
    
    # Test 1: Template filters
    if not test_imports():
        success = False
    
    # Test 2: Django setup
    if not test_django_setup():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✅ ALL TESTS PASSED! Dashboard should be working.")
        print("\n📋 Summary of fixes:")
        print("  • Created custom_filters.py with 13+ template filters")
        print("  • Fixed WeatherData import in views_enhanced_dashboard.py")
        print("  • Fixed account_logout URL in mobile_navigation.html")
        print("  • Corrected mock classes to use proper Django Manager pattern")
        print("\n🌐 You can now access the enhanced dashboard at:")
        print("  http://127.0.0.1:8000/enhanced/")
    else:
        print("❌ Some tests failed. Check the errors above.")
    
    return success

if __name__ == "__main__":
    main()
