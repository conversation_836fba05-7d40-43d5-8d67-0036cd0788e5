<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Labor Management Dashboard - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Labor Header */
    .labor-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .labor-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: labor-sweep 4s infinite;
    }
    
    @keyframes labor-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .stat-card {
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        background: white;
        border: none;
        height: 100%;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
        font-size: 3rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .labor-count {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .labor-label {
        font-size: 1.1rem;
        color: #6c757d;
        font-weight: 500;
    }

    .task-list-item {
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
        background: white;
        border-radius: 10px;
        margin-bottom: 10px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    .task-list-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    }

    .task-list-item.priority-low {
        border-left-color: #6c757d;
    }

    .task-list-item.priority-medium {
        border-left-color: #0d6efd;
    }

    .task-list-item.priority-high {
        border-left-color: #fd7e14;
    }

    .task-list-item.priority-urgent {
        border-left-color: #dc3545;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 600;
    }

    /* Enhanced Live tracking map styles */
    #live-tracking-map {
        height: 450px;
        width: 100%;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .map-card {
        background: white;
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .worker-marker {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        border: 3px solid white;
        color: white;
        font-size: 16px;
        font-weight: bold;
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.5rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .worker-status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-working { background-color: #10b981; }
    .status-on_break { background-color: #f59e0b; }
    .status-traveling { background-color: #3b82f6; }
    .status-idle { background-color: #6b7280; }
    .status-available { background-color: #8b5cf6; }
    .status-off_duty { background-color: #ef4444; }

    .gradient-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Labor Header -->
    <div class="labor-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Labor Management Dashboard</h1>
                <p class="lead mb-0">Comprehensive workforce monitoring and task management system</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <i class="fas fa-users stat-icon opacity-50"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="{% url 'labor:location_tracking_dashboard' %}" class="quick-action">
            <i class="fas fa-map-marked-alt"></i>
            Location Tracking
        </a>
        <a href="{% url 'labor:safety_dashboard' %}" class="quick-action">
            <i class="fas fa-shield-alt"></i>
            Safety Dashboard
        </a>
        <a href="{% url 'labor:equipment_dashboard' %}" class="quick-action">
            <i class="fas fa-tools"></i>
            Equipment
        </a>
        <a href="{% url 'labor:task_create' %}" class="quick-action">
            <i class="fas fa-plus"></i>
            New Task
        </a>
        <a href="{% url 'labor:worker_create' %}" class="quick-action">
            <i class="fas fa-user-plus"></i>
            Add Worker
        </a>
    </div>

    <!-- Statistics Cards Row -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="fas fa-users stat-icon text-primary"></i>
                    <div class="labor-count text-primary">24</div>
                    <div class="labor-label">Active Workers</div>
                    <div class="mt-3">
                        <a href="{% url 'labor:worker_list' %}" class="btn btn-outline-primary btn-sm">
                            <small>View All <i class="fas fa-arrow-right ms-1"></i></small>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="fas fa-layer-group stat-icon text-success"></i>
                    <div class="labor-count text-success">8</div>
                    <div class="labor-label">Teams</div>
                    <div class="mt-3">
                        <a href="{% url 'labor:team_list' %}" class="btn btn-outline-success btn-sm">
                            <small>Manage <i class="fas fa-arrow-right ms-1"></i></small>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="fas fa-tasks stat-icon text-warning"></i>
                    <div class="labor-count text-warning">42</div>
                    <div class="labor-label">Active Tasks</div>
                    <div class="mt-3">
                        <a href="{% url 'labor:task_list' %}" class="btn btn-outline-warning btn-sm">
                            <small>View All <i class="fas fa-arrow-right ms-1"></i></small>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3 mb-4">
            <div class="card stat-card text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle stat-icon text-info"></i>
                    <div class="labor-count text-info">18</div>
                    <div class="labor-label">Completed Today</div>
                    <div class="mt-3">
                        <a href="{% url 'labor:task_list' %}?status=completed" class="btn btn-outline-info btn-sm">
                            <small>View <i class="fas fa-arrow-right ms-1"></i></small>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Live Worker Tracking Map -->
        <div class="col-lg-8 mb-4">
            <div class="map-card">
                <h3 class="section-title">
                    <i class="fas fa-map-marked-alt me-2"></i>
                    Live Worker Tracking
                </h3>
                
                <!-- Map Controls -->
                <div class="mb-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <div class="d-flex gap-2 align-items-center flex-wrap">
                                <button class="control-btn btn-sm" onclick="refreshMap()">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                                    <label class="form-check-label small" for="auto-refresh">
                                        Auto-refresh
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex gap-2 justify-content-end flex-wrap">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-geofences" checked>
                                    <label class="form-check-label small" for="show-geofences">
                                        Geofences
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="show-paths">
                                    <label class="form-check-label small" for="show-paths">
                                        Worker Paths
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Map Container -->
                <div id="live-tracking-map" class="rounded"></div>
                
                <!-- Worker Status Legend -->
                <div class="mt-3">
                    <h6 class="fw-bold mb-2">Worker Status Legend:</h6>
                    <div class="d-flex gap-3 flex-wrap">
                        <small><span class="worker-status-indicator status-working"></span>Working</small>
                        <small><span class="worker-status-indicator status-on_break"></span>On Break</small>
                        <small><span class="worker-status-indicator status-traveling"></span>Traveling</small>
                        <small><span class="worker-status-indicator status-available"></span>Available</small>
                        <small><span class="worker-status-indicator status-idle"></span>Idle</small>
                        <small><span class="worker-status-indicator status-off_duty"></span>Off Duty</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Tasks and Worker Status -->
        <div class="col-lg-4">
            <!-- Recent Tasks -->
            <div class="enhanced-card mb-4">
                <h3 class="section-title">
                    <i class="fas fa-tasks me-2"></i>
                    Recent Tasks
                </h3>
                
                <div class="task-list">
                    <div class="task-list-item priority-high">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="fw-bold mb-1">Water Quality Check</h6>
                                <p class="mb-1 text-muted small">Pond A-1 • Due today</p>
                                <span class="status-badge bg-warning">In Progress</span>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">2h ago</small>
                            </div>
                        </div>
                    </div>

                    <div class="task-list-item priority-medium">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="fw-bold mb-1">Feed Distribution</h6>
                                <p class="mb-1 text-muted small">Pond B-3 • Scheduled</p>
                                <span class="status-badge bg-primary">Assigned</span>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">4h ago</small>
                            </div>
                        </div>
                    </div>

                    <div class="task-list-item priority-low">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="fw-bold mb-1">Equipment Maintenance</h6>
                                <p class="mb-1 text-muted small">Aerator System • Weekly</p>
                                <span class="status-badge bg-success">Completed</span>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">6h ago</small>
                            </div>
                        </div>
                    </div>

                    <div class="task-list-item priority-urgent">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="fw-bold mb-1">Emergency Response</h6>
                                <p class="mb-1 text-muted small">Pond C-2 • Urgent</p>
                                <span class="status-badge bg-danger">Critical</span>
                            </div>
                            <div class="text-end">
                                <small class="text-muted">1h ago</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <a href="{% url 'labor:task_list' %}" class="btn btn-outline-primary btn-sm">
                        View All Tasks <i class="fas fa-arrow-right ms-1"></i>
                    </a>
                </div>
            </div>

            <!-- Worker Performance -->
            <div class="enhanced-card">
                <h3 class="section-title">
                    <i class="fas fa-chart-line me-2"></i>
                    Worker Performance
                </h3>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">John Smith</span>
                        <span class="badge bg-success">Excellent</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: 95%"></div>
                    </div>
                    <small class="text-muted">95% completion rate</small>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">Maria Garcia</span>
                        <span class="badge bg-primary">Good</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-primary" style="width: 88%"></div>
                    </div>
                    <small class="text-muted">88% completion rate</small>
                </div>

                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="fw-semibold">David Lee</span>
                        <span class="badge bg-warning">Average</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-warning" style="width: 72%"></div>
                    </div>
                    <small class="text-muted">72% completion rate</small>
                </div>

                <div class="text-center mt-3">
                    <a href="#" class="btn btn-outline-primary btn-sm">
                        View Reports <i class="fas fa-chart-bar ms-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Stats Row -->
    <div class="row mt-4">
        <div class="col-md-4 mb-4">
            <div class="enhanced-card text-center">
                <i class="fas fa-clock text-primary mb-3" style="font-size: 2rem;"></i>
                <h5 class="gradient-text">Work Hours Today</h5>
                <h3 class="text-primary">186 hrs</h3>
                <p class="text-muted mb-0">Across all workers</p>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="enhanced-card text-center">
                <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 2rem;"></i>
                <h5 class="gradient-text">Safety Alerts</h5>
                <h3 class="text-warning">3</h3>
                <p class="text-muted mb-0">Active safety concerns</p>
            </div>
        </div>

        <div class="col-md-4 mb-4">
            <div class="enhanced-card text-center">
                <i class="fas fa-award text-success mb-3" style="font-size: 2rem;"></i>
                <h5 class="gradient-text">Efficiency Rate</h5>
                <h3 class="text-success">87%</h3>
                <p class="text-muted mb-0">Overall team performance</p>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<!-- Google Maps API -->
<script src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=visualization,geometry&callback=initMap" async defer></script>

<script>
// Django context data
const USE_MOCK_DATA = {{ use_mock_data|yesno:"true,false" }};
const MOCK_DATA = {{ mock_data|safe }};
const TRACKED_WORKERS = {{ tracked_workers_json|safe }};
const GEOFENCES = {{ geofences_json|safe }};

// Simple map initialization
let map, infoWindow;

function initMap() {
    const defaultCenter = { lat: 10.3157, lng: 123.8854 };
    
    map = new google.maps.Map(document.getElementById('live-tracking-map'), {
        zoom: 12,
        center: defaultCenter,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
            {
                featureType: "all",
                elementType: "geometry.fill",
                stylers: [{ weight: "2.00" }]
            },
            {
                featureType: "all",
                elementType: "geometry.stroke",
                stylers: [{ color: "#9c9c9c" }]
            }
        ]
    });

    infoWindow = new google.maps.InfoWindow();

    // Use real or mock data
    let workers = [];
    
    if (USE_MOCK_DATA) {
        workers = MOCK_DATA.workers || [];
    } else {
        // Use real tracked workers data
        workers = TRACKED_WORKERS.filter(worker => 
            worker.current_latitude !== null && worker.current_longitude !== null
        ).map(worker => ({
            id: worker.id,
            name: worker.name,
            lat: parseFloat(worker.current_latitude),
            lng: parseFloat(worker.current_longitude),
            status: worker.status || 'working',
            role: worker.role || 'Worker'
        }));
    }

    // Add worker markers
    workers.forEach(worker => {
        const marker = new google.maps.Marker({
            position: { lat: worker.lat, lng: worker.lng },
            map: map,
            title: worker.name,
            icon: {
                path: google.maps.SymbolPath.CIRCLE,
                scale: 12,
                fillColor: getStatusColor(worker.status),
                fillOpacity: 0.9,
                strokeWeight: 3,
                strokeColor: '#ffffff'
            }
        });

        marker.addListener('click', () => {
            infoWindow.setContent(`
                <div class="p-2">
                    <h6>${worker.name}</h6>
                    <p class="mb-1">Role: ${worker.role || 'Worker'}</p>
                    <p class="mb-0">Status: <span class="badge bg-primary">${worker.status.replace('_', ' ')}</span></p>
                </div>
            `);
            infoWindow.open(map, marker);
        });
    });

    // Add geofences if available
    let geofences = [];
    if (USE_MOCK_DATA) {
        geofences = MOCK_DATA.geofences || [];
    } else {
        geofences = GEOFENCES || [];
    }

    geofences.forEach(geofence => {
        if (geofence.boundary && Array.isArray(geofence.boundary)) {
            const polygon = new google.maps.Polygon({
                paths: geofence.boundary.map(coord => ({ lat: coord[1], lng: coord[0] })),
                strokeColor: geofence.type === 'restricted' ? '#ff0000' : '#00ff00',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: geofence.type === 'restricted' ? '#ff0000' : '#00ff00',
                fillOpacity: 0.15
            });
            polygon.setMap(map);

            const geofenceInfoWindow = new google.maps.InfoWindow({
                content: `<div><strong>${geofence.name}</strong><br>Type: ${geofence.type}</div>`
            });

            polygon.addListener('click', (event) => {
                geofenceInfoWindow.setPosition(event.latLng);
                geofenceInfoWindow.open(map);
            });
        }
    });

    // Center map on workers if any exist
    if (workers.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        workers.forEach(worker => {
            bounds.extend(new google.maps.LatLng(worker.lat, worker.lng));
        });
        map.fitBounds(bounds);
        
        // Don't zoom in too much
        google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
            if (map.getZoom() > 15) {
                map.setZoom(15);
            }
        });
    }
}

function getStatusColor(status) {
    const colors = {
        'working': '#10b981',
        'on_break': '#f59e0b',
        'traveling': '#3b82f6',
        'available': '#8b5cf6',
        'idle': '#6b7280',
        'off_duty': '#ef4444'
    };
    return colors[status] || '#6b7280';
}

function refreshMap() {
    console.log('Refreshing map...');
    // Simulate refresh with visual feedback
    const btn = document.querySelector('[onclick="refreshMap()"]');
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    
    setTimeout(() => {
        btn.innerHTML = originalText;
    }, 1500);
}

// Handle map loading error
window.gm_authFailure = function() {
    document.getElementById('live-tracking-map').innerHTML = 
        '<div class="alert alert-warning text-center p-4">Google Maps API not configured. Please add your API key.</div>';
};

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    if (typeof google === 'undefined') {
        setTimeout(() => {
            if (typeof google === 'undefined') {
                document.getElementById('live-tracking-map').innerHTML = 
                    '<div class="alert alert-info text-center p-4">Loading map... Please wait or check your internet connection.</div>';
            }
        }, 5000);
    }
});
</script>

</body>
</html>
