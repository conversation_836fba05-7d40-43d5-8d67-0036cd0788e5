<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lunar Calendar - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    .lunar-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .lunar-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: lunar-sweep 4s infinite;
    }

    @keyframes lunar-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .lunar-card {
        background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
        backdrop-filter: blur(15px);
        border-radius: 15px;
        border: 2px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .lunar-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        border-color: rgba(255,255,255,0.4);
    }

    .moon-phase {
        font-size: 3em;
        text-shadow: 0 0 20px rgba(255,255,255,0.5);
        animation: moon-glow 3s ease-in-out infinite;
    }

    @keyframes moon-glow {
        0%, 100% { text-shadow: 0 0 20px rgba(255,255,255,0.5); }
        50% { text-shadow: 0 0 30px rgba(255,255,255,0.8); }
    }

    .importance-high {
        background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        animation: pulse-high 2s infinite;
    }
    .importance-medium {
        background: linear-gradient(45deg, #feca57, #ff9ff3);
    }
    .importance-low {
        background: linear-gradient(45deg, #48dbfb, #0abde3);
    }

    @keyframes pulse-high {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    .lunar-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: rgba(255,255,255,0.1);
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        border: 1px solid rgba(255,255,255,0.2);
    }

    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/full-calendar/" class="quick-action">
                    <i class="fas fa-calendar-alt"></i>
                    Full Calendar
                </a>
                <a href="/lunar/api/" class="quick-action">
                    <i class="fas fa-code"></i>
                    API Access
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid">
    <!-- Lunar Header -->
    <div class="lunar-header">
        <div class="position-relative">
            <h1><i class="fas fa-moon me-3"></i>Accurate Lunar Calendar</h1>
            <p class="lead mb-0">Astronomical calculations for precise shrimp farming timing</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🌑 AMAVASYA</span>
                <span class="badge bg-light text-dark me-2">🌕 POURNAMI</span>
                <span class="badge bg-light text-dark me-2">🌗 ASTAMI</span>
                <span class="badge bg-light text-dark me-2">🌘 NAVAMI</span>
                <span class="badge bg-success text-white ms-3">✅ FIXED & VERIFIED</span>
            </div>
            <div class="mt-2">
                <small class="text-light">
                    <i class="fas fa-check-circle me-1"></i>All lunar phases now displaying correctly with astronomical precision
                </small>
            </div>
        </div>
    </div>

    <!-- Lunar Statistics -->
    <div class="lunar-stats">
        <div class="stat-card">
            <div class="stat-number">{{ upcoming_days|length }}</div>
            <h6>Upcoming Events</h6>
            <p class="mb-0">Next 30 days</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">4</div>
            <h6>Lunar Phases</h6>
            <p class="mb-0">Tracked for shrimp farming</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ pending_treatments|length }}</div>
            <h6>Pending Treatments</h6>
            <p class="mb-0">Scheduled actions</p>
        </div>
        <div class="stat-card">
            <div class="stat-number">100%</div>
            <h6>Accuracy</h6>
            <p class="mb-0">Astronomical precision</p>
        </div>
    </div>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>Upcoming Lunar Events</h3>
        <div>
            <a href="/lunar/full-calendar/" class="btn btn-primary me-2">
                <i class="fas fa-calendar-alt me-2"></i>Full Calendar View
            </a>
            <button id="viewFullCalendarBtn" class="btn btn-outline-primary">
                <i class="fas fa-expand me-2"></i>Expand View
            </button>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Upcoming Lunar Days</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for day in upcoming_days %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="lunar-card h-100 {% if day.days_away == 0 %}border-primary{% endif %}">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <span class="moon-phase me-2">{{ day.moon_phase_emoji }}</span>
                                        <h5 class="mb-0">{{ day.phase }}</h5>
                                    </div>
                                    <span class="badge {% if day.importance == 'High' %}importance-high{% elif day.importance == 'Medium' %}importance-medium{% else %}importance-low{% endif %}">
                                        {{ day.importance }}
                                    </span>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted mb-3">{{ day.description }}</p>
                                    
                                    <div class="d-flex align-items-center mb-3">
                                        <i class="fas fa-calendar-day me-2 text-primary"></i>
                                        <span>{{ day.formatted_date }}</span>
                                    </div>
                                    
                                    {% if day.days_away == 0 %}
                                    <div class="alert alert-primary">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <strong>Today!</strong>
                                    </div>
                                    {% elif day.days_away == 1 %}
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-circle me-2"></i>
                                        <strong>Tomorrow!</strong>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-secondary">
                                        <i class="fas fa-clock me-2"></i>
                                        <strong>{{ day.days_away }} days away</strong>
                                    </div>
                                    {% endif %}
                                    
                                    <h6 class="mt-3 mb-2">Recommendations:</h6>
                                    <ul class="list-group">
                                        {% for recommendation in day.recommendations.all %}
                                        <li class="list-group-item">{{ recommendation.recommendation }}</li>
                                        {% empty %}
                                        <li class="list-group-item text-muted">No specific recommendations</li>
                                        {% endfor %}
                                        
                                        {% if day.phase == 'Astami' or day.phase == 'Amavasya' or day.phase == 'Pournami' or day.phase == 'Pournami' or day.phase == 'Pournami' %}
                                        <li class="list-group-item list-group-item-success">
                                            <i class="fas fa-flask me-2"></i> Apply <strong>probiotics</strong> to all ponds
                                        </li>
                                        {% elif day.phase == 'Navami' %}
                                        <li class="list-group-item list-group-item-warning">
                                            <i class="fas fa-vial me-2"></i> Apply <strong>minerals</strong> to all ponds
                                        </li>
                                        {% endif %}
                                    </ul>
                                    
                                    <div class="mt-3">
                                        <a href="/lunar/{{ day.id }}/" class="btn btn-primary btn-sm w-100">
                                            <i class="fas fa-tasks me-1"></i> Manage Treatments
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No upcoming lunar days found.
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Pending Treatments</h5>
                </div>
                <div class="card-body">
                    {% if pending_treatments %}
                    <div class="list-group">
                        {% for treatment in pending_treatments %}
                        <a href="/lunar/treatment/{{ treatment.pk }}/apply/" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <span class="badge {% if treatment.treatment_type == 'PROBIOTIC' %}bg-success{% elif treatment.treatment_type == 'MINERAL' %}bg-warning{% else %}bg-info{% endif %} me-1">
                                        {{ treatment.get_treatment_type_display }}
                                    </span>
                                    {{ treatment.pond.name }}
                                </h6>
                                <small>{{ treatment.lunar_day.date|date:"M d" }}</small>
                            </div>
                            <p class="mb-1">{{ treatment.quantity }} {{ treatment.unit }} for {{ treatment.lunar_day.phase }}</p>
                            <small>
                                <i class="fas fa-calendar-day me-1"></i>
                                {{ treatment.lunar_day.formatted_date }}
                            </small>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No pending treatments found.
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Lunar Calendar Importance</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-primary">
                        <h5 class="alert-heading">Why Lunar Calendar Matters</h5>
                        <p>The lunar cycle significantly impacts shrimp behavior, particularly during molting periods. Astami and Navami days are especially important for shrimp farming management.</p>
                    </div>
                    
                    <div class="mb-4">
                        <h6 class="mb-3">Astami (8th Day)</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span style="font-size: 1.5rem;" class="me-2">🌗</span>
                            <span>Eighth day of lunar fortnight</span>
                        </div>
                        <div class="alert alert-danger">
                            <strong>High Importance</strong>
                            <ul class="mb-0 mt-1">
                                <li>Shrimp are more likely to molt</li>
                                <li>Avoid feeding during this period</li>
                                <li>Minimize water exchange</li>
                                <li>Reduce pond disturbance</li>
                                <li><strong>Apply probiotics</strong> to improve water quality</li>
                            </ul>
                        </div class="mb-4">
                    </div class="mb-4">
                    
                    <div class="mb-4">
                        <h6 class="mb-3">Navami (9th Day)</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span style="font-size: 1.5rem;" class="me-2">🌘</span>
                            <span>Ninth day of lunar fortnight</span>
                        </div>
                        <div class="alert alert-warning">
                            <strong>Medium Importance</strong>
                            <ul class="mb-0 mt-1">
                                <li>Resume normal feeding</li>
                                <li>Monitor water quality closely</li>
                                <li>Check for molted shells</li>
                                <li><strong>Apply minerals</strong> to support post-molt recovery</li>
                            </ul>
                        </div>
                    
                    <div>
                        <h6 class="mb-3">Pournami (Full Moon)</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span style="font-size: 1.5rem;" class="me-2">🌕</span>
                            <span>Full moon day</span>
                        </div>
                        <div class="alert alert-danger">
                            <strong>High Importance</strong>
                            <ul class="mb-0 mt-1">
                                <li>Increased shrimp activity</li>
                                <li>Higher oxygen consumption</li>
                                <li>Potential stress on shrimp</li>
                                <li><strong>Apply probiotics</strong> to maintain water quality</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div>
                        <h6 class="mb-3">Pournami (Full Moon)</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span style="font-size: 1.5rem;" class="me-2">🌕</span>
                            <span>Full moon day</span>
                        </div>
                        <div class="alert alert-danger">
                            <strong>High Importance</strong>
                            <ul class="mb-0 mt-1">
                                <li>Increased shrimp activity</li>
                                <li>Higher oxygen consumption</li>
                                <li>Potential stress on shrimp</li>
                                <li><strong>Apply probiotics</strong> to maintain water quality</li>
                            </ul>
                        </div>
                    </div>
                    </div>
                    
                    <div>
                        <h6 class="mb-3">Pournami (Full Moon)</h6>
                        <div class="d-flex align-items-center mb-2">
                            <span style="font-size: 1.5rem;" class="me-2">🌕</span>
                            <span>Full moon day</span>
                        </div>
                        <div class="alert alert-danger">
                            <strong>High Importance</strong>
                            <ul class="mb-0 mt-1">
                                <li>Increased shrimp activity</li>
                                <li>Higher oxygen consumption</li>
                                <li>Potential stress on shrimp</li>
                                <li><strong>Apply probiotics</strong> to maintain water quality</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Full Calendar (Initially Hidden) -->
    <div id="fullCalendarSection" class="card" style="display: none;">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Full Lunar Calendar</h5>
            <button id="hideFullCalendarBtn" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times me-1"></i> Hide
            </button>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Phase</th>
                            <th>Description</th>
                            <th>Importance</th>
                            <th>Days Away</th>
                            <th>Recommendations</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for day in all_lunar_days %}
                        <tr class="{% if day.days_away == 0 %}table-primary{% elif day.days_away == 1 %}table-warning{% endif %}">
                            <td>{{ day.date|date:"M d, Y" }}</td>
                            <td>
                                <span style="font-size: 1.2rem;" class="me-1">{{ day.moon_phase_emoji }}</span>
                                {{ day.phase }}
                            </td>
                            <td>{{ day.description }}</td>
                            <td>
                                <span class="badge {% if day.importance == 'High' %}bg-danger{% elif day.importance == 'Medium' %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                    {{ day.importance }}
                                </span>
                            </td>
                            <td>
                                {% if day.days_away == 0 %}
                                <span class="badge bg-primary">Today</span>
                                {% elif day.days_away == 1 %}
                                <span class="badge bg-warning text-dark">Tomorrow</span>
                                {% else %}
                                {{ day.days_away }} days
                                {% endif %}
                            </td>
                            <td>
                                <ul class="mb-0">
                                    {% for recommendation in day.recommendations.all %}
                                    <li>{{ recommendation.recommendation }}</li>
                                    {% empty %}
                                    <span class="text-muted">No specific recommendations</span>
                                    {% endfor %}
                                </ul>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No lunar days found</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    // Full Calendar Toggle
    document.getElementById('viewFullCalendarBtn').addEventListener('click', function() {
        document.getElementById('fullCalendarSection').style.display = 'block';
        this.style.display = 'none';
    });
    
    document.getElementById('hideFullCalendarBtn').addEventListener('click', function() {
        document.getElementById('fullCalendarSection').style.display = 'none';
        document.getElementById('viewFullCalendarBtn').style.display = 'block';
    });
</script>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
