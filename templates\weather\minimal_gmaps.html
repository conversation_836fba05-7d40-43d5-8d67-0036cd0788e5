<!DOCTYPE html>
<html>
<head>
    <title>Minimal Google Maps</title>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        #map { height: 400px; width: 100%; border: 2px solid #333; }
        .info { background: #f0f0f0; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🗺️ Minimal Google Maps Test</h1>
    
    <div class="info">
        <strong>API Key:</strong> {{ google_maps_api_key|slice:":15" }}...
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <div id="status">Loading...</div>
    </div>

    <script>
        function updateStatus(msg) {
            document.getElementById('status').innerHTML = msg;
            console.log(msg);
        }

        function initMap() {
            updateStatus('✅ Google Maps API loaded!');
            
            const map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: 10.8231, lng: 106.6297 },
                zoom: 10
            });
            
            new google.maps.Marker({
                position: { lat: 10.8231, lng: 106.6297 },
                map: map,
                title: 'Test Location'
            });
            
            updateStatus('🎉 Map created successfully!');
        }

        window.onerror = function(msg, url, line) {
            updateStatus('❌ JavaScript Error: ' + msg);
        };

        updateStatus('⏳ Loading Google Maps API...');
    </script>
    
    <script src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"></script>
</body>
</html>
