{% extends "base.html" %}
{% load static %}

{% block title %}Advanced Mapping System - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .mapping-container {
        padding: 0;
        background: #f8f9fa;
        min-height: 100vh;
        position: relative;
    }

    .mapping-header {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 20px;
        position: relative;
        z-index: 1000;
    }

    .mapping-tools {
        position: absolute;
        top: 80px;
        left: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        min-width: 280px;
        max-height: calc(100vh - 120px);
        overflow-y: auto;
    }

    .mapping-tools h4 {
        margin-bottom: 20px;
        color: #2c3e50;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .tool-section {
        margin-bottom: 25px;
        padding-bottom: 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .tool-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .tool-section h5 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 15px;
        font-size: 1rem;
    }

    .tool-btn {
        width: 100%;
        margin-bottom: 10px;
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        background: white;
        color: #495057;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
        text-align: left;
    }

    .tool-btn:hover {
        border-color: #667eea;
        background: #f8f9ff;
        color: #667eea;
    }

    .tool-btn.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .tool-btn i {
        font-size: 1.1rem;
        width: 20px;
        text-align: center;
    }

    .layer-control {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
    }

    .layer-control label {
        margin: 0;
        font-weight: 500;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .layer-toggle {
        width: 50px;
        height: 26px;
        background: #ccc;
        border-radius: 13px;
        position: relative;
        cursor: pointer;
        transition: background 0.3s ease;
    }

    .layer-toggle.active {
        background: #667eea;
    }

    .layer-toggle::after {
        content: '';
        position: absolute;
        top: 2px;
        left: 2px;
        width: 22px;
        height: 22px;
        background: white;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }

    .layer-toggle.active::after {
        transform: translateX(24px);
    }

    .map-container {
        position: absolute;
        top: 80px;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    #advanced-map {
        width: 100%;
        height: 100%;
    }

    .map-info-panel {
        position: absolute;
        top: 80px;
        right: 20px;
        z-index: 1000;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        min-width: 300px;
        max-height: calc(100vh - 120px);
        overflow-y: auto;
        display: none;
    }

    .map-info-panel.show {
        display: block;
    }

    .coordinates-display {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .coordinate-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
    }

    .coordinate-item:last-child {
        margin-bottom: 0;
    }

    .measurement-display {
        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
        border: 1px solid #4caf50;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .measurement-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .measurement-item:last-child {
        margin-bottom: 0;
        font-weight: 700;
        color: #2e7d32;
        border-top: 1px solid #4caf50;
        padding-top: 8px;
    }

    .drawing-controls {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
    }

    .drawing-btn {
        padding: 10px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        background: white;
        color: #495057;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        font-size: 0.9rem;
    }

    .drawing-btn:hover {
        border-color: #667eea;
        background: #f8f9ff;
        color: #667eea;
    }

    .drawing-btn.active {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }

    .satellite-controls {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
    }

    .satellite-btn {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        background: white;
        color: #495057;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .satellite-btn:hover {
        background: #f8f9fa;
    }

    .satellite-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .geofence-list {
        max-height: 200px;
        overflow-y: auto;
    }

    .geofence-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 8px;
    }

    .geofence-item:last-child {
        margin-bottom: 0;
    }

    .geofence-name {
        font-weight: 500;
        color: #495057;
    }

    .geofence-actions {
        display: flex;
        gap: 5px;
    }

    .geofence-action {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        background: #667eea;
        color: white;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .geofence-action:hover {
        background: #5a67d8;
    }

    .geofence-action.delete {
        background: #e53e3e;
    }

    .geofence-action.delete:hover {
        background: #c53030;
    }

    .status-indicator {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1000;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 0.9rem;
        display: none;
    }

    .status-indicator.show {
        display: block;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .mapping-tools {
            left: 10px;
            right: 10px;
            min-width: auto;
            max-width: none;
        }

        .map-info-panel {
            left: 10px;
            right: 10px;
            min-width: auto;
            max-width: none;
        }

        .drawing-controls {
            grid-template-columns: 1fr;
        }

        .satellite-controls {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="mapping-container">
    <!-- Header -->
    <div class="mapping-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-1">🗺️ Advanced Mapping System</h1>
                <p class="mb-0" style="opacity: 0.9;">Professional mapping tools for farm and pond management</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-light" onclick="toggleInfoPanel()">
                    <i class="fas fa-info-circle me-2"></i> Info Panel
                </button>
                <button class="btn btn-outline-light" onclick="resetMap()">
                    <i class="fas fa-undo me-2"></i> Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Mapping Tools Panel -->
    <div class="mapping-tools">
        <h4><i class="fas fa-tools"></i> Mapping Tools</h4>

        <!-- Drawing Tools -->
        <div class="tool-section">
            <h5>Drawing Tools</h5>
            <button class="tool-btn" id="draw-polygon" onclick="setDrawingMode('polygon')">
                <i class="fas fa-draw-polygon"></i>
                <span>Draw Polygon</span>
            </button>
            <button class="tool-btn" id="draw-rectangle" onclick="setDrawingMode('rectangle')">
                <i class="fas fa-square"></i>
                <span>Draw Rectangle</span>
            </button>
            <button class="tool-btn" id="draw-circle" onclick="setDrawingMode('circle')">
                <i class="fas fa-circle"></i>
                <span>Draw Circle</span>
            </button>
            <button class="tool-btn" id="place-marker" onclick="setDrawingMode('marker')">
                <i class="fas fa-map-marker-alt"></i>
                <span>Place Marker</span>
            </button>
        </div>

        <!-- Measurement Tools -->
        <div class="tool-section">
            <h5>Measurement</h5>
            <button class="tool-btn" id="measure-distance" onclick="setMeasureMode('distance')">
                <i class="fas fa-ruler"></i>
                <span>Measure Distance</span>
            </button>
            <button class="tool-btn" id="measure-area" onclick="setMeasureMode('area')">
                <i class="fas fa-expand-arrows-alt"></i>
                <span>Measure Area</span>
            </button>
        </div>

        <!-- Layer Controls -->
        <div class="tool-section">
            <h5>Map Layers</h5>
            <div class="layer-control">
                <label><i class="fas fa-building"></i> Farms</label>
                <div class="layer-toggle active" onclick="toggleLayer('farms')"></div>
            </div>
            <div class="layer-control">
                <label><i class="fas fa-water"></i> Ponds</label>
                <div class="layer-toggle active" onclick="toggleLayer('ponds')"></div>
            </div>
            <div class="layer-control">
                <label><i class="fas fa-users"></i> Workers</label>
                <div class="layer-toggle active" onclick="toggleLayer('workers')"></div>
            </div>
            <div class="layer-control">
                <label><i class="fas fa-fan"></i> Aerators</label>
                <div class="layer-toggle active" onclick="toggleLayer('aerators')"></div>
            </div>
            <div class="layer-control">
                <label><i class="fas fa-cloud"></i> Weather</label>
                <div class="layer-toggle" onclick="toggleLayer('weather')"></div>
            </div>
        </div>

        <!-- Map Type Controls -->
        <div class="tool-section">
            <h5>Map Type</h5>
            <div class="satellite-controls">
                <button class="satellite-btn active" onclick="setMapType('roadmap')">Road</button>
                <button class="satellite-btn" onclick="setMapType('satellite')">Satellite</button>
                <button class="satellite-btn" onclick="setMapType('hybrid')">Hybrid</button>
                <button class="satellite-btn" onclick="setMapType('terrain')">Terrain</button>
            </div>
        </div>

        <!-- Geofencing -->
        <div class="tool-section">
            <h5>Geofences</h5>
            <button class="tool-btn" id="create-geofence" onclick="createGeofence()">
                <i class="fas fa-shield-alt"></i>
                <span>Create Geofence</span>
            </button>
            <div class="geofence-list" id="geofence-list">
                <!-- Geofences will be populated here -->
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="tool-section">
            <h5>Quick Actions</h5>
            <button class="tool-btn" onclick="getCurrentLocation()">
                <i class="fas fa-crosshairs"></i>
                <span>My Location</span>
            </button>
            <button class="tool-btn" onclick="fitAllMarkers()">
                <i class="fas fa-expand"></i>
                <span>Fit All</span>
            </button>
            <button class="tool-btn" onclick="clearDrawings()">
                <i class="fas fa-eraser"></i>
                <span>Clear Drawings</span>
            </button>
            <button class="tool-btn" onclick="exportMap()">
                <i class="fas fa-download"></i>
                <span>Export Map</span>
            </button>
        </div>
    </div>

    <!-- Map Container -->
    <div class="map-container">
        <div id="advanced-map"></div>
    </div>

    <!-- Info Panel -->
    <div class="map-info-panel" id="info-panel">
        <h4><i class="fas fa-info-circle"></i> Map Information</h4>

        <!-- Coordinates Display -->
        <div class="coordinates-display">
            <h6>Current Coordinates</h6>
            <div class="coordinate-item">
                <span>Latitude:</span>
                <span id="current-lat">--</span>
            </div>
            <div class="coordinate-item">
                <span>Longitude:</span>
                <span id="current-lng">--</span>
            </div>
            <div class="coordinate-item">
                <span>Zoom Level:</span>
                <span id="current-zoom">--</span>
            </div>
        </div>

        <!-- Measurement Display -->
        <div class="measurement-display" id="measurement-display" style="display: none;">
            <h6>Measurements</h6>
            <div id="measurement-content">
                <!-- Measurement results will appear here -->
            </div>
        </div>

        <!-- Drawing Controls -->
        <div class="drawing-controls">
            <button class="drawing-btn" onclick="undoLastDrawing()">
                <i class="fas fa-undo"></i><br>Undo
            </button>
            <button class="drawing-btn" onclick="redoLastDrawing()">
                <i class="fas fa-redo"></i><br>Redo
            </button>
            <button class="drawing-btn" onclick="saveDrawings()">
                <i class="fas fa-save"></i><br>Save
            </button>
            <button class="drawing-btn" onclick="loadDrawings()">
                <i class="fas fa-folder-open"></i><br>Load
            </button>
        </div>

        <!-- Selected Feature Info -->
        <div id="feature-info" style="display: none;">
            <h6>Selected Feature</h6>
            <div id="feature-details">
                <!-- Feature details will appear here -->
            </div>
        </div>
    </div>

    <!-- Status Indicator -->
    <div class="status-indicator" id="status-indicator">
        Ready
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let advancedMap;
    let drawingManager;
    let currentDrawingMode = null;
    let currentMeasureMode = null;
    let drawnShapes = [];
    let geofences = [];
    let layerVisibility = {
        farms: true,
        ponds: true,
        workers: true,
        aerators: true,
        weather: false
    };

    // Initialize advanced map
    function initAdvancedMap() {
        advancedMap = new google.maps.Map(document.getElementById('advanced-map'), {
            zoom: 12,
            center: { lat: {{ center_lat|default:"13.0827" }}, lng: {{ center_lng|default:"80.2707" }} },
            mapTypeId: 'roadmap',
            mapTypeControl: true,
            streetViewControl: true,
            fullscreenControl: true,
            zoomControl: true,
            scaleControl: true
        });

        // Initialize drawing manager
        drawingManager = new google.maps.drawing.DrawingManager({
            drawingMode: null,
            drawingControl: false,
            polygonOptions: {
                fillColor: '#667eea',
                fillOpacity: 0.3,
                strokeColor: '#667eea',
                strokeWeight: 2,
                editable: true,
                draggable: true
            },
            rectangleOptions: {
                fillColor: '#667eea',
                fillOpacity: 0.3,
                strokeColor: '#667eea',
                strokeWeight: 2,
                editable: true,
                draggable: true
            },
            circleOptions: {
                fillColor: '#667eea',
                fillOpacity: 0.3,
                strokeColor: '#667eea',
                strokeWeight: 2,
                editable: true,
                draggable: true
            },
            markerOptions: {
                draggable: true,
                animation: google.maps.Animation.DROP
            }
        });

        drawingManager.setMap(advancedMap);

        // Add event listeners
        setupEventListeners();
        
        // Load existing data
        loadMapData();
        
        showStatus('Advanced mapping system ready');
    }

    function setupEventListeners() {
        // Mouse move for coordinates
        advancedMap.addListener('mousemove', function(event) {
            updateCoordinates(event.latLng.lat(), event.latLng.lng());
        });

        // Zoom change
        advancedMap.addListener('zoom_changed', function() {
            document.getElementById('current-zoom').textContent = advancedMap.getZoom();
        });

        // Drawing complete
        drawingManager.addListener('overlaycomplete', function(event) {
            drawnShapes.push(event.overlay);
            calculateMeasurements(event.overlay, event.type);
            
            // Add edit listeners
            if (event.type === 'polygon' || event.type === 'rectangle') {
                event.overlay.addListener('click', function() {
                    selectFeature(event.overlay, event.type);
                });
            }
        });
    }

    function loadMapData() {
        // Load farms, ponds, workers, etc.
        // This would be populated from Django context
        showStatus('Loading map data...');
        
        setTimeout(() => {
            showStatus('Map data loaded successfully');
        }, 1000);
    }

    function setDrawingMode(mode) {
        // Clear previous active states
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        
        // Set new active state
        document.getElementById(`draw-${mode}`).classList.add('active');
        
        currentDrawingMode = mode;
        currentMeasureMode = null;
        
        switch(mode) {
            case 'polygon':
                drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                break;
            case 'rectangle':
                drawingManager.setDrawingMode(google.maps.drawing.OverlayType.RECTANGLE);
                break;
            case 'circle':
                drawingManager.setDrawingMode(google.maps.drawing.OverlayType.CIRCLE);
                break;
            case 'marker':
                drawingManager.setDrawingMode(google.maps.drawing.OverlayType.MARKER);
                break;
        }
        
        showStatus(`Drawing mode: ${mode}`);
    }

    function setMeasureMode(mode) {
        // Clear drawing mode
        drawingManager.setDrawingMode(null);
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        
        // Set measure mode
        document.getElementById(`measure-${mode}`).classList.add('active');
        currentMeasureMode = mode;
        currentDrawingMode = null;
        
        showStatus(`Measurement mode: ${mode}`);
    }

    function calculateMeasurements(overlay, type) {
        const measurementDisplay = document.getElementById('measurement-display');
        const measurementContent = document.getElementById('measurement-content');
        
        let measurements = '';
        
        if (type === 'polygon') {
            const area = google.maps.geometry.spherical.computeArea(overlay.getPath());
            const perimeter = google.maps.geometry.spherical.computeLength(overlay.getPath());
            
            measurements = `
                <div class="measurement-item">
                    <span>Area:</span>
                    <span>${(area / 10000).toFixed(2)} hectares</span>
                </div>
                <div class="measurement-item">
                    <span>Perimeter:</span>
                    <span>${(perimeter / 1000).toFixed(2)} km</span>
                </div>
            `;
        } else if (type === 'circle') {
            const radius = overlay.getRadius();
            const area = Math.PI * radius * radius;
            
            measurements = `
                <div class="measurement-item">
                    <span>Radius:</span>
                    <span>${radius.toFixed(2)} meters</span>
                </div>
                <div class="measurement-item">
                    <span>Area:</span>
                    <span>${(area / 10000).toFixed(2)} hectares</span>
                </div>
            `;
        } else if (type === 'rectangle') {
            const bounds = overlay.getBounds();
            const area = google.maps.geometry.spherical.computeArea([
                bounds.getNorthEast(),
                bounds.getNorthWest(),
                bounds.getSouthWest(),
                bounds.getSouthEast()
            ]);
            
            measurements = `
                <div class="measurement-item">
                    <span>Area:</span>
                    <span>${(area / 10000).toFixed(2)} hectares</span>
                </div>
            `;
        }
        
        measurementContent.innerHTML = measurements;
        measurementDisplay.style.display = 'block';
    }

    function toggleLayer(layer) {
        layerVisibility[layer] = !layerVisibility[layer];
        
        // Update toggle appearance
        const toggle = event.target;
        toggle.classList.toggle('active');
        
        // Implement layer visibility logic here
        showStatus(`${layer} layer ${layerVisibility[layer] ? 'enabled' : 'disabled'}`);
    }

    function setMapType(type) {
        advancedMap.setMapTypeId(type);
        
        // Update button states
        document.querySelectorAll('.satellite-btn').forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        showStatus(`Map type changed to ${type}`);
    }

    function toggleInfoPanel() {
        const panel = document.getElementById('info-panel');
        panel.classList.toggle('show');
    }

    function updateCoordinates(lat, lng) {
        document.getElementById('current-lat').textContent = lat.toFixed(6);
        document.getElementById('current-lng').textContent = lng.toFixed(6);
    }

    function getCurrentLocation() {
        if (navigator.geolocation) {
            showStatus('Getting your location...');
            navigator.geolocation.getCurrentPosition(function(position) {
                const pos = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                advancedMap.setCenter(pos);
                advancedMap.setZoom(16);
                
                new google.maps.Marker({
                    position: pos,
                    map: advancedMap,
                    title: 'Your Location',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="#4285f4" stroke="white" stroke-width="3"/>
                                <circle cx="15" cy="15" r="4" fill="white"/>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    }
                });
                
                showStatus('Location found');
            }, function() {
                showStatus('Error: Unable to get location');
            });
        } else {
            showStatus('Error: Geolocation not supported');
        }
    }

    function fitAllMarkers() {
        const bounds = new google.maps.LatLngBounds();
        
        // Add all drawn shapes to bounds
        drawnShapes.forEach(shape => {
            if (shape.getPosition) {
                bounds.extend(shape.getPosition());
            } else if (shape.getBounds) {
                bounds.union(shape.getBounds());
            } else if (shape.getPath) {
                shape.getPath().forEach(point => bounds.extend(point));
            }
        });
        
        if (!bounds.isEmpty()) {
            advancedMap.fitBounds(bounds);
            showStatus('Fitted all markers');
        } else {
            showStatus('No markers to fit');
        }
    }

    function clearDrawings() {
        drawnShapes.forEach(shape => shape.setMap(null));
        drawnShapes = [];
        document.getElementById('measurement-display').style.display = 'none';
        showStatus('All drawings cleared');
    }

    function resetMap() {
        clearDrawings();
        advancedMap.setCenter({ lat: {{ center_lat|default:"13.0827" }}, lng: {{ center_lng|default:"80.2707" }} });
        advancedMap.setZoom(12);
        drawingManager.setDrawingMode(null);
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        showStatus('Map reset');
    }

    function showStatus(message) {
        const indicator = document.getElementById('status-indicator');
        indicator.textContent = message;
        indicator.classList.add('show');
        
        setTimeout(() => {
            indicator.classList.remove('show');
        }, 3000);
    }

    function createGeofence() {
        showStatus('Click on the map to create a geofence');
        // Implementation for geofence creation
    }

    function exportMap() {
        // Implementation for map export
        showStatus('Exporting map...');
    }

    function saveDrawings() {
        // Implementation for saving drawings
        showStatus('Drawings saved');
    }

    function loadDrawings() {
        // Implementation for loading drawings
        showStatus('Drawings loaded');
    }

    function undoLastDrawing() {
        if (drawnShapes.length > 0) {
            const lastShape = drawnShapes.pop();
            lastShape.setMap(null);
            showStatus('Last drawing undone');
        }
    }

    function redoLastDrawing() {
        // Implementation for redo
        showStatus('Redo not available');
    }

    function selectFeature(overlay, type) {
        // Implementation for feature selection
        showStatus(`Selected ${type}`);
    }

    // Initialize when page loads
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof google !== 'undefined') {
            initAdvancedMap();
        }
    });
</script>
{% endblock %}
