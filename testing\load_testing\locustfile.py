"""
Load Testing Suite for Shrimp Farm Guardian
High-performance load testing with realistic user scenarios
"""

import json
import random
import time
from datetime import datetime, timedelta
from locust import HttpUser, task, between, events
from locust.contrib.fasthttp import <PERSON>HttpUser
import jwt
import uuid


class Shrimp<PERSON><PERSON><PERSON><PERSON>ianUser(FastHttpUser):
    """
    Simulates realistic user behavior for load testing
    """
    wait_time = between(1, 5)
    
    def on_start(self):
        """Initialize user session"""
        self.auth_token = None
        self.farm_id = None
        self.device_ids = []
        self.user_id = str(uuid.uuid4())
        
        # Login and setup
        self.login()
        self.setup_farm()
    
    def login(self):
        """Authenticate user and get JWT token"""
        login_data = {
            "username": f"testuser_{self.user_id[:8]}",
            "password": "testpass123"
        }
        
        with self.client.post("/api/v1/auth/login/", 
                             json=login_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                response.success()
            elif response.status_code == 404:
                # Create user if doesn't exist
                self.create_user()
                self.login()
            else:
                response.failure(f"Login failed: {response.status_code}")
    
    def create_user(self):
        """Create test user"""
        user_data = {
            "username": f"testuser_{self.user_id[:8]}",
            "email": f"test_{self.user_id[:8]}@example.com",
            "password": "testpass123",
            "first_name": "Test",
            "last_name": "User"
        }
        
        with self.client.post("/api/v1/auth/register/", 
                             json=user_data, 
                             catch_response=True) as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"User creation failed: {response.status_code}")
    
    def setup_farm(self):
        """Create test farm and devices"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        # Create farm
        farm_data = {
            "name": f"Test Farm {self.user_id[:8]}",
            "location": "Test Location",
            "latitude": random.uniform(10.0, 11.0),
            "longitude": random.uniform(106.0, 107.0),
            "area_hectares": random.uniform(1.0, 10.0)
        }
        
        with self.client.post("/api/v1/farms/", 
                             json=farm_data, 
                             headers=headers,
                             catch_response=True) as response:
            if response.status_code in [200, 201]:
                self.farm_id = response.json().get("id")
                response.success()
            else:
                response.failure(f"Farm creation failed: {response.status_code}")
        
        # Create devices
        if self.farm_id:
            for i in range(5):  # Create 5 devices per farm
                device_data = {
                    "name": f"Test Device {i+1}",
                    "device_type": "water_quality_sensor",
                    "farm": self.farm_id,
                    "mac_address": f"AA:BB:CC:DD:EE:{i:02d}",
                    "firmware_version": "1.0.0"
                }
                
                with self.client.post("/api/v1/devices/", 
                                     json=device_data, 
                                     headers=headers,
                                     catch_response=True) as response:
                    if response.status_code in [200, 201]:
                        self.device_ids.append(response.json().get("id"))
                        response.success()
                    else:
                        response.failure(f"Device creation failed: {response.status_code}")
    
    @task(10)
    def view_dashboard(self):
        """View main dashboard - most common action"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get("/api/v1/analytics/dashboard/", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Dashboard view failed: {response.status_code}")
    
    @task(8)
    def get_sensor_data(self):
        """Retrieve latest sensor readings"""
        if not self.auth_token or not self.device_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        device_id = random.choice(self.device_ids)
        
        with self.client.get(f"/api/v1/devices/{device_id}/readings/latest/", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Sensor data retrieval failed: {response.status_code}")
    
    @task(5)
    def submit_sensor_reading(self):
        """Submit new sensor reading"""
        if not self.auth_token or not self.device_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        device_id = random.choice(self.device_ids)
        
        sensor_data = {
            "device": device_id,
            "sensor_type": random.choice(["temperature", "ph", "dissolved_oxygen", "salinity"]),
            "value": random.uniform(20.0, 35.0),
            "unit": "celsius",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "quality": "good"
        }
        
        with self.client.post("/api/v1/sensors/readings/", 
                             json=sensor_data, 
                             headers=headers,
                             catch_response=True) as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"Sensor reading submission failed: {response.status_code}")
    
    @task(3)
    def get_historical_data(self):
        """Retrieve historical sensor data"""
        if not self.auth_token or not self.device_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        device_id = random.choice(self.device_ids)
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=7)
        
        params = {
            "device_id": device_id,
            "start_date": start_date.isoformat() + "Z",
            "end_date": end_date.isoformat() + "Z",
            "interval": "1h"
        }
        
        with self.client.get("/api/v1/sensors/readings/", 
                           params=params,
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Historical data retrieval failed: {response.status_code}")
    
    @task(2)
    def get_alerts(self):
        """Check active alerts"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get("/api/v1/alerts/", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Alerts retrieval failed: {response.status_code}")
    
    @task(2)
    def get_weather_data(self):
        """Get current weather data"""
        if not self.auth_token or not self.farm_id:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get(f"/api/v1/weather/current/?farm_id={self.farm_id}", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Weather data retrieval failed: {response.status_code}")
    
    @task(1)
    def generate_report(self):
        """Generate analytics report"""
        if not self.auth_token or not self.farm_id:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        report_data = {
            "template_id": 1,
            "date_range_start": (datetime.utcnow() - timedelta(days=30)).date().isoformat(),
            "date_range_end": datetime.utcnow().date().isoformat(),
            "format": "json",
            "filters": {
                "farm_id": self.farm_id
            }
        }
        
        with self.client.post("/api/v1/reports/generate/", 
                             json=report_data, 
                             headers=headers,
                             catch_response=True) as response:
            if response.status_code in [200, 201, 202]:
                response.success()
            else:
                response.failure(f"Report generation failed: {response.status_code}")
    
    @task(1)
    def device_command(self):
        """Send command to device"""
        if not self.auth_token or not self.device_ids:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        device_id = random.choice(self.device_ids)
        
        command_data = {
            "command": "set_parameter",
            "parameters": {
                "target_temperature": random.uniform(26.0, 30.0),
                "target_ph": random.uniform(7.0, 8.5)
            }
        }
        
        with self.client.post(f"/api/v1/devices/{device_id}/commands/", 
                             json=command_data, 
                             headers=headers,
                             catch_response=True) as response:
            if response.status_code in [200, 201, 202]:
                response.success()
            else:
                response.failure(f"Device command failed: {response.status_code}")


class AdminUser(FastHttpUser):
    """
    Simulates admin user behavior for system management
    """
    wait_time = between(5, 15)
    weight = 1  # Lower weight for admin users
    
    def on_start(self):
        """Initialize admin session"""
        self.auth_token = None
        self.admin_login()
    
    def admin_login(self):
        """Admin authentication"""
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        with self.client.post("/api/v1/auth/login/", 
                             json=login_data, 
                             catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                response.success()
            else:
                response.failure(f"Admin login failed: {response.status_code}")
    
    @task(5)
    def system_health_check(self):
        """Check system health"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get("/health/", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(3)
    def view_metrics(self):
        """View system metrics"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get("/metrics", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Metrics view failed: {response.status_code}")
    
    @task(2)
    def admin_dashboard(self):
        """View admin dashboard"""
        if not self.auth_token:
            return
        
        headers = {"Authorization": f"Bearer {self.auth_token}"}
        
        with self.client.get("/admin/", 
                           headers=headers,
                           catch_response=True) as response:
            if response.status_code == 200:
                response.success()
            else:
                response.failure(f"Admin dashboard failed: {response.status_code}")


class IoTDeviceSimulator(FastHttpUser):
    """
    Simulates IoT device behavior for sensor data submission
    """
    wait_time = between(10, 30)  # Devices send data every 10-30 seconds
    weight = 5  # Higher weight for device simulation
    
    def on_start(self):
        """Initialize device simulation"""
        self.device_id = str(uuid.uuid4())
        self.device_token = self.get_device_token()
    
    def get_device_token(self):
        """Get device authentication token"""
        # Simulate device authentication
        return f"device_token_{self.device_id[:8]}"
    
    @task(10)
    def send_sensor_data(self):
        """Send sensor data from IoT device"""
        sensor_types = ["temperature", "ph", "dissolved_oxygen", "salinity", "turbidity"]
        
        for sensor_type in sensor_types:
            sensor_data = {
                "device_id": self.device_id,
                "sensor_type": sensor_type,
                "value": self.generate_sensor_value(sensor_type),
                "unit": self.get_sensor_unit(sensor_type),
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "quality": random.choice(["good", "fair", "poor"])
            }
            
            headers = {"Authorization": f"Device {self.device_token}"}
            
            with self.client.post("/api/v1/iot/sensor-data/", 
                                 json=sensor_data, 
                                 headers=headers,
                                 catch_response=True) as response:
                if response.status_code in [200, 201]:
                    response.success()
                else:
                    response.failure(f"Sensor data submission failed: {response.status_code}")
    
    def generate_sensor_value(self, sensor_type):
        """Generate realistic sensor values"""
        ranges = {
            "temperature": (25.0, 32.0),
            "ph": (6.5, 8.5),
            "dissolved_oxygen": (4.0, 8.0),
            "salinity": (15.0, 35.0),
            "turbidity": (0.0, 10.0)
        }
        
        min_val, max_val = ranges.get(sensor_type, (0.0, 100.0))
        return round(random.uniform(min_val, max_val), 2)
    
    def get_sensor_unit(self, sensor_type):
        """Get unit for sensor type"""
        units = {
            "temperature": "celsius",
            "ph": "ph",
            "dissolved_oxygen": "mg/L",
            "salinity": "ppt",
            "turbidity": "NTU"
        }
        
        return units.get(sensor_type, "unit")
    
    @task(1)
    def device_heartbeat(self):
        """Send device heartbeat"""
        heartbeat_data = {
            "device_id": self.device_id,
            "status": "online",
            "battery_level": random.randint(20, 100),
            "signal_strength": random.randint(-80, -30),
            "firmware_version": "1.0.0"
        }
        
        headers = {"Authorization": f"Device {self.device_token}"}
        
        with self.client.post("/api/v1/iot/heartbeat/", 
                             json=heartbeat_data, 
                             headers=headers,
                             catch_response=True) as response:
            if response.status_code in [200, 201]:
                response.success()
            else:
                response.failure(f"Device heartbeat failed: {response.status_code}")


# Event handlers for custom metrics
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Custom request metrics"""
    if exception:
        print(f"Request failed: {name} - {exception}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """Test start event"""
    print("Load test starting...")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """Test stop event"""
    print("Load test completed.")
    
    # Print summary statistics
    stats = environment.stats
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Total failures: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"95th percentile: {stats.total.get_response_time_percentile(0.95):.2f}ms")
    print(f"99th percentile: {stats.total.get_response_time_percentile(0.99):.2f}ms")
