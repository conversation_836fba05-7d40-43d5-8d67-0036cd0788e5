<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring & IoT Control Center - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-header p {
            color: #64748b;
            font-size: 1.1rem;
        }

        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            margin-bottom: 20px;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stats-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stats-label {
            color: #64748b;
            font-weight: 500;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn {
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .monitoring-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s ease;
        }

        .monitoring-card:hover {
            transform: translateY(-2px);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }

        .metric-display {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
        }

        .metric-unit {
            font-size: 0.9rem;
            color: #64748b;
            font-weight: 500;
        }

        .alert-panel {
            border-left: 4px solid;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
        }

        .alert-critical { border-left-color: #dc3545; background-color: #f8d7da; }
        .alert-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .alert-info { border-left-color: #17a2b8; background-color: #d1ecf1; }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-button {
            padding: 8px 16px;
            border-radius: 20px;
            border: 2px solid;
            background: transparent;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .control-on {
            border-color: #28a745;
            color: #28a745;
        }

        .control-on:hover {
            background-color: #28a745;
            color: white;
        }

        .control-off {
            border-color: #dc3545;
            color: #dc3545;
        }

        .control-off:hover {
            background-color: #dc3545;
            color: white;
        }

        .weather-widget {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .weather-temp {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .chart-mini {
            height: 120px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>🎛️ Monitoring & IoT Control Center</h1>
        <p>Unified monitoring, control, and management dashboard for all farm operations</p>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="{% url 'realtime_monitoring' %}" class="quick-action">
                <i class="fas fa-satellite-dish"></i>
                <span>Real-time Dashboard</span>
                <span class="badge bg-success ms-1">LIVE</span>
            </a>
            <a href="{% url 'iot_devices' %}" class="quick-action">
                <i class="fas fa-microchip"></i>
                <span>IoT Devices</span>
            </a>
            <a href="{% url 'computer_vision' %}" class="quick-action">
                <i class="fas fa-eye"></i>
                <span>Computer Vision</span>
                <span class="badge bg-info ms-1">AI</span>
            </a>
            <a href="{% url 'blockchain_dashboard' %}" class="quick-action">
                <i class="fas fa-link"></i>
                <span>Blockchain</span>
                <span class="badge bg-warning ms-1">NEW</span>
            </a>
            <a href="{% url 'arvr_dashboard' %}" class="quick-action">
                <i class="fas fa-vr-cardboard"></i>
                <span>AR/VR</span>
                <span class="badge bg-danger ms-1">BETA</span>
            </a>
            <a href="{% url 'feed:feed_analytics' %}" class="quick-action">
                <i class="fas fa-chart-line"></i>
                <span>Feed Analytics</span>
            </a>
            <a href="{% url 'feed:feed_recommendations' %}" class="quick-action">
                <i class="fas fa-brain"></i>
                <span>AI Recommendations</span>
            </a>
            <a href="{% url 'alerts:alert_list' %}" class="quick-action">
                <i class="fas fa-bell"></i>
                <span>Alert Center</span>
                <span class="badge bg-danger ms-1">2</span>
            </a>
            <a href="{% url 'iot_integration:dashboard' %}" class="quick-action">
                <i class="fas fa-network-wired"></i>
                <span>Network Status</span>
            </a>
        </div>
    </div>

    <!-- Overall System Status -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-value">94%</div>
                <div class="stats-label">System Health</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-primary">
                    <i class="fas fa-wifi"></i>
                </div>
                <div class="stats-value">47/52</div>
                <div class="stats-label">Devices Online</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stats-value">3</div>
                <div class="stats-label">Active Alerts</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <div class="stats-icon text-info">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="stats-value">12</div>
                <div class="stats-label">Auto Rules Active</div>
            </div>
        </div>
    </div>

    <!-- Critical Alerts -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-exclamation-circle me-2"></i>
                Critical Alerts & Notifications
            </h5>
        </div>
        <div class="card-body">
            <div class="alert-panel alert-critical">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-thermometer-full me-2"></i>High Temperature Alert</h6>
                        <p class="mb-1">Pond A-03: Water temperature reached 32.5°C</p>
                        <small class="text-muted">Alert triggered: 15 minutes ago</small>
                    </div>
                    <button class="btn btn-sm btn-danger">Take Action</button>
                </div>
            </div>

            <div class="alert-panel alert-warning">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-wifi me-2"></i>Device Connection Issue</h6>
                        <p class="mb-1">Aerator Controller B-07 offline for 45 minutes</p>
                        <small class="text-muted">Last seen: 10:15 AM</small>
                    </div>
                    <button class="btn btn-sm btn-warning">Reconnect</button>
                </div>
            </div>

            <div class="alert-panel alert-info">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="mb-1"><i class="fas fa-cloud-rain me-2"></i>Weather Update</h6>
                        <p class="mb-1">Heavy rain expected in 2 hours - Auto systems activated</p>
                        <small class="text-muted">Weather service: 5 minutes ago</small>
                    </div>
                    <button class="btn btn-sm btn-info">View Details</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Water Quality Monitoring -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-water me-2"></i>
                Real-time Water Quality Monitoring
            </h5>
        </div>
        <div class="card-body">
            <div class="device-grid">
                <div class="monitoring-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="mb-1">
                                <span class="status-indicator status-online"></span>
                                Temperature
                            </h6>
                            <small class="text-muted">12 sensors active</small>
                        </div>
                        <div class="text-end">
                            <div class="metric-display">28.5<span class="metric-unit">°C</span></div>
                        </div>
                    </div>
                    <div class="chart-mini">
                        <i class="fas fa-chart-line me-2"></i>Trend: Stable
                    </div>
                </div>

                <div class="monitoring-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="mb-1">
                                <span class="status-indicator status-warning"></span>
                                Dissolved Oxygen
                            </h6>
                            <small class="text-muted">8 sensors, 1 warning</small>
                        </div>
                        <div class="text-end">
                            <div class="metric-display">5.2<span class="metric-unit">mg/L</span></div>
                        </div>
                    </div>
                    <div class="chart-mini">
                        <i class="fas fa-chart-line me-2"></i>Trend: Declining
                    </div>
                </div>

                <div class="monitoring-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="mb-1">
                                <span class="status-indicator status-online"></span>
                                pH Level
                            </h6>
                            <small class="text-muted">10 sensors active</small>
                        </div>
                        <div class="text-end">
                            <div class="metric-display">7.4<span class="metric-unit">pH</span></div>
                        </div>
                    </div>
                    <div class="chart-mini">
                        <i class="fas fa-chart-line me-2"></i>Trend: Optimal
                    </div>
                </div>

                <div class="monitoring-card">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h6 class="mb-1">
                                <span class="status-indicator status-online"></span>
                                Salinity
                            </h6>
                            <small class="text-muted">6 sensors active</small>
                        </div>
                        <div class="text-end">
                            <div class="metric-display">28.3<span class="metric-unit">ppt</span></div>
                        </div>
                    </div>
                    <div class="chart-mini">
                        <i class="fas fa-chart-line me-2"></i>Trend: Good
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment Control Center -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        Equipment Control Center
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Aerator Controls -->
                        <div class="col-md-6">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-fan me-2"></i>Aerators
                                        </h6>
                                        <small class="text-muted">8 units, 6 active</small>
                                    </div>
                                    <div>
                                        <button class="control-button control-on me-2">AUTO</button>
                                        <button class="control-button control-off">MANUAL</button>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="small text-muted">Power</div>
                                        <div class="fw-bold">2.4kW</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Efficiency</div>
                                        <div class="fw-bold">94%</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Runtime</div>
                                        <div class="fw-bold">6.2h</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pump Controls -->
                        <div class="col-md-6">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-pump-medical me-2"></i>Water Pumps
                                        </h6>
                                        <small class="text-muted">4 units, 3 active</small>
                                    </div>
                                    <div>
                                        <button class="control-button control-on me-2">ON</button>
                                        <button class="control-button control-off">OFF</button>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="small text-muted">Flow Rate</div>
                                        <div class="fw-bold">150L/min</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Pressure</div>
                                        <div class="fw-bold">2.1 bar</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Status</div>
                                        <div class="fw-bold text-success">Normal</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lighting Controls -->
                        <div class="col-md-6">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-lightbulb me-2"></i>LED Lighting
                                        </h6>
                                        <small class="text-muted">12 zones, 10 active</small>
                                    </div>
                                    <div>
                                        <button class="control-button control-on me-2">AUTO</button>
                                        <button class="control-button control-off">MANUAL</button>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="small text-muted">Intensity</div>
                                        <div class="fw-bold">75%</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Schedule</div>
                                        <div class="fw-bold">Day Mode</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Power</div>
                                        <div class="fw-bold">1.8kW</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Temperature Control -->
                        <div class="col-md-6">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-thermometer-half me-2"></i>Temperature Control
                                        </h6>
                                        <small class="text-muted">Heating/Cooling systems</small>
                                    </div>
                                    <div>
                                        <button class="control-button control-on me-2">AUTO</button>
                                        <button class="control-button control-off">MANUAL</button>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="small text-muted">Target</div>
                                        <div class="fw-bold">28°C</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Current</div>
                                        <div class="fw-bold">28.5°C</div>
                                    </div>
                                    <div class="col-4">
                                        <div class="small text-muted">Mode</div>
                                        <div class="fw-bold text-info">Cooling</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Weather & Environment -->
        <div class="col-md-4">
            <div class="weather-widget mb-3">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="mb-1">Current Weather</h6>
                        <small>Updated 5 min ago</small>
                    </div>
                    <i class="fas fa-cloud-sun fa-2x"></i>
                </div>
                <div class="weather-temp mb-2">28°C</div>
                <div class="mb-3">Partly Cloudy</div>
                <div class="row text-center small">
                    <div class="col-6">
                        <div>Humidity</div>
                        <div class="fw-bold">75%</div>
                    </div>
                    <div class="col-6">
                        <div>Wind</div>
                        <div class="fw-bold">12 km/h</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        Worker Status
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Active Workers</span>
                        <span class="badge bg-success">18/24</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>On Break</span>
                        <span class="badge bg-warning">3</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Off Duty</span>
                        <span class="badge bg-secondary">3</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Efficiency Rate</span>
                        <span class="fw-bold text-success">94%</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Analytics & Reports -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chart-bar me-2"></i>
                Analytics & Performance Reports
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <button class="btn btn-primary w-100">
                        <i class="fas fa-file-chart-line me-2"></i>
                        Water Quality Report
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-success w-100">
                        <i class="fas fa-chart-area me-2"></i>
                        Equipment Performance
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-warning w-100">
                        <i class="fas fa-chart-pie me-2"></i>
                        Energy Consumption
                    </button>
                </div>
                <div class="col-md-3">
                    <button class="btn btn-info w-100">
                        <i class="fas fa-download me-2"></i>
                        Export All Data
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Automation & AI -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-robot me-2"></i>
                Smart Automation & AI Control
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="monitoring-card">
                        <h6><i class="fas fa-brain me-2"></i>AI Optimization Rules</h6>
                        <ul class="mb-0 small">
                            <li>Auto-adjust aerators based on DO levels (Active)</li>
                            <li>Temperature control based on weather forecast (Active)</li>
                            <li>Feeding schedule optimization (Active)</li>
                            <li>Energy consumption optimization (Active)</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="monitoring-card">
                        <h6><i class="fas fa-clock me-2"></i>Recent AI Actions</h6>
                        <ul class="mb-0 small">
                            <li>11:30 AM: Increased aeration in Pond A-03</li>
                            <li>10:45 AM: Adjusted lighting schedule</li>
                            <li>09:15 AM: Optimized pump flow rates</li>
                            <li>08:30 AM: Triggered morning feeding cycle</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Monitoring & AI Modules -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-brain me-2"></i>
                        Advanced Monitoring & AI Modules
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Computer Vision Module -->
                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-eye me-2"></i>Computer Vision
                                        </h6>
                                        <small class="text-muted">AI-powered visual monitoring</small>
                                    </div>
                                    <span class="badge bg-info">AI</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Cameras</div>
                                        <div class="fw-bold">12/15</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Accuracy</div>
                                        <div class="fw-bold">97.3%</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'computer_vision' %}" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>View Module
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Blockchain & Supply Chain -->
                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-link me-2"></i>Blockchain
                                        </h6>
                                        <small class="text-muted">Supply chain tracking</small>
                                    </div>
                                    <span class="badge bg-warning">NEW</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Records</div>
                                        <div class="fw-bold">1,247</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Integrity</div>
                                        <div class="fw-bold">100%</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'blockchain_dashboard' %}" class="btn btn-outline-warning btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>View Module
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- AR/VR Experience -->
                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-vr-cardboard me-2"></i>AR/VR
                                        </h6>
                                        <small class="text-muted">Immersive monitoring</small>
                                    </div>
                                    <span class="badge bg-danger">BETA</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Sessions</div>
                                        <div class="fw-bold">23</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Uptime</div>
                                        <div class="fw-bold">89%</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'arvr_dashboard' %}" class="btn btn-outline-danger btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>View Module
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Feed Analytics & AI Section -->
                    <div class="row g-3 mt-2">
                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-chart-line me-2"></i>Feed Analytics
                                        </h6>
                                        <small class="text-muted">Consumption analysis</small>
                                    </div>
                                    <span class="badge bg-success">ACTIVE</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Efficiency</div>
                                        <div class="fw-bold">92.7%</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Savings</div>
                                        <div class="fw-bold">$2,340</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'feed:feed_analytics' %}" class="btn btn-outline-success btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>View Analytics
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-brain me-2"></i>AI Recommendations
                                        </h6>
                                        <small class="text-muted">Smart feed optimization</small>
                                    </div>
                                    <span class="badge bg-primary">AI</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Models</div>
                                        <div class="fw-bold">4</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Accuracy</div>
                                        <div class="fw-bold">95.1%</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'feed:feed_recommendations' %}" class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>View AI Module
                                    </a>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="monitoring-card">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h6 class="mb-1">
                                            <i class="fas fa-robot me-2"></i>AI Demo
                                        </h6>
                                        <small class="text-muted">Interactive AI showcase</small>
                                    </div>
                                    <span class="badge bg-secondary">DEMO</span>
                                </div>
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Features</div>
                                        <div class="fw-bold">8</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Usage</div>
                                        <div class="fw-bold">156</div>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{% url 'feed:ai_demo' %}" class="btn btn-outline-secondary btn-sm w-100">
                                        <i class="fas fa-external-link-alt me-1"></i>Launch Demo
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Management -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-server me-2"></i>
                System Management & Settings
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-2">
                    <button class="btn btn-outline-primary w-100">
                        <i class="fas fa-plus me-2"></i>
                        Add Device
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-success w-100">
                        <i class="fas fa-sync me-2"></i>
                        Sync All
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-warning w-100">
                        <i class="fas fa-wrench me-2"></i>
                        Maintenance
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-info w-100">
                        <i class="fas fa-backup me-2"></i>
                        Backup
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100">
                        <i class="fas fa-cog me-2"></i>
                        Settings
                    </button>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-dark w-100">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Development Notice -->
    <div class="alert alert-info mt-4">
        <h6><i class="fas fa-info-circle me-2"></i>Unified Monitoring & Control Center</h6>
        <p class="mb-0">
            Comprehensive monitoring and control dashboard integrating all farm systems: water quality, IoT devices, 
            equipment automation, weather monitoring, worker management, and AI-powered optimization. 
            Real-time data updates every 30 seconds with instant alert notifications.
        </p>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JavaScript for real-time updates -->
<script>
    // Simulate real-time data updates
    function updateMetrics() {
        // This would connect to WebSocket or polling API in production
        console.log('Updating real-time metrics...');
    }

    // Control equipment functions
    function toggleEquipment(type, action) {
        console.log(`${action} ${type} equipment`);
        // This would send API calls to control actual equipment
    }

    // Initialize real-time updates
    setInterval(updateMetrics, 30000); // Update every 30 seconds

    // Add click handlers for control buttons
    document.querySelectorAll('.control-button').forEach(button => {
        button.addEventListener('click', function() {
            // Toggle active state
            this.classList.toggle('control-on');
            this.classList.toggle('control-off');
        });
    });
</script>

</body>
</html>
