#!/usr/bin/env python
"""
Test script to verify profile_photo field was added to User model
"""
import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

from users.models import User
from django.db import connection

def test_profile_photo_field():
    """Test if profile_photo field is accessible"""
    try:
        # Check if the field exists in the model
        user_fields = [field.name for field in User._meta.fields]
        print(f"User model fields: {user_fields}")
        
        if 'profile_photo' in user_fields:
            print("✅ profile_photo field exists in User model")
        else:
            print("❌ profile_photo field NOT found in User model")
        
        # Check if the column exists in the database
        with connection.cursor() as cursor:
            cursor.execute("PRAGMA table_info(users_user);")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            print(f"Database columns: {column_names}")
            
            if 'profile_photo' in column_names:
                print("✅ profile_photo column exists in database")
                
                # Try to access the field on a user instance
                user = User.objects.first()
                if user:
                    try:
                        photo_value = user.profile_photo
                        print(f"✅ Successfully accessed profile_photo field: {photo_value}")
                        return True
                    except Exception as e:
                        print(f"❌ Error accessing profile_photo field: {e}")
                        return False
                else:
                    print("ℹ️ No users found in database to test field access")
                    return True
            else:
                print("❌ profile_photo column NOT found in database")
                return False
                
    except Exception as e:
        print(f"❌ Error during test: {e}")
        return False

if __name__ == "__main__":
    print("Testing profile_photo field implementation...")
    success = test_profile_photo_field()
    if success:
        print("\n🎉 Profile photo field is working correctly!")
    else:
        print("\n💥 Profile photo field has issues!")
