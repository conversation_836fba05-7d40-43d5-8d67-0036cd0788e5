/**
 * Mobile-Optimized Electricity Dashboard JavaScript
 * Enhanced mobile interactions and touch-friendly features
 */

class MobileElectricityDashboard {
    constructor() {
        this.isMobile = this.detectMobileDevice();
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.isScrolling = false;
        this.charts = {};
        this.orientation = this.getOrientation();
        this.connectionStatus = 'connected';
        this.refreshInterval = null;
        this.lastUpdateTime = null;
        
        // Mobile-specific configuration
        this.config = {
            refreshInterval: this.isMobile ? 15000 : 10000, // Longer intervals on mobile
            chartAnimationDuration: this.isMobile ? 500 : 1000,
            maxDataPoints: this.isMobile ? 50 : 100,
            touchThreshold: 10,
            swipeThreshold: 50
        };
        
        this.init();
    }

    init() {
        this.setupMobileViewport();
        this.setupTouchEvents();
        this.setupOrientationEvents();
        this.setupMobileNavigation();
        this.setupPullToRefresh();
        this.setupOfflineDetection();
        this.initializeMobileCharts();
        this.startDataUpdates();
        this.setupPWAFeatures();
        this.setupAdvancedGestures();
        this.setupPerformanceOptimization();
        this.setupNetworkMonitoring();
        
        console.log('Mobile Electricity Dashboard initialized');
    }

    // Device Detection
    detectMobileDevice() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
               window.innerWidth <= 768;
    }

    getOrientation() {
        return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    }

    // Mobile Viewport Setup
    setupMobileViewport() {
        // Prevent zoom on input focus
        const viewportMeta = document.querySelector('meta[name="viewport"]');
        if (viewportMeta && this.isMobile) {
            viewportMeta.setAttribute('content', 
                'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
            );
        }

        // Fix viewport height for mobile browsers
        this.setVHProperty();
        window.addEventListener('resize', () => this.setVHProperty());
    }

    setVHProperty() {
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    // Touch Events
    setupTouchEvents() {
        if (!this.isMobile) return;

        // Touch event listeners for charts and cards
        document.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
        document.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });
        document.addEventListener('touchend', this.handleTouchEnd.bind(this), { passive: true });

        // Prevent default touch behaviors on specific elements
        const preventElements = document.querySelectorAll('.mobile-chart-container, .mobile-card');
        preventElements.forEach(element => {
            element.addEventListener('touchstart', (e) => {
                e.stopPropagation();
            }, { passive: true });
        });
    }

    handleTouchStart(e) {
        this.touchStartY = e.touches[0].clientY;
        this.touchStartX = e.touches[0].clientX;
        this.isScrolling = false;
    }

    handleTouchMove(e) {
        if (!this.touchStartY || !this.touchStartX) return;

        const currentY = e.touches[0].clientY;
        const currentX = e.touches[0].clientX;
        const diffY = this.touchStartY - currentY;
        const diffX = this.touchStartX - currentX;

        if (Math.abs(diffY) > Math.abs(diffX)) {
            this.isScrolling = true;
        }
    }

    handleTouchEnd(e) {
        if (!this.touchStartY || !this.touchStartX) return;

        const touch = e.changedTouches[0];
        const diffY = this.touchStartY - touch.clientY;
        const diffX = this.touchStartX - touch.clientX;

        // Handle swipe gestures
        if (Math.abs(diffX) > this.config.swipeThreshold && Math.abs(diffY) < this.config.touchThreshold) {
            if (diffX > 0) {
                this.handleSwipeLeft();
            } else {
                this.handleSwipeRight();
            }
        }

        // Reset touch positions
        this.touchStartY = 0;
        this.touchStartX = 0;
        this.isScrolling = false;
    }

    handleSwipeLeft() {
        // Navigate to next section or show more details
        this.showNextSection();
    }

    handleSwipeRight() {
        // Navigate to previous section or show menu
        this.showPreviousSection();
    }

    // Orientation Events
    setupOrientationEvents() {
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 100);
        });

        window.addEventListener('resize', () => {
            const newOrientation = this.getOrientation();
            if (newOrientation !== this.orientation) {
                this.orientation = newOrientation;
                this.handleOrientationChange();
            }
        });
    }

    handleOrientationChange() {
        this.setVHProperty();
        this.resizeMobileCharts();
        this.adjustLayoutForOrientation();
        
        console.log(`Orientation changed to: ${this.orientation}`);
    }

    adjustLayoutForOrientation() {
        const grid = document.querySelector('.mobile-grid');
        if (!grid) return;

        if (this.orientation === 'landscape' && this.isMobile) {
            grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
        } else {
            grid.style.gridTemplateColumns = '1fr';
        }
    }

    // Mobile Navigation
    setupMobileNavigation() {
        this.createMobileNav();
        this.setupBottomActionBar();
    }

    createMobileNav() {
        if (!this.isMobile) return;

        const nav = document.createElement('nav');
        nav.className = 'mobile-nav';
        nav.innerHTML = `
            <a href="#" class="mobile-nav-brand">
                <i class="fas fa-bolt"></i>
                Power Monitor
            </a>
        `;

        document.body.insertBefore(nav, document.body.firstChild);
    }

    setupBottomActionBar() {
        if (!this.isMobile) return;

        const actionBar = document.createElement('div');
        actionBar.className = 'mobile-action-bar';
        actionBar.innerHTML = `
            <button class="mobile-action-btn active" data-action="dashboard">
                <i class="fas fa-tachometer-alt"></i>
                <span>Dashboard</span>
            </button>
            <button class="mobile-action-btn" data-action="charts">
                <i class="fas fa-chart-line"></i>
                <span>Charts</span>
            </button>
            <button class="mobile-action-btn" data-action="equipment">
                <i class="fas fa-cog"></i>
                <span>Equipment</span>
            </button>
            <button class="mobile-action-btn" data-action="alerts">
                <i class="fas fa-bell"></i>
                <span>Alerts</span>
            </button>
        `;

        // Add event listeners for action buttons
        actionBar.addEventListener('click', (e) => {
            const button = e.target.closest('.mobile-action-btn');
            if (button) {
                this.handleActionBarClick(button);
            }
        });

        document.body.appendChild(actionBar);
    }

    handleActionBarClick(button) {
        // Remove active class from all buttons
        document.querySelectorAll('.mobile-action-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Add active class to clicked button
        button.classList.add('active');

        // Handle action
        const action = button.dataset.action;
        this.navigateToSection(action);
    }

    navigateToSection(section) {
        const sections = {
            dashboard: this.showDashboardSection,
            charts: this.showChartsSection,
            equipment: this.showEquipmentSection,
            alerts: this.showAlertsSection
        };

        if (sections[section]) {
            sections[section].call(this);
        }
    }

    // Pull to Refresh
    setupPullToRefresh() {
        if (!this.isMobile) return;

        let startY = 0;
        let currentY = 0;
        let pullDistance = 0;
        const refreshThreshold = 60;
        let isRefreshing = false;

        const container = document.querySelector('.mobile-grid') || document.body;

        container.addEventListener('touchstart', (e) => {
            if (container.scrollTop === 0) {
                startY = e.touches[0].clientY;
            }
        }, { passive: true });

        container.addEventListener('touchmove', (e) => {
            if (container.scrollTop === 0 && !isRefreshing) {
                currentY = e.touches[0].clientY;
                pullDistance = currentY - startY;

                if (pullDistance > 0) {
                    this.showPullToRefreshIndicator(pullDistance, refreshThreshold);
                }
            }
        }, { passive: true });

        container.addEventListener('touchend', () => {
            if (pullDistance > refreshThreshold && !isRefreshing) {
                this.triggerRefresh();
            }
            this.hidePullToRefreshIndicator();
            pullDistance = 0;
        }, { passive: true });
    }

    showPullToRefreshIndicator(distance, threshold) {
        // Create or update pull-to-refresh indicator
        let indicator = document.getElementById('pull-refresh-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'pull-refresh-indicator';
            indicator.style.cssText = `
                position: fixed;
                top: -40px;
                left: 50%;
                transform: translateX(-50%);
                background: rgba(102, 126, 234, 0.9);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 20px;
                font-size: 0.8rem;
                z-index: 1003;
                transition: all 0.2s ease;
            `;
            document.body.appendChild(indicator);
        }

        const progress = Math.min(distance / threshold, 1);
        indicator.style.top = `${Math.min(distance - 40, 20)}px`;
        indicator.style.opacity = progress;
        indicator.textContent = distance > threshold ? 'Release to refresh' : 'Pull to refresh';
    }

    hidePullToRefreshIndicator() {
        const indicator = document.getElementById('pull-refresh-indicator');
        if (indicator) {
            indicator.style.top = '-40px';
            indicator.style.opacity = '0';
        }
    }

    async triggerRefresh() {
        this.showMobileAlert('Refreshing data...', 'info');
        try {
            await this.refreshAllData();
            this.showMobileAlert('Data refreshed successfully', 'success');
        } catch (error) {
            this.showMobileAlert('Failed to refresh data', 'error');
        }
    }

    // Offline Detection
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.connectionStatus = 'connected';
            this.showMobileAlert('Connection restored', 'success');
            this.resumeDataUpdates();
        });

        window.addEventListener('offline', () => {
            this.connectionStatus = 'offline';
            this.showMobileAlert('No internet connection', 'warning');
            this.pauseDataUpdates();
        });
    }

    // Mobile Charts
    initializeMobileCharts() {
        this.createMobilePowerChart();
        this.createMobilePhaseChart();
        this.createMobileEfficiencyChart();
    }

    createMobilePowerChart() {
        const container = document.getElementById('mobile-power-chart');
        if (!container) return;

        // Simplified chart for mobile
        const ctx = container.getContext('2d');
        this.charts.power = new Chart(ctx, this.getMobileChartConfig('line', {
            labels: [],
            datasets: [{
                label: 'Power (kW)',
                data: [],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        }));
    }

    createMobilePhaseChart() {
        // Create simplified 3-phase visualization
        const container = document.getElementById('mobile-phase-display');
        if (!container) return;

        this.updatePhaseDisplay({
            L1: { voltage: 230, current: 18.2 },
            L2: { voltage: 228, current: 17.8 },
            L3: { voltage: 232, current: 19.1 }
        });
    }

    updatePhaseDisplay(phaseData) {
        const container = document.getElementById('mobile-phase-display');
        if (!container) return;

        container.innerHTML = `
            <div class="mobile-phase-grid">
                <div class="mobile-phase-item">
                    <div class="mobile-phase-label">L1</div>
                    <div class="mobile-phase-value">${phaseData.L1.voltage}<span class="mobile-phase-unit">V</span></div>
                </div>
                <div class="mobile-phase-item">
                    <div class="mobile-phase-label">L2</div>
                    <div class="mobile-phase-value">${phaseData.L2.voltage}<span class="mobile-phase-unit">V</span></div>
                </div>
                <div class="mobile-phase-item">
                    <div class="mobile-phase-label">L3</div>
                    <div class="mobile-phase-value">${phaseData.L3.voltage}<span class="mobile-phase-unit">V</span></div>
                </div>
            </div>
        `;
    }

    createMobileEfficiencyChart() {
        // Create mobile-friendly efficiency display
        const container = document.getElementById('mobile-efficiency-display');
        if (!container) return;

        this.updateEfficiencyDisplay(85);
    }

    updateEfficiencyDisplay(efficiency) {
        const container = document.getElementById('mobile-efficiency-display');
        if (!container) return;

        const color = efficiency >= 90 ? '#10b981' : efficiency >= 80 ? '#f59e0b' : '#ef4444';
        
        container.innerHTML = `
            <div style="text-align: center;">
                <div style="position: relative; width: 100px; height: 100px; margin: 0 auto;">
                    <svg viewBox="0 0 100 100" style="transform: rotate(-90deg);">
                        <circle cx="50" cy="50" r="45" fill="none" stroke="rgba(148, 163, 184, 0.3)" stroke-width="6"/>
                        <circle cx="50" cy="50" r="45" fill="none" stroke="${color}" stroke-width="6" 
                                stroke-dasharray="${efficiency * 2.83} 283" stroke-linecap="round"/>
                    </svg>
                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: ${color}; font-weight: 600;">
                        ${efficiency}%
                    </div>
                </div>
                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: #94a3b8;">Efficiency</div>
            </div>
        `;
    }

    resizeMobileCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    }

    // Enhanced Chart Configurations for Mobile and Web
    getMobileChartConfig(type, data) {
        const baseConfig = {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: this.config.chartAnimationDuration
                },
                plugins: {
                    legend: {
                        display: !this.isMobile,
                        position: this.isMobile ? 'bottom' : 'top',
                        labels: {
                            font: {
                                size: this.isMobile ? 10 : 12
                            },
                            usePointStyle: true,
                            padding: this.isMobile ? 10 : 20
                        }
                    },
                    tooltip: {
                        enabled: true,
                        mode: 'index',
                        intersect: false,
                        titleFont: {
                            size: this.isMobile ? 12 : 14
                        },
                        bodyFont: {
                            size: this.isMobile ? 11 : 13
                        },
                        padding: this.isMobile ? 8 : 12
                    }
                },
                scales: {
                    x: {
                        display: true,
                        grid: {
                            display: !this.isMobile
                        },
                        ticks: {
                            font: {
                                size: this.isMobile ? 9 : 11
                            },
                            maxTicksLimit: this.isMobile ? 6 : 12
                        }
                    },
                    y: {
                        display: true,
                        grid: {
                            display: true,
                            color: 'rgba(148, 163, 184, 0.2)'
                        },
                        ticks: {
                            font: {
                                size: this.isMobile ? 9 : 11
                            },
                            maxTicksLimit: this.isMobile ? 5 : 8
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        };
        
        // Mobile-specific optimizations
        if (this.isMobile) {
            baseConfig.options.elements = {
                point: {
                    radius: 2,
                    hoverRadius: 4
                },
                line: {
                    borderWidth: 2
                }
            };
        }
        
        return baseConfig;
    }
    
    // Progressive Web App Features
    setupPWAFeatures() {
        // Service Worker Registration
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/js/electricity-sw.js')
                .then(registration => {
                    console.log('Service Worker registered:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
        
        // Install Prompt
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });
        
        // Handle app installation
        window.addEventListener('appinstalled', () => {
            console.log('PWA installed successfully');
            this.hideInstallButton();
        });
    }
    
    showInstallButton() {
        const installBtn = document.createElement('button');
        installBtn.className = 'btn btn-primary mobile-install-btn';
        installBtn.innerHTML = '<i class="fas fa-download"></i> Install App';
        installBtn.onclick = () => this.installPWA();
        
        const mobileNav = document.querySelector('.mobile-nav');
        if (mobileNav) {
            mobileNav.appendChild(installBtn);
        }
    }
    
    hideInstallButton() {
        const installBtn = document.querySelector('.mobile-install-btn');
        if (installBtn) {
            installBtn.remove();
        }
    }
    
    async installPWA() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            console.log(`User response to install prompt: ${outcome}`);
            this.deferredPrompt = null;
            this.hideInstallButton();
        }
    }
    
    // Advanced Touch Gestures
    setupAdvancedGestures() {
        let touchStartTime = 0;
        let touchStartPos = { x: 0, y: 0 };
        
        // Double tap to refresh
        document.addEventListener('touchstart', (e) => {
            const now = new Date().getTime();
            const timeDiff = now - touchStartTime;
            
            if (timeDiff < 300 && timeDiff > 0) {
                // Double tap detected
                this.refreshDashboard();
                e.preventDefault();
            }
            
            touchStartTime = now;
            touchStartPos.x = e.touches[0].clientX;
            touchStartPos.y = e.touches[0].clientY;
        });
        
        // Pinch to zoom for charts
        let initialDistance = 0;
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length === 2) {
                initialDistance = this.getDistance(e.touches[0], e.touches[1]);
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length === 2) {
                const currentDistance = this.getDistance(e.touches[0], e.touches[1]);
                const scale = currentDistance / initialDistance;
                
                if (scale > 1.1) {
                    // Zoom in
                    this.zoomChart('in');
                } else if (scale < 0.9) {
                    // Zoom out
                    this.zoomChart('out');
                }
            }
        });
    }
    
    getDistance(touch1, touch2) {
        const dx = touch1.clientX - touch2.clientX;
        const dy = touch1.clientY - touch2.clientY;
        return Math.sqrt(dx * dx + dy * dy);
    }
    
    zoomChart(direction) {
        // Implementation for chart zoom functionality
        console.log(`Zooming chart: ${direction}`);
    }
    
    // Battery and Performance Optimization
    setupPerformanceOptimization() {
        // Monitor battery level
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                this.updateRefreshRateBasedOnBattery(battery.level);
                
                battery.addEventListener('levelchange', () => {
                    this.updateRefreshRateBasedOnBattery(battery.level);
                });
            });
        }
        
        // Monitor memory usage
        if ('memory' in performance) {
            const memoryInfo = performance.memory;
            if (memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit > 0.8) {
                this.enableLowMemoryMode();
            }
        }
        
        // Reduce refresh rate when tab is not visible
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.config.refreshInterval *= 3;
            } else {
                this.config.refreshInterval = this.isMobile ? 15000 : 10000;
            }
            this.restartDataUpdates();
        });
    }
    
    updateRefreshRateBasedOnBattery(level) {
        if (level < 0.2) {
            // Low battery: reduce refresh rate
            this.config.refreshInterval = 30000;
        } else if (level < 0.5) {
            // Medium battery: normal mobile rate
            this.config.refreshInterval = 20000;
        } else {
            // Good battery: normal rate
            this.config.refreshInterval = this.isMobile ? 15000 : 10000;
        }
        this.restartDataUpdates();
    }
    
    enableLowMemoryMode() {
        console.log('Enabling low memory mode');
        
        // Reduce chart data points
        this.config.maxDataPoints = 25;
        
        // Disable animations
        this.config.chartAnimationDuration = 0;
        
        // Increase refresh interval
        this.config.refreshInterval *= 2;
        
        // Show user notification
        this.showNotification('Low memory detected. Reduced dashboard features for better performance.');
    }
    
    // Network Status Monitoring
    setupNetworkMonitoring() {
        // Monitor connection status
        window.addEventListener('online', () => {
            this.connectionStatus = 'connected';
            this.showNotification('Connection restored', 'success');
            this.startDataUpdates();
        });
        
        window.addEventListener('offline', () => {
            this.connectionStatus = 'offline';
            this.showNotification('Connection lost. Using cached data.', 'warning');
            this.stopDataUpdates();
        });
        
        // Monitor connection quality
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            connection.addEventListener('change', () => {
                this.adaptToConnectionSpeed(connection.effectiveType);
            });
            
            this.adaptToConnectionSpeed(connection.effectiveType);
        }
    }
    
    adaptToConnectionSpeed(effectiveType) {
        switch (effectiveType) {
            case 'slow-2g':
            case '2g':
                this.config.refreshInterval = 60000; // 1 minute
                this.config.maxDataPoints = 20;
                break;
            case '3g':
                this.config.refreshInterval = 30000; // 30 seconds
                this.config.maxDataPoints = 40;
                break;
            case '4g':
            default:
                this.config.refreshInterval = this.isMobile ? 15000 : 10000;
                this.config.maxDataPoints = this.isMobile ? 50 : 100;
                break;
        }
        this.restartDataUpdates();
    }
    
    // Enhanced Notification System
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.mobile-notification');
        existingNotifications.forEach(notification => notification.remove());
        
        const notification = document.createElement('div');
        notification.className = `mobile-notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)}"></i>
                <span>${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, duration);
        
        // Add animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
    }
    
    getNotificationIcon(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            error: 'times-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    // Data Management
    restartDataUpdates() {
        this.stopDataUpdates();
        this.startDataUpdates();
    }
    
    stopDataUpdates() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    refreshDashboard() {
        this.showNotification('Refreshing dashboard...', 'info', 2000);
        // Trigger data refresh
        this.updateElectricityData();
    }
    
    // Cleanup
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });

        // Remove mobile-specific elements
        const mobileElements = document.querySelectorAll('.mobile-nav, .mobile-action-bar, .mobile-alert');
        mobileElements.forEach(element => element.remove());
    }
}

// Auto-initialize for mobile devices
if (typeof module === 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        window.mobileElectricityDashboard = new MobileElectricityDashboard();
    });
}

// Export for module environments
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileElectricityDashboard;
}
