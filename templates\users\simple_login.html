<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #f97316;
            --accent-color: #fbbf24;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
            min-height: 600px;
        }
        
        .login-left {
            background: white;
            padding: 60px 40px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .login-right {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
            position: relative;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            padding: 60px 40px;
        }
        
        .login-right::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><circle fill="%23ffffff10" cx="200" cy="200" r="100"/><circle fill="%23ffffff05" cx="800" cy="300" r="150"/><circle fill="%23ffffff08" cx="400" cy="700" r="80"/></svg>');
            opacity: 0.5;
        }
        
        .login-right-content {
            position: relative;
            z-index: 2;
        }
        
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .brand-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            line-height: 1.2;
        }
        
        .brand-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .login-form-container {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .login-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
        }
        
        .login-subtitle {
            color: #6b7280;
            margin-bottom: 40px;
        }
        
        .auth-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 30px;
        }
        
        .auth-tab {
            flex: 1;
            text-align: center;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #6b7280;
        }
        
        .auth-tab.active {
            background: white;
            color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-weight: 500;
            color: var(--dark-color);
            margin-bottom: 8px;
        }
        
        .form-control {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #fafbfc;
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            background: white;
        }
        
        .btn-login {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .signup-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
        }
        
        .signup-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 600;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: none;
        }
        
        .alert-danger {
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }
        
        .features-list {
            list-style: none;
            padding: 0;
            margin-top: 30px;
        }
        
        .features-list li {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1rem;
        }
        
        .features-list i {
            margin-right: 12px;
            font-size: 1.2rem;
            opacity: 0.8;
        }
        
        @media (max-width: 768px) {
            .login-card {
                margin: 10px;
                min-height: auto;
            }
            
            .login-left, .login-right {
                padding: 40px 30px;
            }
            
            .brand-title {
                font-size: 2rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="row g-0 h-100">
                <!-- Left Side - Login Form -->
                <div class="col-lg-6">
                    <div class="login-left">
                        <div class="login-form-container">
                            <h1 class="login-title">Welcome Back!</h1>
                            <p class="login-subtitle">Sign in to your Shrimp Farm Guardian account</p>
                            
                            <!-- Auth Method Tabs -->
                            <div class="auth-tabs">
                                <button class="auth-tab active" id="emailTab">
                                    <i class="fas fa-envelope me-2"></i>Email
                                </button>
                                <button class="auth-tab" id="phoneTab">
                                    <i class="fas fa-phone me-2"></i>Phone
                                </button>
                            </div>
                            
                            <form method="post" id="loginForm">
                                {% csrf_token %}
                                
                                {% if error %}
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                                </div>
                                {% endif %}
                                
                                <!-- Email Login Form -->
                                <div id="emailForm">
                                    <div class="form-group">
                                        <label for="email" class="form-label">Email Address</label>
                                        <input type="email" 
                                               name="email" 
                                               id="email" 
                                               class="form-control" 
                                               placeholder="Enter your email address"
                                               required>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password" 
                                               name="password" 
                                               id="password" 
                                               class="form-control" 
                                               placeholder="Enter your password"
                                               required>
                                    </div>
                                </div>
                                
                                <!-- Phone Login Form (Hidden by default) -->
                                <div id="phoneForm" style="display: none;">
                                    <div class="form-group">
                                        <label for="phone" class="form-label">Phone Number</label>
                                        <input type="tel" 
                                               name="phone" 
                                               id="phone" 
                                               class="form-control" 
                                               placeholder="Enter your phone number">
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="otp" class="form-label">OTP Code</label>
                                        <input type="text" 
                                               name="otp" 
                                               id="otp" 
                                               class="form-control" 
                                               placeholder="Enter OTP code">
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn-login">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </form>
                            
                            <div class="forgot-password">
                                <a href="#" onclick="alert('Password reset feature coming soon!')">
                                    Forgot your password?
                                </a>
                            </div>
                            
                            <div class="signup-link">
                                <p>Don't have an account? <a href="{% url 'users:register' %}">Sign up now</a></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Right Side - Branding -->
                <div class="col-lg-6">
                    <div class="login-right">
                        <div class="login-right-content">
                            <div class="brand-logo">
                                <i class="fas fa-fish"></i>
                            </div>
                            <h2 class="brand-title">Shrimp Farm Guardian</h2>
                            <p class="brand-subtitle">Advanced IoT-powered aquaculture management system</p>
                            
                            <ul class="features-list">
                                <li>
                                    <i class="fas fa-chart-line"></i>
                                    Real-time monitoring & analytics
                                </li>
                                <li>
                                    <i class="fas fa-robot"></i>
                                    AI-powered disease detection
                                </li>
                                <li>
                                    <i class="fas fa-mobile-alt"></i>
                                    Mobile-first responsive design
                                </li>
                                <li>
                                    <i class="fas fa-shield-alt"></i>
                                    Enterprise-grade security
                                </li>
                                <li>
                                    <i class="fas fa-cloud"></i>
                                    Cloud-based data management
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Tab switching functionality
        document.getElementById('emailTab').addEventListener('click', function() {
            document.getElementById('emailTab').classList.add('active');
            document.getElementById('phoneTab').classList.remove('active');
            document.getElementById('emailForm').style.display = 'block';
            document.getElementById('phoneForm').style.display = 'none';
        });
        
        document.getElementById('phoneTab').addEventListener('click', function() {
            document.getElementById('phoneTab').classList.add('active');
            document.getElementById('emailTab').classList.remove('active');
            document.getElementById('phoneForm').style.display = 'block';
            document.getElementById('emailForm').style.display = 'none';
            
            // Show info message for phone login
            if (!document.getElementById('phoneInfo')) {
                const phoneInfo = document.createElement('div');
                phoneInfo.id = 'phoneInfo';
                phoneInfo.className = 'alert alert-info mt-3';
                phoneInfo.innerHTML = '<i class="fas fa-info-circle me-2"></i>Phone login feature coming soon! Please use email login.';
                document.getElementById('phoneForm').appendChild(phoneInfo);
            }
        });
        
        // Form animation on load
        window.addEventListener('load', function() {
            document.querySelector('.login-card').style.opacity = '0';
            document.querySelector('.login-card').style.transform = 'translateY(30px)';
            
            setTimeout(function() {
                document.querySelector('.login-card').style.transition = 'all 0.6s ease';
                document.querySelector('.login-card').style.opacity = '1';
                document.querySelector('.login-card').style.transform = 'translateY(0)';
            }, 100);
        });
        
        // Input focus animations
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', function() {
                if (this.value === '') {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    </script>
</body>
</html>