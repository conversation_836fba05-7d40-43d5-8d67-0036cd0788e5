"""
Global CDN and Edge Computing Configuration
Manages content delivery and edge computing for global deployment
"""

import os
import json
import boto3
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import requests
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)

@dataclass
class EdgeLocation:
    """Edge computing location configuration"""
    region: str
    city: str
    country: str
    latitude: float
    longitude: float
    endpoint_url: str
    capabilities: List[str]
    status: str = "active"
    load: float = 0.0
    latency: float = 0.0

@dataclass
class CDNConfig:
    """CDN configuration settings"""
    distribution_id: str
    domain_name: str
    origin_domain: str
    cache_behaviors: Dict[str, Any]
    edge_locations: List[str]
    ssl_certificate: str
    compression_enabled: bool = True
    logging_enabled: bool = True

class GlobalCDNManager:
    """
    Global CDN and Edge Computing Manager
    Handles content delivery optimization and edge computing deployment
    """
    
    def __init__(self):
        self.cloudfront_client = boto3.client('cloudfront') if self._aws_configured() else None
        self.edge_locations: Dict[str, EdgeLocation] = {}
        self.cdn_distributions: Dict[str, CDNConfig] = {}
        self.performance_metrics: Dict[str, Dict] = {}
        
        # Initialize edge locations
        self._setup_edge_locations()
        
        # Initialize CDN configurations
        self._setup_cdn_configurations()
    
    def _aws_configured(self) -> bool:
        """Check if AWS is configured"""
        return all([
            os.getenv('AWS_ACCESS_KEY_ID'),
            os.getenv('AWS_SECRET_ACCESS_KEY'),
            os.getenv('AWS_DEFAULT_REGION')
        ])
    
    def _setup_edge_locations(self):
        """Setup global edge computing locations"""
        edge_locations = {
            "us-east-1": EdgeLocation(
                region="us-east-1",
                city="Virginia",
                country="USA",
                latitude=38.13,
                longitude=-78.45,
                endpoint_url="https://us-east-1.shrimp-farm.edge.com",
                capabilities=["api", "ml", "storage", "analytics"]
            ),
            "us-west-2": EdgeLocation(
                region="us-west-2",
                city="Oregon",
                country="USA",
                latitude=45.87,
                longitude=-119.69,
                endpoint_url="https://us-west-2.shrimp-farm.edge.com",
                capabilities=["api", "ml", "storage"]
            ),
            "eu-west-1": EdgeLocation(
                region="eu-west-1",
                city="Ireland",
                country="Ireland",
                latitude=53.41,
                longitude=-8.24,
                endpoint_url="https://eu-west-1.shrimp-farm.edge.com",
                capabilities=["api", "storage", "analytics"]
            ),
            "ap-southeast-1": EdgeLocation(
                region="ap-southeast-1",
                city="Singapore",
                country="Singapore",
                latitude=1.37,
                longitude=103.80,
                endpoint_url="https://ap-southeast-1.shrimp-farm.edge.com",
                capabilities=["api", "ml", "storage", "iot"]
            ),
            "ap-northeast-1": EdgeLocation(
                region="ap-northeast-1",
                city="Tokyo",
                country="Japan",
                latitude=35.41,
                longitude=139.42,
                endpoint_url="https://ap-northeast-1.shrimp-farm.edge.com",
                capabilities=["api", "storage"]
            )
        }
        
        self.edge_locations.update(edge_locations)
        logger.info(f"Configured {len(edge_locations)} edge locations")
    
    def _setup_cdn_configurations(self):
        """Setup CDN distribution configurations"""
        cdn_configs = {
            "main-app": CDNConfig(
                distribution_id="E1234567890ABC",
                domain_name="app.shrimp-farm.com",
                origin_domain="origin.shrimp-farm.com",
                cache_behaviors={
                    "/static/*": {
                        "ttl": 86400,  # 24 hours
                        "compress": True,
                        "viewer_protocol_policy": "redirect-to-https"
                    },
                    "/api/*": {
                        "ttl": 0,  # No caching for API
                        "compress": True,
                        "viewer_protocol_policy": "https-only",
                        "allowed_methods": ["GET", "HEAD", "OPTIONS", "PUT", "POST", "PATCH", "DELETE"]
                    },
                    "/media/*": {
                        "ttl": 604800,  # 7 days
                        "compress": True,
                        "viewer_protocol_policy": "redirect-to-https"
                    }
                },
                edge_locations=["us-east-1", "us-west-2", "eu-west-1", "ap-southeast-1"],
                ssl_certificate="arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
            ),
            "mobile-api": CDNConfig(
                distribution_id="E0987654321XYZ",
                domain_name="mobile-api.shrimp-farm.com",
                origin_domain="api.shrimp-farm.com",
                cache_behaviors={
                    "/api/v1/ponds": {
                        "ttl": 300,  # 5 minutes
                        "compress": True,
                        "viewer_protocol_policy": "https-only"
                    },
                    "/api/v1/water-quality": {
                        "ttl": 60,  # 1 minute
                        "compress": True,
                        "viewer_protocol_policy": "https-only"
                    }
                },
                edge_locations=["us-east-1", "ap-southeast-1", "ap-northeast-1"],
                ssl_certificate="arn:aws:acm:us-east-1:123456789012:certificate/87654321-4321-4321-4321-210987654321"
            )
        }
        
        self.cdn_distributions.update(cdn_configs)
        logger.info(f"Configured {len(cdn_configs)} CDN distributions")
    
    def get_optimal_edge_location(self, client_lat: float, client_lon: float, 
                                 required_capabilities: List[str] = None) -> Optional[EdgeLocation]:
        """Find optimal edge location based on client location and requirements"""
        if required_capabilities is None:
            required_capabilities = ["api"]
        
        best_location = None
        min_distance = float('inf')
        
        for location in self.edge_locations.values():
            # Check if location has required capabilities
            if not all(cap in location.capabilities for cap in required_capabilities):
                continue
            
            # Check if location is active
            if location.status != "active":
                continue
            
            # Calculate distance (simplified great circle distance)
            distance = self._calculate_distance(
                client_lat, client_lon,
                location.latitude, location.longitude
            )
            
            # Factor in current load
            adjusted_distance = distance * (1 + location.load)
            
            if adjusted_distance < min_distance:
                min_distance = adjusted_distance
                best_location = location
        
        return best_location
    
    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate great circle distance between two points"""
        import math
        
        # Convert to radians
        lat1, lon1, lat2, lon2 = map(math.radians, [lat1, lon1, lat2, lon2])
        
        # Haversine formula
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = math.sin(dlat/2)**2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon/2)**2
        c = 2 * math.asin(math.sqrt(a))
        
        # Earth radius in kilometers
        r = 6371
        
        return c * r
    
    def deploy_to_edge(self, service_name: str, edge_regions: List[str], 
                      deployment_config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy service to specified edge locations"""
        deployment_results = {}
        
        for region in edge_regions:
            if region not in self.edge_locations:
                deployment_results[region] = {
                    "success": False,
                    "error": f"Edge location {region} not found"
                }
                continue
            
            try:
                # Deploy to edge location
                result = self._deploy_service_to_edge(
                    service_name, 
                    self.edge_locations[region], 
                    deployment_config
                )
                deployment_results[region] = result
                
            except Exception as e:
                deployment_results[region] = {
                    "success": False,
                    "error": str(e)
                }
        
        return deployment_results
    
    def _deploy_service_to_edge(self, service_name: str, edge_location: EdgeLocation, 
                               config: Dict[str, Any]) -> Dict[str, Any]:
        """Deploy service to specific edge location"""
        try:
            # Generate edge deployment configuration
            edge_config = {
                "service_name": service_name,
                "region": edge_location.region,
                "endpoint": edge_location.endpoint_url,
                "capabilities": edge_location.capabilities,
                "config": config,
                "deployment_time": datetime.now().isoformat()
            }
            
            # In production, this would deploy to actual edge infrastructure
            # For now, we'll simulate the deployment
            logger.info(f"Deploying {service_name} to edge location {edge_location.region}")
            
            return {
                "success": True,
                "deployment_id": f"{service_name}-{edge_location.region}-{int(datetime.now().timestamp())}",
                "endpoint": edge_location.endpoint_url,
                "region": edge_location.region
            }
            
        except Exception as e:
            logger.error(f"Edge deployment failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def create_cdn_distribution(self, config: CDNConfig) -> Dict[str, Any]:
        """Create CloudFront distribution"""
        if not self.cloudfront_client:
            return {
                "success": False,
                "error": "AWS CloudFront not configured"
            }
        
        try:
            # Build CloudFront distribution configuration
            distribution_config = {
                'CallerReference': f"{config.domain_name}-{int(datetime.now().timestamp())}",
                'Comment': f'CDN for {config.domain_name}',
                'DefaultRootObject': 'index.html',
                'Origins': {
                    'Quantity': 1,
                    'Items': [
                        {
                            'Id': 'origin1',
                            'DomainName': config.origin_domain,
                            'CustomOriginConfig': {
                                'HTTPPort': 80,
                                'HTTPSPort': 443,
                                'OriginProtocolPolicy': 'https-only'
                            }
                        }
                    ]
                },
                'DefaultCacheBehavior': {
                    'TargetOriginId': 'origin1',
                    'ViewerProtocolPolicy': 'redirect-to-https',
                    'TrustedSigners': {
                        'Enabled': False,
                        'Quantity': 0
                    },
                    'ForwardedValues': {
                        'QueryString': True,
                        'Cookies': {'Forward': 'none'}
                    },
                    'MinTTL': 0,
                    'Compress': config.compression_enabled
                },
                'Enabled': True,
                'PriceClass': 'PriceClass_All'
            }
            
            # Add custom cache behaviors
            if config.cache_behaviors:
                cache_behaviors = []
                for path_pattern, behavior in config.cache_behaviors.items():
                    cache_behaviors.append({
                        'PathPattern': path_pattern,
                        'TargetOriginId': 'origin1',
                        'ViewerProtocolPolicy': behavior.get('viewer_protocol_policy', 'redirect-to-https'),
                        'MinTTL': behavior.get('ttl', 0),
                        'ForwardedValues': {
                            'QueryString': True,
                            'Cookies': {'Forward': 'none'}
                        },
                        'TrustedSigners': {
                            'Enabled': False,
                            'Quantity': 0
                        },
                        'Compress': behavior.get('compress', True)
                    })
                
                distribution_config['CacheBehaviors'] = {
                    'Quantity': len(cache_behaviors),
                    'Items': cache_behaviors
                }
            
            # Create distribution
            response = self.cloudfront_client.create_distribution(
                DistributionConfig=distribution_config
            )
            
            distribution_id = response['Distribution']['Id']
            domain_name = response['Distribution']['DomainName']
            
            logger.info(f"Created CloudFront distribution: {distribution_id}")
            
            return {
                "success": True,
                "distribution_id": distribution_id,
                "domain_name": domain_name,
                "status": response['Distribution']['Status']
            }
            
        except Exception as e:
            logger.error(f"Failed to create CloudFront distribution: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def invalidate_cache(self, distribution_id: str, paths: List[str]) -> Dict[str, Any]:
        """Invalidate CloudFront cache for specified paths"""
        if not self.cloudfront_client:
            return {
                "success": False,
                "error": "AWS CloudFront not configured"
            }
        
        try:
            response = self.cloudfront_client.create_invalidation(
                DistributionId=distribution_id,
                InvalidationBatch={
                    'Paths': {
                        'Quantity': len(paths),
                        'Items': paths
                    },
                    'CallerReference': f"invalidation-{int(datetime.now().timestamp())}"
                }
            )
            
            invalidation_id = response['Invalidation']['Id']
            
            logger.info(f"Created cache invalidation: {invalidation_id}")
            
            return {
                "success": True,
                "invalidation_id": invalidation_id,
                "status": response['Invalidation']['Status']
            }
            
        except Exception as e:
            logger.error(f"Cache invalidation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_edge_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for all edge locations"""
        metrics = {}
        
        for region, location in self.edge_locations.items():
            try:
                # Check edge location health and performance
                health_check = self._check_edge_health(location)
                
                metrics[region] = {
                    "status": location.status,
                    "load": location.load,
                    "latency": location.latency,
                    "health": health_check,
                    "capabilities": location.capabilities,
                    "last_updated": datetime.now().isoformat()
                }
                
            except Exception as e:
                metrics[region] = {
                    "status": "error",
                    "error": str(e),
                    "last_updated": datetime.now().isoformat()
                }
        
        return metrics
    
    def _check_edge_health(self, location: EdgeLocation) -> Dict[str, Any]:
        """Check health of edge location"""
        try:
            # Perform health check
            response = requests.get(
                f"{location.endpoint_url}/health",
                timeout=5
            )
            
            if response.status_code == 200:
                return {
                    "status": "healthy",
                    "response_time": response.elapsed.total_seconds()
                }
            else:
                return {
                    "status": "unhealthy",
                    "status_code": response.status_code
                }
                
        except Exception as e:
            return {
                "status": "unreachable",
                "error": str(e)
            }
    
    def get_cdn_analytics(self, distribution_id: str, 
                         start_date: datetime, end_date: datetime) -> Dict[str, Any]:
        """Get CDN analytics and performance data"""
        try:
            # In production, this would fetch real CloudFront analytics
            # For now, return mock data
            analytics = {
                "distribution_id": distribution_id,
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "metrics": {
                    "requests": 1250000,
                    "bytes_downloaded": 15750000000,  # ~15GB
                    "cache_hit_rate": 87.5,
                    "origin_requests": 156250,
                    "4xx_errors": 1250,
                    "5xx_errors": 125
                },
                "top_countries": [
                    {"country": "United States", "requests": 500000},
                    {"country": "Singapore", "requests": 300000},
                    {"country": "Japan", "requests": 200000},
                    {"country": "Ireland", "requests": 150000},
                    {"country": "Others", "requests": 100000}
                ],
                "performance": {
                    "avg_response_time": 45.2,  # ms
                    "p95_response_time": 120.5,
                    "p99_response_time": 250.8
                }
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to get CDN analytics: {e}")
            return {
                "error": str(e)
            }

# Global CDN manager instance
cdn_manager = GlobalCDNManager()
