{% extends 'base.html' %}
{% load static %}

{% block title %}Enhanced Map Debug - {{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    #map {
        width: 100%;
        height: 500px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background: #f0f0f0;
    }
    
    .debug-panel {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        font-family: 'Courier New', monospace;
        font-size: 12px;
    }
    
    .debug-section {
        margin: 10px 0;
        padding: 10px;
        background: white;
        border-radius: 4px;
        border-left: 4px solid #007bff;
    }
    
    .status-log {
        height: 200px;
        overflow-y: auto;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        padding: 10px;
        margin: 10px 0;
        font-family: monospace;
        font-size: 11px;
    }
    
    .error { color: #dc3545; }
    .success { color: #28a745; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
    
    button {
        margin: 5px;
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        background: #007bff;
        color: white;
        cursor: pointer;
    }
    
    button:hover {
        background: #0056b3;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>Enhanced Map Debug Dashboard</h1>
    
    <div class="debug-panel">
        <h3>Debug Information</h3>
        
        <div class="debug-section">
            <strong>Data Summary:</strong><br>
            • Farms: {{ total_farms }}<br>
            • Ponds: {{ total_ponds }}<br>
            • Workers: {{ total_workers }}<br>
            • Weather Stations: {{ weather_stations }}<br>
            • Center: {{ center_lat|floatformat:4 }}, {{ center_lng|floatformat:4 }}<br>
            • API Key: {{ google_maps_api_key|slice:":15" }}...<br>
        </div>
        
        <div class="debug-section">
            <strong>Controls:</strong><br>
            <button onclick="testMapCreation()">Test Map Creation</button>
            <button onclick="logDataStructure()">Log Data Structure</button>
            <button onclick="testAPIConnection()">Test API Connection</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="status-log" id="statusLog">
            Waiting for debug commands...<br>
        </div>
    </div>
    
    <div id="map"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Debug utilities
    function log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const logDiv = document.getElementById('statusLog');
        const className = type;
        logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span><br>`;
        logDiv.scrollTop = logDiv.scrollHeight;
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    function clearLog() {
        document.getElementById('statusLog').innerHTML = 'Log cleared...<br>';
    }
    
    // Global variables
    let map;
    let markers = [];
    
    // Data from Django
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    const workersData = {{ workers_data|safe }};
    const weatherData = {{ weather_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    const apiKey = '{{ google_maps_api_key }}';
    
    log('Debug dashboard loaded', 'success');
    log(`Data loaded - Farms: ${farmsData.length}, Ponds: ${pondsData.length}, Workers: ${workersData.length}, Weather: ${weatherData.length}`, 'info');
    
    function testMapCreation() {
        log('Testing map creation...', 'info');
        
        try {
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                throw new Error('Map container not found');
            }
            log('Map container found', 'success');
            
            if (typeof google === 'undefined') {
                throw new Error('Google Maps API not loaded');
            }
            log('Google Maps API is available', 'success');
            
            if (!google.maps) {
                throw new Error('Google Maps object not available');
            }
            log('Google Maps object is available', 'success');
            
            // Test coordinates
            if (isNaN(centerLat) || isNaN(centerLng)) {
                throw new Error(`Invalid coordinates: lat=${centerLat}, lng=${centerLng}`);
            }
            log(`Coordinates valid: ${centerLat}, ${centerLng}`, 'success');
            
            // Create map
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: 'roadmap'
            });
            
            log('Map created successfully!', 'success');
            
            // Add test marker
            const marker = new google.maps.Marker({
                position: { lat: centerLat, lng: centerLng },
                map: map,
                title: 'Center Point'
            });
            
            log('Test marker added', 'success');
            
        } catch (error) {
            log(`Error: ${error.message}`, 'error');
        }
    }
    
    function logDataStructure() {
        log('Logging data structure...', 'info');
        log(`Farms data: ${JSON.stringify(farmsData.slice(0, 2))}`, 'info');
        log(`Ponds data: ${JSON.stringify(pondsData.slice(0, 2))}`, 'info');
        log(`Workers data: ${JSON.stringify(workersData.slice(0, 2))}`, 'info');
        log(`Weather data: ${JSON.stringify(weatherData.slice(0, 2))}`, 'info');
    }
    
    function testAPIConnection() {
        log('Testing Google Maps API connection...', 'info');
        
        if (typeof google !== 'undefined') {
            log('Google object exists', 'success');
            if (google.maps) {
                log('Google Maps API loaded', 'success');
                log(`API version: ${google.maps.version}`, 'info');
            } else {
                log('Google Maps API not loaded', 'error');
            }
        } else {
            log('Google object not found', 'error');
        }
    }
    
    // Automatic initialization
    function initMap() {
        log('initMap() called by Google Maps API', 'success');
        testMapCreation();
    }
    
    function handleMapError() {
        log('Google Maps API failed to load', 'error');
    }
    
    // Check API loading status
    setTimeout(() => {
        if (typeof google === 'undefined') {
            log('Google Maps API not loaded after 10 seconds', 'error');
        }
    }, 10000);
    
    log('Waiting for Google Maps API...', 'info');
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
