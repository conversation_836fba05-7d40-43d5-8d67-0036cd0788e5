"""
Advanced Business Intelligence and Analytics Engine
Provides comprehensive analytics, reporting, and business insights
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
from django.db import connection
from django.utils import timezone
from django.core.cache import cache
import json
from dataclasses import dataclass, asdict
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
import plotly.graph_objects as go
import plotly.express as px
from plotly.utils import PlotlyJSONEncoder

logger = logging.getLogger(__name__)

@dataclass
class AnalyticsQuery:
    """Analytics query configuration"""
    name: str
    description: str
    sql_query: str
    parameters: Dict[str, Any]
    cache_ttl: int = 3600  # 1 hour default
    visualization_type: str = "table"
    
@dataclass
class BusinessMetric:
    """Business metric definition"""
    name: str
    value: float
    unit: str
    trend: str  # "up", "down", "stable"
    change_percent: float
    target: Optional[float] = None
    status: str = "normal"  # "normal", "warning", "critical"

class BusinessIntelligenceEngine:
    """
    Advanced Business Intelligence and Analytics Engine
    """
    
    def __init__(self):
        self.queries: Dict[str, AnalyticsQuery] = {}
        self.cached_results: Dict[str, Any] = {}
        self.kpi_definitions: Dict[str, Dict] = {}
        
        # Initialize predefined queries
        self._setup_analytics_queries()
        
        # Initialize KPI definitions
        self._setup_kpi_definitions()
    
    def _setup_analytics_queries(self):
        """Setup predefined analytics queries"""
        queries = {
            "pond_performance": AnalyticsQuery(
                name="Pond Performance Analysis",
                description="Comprehensive pond performance metrics",
                sql_query="""
                    SELECT 
                        p.id,
                        p.name,
                        p.size,
                        p.depth,
                        COUNT(wq.id) as water_quality_readings,
                        AVG(wq.ph) as avg_ph,
                        AVG(wq.temperature) as avg_temperature,
                        AVG(wq.dissolved_oxygen) as avg_dissolved_oxygen,
                        AVG(wq.salinity) as avg_salinity,
                        STDDEV(wq.ph) as ph_stability,
                        COUNT(CASE WHEN wq.ph < 7.0 OR wq.ph > 8.5 THEN 1 END) as ph_violations
                    FROM ponds_pond p
                    LEFT JOIN water_quality_waterqualityreading wq ON p.id = wq.pond_id
                    WHERE wq.timestamp >= %s AND wq.timestamp <= %s
                    GROUP BY p.id, p.name, p.size, p.depth
                    ORDER BY p.name
                """,
                parameters={"start_date": None, "end_date": None},
                visualization_type="table"
            ),
            
            "production_trends": AnalyticsQuery(
                name="Production Trends",
                description="Production and harvest trends over time",
                sql_query="""
                    SELECT 
                        DATE_TRUNC('month', h.harvest_date) as month,
                        COUNT(*) as harvest_count,
                        SUM(h.total_weight) as total_production,
                        AVG(h.total_weight) as avg_harvest_weight,
                        AVG(h.survival_rate) as avg_survival_rate,
                        SUM(h.total_weight * h.price_per_kg) as revenue
                    FROM harvest_harvestrecord h
                    WHERE h.harvest_date >= %s AND h.harvest_date <= %s
                    GROUP BY DATE_TRUNC('month', h.harvest_date)
                    ORDER BY month
                """,
                parameters={"start_date": None, "end_date": None},
                visualization_type="line_chart"
            ),
            
            "water_quality_trends": AnalyticsQuery(
                name="Water Quality Trends",
                description="Water quality parameter trends",
                sql_query="""
                    SELECT 
                        DATE_TRUNC('day', wq.timestamp) as date,
                        AVG(wq.ph) as avg_ph,
                        AVG(wq.temperature) as avg_temperature,
                        AVG(wq.dissolved_oxygen) as avg_dissolved_oxygen,
                        AVG(wq.salinity) as avg_salinity,
                        AVG(wq.turbidity) as avg_turbidity,
                        COUNT(*) as reading_count
                    FROM water_quality_waterqualityreading wq
                    WHERE wq.timestamp >= %s AND wq.timestamp <= %s
                    GROUP BY DATE_TRUNC('day', wq.timestamp)
                    ORDER BY date
                """,
                parameters={"start_date": None, "end_date": None},
                visualization_type="multi_line_chart"
            ),
            
            "farm_efficiency": AnalyticsQuery(
                name="Farm Efficiency Analysis",
                description="Farm operational efficiency metrics",
                sql_query="""
                    SELECT 
                        f.id,
                        f.name,
                        COUNT(DISTINCT p.id) as pond_count,
                        SUM(p.size) as total_pond_area,
                        COUNT(DISTINCT h.id) as harvest_count,
                        SUM(h.total_weight) as total_production,
                        SUM(h.total_weight) / SUM(p.size) as production_per_area,
                        AVG(h.survival_rate) as avg_survival_rate,
                        SUM(h.total_weight * h.price_per_kg) as total_revenue
                    FROM farms_farm f
                    LEFT JOIN ponds_pond p ON f.id = p.farm_id
                    LEFT JOIN harvest_harvestrecord h ON p.id = h.pond_id
                    WHERE h.harvest_date >= %s AND h.harvest_date <= %s
                    GROUP BY f.id, f.name
                    ORDER BY production_per_area DESC
                """,
                parameters={"start_date": None, "end_date": None},
                visualization_type="bar_chart"
            ),
            
            "alert_analysis": AnalyticsQuery(
                name="Alert Analysis",
                description="Alert frequency and resolution analysis",
                sql_query="""
                    SELECT 
                        a.alert_type,
                        a.severity,
                        COUNT(*) as alert_count,
                        AVG(EXTRACT(EPOCH FROM (a.resolved_at - a.created_at))/3600) as avg_resolution_hours,
                        COUNT(CASE WHEN a.status = 'resolved' THEN 1 END) as resolved_count,
                        COUNT(CASE WHEN a.status = 'active' THEN 1 END) as active_count
                    FROM alerts_alert a
                    WHERE a.created_at >= %s AND a.created_at <= %s
                    GROUP BY a.alert_type, a.severity
                    ORDER BY alert_count DESC
                """,
                parameters={"start_date": None, "end_date": None},
                visualization_type="heatmap"
            )
        }
        
        self.queries.update(queries)
        logger.info(f"Configured {len(queries)} analytics queries")
    
    def _setup_kpi_definitions(self):
        """Setup KPI definitions and calculations"""
        self.kpi_definitions = {
            "production_efficiency": {
                "name": "Production Efficiency",
                "description": "Production per unit area",
                "unit": "kg/m²",
                "target": 2.5,
                "calculation": "total_production / total_pond_area"
            },
            "survival_rate": {
                "name": "Average Survival Rate",
                "description": "Average shrimp survival rate",
                "unit": "%",
                "target": 85.0,
                "calculation": "AVG(survival_rate)"
            },
            "water_quality_score": {
                "name": "Water Quality Score",
                "description": "Composite water quality score",
                "unit": "score",
                "target": 90.0,
                "calculation": "custom"
            },
            "revenue_per_pond": {
                "name": "Revenue per Pond",
                "description": "Average revenue per pond",
                "unit": "USD",
                "target": 10000.0,
                "calculation": "total_revenue / pond_count"
            },
            "alert_resolution_time": {
                "name": "Alert Resolution Time",
                "description": "Average time to resolve alerts",
                "unit": "hours",
                "target": 2.0,
                "calculation": "AVG(resolution_time)"
            }
        }
    
    def execute_query(self, query_name: str, parameters: Dict[str, Any] = None) -> pd.DataFrame:
        """Execute analytics query and return results"""
        if query_name not in self.queries:
            raise ValueError(f"Query '{query_name}' not found")
        
        query = self.queries[query_name]
        
        # Use provided parameters or defaults
        if parameters:
            query_params = {**query.parameters, **parameters}
        else:
            query_params = query.parameters.copy()
        
        # Set default date range if not provided
        if "start_date" in query_params and query_params["start_date"] is None:
            query_params["start_date"] = timezone.now() - timedelta(days=30)
        if "end_date" in query_params and query_params["end_date"] is None:
            query_params["end_date"] = timezone.now()
        
        # Check cache first
        cache_key = f"analytics:{query_name}:{hash(str(query_params))}"
        cached_result = cache.get(cache_key)
        
        if cached_result:
            logger.info(f"Returning cached result for query: {query_name}")
            return pd.read_json(cached_result)
        
        try:
            # Execute query
            with connection.cursor() as cursor:
                cursor.execute(query.sql_query, list(query_params.values()))
                columns = [desc[0] for desc in cursor.description]
                data = cursor.fetchall()
            
            # Create DataFrame
            df = pd.DataFrame(data, columns=columns)
            
            # Cache result
            cache.set(cache_key, df.to_json(), query.cache_ttl)
            
            logger.info(f"Executed query '{query_name}' - {len(df)} rows returned")
            return df
            
        except Exception as e:
            logger.error(f"Query execution failed for '{query_name}': {e}")
            raise
    
    def calculate_kpis(self, date_range: Tuple[datetime, datetime] = None) -> Dict[str, BusinessMetric]:
        """Calculate key performance indicators"""
        if date_range is None:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=30)
        else:
            start_date, end_date = date_range
        
        kpis = {}
        
        try:
            # Get production data
            production_df = self.execute_query("farm_efficiency", {
                "start_date": start_date,
                "end_date": end_date
            })
            
            # Calculate production efficiency
            if not production_df.empty:
                total_production = production_df['total_production'].sum()
                total_area = production_df['total_pond_area'].sum()
                production_efficiency = total_production / total_area if total_area > 0 else 0
                
                kpis["production_efficiency"] = BusinessMetric(
                    name="Production Efficiency",
                    value=round(production_efficiency, 2),
                    unit="kg/m²",
                    trend=self._calculate_trend("production_efficiency", production_efficiency),
                    change_percent=self._calculate_change_percent("production_efficiency", production_efficiency),
                    target=2.5,
                    status=self._get_metric_status(production_efficiency, 2.5)
                )
                
                # Calculate survival rate
                avg_survival_rate = production_df['avg_survival_rate'].mean()
                kpis["survival_rate"] = BusinessMetric(
                    name="Average Survival Rate",
                    value=round(avg_survival_rate, 1),
                    unit="%",
                    trend=self._calculate_trend("survival_rate", avg_survival_rate),
                    change_percent=self._calculate_change_percent("survival_rate", avg_survival_rate),
                    target=85.0,
                    status=self._get_metric_status(avg_survival_rate, 85.0)
                )
            
            # Calculate water quality score
            water_quality_score = self._calculate_water_quality_score(start_date, end_date)
            kpis["water_quality_score"] = BusinessMetric(
                name="Water Quality Score",
                value=round(water_quality_score, 1),
                unit="score",
                trend=self._calculate_trend("water_quality_score", water_quality_score),
                change_percent=self._calculate_change_percent("water_quality_score", water_quality_score),
                target=90.0,
                status=self._get_metric_status(water_quality_score, 90.0)
            )
            
            # Calculate alert resolution time
            alert_df = self.execute_query("alert_analysis", {
                "start_date": start_date,
                "end_date": end_date
            })
            
            if not alert_df.empty:
                avg_resolution_time = alert_df['avg_resolution_hours'].mean()
                kpis["alert_resolution_time"] = BusinessMetric(
                    name="Alert Resolution Time",
                    value=round(avg_resolution_time, 1),
                    unit="hours",
                    trend=self._calculate_trend("alert_resolution_time", avg_resolution_time),
                    change_percent=self._calculate_change_percent("alert_resolution_time", avg_resolution_time),
                    target=2.0,
                    status=self._get_metric_status(avg_resolution_time, 2.0, reverse=True)
                )
            
            logger.info(f"Calculated {len(kpis)} KPIs")
            return kpis
            
        except Exception as e:
            logger.error(f"KPI calculation failed: {e}")
            return {}
    
    def _calculate_water_quality_score(self, start_date: datetime, end_date: datetime) -> float:
        """Calculate composite water quality score"""
        try:
            water_quality_df = self.execute_query("water_quality_trends", {
                "start_date": start_date,
                "end_date": end_date
            })
            
            if water_quality_df.empty:
                return 0.0
            
            # Define optimal ranges and weights
            optimal_ranges = {
                'avg_ph': (7.0, 8.5, 0.25),
                'avg_temperature': (26.0, 30.0, 0.20),
                'avg_dissolved_oxygen': (5.0, 8.0, 0.30),
                'avg_salinity': (15.0, 25.0, 0.15),
                'avg_turbidity': (0.0, 10.0, 0.10)
            }
            
            total_score = 0.0
            total_weight = 0.0
            
            for param, (min_val, max_val, weight) in optimal_ranges.items():
                if param in water_quality_df.columns:
                    values = water_quality_df[param].dropna()
                    if len(values) > 0:
                        # Calculate percentage of values in optimal range
                        in_range = ((values >= min_val) & (values <= max_val)).sum()
                        score = (in_range / len(values)) * 100
                        total_score += score * weight
                        total_weight += weight
            
            return total_score / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Water quality score calculation failed: {e}")
            return 0.0
    
    def _calculate_trend(self, metric_name: str, current_value: float) -> str:
        """Calculate trend direction for metric"""
        # Get previous period value from cache
        cache_key = f"kpi_history:{metric_name}"
        history = cache.get(cache_key, [])
        
        if len(history) < 2:
            trend = "stable"
        else:
            previous_value = history[-2]
            if current_value > previous_value * 1.05:  # 5% increase
                trend = "up"
            elif current_value < previous_value * 0.95:  # 5% decrease
                trend = "down"
            else:
                trend = "stable"
        
        # Update history
        history.append(current_value)
        if len(history) > 10:  # Keep last 10 values
            history = history[-10:]
        cache.set(cache_key, history, 86400)  # 24 hours
        
        return trend
    
    def _calculate_change_percent(self, metric_name: str, current_value: float) -> float:
        """Calculate percentage change from previous period"""
        cache_key = f"kpi_history:{metric_name}"
        history = cache.get(cache_key, [])
        
        if len(history) < 2:
            return 0.0
        
        previous_value = history[-2]
        if previous_value == 0:
            return 0.0
        
        return ((current_value - previous_value) / previous_value) * 100
    
    def _get_metric_status(self, value: float, target: float, reverse: bool = False) -> str:
        """Get metric status based on target comparison"""
        if reverse:  # Lower is better (e.g., resolution time)
            if value <= target * 0.8:
                return "normal"
            elif value <= target:
                return "warning"
            else:
                return "critical"
        else:  # Higher is better
            if value >= target * 0.9:
                return "normal"
            elif value >= target * 0.7:
                return "warning"
            else:
                return "critical"
    
    def generate_visualization(self, query_name: str, parameters: Dict[str, Any] = None) -> str:
        """Generate visualization for query results"""
        df = self.execute_query(query_name, parameters)
        query = self.queries[query_name]
        
        if df.empty:
            return json.dumps({"error": "No data available"})
        
        try:
            if query.visualization_type == "line_chart":
                fig = px.line(df, x=df.columns[0], y=df.columns[1:])
            elif query.visualization_type == "bar_chart":
                fig = px.bar(df, x=df.columns[0], y=df.columns[1])
            elif query.visualization_type == "multi_line_chart":
                fig = go.Figure()
                for col in df.columns[1:]:
                    if col != 'reading_count':  # Skip count column
                        fig.add_trace(go.Scatter(
                            x=df[df.columns[0]],
                            y=df[col],
                            mode='lines',
                            name=col.replace('avg_', '').replace('_', ' ').title()
                        ))
            elif query.visualization_type == "heatmap":
                pivot_df = df.pivot(index=df.columns[0], columns=df.columns[1], values=df.columns[2])
                fig = px.imshow(pivot_df, aspect="auto")
            else:
                # Default to table
                return df.to_json(orient='records')
            
            fig.update_layout(
                title=query.name,
                xaxis_title=df.columns[0].replace('_', ' ').title(),
                yaxis_title="Value"
            )
            
            return json.dumps(fig, cls=PlotlyJSONEncoder)
            
        except Exception as e:
            logger.error(f"Visualization generation failed: {e}")
            return json.dumps({"error": str(e)})
    
    def generate_executive_dashboard(self) -> Dict[str, Any]:
        """Generate executive dashboard with key metrics and insights"""
        try:
            # Calculate KPIs
            kpis = self.calculate_kpis()
            
            # Get recent trends
            end_date = timezone.now()
            start_date = end_date - timedelta(days=30)
            
            production_trends = self.execute_query("production_trends", {
                "start_date": start_date,
                "end_date": end_date
            })
            
            # Generate insights
            insights = self._generate_insights(kpis, production_trends)
            
            dashboard = {
                "generated_at": timezone.now().isoformat(),
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "kpis": {name: asdict(metric) for name, metric in kpis.items()},
                "insights": insights,
                "recommendations": self._generate_recommendations(kpis),
                "alerts": self._get_critical_alerts()
            }
            
            return dashboard
            
        except Exception as e:
            logger.error(f"Executive dashboard generation failed: {e}")
            return {"error": str(e)}
    
    def _generate_insights(self, kpis: Dict[str, BusinessMetric], 
                          production_df: pd.DataFrame) -> List[str]:
        """Generate business insights from data"""
        insights = []
        
        # Production insights
        if "production_efficiency" in kpis:
            efficiency = kpis["production_efficiency"]
            if efficiency.trend == "up":
                insights.append(f"Production efficiency is trending upward (+{efficiency.change_percent:.1f}%)")
            elif efficiency.trend == "down":
                insights.append(f"Production efficiency declined by {abs(efficiency.change_percent):.1f}%")
        
        # Water quality insights
        if "water_quality_score" in kpis:
            wq_score = kpis["water_quality_score"]
            if wq_score.status == "critical":
                insights.append("Water quality requires immediate attention")
            elif wq_score.status == "warning":
                insights.append("Water quality is below optimal levels")
        
        # Production trends
        if not production_df.empty and len(production_df) > 1:
            recent_production = production_df['total_production'].iloc[-1]
            previous_production = production_df['total_production'].iloc[-2]
            change = ((recent_production - previous_production) / previous_production) * 100
            
            if change > 10:
                insights.append(f"Production increased significantly by {change:.1f}% this month")
            elif change < -10:
                insights.append(f"Production decreased by {abs(change):.1f}% this month")
        
        return insights
    
    def _generate_recommendations(self, kpis: Dict[str, BusinessMetric]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        for name, metric in kpis.items():
            if metric.status == "critical":
                if name == "production_efficiency":
                    recommendations.append("Consider optimizing feeding schedules and pond stocking density")
                elif name == "water_quality_score":
                    recommendations.append("Implement immediate water quality improvement measures")
                elif name == "survival_rate":
                    recommendations.append("Review disease prevention protocols and water management")
                elif name == "alert_resolution_time":
                    recommendations.append("Improve alert response procedures and staff training")
        
        return recommendations
    
    def _get_critical_alerts(self) -> List[Dict[str, Any]]:
        """Get current critical alerts"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT alert_type, severity, message, created_at
                    FROM alerts_alert
                    WHERE status = 'active' AND severity = 'critical'
                    ORDER BY created_at DESC
                    LIMIT 5
                """)
                
                alerts = []
                for row in cursor.fetchall():
                    alerts.append({
                        "type": row[0],
                        "severity": row[1],
                        "message": row[2],
                        "created_at": row[3].isoformat()
                    })
                
                return alerts
                
        except Exception as e:
            logger.error(f"Failed to get critical alerts: {e}")
            return []

# Global BI engine instance
bi_engine = BusinessIntelligenceEngine()
