{% extends 'base.html' %}
{% load static %}

{% block title %}Computer Vision Center - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<link href="{% static 'css/unified-template.css' %}" rel="stylesheet">
<link href="{% static 'css/computer-vision.css' %}" rel="stylesheet">
<style>
    .vision-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .vision-hero::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: pulse 4s ease-in-out infinite;
    }
    
    .ai-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        margin-top: 1rem;
    }
    
    .ai-dot {
        width: 8px;
        height: 8px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse-ai 2s infinite;
    }
    
    @keyframes pulse-ai {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.3); }
    }
    
    .vision-stats {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        color: #e2e8f0;
    }
    
    .vision-stat-item {
        text-align: center;
        padding: 1rem;
    }
    
    .vision-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: #e2e8f0;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .vision-stat-label {
        font-size: 0.9rem;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        font-weight: 500;
    }
    
    .vision-stat-change {
        font-size: 0.8rem;
        font-weight: 600;
        margin-top: 0.25rem;
    }
    
    .vision-stat-change.positive {
        color: #10b981;
    }
    
    .vision-stat-change.negative {
        color: #ef4444;
    }
    
    .vision-stat-change.neutral {
        color: #94a3b8;
    }
    
    @media (max-width: 768px) {
        .vision-hero {
            padding: 2rem 1rem;
        }
        
        .vision-stats {
            padding: 1rem;
        }
        
        .vision-stat-item {
            padding: 0.75rem 0.5rem;
        }
        
        .vision-stat-value {
            font-size: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Vision Hero Section -->
    <div class="vision-hero">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-3">
                    <i class="fas fa-eye me-3"></i>
                    Computer Vision Center
                </h1>
                <p class="mb-0 fs-5">
                    AI-powered image analysis, automated monitoring, and intelligent insights
                </p>
                <div class="ai-indicator">
                    <div class="ai-dot"></div>
                    <span>AI VISION ACTIVE</span>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="vision-overview">
                    <div class="mb-2">
                        <i class="fas fa-camera me-2"></i>
                        <span id="activeCameras">5</span> Active Cameras
                    </div>
                    <div class="mb-2">
                        <i class="fas fa-brain me-2"></i>
                        <span id="aiModels">5</span> AI Models
                    </div>
                    <div>
                        <i class="fas fa-chart-line me-2"></i>
                        <span id="analysisAccuracy">94.2%</span> Accuracy
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vision Statistics -->
    <div class="vision-stats">
        <div class="row">
            <div class="col-md-3 col-6">
                <div class="vision-stat-item">
                    <span class="vision-stat-value" id="totalAnalyses">1,247</span>
                    <span class="vision-stat-label">Total Analyses</span>
                    <div class="vision-stat-change positive" id="analysesChange">+23 today</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="vision-stat-item">
                    <span class="vision-stat-value" id="healthyShrimp">94.8%</span>
                    <span class="vision-stat-label">Healthy Shrimp</span>
                    <div class="vision-stat-change positive" id="healthChange">+2.1%</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="vision-stat-item">
                    <span class="vision-stat-value" id="avgSize">14.2 cm</span>
                    <span class="vision-stat-label">Avg Size</span>
                    <div class="vision-stat-change positive" id="sizeChange">+0.3 cm</div>
                </div>
            </div>
            <div class="col-md-3 col-6">
                <div class="vision-stat-item">
                    <span class="vision-stat-value" id="alertsGenerated">3</span>
                    <span class="vision-stat-label">Active Alerts</span>
                    <div class="vision-stat-change negative" id="alertsChange">-2 alerts</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vision Dashboard Container -->
    <div id="vision-dashboard-container">
        <!-- Vision dashboard will be rendered here by JavaScript -->
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="quick-actions-container">
                <div class="vision-stats">
                    <h5 class="mb-3">
                        <i class="fas fa-bolt me-2"></i>
                        Vision Quick Actions
                    </h5>
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-primary w-100" id="healthCheckBtn">
                                <i class="fas fa-heartbeat me-2"></i>
                                Health Check All
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-success w-100" id="growthAnalysisBtn">
                                <i class="fas fa-ruler me-2"></i>
                                Growth Analysis
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-info w-100" id="behaviorAnalysisBtn">
                                <i class="fas fa-fish me-2"></i>
                                Behavior Analysis
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-warning w-100" id="waterQualityBtn">
                                <i class="fas fa-tint me-2"></i>
                                Water Quality
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-secondary w-100" id="captureAllBtn">
                                <i class="fas fa-camera me-2"></i>
                                Capture All
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-secondary w-100" id="exportResultsBtn">
                                <i class="fas fa-download me-2"></i>
                                Export Results
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-secondary w-100" id="modelStatusBtn">
                                <i class="fas fa-cogs me-2"></i>
                                Model Status
                            </button>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <button class="btn btn-outline-secondary w-100" id="visionReportBtn">
                                <i class="fas fa-chart-bar me-2"></i>
                                Vision Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Computer Vision Engine -->
<script src="{% static 'js/computer-vision-engine.js' %}"></script>
<!-- Vision Dashboard -->
<script src="{% static 'js/vision-dashboard.js' %}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize vision dashboard
    initializeVisionDashboard();
    
    // Setup quick actions
    setupVisionQuickActions();
    
    // Start live data simulation
    startVisionDataSimulation();
});

function initializeVisionDashboard() {
    // Listen for vision analysis updates
    window.addEventListener('visionAnalysisComplete', (event) => {
        updateVisionStats(event.detail);
    });
    
    // Listen for vision alerts
    window.addEventListener('visionAlert', (event) => {
        updateVisionAlerts(event.detail);
    });
    
    // Listen for vision reports
    window.addEventListener('visionReport', (event) => {
        updateVisionReports(event.detail);
    });
}

function updateVisionStats(data) {
    // Update analysis statistics
    const totalElement = document.getElementById('totalAnalyses');
    if (totalElement) {
        const current = parseInt(totalElement.textContent.replace(',', ''));
        totalElement.textContent = (current + 1).toLocaleString();
    }
    
    // Update health statistics based on analysis results
    if (data.result && data.result.summary) {
        const summary = data.result.summary;
        
        if (summary.overall_health) {
            updateHealthPercentage(summary.overall_health);
        }
        
        if (summary.average_size) {
            updateAverageSize(summary.average_size);
        }
    }
}

function updateHealthPercentage(healthStatus) {
    const healthElement = document.getElementById('healthyShrimp');
    if (healthElement) {
        let percentage = 94.8; // Default
        
        switch (healthStatus) {
            case 'healthy':
                percentage = 95 + Math.random() * 3;
                break;
            case 'stressed':
                percentage = 85 + Math.random() * 10;
                break;
            case 'diseased':
                percentage = 70 + Math.random() * 15;
                break;
            case 'molting':
                percentage = 90 + Math.random() * 5;
                break;
        }
        
        healthElement.textContent = percentage.toFixed(1) + '%';
        updateStatChange('healthChange', Math.random() * 4 - 2, '%');
    }
}

function updateAverageSize(size) {
    const sizeElement = document.getElementById('avgSize');
    if (sizeElement) {
        sizeElement.textContent = size.toFixed(1) + ' cm';
        updateStatChange('sizeChange', Math.random() * 0.6 - 0.3, ' cm');
    }
}

function updateVisionAlerts(alert) {
    const alertsElement = document.getElementById('alertsGenerated');
    if (alertsElement) {
        const currentCount = parseInt(alertsElement.textContent);
        alertsElement.textContent = currentCount + 1;
        updateStatChange('alertsChange', 1, ' alert');
    }
}

function updateVisionReports(report) {
    console.log('Vision Report received:', report);
}

function updateStatChange(elementId, change, unit) {
    const element = document.getElementById(elementId);
    if (element) {
        const changeText = change > 0 ? '+' + change.toFixed(1) : change.toFixed(1);
        element.textContent = changeText + unit;
        
        // Update color based on change
        element.className = 'vision-stat-change ' + (change > 0 ? 'positive' : change < 0 ? 'negative' : 'neutral');
    }
}

function setupVisionQuickActions() {
    // Health Check All
    document.getElementById('healthCheckBtn').addEventListener('click', function() {
        if (window.visionDashboard) {
            window.visionDashboard.runQuickAnalysis('health_check');
        } else {
            showNotification('Running health check on all cameras...', 'info');
        }
    });
    
    // Growth Analysis
    document.getElementById('growthAnalysisBtn').addEventListener('click', function() {
        if (window.visionDashboard) {
            window.visionDashboard.runQuickAnalysis('growth_measurement');
        } else {
            showNotification('Starting growth analysis...', 'info');
        }
    });
    
    // Behavior Analysis
    document.getElementById('behaviorAnalysisBtn').addEventListener('click', function() {
        if (window.visionDashboard) {
            window.visionDashboard.runQuickAnalysis('feeding_analysis');
        } else {
            showNotification('Analyzing shrimp behavior...', 'info');
        }
    });
    
    // Water Quality
    document.getElementById('waterQualityBtn').addEventListener('click', function() {
        if (window.visionDashboard) {
            window.visionDashboard.runQuickAnalysis('water_quality');
        } else {
            showNotification('Analyzing water quality visually...', 'info');
        }
    });
    
    // Capture All
    document.getElementById('captureAllBtn').addEventListener('click', function() {
        showNotification('Capturing images from all cameras...', 'info');
        setTimeout(() => {
            showNotification('All images captured successfully!', 'success');
        }, 3000);
    });
    
    // Export Results
    document.getElementById('exportResultsBtn').addEventListener('click', function() {
        showNotification('Exporting vision analysis results...', 'info');
        setTimeout(() => {
            showNotification('Results exported successfully!', 'success');
        }, 2000);
    });
    
    // Model Status
    document.getElementById('modelStatusBtn').addEventListener('click', function() {
        showNotification('Checking AI model status...', 'info');
        setTimeout(() => {
            showNotification('All AI models are running optimally!', 'success');
        }, 1500);
    });
    
    // Vision Report
    document.getElementById('visionReportBtn').addEventListener('click', function() {
        showNotification('Generating comprehensive vision report...', 'info');
        setTimeout(() => {
            showNotification('Vision report generated successfully!', 'success');
        }, 4000);
    });
}

function startVisionDataSimulation() {
    // Update live stats periodically
    setInterval(() => {
        // Simulate camera count changes
        const cameras = 5 + Math.floor(Math.random() * 2) - 1; // 4-6 cameras
        document.getElementById('activeCameras').textContent = cameras;
        
        // Simulate accuracy changes
        const accuracy = (93 + Math.random() * 3).toFixed(1); // 93-96%
        document.getElementById('analysisAccuracy').textContent = accuracy + '%';
        
        // Simulate daily analysis changes
        const dailyChange = Math.floor(Math.random() * 10) + 15; // 15-25
        document.getElementById('analysesChange').textContent = '+' + dailyChange + ' today';
    }, 10000); // Update every 10 seconds
}

function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}
</script>
{% endblock %}
