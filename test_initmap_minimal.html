<!DOCTYPE html>
<html>
<head>
    <title>Google Maps InitMap Test</title>
    <style>
        #map { height: 400px; width: 100%; border: 2px solid #ccc; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Minimal Google Maps InitMap Test</h1>
    <div id="status" class="status">Testing initMap function...</div>
    <div id="map"></div>

    <script>
        console.log('🧪 Testing initMap function definition...');
        
        // Test 1: Define initMap function exactly as in the template
        window.initMap = function() {
            console.log('✅ initMap function called successfully!');
            document.getElementById('status').innerHTML = '✅ initMap function working!';
            document.getElementById('status').className = 'status success';
            
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                console.error('❌ Map container not found');
                return;
            }
            
            // Set container styles
            mapElement.style.height = '400px';
            mapElement.style.width = '100%';
            
            try {
                // Create simple map
                const map = new google.maps.Map(mapElement, {
                    zoom: 12,
                    center: { lat: 13.0827, lng: 80.2707 }
                });
                
                console.log('✅ Map created successfully!');
                
                // Add test marker
                new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location'
                });
                
            } catch (error) {
                console.error('❌ Error creating map:', error);
                mapElement.innerHTML = '<div style="padding: 20px; text-align: center; color: red;">Error: ' + error.message + '</div>';
            }
        };
        
        // Test 2: Error handler
        window.handleMapError = function() {
            console.error('❌ Google Maps API failed to load');
            document.getElementById('status').innerHTML = '❌ Google Maps API failed to load';
            document.getElementById('status').className = 'status error';
        };
        
        // Test 3: Check if functions are properly defined
        console.log('🔍 Checking function definitions...');
        console.log('initMap type:', typeof window.initMap);
        console.log('handleMapError type:', typeof window.handleMapError);
        
        if (typeof window.initMap === 'function') {
            console.log('✅ initMap is properly defined as a function');
        } else {
            console.error('❌ initMap is NOT a function');
        }
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
