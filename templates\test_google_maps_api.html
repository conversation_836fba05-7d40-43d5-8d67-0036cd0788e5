<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test - Shrimp Farm Guardian</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        #map {
            height: 400px;
            width: 100%;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid rgba(255,255,255,0.3);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: rgba(40, 167, 69, 0.8); }
        .error { background: rgba(220, 53, 69, 0.8); }
        .info { background: rgba(23, 162, 184, 0.8); }
        .test-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps API Test</h1>
        <div class="test-info">
            <strong>API Key:</strong> {{ google_maps_api_key|slice:":25" }}...<br>
            <strong>Test Date:</strong> <span id="test-date"></span><br>
            <strong>Test Purpose:</strong> Verify Google Maps functionality after API key updates
        </div>
        
        <div id="status" class="status info">⏳ Initializing Google Maps test...</div>
        
        <div id="map"></div>
        
        <div class="test-info">
            <h3>🔧 Test Results:</h3>
            <div id="test-results">
                <p>• Checking Google Maps API availability...</p>
            </div>
        </div>

        <div class="test-info">
            <h3>📍 Test Locations:</h3>
            <ul>
                <li>Main Farm: 16.422491, 81.105183</li>
                <li>Coastal Farm: 10.8211, 106.6267</li>
                <li>Delta Farm: 10.043199999999999, 105.7439</li>
            </ul>
        </div>
    </div>

    <script>
        document.getElementById('test-date').textContent = new Date().toLocaleString();
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function addTestResult(message, success = true) {
            const resultsEl = document.getElementById('test-results');
            const resultItem = document.createElement('p');
            resultItem.innerHTML = `• ${success ? '✅' : '❌'} ${message}`;
            resultsEl.appendChild(resultItem);
        }

        // Test pond locations from the database
        const testLocations = [
            { name: "18acers", lat: 16.422491, lng: 81.105183 },
            { name: "Coastal Shrimp Farm - Pond 1", lat: 10.8211, lng: 106.6267 },
            { name: "Coastal Shrimp Farm - Pond 2", lat: 10.8231, lng: 106.6297 },
            { name: "Delta Shrimp Farm - Pond 1", lat: 10.043199999999999, lng: 105.7439 }
        ];

        function initMap() {
            try {
                updateStatus('🗺️ Google Maps API loaded successfully!', 'success');
                addTestResult('Google Maps API loaded successfully');
                
                // Test Google Maps version
                if (google.maps.version) {
                    addTestResult(`Google Maps version: ${google.maps.version}`);
                }
                
                // Create map centered on first pond
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: testLocations[0].lat, lng: testLocations[0].lng },
                    zoom: 8,
                    mapTypeId: google.maps.MapTypeId.SATELLITE
                });
                
                addTestResult('Map created successfully');
                
                // Add markers for all test locations
                testLocations.forEach((location, index) => {
                    const marker = new google.maps.Marker({
                        position: { lat: location.lat, lng: location.lng },
                        map: map,
                        title: location.name,
                        animation: google.maps.Animation.DROP,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            scale: 8,
                            fillColor: index === 0 ? '#ff6b6b' : '#4ecdc4',
                            fillOpacity: 0.8,
                            strokeColor: '#ffffff',
                            strokeWeight: 2
                        }
                    });
                    
                    const infoWindow = new google.maps.InfoWindow({
                        content: `
                            <div style="padding: 10px; color: #333;">
                                <h3>🦐 ${location.name}</h3>
                                <p><strong>Coordinates:</strong> ${location.lat}, ${location.lng}</p>
                                <p><strong>Status:</strong> ✅ Google Maps Working!</p>
                            </div>
                        `
                    });
                    
                    marker.addListener('click', () => {
                        infoWindow.open(map, marker);
                    });
                });
                
                addTestResult(`Added ${testLocations.length} pond markers`);
                
                // Fit map to show all markers
                const bounds = new google.maps.LatLngBounds();
                testLocations.forEach(location => {
                    bounds.extend(new google.maps.LatLng(location.lat, location.lng));
                });
                map.fitBounds(bounds);
                
                addTestResult('Map bounds adjusted to show all ponds');
                updateStatus('🎉 All Google Maps tests passed successfully!', 'success');
                
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                addTestResult(`Error: ${error.message}`, false);
            }
        }

        function handleMapError() {
            updateStatus('❌ Google Maps API failed to load', 'error');
            addTestResult('Google Maps API failed to load', false);
            addTestResult('Check API key validity and quotas', false);
        }

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            initMap();
        } else {
            updateStatus('⏳ Waiting for Google Maps API...', 'info');
        }
    </script>
    
    <script async defer 
            src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
            onerror="handleMapError()">
    </script>
</body>
</html>
