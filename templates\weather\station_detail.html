{% extends 'layouts/detail_base.html' %}
{% load static %}

{% block detail_title %}{{ station.name }} - Weather Station{% endblock %}

{% block extra_css %}
<style>
    #station-map {
        width: 100%;
        height: 300px;
        border-radius: 0.5rem;
    }

    .farm-boundary {
        fill: rgba(13, 110, 253, 0.2);
        stroke: #0d6efd;
        stroke-width: 2;
    }

    /* Fix control positioning */
    .mapboxgl-ctrl-top-left {
        top: 10px;
        left: 10px;
    }

    .mapboxgl-ctrl-top-right {
        top: 10px;
        right: 10px;
    }

    /* Make controls more visible */
    .mapboxgl-ctrl-group {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }
</style>
<link href="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css" rel="stylesheet">
{% endblock %}

{% block detail_header %}{{ station.name }}{% endblock %}
{% block detail_header_icon %}<i class="fas fa-broadcast-tower text-primary me-2"></i>{% endblock %}

{% block detail_actions %}
<a href="{% url 'weather:data_create' %}?station_id={{ station.id }}" class="btn btn-primary me-2">
    <i class="fas fa-plus me-1"></i> Add Reading
</a>
<a href="{% url 'weather:station_update' station.id %}" class="btn btn-outline-primary me-2">
    <i class="fas fa-edit me-1"></i> Edit Station
</a>
<a href="{% url 'weather:station_list' %}" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i> Back to Stations
</a>
{% endblock %}

{% block detail_content %}

    <div class="row">
        <!-- Left Column -->
        <div class="col-lg-4">
            <!-- Station Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">Station Details</h5>
                </div>
                <div class="card-body">
                    <div id="station-map" class="mb-3"></div>

                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Location</p>
                        <p class="mb-0">{{ station.location }}</p>
                    </div>

                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Coordinates</p>
                        <p class="mb-0">{{ station.latitude|floatformat:6 }}, {{ station.longitude|floatformat:6 }}</p>
                    </div>

                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Elevation</p>
                        <p class="mb-0">{{ station.elevation|floatformat:0 }} m</p>
                    </div>

                    <div class="mb-3">
                        <p class="text-muted mb-1 small">Status</p>
                        <span class="badge {% if station.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                            {{ station.is_active|yesno:"Active,Inactive" }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Active Alerts -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Active Alerts</h5>
                    <a href="{% url 'weather:alert_create' %}?station_id={{ station.id }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i> Add Alert
                    </a>
                </div>
                <div class="card-body">
                    {% if active_alerts %}
                    {% for alert in active_alerts %}
                    <div class="alert {% if alert.severity == 'High' or alert.severity == 'Extreme' %}alert-danger{% elif alert.severity == 'Medium' %}alert-warning{% else %}alert-info{% endif %}">
                        <div class="d-flex">
                            <div class="me-3">
                                {% if alert.severity == 'High' or alert.severity == 'Extreme' %}
                                <i class="fas fa-exclamation-circle text-danger"></i>
                                {% elif alert.severity == 'Medium' %}
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                {% else %}
                                <i class="fas fa-info-circle text-info"></i>
                                {% endif %}
                            </div>
                            <div>
                                <h6 class="alert-heading">{{ alert.title }}</h6>
                                <p class="mb-1">{{ alert.description }}</p>
                                <p class="small mb-0">
                                    <span class="badge {% if alert.severity == 'High' or alert.severity == 'Extreme' %}bg-danger{% elif alert.severity == 'Medium' %}bg-warning text-dark{% else %}bg-info{% endif %}">
                                        {{ alert.severity }} - {{ alert.alert_type }}
                                    </span>
                                    <span class="ms-2">Until {{ alert.end_time|date:"M d, H:i" }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle text-success mb-3" style="font-size: 3rem;"></i>
                        <h5>No Active Alerts</h5>
                        <p class="text-muted">There are no active weather alerts for this station.</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:alert_list' %}" class="btn btn-sm btn-outline-primary w-100">
                        View All Alerts
                    </a>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="col-lg-8">
            <!-- Current Weather -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Current Weather</h5>
                    <a href="{% url 'weather:data_create' %}?station_id={{ station.id }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-plus me-1"></i> Add Reading
                    </a>
                </div>
                <div class="card-body">
                    {% if latest_weather %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex align-items-center mb-4">
                                <div class="me-3">
                                    {% if 'sunny' in latest_weather.condition|lower or 'clear' in latest_weather.condition|lower %}
                                    <i class="fas fa-sun text-warning" style="font-size: 3rem;"></i>
                                    {% elif 'cloud' in latest_weather.condition|lower %}
                                    <i class="fas fa-cloud text-secondary" style="font-size: 3rem;"></i>
                                    {% elif 'rain' in latest_weather.condition|lower or 'shower' in latest_weather.condition|lower %}
                                    <i class="fas fa-cloud-rain text-primary" style="font-size: 3rem;"></i>
                                    {% elif 'storm' in latest_weather.condition|lower or 'thunder' in latest_weather.condition|lower %}
                                    <i class="fas fa-bolt text-warning" style="font-size: 3rem;"></i>
                                    {% else %}
                                    <i class="fas fa-cloud-sun text-primary" style="font-size: 3rem;"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <h2 class="mb-0">{{ latest_weather.temperature|floatformat:1 }}°C</h2>
                                    <p class="mb-0">{{ latest_weather.condition }}</p>
                                    <p class="text-muted small mb-0">Last updated: {{ latest_weather.timestamp|date:"M d, Y H:i" }}</p>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Humidity</p>
                                        <p class="mb-0">{{ latest_weather.humidity|floatformat:0 }}%</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Pressure</p>
                                        <p class="mb-0">{{ latest_weather.pressure|floatformat:0 }} hPa</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Wind</p>
                                        <p class="mb-0">{{ latest_weather.wind_speed|floatformat:1 }} km/h {{ latest_weather.wind_direction }}</p>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <p class="text-muted small mb-1">Precipitation</p>
                                        <p class="mb-0">{{ latest_weather.precipitation|floatformat:1 }} mm</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">Forecasts</h6>
                                    {% if forecasts %}
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Time</th>
                                                    <th>Temp</th>
                                                    <th>Condition</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for forecast in forecasts|slice:":5" %}
                                                <tr>
                                                    <td>{{ forecast.forecast_date|date:"M d" }}</td>
                                                    <td>{{ forecast.forecast_time|time:"H:i" }}</td>
                                                    <td>{{ forecast.temperature_max|floatformat:0 }}°C</td>
                                                    <td>{{ forecast.condition }}</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <span>No forecasts available for this station.</span>
                                    </div>
                                    {% endif %}
                                    <a href="{% url 'weather:forecast_create' %}?station_id={{ station.id }}" class="btn btn-sm btn-outline-primary mt-2">
                                        Add Forecast
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No weather data available for this station.</span>
                    </div>
                    <div class="text-center">
                        <a href="{% url 'weather:data_create' %}?station_id={{ station.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> Add First Reading
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Recent Readings -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Recent Readings</h5>
                </div>
                <div class="card-body">
                    {% if recent_data %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Temp</th>
                                    <th>Humidity</th>
                                    <th>Wind</th>
                                    <th>Precip</th>
                                    <th>Condition</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for data in recent_data %}
                                <tr>
                                    <td>{{ data.timestamp|date:"M d, Y H:i" }}</td>
                                    <td>{{ data.temperature|floatformat:1 }}°C</td>
                                    <td>{{ data.humidity|floatformat:0 }}%</td>
                                    <td>{{ data.wind_speed|floatformat:1 }} km/h {{ data.wind_direction }}</td>
                                    <td>{{ data.precipitation|floatformat:1 }} mm</td>
                                    <td>{{ data.condition }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>No weather data available for this station.</span>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light">
                    <a href="{% url 'weather:weather_history' %}?station={{ station.id }}" class="btn btn-sm btn-outline-primary w-100">
                        View Full History
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mapbox access token from context
        mapboxgl.accessToken = '{{ mapbox_access_token }}';

        // Initialize map
        const map = new mapboxgl.Map({
            container: 'station-map',
            style: 'mapbox://styles/mapbox/satellite-streets-v12', // Satellite imagery with streets
            center: [{{ station.longitude }}, {{ station.latitude }}],
            zoom: 15,
            attributionControl: true
        });

        // Add navigation controls with specific position
        map.addControl(new mapboxgl.NavigationControl({
            showCompass: true,
            showZoom: true,
            visualizePitch: true
        }), 'top-left');

        // Add fullscreen control with specific position
        map.addControl(new mapboxgl.FullscreenControl(), 'top-right');

        // Wait for map to load
        map.on('load', function() {
            // Add station marker
            new mapboxgl.Marker({
                color: '#0d6efd'
            })
            .setLngLat([{{ station.longitude }}, {{ station.latitude }}])
            .setPopup(new mapboxgl.Popup().setHTML("<strong>{{ station.name }}</strong><br>{{ station.location }}"))
            .addTo(map);

            // Add farm boundary if available
            {% if station.farm_boundary %}
            try {
                const boundaryCoordinates = JSON.parse('{{ station.farm_boundary|escapejs }}');

                if (boundaryCoordinates && boundaryCoordinates.length > 0) {
                    // Convert coordinates to GeoJSON format
                    const polygonCoords = boundaryCoordinates.map(coord => [coord[1], coord[0]]);

                    // Add source
                    map.addSource('farm-boundary', {
                        'type': 'geojson',
                        'data': {
                            'type': 'Feature',
                            'geometry': {
                                'type': 'Polygon',
                                'coordinates': [polygonCoords]
                            },
                            'properties': {
                                'name': '{{ station.name }} Farm'
                            }
                        }
                    });

                    // Add fill layer
                    map.addLayer({
                        'id': 'farm-boundary-fill',
                        'type': 'fill',
                        'source': 'farm-boundary',
                        'layout': {},
                        'paint': {
                            'fill-color': '#0d6efd',
                            'fill-opacity': 0.2
                        }
                    });

                    // Add outline layer
                    map.addLayer({
                        'id': 'farm-boundary-outline',
                        'type': 'line',
                        'source': 'farm-boundary',
                        'layout': {},
                        'paint': {
                            'line-color': '#0d6efd',
                            'line-width': 2
                        }
                    });

                    // Fit map to boundary
                    const bounds = new mapboxgl.LngLatBounds();
                    polygonCoords.forEach(coord => bounds.extend(coord));
                    map.fitBounds(bounds, { padding: 50 });
                }
            } catch (error) {
                console.error("Error parsing farm boundary:", error);
            }
            {% endif %}
        });
    });
</script>
{% endblock %}

{% block detail_extra_js %}
<script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
{% endblock %}