{% extends "base.html" %}
{% load static %}

{% block title %}{{ farm.name }} - Farm Details - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .farm-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
    }

    .farm-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .farm-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 35px rgba(0,0,0,0.15);
    }

    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 10px;
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.9rem;
        font-weight: 500;
    }
    
    .map-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .map-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .map-header h2 {
        color: #2d3748;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .map-controls {
        display: flex;
        gap: 10px;
    }

    .map-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .map-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    #farm-map {
        width: 100%;
        height: 500px;
        border-radius: 10px;
        overflow: hidden;
    }

    .ponds-section {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .ponds-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .ponds-header h2 {
        color: #2d3748;
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .add-pond-btn {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 12px 20px;
        cursor: pointer;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .add-pond-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
        color: white;
        text-decoration: none;
    }

    .ponds-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }

    .pond-card {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: 15px;
        padding: 20px;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .pond-card:hover {
        transform: translateY(-5px);
        border-color: #10b981;
        box-shadow: 0 12px 35px rgba(16, 185, 129, 0.2);
    }

    .pond-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .pond-name {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }

    .pond-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 0.5rem;
    }
    
    .status-active {
        background: #d1fae5;
        color: #065f46;
    }

    .status-preparation {
        background: #fef3c7;
        color: #92400e;
    }

    .status-maintenance {
        background: #fee2e2;
        color: #991b1b;
    }

    .pond-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .pond-detail {
        text-align: center;
    }

    .pond-detail-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 2px;
    }

    .pond-detail-label {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
    }

    .pond-actions {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .pond-action-btn {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 8px;
        padding: 6px 12px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .pond-action-btn:hover {
        background: #667eea;
        color: white;
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        margin-bottom: 10px;
        color: #2d3748;
    }

    .empty-state p {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .farm-header {
            padding: 20px 0;
        }

        .farm-header h1 {
            font-size: 2rem;
        }

        .farm-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .stat-card {
            padding: 15px;
        }

        .stat-icon {
            font-size: 2rem;
        }

        .stat-value {
            font-size: 1.5rem;
        }

        #farm-map {
            height: 400px;
        }

        .ponds-grid {
            grid-template-columns: 1fr;
            gap: 15px;
        }

        .pond-details {
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .pond-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="farm-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1>🏢 {{ farm.name }}</h1>
                <p style="font-size: 1.1rem; opacity: 0.9; margin: 0;">
                    {% if farm.location %}{{ farm.location }}{% endif %}
                    {% if farm.contact_person %} • Managed by {{ farm.contact_person }}{% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Farm Statistics -->
    <div class="farm-stats">
        <div class="stat-card">
            <div class="stat-icon" style="color: #10b981;">🦐</div>
            <div class="stat-value" style="color: #10b981;">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="color: #3b82f6;">✅</div>
            <div class="stat-value" style="color: #3b82f6;">{{ active_ponds }}</div>
            <div class="stat-label">Active Ponds</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="color: #8b5cf6;">📏</div>
            <div class="stat-value" style="color: #8b5cf6;">{{ total_pond_area|floatformat:1 }}</div>
            <div class="stat-label">Total Area (m²)</div>
        </div>
        <div class="stat-card">
            <div class="stat-icon" style="color: #f59e0b;">⚡</div>
            <div class="stat-value" style="color: #f59e0b;">{{ total_aerators }}</div>
            <div class="stat-label">Total Aerators</div>
        </div>
    </div>

    <!-- Farm Map -->
    <div class="map-container">
        <div class="map-header">
            <h2>🗺️ Farm Map</h2>
            <div class="map-controls">
                <button class="map-btn" onclick="centerMap()">
                    <i class="fas fa-crosshairs"></i> Center
                </button>
                <button class="map-btn" onclick="toggleMapType()">
                    <i class="fas fa-layer-group"></i> Satellite
                </button>
            </div>
        </div>
        <div id="farm-map"></div>
    </div>

    <!-- Ponds Section -->
    <div class="ponds-section">
        <div class="ponds-header">
            <h2>🦐 Ponds in Farm</h2>
            <a href="{% url 'ponds:create_pond_wizard' %}?farm={{ farm.id }}" class="add-pond-btn">
                <i class="fas fa-plus"></i> Add New Pond
            </a>
        </div>

        {% if ponds %}
            <div class="ponds-grid">
                {% for pond in ponds %}
                <div class="pond-card" onclick="window.location.href='{% url 'ponds:pond_detail' pond.id %}'">
                    <div class="pond-card-header">
                        <h3 class="pond-name">🦐 {{ pond.name }}</h3>
                        <span class="pond-status status-{{ pond.status }}">{{ pond.get_status_display }}</span>
                    </div>

                    <div class="pond-details">
                        <div class="pond-detail">
                            <div class="pond-detail-value">{{ pond.size|default:"N/A" }}</div>
                            <div class="pond-detail-label">Area (m²)</div>
                        </div>
                        <div class="pond-detail">
                            <div class="pond-detail-value">{{ pond.species|default:"Not Set" }}</div>
                            <div class="pond-detail-label">Species</div>
                        </div>
                        <div class="pond-detail">
                            <div class="pond-detail-value">{{ pond.aerators.count }}</div>
                            <div class="pond-detail-label">Aerators</div>
                        </div>
                        <div class="pond-detail">
                            <div class="pond-detail-value">{{ pond.water_quality|default:"Unknown" }}</div>
                            <div class="pond-detail-label">Water Quality</div>
                        </div>
                    </div>

                    <div class="pond-actions">
                        <a href="{% url 'ponds:pond_detail' pond.id %}" class="pond-action-btn">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <a href="{% url 'ponds:aerator_management' pond.id %}" class="pond-action-btn">
                            <i class="fas fa-cog"></i> Aerators
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-water"></i>
                <h3>No Ponds Yet</h3>
                <p>This farm doesn't have any ponds yet. Start by creating your first pond.</p>
                <a href="{% url 'ponds:create_pond_wizard' %}?farm={{ farm.id }}" class="add-pond-btn">
                    <i class="fas fa-plus"></i> Create First Pond
                </a>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    let farmMap;
    let mapType = 'satellite';

    // Initialize Google Maps
    function initMap() {
        console.log('🗺️ Initializing farm detail map...');

        const mapElement = document.getElementById('farm-map');
        if (!mapElement) {
            console.error('❌ Farm map element not found');
            return;
        }

        // Map center
        const center = { lat: {{ center_lat }}, lng: {{ center_lng }} };

        // Create map
        farmMap = new google.maps.Map(mapElement, {
            zoom: 15,
            center: center,
            mapTypeId: 'satellite',
            mapTypeControl: true,
            streetViewControl: false,
            fullscreenControl: true,
            zoomControl: true
        });

        console.log('✅ Farm map created successfully');

        // Add farm boundary if available
        {% if farm_boundary %}
        const farmBoundary = {{ farm_boundary|safe }};
        if (farmBoundary && farmBoundary.length > 0) {
            const farmPolygon = new google.maps.Polygon({
                paths: farmBoundary,
                strokeColor: '#667eea',
                strokeOpacity: 0.8,
                strokeWeight: 3,
                fillColor: '#667eea',
                fillOpacity: 0.2,
                map: farmMap
            });

            // Add farm label
            const farmBounds = new google.maps.LatLngBounds();
            farmBoundary.forEach(point => farmBounds.extend(point));
            const farmCenter = farmBounds.getCenter();

            const farmLabel = new google.maps.InfoWindow({
                content: `<div style="text-align: center; padding: 8px;"><strong>🏢 {{ farm.name }}</strong></div>`,
                position: farmCenter
            });
            farmLabel.open(farmMap);

            console.log('✅ Farm boundary displayed');
        }
        {% endif %}

        // Add pond markers
        const pondsData = {{ ponds_data|safe }};
        if (pondsData && pondsData.length > 0) {
            pondsData.forEach(pond => {
                // Create pond marker (using default marker)
                const pondMarker = new google.maps.Marker({
                    position: { lat: pond.latitude, lng: pond.longitude },
                    map: farmMap,
                    title: pond.name
                    // Using default red marker - no custom icon
                });

                // Create info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 12px; max-width: 250px;">
                            <h4 style="margin: 0 0 8px 0; color: #10b981;">🦐 ${pond.name}</h4>
                            <div style="margin-bottom: 6px;"><strong>Status:</strong> ${pond.status}</div>
                            <div style="margin-bottom: 6px;"><strong>Size:</strong> ${pond.size} m²</div>
                            <div style="margin-bottom: 6px;"><strong>Species:</strong> ${pond.species || 'Not set'}</div>
                            <div style="margin-bottom: 10px;"><strong>Aerators:</strong> ${pond.aerator_count}</div>
                            <a href="/ponds/${pond.id}/" style="
                                background: #10b981;
                                color: white;
                                padding: 6px 12px;
                                border-radius: 6px;
                                text-decoration: none;
                                font-size: 0.9rem;
                                font-weight: 600;
                            ">View Details</a>
                        </div>
                    `
                });

                // Add click listener
                pondMarker.addListener('click', () => {
                    infoWindow.open(farmMap, pondMarker);
                });
            });

            console.log(`✅ Added ${pondsData.length} pond markers`);
        }
    }

    // Map control functions
    function centerMap() {
        if (farmMap) {
            farmMap.setCenter({ lat: {{ center_lat }}, lng: {{ center_lng }} });
            farmMap.setZoom(15);
        }
    }

    function toggleMapType() {
        if (farmMap) {
            mapType = mapType === 'satellite' ? 'roadmap' : 'satellite';
            farmMap.setMapTypeId(mapType);

            // Update button text
            const btn = event.target.closest('.map-btn');
            btn.innerHTML = mapType === 'satellite' ?
                '<i class="fas fa-layer-group"></i> Satellite' :
                '<i class="fas fa-map"></i> Road';
        }
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🏢 Farm detail page initializing...');

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            initMap();
        } else {
            console.log('📍 Waiting for Google Maps API...');
            window.initMap = initMap;
        }
    });
</script>

<!-- Load Google Maps API -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=geometry&callback=initMap"
    onerror="console.error('Failed to load Google Maps API')">
</script>
{% endblock %}