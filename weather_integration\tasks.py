from celery import shared_task
from django.utils import timezone
from datetime import timed<PERSON>ta
import logging
from typing import Dict, List

from .models import WeatherStation, WeatherData, WeatherAlert, WeatherAutomationRule
from .services.weather_service import WeatherService
from .services.weather_automation_service import WeatherAutomationService
from iot_integration.models import Farm, DeviceAlert

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3)
def update_all_weather_stations(self):
    """Update weather data for all active weather stations"""
    try:
        active_stations = WeatherStation.objects.filter(is_active=True)
        weather_service = WeatherService()
        
        results = []
        
        for station in active_stations:
            try:
                result = weather_service.update_weather_station_data(station.id)
                results.append({
                    'station_id': station.id,
                    'station_name': station.name,
                    'success': result['success'],
                    'error': result.get('error', None)
                })
                
                # Process weather automation rules if update was successful
                if result['success']:
                    latest_weather = WeatherData.objects.filter(station=station).order_by('-timestamp').first()
                    if latest_weather:
                        automation_service = WeatherAutomationService()
                        automation_result = automation_service.process_weather_automation_rules(latest_weather)
                        results[-1]['automation_result'] = automation_result
                
            except Exception as e:
                logger.error(f"Error updating weather station {station.name}: {e}")
                results.append({
                    'station_id': station.id,
                    'station_name': station.name,
                    'success': False,
                    'error': str(e)
                })
        
        successful_updates = len([r for r in results if r['success']])
        
        logger.info(f"Weather stations update completed: {successful_updates}/{len(results)} successful")
        
        return {
            'success': True,
            'total_stations': len(results),
            'successful_updates': successful_updates,
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Weather stations update task error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return {
            'success': False,
            'error': str(e)
        }


@shared_task(bind=True, max_retries=3)
def update_weather_station(self, station_id: int):
    """Update weather data for a specific weather station"""
    try:
        weather_service = WeatherService()
        result = weather_service.update_weather_station_data(station_id)
        
        if result['success']:
            # Process weather automation rules
            station = WeatherStation.objects.get(id=station_id)
            latest_weather = WeatherData.objects.filter(station=station).order_by('-timestamp').first()
            
            if latest_weather:
                automation_service = WeatherAutomationService()
                automation_result = automation_service.process_weather_automation_rules(latest_weather)
                result['automation_result'] = automation_result
        
        return result
        
    except Exception as e:
        logger.error(f"Weather station update task error: {e}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def process_weather_automation_rules():
    """Process weather automation rules for all recent weather data"""
    try:
        automation_service = WeatherAutomationService()
        
        # Get recent weather data (last hour)
        recent_time = timezone.now() - timedelta(hours=1)
        recent_weather_data = WeatherData.objects.filter(
            timestamp__gte=recent_time
        ).order_by('-timestamp')
        
        results = []
        
        for weather_data in recent_weather_data:
            try:
                result = automation_service.process_weather_automation_rules(weather_data)
                results.append({
                    'station_id': weather_data.station.id,
                    'station_name': weather_data.station.name,
                    'weather_timestamp': weather_data.timestamp.isoformat(),
                    'result': result
                })
            except Exception as e:
                logger.error(f"Error processing automation rules for station {weather_data.station.name}: {e}")
                results.append({
                    'station_id': weather_data.station.id,
                    'station_name': weather_data.station.name,
                    'weather_timestamp': weather_data.timestamp.isoformat(),
                    'error': str(e)
                })
        
        logger.info(f"Weather automation rules processed for {len(results)} weather data points")
        
        return {
            'success': True,
            'processed_count': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Weather automation rules processing error: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def check_weather_alerts():
    """Check for severe weather conditions and create alerts"""
    try:
        # Get recent weather data (last 30 minutes)
        recent_time = timezone.now() - timedelta(minutes=30)
        recent_weather_data = WeatherData.objects.filter(
            timestamp__gte=recent_time
        )
        
        alerts_created = []
        
        for weather_data in recent_weather_data:
            try:
                # Check for severe weather conditions
                alerts = check_severe_weather_conditions(weather_data)
                
                for alert_data in alerts:
                    # Check if similar alert already exists
                    existing_alert = WeatherAlert.objects.filter(
                        station=weather_data.station,
                        alert_type=alert_data['alert_type'],
                        status='active',
                        end_time__gt=timezone.now()
                    ).first()
                    
                    if not existing_alert:
                        # Create new weather alert
                        alert = WeatherAlert.objects.create(
                            station=weather_data.station,
                            alert_type=alert_data['alert_type'],
                            severity=alert_data['severity'],
                            title=alert_data['title'],
                            description=alert_data['description'],
                            start_time=timezone.now(),
                            end_time=timezone.now() + timedelta(hours=alert_data.get('duration_hours', 6)),
                            threshold_values=alert_data['threshold_values'],
                            current_values=alert_data['current_values'],
                            impact_level=alert_data['severity']
                        )
                        
                        alerts_created.append({
                            'alert_id': alert.id,
                            'station_name': weather_data.station.name,
                            'alert_type': alert_data['alert_type'],
                            'severity': alert_data['severity']
                        })
                        
                        # Create device alerts for farms near this weather station
                        create_device_alerts_for_weather(alert, weather_data)
                
            except Exception as e:
                logger.error(f"Error checking weather alerts for station {weather_data.station.name}: {e}")
        
        logger.info(f"Weather alerts check completed: {len(alerts_created)} alerts created")
        
        return {
            'success': True,
            'alerts_created': len(alerts_created),
            'alerts': alerts_created
        }
        
    except Exception as e:
        logger.error(f"Weather alerts check error: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def cleanup_old_weather_data():
    """Clean up old weather data and alerts"""
    try:
        # Remove weather data older than 90 days
        old_weather_data = WeatherData.objects.filter(
            timestamp__lt=timezone.now() - timedelta(days=90)
        )
        weather_data_count = old_weather_data.count()
        old_weather_data.delete()
        
        # Remove expired weather alerts
        expired_alerts = WeatherAlert.objects.filter(
            end_time__lt=timezone.now(),
            status__in=['active', 'acknowledged']
        )
        expired_alerts.update(status='expired')
        
        # Remove old weather alerts (older than 30 days)
        old_alerts = WeatherAlert.objects.filter(
            issued_at__lt=timezone.now() - timedelta(days=30)
        )
        alerts_count = old_alerts.count()
        old_alerts.delete()
        
        # Remove old automation logs (older than 60 days)
        from .models import WeatherAutomationLog
        old_logs = WeatherAutomationLog.objects.filter(
            executed_at__lt=timezone.now() - timedelta(days=60)
        )
        logs_count = old_logs.count()
        old_logs.delete()
        
        logger.info(f"Weather data cleanup completed: {weather_data_count} weather records, {alerts_count} alerts, {logs_count} logs removed")
        
        return {
            'success': True,
            'weather_data_removed': weather_data_count,
            'alerts_removed': alerts_count,
            'logs_removed': logs_count
        }
        
    except Exception as e:
        logger.error(f"Weather data cleanup error: {e}")
        return {
            'success': False,
            'error': str(e)
        }


@shared_task
def generate_weather_reports():
    """Generate daily weather reports for farms"""
    try:
        # Get all farms with weather stations
        farms = Farm.objects.filter(
            latitude__isnull=False,
            longitude__isnull=False
        )
        
        reports_generated = []
        
        for farm in farms:
            try:
                # Find nearest weather station
                weather_station = find_nearest_weather_station(farm.latitude, farm.longitude)
                
                if weather_station:
                    # Generate daily weather report
                    report = generate_daily_weather_report(farm, weather_station)
                    
                    if report:
                        reports_generated.append({
                            'farm_id': farm.id,
                            'farm_name': farm.name,
                            'weather_station': weather_station.name,
                            'report': report
                        })
                
            except Exception as e:
                logger.error(f"Error generating weather report for farm {farm.name}: {e}")
        
        logger.info(f"Weather reports generated for {len(reports_generated)} farms")
        
        return {
            'success': True,
            'reports_generated': len(reports_generated),
            'reports': reports_generated
        }
        
    except Exception as e:
        logger.error(f"Weather reports generation error: {e}")
        return {
            'success': False,
            'error': str(e)
        }


# Helper functions
def check_severe_weather_conditions(weather_data: WeatherData) -> List[Dict]:
    """Check for severe weather conditions and return alert data"""
    alerts = []
    
    # High temperature alert
    if weather_data.temperature > 35:
        alerts.append({
            'alert_type': 'temperature_extreme',
            'severity': 'high' if weather_data.temperature > 40 else 'medium',
            'title': 'High Temperature Alert',
            'description': f'Temperature has reached {weather_data.temperature}°C, which may stress aquatic life.',
            'threshold_values': {'temperature_max': 35},
            'current_values': {'temperature': weather_data.temperature},
            'duration_hours': 6
        })
    
    # Low temperature alert
    if weather_data.temperature < 15:
        alerts.append({
            'alert_type': 'temperature_extreme',
            'severity': 'high' if weather_data.temperature < 10 else 'medium',
            'title': 'Low Temperature Alert',
            'description': f'Temperature has dropped to {weather_data.temperature}°C, which may affect aquatic life.',
            'threshold_values': {'temperature_min': 15},
            'current_values': {'temperature': weather_data.temperature},
            'duration_hours': 8
        })
    
    # High wind alert
    if weather_data.wind_speed > 15:
        alerts.append({
            'alert_type': 'high_wind',
            'severity': 'critical' if weather_data.wind_speed > 25 else 'high',
            'title': 'High Wind Alert',
            'description': f'Wind speed has reached {weather_data.wind_speed} m/s, which may affect equipment operation.',
            'threshold_values': {'wind_speed_max': 15},
            'current_values': {'wind_speed': weather_data.wind_speed},
            'duration_hours': 4
        })
    
    # Heavy rain alert
    if weather_data.rainfall > 20:
        alerts.append({
            'alert_type': 'heavy_rain',
            'severity': 'high' if weather_data.rainfall > 50 else 'medium',
            'title': 'Heavy Rain Alert',
            'description': f'Heavy rainfall detected: {weather_data.rainfall}mm. Monitor water levels and salinity.',
            'threshold_values': {'rainfall_max': 20},
            'current_values': {'rainfall': weather_data.rainfall},
            'duration_hours': 6
        })
    
    return alerts


def create_device_alerts_for_weather(weather_alert: WeatherAlert, weather_data: WeatherData):
    """Create device alerts for farms near the weather station"""
    try:
        # Find farms within 50km of the weather station
        nearby_farms = Farm.objects.filter(
            latitude__range=[weather_data.station.latitude - 0.5, weather_data.station.latitude + 0.5],
            longitude__range=[weather_data.station.longitude - 0.5, weather_data.station.longitude + 0.5]
        )
        
        for farm in nearby_farms:
            # Get devices for this farm
            devices = farm.devices.filter(status='online')
            
            for device in devices:
                DeviceAlert.objects.create(
                    device=device,
                    alert_type='weather_alert',
                    severity=weather_alert.severity,
                    message=f"Weather Alert: {weather_alert.title}",
                    data={
                        'weather_alert_id': weather_alert.id,
                        'weather_station': weather_data.station.name,
                        'weather_conditions': {
                            'temperature': weather_data.temperature,
                            'wind_speed': weather_data.wind_speed,
                            'rainfall': weather_data.rainfall,
                        }
                    }
                )
    
    except Exception as e:
        logger.error(f"Error creating device alerts for weather: {e}")


def find_nearest_weather_station(latitude: float, longitude: float) -> WeatherStation:
    """Find the nearest weather station to given coordinates"""
    try:
        # Simple distance calculation (would use proper geospatial queries in production)
        weather_stations = WeatherStation.objects.filter(is_active=True)
        
        nearest_station = None
        min_distance = float('inf')
        
        for station in weather_stations:
            distance = ((station.latitude - latitude) ** 2 + (station.longitude - longitude) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                nearest_station = station
        
        return nearest_station
        
    except Exception as e:
        logger.error(f"Error finding nearest weather station: {e}")
        return None


def generate_daily_weather_report(farm: Farm, weather_station: WeatherStation) -> Dict:
    """Generate daily weather report for a farm"""
    try:
        # Get weather data for the last 24 hours
        yesterday = timezone.now() - timedelta(days=1)
        weather_data = WeatherData.objects.filter(
            station=weather_station,
            timestamp__gte=yesterday
        ).order_by('timestamp')
        
        if not weather_data.exists():
            return None
        
        # Calculate daily statistics
        temperatures = [data.temperature for data in weather_data]
        humidities = [data.humidity for data in weather_data]
        wind_speeds = [data.wind_speed for data in weather_data]
        total_rainfall = sum(data.rainfall for data in weather_data)
        
        report = {
            'date': timezone.now().date().isoformat(),
            'farm_name': farm.name,
            'weather_station': weather_station.name,
            'temperature': {
                'min': min(temperatures),
                'max': max(temperatures),
                'avg': sum(temperatures) / len(temperatures)
            },
            'humidity': {
                'min': min(humidities),
                'max': max(humidities),
                'avg': sum(humidities) / len(humidities)
            },
            'wind_speed': {
                'max': max(wind_speeds),
                'avg': sum(wind_speeds) / len(wind_speeds)
            },
            'total_rainfall': total_rainfall,
            'data_points': len(weather_data)
        }
        
        return report
        
    except Exception as e:
        logger.error(f"Error generating daily weather report: {e}")
        return None
