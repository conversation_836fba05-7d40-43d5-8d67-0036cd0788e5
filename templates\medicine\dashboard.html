<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicine Dashboard - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    
    /* Enhanced Medicine Header */
    .medicine-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .medicine-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: medicine-sweep 4s infinite;
    }
    
    @keyframes medicine-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    .medicine-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .medicine-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .medicine-count {
        font-size: 2rem;
        font-weight: 700;
    }

    .medicine-label {
        font-size: 1rem;
        color: #6c757d;
    }

    .treatment-item {
        border-left: 4px solid transparent;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .treatment-item:hover {
        transform: translateX(5px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .treatment-item.active {
        border-left-color: #10b981;
    }

    .treatment-item.completed {
        border-left-color: #3b82f6;
    }

    .treatment-item.scheduled {
        border-left-color: #f59e0b;
    }

    .treatment-title {
        font-weight: 600;
    }

    .treatment-pond {
        font-weight: 500;
    }

    .treatment-dates {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .treatment-dosage {
        font-weight: 700;
    }

    .timeline {
        position: relative;
        max-width: 1200px;
        margin: 0 auto;
    }

    .timeline::after {
        content: '';
        position: absolute;
        width: 2px;
        background-color: #e5e7eb;
        top: 0;
        bottom: 0;
        left: 20px;
        margin-left: -1px;
    }

    .timeline-item {
        padding: 10px 40px;
        position: relative;
        background-color: inherit;
        width: 100%;
    }

    .timeline-icon {
        position: absolute;
        width: 40px;
        height: 40px;
        left: 0;
        top: 15px;
        border-radius: 50%;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .timeline-content {
        padding: 15px;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .medicine-type-card {
        border-radius: 0.5rem;
        transition: all 0.3s ease;
    }

    .medicine-type-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    .medicine-type-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .medicine-property {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
    }

    .medicine-property-label {
        color: #6c757d;
    }

    .medicine-property-value {
        font-weight: 500;
    }

    .quick-action-btn {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 0.75rem 1rem;
        margin-bottom: 0.5rem;
        border-radius: 0.375rem;
        background-color: #f8f9fa;
        color: #4b5563;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .quick-action-btn:hover {
        background-color: #3b82f6;
        color: white;
    }

    .quick-action-btn i {
        margin-right: 0.75rem;
        font-size: 1.25rem;
        width: 1.5rem;
        text-align: center;
    }

    .inventory-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e5e7eb;
    }

    .inventory-item:last-child {
        border-bottom: none;
    }

    .inventory-name {
        font-weight: 500;
    }

    .inventory-quantity {
        font-weight: 700;
    }

    .inventory-unit {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .inventory-low {
        color: #ef4444;
    }

    .inventory-warning {
        color: #f59e0b;
    }

    .inventory-good {
        color: #10b981;
    }
</style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Medicine Header -->
            <div class="medicine-header">
                <div class="position-relative">
                    <h1><i class="fas fa-pills me-3"></i>Medicine Management Dashboard</h1>
                    <p class="lead mb-0">Comprehensive medicine tracking and treatment management system</p>
                    <div class="mt-3">
                        <span class="badge bg-light text-dark me-2">💊 MEDICINE TRACKING</span>
                        <span class="badge bg-light text-dark me-2">📋 TREATMENT PLANS</span>
                        <span class="badge bg-light text-dark me-2">📊 INVENTORY MANAGEMENT</span>
                        <span class="badge bg-light text-dark me-2">📱 MOBILE READY</span>
                        <span class="badge bg-success text-white ms-3">✅ ENHANCED</span>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/medicine/" class="quick-action">
                    <i class="fas fa-list"></i>
                    Medicine List
                </a>
                <a href="/medicine/medicine/create/" class="quick-action">
                    <i class="fas fa-plus"></i>
                    Add Medicine
                </a>
                <a href="/medicine/applications/" class="quick-action">
                    <i class="fas fa-syringe"></i>
                    Applications
                </a>
                <a href="/medicine/treatment-plans/" class="quick-action">
                    <i class="fas fa-clipboard-list"></i>
                    Treatment Plans
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-pills text-primary me-2" style="font-size: 1.5rem;"></i>
            <h1 class="h3 mb-0">Medicine Dashboard</h1>
        </div>
        <div>
            <a href="{% url 'medicine:treatment_plan_create' %}" class="btn btn-primary me-2">
                <i class="fas fa-plus"></i> Start New Treatment
            </a>
            <button class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Pond Selector -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="pond" class="form-label">Select Pond</label>
                    <select id="pond" name="pond" class="form-select" onchange="this.form.submit()">
                        <option value="">All Ponds</option>
                        {% for pond in ponds %}
                            <option value="{{ pond.id }}" {% if selected_pond == pond.id %}selected{% endif %}>
                                {{ pond.name }} ({{ pond.farm.name }})
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="date_range" class="form-label">Date Range</label>
                    <select id="date_range" name="date_range" class="form-select" onchange="this.form.submit()">
                        <option value="current" {% if date_range == 'current' %}selected{% endif %}>Current Treatments</option>
                        <option value="week" {% if date_range == 'week' %}selected{% endif %}>Last 7 Days</option>
                        <option value="month" {% if date_range == 'month' %}selected{% endif %}>Last 30 Days</option>
                        <option value="all" {% if date_range == 'all' %}selected{% endif %}>All Treatments</option>
                        <option value="custom" {% if date_range == 'custom' %}selected{% endif %}>Custom Range</option>
                    </select>
                </div>
                {% if date_range == 'custom' %}
                <div class="col-md-2">
                    <label for="start_date" class="form-label">Start Date</label>
                    <input type="date" id="start_date" name="start_date" class="form-control" value="{{ start_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <label for="end_date" class="form-label">End Date</label>
                    <input type="date" id="end_date" name="end_date" class="form-control" value="{{ end_date|date:'Y-m-d' }}">
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Apply</button>
                </div>
                {% endif %}
            </form>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card medicine-card text-center">
                <div class="card-body">
                    <div class="medicine-icon text-primary">
                        <i class="fas fa-pills"></i>
                    </div>
                    <div class="medicine-count">{{ active_treatments }}</div>
                    <div class="medicine-label">Active Treatments</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card medicine-card text-center">
                <div class="card-body">
                    <div class="medicine-icon text-success">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="medicine-count">{{ completed_treatments }}</div>
                    <div class="medicine-label">Completed Treatments</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card medicine-card text-center">
                <div class="card-body">
                    <div class="medicine-icon text-warning">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="medicine-count">{{ scheduled_treatments }}</div>
                    <div class="medicine-label">Scheduled Treatments</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card medicine-card text-center">
                <div class="card-body">
                    <div class="medicine-icon text-info">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="medicine-count">{{ medicine_types }}</div>
                    <div class="medicine-label">Medicine Types</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Treatment Usage Chart -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Treatment Usage</h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" onclick="updateChartTimeframe('month')">Month</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="updateChartTimeframe('quarter')">Quarter</button>
                        <button type="button" class="btn btn-outline-secondary" onclick="updateChartTimeframe('year')">Year</button>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="treatmentChart" height="250"></canvas>
                </div>
            </div>
        </div>

        <!-- Active Treatments -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Active Treatments</h5>
                    <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if active_treatment_list %}
                        {% for treatment in active_treatment_list %}
                        <div class="treatment-item active">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="treatment-title">{{ treatment.medicine.name }}</span>
                                <span class="badge bg-success">ACTIVE</span>
                            </div>
                            <div class="treatment-pond">{{ treatment.pond.name }}</div>
                            <div class="treatment-dates">
                                {{ treatment.start_date|date:"M d, Y" }} - {{ treatment.end_date|date:"M d, Y" }}
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span class="treatment-dosage">{{ treatment.dosage }} {{ treatment.medicine.unit }}/{{ treatment.dosage_unit }}</span>
                                <a href="{% url 'medicine:treatment_plan_detail' treatment.id %}" class="btn btn-sm btn-outline-primary">View</a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-muted mb-3" style="font-size: 3rem;"></i>
                            <p class="mb-0">No active treatments at the moment.</p>
                            <a href="{% url 'medicine:treatment_plan_create' %}" class="btn btn-sm btn-outline-primary mt-3">Start New Treatment</a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Treatment Records -->
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Treatment Records</h5>
                    <a href="{% url 'medicine:treatment_plan_list' %}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_records %}
                    <div class="timeline">
                        {% for record in recent_records %}
                        <div class="timeline-item">
                            <div class="timeline-icon bg-primary">
                                <i class="fas fa-pills"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ record.treatment.medicine.name }}</h6>
                                        <p class="mb-1 small">{{ record.treatment.pond.name }} - {{ record.dosage }} {{ record.treatment.medicine.unit }}</p>
                                    </div>
                                    <span class="badge bg-{{ record.status_color }}">{{ record.status|upper }}</span>
                                </div>
                                <div class="small text-muted">
                                    {{ record.application_date|date:"M d, Y" }} by {{ record.applied_by.name }}
                                </div>
                                {% if record.notes %}
                                <div class="small mt-2">
                                    <i class="fas fa-comment-alt me-1"></i> {{ record.notes }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list text-muted mb-3" style="font-size: 3rem;"></i>
                        <p class="mb-0">No recent treatment records found.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Medicine Inventory & Quick Actions -->
        <div class="col-md-6 mb-4">
            <div class="row">
                <!-- Medicine Inventory -->
                <div class="col-12 mb-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Medicine Inventory</h5>
                            <a href="{% url 'medicine:inventory_dashboard' %}" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="card-body">
                            {% if inventory_items %}
                                {% for item in inventory_items %}
                                <div class="inventory-item">
                                    <div>
                                        <div class="inventory-name">{{ item.medicine.name }}</div>
                                        <div class="small text-muted">{{ item.medicine.category }}</div>
                                    </div>
                                    <div class="text-end">
                                        <div class="inventory-quantity {% if item.quantity <= item.reorder_level %}inventory-low{% elif item.quantity <= item.reorder_level|add:5 %}inventory-warning{% else %}inventory-good{% endif %}">
                                            {{ item.quantity }} <span class="inventory-unit">{{ item.medicine.unit }}</span>
                                        </div>
                                        <div class="small text-muted">Expires: {{ item.expiry_date|date:"M d, Y" }}</div>
                                    </div>
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-3">
                                    <p class="mb-0">No inventory items found.</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Quick Actions</h5>
                        </div>
                        <div class="card-body">
                            <a href="{% url 'medicine:treatment_plan_create' %}" class="quick-action-btn">
                                <i class="fas fa-plus-circle text-primary"></i>
                                <span>Start New Treatment</span>
                            </a>
                            <a href="{% url 'medicine:application_create' %}" class="quick-action-btn">
                                <i class="fas fa-syringe text-success"></i>
                                <span>Record Treatment Application</span>
                            </a>
                            <a href="{% url 'medicine:medicine_create' %}" class="quick-action-btn">
                                <i class="fas fa-capsules text-warning"></i>
                                <span>Add New Medicine</span>
                            </a>
                            <!-- Inventory update link removed as the view doesn't exist yet -->
                            <a href="{% url 'medicine:inventory_dashboard' %}" class="quick-action-btn">
                                <i class="fas fa-boxes text-info"></i>
                                <span>View Inventory</span>
                            </a>
                            <!-- Reports link removed as the view doesn't exist yet -->
                            <a href="{% url 'medicine:analytics_dashboard' %}" class="quick-action-btn">
                                <i class="fas fa-chart-bar text-secondary"></i>
                                <span>Analytics Dashboard</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    // Treatment Chart
    const treatmentCtx = document.getElementById('treatmentChart').getContext('2d');
    const treatmentChart = new Chart(treatmentCtx, {
        type: 'bar',
        data: {
            labels: {{ chart_labels|safe }},
            datasets: [
                {
                    label: 'Antibiotics',
                    data: {{ antibiotics_data|safe }},
                    backgroundColor: 'rgba(239, 68, 68, 0.5)',
                    borderColor: 'rgba(239, 68, 68, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Probiotics',
                    data: {{ probiotics_data|safe }},
                    backgroundColor: 'rgba(16, 185, 129, 0.5)',
                    borderColor: 'rgba(16, 185, 129, 1)',
                    borderWidth: 1
                },
                {
                    label: 'Water Treatment',
                    data: {{ water_treatment_data|safe }},
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: ${context.raw} treatments`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Treatments'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            }
        }
    });

    // Update chart timeframe
    function updateChartTimeframe(timeframe) {
        // This would be replaced with an AJAX call to get data for the selected timeframe
        // For now, we'll just simulate changing the active button
        const buttons = document.querySelectorAll('.btn-group .btn');
        buttons.forEach(button => {
            button.classList.remove('active');
            if (button.textContent.toLowerCase().includes(timeframe)) {
                button.classList.add('active');
            }
        });
    }
</script>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
</body>
</html>
