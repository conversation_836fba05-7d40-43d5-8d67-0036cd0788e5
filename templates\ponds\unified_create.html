{% extends 'base.html' %}
{% load static %}

{% block title %}Create Pond or Farm{% endblock %}

{% block extra_css %}
<style>
    .creation-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    }

    .creation-card {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .creation-type-selector {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 2px solid #e9ecef;
    }

    .creation-type-option {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .creation-type-option:hover {
        border-color: #667eea;
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
    }

    .creation-type-option.selected {
        border-color: #667eea;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
    }

    .form-section {
        margin-bottom: 30px;
        padding: 25px;
        background: #f8f9fa;
        border-radius: 15px;
        border-left: 5px solid #667eea;
    }

    .form-section h5 {
        color: #667eea;
        margin-bottom: 20px;
        font-weight: bold;
    }

    .form-section.hidden {
        display: none;
    }

    .pond-layout-preview {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-top: 15px;
        text-align: center;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .layout-grid {
        display: grid;
        gap: 10px;
        justify-content: center;
    }

    .pond-preview {
        width: 30px;
        height: 30px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 5px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.8em;
        font-weight: bold;
    }

    .btn-primary {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
    }

    .helper-text {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        border-left: 4px solid #2196f3;
    }

    .coordinate-helper {
        background: #e8f5e8;
        border-radius: 8px;
        padding: 15px;
        margin-top: 10px;
        border-left: 4px solid #4caf50;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="creation-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-plus-circle"></i> Create Pond or Farm</h1>
                <p class="mb-2">Choose to create a single pond or a complete farm with multiple ponds</p>
                <span class="badge bg-light text-dark">
                    <i class="fas fa-magic"></i> Unified Creation System
                </span>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'ponds:pond_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left"></i> Back to Ponds
                </a>
            </div>
        </div>
    </div>

    <!-- Creation Form -->
    <div class="creation-card">
        <form method="post" id="unifiedCreationForm">
            {% csrf_token %}
            
            <!-- Creation Type Selection -->
            <div class="creation-type-selector">
                <h4><i class="fas fa-route"></i> What would you like to create?</h4>
                <p class="text-muted mb-4">Choose your creation type to get started</p>
                
                <div class="creation-type-option" data-type="single_pond">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <i class="fas fa-water fa-3x"></i>
                        </div>
                        <div class="col-md-10">
                            <h5>Create Single Pond</h5>
                            <p class="mb-0">Create one pond with detailed specifications. Perfect for adding to existing farms or starting small.</p>
                        </div>
                    </div>
                    <input type="radio" name="creation_type" value="single_pond" class="d-none" checked>
                </div>
                
                <div class="creation-type-option" data-type="farm_multiple_ponds">
                    <div class="row align-items-center">
                        <div class="col-md-2 text-center">
                            <i class="fas fa-industry fa-3x"></i>
                        </div>
                        <div class="col-md-10">
                            <h5>Create Farm with Multiple Ponds</h5>
                            <p class="mb-0">Create a complete farm with multiple ponds in organized layouts. Ideal for large-scale operations.</p>
                        </div>
                    </div>
                    <input type="radio" name="creation_type" value="farm_multiple_ponds" class="d-none">
                </div>
            </div>

            <!-- Farm Information (Always shown) -->
            <div class="form-section">
                <h5><i class="fas fa-building"></i> Farm Information</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.farm_name.id_for_label }}" class="form-label">
                                <strong>Farm Name</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.farm_name }}
                            {% if form.farm_name.errors %}
                                <div class="text-danger">{{ form.farm_name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.farm_location.id_for_label }}" class="form-label">
                                <strong>Location</strong>
                            </label>
                            {{ form.farm_location }}
                            {% if form.farm_location.errors %}
                                <div class="text-danger">{{ form.farm_location.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.farm_latitude.id_for_label }}" class="form-label">
                                <strong>Farm Latitude</strong>
                            </label>
                            {{ form.farm_latitude }}
                            {% if form.farm_latitude.errors %}
                                <div class="text-danger">{{ form.farm_latitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.farm_longitude.id_for_label }}" class="form-label">
                                <strong>Farm Longitude</strong>
                            </label>
                            {{ form.farm_longitude }}
                            {% if form.farm_longitude.errors %}
                                <div class="text-danger">{{ form.farm_longitude.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="coordinate-helper">
                    <small><strong>📍 Location Helper:</strong></small><br>
                    <small>Enter the center coordinates of your farm area</small><br>
                    <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="getCurrentLocation('farm')">
                        <i class="fas fa-location-arrow"></i> Get Current Location
                    </button>
                </div>
                <div class="mt-3">
                    <label for="{{ form.farm_description.id_for_label }}" class="form-label">
                        <strong>Description</strong>
                    </label>
                    {{ form.farm_description }}
                    {% if form.farm_description.errors %}
                        <div class="text-danger">{{ form.farm_description.errors }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Contact Information -->
            <div class="form-section">
                <h5><i class="fas fa-address-book"></i> Contact Information</h5>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.contact_person.id_for_label }}" class="form-label">
                                <strong>Contact Person</strong>
                            </label>
                            {{ form.contact_person }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.contact_phone.id_for_label }}" class="form-label">
                                <strong>Phone</strong>
                            </label>
                            {{ form.contact_phone }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.contact_email.id_for_label }}" class="form-label">
                                <strong>Email</strong>
                            </label>
                            {{ form.contact_email }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Single Pond Section -->
            <div class="form-section" id="single-pond-section">
                <h5><i class="fas fa-water"></i> Pond Details</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_name.id_for_label }}" class="form-label">
                                <strong>Pond Name</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.pond_name }}
                            {% if form.pond_name.errors %}
                                <div class="text-danger">{{ form.pond_name.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_size.id_for_label }}" class="form-label">
                                <strong>Size (m²)</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.pond_size }}
                            {% if form.pond_size.errors %}
                                <div class="text-danger">{{ form.pond_size.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_species.id_for_label }}" class="form-label">
                                <strong>Species</strong>
                            </label>
                            {{ form.pond_species }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_density.id_for_label }}" class="form-label">
                                <strong>Stocking Density</strong>
                            </label>
                            {{ form.pond_density }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_latitude.id_for_label }}" class="form-label">
                                <strong>Pond Latitude</strong>
                            </label>
                            {{ form.pond_latitude }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_longitude.id_for_label }}" class="form-label">
                                <strong>Pond Longitude</strong>
                            </label>
                            {{ form.pond_longitude }}
                        </div>
                    </div>
                </div>
                <div class="coordinate-helper">
                    <small><strong>📍 Pond Location:</strong></small><br>
                    <small>Specify exact pond coordinates or use farm center</small><br>
                    <button type="button" class="btn btn-sm btn-outline-success mt-2" onclick="useFarmLocation()">
                        <i class="fas fa-crosshairs"></i> Use Farm Location
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-info mt-2" onclick="getCurrentLocation('pond')">
                        <i class="fas fa-location-arrow"></i> Get Current Location
                    </button>
                </div>
            </div>

            <!-- Multiple Ponds Section -->
            <div class="form-section hidden" id="multiple-ponds-section">
                <h5><i class="fas fa-th"></i> Multiple Ponds Configuration</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.number_of_ponds.id_for_label }}" class="form-label">
                                <strong>Number of Ponds</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.number_of_ponds }}
                            {% if form.number_of_ponds.errors %}
                                <div class="text-danger">{{ form.number_of_ponds.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_naming_pattern.id_for_label }}" class="form-label">
                                <strong>Naming Pattern</strong>
                            </label>
                            {{ form.pond_naming_pattern }}
                            <small class="form-text text-muted">Use {number} for pond number</small>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.default_pond_size.id_for_label }}" class="form-label">
                                <strong>Default Size (m²)</strong> <span class="text-danger">*</span>
                            </label>
                            {{ form.default_pond_size }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.default_species.id_for_label }}" class="form-label">
                                <strong>Default Species</strong>
                            </label>
                            {{ form.default_species }}
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="{{ form.default_density.id_for_label }}" class="form-label">
                                <strong>Default Density</strong>
                            </label>
                            {{ form.default_density }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_layout.id_for_label }}" class="form-label">
                                <strong>Layout Pattern</strong>
                            </label>
                            {{ form.pond_layout }}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.pond_spacing.id_for_label }}" class="form-label">
                                <strong>Spacing (meters)</strong>
                            </label>
                            {{ form.pond_spacing }}
                        </div>
                    </div>
                </div>
                
                <!-- Layout Preview -->
                <div class="pond-layout-preview" id="layout-preview">
                    <div class="text-muted">
                        <i class="fas fa-eye fa-2x mb-2"></i><br>
                        Layout preview will appear here
                    </div>
                </div>
            </div>

            <!-- Additional Options -->
            <div class="form-section">
                <h5><i class="fas fa-cogs"></i> Additional Setup Options</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.setup_weather_monitoring }}
                            <label class="form-check-label" for="{{ form.setup_weather_monitoring.id_for_label }}">
                                <strong>Setup Weather Monitoring</strong>
                            </label>
                            <small class="form-text text-muted">Automatically create weather stations for monitoring</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check mb-3">
                            {{ form.setup_aerators }}
                            <label class="form-check-label" for="{{ form.setup_aerators.id_for_label }}">
                                <strong>Setup Default Aerators</strong>
                            </label>
                            <small class="form-text text-muted">Add default aerators to each pond</small>
                        </div>
                    </div>
                </div>
                <div class="row" id="aerator-config" style="display: none;">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="{{ form.aerators_per_pond.id_for_label }}" class="form-label">
                                <strong>Aerators per Pond</strong>
                            </label>
                            {{ form.aerators_per_pond }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="d-flex justify-content-between align-items-center mt-4">
                <a href="{% url 'ponds:pond_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
                <div>
                    <button type="button" class="btn btn-info me-2" onclick="previewCreation()">
                        <i class="fas fa-eye"></i> Preview
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Creation type selection
document.querySelectorAll('.creation-type-option').forEach(option => {
    option.addEventListener('click', function() {
        // Remove selected class from all options
        document.querySelectorAll('.creation-type-option').forEach(opt => {
            opt.classList.remove('selected');
            opt.querySelector('input[type="radio"]').checked = false;
        });
        
        // Add selected class to clicked option
        this.classList.add('selected');
        this.querySelector('input[type="radio"]').checked = true;
        
        // Show/hide relevant sections
        const type = this.dataset.type;
        toggleSections(type);
    });
});

function toggleSections(type) {
    const singlePondSection = document.getElementById('single-pond-section');
    const multiplePondsSection = document.getElementById('multiple-ponds-section');
    
    if (type === 'single_pond') {
        singlePondSection.classList.remove('hidden');
        multiplePondsSection.classList.add('hidden');
    } else if (type === 'farm_multiple_ponds') {
        singlePondSection.classList.add('hidden');
        multiplePondsSection.classList.remove('hidden');
        updateLayoutPreview();
    }
}

// Aerator setup toggle
document.getElementById('{{ form.setup_aerators.id_for_label }}').addEventListener('change', function() {
    const aeratorConfig = document.getElementById('aerator-config');
    if (this.checked) {
        aeratorConfig.style.display = 'block';
    } else {
        aeratorConfig.style.display = 'none';
    }
});

// Layout preview update
document.getElementById('{{ form.number_of_ponds.id_for_label }}').addEventListener('input', updateLayoutPreview);
document.getElementById('{{ form.pond_layout.id_for_label }}').addEventListener('change', updateLayoutPreview);

function updateLayoutPreview() {
    const numberOfPonds = parseInt(document.getElementById('{{ form.number_of_ponds.id_for_label }}').value) || 0;
    const layout = document.getElementById('{{ form.pond_layout.id_for_label }}').value;
    const preview = document.getElementById('layout-preview');
    
    if (numberOfPonds === 0) {
        preview.innerHTML = '<div class="text-muted"><i class="fas fa-eye fa-2x mb-2"></i><br>Enter number of ponds to see preview</div>';
        return;
    }
    
    let gridHtml = '';
    let gridStyle = '';
    
    switch (layout) {
        case 'grid':
            const cols = Math.ceil(Math.sqrt(numberOfPonds));
            gridStyle = `grid-template-columns: repeat(${cols}, 1fr);`;
            break;
        case 'linear':
            gridStyle = `grid-template-columns: repeat(${numberOfPonds}, 1fr);`;
            break;
        case 'circular':
            gridStyle = 'grid-template-columns: repeat(3, 1fr);';
            break;
        default:
            gridStyle = 'grid-template-columns: repeat(3, 1fr);';
    }
    
    for (let i = 1; i <= Math.min(numberOfPonds, 12); i++) {
        gridHtml += `<div class="pond-preview">${i}</div>`;
    }
    
    if (numberOfPonds > 12) {
        gridHtml += `<div class="pond-preview">...</div>`;
    }
    
    preview.innerHTML = `
        <div>
            <div class="layout-grid" style="${gridStyle}">
                ${gridHtml}
            </div>
            <small class="text-muted mt-2 d-block">${numberOfPonds} ponds in ${layout} layout</small>
        </div>
    `;
}

// Location helpers
function getCurrentLocation(type) {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            if (type === 'farm') {
                document.getElementById('{{ form.farm_latitude.id_for_label }}').value = position.coords.latitude.toFixed(6);
                document.getElementById('{{ form.farm_longitude.id_for_label }}').value = position.coords.longitude.toFixed(6);
            } else if (type === 'pond') {
                document.getElementById('{{ form.pond_latitude.id_for_label }}').value = position.coords.latitude.toFixed(6);
                document.getElementById('{{ form.pond_longitude.id_for_label }}').value = position.coords.longitude.toFixed(6);
            }
            showAlert('success', 'Current location coordinates applied');
        }, function(error) {
            showAlert('error', 'Unable to get current location: ' + error.message);
        });
    } else {
        showAlert('error', 'Geolocation is not supported by this browser');
    }
}

function useFarmLocation() {
    const farmLat = document.getElementById('{{ form.farm_latitude.id_for_label }}').value;
    const farmLng = document.getElementById('{{ form.farm_longitude.id_for_label }}').value;
    
    if (farmLat && farmLng) {
        document.getElementById('{{ form.pond_latitude.id_for_label }}').value = farmLat;
        document.getElementById('{{ form.pond_longitude.id_for_label }}').value = farmLng;
        showAlert('success', 'Farm location coordinates applied to pond');
    } else {
        showAlert('error', 'Please enter farm coordinates first');
    }
}

function previewCreation() {
    const type = document.querySelector('input[name="creation_type"]:checked').value;
    const farmName = document.getElementById('{{ form.farm_name.id_for_label }}').value;
    
    let message = `Farm: ${farmName}\n`;
    
    if (type === 'single_pond') {
        const pondName = document.getElementById('{{ form.pond_name.id_for_label }}').value;
        const pondSize = document.getElementById('{{ form.pond_size.id_for_label }}').value;
        message += `Pond: ${pondName} (${pondSize} m²)`;
    } else {
        const numberOfPonds = document.getElementById('{{ form.number_of_ponds.id_for_label }}').value;
        const layout = document.getElementById('{{ form.pond_layout.id_for_label }}').value;
        message += `Ponds: ${numberOfPonds} ponds in ${layout} layout`;
    }
    
    alert('Creation Preview:\n\n' + message);
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 5000);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Set initial state
    toggleSections('single_pond');
    updateLayoutPreview();
});
</script>
{% endblock %}
