<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Location Tracking Dashboard - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
      <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Header */
    .tracking-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .tracking-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: tracking-sweep 4s infinite;
    }
    
    @keyframes tracking-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    #map {
        height: 600px;
        width: 100%;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
        position: relative;
        z-index: 10;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
        pointer-events: auto;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .worker-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-left: 4px solid #667eea;
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
    }

    .worker-item:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        text-decoration: none;
        color: inherit;
    }

    .event-item {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .event-item.event-entry {
        border-left: 4px solid #28a745;
    }

    .event-item.event-exit {
        border-left: 4px solid #dc3545;
    }

    .event-item.location-log-item {
        border-left: 4px solid #007bff;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 600;
    }

    .worker-marker {
        border-radius: 50%;
        width: 12px;
        height: 12px;
        border: 2px solid #fff;
        box-shadow: 0 0 5px rgba(0,0,0,0.5);
    }
    .worker-marker.active {
        background-color: #28a745;
    }
    .worker-marker.inactive {
        background-color: #dc3545;
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Header -->
    <div class="tracking-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Location Tracking Dashboard</h1>
                <p class="lead mb-0">Real-time worker tracking and geofence monitoring system</p>
                <div class="mt-3">
                    <span class="badge bg-success me-2" id="onlineWorkers">12 Online</span>
                    <span class="badge bg-warning me-2" id="activeWorkers">8 Active</span>
                    <span class="badge bg-info me-2" id="lastUpdate">Updated: <span id="lastUpdateTime">just now</span></span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end align-items-center">
                    <button class="btn btn-outline-light btn-sm" onclick="toggleRealTimeUpdates()" id="realTimeBtn">
                        <i class="fas fa-pause"></i> Pause Updates
                    </button>
                    <i class="fas fa-map-marked-alt" style="font-size: 3rem; opacity: 0.5;"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="/labor/heat-map/" class="quick-action">
            <i class="fas fa-fire"></i>
            Heat Map
        </a>
        <a href="/labor/geofences/" class="quick-action">
            <i class="fas fa-map-marker-alt"></i>
            Geofences
        </a>
        <a href="/labor/dashboard/" class="quick-action">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
        <a href="/labor/workers/" class="quick-action">
            <i class="fas fa-users"></i>
            Workers
        </a>
    </div>

    <div class="row">
        <!-- Map Column -->
        <div class="col-lg-8">
            <div class="enhanced-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3 class="section-title mb-0">
                        <i class="fas fa-satellite-dish me-2"></i>
                        Live Tracking Map
                    </h3>
                    <div class="d-flex gap-2 align-items-center flex-wrap">
                        <button id="refreshMap" class="control-btn">
                            <i class="fas fa-sync-alt"></i> Refresh
                        </button>
                        <button id="toggleHeatmap" class="control-btn" onclick="toggleHeatmapLayer()">
                            <i class="fas fa-fire"></i> Heatmap
                        </button>
                        <button id="togglePaths" class="control-btn" onclick="toggleWorkerPaths()">
                            <i class="fas fa-route"></i> Paths
                        </button>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">
                                Auto Refresh
                            </label>
                        </div>
                    </div>
                </div>
                <div id="map">
                    <!-- Fallback content while map loads -->
                    <div class="d-flex align-items-center justify-content-center h-100" style="min-height: 500px;">
                        <div class="text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5 class="text-muted">Loading Google Maps...</h5>
                            <p class="text-muted">Initializing location tracking interface</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Column -->
        <div class="col-lg-4">
            <!-- Active Workers -->
            <div class="enhanced-card">
                <h3 class="section-title">
                    <i class="fas fa-users me-2"></i>
                    Active Workers (3)
                </h3>
                <div class="worker-list">
                    <!-- Mock worker data for testing -->
                    <a href="/labor/workers/1/location-history/" class="worker-item d-block">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">John Doe</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i> 
                                    13.082700, 80.270700
                                </p>
                                <p class="mb-0 text-muted small">
                                    <i class="fas fa-clock me-1"></i> 
                                    Last update: Jul 05, 14:30
                                </p>
                            </div>
                            <div>
                                <span class="status-badge bg-success">Active</span>
                            </div>
                        </div>
                    </a>
                    <a href="/labor/workers/2/location-history/" class="worker-item d-block">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Jane Smith</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i> 
                                    13.085000, 80.275000
                                </p>
                                <p class="mb-0 text-muted small">
                                    <i class="fas fa-clock me-1"></i> 
                                    Last update: Jul 05, 14:25
                                </p>
                            </div>
                            <div>
                                <span class="status-badge bg-success">Active</span>
                            </div>
                        </div>
                    </a>
                    <a href="/labor/workers/3/location-history/" class="worker-item d-block">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Mike Johnson</h6>
                                <p class="mb-1 text-muted small">
                                    <i class="fas fa-map-marker-alt me-1"></i> 
                                    13.080000, 80.268000
                                </p>
                                <p class="mb-0 text-muted small">
                                    <i class="fas fa-clock me-1"></i> 
                                    Last update: Jul 05, 14:28
                                </p>
                            </div>
                            <div>
                                <span class="status-badge bg-warning text-dark">Break</span>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Recent Geofence Events -->
            <div class="enhanced-card">
                <h3 class="section-title">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Recent Geofence Events
                </h3>
                <div class="event-list">
                    <!-- Mock geofence events -->
                    <div class="event-item event-entry">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">John Doe</h6>
                                <p class="mb-1 text-muted small">
                                    <span class="me-2">
                                        <i class="fas fa-sign-in-alt me-1"></i> 
                                        Entry - Pond Area A
                                    </span>
                                    <br>
                                    <span><i class="fas fa-clock me-1"></i> Jul 05, 2025 14:30:15</span>
                                </p>
                            </div>
                            <div>
                                <span class="status-badge bg-success text-white">Entry</span>
                            </div>
                        </div>
                    </div>
                    <div class="event-item event-exit">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Jane Smith</h6>
                                <p class="mb-1 text-muted small">
                                    <span class="me-2">
                                        <i class="fas fa-sign-out-alt me-1"></i> 
                                        Exit - Processing Area
                                    </span>
                                    <br>
                                    <span><i class="fas fa-clock me-1"></i> Jul 05, 2025 14:25:42</span>
                                </p>
                            </div>
                            <div>
                                <span class="status-badge bg-danger text-white">Exit</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Location Logs -->
            <div class="enhanced-card">
                <h3 class="section-title">
                    <i class="fas fa-route me-2"></i>
                    Recent Location Updates
                </h3>
                <div class="location-logs">
                    <!-- Mock location logs -->
                    <div class="event-item location-log-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">John Doe</h6>
                                <p class="mb-1 text-muted small">
                                    <span class="me-2"><i class="fas fa-map-marker-alt me-1"></i> 13.082700, 80.270700</span>
                                    <br>
                                    <span><i class="fas fa-clock me-1"></i> Jul 05, 2025 14:30:15</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="event-item location-log-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Jane Smith</h6>
                                <p class="mb-1 text-muted small">
                                    <span class="me-2"><i class="fas fa-map-marker-alt me-1"></i> 13.085000, 80.275000</span>
                                    <br>
                                    <span><i class="fas fa-clock me-1"></i> Jul 05, 2025 14:25:42</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="event-item location-log-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Mike Johnson</h6>
                                <p class="mb-1 text-muted small">
                                    <span class="me-2"><i class="fas fa-map-marker-alt me-1"></i> 13.080000, 80.268000</span>
                                    <br>
                                    <span><i class="fas fa-clock me-1"></i> Jul 05, 2025 14:28:33</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    let map, infoWindow;
    let workerMarkers = {};
    let geofencePolygons = {};
    let workerPaths = {};
    let showPaths = false;
    let refreshInterval;

    function initMap() {
        console.log('Initializing Google Maps for Location Tracking...');

        try {
            // Check if google is available
            if (typeof google === 'undefined') {
                console.error('Google Maps API not loaded');
                document.getElementById('map').innerHTML = `
                    <div class="alert alert-warning m-3">
                        <h5><i class="fas fa-exclamation-triangle"></i> Google Maps Loading...</h5>
                        <p>Please wait while Google Maps loads...</p>
                    </div>
                `;
                // Retry after 2 seconds
                setTimeout(initMap, 2000);
                return;
            }

            // Initialize map
            map = new google.maps.Map(document.getElementById('map'), {
                center: { lat: 13.0827, lng: 80.2707 }, // Chennai, India
                zoom: 12,
                mapTypeId: 'roadmap',
                mapTypeControl: true,
                mapTypeControlOptions: {
                    style: google.maps.MapTypeControlStyle.DROPDOWN_MENU
                },
                zoomControl: true,
                streetViewControl: false,
                fullscreenControl: true
            });

            // Initialize info window
            infoWindow = new google.maps.InfoWindow();

            // Load initial data
            updateMap();

            console.log('Google Maps initialized successfully');
        } catch (error) {
            console.error('Error initializing Google Maps:', error);
            document.getElementById('map').innerHTML = `
                <div class="alert alert-danger m-3">
                    <h5><i class="fas fa-exclamation-triangle"></i> Error Loading Map</h5>
                    <p>${error.message}</p>
                    <p class="mb-0">Please check your internet connection and refresh the page.</p>
                </div>
            `;
        }
    }

    function updateMap() {
        // Clear existing markers
        clearMarkers();

        // Add worker markers (using mock data for now)
        const mockWorkers = [
            { id: 1, name: "John Doe", lat: 13.0827, lng: 80.2707, status: "working", role: "Pond Technician" },
            { id: 2, name: "Jane Smith", lat: 13.0850, lng: 80.2750, status: "available", role: "Feed Manager" },
            { id: 3, name: "Mike Johnson", lat: 13.0800, lng: 80.2680, status: "break", role: "Water Quality Specialist" },
            { id: 4, name: "Sarah Wilson", lat: 13.0870, lng: 80.2720, status: "working", role: "Harvest Coordinator" }
        ];

        mockWorkers.forEach(worker => {
            addWorkerMarker(worker.id, worker.name, worker.lat, worker.lng, worker.status, worker.role);
        });

        // Add geofences (mock data)
        const mockGeofences = [
            { id: 1, name: "Pond Area A", coordinates: [
                { lat: 13.0820, lng: 80.2700 },
                { lat: 13.0840, lng: 80.2700 },
                { lat: 13.0840, lng: 80.2730 },
                { lat: 13.0820, lng: 80.2730 }
            ]},
            { id: 2, name: "Processing Area", coordinates: [
                { lat: 13.0850, lng: 80.2740 },
                { lat: 13.0870, lng: 80.2740 },
                { lat: 13.0870, lng: 80.2770 },
                { lat: 13.0850, lng: 80.2770 }
            ]}
        ];

        mockGeofences.forEach(geofence => {
            addGeofence(geofence.id, geofence.name, geofence.coordinates);
        });
    }

    function addWorkerMarker(workerId, name, lat, lng, status, role) {
        const statusColors = {
            'working': '#28a745',
            'available': '#007bff',
            'break': '#ffc107',
            'offline': '#6c757d'
        };

        // Use AdvancedMarkerElement if available, fallback to regular Marker
        let marker;
        try {
            if (google.maps.marker && google.maps.marker.AdvancedMarkerElement) {
                // Create a custom pin element
                const pinElement = new google.maps.marker.PinElement({
                    background: statusColors[status] || '#6c757d',
                    borderColor: '#ffffff',
                    glyphColor: '#ffffff',
                    scale: 1.2
                });

                marker = new google.maps.marker.AdvancedMarkerElement({
                    position: { lat: lat, lng: lng },
                    map: map,
                    title: name,
                    content: pinElement.element
                });
            } else {
                // Fallback to regular marker
                marker = new google.maps.Marker({
                    position: { lat: lat, lng: lng },
                    map: map,
                    title: name,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: statusColors[status] || '#6c757d',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 2
                    }
                });
            }
        } catch (error) {
            console.warn('Using fallback marker due to:', error);
            // Fallback to regular marker
            marker = new google.maps.Marker({
                position: { lat: lat, lng: lng },
                map: map,
                title: name,
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    scale: 8,
                    fillColor: statusColors[status] || '#6c757d',
                    fillOpacity: 1,
                    strokeColor: '#ffffff',
                    strokeWeight: 2
                }
            });
        }

        const infoContent = `
            <div style="padding: 10px; min-width: 200px;">
                <h6 style="margin: 0 0 8px 0; color: #333;">${name}</h6>
                <p style="margin: 0 0 4px 0; color: #666;"><strong>Role:</strong> ${role}</p>
                <p style="margin: 0 0 4px 0; color: #666;"><strong>Status:</strong>
                    <span style="color: ${statusColors[status]}; font-weight: bold;">${status.charAt(0).toUpperCase() + status.slice(1)}</span>
                </p>
                <p style="margin: 0; color: #666;"><strong>Location:</strong> ${lat.toFixed(4)}, ${lng.toFixed(4)}</p>
            </div>
        `;

        marker.addListener('click', () => {
            infoWindow.setContent(infoContent);
            infoWindow.open(map, marker);
        });

        workerMarkers[workerId] = marker;
    }

    function addGeofence(geofenceId, name, coordinates) {
        const polygon = new google.maps.Polygon({
            paths: coordinates,
            strokeColor: '#007bff',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#007bff',
            fillOpacity: 0.2,
            map: map
        });

        const infoContent = `
            <div style="padding: 10px;">
                <h6 style="margin: 0; color: #333;">${name}</h6>
                <p style="margin: 4px 0 0 0; color: #666;">Geofence Area</p>
            </div>
        `;

        polygon.addListener('click', (event) => {
            infoWindow.setContent(infoContent);
            infoWindow.setPosition(event.latLng);
            infoWindow.open(map);
        });

        geofencePolygons[geofenceId] = polygon;
    }

    function clearMarkers() {
        // Clear worker markers
        Object.values(workerMarkers).forEach(marker => {
            marker.setMap(null);
        });
        workerMarkers = {};

        // Clear geofences
        Object.values(geofencePolygons).forEach(polygon => {
            polygon.setMap(null);
        });
        geofencePolygons = {};
    }

    // Auto refresh functionality
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            updateMap();
        }, 30000); // Refresh every 30 seconds
    }

    function stopAutoRefresh() {
        clearInterval(refreshInterval);
    }

    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Location tracking dashboard initialized');
        
        // Add a manual map initialization button if needed
        setTimeout(function() {
            if (!map && document.getElementById('map')) {
                const mapElement = document.getElementById('map');
                mapElement.innerHTML = `
                    <div class="alert alert-warning m-3">
                        <h5><i class="fas fa-exclamation-triangle"></i> Map Loading Issue</h5>
                        <p>Google Maps is taking longer than expected to load.</p>
                        <button class="btn btn-primary" onclick="initMap()">
                            <i class="fas fa-refresh"></i> Try Loading Map Again
                        </button>
                    </div>
                `;
            }
        }, 10000); // Wait 10 seconds before showing manual retry
        
        // Add click handlers for quick actions (debugging)
        document.querySelectorAll('.quick-action').forEach(function(link) {
            link.addEventListener('click', function(e) {
                console.log('Quick action clicked:', this.href);
                // Allow normal navigation to proceed
            });
        });
        
        // Auto refresh toggle
        const autoRefreshToggle = document.getElementById('autoRefresh');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', function() {
                console.log('Auto refresh toggled:', this.checked);
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });

            // Start auto refresh by default if checked
            if (autoRefreshToggle.checked) {
                startAutoRefresh();
            }
        }

        // Manual refresh button
        const refreshButton = document.getElementById('refreshMap');
        if (refreshButton) {
            refreshButton.addEventListener('click', function() {
                console.log('Manual refresh clicked');
                if (map) {
                    updateMap();
                } else {
                    console.log('Map not initialized, trying to initialize...');
                    initMap();
                }
            });
        }
    });

</script>

<!-- Leaflet JS (Alternative Map) -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" 
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" 
        crossorigin=""></script>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Google Maps API -->
<script>
    // Fallback to OpenStreetMap if Google Maps fails
    function initFallbackMap() {
        console.log('Initializing fallback OpenStreetMap...');
        
        // Clear loading content
        document.getElementById('map').innerHTML = '';
        
        // Initialize Leaflet map
        map = L.map('map').setView([13.0827, 80.2707], 12);
        
        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);
        
        // Add worker markers
        const mockWorkers = [
            { id: 1, name: "John Doe", lat: 13.0827, lng: 80.2707, status: "working", role: "Pond Technician" },
            { id: 2, name: "Jane Smith", lat: 13.0850, lng: 80.2750, status: "available", role: "Feed Manager" },
            { id: 3, name: "Mike Johnson", lat: 13.0800, lng: 80.2680, status: "break", role: "Water Quality Specialist" },
            { id: 4, name: "Sarah Wilson", lat: 13.0870, lng: 80.2720, status: "working", role: "Harvest Coordinator" }
        ];
        
        mockWorkers.forEach(worker => {
            const statusColors = {
                'working': 'green',
                'available': 'blue',
                'break': 'orange',
                'offline': 'gray'
            };
            
            const marker = L.circleMarker([worker.lat, worker.lng], {
                color: 'white',
                fillColor: statusColors[worker.status],
                fillOpacity: 1,
                radius: 8,
                weight: 2
            }).addTo(map);
            
            marker.bindPopup(`
                <div>
                    <h6>${worker.name}</h6>
                    <p><strong>Role:</strong> ${worker.role}</p>
                    <p><strong>Status:</strong> ${worker.status}</p>
                    <p><strong>Location:</strong> ${worker.lat}, ${worker.lng}</p>
                </div>
            `);
        });
        
        console.log('Fallback map initialized successfully');
    }
    
    // Try Google Maps first, fallback to OpenStreetMap
    function loadMapWithFallback() {
        if (typeof google !== 'undefined' && google.maps) {
            initMap();
        } else {
            console.log('Google Maps not available, using OpenStreetMap fallback');
            initFallbackMap();
        }
    }
    
    // Real-time Status Updates System
    let realTimeUpdatesActive = true;
    let updateInterval;

    function startRealTimeUpdates() {
        updateInterval = setInterval(() => {
            if (realTimeUpdatesActive) {
                updateWorkerStatuses();
                updateLastUpdateTime();
            }
        }, 5000); // Update every 5 seconds
    }

    function updateWorkerStatuses() {
        // Simulate real-time worker status updates
        const onlineCount = Math.floor(Math.random() * 3) + 10; // 10-12 workers
        const activeCount = Math.floor(Math.random() * 3) + 6;  // 6-8 workers

        document.getElementById('onlineWorkers').textContent = `${onlineCount} Online`;
        document.getElementById('activeWorkers').textContent = `${activeCount} Active`;

        // Update worker positions slightly (simulate movement)
        if (typeof mockWorkers !== 'undefined') {
            mockWorkers.forEach(worker => {
                worker.lat += (Math.random() - 0.5) * 0.0001;
                worker.lng += (Math.random() - 0.5) * 0.0001;
                worker.lastUpdate = new Date().toISOString();
            });
        }

        console.log('Worker statuses updated:', { online: onlineCount, active: activeCount });
    }

    function updateLastUpdateTime() {
        document.getElementById('lastUpdateTime').textContent = new Date().toLocaleTimeString();
    }

    function toggleRealTimeUpdates() {
        realTimeUpdatesActive = !realTimeUpdatesActive;
        const btn = document.getElementById('realTimeBtn');

        if (realTimeUpdatesActive) {
            btn.innerHTML = '<i class="fas fa-pause"></i> Pause Updates';
            btn.className = 'btn btn-outline-light btn-sm';
        } else {
            btn.innerHTML = '<i class="fas fa-play"></i> Resume Updates';
            btn.className = 'btn btn-outline-warning btn-sm';
        }
    }

    // Worker Status Notification System
    function showWorkerStatusNotification(workerId, status, message) {
        const notification = document.createElement('div');
        notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; max-width: 300px;';
        notification.innerHTML = `
            <strong>Worker ${workerId}:</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    // Simulate worker status changes
    function simulateWorkerEvents() {
        const events = [
            { workerId: 'FW001', status: 'task_completed', message: 'Completed water quality check' },
            { workerId: 'FW002', status: 'break_started', message: 'Started break period' },
            { workerId: 'FW003', status: 'geofence_exit', message: 'Left Pond A-1 area' },
            { workerId: 'FW004', status: 'task_started', message: 'Started equipment maintenance' },
            { workerId: 'FW005', status: 'emergency', message: 'Emergency alert triggered' }
        ];

        setInterval(() => {
            if (realTimeUpdatesActive && Math.random() > 0.7) {
                const event = events[Math.floor(Math.random() * events.length)];
                showWorkerStatusNotification(event.workerId, event.status, event.message);
            }
        }, 10000); // Random events every 10 seconds
    }

    // Enhanced Heatmap System
    let heatmapLayer = null;
    let heatmapVisible = false;
    let enhancedWorkerPaths = [];
    let pathsVisible = false;

    function generateHeatmapData() {
        // Generate realistic heatmap data points based on worker activity
        const heatmapData = [];
        const hotspots = [
            { lat: 10.7905, lng: 106.6800, intensity: 0.8 }, // Pond A-1
            { lat: 10.7910, lng: 106.6805, intensity: 0.6 }, // Pond A-2
            { lat: 10.7900, lng: 106.6810, intensity: 0.9 }, // Feed Station
            { lat: 10.7915, lng: 106.6795, intensity: 0.7 }, // Equipment Shed
            { lat: 10.7895, lng: 106.6815, intensity: 0.5 }  // Water Treatment
        ];

        hotspots.forEach(hotspot => {
            // Add multiple points around each hotspot to create density
            for (let i = 0; i < 20; i++) {
                heatmapData.push({
                    lat: hotspot.lat + (Math.random() - 0.5) * 0.001,
                    lng: hotspot.lng + (Math.random() - 0.5) * 0.001,
                    weight: hotspot.intensity * Math.random()
                });
            }
        });

        return heatmapData;
    }

    function toggleHeatmapLayer() {
        const btn = document.getElementById('toggleHeatmap');

        if (!heatmapVisible) {
            // Show heatmap
            const heatmapData = generateHeatmapData();

            if (typeof google !== 'undefined' && google.maps) {
                // Google Maps heatmap
                heatmapLayer = new google.maps.visualization.HeatmapLayer({
                    data: heatmapData.map(point => ({
                        location: new google.maps.LatLng(point.lat, point.lng),
                        weight: point.weight
                    })),
                    map: map,
                    radius: 50,
                    opacity: 0.7
                });
            } else {
                // Fallback visualization
                console.log('Heatmap data generated:', heatmapData);
                showHeatmapFallback(heatmapData);
            }

            btn.innerHTML = '<i class="fas fa-fire"></i> Hide Heatmap';
            btn.classList.add('active');
            heatmapVisible = true;

            showWorkerStatusNotification('SYSTEM', 'heatmap_enabled', 'Worker density heatmap activated');
        } else {
            // Hide heatmap
            if (heatmapLayer) {
                heatmapLayer.setMap(null);
                heatmapLayer = null;
            }

            btn.innerHTML = '<i class="fas fa-fire"></i> Heatmap';
            btn.classList.remove('active');
            heatmapVisible = false;

            showWorkerStatusNotification('SYSTEM', 'heatmap_disabled', 'Worker density heatmap deactivated');
        }
    }

    function showHeatmapFallback(heatmapData) {
        // Create visual representation for fallback
        const mapDiv = document.getElementById('map');
        const heatmapOverlay = document.createElement('div');
        heatmapOverlay.id = 'heatmapOverlay';
        heatmapOverlay.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        `;

        heatmapData.forEach(point => {
            const dot = document.createElement('div');
            dot.style.cssText = `
                position: absolute;
                width: 20px;
                height: 20px;
                background: radial-gradient(circle, rgba(255,0,0,${point.weight}) 0%, transparent 70%);
                border-radius: 50%;
                left: ${Math.random() * 80 + 10}%;
                top: ${Math.random() * 80 + 10}%;
            `;
            heatmapOverlay.appendChild(dot);
        });

        mapDiv.appendChild(heatmapOverlay);
    }

    function toggleWorkerPaths() {
        const btn = document.getElementById('togglePaths');

        if (!pathsVisible) {
            // Show worker movement paths
            generateWorkerPaths();
            btn.innerHTML = '<i class="fas fa-route"></i> Hide Paths';
            btn.classList.add('active');
            pathsVisible = true;

            showWorkerStatusNotification('SYSTEM', 'paths_enabled', 'Worker movement paths displayed');
        } else {
            // Hide paths
            clearWorkerPaths();
            btn.innerHTML = '<i class="fas fa-route"></i> Paths';
            btn.classList.remove('active');
            pathsVisible = false;

            showWorkerStatusNotification('SYSTEM', 'paths_disabled', 'Worker movement paths hidden');
        }
    }

    function generateWorkerPaths() {
        // Generate realistic worker movement paths
        if (typeof mockWorkers !== 'undefined') {
            mockWorkers.forEach((worker, index) => {
                const path = [];
                const startLat = worker.lat;
                const startLng = worker.lng;

                // Generate path points (simulate movement over time)
                for (let i = 0; i < 10; i++) {
                    path.push({
                        lat: startLat + (Math.random() - 0.5) * 0.002,
                        lng: startLng + (Math.random() - 0.5) * 0.002,
                        timestamp: new Date(Date.now() - (10 - i) * 60000).toISOString()
                    });
                }

                enhancedWorkerPaths.push({
                    workerId: worker.id,
                    path: path,
                    color: `hsl(${index * 60}, 70%, 50%)`
                });
            });
        }

        console.log('Worker paths generated:', enhancedWorkerPaths);
    }

    function clearWorkerPaths() {
        enhancedWorkerPaths = [];
        const pathOverlay = document.getElementById('pathOverlay');
        if (pathOverlay) {
            pathOverlay.remove();
        }
    }

    // Initialize real-time updates
    document.addEventListener('DOMContentLoaded', function() {
        startRealTimeUpdates();
        simulateWorkerEvents();
        updateLastUpdateTime();
    });

    // Initialize map after a delay to allow Google Maps to load
    setTimeout(loadMapWithFallback, 3000);
</script>

<script async defer
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap"
    onerror="initFallbackMap()">
</script>

</body>
</html>