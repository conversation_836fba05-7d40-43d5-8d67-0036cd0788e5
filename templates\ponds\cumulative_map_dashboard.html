{% extends "base.html" %}
{% load static %}

{% block title %}Cumulative Map Dashboard{% endblock %}

{% block extra_css %}
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #2563eb;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --info-color: #3b82f6;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
    }

    .dashboard-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    }

    .stat-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }

    .stat-icon.farms { background: linear-gradient(135deg, #667eea, #764ba2); }
    .stat-icon.ponds { background: linear-gradient(135deg, #f093fb, #f5576c); }
    .stat-icon.aerators { background: linear-gradient(135deg, #4facfe, #00f2fe); }
    .stat-icon.area { background: linear-gradient(135deg, #43e97b, #38f9d7); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--dark-color);
        line-height: 1;
    }

    .stat-label {
        color: #6b7280;
        font-size: 0.9rem;
        margin-top: 5px;
    }

    .stat-change {
        font-size: 0.8rem;
        margin-top: 8px;
        padding: 4px 8px;
        border-radius: 20px;
        display: inline-block;
    }

    .stat-change.positive {
        background: #dcfce7;
        color: #166534;
    }    .map-container {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        min-height: 1080px; /* 1000px map + 80px padding/header */
        height: auto;
    }

    .map-header {
        background: linear-gradient(135deg, var(--primary-color), #1e40af);
        color: white;
        padding: 20px 30px;
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .map-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
    }

    .map-controls {
        display: flex;
        gap: 10px;
    }

    .map-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .map-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
    }

    .map-btn.active {
        background: white;
        color: var(--primary-color);
    }    #map {
        height: 1000px;
        width: 100%;
    }

    .legend-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .legend-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .legend-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 10px;
        background: #f8fafc;
        border-radius: 10px;
        transition: background 0.3s ease;
    }

    .legend-item:hover {
        background: #e2e8f0;
    }

    .legend-marker {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .legend-marker.farm { background: #2563eb; }
    .legend-marker.pond-active { background: #10b981; }
    .legend-marker.pond-maintenance { background: #f59e0b; }
    .legend-marker.pond-empty { background: #6b7280; }
    .legend-marker.pond-harvested { background: #8b5cf6; }

    .details-panel {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 500px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        z-index: 10000;
    }

    .details-panel.show {
        display: block;
    }

    .details-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #e5e7eb;
    }

    .details-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #6b7280;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .close-btn:hover {
        background: #f3f4f6;
        color: var(--dark-color);
    }

    .details-grid {
        display: grid;
        gap: 15px;
    }

    .detail-item {
        display: flex;
        justify-content: between;
        align-items: center;
        padding: 12px;
        background: #f8fafc;
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }

    .detail-label {
        font-weight: 500;
        color: #374151;
    }

    .detail-value {
        font-weight: 600;
        color: var(--dark-color);
    }

    .weather-widget {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-top: 20px;
    }

    .weather-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 15px;
    }

    .weather-temp {
        font-size: 2rem;
        font-weight: 700;
    }

    .weather-desc {
        opacity: 0.9;
        text-transform: capitalize;
    }

    .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        display: none;
    }

    .overlay.show {
        display: block;
    }

    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }    .status-active { background: #dcfce7; color: #166534; }
    .status-maintenance { background: #fef3c7; color: #92400e; }
    .status-empty { background: #f3f4f6; color: #4b5563; }
    .status-harvested { background: #ede9fe; color: #6b21a8; }
    .status-inactive { background: #f3f4f6; color: #6b7280; }
    .status-restricted { background: #fecaca; color: #991b1b; }
    .status-pond { background: #dcfce7; color: #166534; }
    .status-warehouse { background: #dbeafe; color: #1d4ed8; }
    .status-custom { background: #fed7aa; color: #c2410c; }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }

    .action-btn {
        flex: 1;
        padding: 10px;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        text-align: center;
        display: inline-block;
    }

    .action-btn.primary {
        background: var(--primary-color);
        color: white;
    }

    .action-btn.secondary {
        background: #e5e7eb;
        color: var(--dark-color);
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 10px;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-card {
            padding: 20px;
        }

        .stat-number {
            font-size: 2rem;
        }

        #map {
            height: 400px;
        }

        .details-panel {
            max-width: 95%;
            max-height: 90vh;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container standard-cumulative">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="text-white mb-2">
                <i class="fas fa-globe-americas"></i> Cumulative Map Dashboard
            </h1>
            <p class="text-white-50 mb-0">Comprehensive view of all farms and ponds with interactive mapping</p>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon farms">
                    <i class="fas fa-industry"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_farms }}</div>
            <div class="stat-label">Total Farms</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up"></i> {{ farms_with_location }} with GPS
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon ponds">
                    <i class="fas fa-water"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
            <div class="stat-change positive">
                <i class="fas fa-check-circle"></i> {{ active_ponds }} Active
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon aerators">
                    <i class="fas fa-cogs"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_aerators }}</div>
            <div class="stat-label">Total Aerators</div>
            <div class="stat-change positive">
                <i class="fas fa-bolt"></i> {{ active_aerators }} Active
            </div>
        </div>        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon area">
                    <i class="fas fa-expand-arrows-alt"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_area|floatformat:0 }}</div>
            <div class="stat-label">Total Area (m²)</div>
            <div class="stat-change positive">
                <i class="fas fa-map"></i> {{ ponds_with_location }} Mapped
            </div>
        </div>

        <!-- Labor Management Statistics -->
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #8B5CF6, #A855F7);">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_workers|default:0 }}</div>
            <div class="stat-label">Total Workers</div>
            <div class="stat-change positive">
                <i class="fas fa-user-check"></i> {{ active_workers|default:0 }} Active
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon" style="background: linear-gradient(135deg, #F59E0B, #F97316);">
                    <i class="fas fa-map-marked-alt"></i>
                </div>
            </div>
            <div class="stat-number">{{ total_geofences|default:0 }}</div>
            <div class="stat-label">Geofences</div>
            <div class="stat-change positive">
                <i class="fas fa-shield-alt"></i> Safety Zones
            </div>
        </div>
    </div>

    <!-- Interactive Map -->
    <div class="map-container">
        <div class="map-header">
            <h3 class="map-title">
                <i class="fas fa-map-marked-alt"></i> Interactive Farm & Pond Map
            </h3>            <div class="map-controls">
                <button class="map-btn active" id="showAllBtn" onclick="toggleLayer('all')">
                    <i class="fas fa-eye"></i> Show All
                </button>
                <button class="map-btn" id="showFarmsBtn" onclick="toggleLayer('farms')">
                    <i class="fas fa-industry"></i> Farms Only
                </button>
                <button class="map-btn" id="showPondsBtn" onclick="toggleLayer('ponds')">
                    <i class="fas fa-water"></i> Ponds Only
                </button>
                <button class="map-btn" id="showWorkersBtn" onclick="toggleLayer('workers')">
                    <i class="fas fa-users"></i> Workers
                </button>
                <button class="map-btn" id="showGeofencesBtn" onclick="toggleLayer('geofences')">
                    <i class="fas fa-map-marked-alt"></i> Geofences
                </button>
                <button class="map-btn" id="satelliteBtn" onclick="toggleMapType()">
                    <i class="fas fa-satellite"></i> Satellite
                </button>
                <button class="map-btn" id="resetViewBtn" onclick="resetMapView()" title="Reset to overview">
                    <i class="fas fa-home"></i> Reset View
                </button>
            </div>
        </div>
        <div id="map"></div>
        
        <!-- Debug Panel for troubleshooting -->
        <div id="debug-panel" style="position: absolute; top: 10px; right: 10px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); max-width: 300px; z-index: 1000; font-size: 12px;">
            <h4 style="margin: 0 0 10px 0; color: #007bff;">🔍 Map Debug Info</h4>
            <div id="debug-content">
                <div>⏳ Initializing debug panel...</div>
            </div>
        </div>
    </div>

    <!-- Legend Panel -->
    <div class="legend-panel">
        <h4 class="legend-title">
            <i class="fas fa-info-circle"></i> Map Legend
        </h4>
        <div class="legend-grid">
            <div class="legend-item">
                <div class="legend-marker farm"></div>
                <span>Farm Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-active"></div>
                <span>Active Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-maintenance"></div>
                <span>Maintenance Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker pond-empty"></div>
                <span>Empty Pond</span>
            </div>            <div class="legend-item">
                <div class="legend-marker pond-harvested"></div>
                <span>Harvested Pond</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker" style="background: linear-gradient(135deg, #8B5CF6, #A855F7); border-radius: 50%;"></div>
                <span>Worker Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-marker" style="background: linear-gradient(135deg, #F59E0B, #F97316); border-radius: 50%;"></div>
                <span>Geofence Zone</span>
            </div>
        </div>
    </div>
</div>

<!-- Details Panel Overlay -->
<div class="overlay" id="overlay" onclick="closeDetailsPanel()"></div>

<!-- Details Panel -->
<div class="details-panel" id="detailsPanel">
    <div class="details-header">
        <h4 class="details-title" id="detailsTitle">Details</h4>
        <button class="close-btn" onclick="closeDetailsPanel()">
            <i class="fas fa-times"></i>
        </button>
    </div>
    <div class="details-grid" id="detailsContent">
        <!-- Dynamic content will be inserted here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let map;
    let infoWindow;
    let farmMarkers = [];
    let pondMarkers = [];
    let pondPolygons = [];
    let workerMarkers = [];
    let geofenceShapes = [];
    let currentMapType = 'roadmap';
    let visibleLayers = ['farms', 'ponds', 'workers', 'geofences'];

    // Data from Django
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    const workersData = {{ workers_data|safe }};
    const geofencesData = {{ geofences_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};    // Debug logging
    console.log('=== CUMULATIVE MAP DEBUG INFO ===');
    console.log('Farms data:', farmsData);
    console.log('Ponds data:', pondsData);
    console.log('Workers data:', workersData);
    console.log('Geofences data:', geofencesData);
    console.log('Center coordinates:', centerLat, centerLng);
    console.log('API Key available:', '{{ google_maps_api_key }}' ? 'Yes' : 'No');
    console.log('Map container exists:', document.getElementById('map') ? 'Yes' : 'No');
    console.log('Google Maps available:', typeof google !== 'undefined' && google.maps ? 'Yes' : 'No');
    console.log('====================================');

    // Update debug panel
    function updateDebugPanel(message, type = 'info') {
        const debugContent = document.getElementById('debug-content');
        if (debugContent) {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
            debugContent.innerHTML += `<div style="color: ${color}; margin: 2px 0;">${timestamp}: ${message}</div>`;
        }
        console.log(message);
    }
    
    // Initial debug info
    updateDebugPanel('🚀 Starting cumulative map initialization...');
    updateDebugPanel(`📊 Data: ${farmsData.length} farms, ${pondsData.length} ponds, ${workersData.length} workers, ${geofencesData.length} geofences`);
    updateDebugPanel(`📍 Center: ${centerLat}, ${centerLng}`);    // Global error handler for Google Maps
    window.gm_authFailure = function() {
        console.error('❌ Google Maps authentication failed!');
        updateDebugPanel('❌ Google Maps authentication failed!', 'error');
        const mapEl = document.getElementById('map');
        if (mapEl) {
            mapEl.innerHTML = '<div class="alert alert-danger m-3"><h5>Authentication Error</h5><p>Google Maps authentication failed. Please check the API key.</p></div>';
        }
    };

    function initMap() {
        console.log('🗺️ initMap() callback triggered by Google Maps API');
        updateDebugPanel('🗺️ initMap() callback triggered', 'success');
        
        // Check if Google Maps API is loaded
        if (typeof google === 'undefined' || !google.maps) {
            console.error('❌ Google Maps API not loaded');
            updateDebugPanel('❌ Google Maps API not loaded', 'error');
            const mapEl = document.getElementById('map');
            if (mapEl) {
                mapEl.innerHTML = '<div class="alert alert-danger m-3"><h5>Google Maps Not Loaded</h5><p>The Google Maps API failed to load. Please check your internet connection and refresh the page.</p></div>';
            }
            return;
        }
        
        updateDebugPanel('✅ Google Maps API loaded successfully');
        
        // Check if map container exists
        const mapContainer = document.getElementById('map');
        if (!mapContainer) {
            console.error('❌ Map container not found');
            updateDebugPanel('❌ Map container not found', 'error');
            return;
        }
        
        console.log('✅ Map container found:', mapContainer);
        updateDebugPanel('✅ Map container found');
        
        // Check if we have the required data
        if (typeof centerLat === 'undefined' || typeof centerLng === 'undefined') {
            console.error('❌ Center coordinates not available');
            updateDebugPanel('❌ Center coordinates not available', 'error');
            mapContainer.innerHTML = 
                '<div class="alert alert-danger m-3"><h5>Map Data Error</h5><p>Map data not available. Please check your permissions.</p></div>';
            return;
        }

        console.log('✅ Initializing map with center:', centerLat, centerLng);
        updateDebugPanel(`✅ Initializing map at ${centerLat}, ${centerLng}`);
        
        try {
            // Initialize map
            map = new google.maps.Map(mapContainer, {
            zoom: 10,
            center: { lat: centerLat, lng: centerLng },
            mapTypeId: currentMapType,
            styles: [
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ]
        });        console.log('Map created successfully');
        updateDebugPanel('✅ Map created successfully');        infoWindow = new google.maps.InfoWindow();
        updateDebugPanel('✅ Info window created');

        // Add farm markers
        updateDebugPanel('📍 Adding farm markers...');
        addFarmMarkers();

        // Add pond markers and polygons
        updateDebugPanel('🏊 Adding pond markers...');
        addPondMarkers();

        // Add worker markers
        updateDebugPanel('👥 Adding worker markers...');
        addWorkerMarkers();

        // Add geofence shapes
        updateDebugPanel('🛡️ Adding geofences...');
        addGeofenceShapes();

        // Fit map to show all markers
        updateDebugPanel('🎯 Fitting map to markers...');
        fitMapToMarkers();
        
        console.log('✅ Map initialization complete');
        updateDebugPanel('🎉 Map initialization complete!', 'success');
        addGeofenceShapes();

        // Fit map to show all markers
        fitMapToMarkers();
        
        console.log('✅ Map initialization complete');
        
        } catch (error) {
            console.error('❌ Error initializing map:', error);
            const mapEl = document.getElementById('map');
            if (mapEl) {
                mapEl.innerHTML = `<div class="alert alert-danger m-3"><h5>Map Initialization Error</h5><p>Failed to initialize the map: ${error.message}</p><button class="btn btn-primary" onclick="location.reload()">Reload Page</button></div>`;
            }
        }
    }    function addFarmMarkers() {
        farmsData.forEach(function(farm) {
            // Validate coordinates before creating marker
            if (!farm.latitude || !farm.longitude || 
                isNaN(farm.latitude) || isNaN(farm.longitude) ||
                farm.latitude === null || farm.longitude === null) {
                console.warn(`Skipping farm "${farm.name}" - invalid coordinates (${farm.latitude}, ${farm.longitude})`);
                return;
            }
            
            const farmMarker = new google.maps.Marker({
                position: { lat: parseFloat(farm.latitude), lng: parseFloat(farm.longitude) },
                map: map,
                title: farm.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="18" fill="#2563eb" stroke="white" stroke-width="3"/>
                            <text x="20" y="26" text-anchor="middle" fill="white" font-size="14" font-family="Arial">F</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(40, 40)
                }
            });farmMarker.addListener('click', function() {
                // Enhanced zoom-in functionality for farm clicks
                console.log('Farm clicked:', farm.name);
                
                // Show farm details panel
                showFarmDetails(farm);
                
                // Smooth zoom animation to farm location
                map.panTo({ lat: farm.latitude, lng: farm.longitude });
                
                // Set zoom level for farm overview (showing farm and nearby ponds)
                const targetZoom = 14;
                
                // Animate zoom smoothly
                const currentZoom = map.getZoom();
                if (currentZoom !== targetZoom) {
                    map.setZoom(targetZoom);
                }
            });

            farmMarkers.push(farmMarker);
        });
    }    function addPondMarkers() {
        pondsData.forEach(function(pond) {
            // Validate coordinates before creating marker
            if (!pond.latitude || !pond.longitude || 
                isNaN(pond.latitude) || isNaN(pond.longitude) ||
                pond.latitude === null || pond.longitude === null) {
                console.warn(`Skipping pond "${pond.name}" - invalid coordinates (${pond.latitude}, ${pond.longitude})`);
                return;
            }
            
            let pondColor = '#10b981'; // active - green
            if (pond.status === 'maintenance') {
                pondColor = '#f59e0b'; // maintenance - yellow
            } else if (pond.status === 'empty') {
                pondColor = '#6b7280'; // empty - gray
            } else if (pond.status === 'harvested') {
                pondColor = '#8b5cf6'; // harvested - purple
            }

            // Add pond marker
            const pondMarker = new google.maps.Marker({
                position: { lat: parseFloat(pond.latitude), lng: parseFloat(pond.longitude) },
                map: map,
                title: pond.name,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="15" cy="15" r="12" fill="${pondColor}" stroke="white" stroke-width="2"/>
                            <text x="15" y="20" text-anchor="middle" fill="white" font-size="10" font-family="Arial">P</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(30, 30)
                }
            });pondMarker.addListener('click', function() {
                // Enhanced zoom-in functionality for pond clicks
                console.log('Pond clicked:', pond.name);
                
                // Show pond details panel
                showPondDetails(pond);
                
                // Smooth zoom animation to pond location
                map.panTo({ lat: pond.latitude, lng: pond.longitude });
                
                // Set zoom level based on pond boundaries or default to close-up view
                let targetZoom = pond.boundary && pond.boundary.length > 0 ? 18 : 17;
                
                // Animate zoom smoothly
                const currentZoom = map.getZoom();
                if (currentZoom < targetZoom) {
                    let zoomLevel = currentZoom;
                    const zoomInterval = setInterval(() => {
                        zoomLevel += 1;
                        map.setZoom(zoomLevel);
                        if (zoomLevel >= targetZoom) {
                            clearInterval(zoomInterval);
                        }
                    }, 100);
                } else {
                    map.setZoom(targetZoom);
                }
                
                // Highlight the clicked pond temporarily
                highlightPond(pondMarker, pond);
            });

            pondMarkers.push(pondMarker);

            // Add pond boundary if available
            if (pond.boundary && pond.boundary.length > 0) {
                const polygon = new google.maps.Polygon({
                    paths: pond.boundary.map(coord => ({ lat: coord.lat, lng: coord.lng })),
                    strokeColor: pondColor,
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: pondColor,
                    fillOpacity: 0.2,
                    map: map
                });                polygon.addListener('click', function() {
                    // Enhanced zoom-in functionality for pond boundary clicks
                    console.log('Pond boundary clicked:', pond.name);
                    
                    // Show pond details panel
                    showPondDetails(pond);
                    
                    // Smooth zoom animation to pond location
                    map.panTo({ lat: pond.latitude, lng: pond.longitude });
                    
                    // Zoom to fit the pond boundary perfectly
                    const bounds = new google.maps.LatLngBounds();
                    pond.boundary.forEach(coord => {
                        bounds.extend(new google.maps.LatLng(coord.lat, coord.lng));
                    });
                    
                    // Fit bounds with padding for better view
                    map.fitBounds(bounds, {
                        top: 50,
                        right: 50,
                        bottom: 50,
                        left: 50
                    });
                    
                    // Ensure minimum zoom level for detail
                    setTimeout(() => {
                        if (map.getZoom() > 19) map.setZoom(19);
                        if (map.getZoom() < 16) map.setZoom(16);
                    }, 500);
                });

                pondPolygons.push(polygon);
            }
        });    }

    function addWorkerMarkers() {
        workersData.forEach(function(worker) {
            // Create worker marker with purple color
            const workerMarker = new google.maps.Marker({
                position: { lat: worker.latitude, lng: worker.longitude },
                map: map,
                title: `${worker.name} - ${worker.role}`,
                icon: {
                    url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                        <svg width="35" height="35" viewBox="0 0 35 35" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="17.5" cy="17.5" r="15" fill="#8B5CF6" stroke="white" stroke-width="3"/>
                            <text x="17.5" y="22" text-anchor="middle" fill="white" font-size="12" font-family="Arial" font-weight="bold">W</text>
                        </svg>
                    `),
                    scaledSize: new google.maps.Size(35, 35)
                }
            });

            workerMarker.addListener('click', function() {
                // Show worker details and zoom in
                console.log('Worker clicked:', worker.name);
                showWorkerDetails(worker);
                map.panTo({ lat: worker.latitude, lng: worker.longitude });
                map.setZoom(16);
            });

            workerMarkers.push(workerMarker);
        });
    }

    function addGeofenceShapes() {
        geofencesData.forEach(function(geofence) {
            let geofenceShape = null;
            
            // Determine color based on geofence type
            let fillColor = '#F59E0B';
            let strokeColor = '#D97706';
            
            if (geofence.type === 'restricted') {
                fillColor = '#EF4444';
                strokeColor = '#DC2626';
            } else if (geofence.type === 'pond') {
                fillColor = '#10B981';
                strokeColor = '#059669';
            } else if (geofence.type === 'warehouse') {
                fillColor = '#6366F1';
                strokeColor = '#4F46E5';
            }

            if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude && geofence.radius) {
                // Create circular geofence
                geofenceShape = new google.maps.Circle({
                    strokeColor: strokeColor,
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: fillColor,
                    fillOpacity: 0.2,
                    map: map,
                    center: { lat: geofence.center_latitude, lng: geofence.center_longitude },
                    radius: geofence.radius
                });
            } else if (geofence.boundary && geofence.boundary.length > 0) {
                // Create polygon geofence
                const paths = geofence.boundary.map(coord => ({
                    lat: parseFloat(coord.lat || coord.latitude),
                    lng: parseFloat(coord.lng || coord.longitude)
                }));

                geofenceShape = new google.maps.Polygon({
                    paths: paths,
                    strokeColor: strokeColor,
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: fillColor,
                    fillOpacity: 0.2,
                    map: map
                });
            }

            if (geofenceShape) {
                geofenceShape.addListener('click', function() {
                    showGeofenceDetails(geofence);
                });

                geofenceShapes.push({
                    shape: geofenceShape,
                    data: geofence
                });
            }
        });
    }

    function showFarmDetails(farm) {
        const title = `<i class="fas fa-industry"></i> ${farm.name}`;
        const content = `
            <div class="detail-item">
                <span class="detail-label">Location:</span>
                <span class="detail-value">${farm.location}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Total Ponds:</span>
                <span class="detail-value">${farm.total_ponds}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Active Ponds:</span>
                <span class="detail-value">${farm.active_ponds}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Total Area:</span>
                <span class="detail-value">${farm.total_area.toFixed(1)} m²</span>
            </div>
            ${farm.contact_person ? `
            <div class="detail-item">
                <span class="detail-label">Contact Person:</span>
                <span class="detail-value">${farm.contact_person}</span>
            </div>` : ''}
            ${farm.contact_phone ? `
            <div class="detail-item">
                <span class="detail-label">Phone:</span>
                <span class="detail-value">${farm.contact_phone}</span>
            </div>` : ''}
            ${farm.weather ? `
            <div class="weather-widget">
                <div class="weather-header">
                    <span>Current Weather</span>
                    <i class="fas fa-cloud-sun"></i>
                </div>
                <div class="weather-temp">${farm.weather.temperature}°C</div>
                <div class="weather-desc">${farm.weather.description}</div>
            </div>` : ''}
            <div class="action-buttons">
                <a href="/ponds/farms/${farm.id}/" class="action-btn primary">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="/ponds/farms/${farm.id}/update/" class="action-btn secondary">
                    <i class="fas fa-edit"></i> Edit Farm
                </a>
            </div>
        `;

        showDetailsPanel(title, content);
    }

    function showPondDetails(pond) {
        const title = `<i class="fas fa-water"></i> ${pond.name}`;
        const content = `
            <div class="detail-item">
                <span class="detail-label">Farm:</span>
                <span class="detail-value">${pond.farm_name}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="detail-value">
                    <span class="status-badge status-${pond.status}">${pond.status}</span>
                </span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Species:</span>
                <span class="detail-value">${pond.species}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Size:</span>
                <span class="detail-value">${pond.size} m²</span>
            </div>
            ${pond.length ? `
            <div class="detail-item">
                <span class="detail-label">Dimensions:</span>
                <span class="detail-value">${pond.length}m × ${pond.width}m</span>
            </div>` : ''}
            <div class="detail-item">
                <span class="detail-label">Water Quality:</span>
                <span class="detail-value">${pond.water_quality}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Aerators:</span>
                <span class="detail-value">${pond.active_aerators}/${pond.aerators_count} Active</span>
            </div>
            ${pond.shrimp_seed_count ? `
            <div class="detail-item">
                <span class="detail-label">Shrimp Seeds:</span>
                <span class="detail-value">${pond.shrimp_seed_count.toLocaleString()}</span>
            </div>` : ''}
            ${pond.weather ? `
            <div class="weather-widget">
                <div class="weather-header">
                    <span>Current Weather</span>
                    <i class="fas fa-cloud-sun"></i>
                </div>
                <div class="weather-temp">${pond.weather.temperature}°C</div>
                <div class="weather-desc">${pond.weather.description}</div>
            </div>` : ''}
            <div class="action-buttons">
                <a href="/ponds/${pond.id}/" class="action-btn primary">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="/ponds/${pond.id}/aerator-map/" class="action-btn secondary">
                    <i class="fas fa-map"></i> Aerator Map
                </a>
            </div>
        `;        showDetailsPanel(title, content);
    }    function showWorkerDetails(worker) {
        const title = `<i class="fas fa-user"></i> ${worker.name}`;
        const content = `
            <div class="detail-item">
                <span class="detail-label">Employee ID:</span>
                <span class="detail-value">${worker.employee_id || 'Not assigned'}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Team:</span>
                <span class="detail-value">${worker.team || 'Not assigned'}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="detail-value">
                    <span class="status-badge status-${worker.status}">${worker.status}</span>
                </span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Skill Level:</span>
                <span class="detail-value">${worker.skill_level}</span>
            </div>
            ${worker.current_task ? `
            <div class="detail-item">
                <span class="detail-label">Current Task:</span>
                <span class="detail-value">${worker.current_task}</span>
            </div>` : ''}
            ${worker.task_location ? `
            <div class="detail-item">
                <span class="detail-label">Task Location:</span>
                <span class="detail-value">${worker.task_location}</span>
            </div>` : ''}
            <div class="detail-item">
                <span class="detail-label">Location:</span>
                <span class="detail-value">${worker.latitude.toFixed(6)}, ${worker.longitude.toFixed(6)}</span>
            </div>
            ${worker.phone ? `
            <div class="detail-item">
                <span class="detail-label">Phone:</span>
                <span class="detail-value">${worker.phone}</span>
            </div>` : ''}
            ${worker.email ? `
            <div class="detail-item">
                <span class="detail-label">Email:</span>
                <span class="detail-value">${worker.email}</span>
            </div>` : ''}
            <div class="action-buttons">
                <a href="/labor/workers/${worker.id}/" class="action-btn primary">
                    <i class="fas fa-eye"></i> View Profile
                </a>
                <a href="/labor/workers/${worker.id}/location-history/" class="action-btn secondary">
                    <i class="fas fa-map-marked-alt"></i> Location History
                </a>
            </div>
        `;

        showDetailsPanel(title, content);
    }

    function showGeofenceDetails(geofence) {
        const title = `<i class="fas fa-map-marked-alt"></i> ${geofence.name}`;
        const content = `
            <div class="detail-item">
                <span class="detail-label">Type:</span>
                <span class="detail-value">
                    <span class="status-badge status-${geofence.type}">${geofence.type}</span>
                </span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Description:</span>
                <span class="detail-value">${geofence.description || 'No description available'}</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Shape:</span>
                <span class="detail-value">${geofence.shape_type}</span>
            </div>
            ${geofence.shape_type === 'circle' ? `
            <div class="detail-item">
                <span class="detail-label">Radius:</span>
                <span class="detail-value">${geofence.radius}m</span>
            </div>
            <div class="detail-item">
                <span class="detail-label">Center:</span>
                <span class="detail-value">${geofence.center_latitude.toFixed(6)}, ${geofence.center_longitude.toFixed(6)}</span>
            </div>` : `
            <div class="detail-item">
                <span class="detail-label">Area:</span>
                <span class="detail-value">${geofence.boundary ? geofence.boundary.length : 0} points</span>
            </div>`}
            ${geofence.is_active !== undefined ? `
            <div class="detail-item">
                <span class="detail-label">Status:</span>
                <span class="detail-value">
                    <span class="status-badge ${geofence.is_active ? 'status-active' : 'status-inactive'}">
                        ${geofence.is_active ? 'Active' : 'Inactive'}
                    </span>
                </span>
            </div>` : ''}
            <div class="action-buttons">
                <a href="/labor/geofences/${geofence.id}/" class="action-btn primary">
                    <i class="fas fa-eye"></i> View Details
                </a>
                <a href="/labor/geofences/${geofence.id}/events/" class="action-btn secondary">
                    <i class="fas fa-history"></i> Event History
                </a>
            </div>
        `;

        showDetailsPanel(title, content);
    }

    function showDetailsPanel(title, content) {
        document.getElementById('detailsTitle').innerHTML = title;
        document.getElementById('detailsContent').innerHTML = content;
        document.getElementById('overlay').classList.add('show');
        document.getElementById('detailsPanel').classList.add('show');
    }    function closeDetailsPanel() {
        document.getElementById('overlay').classList.remove('show');
        document.getElementById('detailsPanel').classList.remove('show');
    }

    function highlightPond(marker, pond) {
        // Create a temporary highlight effect for the clicked pond
        const originalIcon = marker.getIcon();
        
        // Create a larger, highlighted version of the marker
        const highlightIcon = {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                <svg width="45" height="45" viewBox="0 0 45 45" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="22.5" cy="22.5" r="20" fill="#FFD700" stroke="white" stroke-width="3" opacity="0.8"/>
                    <circle cx="22.5" cy="22.5" r="15" fill="${getPondColor(pond.status)}" stroke="white" stroke-width="2"/>
                    <text x="22.5" y="28" text-anchor="middle" fill="white" font-size="12" font-family="Arial" font-weight="bold">P</text>
                </svg>
            `),
            scaledSize: new google.maps.Size(45, 45),
            anchor: new google.maps.Point(22.5, 22.5)
        };
        
        // Apply highlight effect
        marker.setIcon(highlightIcon);
        
        // Create a pulsing circle around the pond
        const pulseCircle = new google.maps.Circle({
            strokeColor: '#FFD700',
            strokeOpacity: 0.8,
            strokeWeight: 3,
            fillColor: '#FFD700',
            fillOpacity: 0.1,
            map: map,
            center: { lat: pond.latitude, lng: pond.longitude },
            radius: 50 // 50 meters radius
        });
        
        // Animate the pulse effect
        let radius = 50;
        let growing = true;
        const pulseInterval = setInterval(() => {
            if (growing) {
                radius += 5;
                if (radius >= 100) growing = false;
            } else {
                radius -= 5;
                if (radius <= 50) growing = true;
            }
            pulseCircle.setRadius(radius);
        }, 100);
        
        // Remove highlight after 3 seconds        setTimeout(() => {
            marker.setIcon(originalIcon);
            pulseCircle.setMap(null);
            clearInterval(pulseInterval);
        }, 3000);
    }

    function resetMapView() {
        // Reset to show all layers
        visibleLayers = ['farms', 'ponds', 'workers', 'geofences'];
        
        // Update button states
        document.querySelectorAll('.map-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById('showAllBtn').classList.add('active');
        
        // Show all markers and shapes
        farmMarkers.forEach(marker => marker.setVisible(true));
        pondMarkers.forEach(marker => marker.setVisible(true));
        pondPolygons.forEach(polygon => polygon.setVisible(true));
        workerMarkers.forEach(marker => marker.setVisible(true));
        geofenceShapes.forEach(geofenceData => geofenceData.shape.setVisible(true));
        
        // Fit map to show all markers
        fitMapToMarkers();
    }

    function getPondColor(status) {
        switch(status) {
            case 'active': return '#10b981';
            case 'maintenance': return '#f59e0b';
            case 'empty': return '#6b7280';
            case 'harvested': return '#8b5cf6';
            default: return '#10b981';
        }
    }

    function toggleLayer(layer) {
        // Update button states
        document.querySelectorAll('.map-btn').forEach(btn => btn.classList.remove('active'));
        
        if (layer === 'all') {
            visibleLayers = ['farms', 'ponds', 'workers', 'geofences'];
            document.getElementById('showAllBtn').classList.add('active');
        } else if (layer === 'farms') {
            visibleLayers = ['farms'];
            document.getElementById('showFarmsBtn').classList.add('active');
        } else if (layer === 'ponds') {
            visibleLayers = ['ponds'];
            document.getElementById('showPondsBtn').classList.add('active');
        } else if (layer === 'workers') {
            visibleLayers = ['workers'];
            document.getElementById('showWorkersBtn').classList.add('active');
        } else if (layer === 'geofences') {
            visibleLayers = ['geofences'];
            document.getElementById('showGeofencesBtn').classList.add('active');
        }

        // Show/hide farm markers
        farmMarkers.forEach(marker => {
            marker.setVisible(visibleLayers.includes('farms'));
        });

        // Show/hide pond markers and polygons
        pondMarkers.forEach(marker => {
            marker.setVisible(visibleLayers.includes('ponds'));
        });

        pondPolygons.forEach(polygon => {
            polygon.setVisible(visibleLayers.includes('ponds'));
        });

        // Show/hide worker markers
        workerMarkers.forEach(marker => {
            marker.setVisible(visibleLayers.includes('workers'));
        });

        // Show/hide geofence shapes
        geofenceShapes.forEach(geofenceData => {
            geofenceData.shape.setVisible(visibleLayers.includes('geofences'));
        });
    }

    function toggleMapType() {
        currentMapType = currentMapType === 'roadmap' ? 'hybrid' : 'roadmap';
        map.setMapTypeId(currentMapType);
        
        const btn = document.getElementById('satelliteBtn');
        btn.innerHTML = currentMapType === 'hybrid' ? 
            '<i class="fas fa-map"></i> Road' : 
            '<i class="fas fa-satellite"></i> Satellite';    }

    function fitMapToMarkers() {
        if (farmMarkers.length === 0 && pondMarkers.length === 0 && workerMarkers.length === 0 && geofenceShapes.length === 0) return;

        const bounds = new google.maps.LatLngBounds();
        
        farmMarkers.forEach(marker => {
            bounds.extend(marker.getPosition());
        });
        
        pondMarkers.forEach(marker => {
            bounds.extend(marker.getPosition());
        });

        // Include worker markers in bounds
        workerMarkers.forEach(marker => {
            bounds.extend(marker.getPosition());
        });

        // Include geofence shapes in bounds
        geofenceShapes.forEach(geofenceData => {
            const geofence = geofenceData.data;
            if (geofence.shape_type === 'circle' && geofence.center_latitude && geofence.center_longitude) {
                bounds.extend(new google.maps.LatLng(geofence.center_latitude, geofence.center_longitude));
            } else if (geofence.boundary && geofence.boundary.length > 0) {
                geofence.boundary.forEach(coord => {
                    bounds.extend(new google.maps.LatLng(
                        parseFloat(coord.lat || coord.latitude),
                        parseFloat(coord.lng || coord.longitude)
                    ));
                });
            }
        });        map.fitBounds(bounds);
        
        // Ensure minimum zoom level
        const maxZoom = 15;
        const listener = google.maps.event.addListener(map, 'idle', function() {
            if (map.getZoom() > maxZoom) map.setZoom(maxZoom);
            google.maps.event.removeListener(listener);
        });
    }

    // Error handling for Google Maps
    window.gm_authFailure = function() {
        console.error('Google Maps authentication failed');
        document.getElementById('map').innerHTML = 
            '<div class="alert alert-danger m-3">Google Maps failed to load. Please check your API key.</div>';
    };

    // Timeout check for Google Maps loading
    let mapLoadTimeout = setTimeout(() => {
        if (typeof google === 'undefined') {
            console.error('Google Maps failed to load within 10 seconds');
            document.getElementById('map').innerHTML = 
                '<div class="alert alert-warning m-3">Google Maps is taking too long to load. Please refresh the page.</div>';
        }
    }, 10000);

    // Clear timeout when map loads
    window.addEventListener('load', () => {
        if (typeof google !== 'undefined') {
            clearTimeout(mapLoadTimeout);
        }
    });

    // Close panel with Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeDetailsPanel();
        }
    });
</script>

<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="console.error('Failed to load Google Maps script'); document.getElementById('map').innerHTML='<div class=&quot;alert alert-danger m-3&quot;>Failed to load Google Maps script. Check network connection.</div>';">
</script>
{% endblock %}
