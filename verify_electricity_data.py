#!/usr/bin/env python
"""
Verify electricity module data and functionality
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

from enhancements.modules.electricity.models import PowerEquipment, PowerConsumptionRecord

def verify_data():
    """Verify that the electricity data was created successfully"""
    print("=== Electricity Module Data Verification ===")
    
    # Check equipment count
    equipment_count = PowerEquipment.objects.count()
    print(f"✓ Power Equipment: {equipment_count} items")
    
    # List all equipment
    for equipment in PowerEquipment.objects.all():
        print(f"  - {equipment.name} ({equipment.device_id}) - {equipment.rated_power_kw}kW")
    
    # Check consumption records
    consumption_count = PowerConsumptionRecord.objects.count()
    print(f"✓ Power Consumption Records: {consumption_count} records")
    
    # Check latest records
    latest_records = PowerConsumptionRecord.objects.order_by('-timestamp')[:5]
    print("\nLatest 5 consumption records:")
    for record in latest_records:
        print(f"  - {record.equipment.name}: {record.active_power_kw:.2f}kW at {record.timestamp}")
    
    # Test electricity service
    try:
        from enhancements.modules.electricity.electricity_service import ElectricityManagementService
        service = ElectricityManagementService()
        print("\n✓ Electricity Service: Initialized successfully")
        
        # Get summary
        summary = service.get_dashboard_summary()
        print(f"✓ Dashboard Summary: {len(summary)} data points")
        
    except Exception as e:
        print(f"⚠ Electricity Service Error: {e}")
    
    print("\n=== Verification Complete ===")
    print(f"📊 Total Equipment: {equipment_count}")
    print(f"📈 Total Records: {consumption_count}")
    print("🎉 Electricity module is ready for testing!")

if __name__ == '__main__':
    try:
        verify_data()
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        import traceback
        traceback.print_exc()
