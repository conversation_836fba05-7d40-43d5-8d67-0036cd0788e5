#!/usr/bin/env python
"""
Test Django model imports to identify syntax errors
"""
import os
import sys

# Add the project directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')

try:
    import django
    django.setup()
    print("✅ Django setup successful")
    
    # Test individual model imports
    try:
        from core.models.water_quality import Alert, AlertSetting
        print("✅ Core water quality models imported successfully")
    except Exception as e:
        print(f"❌ Error importing core water quality models: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        from core.models.feed import FeedType, FeedInventory
        print("✅ Core feed models imported successfully")
    except Exception as e:
        print(f"❌ Error importing core feed models: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        from digital_twin_api.models import WeatherAlertHistory
        print("✅ Digital twin models imported successfully")
    except Exception as e:
        print(f"❌ Error importing digital twin models: {e}")
        import traceback
        traceback.print_exc()
    
    try:
        from labor.models import SafetyAlert
        print("✅ Labor models imported successfully")
    except Exception as e:
        print(f"❌ Error importing labor models: {e}")
        import traceback
        traceback.print_exc()
        
except Exception as e:
    print(f"❌ Django setup error: {e}")
    import traceback
    traceback.print_exc()
