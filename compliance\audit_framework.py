"""
Comprehensive Compliance and Audit Framework
Implements audit logging, compliance monitoring, and regulatory reporting
"""

import json
import logging
import hashlib
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from django.db import models, connection
from django.core.cache import cache
from django.conf import settings
from django.utils import timezone
from enum import Enum
import threading
import uuid

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    USER_CREATED = "user_created"
    USER_MODIFIED = "user_modified"
    USER_DELETED = "user_deleted"
    DATA_ACCESS = "data_access"
    DATA_CREATED = "data_created"
    DATA_MODIFIED = "data_modified"
    DATA_DELETED = "data_deleted"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_REVOKED = "permission_revoked"
    SYSTEM_CONFIG_CHANGED = "system_config_changed"
    BACKUP_CREATED = "backup_created"
    BACKUP_RESTORED = "backup_restored"
    SECURITY_VIOLATION = "security_violation"
    COMPLIANCE_CHECK = "compliance_check"

class ComplianceStandard(Enum):
    GDPR = "gdpr"
    SOX = "sox"
    HIPAA = "hipaa"
    ISO27001 = "iso27001"
    PCI_DSS = "pci_dss"
    CUSTOM = "custom"

class AuditSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class AuditEvent:
    """Audit event record"""
    event_id: str
    event_type: AuditEventType
    timestamp: datetime
    user_id: Optional[str]
    tenant_id: Optional[str]
    session_id: Optional[str]
    ip_address: str
    user_agent: str
    resource: str
    action: str
    outcome: str  # success, failure, error
    severity: AuditSeverity
    details: Dict[str, Any]
    compliance_tags: List[str]
    data_classification: str
    retention_period: int  # days
    
    def __post_init__(self):
        if isinstance(self.event_type, str):
            self.event_type = AuditEventType(self.event_type)
        if isinstance(self.severity, str):
            self.severity = AuditSeverity(self.severity)

@dataclass
class ComplianceRule:
    """Compliance rule definition"""
    rule_id: str
    name: str
    description: str
    standard: ComplianceStandard
    category: str
    severity: AuditSeverity
    conditions: Dict[str, Any]
    actions: List[str]
    enabled: bool
    last_checked: Optional[datetime] = None
    
    def __post_init__(self):
        if isinstance(self.standard, str):
            self.standard = ComplianceStandard(self.standard)
        if isinstance(self.severity, str):
            self.severity = AuditSeverity(self.severity)

class AuditFramework:
    """
    Comprehensive Audit and Compliance Framework
    """
    
    def __init__(self):
        self.audit_events: List[AuditEvent] = []
        self.compliance_rules: Dict[str, ComplianceRule] = {}
        self.compliance_violations: List[Dict[str, Any]] = []
        self.data_retention_policies: Dict[str, int] = {}
        self.audit_lock = threading.Lock()
        
        # Setup compliance rules
        self._setup_compliance_rules()
        
        # Setup data retention policies
        self._setup_retention_policies()
        
        # Initialize audit storage
        self._initialize_audit_storage()
    
    def _setup_compliance_rules(self):
        """Setup compliance monitoring rules"""
        rules = {
            "gdpr_data_access": ComplianceRule(
                rule_id="gdpr_data_access",
                name="GDPR Data Access Monitoring",
                description="Monitor access to personal data for GDPR compliance",
                standard=ComplianceStandard.GDPR,
                category="data_protection",
                severity=AuditSeverity.HIGH,
                conditions={
                    "event_types": ["data_access", "data_modified"],
                    "data_classification": ["personal", "sensitive"],
                    "monitor_frequency": "real_time"
                },
                actions=["log_event", "notify_dpo", "check_consent"],
                enabled=True
            ),
            
            "sox_financial_data": ComplianceRule(
                rule_id="sox_financial_data",
                name="SOX Financial Data Controls",
                description="Monitor access to financial data for SOX compliance",
                standard=ComplianceStandard.SOX,
                category="financial_controls",
                severity=AuditSeverity.CRITICAL,
                conditions={
                    "event_types": ["data_access", "data_modified", "data_deleted"],
                    "resources": ["financial/*", "billing/*", "revenue/*"],
                    "require_approval": True
                },
                actions=["log_event", "require_dual_approval", "notify_auditor"],
                enabled=True
            ),
            
            "iso27001_security": ComplianceRule(
                rule_id="iso27001_security",
                name="ISO 27001 Security Monitoring",
                description="Monitor security events for ISO 27001 compliance",
                standard=ComplianceStandard.ISO27001,
                category="information_security",
                severity=AuditSeverity.HIGH,
                conditions={
                    "event_types": ["security_violation", "permission_granted", "permission_revoked"],
                    "monitor_all_users": True
                },
                actions=["log_event", "security_review", "incident_response"],
                enabled=True
            ),
            
            "data_retention": ComplianceRule(
                rule_id="data_retention",
                name="Data Retention Compliance",
                description="Ensure data retention policies are followed",
                standard=ComplianceStandard.CUSTOM,
                category="data_lifecycle",
                severity=AuditSeverity.MEDIUM,
                conditions={
                    "check_frequency": "daily",
                    "retention_policies": True
                },
                actions=["check_retention", "archive_data", "purge_expired"],
                enabled=True
            )
        }
        
        self.compliance_rules.update(rules)
        logger.info(f"Configured {len(rules)} compliance rules")
    
    def _setup_retention_policies(self):
        """Setup data retention policies"""
        self.data_retention_policies = {
            "audit_events": 2555,  # 7 years for audit logs
            "user_activity": 1095,  # 3 years for user activity
            "financial_data": 2555,  # 7 years for financial records
            "personal_data": 1095,  # 3 years for personal data (GDPR)
            "system_logs": 365,  # 1 year for system logs
            "security_events": 1825,  # 5 years for security events
            "backup_metadata": 365,  # 1 year for backup metadata
            "compliance_reports": 2555  # 7 years for compliance reports
        }
    
    def _initialize_audit_storage(self):
        """Initialize audit event storage"""
        # In production, this would setup database tables or external audit systems
        # For now, use in-memory storage with periodic persistence
        pass
    
    def log_audit_event(self, event_type: AuditEventType, user_id: Optional[str],
                       tenant_id: Optional[str], resource: str, action: str,
                       outcome: str, details: Dict[str, Any],
                       request_context: Optional[Dict[str, Any]] = None) -> str:
        """Log audit event with compliance checking"""
        
        event_id = str(uuid.uuid4())
        
        # Extract request context
        if request_context is None:
            request_context = {}
        
        ip_address = request_context.get('ip_address', 'unknown')
        user_agent = request_context.get('user_agent', 'unknown')
        session_id = request_context.get('session_id', '')
        
        # Determine data classification
        data_classification = self._classify_data(resource, details)
        
        # Determine severity
        severity = self._determine_severity(event_type, outcome, data_classification)
        
        # Get compliance tags
        compliance_tags = self._get_compliance_tags(event_type, resource, data_classification)
        
        # Determine retention period
        retention_period = self._get_retention_period(event_type, data_classification)
        
        # Create audit event
        audit_event = AuditEvent(
            event_id=event_id,
            event_type=event_type,
            timestamp=datetime.now(),
            user_id=user_id,
            tenant_id=tenant_id,
            session_id=session_id,
            ip_address=ip_address,
            user_agent=user_agent,
            resource=resource,
            action=action,
            outcome=outcome,
            severity=severity,
            details=details,
            compliance_tags=compliance_tags,
            data_classification=data_classification,
            retention_period=retention_period
        )
        
        # Store audit event
        with self.audit_lock:
            self.audit_events.append(audit_event)
            
            # Persist to storage
            self._persist_audit_event(audit_event)
            
            # Check compliance rules
            self._check_compliance_rules(audit_event)
        
        logger.info(f"Audit event logged: {event_id} - {event_type.value}")
        return event_id
    
    def _classify_data(self, resource: str, details: Dict[str, Any]) -> str:
        """Classify data based on resource and content"""
        # Check for personal data indicators
        personal_indicators = ['email', 'phone', 'address', 'name', 'ssn', 'id_number']
        if any(indicator in str(details).lower() for indicator in personal_indicators):
            return "personal"
        
        # Check for financial data
        financial_indicators = ['payment', 'billing', 'revenue', 'cost', 'price']
        if any(indicator in resource.lower() for indicator in financial_indicators):
            return "financial"
        
        # Check for sensitive operational data
        sensitive_indicators = ['password', 'secret', 'key', 'token', 'credential']
        if any(indicator in str(details).lower() for indicator in sensitive_indicators):
            return "sensitive"
        
        return "general"
    
    def _determine_severity(self, event_type: AuditEventType, outcome: str, 
                          data_classification: str) -> AuditSeverity:
        """Determine event severity"""
        # Critical events
        if event_type in [AuditEventType.SECURITY_VIOLATION, AuditEventType.USER_DELETED]:
            return AuditSeverity.CRITICAL
        
        # High severity for sensitive data or failures
        if data_classification in ["personal", "financial", "sensitive"] or outcome == "failure":
            return AuditSeverity.HIGH
        
        # Medium severity for data modifications
        if event_type in [AuditEventType.DATA_MODIFIED, AuditEventType.DATA_DELETED]:
            return AuditSeverity.MEDIUM
        
        return AuditSeverity.LOW
    
    def _get_compliance_tags(self, event_type: AuditEventType, resource: str, 
                           data_classification: str) -> List[str]:
        """Get compliance tags for event"""
        tags = []
        
        # GDPR tags
        if data_classification == "personal":
            tags.append("gdpr")
        
        # SOX tags
        if "financial" in resource.lower() or data_classification == "financial":
            tags.append("sox")
        
        # ISO 27001 tags
        if event_type in [AuditEventType.SECURITY_VIOLATION, AuditEventType.PERMISSION_GRANTED]:
            tags.append("iso27001")
        
        # Data retention tags
        tags.append("retention")
        
        return tags
    
    def _get_retention_period(self, event_type: AuditEventType, data_classification: str) -> int:
        """Get retention period for event"""
        if data_classification == "financial":
            return self.data_retention_policies["financial_data"]
        elif data_classification == "personal":
            return self.data_retention_policies["personal_data"]
        elif event_type == AuditEventType.SECURITY_VIOLATION:
            return self.data_retention_policies["security_events"]
        else:
            return self.data_retention_policies["audit_events"]
    
    def _persist_audit_event(self, event: AuditEvent):
        """Persist audit event to storage"""
        try:
            # In production, store in dedicated audit database or SIEM
            # For now, use cache with long TTL
            cache_key = f"audit_event:{event.event_id}"
            cache.set(cache_key, asdict(event), event.retention_period * 86400)
            
            # Also append to audit log file
            audit_log_file = getattr(settings, 'AUDIT_LOG_FILE', '/var/log/shrimp_farm_audit.log')
            
            log_entry = {
                "timestamp": event.timestamp.isoformat(),
                "event_id": event.event_id,
                "event_type": event.event_type.value,
                "user_id": event.user_id,
                "tenant_id": event.tenant_id,
                "resource": event.resource,
                "action": event.action,
                "outcome": event.outcome,
                "severity": event.severity.value,
                "ip_address": event.ip_address,
                "compliance_tags": event.compliance_tags,
                "data_classification": event.data_classification
            }
            
            # In production, use proper audit logging
            logger.info(f"AUDIT: {json.dumps(log_entry)}")
            
        except Exception as e:
            logger.error(f"Failed to persist audit event: {e}")
    
    def _check_compliance_rules(self, event: AuditEvent):
        """Check event against compliance rules"""
        for rule_id, rule in self.compliance_rules.items():
            if not rule.enabled:
                continue
            
            try:
                if self._event_matches_rule(event, rule):
                    self._execute_compliance_actions(event, rule)
                    
            except Exception as e:
                logger.error(f"Compliance rule check failed for {rule_id}: {e}")
    
    def _event_matches_rule(self, event: AuditEvent, rule: ComplianceRule) -> bool:
        """Check if event matches compliance rule conditions"""
        conditions = rule.conditions
        
        # Check event types
        if "event_types" in conditions:
            if event.event_type.value not in conditions["event_types"]:
                return False
        
        # Check data classification
        if "data_classification" in conditions:
            if event.data_classification not in conditions["data_classification"]:
                return False
        
        # Check resources
        if "resources" in conditions:
            resource_patterns = conditions["resources"]
            if not any(pattern in event.resource for pattern in resource_patterns):
                return False
        
        # Check compliance tags
        if rule.standard.value in event.compliance_tags:
            return True
        
        return True
    
    def _execute_compliance_actions(self, event: AuditEvent, rule: ComplianceRule):
        """Execute compliance rule actions"""
        for action in rule.actions:
            try:
                if action == "log_event":
                    self._log_compliance_event(event, rule)
                elif action == "notify_dpo":
                    self._notify_data_protection_officer(event, rule)
                elif action == "notify_auditor":
                    self._notify_auditor(event, rule)
                elif action == "require_dual_approval":
                    self._require_dual_approval(event, rule)
                elif action == "security_review":
                    self._trigger_security_review(event, rule)
                elif action == "incident_response":
                    self._trigger_incident_response(event, rule)
                    
            except Exception as e:
                logger.error(f"Compliance action failed: {action} - {e}")
    
    def _log_compliance_event(self, event: AuditEvent, rule: ComplianceRule):
        """Log compliance-specific event"""
        compliance_event = {
            "timestamp": datetime.now().isoformat(),
            "rule_id": rule.rule_id,
            "rule_name": rule.name,
            "standard": rule.standard.value,
            "triggered_by": event.event_id,
            "severity": rule.severity.value,
            "user_id": event.user_id,
            "tenant_id": event.tenant_id,
            "resource": event.resource
        }
        
        self.compliance_violations.append(compliance_event)
        logger.warning(f"Compliance rule triggered: {rule.name}")
    
    def _notify_data_protection_officer(self, event: AuditEvent, rule: ComplianceRule):
        """Notify Data Protection Officer"""
        # In production, send actual notification
        logger.info(f"DPO notification: {rule.name} triggered by event {event.event_id}")
    
    def _notify_auditor(self, event: AuditEvent, rule: ComplianceRule):
        """Notify auditor"""
        # In production, send actual notification
        logger.info(f"Auditor notification: {rule.name} triggered by event {event.event_id}")
    
    def _require_dual_approval(self, event: AuditEvent, rule: ComplianceRule):
        """Require dual approval for action"""
        # In production, implement approval workflow
        logger.info(f"Dual approval required for event {event.event_id}")
    
    def _trigger_security_review(self, event: AuditEvent, rule: ComplianceRule):
        """Trigger security review"""
        # In production, create security review ticket
        logger.info(f"Security review triggered for event {event.event_id}")
    
    def _trigger_incident_response(self, event: AuditEvent, rule: ComplianceRule):
        """Trigger incident response"""
        # In production, activate incident response procedures
        logger.warning(f"Incident response triggered for event {event.event_id}")
    
    def search_audit_events(self, filters: Dict[str, Any], 
                          limit: int = 100) -> List[AuditEvent]:
        """Search audit events with filters"""
        results = []
        
        for event in self.audit_events:
            if self._event_matches_filters(event, filters):
                results.append(event)
                
                if len(results) >= limit:
                    break
        
        return results
    
    def _event_matches_filters(self, event: AuditEvent, filters: Dict[str, Any]) -> bool:
        """Check if event matches search filters"""
        # User ID filter
        if "user_id" in filters and event.user_id != filters["user_id"]:
            return False
        
        # Tenant ID filter
        if "tenant_id" in filters and event.tenant_id != filters["tenant_id"]:
            return False
        
        # Event type filter
        if "event_type" in filters and event.event_type.value != filters["event_type"]:
            return False
        
        # Date range filter
        if "start_date" in filters and event.timestamp < filters["start_date"]:
            return False
        
        if "end_date" in filters and event.timestamp > filters["end_date"]:
            return False
        
        # Resource filter
        if "resource" in filters and filters["resource"] not in event.resource:
            return False
        
        # Outcome filter
        if "outcome" in filters and event.outcome != filters["outcome"]:
            return False
        
        return True
    
    def generate_compliance_report(self, standard: ComplianceStandard,
                                 start_date: datetime, end_date: datetime,
                                 tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate compliance report for specific standard"""
        
        # Filter events for the period and standard
        relevant_events = [
            event for event in self.audit_events
            if (start_date <= event.timestamp <= end_date and
                standard.value in event.compliance_tags and
                (tenant_id is None or event.tenant_id == tenant_id))
        ]
        
        # Filter compliance violations
        relevant_violations = [
            violation for violation in self.compliance_violations
            if (violation["standard"] == standard.value and
                start_date <= datetime.fromisoformat(violation["timestamp"]) <= end_date and
                (tenant_id is None or violation["tenant_id"] == tenant_id))
        ]
        
        # Generate report
        report = {
            "standard": standard.value,
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat()
            },
            "tenant_id": tenant_id,
            "generated_at": datetime.now().isoformat(),
            "summary": {
                "total_events": len(relevant_events),
                "compliance_violations": len(relevant_violations),
                "compliance_score": self._calculate_compliance_score(relevant_events, relevant_violations)
            },
            "event_breakdown": self._analyze_events_by_type(relevant_events),
            "violations": relevant_violations,
            "recommendations": self._generate_compliance_recommendations(standard, relevant_violations)
        }
        
        return report
    
    def _calculate_compliance_score(self, events: List[AuditEvent], 
                                  violations: List[Dict[str, Any]]) -> float:
        """Calculate compliance score (0-100)"""
        if not events:
            return 100.0
        
        violation_weight = len(violations) * 10  # Each violation reduces score by 10
        total_events = len(events)
        
        # Calculate score based on violations vs total events
        score = max(0, 100 - (violation_weight / total_events * 100))
        return round(score, 2)
    
    def _analyze_events_by_type(self, events: List[AuditEvent]) -> Dict[str, int]:
        """Analyze events by type"""
        breakdown = {}
        
        for event in events:
            event_type = event.event_type.value
            breakdown[event_type] = breakdown.get(event_type, 0) + 1
        
        return breakdown
    
    def _generate_compliance_recommendations(self, standard: ComplianceStandard,
                                           violations: List[Dict[str, Any]]) -> List[str]:
        """Generate compliance recommendations"""
        recommendations = []
        
        if standard == ComplianceStandard.GDPR:
            if violations:
                recommendations.append("Review data access controls and implement additional consent mechanisms")
                recommendations.append("Conduct privacy impact assessment for high-risk data processing")
            recommendations.append("Implement automated data retention and deletion procedures")
            recommendations.append("Provide data subject rights management interface")
        
        elif standard == ComplianceStandard.SOX:
            if violations:
                recommendations.append("Strengthen financial data access controls")
                recommendations.append("Implement segregation of duties for financial processes")
            recommendations.append("Establish quarterly financial controls testing")
            recommendations.append("Document all financial process changes")
        
        elif standard == ComplianceStandard.ISO27001:
            if violations:
                recommendations.append("Review and update information security policies")
                recommendations.append("Conduct security awareness training")
            recommendations.append("Implement continuous security monitoring")
            recommendations.append("Perform regular security risk assessments")
        
        return recommendations
    
    def cleanup_expired_audit_data(self):
        """Clean up expired audit data based on retention policies"""
        try:
            current_time = datetime.now()
            expired_events = []
            
            for event in self.audit_events:
                expiry_date = event.timestamp + timedelta(days=event.retention_period)
                
                if current_time > expiry_date:
                    expired_events.append(event)
            
            # Remove expired events
            for event in expired_events:
                self.audit_events.remove(event)
                
                # Remove from cache
                cache_key = f"audit_event:{event.event_id}"
                cache.delete(cache_key)
            
            if expired_events:
                logger.info(f"Cleaned up {len(expired_events)} expired audit events")
                
        except Exception as e:
            logger.error(f"Audit data cleanup failed: {e}")
    
    def get_audit_dashboard(self) -> Dict[str, Any]:
        """Get audit framework dashboard data"""
        total_events = len(self.audit_events)
        recent_events = [e for e in self.audit_events if e.timestamp > datetime.now() - timedelta(days=7)]
        
        severity_breakdown = {}
        for event in recent_events:
            severity = event.severity.value
            severity_breakdown[severity] = severity_breakdown.get(severity, 0) + 1
        
        return {
            "summary": {
                "total_events": total_events,
                "recent_events": len(recent_events),
                "compliance_violations": len(self.compliance_violations),
                "active_rules": len([r for r in self.compliance_rules.values() if r.enabled])
            },
            "severity_breakdown": severity_breakdown,
            "compliance_standards": list(set(r.standard.value for r in self.compliance_rules.values())),
            "recent_violations": self.compliance_violations[-10:],
            "retention_policies": self.data_retention_policies
        }

# Global audit framework instance
audit_framework = AuditFramework()
