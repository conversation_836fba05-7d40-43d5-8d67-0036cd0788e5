import requests
import json

try:
    # Test the public lunar calendar API
    response = requests.get('http://127.0.0.1:8000/lunar/api/calendar/public/')
    data = response.json()
    
    print(f"API Status: {response.status_code}")
    print(f"Total events found: {len(data['events'])}")
    
    # Filter for important lunar phases
    important_phases = [e for e in data['events'] if e['type'] in ['astami', 'navami', 'pournami', 'amavasya']]
    print(f"Important lunar phases found: {len(important_phases)}")
    
    print("\nUpcoming important lunar phases:")
    print("-" * 50)
    for phase in important_phases[:15]:  # Show first 15 events
        print(f"{phase['date']}: {phase['title']} ({phase['type']})")
        
    # Check for specific phases in next 3 months
    astami_count = len([e for e in important_phases if e['type'] == 'astami'])
    navami_count = len([e for e in important_phases if e['type'] == 'navami'])
    pournami_count = len([e for e in important_phases if e['type'] == 'pournami'])
    amavasya_count = len([e for e in important_phases if e['type'] == 'amavasya'])
    
    print(f"\nPhase counts:")
    print(f"Astami: {astami_count}")
    print(f"Navami: {navami_count}")
    print(f"Pournami: {pournami_count}")
    print(f"Amavasya: {amavasya_count}")
    
except Exception as e:
    print(f"Error testing API: {e}")
