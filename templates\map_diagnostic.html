<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 400px; width: 100%; border: 2px solid #ccc; margin: 20px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .log { background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 10px 0; border-radius: 5px; }
        code { background-color: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🗺️ Google Maps API Diagnostic Tool</h1>
    
    <div id="status-container">
        <div class="status info">
            <strong>Step 1:</strong> Loading Google Maps API...
        </div>
    </div>

    <div id="map"></div>

    <div class="log">
        <h3>Debug Log:</h3>
        <div id="debug-log"></div>
    </div>

    <script>
        const debugLog = document.getElementById('debug-log');
        const statusContainer = document.getElementById('status-container');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            debugLog.appendChild(logEntry);
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
            statusContainer.appendChild(statusDiv);
        }

        // Test 1: Check if Google Maps API script loads
        log('Starting Google Maps API diagnostic...');
        updateStatus('<strong>Step 1:</strong> Loading Google Maps API script...', 'info');

        // Global callback function
        window.initMap = function() {
            log('✅ Google Maps API loaded successfully!');
            updateStatus('<strong>Step 2:</strong> Google Maps API loaded successfully!', 'success');
            
            try {
                // Test 2: Try to create a map
                log('Attempting to create map instance...');
                updateStatus('<strong>Step 3:</strong> Creating map instance...', 'info');
                
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai coordinates
                    zoom: 10,
                    mapTypeId: 'roadmap'
                });

                log('✅ Map instance created successfully!');
                updateStatus('<strong>Step 4:</strong> Map instance created successfully!', 'success');

                // Test 3: Add a test marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai'
                });

                log('✅ Test marker added successfully!');
                updateStatus('<strong>Step 5:</strong> Test marker added successfully!', 'success');
                updateStatus('<strong>🎉 All tests passed!</strong> Google Maps API is working correctly.', 'success');

            } catch (error) {
                log(`❌ Error creating map: ${error.message}`);
                updateStatus(`<strong>❌ Error:</strong> Failed to create map - ${error.message}`, 'error');
            }
        };

        // Global error handler for Google Maps
        window.gm_authFailure = function() {
            log('❌ Google Maps authentication failed!');
            updateStatus('<strong>❌ Authentication Error:</strong> Google Maps API key is invalid or has restrictions.', 'error');
        };

        // Load the Google Maps API
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap';
        script.async = true;
        script.defer = true;
        
        script.onerror = function() {
            log('❌ Failed to load Google Maps API script');
            updateStatus('<strong>❌ Script Error:</strong> Failed to load Google Maps API script. Check network connectivity.', 'error');
        };

        script.onload = function() {
            log('Google Maps API script loaded, waiting for callback...');
        };

        document.head.appendChild(script);

        // Timeout check
        setTimeout(() => {
            if (typeof google === 'undefined') {
                log('❌ Google Maps API failed to load within 10 seconds');
                updateStatus('<strong>❌ Timeout Error:</strong> Google Maps API failed to load within 10 seconds.', 'error');
            }
        }, 10000);

        log(`Using API Key: {{ google_maps_api_key }}`);
    </script>
</body>
</html>
