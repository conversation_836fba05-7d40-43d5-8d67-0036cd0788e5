#!/usr/bin/env python
"""
Test script to verify Redis fix.
"""

import os
import sys
import django
from unittest.mock import patch

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

def test_redis_services():
    """Test that Redis services handle connection failures gracefully."""
    print("Testing Redis services...")
    
    try:
        from security_enhancements.services import (
            TwoFactorAuth, 
            GranularPermissions, 
            APIKeyManager, 
            EnhancedAuditLogger
        )
        
        print("✓ Successfully imported security services")
        
        # Test TwoFactorAuth
        print("\n1. Testing TwoFactorAuth...")
        tfa = TwoFactorAuth()
        print(f"   Redis client type: {type(tfa.redis_client)}")
        print("✓ TwoFactorAuth initialized without errors")
        
        # Test GranularPermissions
        print("\n2. Testing GranularPermissions...")
        gp = GranularPermissions()
        print(f"   Redis client type: {type(gp.redis_client)}")
        print("✓ GranularPermissions initialized without errors")
        
        # Test APIKeyManager
        print("\n3. Testing APIKeyManager...")
        akm = APIKeyManager()
        print(f"   Redis client type: {type(akm.redis_client)}")
        print("✓ APIKeyManager initialized without errors")
        
        # Test EnhancedAuditLogger
        print("\n4. Testing EnhancedAuditLogger...")
        eal = EnhancedAuditLogger()
        print(f"   Redis client type: {type(eal.redis_client)}")
        print("✓ EnhancedAuditLogger initialized without errors")
        
        # Test logging a security event
        print("\n5. Testing security event logging...")
        from security_enhancements.services import SecurityEvent
        from datetime import datetime
        
        event = SecurityEvent(
            event_type='test',
            user_id=1,
            ip_address='127.0.0.1',
            user_agent='test',
            resource='test',
            action='test',
            result='success',
            details={},
            timestamp=datetime.now(),
            risk_score=0
        )
        
        eal.log_security_event(event)
        print("✓ Security event logged without errors")
        
        print("\n🎉 All Redis services are working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing Redis services: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_deployment_service():
    """Test that ModelDeploymentService works without Redis."""
    print("\n" + "="*50)
    print("Testing ModelDeploymentService...")
    
    try:
        from enhancements.modules.predictive_analytics.model_deployment import ModelDeploymentService
        
        service = ModelDeploymentService()
        print("✓ ModelDeploymentService initialized")
        
        # Test getting deployment config
        config = service.get_deployment_config()
        print("✓ Got deployment config")
        print(f"   Config keys: {list(config.keys())}")
        
        print("\n🎉 ModelDeploymentService is working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing ModelDeploymentService: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_creation_and_login():
    """Test user creation and force login without Redis issues."""
    print("\n" + "="*50)
    print("Testing user creation and login...")
    
    try:
        from django.contrib.auth import get_user_model
        from django.test import Client
        
        User = get_user_model()
        
        # Create a test user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword'
        )
        print("✓ Test user created")
        
        # Test force login
        client = Client()
        client.force_login(user)
        print("✓ Force login successful")
        
        # Clean up
        user.delete()
        print("✓ Test user cleaned up")
        
        print("\n🎉 User creation and login working correctly!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error testing user creation and login: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting Redis Fix Verification Tests...")
    print("="*50)
    
    success1 = test_redis_services()
    success2 = test_model_deployment_service()
    success3 = test_user_creation_and_login()
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Redis fix is working correctly!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)
