from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from django.utils import timezone
from datetime import timedelta
from .models import WaterQualityReading, WaterQualityParameter
from .forms import WaterQualityReadingForm, WaterQualityFilterForm
from ponds.models import Pond
from alerts.models import Alert


@login_required
def water_quality_dashboard(request):
    """Main water quality dashboard view"""
    # Get all ponds
    ponds = Pond.objects.all()
    
    # Get selected pond or default to first pond
    selected_pond_id = request.GET.get('pond_id')
    if selected_pond_id:
        selected_pond = get_object_or_404(Pond, id=selected_pond_id)
    else:
        selected_pond = ponds.first()
    
    # Get filter parameters
    filter_form = WaterQualityFilterForm(request.GET)
    from_date = request.GET.get('from_date')
    to_date = request.GET.get('to_date')
    parameter = request.GET.get('parameter', 'all')
    
    # Get water quality readings for selected pond
    readings = WaterQualityReading.objects.filter(pond=selected_pond)
    
    # Apply date filters if provided
    if from_date:
        readings = readings.filter(timestamp__date__gte=from_date)
    if to_date:
        readings = readings.filter(timestamp__date__lte=to_date)
    
    # Get latest reading for the selected pond
    latest_reading = readings.first()
    
    # Get historical data for charts
    historical_data = readings.order_by('timestamp')
    
    # Get all historical data for pond comparison
    all_historical_data = WaterQualityReading.objects.filter(
        timestamp__gte=timezone.now() - timedelta(days=30)
    ).order_by('timestamp')
    
    context = {
        'ponds': ponds,
        'selected_pond': selected_pond,
        'latest_reading': latest_reading,
        'historical_data': historical_data,
        'all_historical_data': all_historical_data,
        'filter_form': filter_form,
        'parameter': parameter,
    }
    
    return render(request, 'water_quality/dashboard_modern.html', context)


@login_required
def add_reading(request):
    """Add a new water quality reading"""
    if request.method == 'POST':
        form = WaterQualityReadingForm(request.POST)
        if form.is_valid():
            reading = form.save()
            
            # Update pond water quality status
            reading.update_pond_water_quality()
            
            # Check for critical readings and create alerts if needed
            check_critical_readings(reading)
            
            messages.success(request, 'Water quality reading added successfully!')
            return redirect('water_quality:dashboard')
    else:
        # Pre-select pond if provided in URL
        pond_id = request.GET.get('pond_id')
        if pond_id:
            form = WaterQualityReadingForm(initial={'pond': pond_id})
        else:
            form = WaterQualityReadingForm()
    
    return render(request, 'water_quality/add_reading_modern.html', {'form': form})


def check_critical_readings(reading):
    """Check for critical readings and create alerts"""
    critical_params = []
    
    # Check each parameter
    if reading.get_temperature_status() == 'critical':
        critical_params.append(f"Temperature: {reading.temperature}°C")
    
    if reading.get_ph_status() == 'critical':
        critical_params.append(f"pH: {reading.ph}")
    
    if reading.get_oxygen_status() == 'critical':
        critical_params.append(f"Oxygen: {reading.oxygen} mg/L")
    
    if reading.get_salinity_status() == 'critical':
        critical_params.append(f"Salinity: {reading.salinity} ppt")
    
    if reading.get_ammonia_status() == 'critical':
        critical_params.append(f"Ammonia: {reading.ammonia} mg/L")
    
    # Create alert if any critical parameters
    if critical_params:
        Alert.objects.create(
            type='critical',
            title=f"Critical water quality in {reading.pond.name}",
            message=f"Critical readings detected: {', '.join(critical_params)}",
            read=False
        )
