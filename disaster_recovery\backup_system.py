"""
Comprehensive Disaster Recovery and Backup System
Implements automated backups, point-in-time recovery, and disaster recovery procedures
"""

import os
import subprocess
import boto3
import logging
import json
import gzip
import shutil
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from django.conf import settings
from django.core.management import call_command
from django.db import connection
import threading
import time
from enum import Enum

logger = logging.getLogger(__name__)

class BackupType(Enum):
    FULL = "full"
    INCREMENTAL = "incremental"
    DIFFERENTIAL = "differential"
    TRANSACTION_LOG = "transaction_log"

class BackupStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    EXPIRED = "expired"

@dataclass
class BackupJob:
    """Backup job configuration and status"""
    job_id: str
    backup_type: BackupType
    tenant_id: Optional[str]
    started_at: datetime
    completed_at: Optional[datetime]
    status: BackupStatus
    file_path: str
    file_size_bytes: int
    compression_ratio: float
    retention_days: int
    metadata: Dict[str, Any]
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if isinstance(self.backup_type, str):
            self.backup_type = BackupType(self.backup_type)
        if isinstance(self.status, str):
            self.status = BackupStatus(self.status)

@dataclass
class RecoveryPoint:
    """Point-in-time recovery point"""
    timestamp: datetime
    backup_job_id: str
    transaction_log_sequence: int
    data_consistency_check: bool
    recovery_time_estimate: int  # seconds
    description: str

class DisasterRecoverySystem:
    """
    Comprehensive Disaster Recovery and Backup System
    """
    
    def __init__(self):
        self.backup_storage_path = getattr(settings, 'BACKUP_STORAGE_PATH', '/var/backups/shrimp_farm')
        self.s3_client = boto3.client('s3') if self._aws_configured() else None
        self.backup_bucket = getattr(settings, 'BACKUP_S3_BUCKET', 'shrimp-farm-backups')
        
        self.backup_jobs: Dict[str, BackupJob] = {}
        self.recovery_points: List[RecoveryPoint] = []
        self.backup_schedules: Dict[str, Dict[str, Any]] = {}
        
        # Ensure backup directory exists
        os.makedirs(self.backup_storage_path, exist_ok=True)
        
        # Setup backup schedules
        self._setup_backup_schedules()
        
        # Load existing backup jobs
        self._load_backup_history()
    
    def _aws_configured(self) -> bool:
        """Check if AWS is configured for cloud backups"""
        return all([
            os.getenv('AWS_ACCESS_KEY_ID'),
            os.getenv('AWS_SECRET_ACCESS_KEY'),
            os.getenv('AWS_DEFAULT_REGION')
        ])
    
    def _setup_backup_schedules(self):
        """Setup automated backup schedules"""
        self.backup_schedules = {
            "full_daily": {
                "backup_type": BackupType.FULL,
                "schedule": "0 2 * * *",  # Daily at 2 AM
                "retention_days": 30,
                "enabled": True
            },
            "incremental_hourly": {
                "backup_type": BackupType.INCREMENTAL,
                "schedule": "0 * * * *",  # Every hour
                "retention_days": 7,
                "enabled": True
            },
            "transaction_log_continuous": {
                "backup_type": BackupType.TRANSACTION_LOG,
                "schedule": "*/15 * * * *",  # Every 15 minutes
                "retention_days": 3,
                "enabled": True
            }
        }
    
    def _load_backup_history(self):
        """Load backup job history"""
        try:
            history_file = os.path.join(self.backup_storage_path, 'backup_history.json')
            if os.path.exists(history_file):
                with open(history_file, 'r') as f:
                    history_data = json.load(f)
                    
                for job_data in history_data.get('jobs', []):
                    job = BackupJob(**job_data)
                    self.backup_jobs[job.job_id] = job
                
                logger.info(f"Loaded {len(self.backup_jobs)} backup jobs from history")
        
        except Exception as e:
            logger.error(f"Failed to load backup history: {e}")
    
    def create_backup(self, backup_type: BackupType, tenant_id: Optional[str] = None,
                     retention_days: int = 30) -> str:
        """Create a new backup"""
        job_id = f"backup_{int(time.time())}_{backup_type.value}"
        
        try:
            # Create backup job
            backup_job = BackupJob(
                job_id=job_id,
                backup_type=backup_type,
                tenant_id=tenant_id,
                started_at=datetime.now(),
                completed_at=None,
                status=BackupStatus.PENDING,
                file_path="",
                file_size_bytes=0,
                compression_ratio=0.0,
                retention_days=retention_days,
                metadata={}
            )
            
            self.backup_jobs[job_id] = backup_job
            
            # Start backup in background thread
            backup_thread = threading.Thread(
                target=self._execute_backup,
                args=(backup_job,)
            )
            backup_thread.daemon = True
            backup_thread.start()
            
            logger.info(f"Started backup job: {job_id}")
            return job_id
            
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            if job_id in self.backup_jobs:
                self.backup_jobs[job_id].status = BackupStatus.FAILED
                self.backup_jobs[job_id].error_message = str(e)
            raise
    
    def _execute_backup(self, backup_job: BackupJob):
        """Execute backup job"""
        try:
            backup_job.status = BackupStatus.IN_PROGRESS
            
            if backup_job.backup_type == BackupType.FULL:
                self._create_full_backup(backup_job)
            elif backup_job.backup_type == BackupType.INCREMENTAL:
                self._create_incremental_backup(backup_job)
            elif backup_job.backup_type == BackupType.TRANSACTION_LOG:
                self._create_transaction_log_backup(backup_job)
            
            # Compress backup
            self._compress_backup(backup_job)
            
            # Upload to cloud storage
            if self.s3_client:
                self._upload_to_cloud(backup_job)
            
            # Verify backup integrity
            if self._verify_backup_integrity(backup_job):
                backup_job.status = BackupStatus.COMPLETED
                backup_job.completed_at = datetime.now()
                
                # Create recovery point
                self._create_recovery_point(backup_job)
                
                logger.info(f"Backup completed successfully: {backup_job.job_id}")
            else:
                backup_job.status = BackupStatus.FAILED
                backup_job.error_message = "Backup integrity verification failed"
                
        except Exception as e:
            backup_job.status = BackupStatus.FAILED
            backup_job.error_message = str(e)
            backup_job.completed_at = datetime.now()
            logger.error(f"Backup failed: {backup_job.job_id} - {e}")
        
        finally:
            # Save backup history
            self._save_backup_history()
    
    def _create_full_backup(self, backup_job: BackupJob):
        """Create full database backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if backup_job.tenant_id:
            # Tenant-specific backup
            backup_file = os.path.join(
                self.backup_storage_path,
                f"full_backup_tenant_{backup_job.tenant_id}_{timestamp}.sql"
            )
            
            # Get tenant database configuration
            from tenancy.multi_tenant import tenant_manager
            tenant = tenant_manager.get_tenant(backup_job.tenant_id)
            
            if tenant and tenant.isolation_level.value == "separate_database":
                # Backup separate tenant database
                db_name = tenant_manager.tenant_databases.get(backup_job.tenant_id)
                self._dump_database(db_name, backup_file)
            else:
                # Backup tenant data from shared database
                self._dump_tenant_data(backup_job.tenant_id, backup_file)
        else:
            # Full system backup
            backup_file = os.path.join(
                self.backup_storage_path,
                f"full_backup_system_{timestamp}.sql"
            )
            self._dump_database(settings.DATABASES['default']['NAME'], backup_file)
        
        backup_job.file_path = backup_file
        backup_job.file_size_bytes = os.path.getsize(backup_file)
        backup_job.metadata = {
            "backup_method": "pg_dump",
            "timestamp": timestamp,
            "database_size": self._get_database_size()
        }
    
    def _create_incremental_backup(self, backup_job: BackupJob):
        """Create incremental backup"""
        # Find last full backup
        last_full_backup = self._find_last_backup(BackupType.FULL, backup_job.tenant_id)
        
        if not last_full_backup:
            # No full backup found, create full backup instead
            logger.warning("No full backup found, creating full backup instead")
            backup_job.backup_type = BackupType.FULL
            self._create_full_backup(backup_job)
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(
            self.backup_storage_path,
            f"incremental_backup_{timestamp}.sql"
        )
        
        # Create incremental backup using WAL files or timestamp-based changes
        self._create_incremental_dump(last_full_backup.started_at, backup_file, backup_job.tenant_id)
        
        backup_job.file_path = backup_file
        backup_job.file_size_bytes = os.path.getsize(backup_file)
        backup_job.metadata = {
            "backup_method": "incremental",
            "base_backup": last_full_backup.job_id,
            "timestamp": timestamp
        }
    
    def _create_transaction_log_backup(self, backup_job: BackupJob):
        """Create transaction log backup"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = os.path.join(
            self.backup_storage_path,
            f"transaction_log_{timestamp}.wal"
        )
        
        # Archive current WAL files
        self._archive_wal_files(backup_file)
        
        backup_job.file_path = backup_file
        backup_job.file_size_bytes = os.path.getsize(backup_file) if os.path.exists(backup_file) else 0
        backup_job.metadata = {
            "backup_method": "wal_archive",
            "timestamp": timestamp
        }
    
    def _dump_database(self, db_name: str, output_file: str):
        """Dump database using pg_dump"""
        db_config = settings.DATABASES['default']
        
        cmd = [
            'pg_dump',
            '-h', db_config['HOST'],
            '-p', str(db_config['PORT']),
            '-U', db_config['USER'],
            '-d', db_name,
            '-f', output_file,
            '--verbose',
            '--no-password'
        ]
        
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['PASSWORD']
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode != 0:
            raise Exception(f"pg_dump failed: {result.stderr}")
    
    def _dump_tenant_data(self, tenant_id: str, output_file: str):
        """Dump tenant-specific data from shared database"""
        # This would involve complex queries to extract only tenant data
        # For now, create a simplified version
        
        with connection.cursor() as cursor:
            # Get all tables with tenant_id column
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.columns 
                WHERE column_name = 'tenant_id' 
                AND table_schema = 'public'
            """)
            
            tenant_tables = [row[0] for row in cursor.fetchall()]
            
            with open(output_file, 'w') as f:
                f.write(f"-- Tenant backup for: {tenant_id}\n")
                f.write(f"-- Generated at: {datetime.now()}\n\n")
                
                for table in tenant_tables:
                    cursor.execute(f"SELECT * FROM {table} WHERE tenant_id = %s", [tenant_id])
                    # Write table data (simplified)
                    f.write(f"-- Data for table: {table}\n")
    
    def _create_incremental_dump(self, since_timestamp: datetime, output_file: str, tenant_id: Optional[str]):
        """Create incremental dump based on timestamp"""
        # This is a simplified implementation
        # In production, use WAL-based incremental backups
        
        with connection.cursor() as cursor:
            with open(output_file, 'w') as f:
                f.write(f"-- Incremental backup since: {since_timestamp}\n")
                f.write(f"-- Generated at: {datetime.now()}\n\n")
                
                # Find tables with updated_at or modified_at columns
                cursor.execute("""
                    SELECT table_name, column_name
                    FROM information_schema.columns 
                    WHERE column_name IN ('updated_at', 'modified_at', 'last_modified')
                    AND table_schema = 'public'
                """)
                
                for table_name, timestamp_column in cursor.fetchall():
                    where_clause = f"{timestamp_column} > %s"
                    params = [since_timestamp]
                    
                    if tenant_id:
                        where_clause += " AND tenant_id = %s"
                        params.append(tenant_id)
                    
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {where_clause}", params)
                    count = cursor.fetchone()[0]
                    
                    if count > 0:
                        f.write(f"-- {count} changed records in {table_name}\n")
    
    def _archive_wal_files(self, output_file: str):
        """Archive WAL files for point-in-time recovery"""
        # This would involve PostgreSQL WAL archiving
        # For now, create a placeholder
        
        with open(output_file, 'w') as f:
            f.write(f"-- WAL archive created at: {datetime.now()}\n")
            f.write("-- This would contain WAL file data for point-in-time recovery\n")
    
    def _compress_backup(self, backup_job: BackupJob):
        """Compress backup file"""
        if not os.path.exists(backup_job.file_path):
            return
        
        original_size = backup_job.file_size_bytes
        compressed_path = f"{backup_job.file_path}.gz"
        
        with open(backup_job.file_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        # Remove original file
        os.remove(backup_job.file_path)
        
        # Update backup job
        backup_job.file_path = compressed_path
        compressed_size = os.path.getsize(compressed_path)
        backup_job.file_size_bytes = compressed_size
        backup_job.compression_ratio = (original_size - compressed_size) / original_size
        
        logger.info(f"Compressed backup: {original_size} -> {compressed_size} bytes "
                   f"({backup_job.compression_ratio:.2%} reduction)")
    
    def _upload_to_cloud(self, backup_job: BackupJob):
        """Upload backup to cloud storage"""
        if not self.s3_client or not os.path.exists(backup_job.file_path):
            return
        
        try:
            s3_key = f"backups/{backup_job.backup_type.value}/{os.path.basename(backup_job.file_path)}"
            
            self.s3_client.upload_file(
                backup_job.file_path,
                self.backup_bucket,
                s3_key,
                ExtraArgs={
                    'Metadata': {
                        'job_id': backup_job.job_id,
                        'backup_type': backup_job.backup_type.value,
                        'tenant_id': backup_job.tenant_id or 'system',
                        'created_at': backup_job.started_at.isoformat()
                    }
                }
            )
            
            backup_job.metadata['s3_key'] = s3_key
            backup_job.metadata['s3_bucket'] = self.backup_bucket
            
            logger.info(f"Uploaded backup to S3: {s3_key}")
            
        except Exception as e:
            logger.error(f"Failed to upload backup to S3: {e}")
            # Don't fail the backup job for cloud upload failures
    
    def _verify_backup_integrity(self, backup_job: BackupJob) -> bool:
        """Verify backup file integrity"""
        try:
            if not os.path.exists(backup_job.file_path):
                return False
            
            # Check file size
            if backup_job.file_size_bytes == 0:
                return False
            
            # For compressed files, try to decompress a small portion
            if backup_job.file_path.endswith('.gz'):
                with gzip.open(backup_job.file_path, 'rb') as f:
                    f.read(1024)  # Try to read first 1KB
            
            # Additional integrity checks could include:
            # - Checksum verification
            # - Partial restore test
            # - SQL syntax validation
            
            return True
            
        except Exception as e:
            logger.error(f"Backup integrity verification failed: {e}")
            return False
    
    def _create_recovery_point(self, backup_job: BackupJob):
        """Create recovery point for backup"""
        recovery_point = RecoveryPoint(
            timestamp=backup_job.completed_at,
            backup_job_id=backup_job.job_id,
            transaction_log_sequence=0,  # Would be actual sequence number
            data_consistency_check=True,
            recovery_time_estimate=self._estimate_recovery_time(backup_job),
            description=f"{backup_job.backup_type.value.title()} backup"
        )
        
        self.recovery_points.append(recovery_point)
        
        # Keep only recent recovery points
        self.recovery_points = sorted(
            self.recovery_points,
            key=lambda rp: rp.timestamp,
            reverse=True
        )[:100]  # Keep last 100 recovery points
    
    def _estimate_recovery_time(self, backup_job: BackupJob) -> int:
        """Estimate recovery time in seconds"""
        # Simple estimation based on file size
        # In production, use historical recovery performance data
        
        size_gb = backup_job.file_size_bytes / (1024 ** 3)
        
        if backup_job.backup_type == BackupType.FULL:
            return int(size_gb * 300)  # 5 minutes per GB
        elif backup_job.backup_type == BackupType.INCREMENTAL:
            return int(size_gb * 120)  # 2 minutes per GB
        else:
            return int(size_gb * 60)   # 1 minute per GB
    
    def _find_last_backup(self, backup_type: BackupType, tenant_id: Optional[str]) -> Optional[BackupJob]:
        """Find last successful backup of specified type"""
        matching_backups = [
            job for job in self.backup_jobs.values()
            if (job.backup_type == backup_type and
                job.tenant_id == tenant_id and
                job.status == BackupStatus.COMPLETED)
        ]
        
        if matching_backups:
            return max(matching_backups, key=lambda job: job.started_at)
        
        return None
    
    def _get_database_size(self) -> int:
        """Get current database size in bytes"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database())),
                           pg_database_size(current_database())
                """)
                pretty_size, size_bytes = cursor.fetchone()
                return size_bytes
        except Exception as e:
            logger.error(f"Failed to get database size: {e}")
            return 0
    
    def _save_backup_history(self):
        """Save backup job history to file"""
        try:
            history_file = os.path.join(self.backup_storage_path, 'backup_history.json')
            
            history_data = {
                'last_updated': datetime.now().isoformat(),
                'jobs': [asdict(job) for job in self.backup_jobs.values()]
            }
            
            with open(history_file, 'w') as f:
                json.dump(history_data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Failed to save backup history: {e}")
    
    def get_backup_status(self, job_id: str) -> Optional[BackupJob]:
        """Get backup job status"""
        return self.backup_jobs.get(job_id)
    
    def list_backups(self, tenant_id: Optional[str] = None, 
                    backup_type: Optional[BackupType] = None) -> List[BackupJob]:
        """List backup jobs with optional filtering"""
        backups = list(self.backup_jobs.values())
        
        if tenant_id:
            backups = [b for b in backups if b.tenant_id == tenant_id]
        
        if backup_type:
            backups = [b for b in backups if b.backup_type == backup_type]
        
        return sorted(backups, key=lambda b: b.started_at, reverse=True)
    
    def cleanup_expired_backups(self):
        """Clean up expired backup files"""
        try:
            current_time = datetime.now()
            expired_jobs = []
            
            for job in self.backup_jobs.values():
                if job.status == BackupStatus.COMPLETED:
                    expiry_date = job.started_at + timedelta(days=job.retention_days)
                    
                    if current_time > expiry_date:
                        expired_jobs.append(job)
            
            for job in expired_jobs:
                # Remove local file
                if os.path.exists(job.file_path):
                    os.remove(job.file_path)
                
                # Remove from S3 if exists
                if self.s3_client and 's3_key' in job.metadata:
                    try:
                        self.s3_client.delete_object(
                            Bucket=self.backup_bucket,
                            Key=job.metadata['s3_key']
                        )
                    except Exception as e:
                        logger.warning(f"Failed to delete S3 backup: {e}")
                
                # Update job status
                job.status = BackupStatus.EXPIRED
            
            if expired_jobs:
                self._save_backup_history()
                logger.info(f"Cleaned up {len(expired_jobs)} expired backups")
                
        except Exception as e:
            logger.error(f"Backup cleanup failed: {e}")
    
    def get_recovery_points(self, tenant_id: Optional[str] = None) -> List[RecoveryPoint]:
        """Get available recovery points"""
        if tenant_id:
            # Filter recovery points for specific tenant
            tenant_backup_ids = {
                job.job_id for job in self.backup_jobs.values()
                if job.tenant_id == tenant_id
            }
            return [
                rp for rp in self.recovery_points
                if rp.backup_job_id in tenant_backup_ids
            ]
        
        return self.recovery_points
    
    def get_backup_dashboard(self) -> Dict[str, Any]:
        """Get backup system dashboard data"""
        total_backups = len(self.backup_jobs)
        successful_backups = sum(1 for job in self.backup_jobs.values() if job.status == BackupStatus.COMPLETED)
        failed_backups = sum(1 for job in self.backup_jobs.values() if job.status == BackupStatus.FAILED)
        
        total_storage = sum(
            job.file_size_bytes for job in self.backup_jobs.values()
            if job.status == BackupStatus.COMPLETED
        )
        
        return {
            "summary": {
                "total_backups": total_backups,
                "successful_backups": successful_backups,
                "failed_backups": failed_backups,
                "success_rate": (successful_backups / total_backups * 100) if total_backups > 0 else 0
            },
            "storage": {
                "total_storage_bytes": total_storage,
                "total_storage_gb": total_storage / (1024 ** 3),
                "average_compression_ratio": self._calculate_average_compression()
            },
            "recent_backups": [
                asdict(job) for job in sorted(
                    self.backup_jobs.values(),
                    key=lambda j: j.started_at,
                    reverse=True
                )[:10]
            ],
            "recovery_points": len(self.recovery_points),
            "schedules": self.backup_schedules
        }
    
    def _calculate_average_compression(self) -> float:
        """Calculate average compression ratio"""
        completed_jobs = [
            job for job in self.backup_jobs.values()
            if job.status == BackupStatus.COMPLETED and job.compression_ratio > 0
        ]
        
        if completed_jobs:
            return sum(job.compression_ratio for job in completed_jobs) / len(completed_jobs)
        
        return 0.0

# Global disaster recovery system instance
disaster_recovery = DisasterRecoverySystem()
