<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Geofences - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Header */
    .geofence-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .geofence-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: geofence-sweep 4s infinite;
    }
    
    @keyframes geofence-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-decoration: none;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
        position: relative;
        z-index: 10;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
        pointer-events: auto;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    }

    .geofence-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    }

    .table-enhanced {
        margin-bottom: 0;
    }

    .table-enhanced thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px;
        font-weight: 600;
    }

    .table-enhanced tbody tr {
        transition: all 0.3s ease;
    }

    .table-enhanced tbody tr:hover {
        background-color: rgba(102, 126, 234, 0.05);
        transform: scale(1.01);
    }

    .table-enhanced td {
        padding: 15px;
        border-color: rgba(102, 126, 234, 0.1);
        vertical-align: middle;
    }

    .status-badge {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        font-weight: 600;
    }

    .action-btn {
        padding: 6px 12px;
        border-radius: 8px;
        border: 1px solid;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    /* Alert Styles */
    .live-indicator {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: rgba(76, 175, 80, 0.1);
        border: 2px solid #4CAF50;
        border-radius: 20px;
        padding: 4px 12px;
        color: #4CAF50;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .live-dot {
        width: 8px;
        height: 8px;
        background: #4CAF50;
        border-radius: 50%;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(1.2); }
    }

    .alert-card {
        background: white;
        border-radius: 15px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        border-left: 5px solid #f44336;
        transition: all 0.3s ease;
        animation: slideIn 0.5s ease-out;
    }

    .alert-card:hover {
        transform: translateX(5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }

    .alert-card.entry {
        border-left-color: #4CAF50;
    }

    .alert-card.exit {
        border-left-color: #FF9800;
    }

    .alert-card.violation {
        border-left-color: #f44336;
    }

    .alert-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .alert-icon {
        font-size: 1.2rem;
        margin-right: 10px;
    }

    .alert-icon.entry {
        color: #4CAF50;
    }

    .alert-icon.exit {
        color: #FF9800;
    }

    .alert-icon.violation {
        color: #f44336;
    }

    .alert-time {
        font-size: 0.8rem;
        color: #666;
        margin-left: auto;
    }

    .worker-name {
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .geofence-name {
        font-weight: 600;
        color: #667eea;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .alerts-container {
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 15px;
        background: #f8f9fa;
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Header -->
    <div class="geofence-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Geofence Management</h1>
                <p class="lead mb-0">Manage location boundaries and monitoring zones</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <a href="{% url 'labor:geofence_create' %}" class="control-btn">
                        <i class="fas fa-plus me-2"></i>New Geofence
                    </a>
                    <a href="{% url 'labor:geofence_alerts_dashboard' %}" class="control-btn" style="background: linear-gradient(135deg, #f44336 0%, #e91e63 100%);">
                        <i class="fas fa-exclamation-triangle me-2"></i>Alert Dashboard
                    </a>
                    <button class="control-btn" onclick="toggleRealTimeAlerts()" id="alertToggleBtn">
                        <i class="fas fa-bell me-2"></i>Live Alerts: ON
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="/labor/location-tracking/" class="quick-action">
            <i class="fas fa-map-marked-alt"></i>
            Location Tracking
        </a>
        <a href="/labor/heat-map/" class="quick-action">
            <i class="fas fa-fire"></i>
            Heat Map
        </a>
        <a href="/labor/dashboard/" class="quick-action">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
        <a href="/labor/workers/" class="quick-action">
            <i class="fas fa-users"></i>
            Workers
        </a>
    </div>

    <!-- Stats Row -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Active Geofences</h6>
                        <h2 class="mb-0">5</h2>
                    </div>
                    <div>
                        <i class="fas fa-map-marker-alt fa-2x" style="opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Total Areas</h6>
                        <h2 class="mb-0">8</h2>
                    </div>
                    <div>
                        <i class="fas fa-shapes fa-2x" style="opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Recent Events</h6>
                        <h2 class="mb-0">12</h2>
                    </div>
                    <div>
                        <i class="fas fa-bell fa-2x" style="opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">Pond Zones</h6>
                        <h2 class="mb-0">6</h2>
                    </div>
                    <div>
                        <i class="fas fa-water fa-2x" style="opacity: 0.7;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Alerts Section -->
    <div class="enhanced-card mb-4" id="alertsSection">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h3 class="section-title mb-0">
                <i class="fas fa-bell me-2"></i>
                Live Geofence Alerts
                <span class="live-indicator ms-2">
                    <span class="live-dot"></span>
                    LIVE
                </span>
            </h3>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="clearAllAlerts()">
                    <i class="fas fa-trash me-1"></i>Clear All
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="exportAlerts()">
                    <i class="fas fa-download me-1"></i>Export
                </button>
            </div>
        </div>

        <!-- Alert Filters -->
        <div class="mb-3">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-secondary btn-sm active" onclick="filterAlerts('all')">All Events</button>
                <button type="button" class="btn btn-outline-success btn-sm" onclick="filterAlerts('entry')">Entries</button>
                <button type="button" class="btn btn-outline-warning btn-sm" onclick="filterAlerts('exit')">Exits</button>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="filterAlerts('violation')">Violations</button>
            </div>
        </div>

        <!-- Alerts List -->
        <div id="alertsList" class="alerts-container" style="max-height: 400px; overflow-y: auto;">
            <!-- Sample Recent Events -->
            <div class="alert-card entry">
                <div class="alert-header">
                    <i class="fas fa-sign-in-alt alert-icon entry"></i>
                    <div class="alert-time">2 minutes ago</div>
                </div>
                <div class="alert-details">
                    <div class="worker-name">Rajesh Kumar</div>
                    <div>Entered <span class="geofence-name">Pond A-1 Monitoring Zone</span></div>
                    <small class="text-muted">📍 10.790523, 106.680045</small>
                </div>
            </div>

            <div class="alert-card exit">
                <div class="alert-header">
                    <i class="fas fa-sign-out-alt alert-icon exit"></i>
                    <div class="alert-time">5 minutes ago</div>
                </div>
                <div class="alert-details">
                    <div class="worker-name">Priya Sharma</div>
                    <div>Exited <span class="geofence-name">Feed Storage Area</span></div>
                    <small class="text-muted">📍 10.791234, 106.681567</small>
                </div>
            </div>

            <div class="alert-card violation">
                <div class="alert-header">
                    <i class="fas fa-exclamation-triangle alert-icon violation"></i>
                    <div class="alert-time">8 minutes ago</div>
                </div>
                <div class="alert-details">
                    <div class="worker-name">Amit Singh</div>
                    <div>Violation in <span class="geofence-name">Restricted Equipment Zone</span></div>
                    <small class="text-muted">📍 10.789876, 106.679123</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Geofences Table -->
    <div class="enhanced-card">
        <h3 class="section-title">
            <i class="fas fa-list me-2"></i>
            All Geofences
        </h3>
        
        <div class="geofence-table">
            <!-- Mock geofence data for demonstration -->
            <table class="table table-enhanced">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Pond</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <strong>Pond Area A</strong>
                            </div>
                        </td>
                        <td><span class="badge bg-info">Pond Zone</span></td>
                        <td>Main Pond #1</td>
                        <td><span class="status-badge bg-success text-white">Active</span></td>
                        <td>Jul 01, 2025</td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{% url 'labor:geofence_detail' pk=1 %}" class="action-btn text-primary border-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'labor:geofence_update' pk=1 %}" class="action-btn text-warning border-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'labor:geofence_delete' pk=1 %}" class="action-btn text-danger border-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-industry text-secondary me-2"></i>
                                <strong>Processing Area</strong>
                            </div>
                        </td>
                        <td><span class="badge bg-warning">Work Zone</span></td>
                        <td>-</td>
                        <td><span class="status-badge bg-success text-white">Active</span></td>
                        <td>Jun 28, 2025</td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{% url 'labor:geofence_detail' pk=2 %}" class="action-btn text-primary border-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'labor:geofence_update' pk=2 %}" class="action-btn text-warning border-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'labor:geofence_delete' pk=2 %}" class="action-btn text-danger border-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-warehouse text-success me-2"></i>
                                <strong>Storage Zone</strong>
                            </div>
                        </td>
                        <td><span class="badge bg-secondary">Storage</span></td>
                        <td>-</td>
                        <td><span class="status-badge bg-success text-white">Active</span></td>
                        <td>Jun 25, 2025</td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{% url 'labor:geofence_detail' pk=3 %}" class="action-btn text-primary border-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'labor:geofence_update' pk=3 %}" class="action-btn text-warning border-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'labor:geofence_delete' pk=3 %}" class="action-btn text-danger border-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                                <strong>Restricted Area</strong>
                            </div>
                        </td>
                        <td><span class="badge bg-danger">Restricted</span></td>
                        <td>-</td>
                        <td><span class="status-badge bg-secondary text-white">Inactive</span></td>
                        <td>Jun 20, 2025</td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{% url 'labor:geofence_detail' pk=4 %}" class="action-btn text-primary border-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'labor:geofence_update' pk=4 %}" class="action-btn text-warning border-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'labor:geofence_delete' pk=4 %}" class="action-btn text-danger border-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-shield-alt text-info me-2"></i>
                                <strong>Safety Zone</strong>
                            </div>
                        </td>
                        <td><span class="badge bg-success">Safety</span></td>
                        <td>Emergency Area</td>
                        <td><span class="status-badge bg-success text-white">Active</span></td>
                        <td>Jun 15, 2025</td>
                        <td>
                            <div class="d-flex gap-1">
                                <a href="{% url 'labor:geofence_detail' pk=5 %}" class="action-btn text-primary border-primary">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'labor:geofence_update' pk=5 %}" class="action-btn text-warning border-warning">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'labor:geofence_delete' pk=5 %}" class="action-btn text-danger border-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Real-time alerts functionality
let realTimeAlertsEnabled = true;
let alertsInterval;
let currentFilter = 'all';

function toggleRealTimeAlerts() {
    const btn = document.getElementById('alertToggleBtn');
    if (realTimeAlertsEnabled) {
        realTimeAlertsEnabled = false;
        btn.innerHTML = '<i class="fas fa-bell-slash me-2"></i>Live Alerts: OFF';
        btn.style.background = 'linear-gradient(135deg, #6c757d 0%, #495057 100%)';
        clearInterval(alertsInterval);
    } else {
        realTimeAlertsEnabled = true;
        btn.innerHTML = '<i class="fas fa-bell me-2"></i>Live Alerts: ON';
        btn.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        startRealTimeAlerts();
    }
}

function startRealTimeAlerts() {
    alertsInterval = setInterval(() => {
        if (realTimeAlertsEnabled) {
            generateRandomAlert();
        }
    }, Math.random() * 20000 + 10000); // Random interval between 10-30 seconds
}

function generateRandomAlert() {
    const workers = ['Rajesh Kumar', 'Priya Sharma', 'Amit Singh', 'Sunita Devi', 'Ravi Patel', 'Meera Joshi'];
    const zones = ['Pond A-1 Monitoring', 'Feed Storage Area', 'Equipment Zone', 'Water Treatment', 'Processing Area', 'Storage Zone'];
    const types = ['entry', 'exit', 'violation'];
    const icons = { entry: 'sign-in-alt', exit: 'sign-out-alt', violation: 'exclamation-triangle' };

    const type = types[Math.floor(Math.random() * types.length)];
    const worker = workers[Math.floor(Math.random() * workers.length)];
    const zone = zones[Math.floor(Math.random() * zones.length)];

    const alert = {
        type: type,
        worker: worker,
        zone: zone,
        icon: icons[type],
        lat: (10.78 + Math.random() * 0.02).toFixed(6),
        lng: (106.67 + Math.random() * 0.02).toFixed(6)
    };

    addAlertToList(alert);
}

function addAlertToList(alert) {
    const alertsList = document.getElementById('alertsList');
    const alertCard = document.createElement('div');
    alertCard.className = `alert-card ${alert.type}`;
    alertCard.innerHTML = `
        <div class="alert-header">
            <i class="fas fa-${alert.icon} alert-icon ${alert.type}"></i>
            <div class="alert-time">just now</div>
        </div>
        <div class="alert-details">
            <div class="worker-name">${alert.worker}</div>
            <div>
                ${alert.type === 'entry' ? 'Entered' : alert.type === 'exit' ? 'Exited' : 'Violation in'}
                <span class="geofence-name">${alert.zone}</span>
            </div>
            <small class="text-muted">📍 ${alert.lat}, ${alert.lng}</small>
        </div>
    `;

    // Add to top of list
    alertsList.insertBefore(alertCard, alertsList.firstChild);

    // Remove old alerts (keep only last 10)
    const alerts = alertsList.querySelectorAll('.alert-card');
    if (alerts.length > 10) {
        alerts[alerts.length - 1].remove();
    }

    // Apply current filter
    if (currentFilter !== 'all' && !alertCard.classList.contains(currentFilter)) {
        alertCard.style.display = 'none';
    }
}

function filterAlerts(type) {
    currentFilter = type;
    const alerts = document.querySelectorAll('.alert-card');
    const buttons = document.querySelectorAll('.btn-group button');

    // Update button states
    buttons.forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');

    // Filter alerts
    alerts.forEach(alert => {
        if (type === 'all' || alert.classList.contains(type)) {
            alert.style.display = 'block';
        } else {
            alert.style.display = 'none';
        }
    });
}

function clearAllAlerts() {
    const alertsList = document.getElementById('alertsList');
    const alerts = alertsList.querySelectorAll('.alert-card');

    alerts.forEach((alert, index) => {
        setTimeout(() => {
            alert.style.animation = 'slideOut 0.3s ease-in forwards';
            setTimeout(() => alert.remove(), 300);
        }, index * 100);
    });
}

function exportAlerts() {
    const alerts = document.querySelectorAll('.alert-card');
    const data = Array.from(alerts).map(alert => ({
        worker: alert.querySelector('.worker-name').textContent,
        type: alert.className.split(' ').find(c => ['entry', 'exit', 'violation'].includes(c)),
        zone: alert.querySelector('.geofence-name').textContent,
        time: alert.querySelector('.alert-time').textContent,
        location: alert.querySelector('.text-muted').textContent.replace('📍 ', '')
    }));

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `geofence-alerts-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Geofence management dashboard initialized');

    // Start real-time alerts
    startRealTimeAlerts();

    // Add click handlers for action buttons
    document.querySelectorAll('.action-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            console.log('Action clicked:', this.href);
        });
    });

    // Add click handlers for quick actions
    document.querySelectorAll('.quick-action').forEach(function(link) {
        link.addEventListener('click', function(e) {
            console.log('Quick action clicked:', this.href);
        });
    });

    // Add table row click handling
    document.querySelectorAll('.table-enhanced tbody tr').forEach(function(row) {
        row.addEventListener('click', function(e) {
            if (!e.target.closest('.action-btn')) {
                console.log('Row clicked');
                // Could navigate to detail view
            }
        });
    });

    // Add slideOut animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(-100%);
            }
        }
    `;
    document.head.appendChild(style);
});
</script>

</body>
</html>