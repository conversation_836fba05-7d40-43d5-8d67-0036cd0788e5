{% extends 'base.html' %}
{% load static %}

{% block title %}IoT Device Management - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<link href="{% static 'css/unified-template.css' %}" rel="stylesheet">
<link href="{% static 'css/iot-realtime.css' %}" rel="stylesheet">
<style>
    .device-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .device-card {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        transition: all 0.3s ease;
        color: #e2e8f0;
        position: relative;
    }
    
    .device-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
        border-color: rgba(102, 126, 234, 0.5);
    }
    
    .device-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .device-info h6 {
        font-size: 1rem;
        font-weight: 600;
        color: #e2e8f0;
        margin-bottom: 0.25rem;
    }
    
    .device-type {
        font-size: 0.8rem;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .device-status {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .device-status.online {
        color: #10b981;
    }
    
    .device-status.offline {
        color: #ef4444;
    }
    
    .device-status.maintenance {
        color: #f59e0b;
    }
    
    .device-metrics {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin: 1rem 0;
    }
    
    .metric {
        text-align: center;
        padding: 0.75rem;
        background: rgba(74, 85, 104, 0.2);
        border-radius: 8px;
    }
    
    .metric-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: #e2e8f0;
        display: block;
    }
    
    .metric-label {
        font-size: 0.7rem;
        color: #94a3b8;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .device-actions {
        display: flex;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .device-actions .btn {
        flex: 1;
        font-size: 0.8rem;
        padding: 0.5rem;
    }
    
    .add-device-card {
        background: rgba(74, 85, 104, 0.2);
        border: 2px dashed rgba(102, 126, 234, 0.3);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 200px;
    }
    
    .add-device-card:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }
    
    .add-device-icon {
        font-size: 2rem;
        color: #667eea;
        margin-bottom: 1rem;
    }
    
    .network-topology {
        background: rgba(30, 41, 59, 0.8);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(74, 85, 104, 0.3);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        color: #e2e8f0;
    }
    
    .topology-node {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        position: relative;
        margin: 1rem;
    }
    
    .topology-connection {
        position: absolute;
        height: 2px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        opacity: 0.6;
    }
    
    @media (max-width: 768px) {
        .device-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .device-metrics {
            grid-template-columns: 1fr;
            gap: 0.5rem;
        }
        
        .device-actions {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- IoT Management Header -->
    <div class="realtime-header">
        <div class="realtime-header-content">
            <div class="realtime-header-info">
                <h2>
                    <i class="fas fa-microchip me-2"></i>
                    IoT Device Management
                </h2>
                <p>Manage and monitor IoT sensors and devices</p>
            </div>
            <div class="realtime-header-controls">
                <div class="connection-status">
                    <div class="status-indicator online"></div>
                    <span>Network Online</span>
                </div>
                <div class="system-status">
                    <span class="status-text">8/8 devices connected</span>
                </div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
                    <i class="fas fa-plus"></i>
                    <span>Add Device</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Network Topology Overview -->
    <div class="network-topology">
        <h5 class="mb-3">
            <i class="fas fa-network-wired me-2"></i>
            Network Topology
        </h5>
        <div class="d-flex justify-content-center align-items-center position-relative" style="height: 200px;">
            <!-- Gateway -->
            <div class="topology-node" style="position: absolute; left: 50%; top: 20px; transform: translateX(-50%);">
                <i class="fas fa-wifi"></i>
                <div class="position-absolute" style="top: 70px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; white-space: nowrap;">
                    Gateway
                </div>
            </div>
            
            <!-- Sensor Nodes -->
            <div class="topology-node" style="position: absolute; left: 20%; top: 120px;">
                <i class="fas fa-thermometer-half"></i>
                <div class="position-absolute" style="top: 70px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; white-space: nowrap;">
                    Temp Sensor
                </div>
            </div>
            
            <div class="topology-node" style="position: absolute; left: 40%; top: 120px;">
                <i class="fas fa-wind"></i>
                <div class="position-absolute" style="top: 70px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; white-space: nowrap;">
                    DO Sensor
                </div>
            </div>
            
            <div class="topology-node" style="position: absolute; left: 60%; top: 120px;">
                <i class="fas fa-flask"></i>
                <div class="position-absolute" style="top: 70px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; white-space: nowrap;">
                    pH Sensor
                </div>
            </div>
            
            <div class="topology-node" style="position: absolute; left: 80%; top: 120px;">
                <i class="fas fa-tint"></i>
                <div class="position-absolute" style="top: 70px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; white-space: nowrap;">
                    Salinity
                </div>
            </div>
            
            <!-- Connection Lines -->
            <div class="topology-connection" style="top: 50px; left: 50%; width: 30%; transform: translateX(-50%) rotate(45deg);"></div>
            <div class="topology-connection" style="top: 50px; left: 50%; width: 30%; transform: translateX(-50%) rotate(-45deg);"></div>
            <div class="topology-connection" style="top: 50px; left: 50%; width: 30%; transform: translateX(-50%) rotate(135deg);"></div>
            <div class="topology-connection" style="top: 50px; left: 50%; width: 30%; transform: translateX(-50%) rotate(-135deg);"></div>
        </div>
    </div>

    <!-- Device Grid -->
    <div class="device-grid" id="deviceGrid">
        <!-- Temperature Sensor -->
        <div class="device-card">
            <div class="device-header">
                <div class="device-info">
                    <h6>Water Temperature Sensor</h6>
                    <div class="device-type">DS18B20 Digital</div>
                </div>
                <div class="device-status online">
                    <i class="fas fa-circle"></i>
                    Online
                </div>
            </div>
            <div class="device-metrics">
                <div class="metric">
                    <span class="metric-value">29.5°C</span>
                    <span class="metric-label">Current</span>
                </div>
                <div class="metric">
                    <span class="metric-value">98%</span>
                    <span class="metric-label">Uptime</span>
                </div>
                <div class="metric">
                    <span class="metric-value">85%</span>
                    <span class="metric-label">Battery</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-65 dBm</span>
                    <span class="metric-label">Signal</span>
                </div>
            </div>
            <div class="device-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog"></i> Configure
                </button>
                <button class="btn btn-outline-success btn-sm">
                    <i class="fas fa-sync"></i> Calibrate
                </button>
                <button class="btn btn-outline-info btn-sm">
                    <i class="fas fa-chart-line"></i> Data
                </button>
            </div>
        </div>

        <!-- Dissolved Oxygen Sensor -->
        <div class="device-card">
            <div class="device-header">
                <div class="device-info">
                    <h6>Dissolved Oxygen Sensor</h6>
                    <div class="device-type">Optical DO Probe</div>
                </div>
                <div class="device-status online">
                    <i class="fas fa-circle"></i>
                    Online
                </div>
            </div>
            <div class="device-metrics">
                <div class="metric">
                    <span class="metric-value">6.2 mg/L</span>
                    <span class="metric-label">Current</span>
                </div>
                <div class="metric">
                    <span class="metric-value">99%</span>
                    <span class="metric-label">Uptime</span>
                </div>
                <div class="metric">
                    <span class="metric-value">92%</span>
                    <span class="metric-label">Battery</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-58 dBm</span>
                    <span class="metric-label">Signal</span>
                </div>
            </div>
            <div class="device-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog"></i> Configure
                </button>
                <button class="btn btn-outline-success btn-sm">
                    <i class="fas fa-sync"></i> Calibrate
                </button>
                <button class="btn btn-outline-info btn-sm">
                    <i class="fas fa-chart-line"></i> Data
                </button>
            </div>
        </div>

        <!-- pH Sensor -->
        <div class="device-card">
            <div class="device-header">
                <div class="device-info">
                    <h6>pH Level Sensor</h6>
                    <div class="device-type">Glass Electrode</div>
                </div>
                <div class="device-status maintenance">
                    <i class="fas fa-circle"></i>
                    Maintenance
                </div>
            </div>
            <div class="device-metrics">
                <div class="metric">
                    <span class="metric-value">8.1</span>
                    <span class="metric-label">Current</span>
                </div>
                <div class="metric">
                    <span class="metric-value">95%</span>
                    <span class="metric-label">Uptime</span>
                </div>
                <div class="metric">
                    <span class="metric-value">78%</span>
                    <span class="metric-label">Battery</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-72 dBm</span>
                    <span class="metric-label">Signal</span>
                </div>
            </div>
            <div class="device-actions">
                <button class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-wrench"></i> Maintain
                </button>
                <button class="btn btn-outline-success btn-sm">
                    <i class="fas fa-sync"></i> Calibrate
                </button>
                <button class="btn btn-outline-info btn-sm">
                    <i class="fas fa-chart-line"></i> Data
                </button>
            </div>
        </div>

        <!-- Salinity Sensor -->
        <div class="device-card">
            <div class="device-header">
                <div class="device-info">
                    <h6>Salinity Sensor</h6>
                    <div class="device-type">Conductivity Probe</div>
                </div>
                <div class="device-status online">
                    <i class="fas fa-circle"></i>
                    Online
                </div>
            </div>
            <div class="device-metrics">
                <div class="metric">
                    <span class="metric-value">20.5 ppt</span>
                    <span class="metric-label">Current</span>
                </div>
                <div class="metric">
                    <span class="metric-value">97%</span>
                    <span class="metric-label">Uptime</span>
                </div>
                <div class="metric">
                    <span class="metric-value">88%</span>
                    <span class="metric-label">Battery</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-61 dBm</span>
                    <span class="metric-label">Signal</span>
                </div>
            </div>
            <div class="device-actions">
                <button class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cog"></i> Configure
                </button>
                <button class="btn btn-outline-success btn-sm">
                    <i class="fas fa-sync"></i> Calibrate
                </button>
                <button class="btn btn-outline-info btn-sm">
                    <i class="fas fa-chart-line"></i> Data
                </button>
            </div>
        </div>

        <!-- Turbidity Sensor -->
        <div class="device-card">
            <div class="device-header">
                <div class="device-info">
                    <h6>Turbidity Sensor</h6>
                    <div class="device-type">Optical Nephelometer</div>
                </div>
                <div class="device-status offline">
                    <i class="fas fa-circle"></i>
                    Offline
                </div>
            </div>
            <div class="device-metrics">
                <div class="metric">
                    <span class="metric-value">-- NTU</span>
                    <span class="metric-label">Current</span>
                </div>
                <div class="metric">
                    <span class="metric-value">89%</span>
                    <span class="metric-label">Uptime</span>
                </div>
                <div class="metric">
                    <span class="metric-value">12%</span>
                    <span class="metric-label">Battery</span>
                </div>
                <div class="metric">
                    <span class="metric-value">-- dBm</span>
                    <span class="metric-label">Signal</span>
                </div>
            </div>
            <div class="device-actions">
                <button class="btn btn-outline-danger btn-sm">
                    <i class="fas fa-power-off"></i> Reconnect
                </button>
                <button class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-battery-quarter"></i> Replace
                </button>
                <button class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-info"></i> Details
                </button>
            </div>
        </div>

        <!-- Add Device Card -->
        <div class="add-device-card" data-bs-toggle="modal" data-bs-target="#addDeviceModal">
            <div class="add-device-icon">
                <i class="fas fa-plus-circle"></i>
            </div>
            <h6>Add New Device</h6>
            <p class="text-muted mb-0">Connect a new IoT sensor</p>
        </div>
    </div>
</div>

<!-- Add Device Modal -->
<div class="modal fade" id="addDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New IoT Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addDeviceForm">
                    <div class="mb-3">
                        <label class="form-label">Device Type</label>
                        <select class="form-select" id="deviceType">
                            <option value="">Select device type...</option>
                            <option value="temperature">Temperature Sensor</option>
                            <option value="dissolved_oxygen">Dissolved Oxygen Sensor</option>
                            <option value="ph_level">pH Sensor</option>
                            <option value="salinity">Salinity Sensor</option>
                            <option value="turbidity">Turbidity Sensor</option>
                            <option value="ammonia">Ammonia Sensor</option>
                            <option value="nitrite">Nitrite Sensor</option>
                            <option value="water_level">Water Level Sensor</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Device Name</label>
                        <input type="text" class="form-control" id="deviceName" placeholder="Enter device name">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Device ID/MAC Address</label>
                        <input type="text" class="form-control" id="deviceId" placeholder="XX:XX:XX:XX:XX:XX">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Pond Assignment</label>
                        <select class="form-select" id="pondAssignment">
                            <option value="">Select pond...</option>
                            <option value="pond1">Pond 1</option>
                            <option value="pond2">Pond 2</option>
                            <option value="pond3">Pond 3</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Sampling Interval (seconds)</label>
                        <input type="number" class="form-control" id="samplingInterval" value="60" min="10" max="3600">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="addDeviceBtn">Add Device</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- IoT Device Management -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add device functionality
    document.getElementById('addDeviceBtn').addEventListener('click', function() {
        const deviceType = document.getElementById('deviceType').value;
        const deviceName = document.getElementById('deviceName').value;
        const deviceId = document.getElementById('deviceId').value;
        const pondAssignment = document.getElementById('pondAssignment').value;
        const samplingInterval = document.getElementById('samplingInterval').value;
        
        if (!deviceType || !deviceName || !deviceId || !pondAssignment) {
            alert('Please fill in all required fields');
            return;
        }
        
        // Simulate device addition
        showNotification('Adding device...', 'info');
        
        setTimeout(() => {
            showNotification('Device added successfully!', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addDeviceModal')).hide();
            document.getElementById('addDeviceForm').reset();
        }, 2000);
    });
    
    // Device action handlers
    document.addEventListener('click', function(e) {
        if (e.target.closest('.btn')) {
            const btn = e.target.closest('.btn');
            const action = btn.textContent.trim();
            
            if (action.includes('Configure')) {
                showNotification('Opening device configuration...', 'info');
            } else if (action.includes('Calibrate')) {
                showNotification('Starting device calibration...', 'info');
                setTimeout(() => {
                    showNotification('Device calibrated successfully!', 'success');
                }, 3000);
            } else if (action.includes('Data')) {
                showNotification('Loading device data...', 'info');
            } else if (action.includes('Maintain')) {
                showNotification('Scheduling maintenance...', 'warning');
            } else if (action.includes('Reconnect')) {
                showNotification('Attempting to reconnect device...', 'info');
                setTimeout(() => {
                    showNotification('Device reconnected successfully!', 'success');
                }, 4000);
            }
        }
    });
});

function showNotification(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 5000);
}
</script>
{% endblock %}
