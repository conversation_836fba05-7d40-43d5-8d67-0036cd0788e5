<!DOCTYPE html>
<html>
<head>
    <title>Map Loading Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .loading { background: #fff3cd; }
        .success { background: #d1edcc; }
        .error { background: #f8d7da; }
        #console { background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; margin: 20px 0; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        #map { height: 500px; width: 100%; border: 1px solid #ccc; margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Google Maps Loading Diagnostic</h1>
    <div id="status" class="status loading">Initializing diagnostic...</div>
    
    <h3>Console Output:</h3>
    <div id="console"></div>
    
    <h3>Map Test:</h3>
    <div id="map"></div>

    <script>
        function log(message, type = 'info') {
            const console_div = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.innerHTML = `<span style="color: #666">[${timestamp}]</span> <span style="color: ${type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue'}">${message}</span>`;
            console_div.appendChild(entry);
            console_div.scrollTop = console_div.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            log(`Status: ${message}`, type);
        }

        // Global variables for debugging
        window.gm_authFailure = function() {
            log('❌ Google Maps API authentication failed!', 'error');
            updateStatus('Authentication Failed - Check API Key', 'error');
        };

        // Check API key
        const apiKey = '{{ google_maps_api_key }}';
        log(`🔑 API Key: ${apiKey ? apiKey.substring(0, 20) + '...' : 'NOT FOUND'}`, apiKey ? 'success' : 'error');

        // Check for Google object before script loads
        log('🔍 Checking initial state...');
        log(`📄 Document ready state: ${document.readyState}`);
        log(`🌐 User agent: ${navigator.userAgent}`);

        function initMap() {
            log('🎯 initMap() called by Google Maps API', 'success');
            updateStatus('Google Maps API loaded successfully', 'success');
            
            try {
                // Check Google Maps availability
                if (typeof google === 'undefined') {
                    throw new Error('Google object not defined');
                }
                
                if (!google.maps) {
                    throw new Error('google.maps not available');
                }
                
                log(`✅ Google Maps version: ${google.maps.version}`, 'success');
                
                // Create map
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 13.0827, lng: 80.2707 },
                    mapTypeId: 'roadmap'
                });
                
                log('✅ Map created successfully', 'success');
                
                // Add test marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai, India'
                });
                
                log('✅ Marker added successfully', 'success');
                updateStatus('✅ Google Maps working perfectly!', 'success');
                
            } catch (error) {
                log(`❌ Error in initMap: ${error.message}`, 'error');
                updateStatus(`Error: ${error.message}`, 'error');
            }
        }

        function handleMapError() {
            log('❌ Google Maps script failed to load', 'error');
            updateStatus('Script Loading Failed', 'error');
        }

        // Monitor script loading
        log('📡 Starting Google Maps API load...');
        updateStatus('Loading Google Maps API...', 'loading');

        // Timeout check
        setTimeout(() => {
            if (typeof google === 'undefined') {
                log('⏰ Timeout: Google Maps API did not load within 15 seconds', 'error');
                updateStatus('Timeout: API failed to load', 'error');
            }
        }, 15000);

        // Error handling
        window.addEventListener('error', function(event) {
            if (event.filename && event.filename.includes('maps.googleapis.com')) {
                log(`❌ Google Maps script error: ${event.message}`, 'error');
                updateStatus('Script Error Detected', 'error');
            }
        });

        log('🚀 Diagnostic initialized - waiting for Google Maps...');
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
