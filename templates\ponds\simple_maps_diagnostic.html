<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Simple Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin: 20px 0;
        }
        .debug-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps API Test</h1>
        <p>This page tests if Google Maps API loads properly with our current configuration.</p>
        
        <div id="status-container">
            <div class="status info">⏳ Initializing Google Maps test...</div>
        </div>
        
        <div id="map"></div>
        
        <h3>Debug Information</h3>
        <div class="debug-info" id="debug-log">
            🔍 Starting diagnostics...<br>
        </div>
        
        <h3>Configuration</h3>
        <div class="debug-info">
            <strong>API Key:</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}<br>
            <strong>Current URL:</strong> {{ request.build_absolute_uri }}<br>
            <strong>User Agent:</strong> <span id="user-agent"></span><br>
            <strong>Timestamp:</strong> <span id="timestamp"></span>
        </div>
    </div>

    <script>
        // Global error handler
        window.onerror = function(message, source, lineno, colno, error) {
            logDebug('🚨 JavaScript Error: ' + message + ' at ' + source + ':' + lineno);
            return false;
        };

        // Authentication failure handler
        window.gm_authFailure = function() {
            logDebug('🔑 Google Maps Authentication Failed!');
            updateStatus('🔑 Google Maps Authentication Failed! Check API key and billing.', 'error');
        };

        function logDebug(message) {
            const debugLog = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            debugLog.innerHTML += `[${timestamp}] ${message}<br>`;
            debugLog.scrollTop = debugLog.scrollHeight;
            console.log(message);
        }

        function updateStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Set basic info
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toLocaleString();

        logDebug('🔍 Browser: ' + navigator.userAgent);
        logDebug('🔍 Page loaded at: ' + new Date().toLocaleString());

        // Test Google Maps API loading
        function initMap() {
            logDebug('🗺️ initMap() callback triggered');
            updateStatus('🗺️ Google Maps API loaded successfully!', 'success');

            // Check if Google Maps API is available
            if (typeof google === 'undefined' || !google.maps) {
                logDebug('❌ Google Maps API not available after load');
                updateStatus('❌ Google Maps API not available after load', 'error');
                return;
            }

            logDebug('✅ Google object available');
            
            try {
                // Create a simple map
                const map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai coordinates
                    zoom: 10,
                    mapTypeId: google.maps.MapTypeId.ROADMAP
                });

                logDebug('✅ Map created successfully');

                // Add a simple marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location (Chennai)'
                });

                logDebug('✅ Marker added successfully');
                updateStatus('🎉 Google Maps loaded and working perfectly!', 'success');

            } catch (error) {
                logDebug('❌ Error creating map: ' + error.message);
                updateStatus('❌ Error creating map: ' + error.message, 'error');
            }
        }

        // Check if script loads
        logDebug('🔄 Loading Google Maps script...');

        // Set timeout to detect if script doesn't load
        setTimeout(function() {
            if (typeof google === 'undefined') {
                logDebug('⏰ Timeout: Google Maps script did not load within 10 seconds');
                updateStatus('⏰ Timeout: Google Maps script failed to load', 'error');
            }
        }, 10000);
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onload="logDebug('✅ Google Maps script loaded successfully')"
        onerror="logDebug('❌ Failed to load Google Maps script'); updateStatus('❌ Failed to load Google Maps script', 'error');">
    </script>
</body>
</html>
