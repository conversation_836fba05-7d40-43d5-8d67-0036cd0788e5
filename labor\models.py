from django.db import models
from django.conf import settings
from django.utils import timezone
from django.db.models import F, ExpressionWrapper, FloatField
from ponds.models import Pond
import json
import math
from datetime import datetime, timedelta


class Team(models.Model):
    """Team model for organizing workers"""
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class Worker(models.Model):
    """Worker model for labor management"""
    class SkillLevel(models.TextChoices):
        BEGINNER = 'beginner', 'Beginner'
        INTERMEDIATE = 'intermediate', 'Intermediate'
        ADVANCED = 'advanced', 'Advanced'
        EXPERT = 'expert', 'Expert'

    class WorkerStatus(models.TextChoices):
        AVAILABLE = 'available', 'Available'
        WORKING = 'working', 'Working'
        ON_BREAK = 'on_break', 'On Break'
        TRAVELING = 'traveling', 'Traveling'
        IDLE = 'idle', 'Idle'
        OFF_DUTY = 'off_duty', 'Off Duty'


    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='worker_profile'
    )
    name = models.CharField(max_length=100)
    employee_id = models.CharField(max_length=20, unique=True, null=True, blank=True)
    phone = models.CharField(max_length=20, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    skill_level = models.CharField(
        max_length=20,
        choices=SkillLevel.choices,
        default=SkillLevel.BEGINNER
    )
    team = models.ForeignKey(
        Team,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='workers'
    )
    hourly_rate = models.DecimalField(
        max_digits=6,
        decimal_places=2,
        default=0.00
    )
    is_active = models.BooleanField(default=True)
    current_latitude = models.FloatField(null=True, blank=True)
    current_longitude = models.FloatField(null=True, blank=True)
    last_location_update = models.DateTimeField(null=True, blank=True)
    tracking_enabled = models.BooleanField(default=True)
    status = models.CharField(
        max_length=20,
        choices=WorkerStatus.choices,
        default=WorkerStatus.AVAILABLE,
        help_text="Current working status of the worker"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def update_location(self, latitude, longitude):
        """Update worker's current location"""
        self.current_latitude = latitude
        self.current_longitude = longitude
        self.last_location_update = timezone.now()
        self.save(update_fields=['current_latitude', 'current_longitude', 'last_location_update'])

        # Create location log entry
        LocationLog.objects.create(
            worker=self,
            latitude=latitude,
            longitude=longitude,
            timestamp=self.last_location_update
        )

        # Check for geofence events
        self.check_geofence_events()

    def check_geofence_events(self):
        """Check if worker has entered or exited any geofenced areas"""
        if not self.current_latitude or not self.current_longitude:
            return

        # Get all active geofences
        geofences = Geofence.objects.filter(is_active=True)

        for geofence in geofences:
            is_inside = geofence.contains_point(self.current_latitude, self.current_longitude)

            # Check if we need to create an entry or exit event
            last_event = GeofenceEvent.objects.filter(
                worker=self,
                geofence=geofence
            ).order_by('-timestamp').first()

            if not last_event and is_inside:
                # First time detection and worker is inside - create entry event
                GeofenceEvent.objects.create(
                    worker=self,
                    geofence=geofence,
                    event_type='entry',
                    latitude=self.current_latitude,
                    longitude=self.current_longitude
                )
            elif last_event and last_event.event_type == 'exit' and is_inside:
                # Last event was exit and worker is now inside - create entry event
                GeofenceEvent.objects.create(
                    worker=self,
                    geofence=geofence,
                    event_type='entry',
                    latitude=self.current_latitude,
                    longitude=self.current_longitude
                )
            elif last_event and last_event.event_type == 'entry' and not is_inside:
                # Last event was entry and worker is now outside - create exit event
                GeofenceEvent.objects.create(
                    worker=self,
                    geofence=geofence,
                    event_type='exit',
                    latitude=self.current_latitude,
                    longitude=self.current_longitude
                )

    def get_current_weather(self):
        """Get current weather at worker's location"""
        if not self.current_latitude or not self.current_longitude:
            return None

        from weather.models import WeatherAPIConfig

        # Get active weather API config
        api_config = WeatherAPIConfig.objects.filter(is_active=True).first()
        if not api_config:
            return None

        return api_config.fetch_current_weather(self.current_latitude, self.current_longitude)

    def get_weather_forecast(self, days=3):
        """Get weather forecast at worker's location"""
        if not self.current_latitude or not self.current_longitude:
            return None

        from weather.models import WeatherAPIConfig

        # Get active weather API config
        api_config = WeatherAPIConfig.objects.filter(is_active=True).first()
        if not api_config:
            return None

        return api_config.fetch_forecast(self.current_latitude, self.current_longitude, days)

    def get_nearest_pond(self):
        """Find the nearest pond to the worker's current location"""
        if not self.current_latitude or not self.current_longitude:
            return None

        # Get all active ponds with coordinates
        ponds = Pond.objects.filter(status='active').exclude(
            latitude=None,
            longitude=None
        )

        if not ponds.exists():
            return None

        # Find the nearest pond (simple Euclidean distance)
        nearest_pond = None
        min_distance = float('inf')

        for pond in ponds:
            # Calculate squared distance (no need for square root for comparison)
            distance = (pond.latitude - self.current_latitude) ** 2 + (pond.longitude - self.current_longitude) ** 2
            if distance < min_distance:
                min_distance = distance
                nearest_pond = pond

        return nearest_pond

    def calculate_distance_to(self, lat, lng):
        """Calculate distance in meters between worker and given coordinates using Haversine formula"""
        if not self.current_latitude or not self.current_longitude:
            return None

        # Earth radius in meters
        R = 6371000

        # Convert latitude and longitude from degrees to radians
        lat1_rad = math.radians(self.current_latitude)
        lon1_rad = math.radians(self.current_longitude)
        lat2_rad = math.radians(lat)
        lon2_rad = math.radians(lng)

        # Haversine formula
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        return distance


class Task(models.Model):
    """Task model for assigning work to workers"""
    class Status(models.TextChoices):
        PENDING = 'pending', 'Pending'
        IN_PROGRESS = 'in_progress', 'In Progress'
        COMPLETED = 'completed', 'Completed'
        CANCELLED = 'cancelled', 'Cancelled'

    class Priority(models.TextChoices):
        LOW = 'low', 'Low'
        MEDIUM = 'medium', 'Medium'
        HIGH = 'high', 'High'
        URGENT = 'urgent', 'Urgent'
    
    class TaskType(models.TextChoices):
        FEEDING = 'feeding', 'Feeding'
        CLEANING = 'cleaning', 'Cleaning'
        MAINTENANCE = 'maintenance', 'Maintenance'
        WATER_TESTING = 'water_testing', 'Water Testing'
        MONITORING = 'monitoring', 'Monitoring'
        HARVESTING = 'harvesting', 'Harvesting'
        EQUIPMENT_CHECK = 'equipment_check', 'Equipment Check'
        SECURITY = 'security', 'Security'
        OTHER = 'other', 'Other'


    title = models.CharField(max_length=200)
    description = models.TextField()
    pond = models.ForeignKey(
        Pond,
        on_delete=models.CASCADE,
        related_name='tasks',
        null=True,
        blank=True
    )
    assigned_to = models.ForeignKey(
        Worker,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_tasks'
    )
    assigned_team = models.ForeignKey(
        Team,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='team_tasks'
    )
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    priority = models.CharField(
        max_length=20,
        choices=Priority.choices,
        default=Priority.MEDIUM
    )
    due_date = models.DateTimeField(null=True, blank=True)
    estimated_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_tasks'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    completion_notes = models.TextField(blank=True)
    
    # Enhanced fields for better task management
    location_notes = models.TextField(
        blank=True,
        help_text="Specific location details within the pond area"
    )
    required_skills = models.TextField(
        blank=True,
        help_text="Skills needed for this task (comma-separated)"
    )
    safety_requirements = models.TextField(
        blank=True,
        help_text="Safety equipment and precautions required"
    )
    
    task_type = models.CharField(
        max_length=50,
        choices=TaskType.choices,
        default=TaskType.OTHER,
        help_text="Type of task (maintenance, feeding, cleaning, etc.)"
    )
    latitude = models.FloatField(null=True, blank=True, help_text="Task location latitude")
    longitude = models.FloatField(null=True, blank=True, help_text="Task location longitude")

    def __str__(self):
        return self.title

    def start_task(self):
        """Mark task as in progress and record start time"""
        if self.status == 'pending':
            self.status = 'in_progress'
            self.started_at = timezone.now()
            self.save()

    def complete_task(self, notes=''):
        """Mark task as completed and record completion time"""
        if self.status in ['pending', 'in_progress']:
            self.status = 'completed'
            self.completed_at = timezone.now()
            self.completion_notes = notes
            self.save()


class WorkLog(models.Model):
    """Work log for tracking time spent on tasks"""
    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        related_name='work_logs'
    )
    task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
        related_name='work_logs',
        null=True,
        blank=True
    )
    description = models.TextField(blank=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    break_duration = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.worker} - {self.start_time.date()}"

    @property
    def duration(self):
        """Calculate duration of work log"""
        if not self.end_time:
            return None

        total_duration = self.end_time - self.start_time

        if self.break_duration:
            return total_duration - self.break_duration

        return total_duration

    @property
    def hours_worked(self):
        """Calculate hours worked as decimal"""
        if not self.duration:
            return 0

        seconds = self.duration.total_seconds()
        return round(seconds / 3600, 2)


class Schedule(models.Model):
    """Schedule model for planning work shifts"""
    name = models.CharField(max_length=100)  # Original field from migration
    title = models.CharField(max_length=200, null=True, blank=True)  # New field
    description = models.TextField(blank=True)
    team = models.ForeignKey(
        Team,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='schedules'
    )  # Original field from migration
    start_date = models.DateField(null=True, blank=True)  # New field
    end_date = models.DateField(null=True, blank=True)  # New field
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.name or self.title or f"Schedule {self.id}"

    @property
    def duration_days(self):
        """Calculate duration in days"""
        if not self.start_date or not self.end_date:
            return 0
        delta = self.end_date - self.start_date
        return delta.days + 1

    @property
    def is_active(self):
        """Check if schedule is currently active"""
        if not self.start_date or not self.end_date:
            return False
        today = timezone.now().date()
        return self.start_date <= today <= self.end_date


class Shift(models.Model):
    """Shift model for scheduling workers"""
    class ShiftType(models.TextChoices):
        MORNING = 'morning', 'Morning'
        AFTERNOON = 'afternoon', 'Afternoon'
        EVENING = 'evening', 'Evening'
        NIGHT = 'night', 'Night'
        CUSTOM = 'custom', 'Custom'


    schedule = models.ForeignKey(
        Schedule,
        on_delete=models.CASCADE,
        related_name='shifts'
    )
    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        related_name='shifts'
    )
    shift_type = models.CharField(
        max_length=20,
        choices=ShiftType.choices,
        default=ShiftType.CUSTOM
    )
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.worker} - {self.shift_type} - {self.start_time.date()}"

    @property
    def duration(self):
        """Calculate duration of shift"""
        return self.end_time - self.start_time

    @property
    def hours(self):
        """Calculate hours of shift as decimal"""
        seconds = self.duration.total_seconds()
        return round(seconds / 3600, 2)


class LocationLog(models.Model):
    """Model for tracking worker locations"""
    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        related_name='location_logs'
    )
    latitude = models.FloatField()
    longitude = models.FloatField()
    altitude = models.FloatField(null=True, blank=True)
    accuracy = models.FloatField(null=True, blank=True, help_text="Accuracy in meters")
    timestamp = models.DateTimeField(default=timezone.now)
    battery_level = models.FloatField(null=True, blank=True, help_text="Battery level in percentage")
    connection_type = models.CharField(max_length=20, blank=True, help_text="Network connection type (e.g., WiFi, 4G)")

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.worker.name} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"

    def get_weather_at_time(self):
        """Get weather data at this location and time."""
        from weather.models import WeatherData, WeatherStation

        # Find the nearest weather station
        stations = WeatherStation.objects.filter(is_active=True)
        if not stations.exists():
            return None

        # Find the nearest station (simple Euclidean distance)
        nearest_station = None
        min_distance = float('inf')

        for station in stations:
            # Calculate squared distance
            distance = (station.latitude - self.latitude) ** 2 + (station.longitude - self.longitude) ** 2
            if distance < min_distance:
                min_distance = distance
                nearest_station = station

        if not nearest_station:
            return None

        # Get weather data closest to this timestamp
        time_threshold = timedelta(hours=1)
        weather_data = WeatherData.objects.filter(
            station=nearest_station,
            timestamp__gte=self.timestamp - time_threshold,
            timestamp__lte=self.timestamp + time_threshold
        ).order_by('timestamp').first()

        return weather_data


class Geofence(models.Model):
    """Model for geofenced areas"""
    class GeofenceType(models.TextChoices):
        POND = 'pond', 'Pond'
        FARM = 'farm', 'Farm'
        WAREHOUSE = 'warehouse', 'Warehouse'
        RESTRICTED = 'restricted', 'Restricted Area'
        CUSTOM = 'custom', 'Custom'


    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    geofence_type = models.CharField(max_length=20, choices=GeofenceType.choices, default=GeofenceType.CUSTOM)
    boundary = models.TextField(help_text="JSON array of lat/lng coordinates defining the geofence boundary")
    pond = models.ForeignKey(
        Pond,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='geofences'
    )
    radius = models.FloatField(null=True, blank=True, help_text="Radius in meters (for circular geofences)")
    center_latitude = models.FloatField(null=True, blank=True)
    center_longitude = models.FloatField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def contains_point(self, latitude, longitude):
        """Check if a point is inside the geofence"""
        # For circular geofences
        if self.radius and self.center_latitude and self.center_longitude:
            # Calculate distance using Haversine formula
            # Earth radius in meters
            R = 6371000

            # Convert latitude and longitude from degrees to radians
            lat1_rad = math.radians(self.center_latitude)
            lon1_rad = math.radians(self.center_longitude)
            lat2_rad = math.radians(latitude)
            lon2_rad = math.radians(longitude)

            # Haversine formula
            dlon = lon2_rad - lon1_rad
            dlat = lat2_rad - lat1_rad
            a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
            c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
            distance_meters = R * c

            return distance_meters <= self.radius

        # For polygon geofences
        elif self.boundary:
            try:
                # Parse boundary coordinates
                coordinates = json.loads(self.boundary)

                # Ray casting algorithm for point in polygon
                inside = False
                j = len(coordinates) - 1

                for i in range(len(coordinates)):
                    if ((coordinates[i][1] > latitude) != (coordinates[j][1] > latitude) and
                        (longitude < coordinates[i][0] + (coordinates[j][0] - coordinates[i][0]) *
                         (latitude - coordinates[i][1]) / (coordinates[j][1] - coordinates[i][1]))):
                        inside = not inside
                    j = i

                return inside
            except (json.JSONDecodeError, IndexError, TypeError):
                return False

        return False

    def get_workers_inside(self):
        """Get all workers currently inside this geofence"""
        workers = []

        for worker in Worker.objects.filter(is_active=True, tracking_enabled=True).exclude(
            current_latitude=None,
            current_longitude=None
        ):
            if self.contains_point(worker.current_latitude, worker.current_longitude):
                workers.append(worker)

        return workers

    def create_from_pond(pond):
        """Create a geofence from a pond boundary"""
        if not pond.boundary:
            return None

        geofence = Geofence.objects.create(
            name=f"Pond: {pond.name}",
            description=f"Geofence for pond {pond.name}",
            geofence_type='pond',
            boundary=pond.boundary,
            pond=pond
        )

        return geofence


class GeofenceEvent(models.Model):
    """Model for geofence entry/exit events"""
    class EventType(models.TextChoices):
        ENTRY = 'entry', 'Entry'
        EXIT = 'exit', 'Exit'


    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        related_name='geofence_events'
    )
    geofence = models.ForeignKey(
        Geofence,
        on_delete=models.CASCADE,
        related_name='events'
    ) # Consider adding related_name='geofence_events' to Geofence
    event_type = models.CharField(max_length=10, choices=EventType.choices)
    timestamp = models.DateTimeField(default=timezone.now)
    latitude = models.FloatField()
    longitude = models.FloatField()
    notification_sent = models.BooleanField(default=False)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.worker.name} - {self.event_type} - {self.geofence.name} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"

    def save(self, *args, **kwargs):
        # Save the instance first
        super().save(*args, **kwargs)

        # Send notification if not already sent
        if not self.notification_sent:
            try:
                from .notifications import NotificationManager
                NotificationManager.send_geofence_notification(self)

                # Update notification_sent flag
                self.__class__.objects.filter(pk=self.pk).update(notification_sent=True)
            except Exception as e:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Failed to send geofence notification: {e}")


class HeatMapData(models.Model):
    """Model for storing heat map data"""
    date = models.DateField()
    data = models.TextField(help_text="JSON data for heat map visualization")
    data_type = models.CharField(max_length=50, default='worker_density')
    created_at = models.DateTimeField(auto_now_add=True)
    worker = models.ForeignKey(
        Worker,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='heat_map_data'
    )
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    weight = models.FloatField(null=True, blank=True)

    class Meta:
        ordering = ['-date']

    def __str__(self):
        return f"Heat Map Data - {self.data_type} - {self.date.strftime('%Y-%m-%d')}"

    @classmethod
    def generate_worker_density_heatmap(cls, date=None):
        """Generate heat map data for worker density"""
        if date is None:
            date = timezone.now().date()

        # Get all location logs for the specified date
        start_datetime = datetime.combine(date, datetime.min.time())
        end_datetime = datetime.combine(date, datetime.max.time())

        location_logs = LocationLog.objects.filter(
            timestamp__gte=start_datetime,
            timestamp__lte=end_datetime
        )

        # Group location data into grid cells
        grid_size = 0.0001  # Approximately 10m grid cells
        grid = {}

        for log in location_logs:
            # Round coordinates to grid
            grid_lat = round(log.latitude / grid_size) * grid_size
            grid_lng = round(log.longitude / grid_size) * grid_size

            grid_key = f"{grid_lat},{grid_lng}"

            if grid_key in grid:
                grid[grid_key]['count'] += 1
            else:
                grid[grid_key] = {
                    'lat': grid_lat,
                    'lng': grid_lng,
                    'count': 1
                }

        # Convert grid to list of points for heat map
        heatmap_data = [
            {
                'lat': point['lat'],
                'lng': point['lng'],
                'weight': point['count']
            }
            for point in grid.values()
        ]

        # Save heat map data
        heatmap = cls.objects.create(
            date=date,
            data_type='worker_density',
            data=json.dumps(heatmap_data)
        )

        return heatmap


class WeatherLog(models.Model):
    """Model for logging weather data at worker locations"""
    worker = models.ForeignKey(
        Worker,
        on_delete=models.CASCADE,
        related_name='weather_logs'
    )
    latitude = models.FloatField(default=0.0)
    longitude = models.FloatField(default=0.0)
    temperature = models.FloatField(help_text="Temperature in °C", default=25.0)
    humidity = models.FloatField(help_text="Humidity in %", default=60.0)
    pressure = models.FloatField(help_text="Atmospheric pressure in hPa", default=1013.25)
    wind_speed = models.FloatField(help_text="Wind speed in km/h", default=0.0)
    wind_direction = models.CharField(max_length=10, help_text="Wind direction", default="N")
    precipitation = models.FloatField(help_text="Precipitation in mm", default=0)
    condition = models.CharField(max_length=50, help_text="Weather condition", default="Unknown")
    timestamp = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.worker.name} - {self.condition} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"


class WeatherWarning(models.Model):
    """Model for weather warnings."""
    class Condition(models.TextChoices):
        HEAVY_RAIN = 'heavy_rain', 'Heavy Rain'
        THUNDERSTORM = 'thunderstorm', 'Thunderstorm'
        LIGHTNING = 'lightning', 'Lightning'
        HIGH_WIND = 'high_wind', 'High Wind'
        EXTREME_HEAT = 'extreme_heat', 'Extreme Heat'
        FLOOD = 'flood', 'Flood'
        CYCLONE = 'cyclone', 'Cyclone'
        OTHER = 'other', 'Other'

    SEVERITY_CHOICES = [
        ('critical', 'Critical'),
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low')
    ]

    condition = models.CharField(max_length=20, choices=Condition.choices)
    severity = models.CharField(max_length=10, choices=SEVERITY_CHOICES, default='medium')
    description = models.TextField()
    area = models.CharField(max_length=255, default="Unknown Area")
    latitude = models.FloatField(blank=True, null=True, default=0.0)
    longitude = models.FloatField(blank=True, null=True, default=0.0)
    radius = models.FloatField(blank=True, null=True, default=1.0, help_text="Affected radius in kilometers")
    start_time = models.DateTimeField(default=timezone.now)
    end_time = models.DateTimeField(blank=True, null=True)
    source = models.CharField(max_length=100, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.get_condition_display()} - {self.area} - {self.start_time.strftime('%Y-%m-%d')}"

    def deactivate(self):
        """Deactivate the warning"""
        self.is_active = False
        self.end_time = timezone.now()
        self.save()
        return True


class SafetyAlert(models.Model):
    """Model for worker safety alerts."""
    class AlertType(models.TextChoices):
        MAN_DOWN = 'man_down', 'Man Down'
        PANIC = 'panic', 'Panic Button'
        NO_MOVEMENT = 'no_movement', 'No Movement'
        BOUNDARY_VIOLATION = 'boundary_violation', 'Farm Boundary Violation'
        UNAUTHORIZED_AREA = 'unauthorized_area', 'Unauthorized Area'
        WEATHER = 'weather', 'Dangerous Weather'
        OTHER = 'other', 'Other'

    class Severity(models.TextChoices):
        LOW = 'low', 'Low'
        MEDIUM = 'medium', 'Medium'
        HIGH = 'high', 'High'
        CRITICAL = 'critical', 'Critical'

    class Status(models.TextChoices):
        NEW = 'new', 'New'
        ACKNOWLEDGED = 'acknowledged', 'Acknowledged'
        IN_PROGRESS = 'in_progress', 'In Progress'
        RESOLVED = 'resolved', 'Resolved'
        FALSE_ALARM = 'false_alarm', 'False Alarm' # Corrected syntax

    worker = models.ForeignKey(Worker, on_delete=models.CASCADE, related_name='safety_alerts')
    alert_type = models.CharField(max_length=20, choices=AlertType.choices)
    severity = models.CharField(max_length=10, choices=Severity.choices, default=Severity.HIGH)
    status = models.CharField(max_length=15, choices=Status.choices, default=Status.NEW)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    title = models.CharField(max_length=200)
    description = models.TextField()
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='labor_resolved_alerts'
    )
    resolution_notes = models.TextField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.worker.name} - {self.get_alert_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M:%S')}"

    def save(self, *args, **kwargs):
        # Save the instance first
        super().save(*args, **kwargs)

        # Try to send notification
        try:
            from .notifications import NotificationManager

            # Prepare details dictionary
            details = {
                'timestamp': self.created_at,
                'location': f"{self.latitude}, {self.longitude}" if self.latitude and self.longitude else None,
                'message': self.description
            }

            NotificationManager.send_safety_alert(self.worker, self.alert_type, details)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to send safety alert notification: {e}")

    def resolve(self, user, notes=None):
        """Mark the alert as resolved"""
        self.status = 'resolved'
        self.resolved_at = timezone.now()
        self.resolved_by = user
        if notes:
            self.resolution_notes = notes
        self.save()

    def acknowledge(self):
        """Mark the alert as acknowledged"""
        if self.status == 'new':
            self.status = 'acknowledged'
            self.save()

    def mark_in_progress(self):
        """Mark the alert as in progress"""
        if self.status in ['new', 'acknowledged']:
            self.status = 'in_progress'
            self.save()

    def mark_false_alarm(self, user, notes=None):
        """Mark the alert as a false alarm"""
        self.status = 'false_alarm'
        self.resolved_at = timezone.now()
        self.resolved_by = user
        if notes:
            self.resolution_notes = notes
        self.save()