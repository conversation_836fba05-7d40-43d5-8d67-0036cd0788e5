from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Q
import logging
from typing import Dict, List, Optional

from ..models import WeatherAutomationRule, WeatherAutomationLog, WeatherData, WeatherForecast
from iot_integration.models import IoTDevice, <PERSON><PERSON><PERSON><PERSON><PERSON>
from iot_integration.services.device_control_service import DeviceControlService

logger = logging.getLogger(__name__)


class WeatherAutomationService:
    """Advanced weather-based automation service"""
    
    def __init__(self):
        self.device_control = DeviceControlService()
    
    def process_weather_automation_rules(self, weather_data: WeatherData) -> Dict:
        """Process all active weather automation rules"""
        try:
            # Get all active automation rules
            active_rules = WeatherAutomationRule.objects.filter(is_active=True)
            
            results = []
            
            for rule in active_rules:
                try:
                    # Check if rule is in cooldown period
                    if self.is_rule_in_cooldown(rule):
                        continue
                    
                    # Evaluate rule condition
                    condition_result = self.evaluate_rule_condition(rule, weather_data)
                    
                    if condition_result['condition_met']:
                        # Execute rule action
                        action_result = self.execute_rule_action(rule, weather_data, condition_result)
                        
                        # Log execution
                        self.log_rule_execution(rule, weather_data, condition_result, action_result)
                        
                        # Update rule execution tracking
                        rule.last_executed = timezone.now()
                        rule.execution_count += 1
                        if action_result['success']:
                            rule.success_count += 1
                        rule.save()
                        
                        results.append({
                            'rule_id': rule.id,
                            'rule_name': rule.name,
                            'condition_met': True,
                            'action_result': action_result
                        })
                    else:
                        # Log that condition was not met
                        self.log_rule_execution(rule, weather_data, condition_result, {'success': False, 'message': 'Condition not met'})
                        
                        results.append({
                            'rule_id': rule.id,
                            'rule_name': rule.name,
                            'condition_met': False,
                            'reason': condition_result.get('reason', 'Condition not met')
                        })
                
                except Exception as e:
                    logger.error(f"Error processing rule {rule.name}: {e}")
                    results.append({
                        'rule_id': rule.id,
                        'rule_name': rule.name,
                        'error': str(e)
                    })
            
            return {
                'success': True,
                'rules_processed': len(active_rules),
                'rules_executed': len([r for r in results if r.get('condition_met')]),
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Error processing weather automation rules: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def evaluate_rule_condition(self, rule: WeatherAutomationRule, weather_data: WeatherData) -> Dict:
        """Evaluate if a rule condition is met"""
        try:
            condition_type = rule.condition_type
            operator = rule.operator
            threshold = rule.threshold_value
            threshold_max = rule.threshold_value_max
            
            # Get the current value based on condition type
            current_value = self.get_weather_value(weather_data, condition_type)
            
            if current_value is None:
                return {
                    'condition_met': False,
                    'reason': f'Weather data for {condition_type} not available',
                    'current_value': None
                }
            
            # Evaluate condition based on operator
            condition_met = False
            
            if operator == 'gt':
                condition_met = current_value > threshold
            elif operator == 'gte':
                condition_met = current_value >= threshold
            elif operator == 'lt':
                condition_met = current_value < threshold
            elif operator == 'lte':
                condition_met = current_value <= threshold
            elif operator == 'eq':
                condition_met = abs(current_value - threshold) < 0.01  # Float comparison
            elif operator == 'ne':
                condition_met = abs(current_value - threshold) >= 0.01
            elif operator == 'between':
                if threshold_max is not None:
                    condition_met = threshold <= current_value <= threshold_max
                else:
                    condition_met = False
            elif operator == 'contains':
                # For string conditions like weather_condition
                condition_met = str(threshold).lower() in str(current_value).lower()
            
            return {
                'condition_met': condition_met,
                'current_value': current_value,
                'threshold_value': threshold,
                'threshold_max': threshold_max,
                'operator': operator,
                'condition_type': condition_type
            }
            
        except Exception as e:
            logger.error(f"Error evaluating rule condition: {e}")
            return {
                'condition_met': False,
                'error': str(e),
                'current_value': None
            }
    
    def get_weather_value(self, weather_data: WeatherData, condition_type: str) -> Optional[float]:
        """Get weather value based on condition type"""
        value_mapping = {
            'temperature': weather_data.temperature,
            'humidity': weather_data.humidity,
            'wind_speed': weather_data.wind_speed,
            'rainfall': weather_data.rainfall,
            'pressure': weather_data.pressure,
            'weather_condition': weather_data.weather_condition,
        }
        
        return value_mapping.get(condition_type)
    
    def execute_rule_action(self, rule: WeatherAutomationRule, weather_data: WeatherData, condition_result: Dict) -> Dict:
        """Execute the action specified by the rule"""
        try:
            action_type = rule.action_type
            action_params = rule.action_parameters
            
            if action_type == 'device_control':
                return self.execute_device_control_action(rule, action_params, weather_data)
            elif action_type == 'alert':
                return self.execute_alert_action(rule, action_params, weather_data, condition_result)
            elif action_type == 'notification':
                return self.execute_notification_action(rule, action_params, weather_data)
            elif action_type == 'schedule_change':
                return self.execute_schedule_change_action(rule, action_params, weather_data)
            elif action_type == 'emergency_action':
                return self.execute_emergency_action(rule, action_params, weather_data)
            else:
                return {
                    'success': False,
                    'error': f'Unknown action type: {action_type}'
                }
                
        except Exception as e:
            logger.error(f"Error executing rule action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_device_control_action(self, rule: WeatherAutomationRule, action_params: Dict, weather_data: WeatherData) -> Dict:
        """Execute device control action"""
        try:
            device_action = action_params.get('device_action', 'off')  # on, off, adjust
            target_devices = rule.target_devices.all()
            
            if not target_devices.exists():
                return {
                    'success': False,
                    'error': 'No target devices specified for rule'
                }
            
            results = []
            
            for device in target_devices:
                try:
                    if device_action == 'on':
                        result = self.device_control.turn_on_device(device.id)
                    elif device_action == 'off':
                        result = self.device_control.turn_off_device(device.id)
                    elif device_action == 'adjust':
                        # Adjust device based on weather conditions
                        adjustment = self.calculate_weather_based_adjustment(device, weather_data, action_params)
                        result = self.device_control.adjust_device_setting(device.id, adjustment)
                    else:
                        result = {'success': False, 'error': f'Unknown device action: {device_action}'}
                    
                    results.append({
                        'device_id': device.id,
                        'device_name': device.name,
                        'action': device_action,
                        'result': result
                    })
                    
                except Exception as e:
                    results.append({
                        'device_id': device.id,
                        'device_name': device.name,
                        'action': device_action,
                        'error': str(e)
                    })
            
            success_count = len([r for r in results if r.get('result', {}).get('success')])
            
            return {
                'success': success_count > 0,
                'devices_controlled': len(results),
                'successful_controls': success_count,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"Error executing device control action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_alert_action(self, rule: WeatherAutomationRule, action_params: Dict, weather_data: WeatherData, condition_result: Dict) -> Dict:
        """Execute alert action"""
        try:
            alert_severity = action_params.get('severity', 'medium')
            alert_message = action_params.get('message', f'Weather automation rule triggered: {rule.name}')
            
            # Create alerts for target devices
            target_devices = rule.target_devices.all()
            alerts_created = []
            
            for device in target_devices:
                alert = DeviceAlert.objects.create(
                    device=device,
                    alert_type='weather_automation',
                    severity=alert_severity,
                    message=alert_message,
                    data={
                        'rule_id': rule.id,
                        'rule_name': rule.name,
                        'weather_condition': condition_result,
                        'weather_data': {
                            'temperature': weather_data.temperature,
                            'humidity': weather_data.humidity,
                            'wind_speed': weather_data.wind_speed,
                            'rainfall': weather_data.rainfall,
                        }
                    }
                )
                alerts_created.append(alert.id)
            
            return {
                'success': True,
                'alerts_created': len(alerts_created),
                'alert_ids': alerts_created
            }
            
        except Exception as e:
            logger.error(f"Error executing alert action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_notification_action(self, rule: WeatherAutomationRule, action_params: Dict, weather_data: WeatherData) -> Dict:
        """Execute notification action"""
        try:
            # Implementation would send notifications via email, SMS, push notifications
            notification_type = action_params.get('type', 'email')
            recipients = action_params.get('recipients', [])
            message = action_params.get('message', f'Weather automation rule triggered: {rule.name}')
            
            # For now, just log the notification
            logger.info(f"Weather notification: {message} to {recipients} via {notification_type}")
            
            return {
                'success': True,
                'notification_type': notification_type,
                'recipients': recipients,
                'message': message
            }
            
        except Exception as e:
            logger.error(f"Error executing notification action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_schedule_change_action(self, rule: WeatherAutomationRule, action_params: Dict, weather_data: WeatherData) -> Dict:
        """Execute schedule change action"""
        try:
            schedule_type = action_params.get('schedule_type', 'temporary')
            duration = action_params.get('duration_hours', 24)
            new_schedule = action_params.get('new_schedule', {})
            
            target_devices = rule.target_devices.all()
            schedules_changed = []
            
            for device in target_devices:
                # Implementation would update device schedules
                # For now, just log the schedule change
                logger.info(f"Schedule change for device {device.name}: {new_schedule}")
                schedules_changed.append(device.id)
            
            return {
                'success': True,
                'schedules_changed': len(schedules_changed),
                'device_ids': schedules_changed,
                'schedule_type': schedule_type,
                'duration_hours': duration
            }
            
        except Exception as e:
            logger.error(f"Error executing schedule change action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def execute_emergency_action(self, rule: WeatherAutomationRule, action_params: Dict, weather_data: WeatherData) -> Dict:
        """Execute emergency action"""
        try:
            emergency_type = action_params.get('emergency_type', 'shutdown')
            
            if emergency_type == 'shutdown':
                # Emergency shutdown of all target devices
                target_devices = rule.target_devices.all()
                shutdown_results = []
                
                for device in target_devices:
                    result = self.device_control.emergency_stop_device(device.id)
                    shutdown_results.append({
                        'device_id': device.id,
                        'device_name': device.name,
                        'result': result
                    })
                
                return {
                    'success': True,
                    'emergency_type': emergency_type,
                    'devices_shutdown': len(shutdown_results),
                    'results': shutdown_results
                }
            
            return {
                'success': False,
                'error': f'Unknown emergency type: {emergency_type}'
            }
            
        except Exception as e:
            logger.error(f"Error executing emergency action: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def calculate_weather_based_adjustment(self, device: IoTDevice, weather_data: WeatherData, action_params: Dict) -> Dict:
        """Calculate device adjustment based on weather conditions"""
        try:
            if device.device_type == 'aerator':
                # Adjust aerator based on temperature and wind
                base_level = action_params.get('base_level', 50)
                temp_factor = (weather_data.temperature - 25) * 2  # Increase 2% per degree above 25°C
                wind_factor = -weather_data.wind_speed * 5  # Decrease 5% per m/s wind speed
                
                adjustment_level = max(10, min(100, base_level + temp_factor + wind_factor))
                
                return {
                    'setting_type': 'operation_level',
                    'value': adjustment_level,
                    'unit': 'percentage'
                }
            
            elif device.device_type == 'water_pump':
                # Adjust pump based on rainfall
                if weather_data.rainfall > 10:  # Heavy rain
                    return {
                        'setting_type': 'operation_mode',
                        'value': 'reduced',
                        'reason': 'Heavy rainfall detected'
                    }
                else:
                    return {
                        'setting_type': 'operation_mode',
                        'value': 'normal',
                        'reason': 'Normal weather conditions'
                    }
            
            return {
                'setting_type': 'no_adjustment',
                'value': 0,
                'reason': 'No weather-based adjustment defined for this device type'
            }
            
        except Exception as e:
            logger.error(f"Error calculating weather-based adjustment: {e}")
            return {
                'setting_type': 'error',
                'value': 0,
                'error': str(e)
            }
    
    def is_rule_in_cooldown(self, rule: WeatherAutomationRule) -> bool:
        """Check if rule is in cooldown period"""
        if not rule.last_executed:
            return False
        
        cooldown_end = rule.last_executed + rule.cooldown_period
        return timezone.now() < cooldown_end
    
    def log_rule_execution(self, rule: WeatherAutomationRule, weather_data: WeatherData, condition_result: Dict, action_result: Dict):
        """Log rule execution"""
        try:
            execution_status = 'success' if action_result.get('success') else 'failed'
            if not condition_result.get('condition_met'):
                execution_status = 'skipped'
            
            WeatherAutomationLog.objects.create(
                rule=rule,
                execution_status=execution_status,
                weather_data={
                    'temperature': weather_data.temperature,
                    'humidity': weather_data.humidity,
                    'wind_speed': weather_data.wind_speed,
                    'rainfall': weather_data.rainfall,
                    'pressure': weather_data.pressure,
                    'weather_condition': weather_data.weather_condition,
                },
                condition_met=condition_result.get('condition_met', False),
                condition_value=condition_result.get('current_value', 0),
                action_taken=action_result,
                action_result=action_result,
                error_message=action_result.get('error', '') if not action_result.get('success') else ''
            )
            
        except Exception as e:
            logger.error(f"Error logging rule execution: {e}")
    
    def get_weather_automation_summary(self, days: int = 7) -> Dict:
        """Get summary of weather automation activity"""
        try:
            end_time = timezone.now()
            start_time = end_time - timedelta(days=days)
            
            # Get execution logs for the period
            logs = WeatherAutomationLog.objects.filter(
                executed_at__gte=start_time,
                executed_at__lte=end_time
            )
            
            total_executions = logs.count()
            successful_executions = logs.filter(execution_status='success').count()
            failed_executions = logs.filter(execution_status='failed').count()
            skipped_executions = logs.filter(execution_status='skipped').count()
            
            # Get most active rules
            rule_activity = {}
            for log in logs:
                rule_name = log.rule.name
                if rule_name not in rule_activity:
                    rule_activity[rule_name] = {'total': 0, 'success': 0, 'failed': 0}
                
                rule_activity[rule_name]['total'] += 1
                if log.execution_status == 'success':
                    rule_activity[rule_name]['success'] += 1
                elif log.execution_status == 'failed':
                    rule_activity[rule_name]['failed'] += 1
            
            return {
                'success': True,
                'period_days': days,
                'total_executions': total_executions,
                'successful_executions': successful_executions,
                'failed_executions': failed_executions,
                'skipped_executions': skipped_executions,
                'success_rate': (successful_executions / total_executions * 100) if total_executions > 0 else 0,
                'rule_activity': rule_activity,
                'active_rules': WeatherAutomationRule.objects.filter(is_active=True).count(),
            }
            
        except Exception as e:
            logger.error(f"Error getting weather automation summary: {e}")
            return {
                'success': False,
                'error': str(e)
            }
