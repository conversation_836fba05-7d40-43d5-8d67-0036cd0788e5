{% extends "base.html" %}
{% load static %}

{% block title %}Unified Map - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .unified-map-container {
        position: relative;
        height: calc(100vh - 80px);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 3px;
    }

    .map-inner {
        height: 100%;
        border-radius: 17px;
        overflow: hidden;
        background: white;
    }

    #unified-map {
        width: 100%;
        height: 100%;
    }

    .floating-panel {
        position: absolute;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        backdrop-filter: blur(15px);
        padding: 25px;
        z-index: 100;
        transition: all 0.3s ease;
    }

    .floating-panel:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    }

    .control-panel {
        top: 30px;
        right: 30px;
        min-width: 320px;
        max-height: calc(100vh - 200px);
        overflow-y: auto;
    }

    .info-panel {
        top: 30px;
        left: 30px;
        min-width: 350px;
    }

    .stats-panel {
        bottom: 30px;
        left: 30px;
        min-width: 280px;
    }

    .legend-panel {
        bottom: 30px;
        right: 30px;
        min-width: 200px;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.98);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 17px;
    }

    .loading-content {
        text-align: center;
        background: rgba(255, 255, 255, 0.9);
        padding: 40px;
        border-radius: 20px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    }

    .loading-spinner {
        width: 60px;
        height: 60px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .panel-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
    }

    .panel-header h6 {
        margin: 0;
        color: #333;
        font-weight: 600;
    }

    .panel-header i {
        color: #667eea;
        margin-right: 10px;
    }

    .control-group {
        margin-bottom: 20px;
    }

    .control-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #555;
        font-size: 14px;
    }

    .btn-unified {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
        width: 100%;
    }

    .btn-unified:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px;
        border-radius: 12px;
        margin-bottom: 15px;
        text-align: center;
    }

    .stat-number {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 12px;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        padding: 8px;
        border-radius: 8px;
        background: rgba(102, 126, 234, 0.05);
    }

    .legend-icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 12px;
        border: 2px solid white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }

    .legend-icon.farm {
        background: #28a745;
    }

    .legend-icon.pond {
        background: #007bff;
    }

    .info-content {
        background: rgba(102, 126, 234, 0.05);
        padding: 15px;
        border-radius: 10px;
        border-left: 4px solid #667eea;
    }

    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #667eea;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    /* Enhanced CSS for info windows */
    .info-window-enhanced {
        max-width: 350px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .info-header {
        border-bottom: 2px solid #f0f0f0;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }

    .info-body {
        margin-bottom: 15px;
    }

    .info-footer {
        border-top: 1px solid #f0f0f0;
        padding-top: 10px;
    }

    .info-window-enhanced .row {
        margin: 0;
    }

    .info-window-enhanced .col-6,
    .info-window-enhanced .col-12 {
        padding: 0 5px;
    }

    .info-window-enhanced small {
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .info-window-enhanced p {
        font-size: 14px;
        line-height: 1.4;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-4">
    <div class="unified-map-container">
        <div class="map-inner">
            <!-- Loading Overlay -->
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <h4 class="text-primary mb-2">Loading Unified Map</h4>
                    <p class="text-muted">Preparing your comprehensive view...</p>
                </div>
            </div>

            <!-- Map -->
            <div id="unified-map"></div>

            <!-- Info Panel -->
            <div class="floating-panel info-panel">
                <div class="panel-header">
                    <i class="fas fa-info-circle"></i>
                    <h6>Map Information</h6>
                </div>
                <div class="info-content">
                    <h6 class="mb-2">🗺️ Unified Farm & Pond Map</h6>
                    <p class="mb-2 small">This interactive map shows all your farms and ponds with real-time data and navigation capabilities.</p>
                    <p class="mb-0 small"><strong>Click markers</strong> to view details and navigate to specific locations.</p>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="floating-panel control-panel">
                <div class="panel-header">
                    <i class="fas fa-sliders-h"></i>
                    <h6>Map Controls</h6>
                </div>

                <div class="control-group">
                    <label>
                        <span>Show Farms</span>
                        <label class="toggle-switch float-end">
                            <input type="checkbox" id="show-farms" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>

                <div class="control-group">
                    <label>
                        <span>Show Ponds</span>
                        <label class="toggle-switch float-end">
                            <input type="checkbox" id="show-ponds" checked>
                            <span class="slider"></span>
                        </label>
                    </label>
                </div>

                <div class="control-group">
                    <label for="map-type">Map Type</label>
                    <select id="map-type" class="form-select">
                        <option value="hybrid">Hybrid</option>
                        <option value="satellite">Satellite</option>
                        <option value="roadmap">Roadmap</option>
                        <option value="terrain">Terrain</option>
                    </select>
                </div>

                <div class="control-group">
                    <button type="button" class="btn btn-unified" onclick="fitToView()">
                        <i class="fas fa-expand-arrows-alt me-2"></i>Fit to View
                    </button>
                </div>

                <div class="control-group">
                    <button type="button" class="btn btn-unified" onclick="refreshMap()">
                        <i class="fas fa-sync-alt me-2"></i>Refresh Map
                    </button>
                </div>
            </div>

            <!-- Stats Panel -->
            <div class="floating-panel stats-panel">
                <div class="panel-header">
                    <i class="fas fa-chart-bar"></i>
                    <h6>Statistics</h6>
                </div>

                <div class="stat-card">
                    <div class="stat-number" id="total-farms">{{ farms.count }}</div>
                    <div class="stat-label">Total Farms</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number" id="total-ponds">{{ ponds.count }}</div>
                    <div class="stat-label">Total Ponds</div>
                </div>

                <div class="stat-card">
                    <div class="stat-number" id="visible-items">0</div>
                    <div class="stat-label">Visible Items</div>
                </div>
            </div>

            <!-- Legend Panel -->
            <div class="floating-panel legend-panel">
                <div class="panel-header">
                    <i class="fas fa-map-signs"></i>
                    <h6>Legend</h6>
                </div>

                <div class="legend-item">
                    <div class="legend-icon farm"></div>
                    <span>Farms</span>
                </div>

                <div class="legend-item">
                    <div class="legend-icon pond"></div>
                    <span>Ponds</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global variables
let map;
let farmMarkers = [];
let pondMarkers = [];
let infoWindow;

// Data from Django
const farmsData = {{ farms_data|safe }};
const pondsData = {{ ponds_data|safe }};
const googleMapsApiKey = '{{ google_maps_api_key }}';

// Debug data
console.log('Debug: Google Maps API Key:', googleMapsApiKey);
console.log('Debug: Farms Data:', farmsData);
console.log('Debug: Ponds Data:', pondsData);

// Initialize map
function initMap() {
    console.log('Initializing unified map...');
    
    try {
        // Check if Google Maps is loaded
        if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
            throw new Error('Google Maps API not loaded');
        }
        
        console.log('Google Maps API version:', google.maps.version);
        
        // Check if the map container exists
        const mapContainer = document.getElementById('unified-map');
        if (!mapContainer) {
            throw new Error('Map container #unified-map not found');
        }
        
        console.log('Map container found, creating map...');
    
        // Create map with enhanced styling
        map = new google.maps.Map(mapContainer, {
            zoom: 10,
            center: { lat: 13.0827, lng: 80.2707 },
            mapTypeId: 'hybrid',
            styles: [
                {
                    featureType: 'water',
                    elementType: 'geometry',
                    stylers: [{ color: '#193341' }]
                },
                {
                    featureType: 'landscape',
                    elementType: 'geometry',
                    stylers: [{ color: '#2c5234' }]
                },
                {
                    featureType: 'poi',
                    elementType: 'labels',
                    stylers: [{ visibility: 'off' }]
                }
            ],
            mapTypeControl: false,
            streetViewControl: false,
            fullscreenControl: true,
            zoomControl: true,
            zoomControlOptions: {
                position: google.maps.ControlPosition.LEFT_BOTTOM
            }
        });

        console.log('Map created successfully');

        // Create info window
        infoWindow = new google.maps.InfoWindow({
            maxWidth: 350
        });

        // Add markers
        addFarmMarkers();
        addPondMarkers();

        // Setup event listeners
        setupEventListeners();

        // Fit map to markers
        fitToView();

        // Hide loading overlay
        setTimeout(() => {
            document.getElementById('loading-overlay').style.display = 'none';
        }, 1000);

        console.log('Unified map initialized successfully');
        
    } catch (error) {
        console.error('Error initializing map:', error);
        
        // Show error in loading overlay
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.innerHTML = `
                <div class="loading-content">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h4 class="text-danger">Map Initialization Error</h4>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="window.location.reload()">
                        <i class="fas fa-refresh me-2"></i>Retry
                    </button>
                </div>
            `;
        }
    }
}

// Add farm markers with enhanced styling
function addFarmMarkers() {
    farmsData.forEach(farm => {
        const marker = new google.maps.Marker({
            position: { lat: farm.latitude, lng: farm.longitude },
            map: map,
            title: farm.name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36">
                        <circle cx="18" cy="18" r="14" fill="#28a745" stroke="#fff" stroke-width="3"/>
                        <circle cx="18" cy="18" r="10" fill="#34ce57" opacity="0.8"/>
                        <text x="18" y="23" text-anchor="middle" fill="white" font-size="14" font-weight="bold">F</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(36, 36),
                anchor: new google.maps.Point(18, 18)
            },
            animation: google.maps.Animation.DROP
        });

        marker.farmData = farm;

        marker.addListener('click', () => {
            showFarmInfo(farm, marker);
        });

        farmMarkers.push(marker);
    });

    console.log(`Added ${farmMarkers.length} farm markers`);
}

// Add pond markers with enhanced styling
function addPondMarkers() {
    pondsData.forEach(pond => {
        const statusColor = getStatusColor(pond.status);

        const marker = new google.maps.Marker({
            position: { lat: pond.latitude, lng: pond.longitude },
            map: map,
            title: pond.name,
            icon: {
                url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
                        <circle cx="16" cy="16" r="12" fill="${statusColor}" stroke="#fff" stroke-width="3"/>
                        <circle cx="16" cy="16" r="8" fill="${statusColor}" opacity="0.7"/>
                        <text x="16" y="20" text-anchor="middle" fill="white" font-size="12" font-weight="bold">P</text>
                    </svg>
                `),
                scaledSize: new google.maps.Size(32, 32),
                anchor: new google.maps.Point(16, 16)
            },
            animation: google.maps.Animation.DROP
        });

        marker.pondData = pond;

        marker.addListener('click', () => {
            showPondInfo(pond, marker);
        });

        pondMarkers.push(marker);
    });

    console.log(`Added ${pondMarkers.length} pond markers`);
}

// Get status color for ponds
function getStatusColor(status) {
    switch(status) {
        case 'active': return '#007bff';
        case 'inactive': return '#6c757d';
        case 'maintenance': return '#ffc107';
        case 'harvested': return '#17a2b8';
        default: return '#007bff';
    }
}

// Show enhanced farm info
function showFarmInfo(farm, marker) {
    const content = `
        <div class="info-window-enhanced">
            <div class="info-header">
                <h5 class="mb-2">
                    <i class="fas fa-building text-success me-2"></i>
                    ${farm.name}
                </h5>
            </div>
            <div class="info-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Location</small>
                        <p class="mb-2">${farm.location || 'Not specified'}</p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Size</small>
                        <p class="mb-2">${farm.size} hectares</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Ponds</small>
                        <p class="mb-2"><strong>${farm.pond_count}</strong></p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Contact</small>
                        <p class="mb-2">${farm.contact_person || 'Not specified'}</p>
                    </div>
                </div>
            </div>
            <div class="info-footer">
                <a href="/ponds/farm/${farm.id}/" class="btn btn-success btn-sm me-2">
                    <i class="fas fa-eye me-1"></i>View Farm
                </a>
                <button class="btn btn-outline-primary btn-sm" onclick="zoomToFarm(${farm.latitude}, ${farm.longitude})">
                    <i class="fas fa-search-plus me-1"></i>Zoom
                </button>
            </div>
        </div>
    `;

    infoWindow.setContent(content);
    infoWindow.open(map, marker);
}

// Show enhanced pond info
function showPondInfo(pond, marker) {
    const content = `
        <div class="info-window-enhanced">
            <div class="info-header">
                <h5 class="mb-2">
                    <i class="fas fa-fish text-primary me-2"></i>
                    ${pond.name}
                </h5>
            </div>
            <div class="info-body">
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Farm</small>
                        <p class="mb-2">${pond.farm_name}</p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Size</small>
                        <p class="mb-2">${pond.size} m²</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">Status</small>
                        <p class="mb-2"><span class="badge bg-${getStatusBadgeColor(pond.status)}">${pond.status}</span></p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Species</small>
                        <p class="mb-2">${pond.species || 'Not specified'}</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <small class="text-muted">Water Quality</small>
                        <p class="mb-2">${pond.water_quality || 'Not assessed'}</p>
                    </div>
                </div>
            </div>
            <div class="info-footer">
                <a href="/ponds/${pond.id}/" class="btn btn-primary btn-sm me-2">
                    <i class="fas fa-eye me-1"></i>View Pond
                </a>
                <button class="btn btn-outline-success btn-sm" onclick="zoomToPond(${pond.latitude}, ${pond.longitude})">
                    <i class="fas fa-search-plus me-1"></i>Zoom
                </button>
            </div>
        </div>
    `;

    infoWindow.setContent(content);
    infoWindow.open(map, marker);
}

// Get status badge color
function getStatusBadgeColor(status) {
    switch(status) {
        case 'active': return 'success';
        case 'inactive': return 'secondary';
        case 'maintenance': return 'warning';
        case 'harvested': return 'info';
        default: return 'secondary';
    }
}

// Setup event listeners
function setupEventListeners() {
    // Show/hide farms
    document.getElementById('show-farms').addEventListener('change', function() {
        const show = this.checked;
        farmMarkers.forEach(marker => {
            marker.setVisible(show);
        });
        updateStats();
    });

    // Show/hide ponds
    document.getElementById('show-ponds').addEventListener('change', function() {
        const show = this.checked;
        pondMarkers.forEach(marker => {
            marker.setVisible(show);
        });
        updateStats();
    });

    // Map type change
    document.getElementById('map-type').addEventListener('change', function() {
        map.setMapTypeId(this.value);
    });

    // Update stats initially
    updateStats();
}

// Update statistics
function updateStats() {
    const visibleFarms = farmMarkers.filter(marker => marker.getVisible()).length;
    const visiblePonds = pondMarkers.filter(marker => marker.getVisible()).length;
    const totalVisible = visibleFarms + visiblePonds;

    document.getElementById('visible-items').textContent = totalVisible;
}

// Fit map to view
function fitToView() {
    const bounds = new google.maps.LatLngBounds();
    let hasMarkers = false;

    // Add visible farm markers to bounds
    farmMarkers.forEach(marker => {
        if (marker.getVisible()) {
            bounds.extend(marker.getPosition());
            hasMarkers = true;
        }
    });

    // Add visible pond markers to bounds
    pondMarkers.forEach(marker => {
        if (marker.getVisible()) {
            bounds.extend(marker.getPosition());
            hasMarkers = true;
        }
    });

    if (hasMarkers) {
        map.fitBounds(bounds);
        // Ensure minimum zoom level
        google.maps.event.addListenerOnce(map, 'bounds_changed', function() {
            if (map.getZoom() > 15) {
                map.setZoom(15);
            }
        });
    }
}

// Zoom to specific farm
function zoomToFarm(lat, lng) {
    map.setCenter({ lat, lng });
    map.setZoom(14);
}

// Zoom to specific pond
function zoomToPond(lat, lng) {
    map.setCenter({ lat, lng });
    map.setZoom(16);
}

// Refresh map
function refreshMap() {
    window.location.reload();
}

// Handle map loading errors
function handleMapError() {
    console.error('Google Maps API failed to load');
    document.getElementById('loading-overlay').innerHTML = `
        <div class="loading-content">
            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
            <h4 class="text-danger">Map Loading Error</h4>
            <p class="text-muted">Unable to load Google Maps. Please check your internet connection and API key.</p>
            <p class="text-muted mt-3"><strong>API Key:</strong> ${googleMapsApiKey}</p>
            <button class="btn btn-primary" onclick="window.location.reload()">
                <i class="fas fa-refresh me-2"></i>Retry
            </button>
        </div>
    `;
}

// Initialize on DOM ready with timeout detection
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, waiting for Google Maps API...');
    
    // Add timeout to check if Google Maps loads
    setTimeout(function() {
        if (typeof google === 'undefined') {
            console.error('Google Maps API failed to load after 10 seconds');
            document.getElementById('loading-overlay').innerHTML = `
                <div class="loading-content">
                    <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                    <h4 class="text-danger">Google Maps API Loading Error</h4>
                    <p class="text-muted">The Google Maps API failed to load. This could be due to:</p>
                    <ul class="text-start text-muted">
                        <li>Invalid API key</li>
                        <li>Network connectivity issues</li>
                        <li>API quota exceeded</li>
                        <li>Domain restrictions</li>
                    </ul>
                    <p class="text-muted mt-3"><strong>API Key:</strong> ${googleMapsApiKey}</p>
                    <button class="btn btn-primary" onclick="window.location.reload()">
                        <i class="fas fa-refresh me-2"></i>Retry
                    </button>
                </div>
            `;
        }
    }, 10000);
});
</script>

<!-- Load Google Maps API -->
<script async defer
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&onerror=handleMapError">
</script>
{% endblock %}
