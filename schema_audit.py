#!/usr/bin/env python3
"""
Database Schema Audit Script
Reviews all models for consistency, indexing, and relationships
"""

import os
import sys
import django
from django.apps import apps
from django.db import models, connection
from collections import defaultdict

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

class SchemaAuditor:
    def __init__(self):
        self.issues = []
        self.recommendations = []
        self.stats = defaultdict(int)
    
    def audit_models(self):
        """Audit all Django models for issues"""
        print("🔍 Auditing Django models...")
        
        for app_config in apps.get_app_configs():
            if app_config.name.startswith('shrimp_farm_guardian') or app_config.name in [
                'core', 'users', 'ponds', 'water_quality', 'feed', 'medicine',
                'lunar', 'weather', 'alerts', 'harvest', 'electricity', 'disease',
                'labor', 'ai_ml', 'iot_integration'
            ]:
                self._audit_app_models(app_config)
    
    def _audit_app_models(self, app_config):
        """Audit models in a specific app"""
        print(f"📦 Auditing {app_config.name}...")
        
        for model in app_config.get_models():
            self.stats['total_models'] += 1
            self._audit_model(model)
    
    def _audit_model(self, model):
        """Audit a specific model"""
        model_name = f"{model._meta.app_label}.{model._meta.model_name}"
        
        # Check for missing indexes
        self._check_indexes(model, model_name)
        
        # Check relationships
        self._check_relationships(model, model_name)
        
        # Check field types and constraints
        self._check_fields(model, model_name)
        
        # Check Meta options
        self._check_meta_options(model, model_name)
    
    def _check_indexes(self, model, model_name):
        """Check for missing or inefficient indexes"""
        # Check for foreign keys without indexes
        for field in model._meta.get_fields():
            if isinstance(field, models.ForeignKey):
                if not any(field.name in idx.fields for idx in model._meta.indexes):
                    self.issues.append({
                        'type': 'missing_index',
                        'model': model_name,
                        'field': field.name,
                        'severity': 'medium',
                        'message': f"Foreign key {field.name} should have an index"
                    })
        
        # Check for commonly queried fields without indexes
        common_query_fields = ['created_at', 'updated_at', 'status', 'is_active']
        for field_name in common_query_fields:
            try:
                field = model._meta.get_field(field_name)
                if not field.db_index and not any(field_name in idx.fields for idx in model._meta.indexes):
                    self.recommendations.append({
                        'type': 'index_recommendation',
                        'model': model_name,
                        'field': field_name,
                        'message': f"Consider adding index to commonly queried field {field_name}"
                    })
            except:
                pass
    
    def _check_relationships(self, model, model_name):
        """Check model relationships for issues"""
        for field in model._meta.get_fields():
            if isinstance(field, models.ForeignKey):
                # Check for missing on_delete
                if not hasattr(field, 'on_delete') or field.on_delete is None:
                    self.issues.append({
                        'type': 'missing_on_delete',
                        'model': model_name,
                        'field': field.name,
                        'severity': 'high',
                        'message': f"ForeignKey {field.name} missing on_delete parameter"
                    })
                
                # Check for CASCADE on critical relationships
                if field.on_delete == models.CASCADE:
                    self.recommendations.append({
                        'type': 'cascade_warning',
                        'model': model_name,
                        'field': field.name,
                        'message': f"CASCADE delete on {field.name} - ensure this is intentional"
                    })
    
    def _check_fields(self, model, model_name):
        """Check field definitions for issues"""
        for field in model._meta.get_fields():
            if hasattr(field, 'max_length'):
                # Check for very large max_length values
                if field.max_length and field.max_length > 1000:
                    self.recommendations.append({
                        'type': 'large_field',
                        'model': model_name,
                        'field': field.name,
                        'message': f"Field {field.name} has large max_length ({field.max_length})"
                    })
            
            # Check for missing help_text on complex fields
            if isinstance(field, (models.JSONField, models.TextField)) and not field.help_text:
                self.recommendations.append({
                    'type': 'missing_help_text',
                    'model': model_name,
                    'field': field.name,
                    'message': f"Complex field {field.name} should have help_text"
                })
    
    def _check_meta_options(self, model, model_name):
        """Check model Meta options"""
        meta = model._meta
        
        # Check for missing ordering
        if not meta.ordering:
            self.recommendations.append({
                'type': 'missing_ordering',
                'model': model_name,
                'message': f"Model {model_name} should define default ordering"
            })
        
        # Check for missing verbose names
        if not meta.verbose_name or meta.verbose_name == model_name.split('.')[-1]:
            self.recommendations.append({
                'type': 'missing_verbose_name',
                'model': model_name,
                'message': f"Model {model_name} should define verbose_name"
            })
    
    def check_database_constraints(self):
        """Check database-level constraints"""
        print("🔍 Checking database constraints...")
        
        with connection.cursor() as cursor:
            # Check for tables without primary keys
            cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name NOT IN (
                    SELECT table_name 
                    FROM information_schema.table_constraints 
                    WHERE constraint_type = 'PRIMARY KEY'
                )
            """)
            
            tables_without_pk = cursor.fetchall()
            for table in tables_without_pk:
                self.issues.append({
                    'type': 'missing_primary_key',
                    'table': table[0],
                    'severity': 'critical',
                    'message': f"Table {table[0]} missing primary key"
                })
    
    def generate_report(self):
        """Generate audit report"""
        print("\n" + "="*60)
        print("📊 DATABASE SCHEMA AUDIT REPORT")
        print("="*60)
        
        print(f"\n📈 Statistics:")
        print(f"  Total Models: {self.stats['total_models']}")
        print(f"  Issues Found: {len(self.issues)}")
        print(f"  Recommendations: {len(self.recommendations)}")
        
        if self.issues:
            print(f"\n🚨 Critical Issues:")
            for issue in sorted(self.issues, key=lambda x: x.get('severity', 'low')):
                severity_icon = {'critical': '🔴', 'high': '🟠', 'medium': '🟡'}.get(issue.get('severity', 'medium'), '🟡')
                print(f"  {severity_icon} {issue['message']}")
        
        if self.recommendations:
            print(f"\n💡 Recommendations:")
            for rec in self.recommendations[:10]:  # Show top 10
                print(f"  💡 {rec['message']}")
        
        print(f"\n✅ Audit completed!")

if __name__ == "__main__":
    auditor = SchemaAuditor()
    auditor.audit_models()
    auditor.check_database_constraints()
    auditor.generate_report()
