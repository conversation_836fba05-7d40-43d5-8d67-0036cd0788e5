<!DOCTYPE html>
<html>
<head>
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps API Test</h1>
        
        <div id="status" class="status info">
            🔄 Initializing Google Maps test...
        </div>
        
        <div id="api-info">
            <p><strong>API Key:</strong> {{ google_maps_api_key }}</p>
            <p><strong>API URL:</strong> <span id="api-url"></span></p>
        </div>
        
        <div id="map"></div>
        
        <div id="debug-info">
            <h3>Debug Information:</h3>
            <ul id="debug-list"></ul>
        </div>
    </div>

    <script>
        let debugList = document.getElementById('debug-list');
        let statusDiv = document.getElementById('status');
        
        function addDebug(message) {
            console.log(message);
            let li = document.createElement('li');
            li.textContent = new Date().toLocaleTimeString() + ': ' + message;
            debugList.appendChild(li);
        }
        
        function updateStatus(message, type = 'info') {
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }
        
        // Test function
        function initMap() {
            addDebug('🎯 initMap() callback called');
            
            try {
                // Check Google object
                if (typeof google === 'undefined') {
                    throw new Error('Google object not available');
                }
                addDebug('✅ Google object available');
                
                // Check Google Maps
                if (!google.maps) {
                    throw new Error('Google Maps not available');
                }
                addDebug('✅ Google Maps available');
                
                // Get map element
                const mapElement = document.getElementById('map');
                if (!mapElement) {
                    throw new Error('Map element not found');
                }
                addDebug('✅ Map element found');
                
                // Create map
                const map = new google.maps.Map(mapElement, {
                    zoom: 10,
                    center: { lat: 13.0827, lng: 80.2707 },
                    mapTypeId: 'roadmap'
                });
                addDebug('✅ Map created successfully');
                
                // Add a simple marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Marker'
                });
                addDebug('✅ Marker added successfully');
                
                updateStatus('🎉 Google Maps loaded successfully!', 'success');
                
            } catch (error) {
                addDebug('❌ Error: ' + error.message);
                updateStatus('❌ Error: ' + error.message, 'error');
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            addDebug('📄 DOM loaded');
            
            // Show API URL
            const apiKey = '{{ google_maps_api_key }}';
            const apiUrl = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=drawing,geometry&callback=initMap`;
            document.getElementById('api-url').textContent = apiUrl;
            
            addDebug('🔑 API Key: ' + apiKey);
            addDebug('🔗 API URL: ' + apiUrl);
            
            // Check if Google is already loaded
            if (typeof google !== 'undefined' && google.maps) {
                addDebug('📍 Google Maps already loaded');
                initMap();
            } else {
                addDebug('⏳ Waiting for Google Maps API...');
                updateStatus('⏳ Loading Google Maps API...', 'info');
            }
            
            // Set timeout for error detection
            setTimeout(() => {
                if (typeof google === 'undefined' || !google.maps) {
                    addDebug('⏰ Timeout: Google Maps failed to load in 10 seconds');
                    updateStatus('⏰ Timeout: Google Maps failed to load', 'error');
                }
            }, 10000);
        });
        
        // Global error handler
        window.addEventListener('error', function(e) {
            addDebug('🚨 Global error: ' + e.message);
            if (e.message.includes('Google') || e.message.includes('maps')) {
                updateStatus('🚨 Google Maps error: ' + e.message, 'error');
            }
        });
        
        // Make initMap global
        window.initMap = initMap;
    </script>

    <!-- Load Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=drawing,geometry&callback=initMap"
        onerror="addDebug('❌ Failed to load Google Maps script'); updateStatus('❌ Failed to load Google Maps script', 'error');">
    </script>
</body>
</html>
