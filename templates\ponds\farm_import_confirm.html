{% extends "base.html" %}
{% load static %}

{% block title %}Confirm Farm Import - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-1">
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_list' %}">Farms</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_import' %}">Import</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Confirm</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0">Confirm Farm Import</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Review Data</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> Please review the data below before confirming the import.
                    </div>
                    
                    <p>You are about to import <strong>{{ farms_to_import|length }}</strong> farms.</p>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Location</th>
                                    <th>Size</th>
                                    <th>Description</th>
                                    <th>Coordinates</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for farm in farms_to_import %}
                                    <tr>
                                        <td>{{ farm.name }}</td>
                                        <td>{{ farm.location|default:"--" }}</td>
                                        <td>{{ farm.size }} hectares</td>
                                        <td>{{ farm.description|default:"--"|truncatechars:30 }}</td>
                                        <td>
                                            {% if farm.latitude and farm.longitude %}
                                                {{ farm.latitude }}, {{ farm.longitude }}
                                            {% else %}
                                                --
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <form method="post" action="{% url 'ponds:farm_import_process' %}">
                        {% csrf_token %}
                        <input type="hidden" name="import_data" value="{{ import_data_json }}">
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ponds:farm_import' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i> Confirm and Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Import Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Total Farms</h6>
                        <p class="mb-0">{{ farms_to_import|length }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Column Mapping</h6>
                        <ul class="list-unstyled small">
                            <li><strong>Name:</strong> {{ column_mapping.name }}</li>
                            <li><strong>Location:</strong> {{ column_mapping.location|default:"Not mapped" }}</li>
                            <li><strong>Size:</strong> {{ column_mapping.size }}</li>
                            <li><strong>Description:</strong> {{ column_mapping.description|default:"Not mapped" }}</li>
                            <li><strong>Latitude:</strong> {{ column_mapping.latitude|default:"Not mapped" }}</li>
                            <li><strong>Longitude:</strong> {{ column_mapping.longitude|default:"Not mapped" }}</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> All farms will be assigned to your user account.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}{% extends "base.html" %}
{% load static %}

{% block title %}Confirm Farm Import - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-1">
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_list' %}">Farms</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_import' %}">Import</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Confirm</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0">Confirm Farm Import</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Review Data</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> Please review the data below before confirming the import.
                    </div>
                    
                    <p>You are about to import <strong>{{ farms_to_import|length }}</strong> farms.</p>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Name</th>
                                    <th>Location</th>
                                    <th>Size</th>
                                    <th>Description</th>
                                    <th>Coordinates</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for farm in farms_to_import %}
                                    <tr>
                                        <td>{{ farm.name }}</td>
                                        <td>{{ farm.location|default:"--" }}</td>
                                        <td>{{ farm.size }} hectares</td>
                                        <td>{{ farm.description|default:"--"|truncatechars:30 }}</td>
                                        <td>
                                            {% if farm.latitude and farm.longitude %}
                                                {{ farm.latitude }}, {{ farm.longitude }}
                                            {% else %}
                                                --
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <form method="post" action="{% url 'ponds:farm_import_process' %}">
                        {% csrf_token %}
                        <input type="hidden" name="import_data" value="{{ import_data_json }}">
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ponds:farm_import' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i> Confirm and Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Import Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>Total Farms</h6>
                        <p class="mb-0">{{ farms_to_import|length }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Column Mapping</h6>
                        <ul class="list-unstyled small">
                            <li><strong>Name:</strong> {{ column_mapping.name }}</li>
                            <li><strong>Location:</strong> {{ column_mapping.location|default:"Not mapped" }}</li>
                            <li><strong>Size:</strong> {{ column_mapping.size }}</li>
                            <li><strong>Description:</strong> {{ column_mapping.description|default:"Not mapped" }}</li>
                            <li><strong>Latitude:</strong> {{ column_mapping.latitude|default:"Not mapped" }}</li>
                            <li><strong>Longitude:</strong> {{ column_mapping.longitude|default:"Not mapped" }}</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> All farms will be assigned to your user account.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}