{% extends 'layouts/form_base.html' %}
{% load static %}

{% block form_title %}{{ title }}{% endblock %}
{% block form_header %}{{ title }}{% endblock %}
{% block form_header_icon %}<i class="fas fa-broadcast-tower text-primary me-2"></i>{% endblock %}

{% block form_card_title %}Weather Station Information{% endblock %}

{% block form_cancel_url %}{% url 'weather:station_list' %}{% endblock %}

{% block extra_css %}
<style>
    #map {
        width: 100%;
        height: 400px;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    .map-controls {
        position: absolute;
        top: 70px;  /* Moved down to avoid overlap with Mapbox controls */
        right: 10px;
        z-index: 999;  /* Lower than Mapbox controls */
        background-color: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .map-controls button {
        margin-bottom: 0.5rem;
    }

    .map-legend {
        position: absolute;
        bottom: 30px;  /* Moved up to avoid overlap with Mapbox attribution */
        left: 10px;
        z-index: 999;  /* Lower than Mapbox controls */
        background-color: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .map-legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .map-legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .station-marker {
        background-color: #0d6efd;
    }

    .farm-boundary {
        background-color: rgba(13, 110, 253, 0.3);
        border: 2px solid #0d6efd;
    }

    .drawing-instructions {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        border-left: 4px solid #0d6efd;
    }

    .drawing-instructions h6 {
        margin-bottom: 0.5rem;
    }

    .drawing-instructions ol {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    /* Fix control positioning */
    .mapboxgl-ctrl-top-left {
        top: 10px;
        left: 10px;
    }

    .mapboxgl-ctrl-top-right {
        top: 10px;
        right: 10px;
    }

    /* Make controls more visible */
    .mapboxgl-ctrl-group {
        background-color: #fff;
        border-radius: 4px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    /* Style the geocoder */
    .mapboxgl-ctrl-geocoder {
        min-width: 250px;
        font-size: 14px;
        line-height: 20px;
        max-width: 300px;
    }
</style>
<link href="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.css" rel="stylesheet">
<link href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-draw/v1.4.0/mapbox-gl-draw.css" rel="stylesheet">
<link href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.7.0/mapbox-gl-geocoder.min.css" rel="stylesheet">
{% endblock %}

{% block form_fields %}
<div class="row">
    <div class="col-md-6">
        <div class="mb-3">
            <label for="{{ form.name.id_for_label }}" class="form-label">Station Name</label>
            {{ form.name.errors }}
            <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
            {% if form.name.help_text %}
            <div class="form-text">{{ form.name.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <label for="{{ form.latitude.id_for_label }}" class="form-label">Latitude</label>
            {{ form.latitude.errors }}
            <input type="number" step="0.000001" name="{{ form.latitude.name }}" id="{{ form.latitude.id_for_label }}" class="form-control {% if form.latitude.errors %}is-invalid{% endif %}" value="{{ form.latitude.value|default:'' }}" required>
            {% if form.latitude.help_text %}
            <div class="form-text">{{ form.latitude.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <label for="{{ form.elevation.id_for_label }}" class="form-label">Elevation (meters)</label>
            {{ form.elevation.errors }}
            <input type="number" step="0.1" name="{{ form.elevation.name }}" id="{{ form.elevation.id_for_label }}" class="form-control {% if form.elevation.errors %}is-invalid{% endif %}" value="{{ form.elevation.value|default:'' }}" required>
            {% if form.elevation.help_text %}
            <div class="form-text">{{ form.elevation.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <div class="form-check">
                <input type="checkbox" name="{{ form.is_active.name }}" id="{{ form.is_active.id_for_label }}" class="form-check-input {% if form.is_active.errors %}is-invalid{% endif %}" {% if form.is_active.value %}checked{% endif %}>
                <label for="{{ form.is_active.id_for_label }}" class="form-check-label">Active Station</label>
                {% if form.is_active.help_text %}
                <div class="form-text">{{ form.is_active.help_text }}</div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="mb-3">
            <label for="{{ form.location.id_for_label }}" class="form-label">Location</label>
            {{ form.location.errors }}
            <input type="text" name="{{ form.location.name }}" id="{{ form.location.id_for_label }}" class="form-control {% if form.location.errors %}is-invalid{% endif %}" value="{{ form.location.value|default:'' }}" required>
            {% if form.location.help_text %}
            <div class="form-text">{{ form.location.help_text }}</div>
            {% endif %}
        </div>
        
        <div class="mb-3">
            <label for="{{ form.longitude.id_for_label }}" class="form-label">Longitude</label>
            {{ form.longitude.errors }}
            <input type="number" step="0.000001" name="{{ form.longitude.name }}" id="{{ form.longitude.id_for_label }}" class="form-control {% if form.longitude.errors %}is-invalid{% endif %}" value="{{ form.longitude.value|default:'' }}" required>
            {% if form.longitude.help_text %}
            <div class="form-text">{{ form.longitude.help_text }}</div>
            {% endif %}
        </div>
        
        <!-- Hidden field for farm boundary coordinates -->
        <input type="hidden" name="farm_boundary" id="farm-boundary-input">
    </div>
</div>

<div class="card mt-4 mb-4">
    <div class="card-header">
        <h5 class="mb-0">Station Location & Farm Boundary</h5>
    </div>
    <div class="card-body p-0 position-relative">
        <div id="map"></div>

        <div class="map-controls">
            <button type="button" class="btn btn-sm btn-primary d-block w-100" id="locate-me-btn">
                <i class="fas fa-location-arrow me-1"></i> My Location
            </button>
            <button type="button" class="btn btn-sm btn-success d-block w-100" id="draw-boundary-btn">
                <i class="fas fa-draw-polygon me-1"></i> Draw Farm Boundary
            </button>
            <button type="button" class="btn btn-sm btn-danger d-block w-100" id="clear-boundary-btn">
                <i class="fas fa-trash-alt me-1"></i> Clear Boundary
            </button>
        </div>

        <div class="map-legend">
            <div class="map-legend-item">
                <div class="map-legend-color station-marker"></div>
                <span>Weather Station</span>
            </div>
            <div class="map-legend-item">
                <div class="map-legend-color farm-boundary"></div>
                <span>Farm Boundary</span>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <div class="drawing-instructions">
            <h6><i class="fas fa-info-circle me-1"></i> How to Mark Farm Boundaries</h6>
            <ol>
                <li>Click the "Draw Farm Boundary" button</li>
                <li>Click on the map to place points around your farm</li>
                <li>Complete the polygon by clicking on the first point</li>
                <li>To edit, drag any point to adjust the boundary</li>
                <li>Click "Clear Boundary" to start over</li>
            </ol>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://api.mapbox.com/mapbox-gl-js/v2.14.1/mapbox-gl.js"></script>
<script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-draw/v1.4.0/mapbox-gl-draw.js"></script>
<script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v4.7.0/mapbox-gl-geocoder.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mapbox access token from context
        mapboxgl.accessToken = '{{ mapbox_access_token }}';

        // Initialize form fields
        const latitudeField = document.getElementById('id_latitude');
        const longitudeField = document.getElementById('id_longitude');
        const farmBoundaryInput = document.getElementById('farm-boundary-input');

        // Default coordinates (Vietnam)
        let initialLat = 10.125;
        let initialLng = 106.458;
        let initialZoom = 15;

        // Set initial coordinates if available
        if (latitudeField.value && longitudeField.value) {
            initialLat = parseFloat(latitudeField.value);
            initialLng = parseFloat(longitudeField.value);
        }

        // Initialize map
        const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/satellite-streets-v12', // Satellite imagery with streets
            center: [initialLng, initialLat],
            zoom: initialZoom
        });

        // Add navigation controls with specific position
        map.addControl(new mapboxgl.NavigationControl({
            showCompass: true,
            showZoom: true,
            visualizePitch: true
        }), 'top-left');

        // Add geocoder (search) with specific position
        const geocoder = new MapboxGeocoder({
            accessToken: mapboxgl.accessToken,
            mapboxgl: mapboxgl,
            marker: false,
            placeholder: 'Search for a location',
            clearOnBlur: true,
            clearAndBlurOnEsc: true
        });
        map.addControl(geocoder, 'top-right');

        // Initialize Draw plugin
        const draw = new MapboxDraw({
            displayControlsDefault: false,
            controls: {
                polygon: false,
                trash: false
            },
            styles: [
                {
                    'id': 'gl-draw-polygon-fill',
                    'type': 'fill',
                    'filter': ['all', ['==', '$type', 'Polygon']],
                    'paint': {
                        'fill-color': '#0d6efd',
                        'fill-outline-color': '#0d6efd',
                        'fill-opacity': 0.2
                    }
                },
                {
                    'id': 'gl-draw-polygon-stroke',
                    'type': 'line',
                    'filter': ['all', ['==', '$type', 'Polygon']],
                    'paint': {
                        'line-color': '#0d6efd',
                        'line-width': 2
                    }
                },
                {
                    'id': 'gl-draw-polygon-and-line-vertex-active',
                    'type': 'circle',
                    'filter': ['all', ['==', 'meta', 'vertex'], ['==', '$type', 'Point']],
                    'paint': {
                        'circle-radius': 6,
                        'circle-color': '#fff',
                        'circle-stroke-color': '#0d6efd',
                        'circle-stroke-width': 2
                    }
                }
            ]
        });
        map.addControl(draw);

        // Add marker
        let marker = new mapboxgl.Marker({
            color: '#0d6efd',
            draggable: true
        });

        // Set initial marker if coordinates are available
        if (latitudeField.value && longitudeField.value) {
            marker.setLngLat([initialLng, initialLat]).addTo(map);
        }

        // Update form fields when marker is dragged
        marker.on('dragend', function() {
            const lngLat = marker.getLngLat();
            latitudeField.value = lngLat.lat.toFixed(6);
            longitudeField.value = lngLat.lng.toFixed(6);
        });

        // Set initial boundary if available
        {% if station.farm_boundary %}
        try {
            const boundaryCoordinates = JSON.parse('{{ station.farm_boundary|escapejs }}');

            if (boundaryCoordinates && boundaryCoordinates.length > 0) {
                // Convert coordinates to GeoJSON format
                const polygonCoords = boundaryCoordinates.map(coord => [coord[1], coord[0]]);

                // Create GeoJSON feature
                const feature = {
                    type: 'Feature',
                    properties: {},
                    geometry: {
                        type: 'Polygon',
                        coordinates: [polygonCoords]
                    }
                };

                // Add feature to draw
                draw.add(feature);

                // Fit map to boundary
                const bounds = new mapboxgl.LngLatBounds();
                polygonCoords.forEach(coord => bounds.extend(coord));
                map.fitBounds(bounds, { padding: 50 });

                // Set boundary input value
                farmBoundaryInput.value = JSON.stringify(boundaryCoordinates);
            }
        } catch (error) {
            console.error("Error parsing farm boundary:", error);
        }
        {% endif %}

        // Handle map click to set station location
        map.on('click', function(e) {
            // Only set marker if not in drawing mode
            if (!draw.mode) {
                marker.setLngLat(e.lngLat).addTo(map);

                // Update form fields
                latitudeField.value = e.lngLat.lat.toFixed(6);
                longitudeField.value = e.lngLat.lng.toFixed(6);
            }
        });

        // Handle draw.create event
        map.on('draw.create', function(e) {
            const features = e.features;
            if (features.length > 0) {
                const coordinates = features[0].geometry.coordinates[0];

                // Convert coordinates to [lat, lng] format for storage
                const latLngCoords = coordinates.map(coord => [coord[1], coord[0]]);

                // Store coordinates in hidden input
                farmBoundaryInput.value = JSON.stringify(latLngCoords);
            }
        });

        // Handle draw.update event
        map.on('draw.update', function(e) {
            const features = e.features;
            if (features.length > 0) {
                const coordinates = features[0].geometry.coordinates[0];

                // Convert coordinates to [lat, lng] format for storage
                const latLngCoords = coordinates.map(coord => [coord[1], coord[0]]);

                // Store coordinates in hidden input
                farmBoundaryInput.value = JSON.stringify(latLngCoords);
            }
        });

        // Handle locate me button
        document.getElementById('locate-me-btn').addEventListener('click', function() {
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    const lat = position.coords.latitude;
                    const lng = position.coords.longitude;

                    // Update map
                    map.flyTo({
                        center: [lng, lat],
                        zoom: 16
                    });

                    // Update marker
                    marker.setLngLat([lng, lat]).addTo(map);

                    // Update form fields
                    latitudeField.value = lat.toFixed(6);
                    longitudeField.value = lng.toFixed(6);

                    // Show accuracy circle
                    const radius = position.coords.accuracy;

                    // Remove existing accuracy circle if any
                    if (map.getSource('accuracy-circle')) {
                        map.removeLayer('accuracy-circle-fill');
                        map.removeSource('accuracy-circle');
                    }

                    map.addSource('accuracy-circle', {
                        type: 'geojson',
                        data: {
                            type: 'Feature',
                            geometry: {
                                type: 'Point',
                                coordinates: [lng, lat]
                            },
                            properties: {
                                radius: radius
                            }
                        }
                    });

                    map.addLayer({
                        id: 'accuracy-circle-fill',
                        type: 'circle',
                        source: 'accuracy-circle',
                        paint: {
                            'circle-radius': ['get', 'radius'],
                            'circle-color': '#0d6efd',
                            'circle-opacity': 0.1,
                            'circle-stroke-color': '#0d6efd',
                            'circle-stroke-width': 1
                        }
                    });
                },
                function(error) {
                    alert("Could not find your location: " + error.message);
                }
            );
        });

        // Handle draw boundary button
        document.getElementById('draw-boundary-btn').addEventListener('click', function() {
            // Clear existing drawings
            draw.deleteAll();

            // Start polygon drawing mode
            draw.changeMode('draw_polygon');
        });

        // Handle clear boundary button
        document.getElementById('clear-boundary-btn').addEventListener('click', function() {
            // Clear drawings
            draw.deleteAll();

            // Clear hidden input
            farmBoundaryInput.value = '';
        });
    });
</script>
{% endblock %}