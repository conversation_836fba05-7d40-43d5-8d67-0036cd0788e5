<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Labor Management - Shrimp Farm Guardian</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary: #3b82f6;
            --primary-dark: #2563eb;
            --secondary: #64748b;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --light: #f8fafc;
            --dark: #0f172a;
            --border: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--dark);
            line-height: 1.6;
        }

        .container-fluid {
            padding: 0;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--dark);
            text-decoration: none;
        }

        .logo i {
            color: var(--primary);
            font-size: 1.5rem;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        .btn-primary {
            background: var(--primary);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        /* Main Content */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 3rem 2rem;
        }

        .page-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .page-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 1rem;
        }

        .page-subtitle {
            font-size: 1.25rem;
            color: var(--secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 4rem;
        }

        .stat-card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            transition: all 0.2s;
            position: relative;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.primary { background: var(--primary); }
        .stat-icon.success { background: var(--success); }
        .stat-icon.warning { background: var(--warning); }
        .stat-icon.danger { background: var(--danger); }
        .stat-icon.info { background: var(--info); }
        .stat-icon.secondary { background: var(--secondary); }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark);
            line-height: 1;
        }

        .stat-label {
            color: var(--secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stat-change.positive {
            color: var(--success);
        }

        .stat-change.negative {
            color: var(--danger);
        }

        /* Features Grid */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 4rem;
        }

        .feature-card {
            background: white;
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            transition: all 0.2s;
            text-decoration: none;
            color: inherit;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary);
            text-decoration: none;
            color: inherit;
        }

        .feature-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .feature-icon {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--dark);
            margin: 0;
        }

        .feature-description {
            color: var(--secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }

        .feature-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .feature-link {
            color: var(--primary);
            font-weight: 500;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s;
        }

        .feature-link:hover {
            color: var(--primary-dark);
        }

        .feature-badge {
            background: var(--light);
            color: var(--secondary);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* Quick Actions */
        .quick-actions {
            background: white;
            border: 1px solid var(--border);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            background: var(--light);
            border: 1px solid var(--border);
            border-radius: 0.75rem;
            padding: 1rem;
            text-decoration: none;
            color: var(--dark);
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 500;
        }

        .action-btn:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
            transform: translateY(-1px);
            text-decoration: none;
        }

        .action-btn i {
            font-size: 1.25rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
            }

            .main-content {
                padding: 2rem 1rem;
            }

            .page-title {
                font-size: 2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .action-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <a href="{% url 'labor:labor_root' %}" class="logo">
                <i class="fas fa-users"></i>
                Labor Management
            </a>
            <div class="header-actions">
                <a href="{% url 'labor:dashboard' %}" class="btn btn-primary">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="page-header">
            <h1 class="page-title">Workforce Management</h1>
            <p class="page-subtitle">
                Comprehensive labor management system for your shrimp farm operations
            </p>
        </div>

        <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon primary">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-label">Active Workers</div>
                <div class="stat-value">{{ worker_count }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +2 this week
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon success">
                        <i class="fas fa-layer-group"></i>
                    </div>
                </div>
                <div class="stat-label">Active Teams</div>
                <div class="stat-value">{{ team_count }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +1 this month
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon warning">
                        <i class="fas fa-tasks"></i>
                    </div>
                </div>
                <div class="stat-label">Active Tasks</div>
                <div class="stat-value">{{ active_tasks }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> +5 today
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon info">
                        <i class="fas fa-check-circle"></i>
                    </div>
                </div>
                <div class="stat-label">Completed Today</div>
                <div class="stat-value">{{ completed_today }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-arrow-up"></i> 85% efficiency
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon secondary">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                </div>
                <div class="stat-label">Active Geofences</div>
                <div class="stat-value">{{ active_geofences }}</div>
                <div class="stat-change positive">
                    <i class="fas fa-shield-alt"></i> All secure
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                </div>
                <div class="stat-label">Recent Alerts</div>
                <div class="stat-value">{{ recent_alerts }}</div>
                <div class="stat-change negative">
                    <i class="fas fa-arrow-down"></i> -3 from yesterday
                </div>
            </div>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <a href="{% url 'labor:dashboard' %}" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon primary">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h3 class="feature-title">Enhanced Dashboard</h3>
                </div>
                <p class="feature-description">
                    Comprehensive dashboard with real-time tracking, analytics, alerts, heat maps, and mobile app integration.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        Open Dashboard <i class="fas fa-arrow-right"></i>
                    </span>
                    <span class="feature-badge">Live</span>
                </div>
            </a>

            <a href="{% url 'labor:location_tracking_dashboard' %}" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon success">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3 class="feature-title">Live Tracking</h3>
                </div>
                <p class="feature-description">
                    Real-time worker location tracking with GPS, geofence monitoring, and movement analysis.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        Track Workers <i class="fas fa-arrow-right"></i>
                    </span>
                    <span class="feature-badge">GPS</span>
                </div>
            </a>

            <a href="{% url 'labor:productivity_analytics' %}" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon info">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3 class="feature-title">Analytics</h3>
                </div>
                <p class="feature-description">
                    Advanced productivity analytics, performance metrics, and comprehensive reporting tools.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        View Analytics <i class="fas fa-arrow-right"></i>
                    </span>
                    <span class="feature-badge">Reports</span>
                </div>
            </a>

            <a href="{% url 'labor:geofence_alerts_dashboard' %}" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3 class="feature-title">Alert System</h3>
                </div>
                <p class="feature-description">
                    Real-time geofence alerts, zone monitoring, and automated notifications for worker safety.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        Monitor Alerts <i class="fas fa-arrow-right"></i>
                    </span>
                    <span class="feature-badge">24/7</span>
                </div>
            </a>

            <a href="{% url 'labor:heat_map' %}" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon warning">
                        <i class="fas fa-fire"></i>
                    </div>
                    <h3 class="feature-title">Heat Maps</h3>
                </div>
                <p class="feature-description">
                    Visual analysis of worker activity patterns and density across different farm zones.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        View Heat Map <i class="fas fa-arrow-right"></i>
                    </span>
                    <span class="feature-badge">Visual</span>
                </div>
            </a>

            <a href="/mobile_demo.html" target="_blank" class="feature-card">
                <div class="feature-header">
                    <div class="feature-icon secondary">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">Mobile App</h3>
                </div>
                <p class="feature-description">
                    Offline-capable mobile application for field workers with task management and photo capture.
                </p>
                <div class="feature-footer">
                    <span class="feature-link">
                        Open Mobile App <i class="fas fa-external-link-alt"></i>
                    </span>
                    <span class="feature-badge">Offline</span>
                </div>
            </a>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2 class="section-title">
                <i class="fas fa-bolt"></i>
                Quick Actions
            </h2>
            <div class="action-grid">
                <a href="{% url 'labor:worker_create' %}" class="action-btn">
                    <i class="fas fa-user-plus"></i>
                    Add Worker
                </a>
                <a href="{% url 'labor:task_create' %}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    Create Task
                </a>
                <a href="{% url 'labor:geofence_create' %}" class="action-btn">
                    <i class="fas fa-draw-polygon"></i>
                    New Geofence
                </a>
                <a href="{% url 'labor:worker_list' %}" class="action-btn">
                    <i class="fas fa-users"></i>
                    View Workers
                </a>
                <a href="{% url 'labor:geofence_list' %}" class="action-btn">
                    <i class="fas fa-map-marker-alt"></i>
                    Manage Geofences
                </a>
                <a href="{% url 'labor:task_list' %}" class="action-btn">
                    <i class="fas fa-tasks"></i>
                    View Tasks
                </a>
            </div>
        </div>
    </main>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Smooth scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, observerOptions);

        // Observe all cards
        const cards = document.querySelectorAll('.stat-card, .feature-card, .quick-actions');
        cards.forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            observer.observe(card);
        });

        // Add loading animation to stats
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
            const finalValue = parseInt(stat.textContent);
            let currentValue = 0;
            const increment = finalValue / 30;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    stat.textContent = finalValue;
                    clearInterval(timer);
                } else {
                    stat.textContent = Math.floor(currentValue);
                }
            }, 50);
        });
    });
    </script>

</body>
</html>
