<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Labor Management - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            --warning-gradient: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            --danger-gradient: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            --info-gradient: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            min-height: calc(100vh - 40px);
        }

        .hero-section {
            text-align: center;
            padding: 60px 0;
            background: var(--primary-gradient);
            border-radius: 20px;
            color: white;
            margin-bottom: 50px;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: sweep 4s infinite;
        }

        @keyframes sweep {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--primary-gradient);
        }

        .stat-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #666;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 50px 0;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: var(--primary-gradient);
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            color: white;
            font-size: 2rem;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .feature-btn {
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .feature-btn:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .quick-actions {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 50px 0;
            text-align: center;
        }

        .quick-actions h3 {
            color: #333;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .action-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
        }

        .action-btn {
            background: white;
            color: #333;
            border: 2px solid #e0e0e0;
            padding: 15px 25px;
            border-radius: 15px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .action-btn:hover {
            color: white;
            background: var(--primary-gradient);
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .main-container {
                margin: 10px;
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="hero-content">
            <h1 class="hero-title">
                <i class="fas fa-users me-3"></i>
                Labor Management
            </h1>
            <p class="hero-subtitle">
                Complete workforce management solution for your shrimp farm
            </p>
            <a href="{% url 'labor:dashboard' %}" class="feature-btn" style="font-size: 1.1rem; padding: 15px 40px;">
                <i class="fas fa-tachometer-alt"></i>
                Open Main Dashboard
            </a>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-users stat-icon"></i>
            <div class="stat-number">{{ worker_count }}</div>
            <div class="stat-label">Active Workers</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-layer-group stat-icon"></i>
            <div class="stat-number">{{ team_count }}</div>
            <div class="stat-label">Teams</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-tasks stat-icon"></i>
            <div class="stat-number">{{ active_tasks }}</div>
            <div class="stat-label">Active Tasks</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-check-circle stat-icon"></i>
            <div class="stat-number">{{ completed_today }}</div>
            <div class="stat-label">Completed Today</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-map-marked-alt stat-icon"></i>
            <div class="stat-number">{{ active_geofences }}</div>
            <div class="stat-label">Active Geofences</div>
        </div>
        <div class="stat-card">
            <i class="fas fa-exclamation-triangle stat-icon"></i>
            <div class="stat-number">{{ recent_alerts }}</div>
            <div class="stat-label">Recent Alerts</div>
        </div>
    </div>

    <!-- Features Grid -->
    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-tachometer-alt"></i>
            </div>
            <h3 class="feature-title">Enhanced Dashboard</h3>
            <p class="feature-description">
                Comprehensive dashboard with real-time tracking, analytics, alerts, heat maps, and mobile app integration.
            </p>
            <a href="{% url 'labor:dashboard' %}" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                Open Dashboard
            </a>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-map-marked-alt"></i>
            </div>
            <h3 class="feature-title">Live Tracking</h3>
            <p class="feature-description">
                Real-time worker location tracking with GPS, geofence monitoring, and movement analysis.
            </p>
            <a href="{% url 'labor:location_tracking_dashboard' %}" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                Track Workers
            </a>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <h3 class="feature-title">Analytics</h3>
            <p class="feature-description">
                Advanced productivity analytics, performance metrics, and comprehensive reporting tools.
            </p>
            <a href="{% url 'labor:productivity_analytics' %}" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                View Analytics
            </a>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="feature-title">Alert System</h3>
            <p class="feature-description">
                Real-time geofence alerts, zone monitoring, and automated notifications for worker safety.
            </p>
            <a href="{% url 'labor:geofence_alerts_dashboard' %}" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                Monitor Alerts
            </a>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-fire"></i>
            </div>
            <h3 class="feature-title">Heat Maps</h3>
            <p class="feature-description">
                Visual analysis of worker activity patterns and density across different farm zones.
            </p>
            <a href="{% url 'labor:heat_map' %}" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                View Heat Map
            </a>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3 class="feature-title">Mobile App</h3>
            <p class="feature-description">
                Offline-capable mobile application for field workers with task management and photo capture.
            </p>
            <a href="/mobile_demo.html" target="_blank" class="feature-btn">
                <i class="fas fa-arrow-right"></i>
                Open Mobile App
            </a>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h3><i class="fas fa-bolt me-2"></i>Quick Actions</h3>
        <div class="action-buttons">
            <a href="{% url 'labor:worker_create' %}" class="action-btn">
                <i class="fas fa-user-plus"></i>Add Worker
            </a>
            <a href="{% url 'labor:task_create' %}" class="action-btn">
                <i class="fas fa-plus"></i>Create Task
            </a>
            <a href="{% url 'labor:geofence_create' %}" class="action-btn">
                <i class="fas fa-draw-polygon"></i>New Geofence
            </a>
            <a href="{% url 'labor:worker_list' %}" class="action-btn">
                <i class="fas fa-users"></i>View Workers
            </a>
            <a href="{% url 'labor:geofence_list' %}" class="action-btn">
                <i class="fas fa-map-marker-alt"></i>Manage Geofences
            </a>
            <a href="{% url 'labor:task_list' %}" class="action-btn">
                <i class="fas fa-tasks"></i>View Tasks
            </a>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.stat-card, .feature-card');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }, index * 100);
            }
        });
    });
    
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
});
</script>

</body>
</html>
