<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Google Maps Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        
        #map {
            height: 500px;
            width: 100%;
            border-radius: 10px;
            border: 3px solid #667eea;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗺️ Google Maps Weather Test</h1>
            <p>Testing Google Maps API Integration</p>
        </div>
        
        <div class="debug-info">
            <h3>🔧 Debug Information</h3>
            <p><strong>API Key:</strong> {{ google_maps_api_key|slice:":10" }}...</p>
            <p><strong>Pond:</strong> {{ pond.name }}</p>
            <p><strong>Location:</strong> {{ pond.latitude }}, {{ pond.longitude }}</p>
            <div id="status-messages"></div>
        </div>
        
        <div id="map"></div>
        
        <div class="debug-info">
            <h3>📊 Console Output</h3>
            <div id="console-output" style="max-height: 200px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace;"></div>
        </div>
    </div>

    <script>
        // Logging function
        function log(message, type = 'info') {
            console.log(message);
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
            
            // Add status message
            const statusDiv = document.getElementById('status-messages');
            const statusMsg = document.createElement('div');
            statusMsg.className = `status ${type}`;
            statusMsg.textContent = message;
            statusDiv.appendChild(statusMsg);
        }

        // Pond data
        const pondData = {
            name: "{{ pond.name }}",
            lat: {{ pond.latitude }},
            lng: {{ pond.longitude }}
        };

        log('🚀 Starting Google Maps initialization...', 'info');
        log(`📍 Pond: ${pondData.name} at ${pondData.lat}, ${pondData.lng}`, 'info');

        // Global map variable
        let map;
        let marker;
        let infoWindow;

        // Initialize Google Maps
        function initMap() {
            try {
                log('✅ Google Maps API loaded successfully!', 'success');
                
                // Create map
                map = new google.maps.Map(document.getElementById('map'), {
                    center: { lat: pondData.lat, lng: pondData.lng },
                    zoom: 15,
                    mapTypeId: google.maps.MapTypeId.SATELLITE
                });
                
                log('🗺️ Map created successfully!', 'success');
                
                // Create marker
                marker = new google.maps.Marker({
                    position: { lat: pondData.lat, lng: pondData.lng },
                    map: map,
                    title: pondData.name,
                    animation: google.maps.Animation.DROP
                });
                
                log('📍 Marker added successfully!', 'success');
                
                // Create info window
                infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="padding: 10px;">
                            <h3>🐟 ${pondData.name}</h3>
                            <p><strong>Location:</strong> ${pondData.lat}, ${pondData.lng}</p>
                            <p><strong>Status:</strong> ✅ Google Maps Working!</p>
                        </div>
                    `
                });
                
                // Add click listener
                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                    log('💬 Info window opened!', 'success');
                });
                
                // Auto-open info window
                setTimeout(() => {
                    infoWindow.open(map, marker);
                    log('🎉 Google Maps fully initialized!', 'success');
                }, 1000);
                
            } catch (error) {
                log(`❌ Error initializing map: ${error.message}`, 'error');
            }
        }

        // Error handler
        function handleMapError() {
            log('❌ Google Maps API failed to load!', 'error');
            log('🔍 Check API key and network connection', 'error');
        }

        // Check if already loaded
        if (typeof google !== 'undefined' && google.maps) {
            log('⚡ Google Maps already available', 'info');
            initMap();
        } else {
            log('⏳ Waiting for Google Maps API...', 'info');
        }
    </script>
    
    <script 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap" 
        async 
        defer 
        onerror="handleMapError()">
    </script>
</body>
</html>
