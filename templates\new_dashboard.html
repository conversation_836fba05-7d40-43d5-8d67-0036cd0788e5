                                                                                    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shrimp Farm Guardian - Dashboard</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --secondary-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }        html, body {
            height: 100vh;
            width: 100vw;
            font-family: 'Roboto', sans-serif;
            background-color: #f8f9fa;
            overflow: hidden;
        }        .dashboard-container {
            display: flex;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
        }

        .sidebar {
            background-color: #ffffff;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
            overflow-x: hidden;
            width: 250px;
            flex-shrink: 0;
        }

        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .sidebar-title {
            display: flex;
            align-items: center;
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .sidebar-title i {
            margin-right: 0.5rem;
            color: var(--primary-color);
        }

        .sidebar-nav {
            padding: 1rem;
        }

        .nav-section {
            margin-bottom: 2rem;
        }

        .nav-section-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.75rem;
        }

        .nav-list {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 0.25rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #4b5563;
            text-decoration: none;
            border-radius: 0.375rem;
            transition: all 0.2s;
        }

        .nav-link:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        .nav-link.active {
            background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
            color: white;
            font-weight: 500;
        }

        .nav-link i {
            margin-right: 0.75rem;
            width: 1.25rem;
            text-align: center;        }

        .main-content {
            background-color: #f8f9fa;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 2rem;
            flex: 1;
            height: 100vh;
            box-sizing: border-box;
        }

        /* Custom scrollbar styling */
        .main-content::-webkit-scrollbar {
            width: 8px;
        }

        .main-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .main-content::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: #f8f9fa;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: #dee2e6;
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: #ced4da;
        }

        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            color: #6b7280;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }        .stat-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-title {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
        }

        .stat-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .stat-icon.primary {
            background-color: #dbeafe;
            color: #3b82f6;
        }

        .stat-icon.success {
            background-color: #d1fae5;
            color: #10b981;
        }

        .stat-icon.warning {
            background-color: #fef3c7;
            color: #f59e0b;
        }

        .stat-icon.danger {
            background-color: #fee2e2;
            color: #ef4444;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }

        .stat-change {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .stat-change.positive {
            color: #10b981;
        }

        .stat-change.negative {
            color: #ef4444;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }

        .chart-card {
            background: white;
            border-radius: 0.5rem;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .chart-placeholder {
            height: 300px;
            background: #f9fafb;
            border: 2px dashed #d1d5db;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
        }        .alerts-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .alert-item {
            padding: 1rem;
            border-radius: 0.375rem;
            border-left: 4px solid;
        }

        .alert-item.warning {
            background-color: #fef3c7;
            border-left-color: #f59e0b;
        }

        .alert-item.danger {
            background-color: #fee2e2;
            border-left-color: #ef4444;
        }

        .alert-item.info {
            background-color: #dbeafe;
            border-left-color: #3b82f6;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .alert-time {
            font-size: 0.875rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1 class="sidebar-title">
                    <i class="fas fa-water"></i>
                    Shrimp Farm Guardian
                </h1>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link active">
                                <i class="fas fa-satellite-dish"></i>
                                Main Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-analytics"></i>
                                Advanced Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-rocket"></i>
                                Enhancements
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Farm Management</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-water"></i>
                                Ponds
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-flask"></i>
                                Water Quality
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-drumstick-bite"></i>
                                Feed Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-pills"></i>
                                Medicine
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Monitoring</div>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-cloud-sun-rain"></i>
                                Weather
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-robot"></i>
                                AI Alerts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-bell"></i>
                                Alerts
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <i class="fas fa-virus"></i>
                                Disease Management
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="dashboard-header">
                <h1 class="dashboard-title">Dashboard</h1>
                <p class="dashboard-subtitle">Welcome to your Shrimp Farm Guardian dashboard</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Total Ponds</div>
                            <div class="stat-value">12</div>
                            <div class="stat-change positive">+2 this month</div>
                        </div>
                        <div class="stat-icon primary">
                            <i class="fas fa-water"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Active Harvests</div>
                            <div class="stat-value">8</div>
                            <div class="stat-change positive">+3 this week</div>
                        </div>
                        <div class="stat-icon success">
                            <i class="fas fa-fish"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Water Quality</div>
                            <div class="stat-value">94%</div>
                            <div class="stat-change positive">+2% optimal</div>
                        </div>
                        <div class="stat-icon success">
                            <i class="fas fa-flask"></i>
                        </div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-header">
                        <div>
                            <div class="stat-title">Active Alerts</div>
                            <div class="stat-value">3</div>
                            <div class="stat-change negative">2 critical</div>
                        </div>
                        <div class="stat-icon warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <div class="chart-card">
                    <h3 class="card-title">Water Quality Trends</h3>
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line" style="font-size: 3rem; margin-right: 1rem;"></i>
                        <div>
                            <div style="font-size: 1.25rem; font-weight: 600;">Water Quality Chart</div>
                            <div>Real-time monitoring data visualization</div>
                        </div>
                    </div>
                </div>

                <div class="chart-card">
                    <h3 class="card-title">Recent Alerts</h3>
                    <div class="alerts-list">
                        <div class="alert-item danger">
                            <div class="alert-title">Critical: Low Oxygen Level</div>
                            <div class="alert-time">Pond #3 - 2 minutes ago</div>
                        </div>
                        <div class="alert-item warning">
                            <div class="alert-title">High Temperature</div>
                            <div class="alert-time">Pond #7 - 15 minutes ago</div>
                        </div>
                        <div class="alert-item info">
                            <div class="alert-title">Feed Schedule Reminder</div>
                            <div class="alert-time">All ponds - 1 hour ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
