# 🪟 Windows Setup Guide - Shrimp Farm Guardian

## Quick Start Options

### Option 1: Docker Setup (Recommended)
The easiest way to get started on Windows:

```cmd
# Double-click or run from Command Prompt
docker-setup.bat
```

### Option 2: Development Setup
For local development without Docker:

```cmd
# Double-click or run from Command Prompt
setup-development.bat
```

### Option 3: Manual PowerShell Setup
For advanced users who want more control:

```powershell
# Run in PowerShell as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# Development environment
.\deployment\scripts\setup-windows.ps1 -Environment development

# Docker environment
.\deployment\scripts\docker-setup-windows.ps1 -Environment development -Build
```

## Prerequisites

### Required Software
- **Windows 10/11** (64-bit)
- **PowerShell 5.0+** (usually pre-installed)
- **Python 3.11+** - Download from [python.org](https://python.org)
- **Git** - Download from [git-scm.com](https://git-scm.com)

### For Docker Setup
- **Docker Desktop** - Download from [docker.com](https://docker.com/products/docker-desktop)

### Optional (for development)
- **Node.js 18+** - Download from [nodejs.org](https://nodejs.org)
- **Visual Studio Code** - Download from [code.visualstudio.com](https://code.visualstudio.com)

## Step-by-Step Instructions

### 1. Download and Extract
```cmd
# Download the project
git clone https://github.com/your-org/shrimp-farm-guardian.git
cd shrimp-farm-guardian

# Or download and extract ZIP file
```

### 2. Choose Your Setup Method

#### Docker Setup (Easiest)
```cmd
# Simply double-click docker-setup.bat
# Or run from Command Prompt:
docker-setup.bat
```

**What it does:**
- Checks Docker installation
- Sets up environment configuration
- Builds Docker images
- Starts all services (web, database, Redis, etc.)
- Initializes database with sample data
- Creates admin user (admin/admin123)

**Access URLs:**
- Web App: http://localhost:8000
- Admin: http://localhost:8000/admin
- API Docs: http://localhost:8000/api/docs
- Monitoring: http://localhost:3000

#### Development Setup
```cmd
# Double-click setup-development.bat
# Or run from Command Prompt:
setup-development.bat
```

**What it does:**
- Checks system requirements
- Creates Python virtual environment
- Installs Python dependencies
- Sets up environment configuration
- Initializes SQLite database
- Creates admin user

**Manual start:**
```cmd
# Activate virtual environment
venv\Scripts\activate

# Start development server
python manage.py runserver

# Access at http://localhost:8000
```

## Troubleshooting

### Common Issues

#### PowerShell Execution Policy Error
```powershell
# Run as Administrator
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### Docker Not Running
```cmd
# Start Docker Desktop
# Wait for Docker to fully start (whale icon in system tray)
# Retry the setup
```

#### Port Already in Use
```cmd
# Check what's using port 8000
netstat -ano | findstr :8000

# Kill the process (replace PID with actual process ID)
taskkill /PID <PID> /F

# Or use different ports in docker-compose.yml
```

#### Python Not Found
```cmd
# Check Python installation
python --version

# If not found, download from python.org
# Make sure to check "Add Python to PATH" during installation
```

#### Git Not Found
```cmd
# Check Git installation
git --version

# If not found, download from git-scm.com
```

### Docker-Specific Issues

#### Docker Desktop Not Starting
1. Restart Docker Desktop
2. Check Windows features: Hyper-V, WSL 2
3. Update Docker Desktop to latest version

#### Container Build Failures
```cmd
# Clean Docker cache
docker system prune -a

# Rebuild without cache
docker-compose build --no-cache
```

#### Database Connection Issues
```cmd
# Check if database container is running
docker-compose ps

# Restart database container
docker-compose restart db

# View database logs
docker-compose logs db
```

## Manual Commands

### Docker Commands
```cmd
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Restart specific service
docker-compose restart web

# Shell access
docker-compose exec web bash

# Database shell
docker-compose exec db psql -U postgres -d shrimp_farm_guardian
```

### Development Commands
```cmd
# Activate virtual environment
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Start development server
python manage.py runserver

# Run tests
python manage.py test

# Collect static files
python manage.py collectstatic
```

## Environment Configuration

### .env File
The setup scripts automatically create a `.env` file from `.env.example`. You can customize it:

```env
# Django Settings
DJANGO_DEBUG=True
DJANGO_SECRET_KEY=your-generated-secret-key
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1

# Database (for production)
DB_NAME=shrimp_farm_guardian
DB_USER=postgres
DB_PASSWORD=your-secure-password
DB_HOST=localhost
DB_PORT=5432

# API Keys (add your own)
OPENWEATHERMAP_API_KEY=your-api-key
GOOGLE_MAPS_API_KEY=your-api-key
```

### Development vs Production
- **Development**: Uses SQLite, debug mode enabled, sample data
- **Production**: Uses PostgreSQL, debug disabled, security hardened

## IDE Setup

### Visual Studio Code
Recommended extensions:
- Python
- Django
- Docker
- GitLens
- Prettier

### PyCharm
1. Open project folder
2. Configure Python interpreter: `venv\Scripts\python.exe`
3. Enable Django support in settings

## Next Steps

1. **Explore the Application**
   - Login with admin/admin123
   - Check out the dashboard
   - Try creating a farm and devices

2. **Read Documentation**
   - User Manual: `docs/user-manual/README.md`
   - API Docs: `docs/api/README.md`
   - Developer Guide: `docs/developer/README.md`

3. **Customize Configuration**
   - Update `.env` file with your settings
   - Add your API keys
   - Configure email settings

4. **Development**
   - Check `docs/developer/README.md` for coding guidelines
   - Set up pre-commit hooks
   - Run tests before committing

## Support

If you encounter issues:

1. Check the log files:
   - Setup logs: `%TEMP%\shrimp-farm-guardian-setup.log`
   - Docker logs: `%TEMP%\shrimp-farm-guardian-docker.log`

2. Common solutions:
   - Restart Docker Desktop
   - Run as Administrator
   - Check firewall settings
   - Update Windows

3. Get help:
   - GitHub Issues: [Create an issue](https://github.com/your-org/shrimp-farm-guardian/issues)
   - Documentation: `docs/` folder
   - Community Forum: [Link to forum]

## Windows-Specific Notes

- Use `\` for file paths in Windows
- PowerShell is preferred over Command Prompt
- Some features may require Administrator privileges
- Windows Defender may need exclusions for development folders
- WSL 2 is recommended for Docker Desktop

Happy coding! 🦐🚀
