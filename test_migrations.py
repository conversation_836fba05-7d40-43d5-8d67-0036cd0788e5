#!/usr/bin/env python
import os
import sys
import django

# Add project root to path
sys.path.append(r'c:\Users\<USER>\Downloads\shrimp-farm-guardian-main')

# Set Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')

# Setup Django
django.setup()

from django.core.management import execute_from_command_line

print("Testing Django migration...")

try:
    # Try to run showmigrations
    execute_from_command_line(['manage.py', 'showmigrations'])
    print("Showmigrations completed successfully")
except Exception as e:
    print(f"Error in showmigrations: {e}")

try:
    # Try to run migrate with dry-run
    execute_from_command_line(['manage.py', 'migrate', '--dry-run'])
    print("Migrate dry-run completed successfully")
except Exception as e:
    print(f"Error in migrate dry-run: {e}")
