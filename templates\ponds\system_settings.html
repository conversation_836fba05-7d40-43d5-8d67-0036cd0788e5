<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Settings - Shrimp Farm Guardian</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>


<style>
    .settings-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .settings-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 40px 20px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(10px);
        color: white;
    }

    .settings-header h1 {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .settings-header p {
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }

    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
    }

    .settings-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
        transition: all 0.4s ease;
    }

    .settings-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
    }

    .settings-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 25px;
        padding-bottom: 15px;
        border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    }

    .settings-card-icon {
        font-size: 2.5rem;
        margin-right: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .settings-card-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #2d3436;
        margin: 0;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .stat-item:last-child {
        border-bottom: none;
    }

    .stat-label {
        font-weight: 500;
        color: #636e72;
    }

    .stat-value {
        font-weight: 700;
        color: #2d3436;
        font-size: 1.1rem;
    }

    .stat-value.success {
        color: #00b894;
    }

    .stat-value.warning {
        color: #fdcb6e;
    }

    .stat-value.danger {
        color: #e17055;
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 5px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .config-item:last-child {
        border-bottom: none;
    }

    .config-label {
        font-weight: 500;
        color: #2d3436;
    }

    .config-status {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .config-status.enabled {
        background: rgba(0, 184, 148, 0.1);
        color: #00b894;
    }

    .config-status.disabled {
        background: rgba(225, 112, 85, 0.1);
        color: #e17055;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 15px;
        font-size: 1rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-message {
        font-weight: 500;
        color: #2d3436;
        margin-bottom: 3px;
    }

    .activity-time {
        font-size: 0.8rem;
        color: #636e72;
    }

    .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 25px;
        flex-wrap: wrap;
    }

    .action-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 12px 24px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .action-btn.secondary {
        background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
    }

    .action-btn.success {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    }

    .action-btn.warning {
        background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    }

    @media (max-width: 768px) {
        .settings-grid {
            grid-template-columns: 1fr;
        }
        
        .settings-header h1 {
            font-size: 2rem;
        }
        
        .action-buttons {
            flex-direction: column;
        }
    }
</style>

<div class="settings-container">
    <div class="container">
        <!-- Settings Header -->
        <div class="settings-header">
            <h1>
                <i class="fas fa-cog"></i>
                System Settings
            </h1>
            <p>Manage your shrimp farm system configuration and monitor performance</p>
        </div>

        <!-- Settings Grid -->
        <div class="settings-grid">
            <!-- Farm Management Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <h3 class="settings-card-title">Farm Management</h3>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Total Farms</span>
                    <span class="stat-value">{{ total_farms }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Farms with GPS Location</span>
                    <span class="stat-value {% if farm_location_coverage >= 80 %}success{% elif farm_location_coverage >= 50 %}warning{% else %}danger{% endif %}">
                        {{ farms_with_location }} ({{ farm_location_coverage }}%)
                    </span>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ farm_location_coverage }}%"></div>
                </div>
                
                <div class="action-buttons">
                    <a href="{% url 'ponds:farm_list' %}" class="action-btn">
                        <i class="fas fa-list"></i>
                        View Farms
                    </a>
                    <a href="{% url 'ponds:farm_create_with_map' %}" class="action-btn success">
                        <i class="fas fa-plus"></i>
                        Add Farm
                    </a>
                </div>
            </div>

            <!-- Pond Management Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <h3 class="settings-card-title">Pond Management</h3>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Total Ponds</span>
                    <span class="stat-value">{{ total_ponds }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Active Ponds</span>
                    <span class="stat-value success">{{ pond_status_counts.active }}</span>
                </div>
                
                <div class="stat-item">
                    <span class="stat-label">Ponds with GPS Location</span>
                    <span class="stat-value {% if pond_location_coverage >= 80 %}success{% elif pond_location_coverage >= 50 %}warning{% else %}danger{% endif %}">
                        {{ ponds_with_location }} ({{ pond_location_coverage }}%)
                    </span>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ pond_location_coverage }}%"></div>
                </div>
                
                <div class="action-buttons">
                    <a href="{% url 'ponds:pond_list' %}" class="action-btn">
                        <i class="fas fa-list"></i>
                        View Ponds
                    </a>
                    <a href="{% url 'ponds:pond_create_with_map' %}" class="action-btn success">
                        <i class="fas fa-plus"></i>
                        Add Pond
                    </a>
                </div>
            </div>

            <!-- Aerator Management Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-fan"></i>
                    </div>
                    <h3 class="settings-card-title">Aerator Management</h3>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Total Aerators</span>
                    <span class="stat-value">{{ total_aerators }}</span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Active Aerators</span>
                    <span class="stat-value success">{{ aerator_status_counts.active }}</span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Maintenance Required</span>
                    <span class="stat-value warning">{{ aerator_status_counts.maintenance }}</span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">GPS Coverage</span>
                    <span class="stat-value {% if aerator_location_coverage >= 80 %}success{% elif aerator_location_coverage >= 50 %}warning{% else %}danger{% endif %}">
                        {{ aerators_with_location }} ({{ aerator_location_coverage }}%)
                    </span>
                </div>

                <div class="progress-bar">
                    <div class="progress-fill" style="width: {{ aerator_location_coverage }}%"></div>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="action-btn">
                        <i class="fas fa-map"></i>
                        Aerator Map
                    </a>
                    <a href="{% url 'ponds:pond_create_with_map' %}" class="action-btn success">
                        <i class="fas fa-plus"></i>
                        Add Aerator
                    </a>
                </div>
            </div>

            <!-- Worker & GPS Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="settings-card-title">Worker & GPS Tracking</h3>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Total Workers</span>
                    <span class="stat-value">{{ workers_count }}</span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">GPS Enabled Workers</span>
                    <span class="stat-value success">{{ workers_with_gps }}</span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">GPS Coverage</span>
                    <span class="stat-value {% if workers_with_gps > 0 %}success{% else %}danger{% endif %}">
                        {% if workers_count > 0 %}{{ workers_with_gps|floatformat:0 }}/{{ workers_count }}{% else %}0/0{% endif %}
                    </span>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'ponds:pond_list' %}" class="action-btn">
                        <i class="fas fa-map-marker-alt"></i>
                        GPS Tracking
                    </a>
                    <a href="{% url 'ponds:pond_list' %}" class="action-btn secondary">
                        <i class="fas fa-user-plus"></i>
                        Add Worker
                    </a>
                </div>
            </div>

            <!-- Weather & Environment Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-cloud-sun"></i>
                    </div>
                    <h3 class="settings-card-title">Weather & Environment</h3>
                </div>

                <div class="config-item">
                    <span class="config-label">Weather API</span>
                    <span class="config-status {% if system_config.weather_api_enabled %}enabled{% else %}disabled{% endif %}">
                        {% if system_config.weather_api_enabled %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>

                <div class="config-item">
                    <span class="config-label">Real-time Updates</span>
                    <span class="config-status enabled">Enabled</span>
                </div>

                <div class="config-item">
                    <span class="config-label">Weather Alerts</span>
                    <span class="config-status enabled">Enabled</span>
                </div>

                <div class="action-buttons">
                    <a href="http://127.0.0.1:8000/weather/" class="action-btn">
                        <i class="fas fa-cloud"></i>
                        Weather Dashboard
                    </a>
                    <a href="{% url 'ponds:pond_dashboard' %}" class="action-btn secondary">
                        <i class="fas fa-cog"></i>
                        Configure
                    </a>
                </div>
            </div>

            <!-- Map & GPS Settings -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-map"></i>
                    </div>
                    <h3 class="settings-card-title">Maps & GPS Configuration</h3>
                </div>

                <div class="config-item">
                    <span class="config-label">Google Maps API</span>
                    <span class="config-status {% if system_config.google_maps_api_key %}enabled{% else %}disabled{% endif %}">
                        {% if system_config.google_maps_api_key %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>

                <div class="config-item">
                    <span class="config-label">GPS Tracking</span>
                    <span class="config-status {% if system_config.gps_tracking_enabled %}enabled{% else %}disabled{% endif %}">
                        {% if system_config.gps_tracking_enabled %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>

                <div class="config-item">
                    <span class="config-label">Real-time Location</span>
                    <span class="config-status enabled">Enabled</span>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'ponds:unified_map_dashboard_with_cards' %}" class="action-btn">
                        <i class="fas fa-map-marked-alt"></i>
                        Unified Map
                    </a>
                    <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="action-btn secondary">
                        <i class="fas fa-layer-group"></i>
                        Layer Control
                    </a>
                </div>
            </div>

            <!-- System Configuration -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <h3 class="settings-card-title">System Configuration</h3>
                </div>

                <div class="config-item">
                    <span class="config-label">Notifications</span>
                    <span class="config-status {% if system_config.notifications_enabled %}enabled{% else %}disabled{% endif %}">
                        {% if system_config.notifications_enabled %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>

                <div class="config-item">
                    <span class="config-label">Auto Backup</span>
                    <span class="config-status {% if system_config.auto_backup_enabled %}enabled{% else %}disabled{% endif %}">
                        {% if system_config.auto_backup_enabled %}Enabled{% else %}Disabled{% endif %}
                    </span>
                </div>

                <div class="config-item">
                    <span class="config-label">Data Retention</span>
                    <span class="config-status enabled">{{ system_config.data_retention_days }} Days</span>
                </div>

                <div class="action-buttons">
                    <a href="{% url 'ponds:export_complete_data' %}" class="action-btn">
                        <i class="fas fa-download"></i>
                        Export Data
                    </a>
                    <a href="{% url 'ponds:comprehensive_pond_dashboard' %}" class="action-btn secondary">
                        <i class="fas fa-chart-bar"></i>
                        Reports
                    </a>
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="settings-card">
                <div class="settings-card-header">
                    <div class="settings-card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="settings-card-title">Recent System Activities</h3>
                </div>

                {% for activity in recent_activities %}
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="{{ activity.icon }}"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-message">{{ activity.message }}</div>
                        <div class="activity-time">{{ activity.timestamp|timesince }} ago</div>
                    </div>
                </div>
                {% endfor %}

                <div class="action-buttons">
                    <a href="{% url 'ponds:unified_map_dashboard_with_cards' %}" class="action-btn">
                        <i class="fas fa-list"></i>
                        View All Activities
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="settings-card" style="margin-bottom: 40px;">
            <div class="settings-card-header">
                <div class="settings-card-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                <h3 class="settings-card-title">Quick Actions</h3>
            </div>

            <div class="action-buttons">
                <a href="{% url 'ponds:unified_map_dashboard_with_cards' %}" class="action-btn">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="{% url 'ponds:farm_create_with_map' %}" class="action-btn success">
                    <i class="fas fa-warehouse"></i>
                    Create Farm
                </a>
                <a href="{% url 'ponds:pond_create_with_map' %}" class="action-btn success">
                    <i class="fas fa-water"></i>
                    Create Pond
                </a>
                <a href="{% url 'ponds:multi_pond_aerator_map' %}" class="action-btn">
                    <i class="fas fa-map"></i>
                    View Maps
                </a>
                <a href="http://127.0.0.1:8000/weather/" class="action-btn">
                    <i class="fas fa-cloud-sun"></i>
                    Weather
                </a>
                <a href="{% url 'ponds:comprehensive_pond_dashboard' %}" class="action-btn secondary">
                    <i class="fas fa-chart-bar"></i>
                    Reports
                </a>
                <a href="{% url 'ponds:export_complete_data' %}" class="action-btn warning">
                    <i class="fas fa-download"></i>
                    Export Data
                </a>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Add any JavaScript for settings page functionality
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 System Settings page loaded');

        // Add click handlers for configuration toggles
        document.querySelectorAll('.config-status').forEach(status => {
            status.addEventListener('click', function() {
                // Add toggle functionality here if needed
                console.log('Config item clicked:', this.previousElementSibling.textContent);
            });
        });

        // Add animation to progress bars
        document.querySelectorAll('.progress-fill').forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    });
</script>
</body>
</html>
