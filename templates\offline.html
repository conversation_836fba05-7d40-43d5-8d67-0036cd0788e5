<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .offline-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }
        
        .offline-icon {
            font-size: 5rem;
            color: #64748b;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
        
        .offline-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 20px;
        }
        
        .offline-message {
            color: #64748b;
            font-size: 1.1rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .retry-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            margin: 10px;
        }
        
        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .offline-features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-top: 30px;
            text-align: left;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }
        
        .feature-text {
            flex: 1;
        }
        
        .feature-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .feature-description {
            color: #64748b;
            font-size: 0.9rem;
        }
        
        .connection-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-weight: 600;
        }
        
        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .dot-offline {
            background: #dc3545;
        }
        
        .dot-online {
            background: #28a745;
        }
    </style>
</head>
<body>

<div class="offline-container">
    <div class="offline-icon">
        <i class="fas fa-wifi-slash"></i>
    </div>
    
    <h1 class="offline-title">You're Offline</h1>
    
    <p class="offline-message">
        It looks like you've lost your internet connection. Don't worry - some features are still available offline!
    </p>
    
    <button class="retry-button" onclick="retryConnection()">
        <i class="fas fa-sync-alt me-2"></i>Try Again
    </button>
    
    <button class="retry-button" onclick="goHome()">
        <i class="fas fa-home me-2"></i>Go to Dashboard
    </button>
    
    <!-- Connection Status -->
    <div id="connection-status" class="connection-status status-offline">
        <div class="status-dot dot-offline"></div>
        <span>Offline</span>
    </div>
    
    <!-- Available Offline Features -->
    <div class="offline-features">
        <h5 class="mb-3">Available Offline:</h5>
        
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-eye"></i>
            </div>
            <div class="feature-text">
                <div class="feature-title">View Cached Data</div>
                <div class="feature-description">Access previously loaded farm and pond information</div>
            </div>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-calculator"></i>
            </div>
            <div class="feature-text">
                <div class="feature-title">FCR Calculator</div>
                <div class="feature-description">Calculate Feed Conversion Ratios offline</div>
            </div>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-save"></i>
            </div>
            <div class="feature-text">
                <div class="feature-title">Save Data Locally</div>
                <div class="feature-description">Your changes will sync when connection is restored</div>
            </div>
        </div>
        
        <div class="feature-item">
            <div class="feature-icon">
                <i class="fas fa-bell"></i>
            </div>
            <div class="feature-text">
                <div class="feature-title">Cached Notifications</div>
                <div class="feature-description">View previously received notifications</div>
            </div>
        </div>
    </div>
</div>

<script>
// Monitor connection status
function updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    const statusDot = statusElement.querySelector('.status-dot');
    
    if (navigator.onLine) {
        statusElement.className = 'connection-status status-online';
        statusElement.innerHTML = '<div class="status-dot dot-online"></div><span>Back Online!</span>';
        
        // Show reconnection message
        setTimeout(() => {
            window.location.href = '/';
        }, 2000);
    } else {
        statusElement.className = 'connection-status status-offline';
        statusElement.innerHTML = '<div class="status-dot dot-offline"></div><span>Offline</span>';
    }
}

// Listen for connection changes
window.addEventListener('online', updateConnectionStatus);
window.addEventListener('offline', updateConnectionStatus);

// Retry connection
function retryConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Checking...';
    button.disabled = true;
    
    // Check connection by trying to fetch a small resource
    fetch('/static/manifest.json', { 
        method: 'HEAD',
        cache: 'no-cache'
    })
    .then(() => {
        // Connection successful
        button.innerHTML = '<i class="fas fa-check me-2"></i>Connected!';
        setTimeout(() => {
            window.location.href = '/';
        }, 1000);
    })
    .catch(() => {
        // Still offline
        button.innerHTML = originalText;
        button.disabled = false;
        
        // Show temporary message
        const tempMessage = document.createElement('div');
        tempMessage.className = 'alert alert-warning mt-3';
        tempMessage.textContent = 'Still offline. Please check your connection.';
        button.parentNode.appendChild(tempMessage);
        
        setTimeout(() => {
            tempMessage.remove();
        }, 3000);
    });
}

// Go to home page (cached version)
function goHome() {
    window.location.href = '/';
}

// Check if we have cached data
function checkCachedData() {
    if ('caches' in window) {
        caches.open('shrimp-farm-guardian-v1.0.0').then(cache => {
            cache.keys().then(keys => {
                if (keys.length > 0) {
                    console.log('Cached data available:', keys.length, 'resources');
                }
            });
        });
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateConnectionStatus();
    checkCachedData();
    
    // Auto-retry every 30 seconds
    setInterval(() => {
        if (!navigator.onLine) {
            fetch('/static/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                updateConnectionStatus();
            })
            .catch(() => {
                // Still offline
            });
        }
    }, 30000);
});
</script>

</body>
</html>
