<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #ccc;
            border-radius: 8px;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Maps API Test</h1>
        <p>Testing Google Maps integration with API key: AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw</p>
        
        <div id="loading" class="loading">
            <p>Loading Google Maps...</p>
        </div>
        
        <div id="map"></div>
        
        <div id="status" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border-radius: 4px;">
            <strong>Status:</strong> <span id="status-text">Initializing...</span>
        </div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status-text').textContent = message;
            console.log('Status:', message);
        }

        function initMap() {
            updateStatus('initMap function called');
            
            try {
                // Hide loading
                document.getElementById('loading').style.display = 'none';
                
                // Check if Google Maps is available
                if (typeof google === 'undefined') {
                    updateStatus('ERROR: Google Maps API not loaded');
                    return;
                }
                
                if (!google.maps) {
                    updateStatus('ERROR: google.maps not available');
                    return;
                }
                
                updateStatus('Creating map...');
                
                // Create map
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 10,
                    center: { lat: 14.5995, lng: 120.9842 }, // Philippines
                    mapTypeId: 'hybrid'
                });
                
                // Add a test marker
                const marker = new google.maps.Marker({
                    position: { lat: 14.5995, lng: 120.9842 },
                    map: map,
                    title: 'Test Location'
                });
                
                updateStatus('SUCCESS: Map loaded successfully!');
                
            } catch (error) {
                updateStatus('ERROR: ' + error.message);
                console.error('Map initialization error:', error);
            }
        }

        // Make initMap available globally
        window.initMap = initMap;
        
        // Also try to initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('DOM loaded, waiting for Google Maps API...');
        });
    </script>

    <!-- Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap">
    </script>
</body>
</html>
