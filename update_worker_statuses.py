#!/usr/bin/env python
"""
Update worker statuses to demonstrate color coding
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

try:
    from labor.models import Worker
    print("Worker model imported successfully")
    
    # Get all workers with GPS data
    workers = Worker.objects.filter(
        current_latitude__isnull=False,
        current_longitude__isnull=False
    )
    
    print(f"Found {workers.count()} workers with GPS data")
    
    # Define status options
    status_options = ['working', 'available', 'on_break', 'traveling', 'offline']
    
    # Update workers with different statuses
    for i, worker in enumerate(workers):
        old_status = getattr(worker, 'status', 'unknown')
        new_status = status_options[i % len(status_options)]
        
        # Update the status
        worker.status = new_status
        worker.save()
        
        print(f"Updated Worker {worker.id} ({getattr(worker, 'name', 'Unknown')}): {old_status} -> {new_status}")
    
    # Verify the updates
    print("\n=== Updated Worker Statuses ===")
    for worker in workers:
        print(f"Worker {worker.id}: {getattr(worker, 'name', 'Unknown')} - Status: {getattr(worker, 'status', 'unknown')} - GPS: ({worker.current_latitude}, {worker.current_longitude})")
    
    print(f"\n✅ Successfully updated {workers.count()} workers with varied statuses!")
    print("Status distribution:")
    for status in status_options:
        count = workers.filter(status=status).count()
        print(f"  {status}: {count} workers")
    
except ImportError as e:
    print(f"❌ Error importing Worker model: {e}")
    print("Labor app might not be available or not properly configured")
except Exception as e:
    print(f"❌ Error updating worker statuses: {e}")
    import traceback
    traceback.print_exc()
