{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 500px;
        width: 100%;
        border: 2px solid #ddd;
        border-radius: 8px;
    }
    .debug-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 15px 0;
        font-family: monospace;
        font-size: 12px;
    }
    .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        font-weight: bold;
    }
    .status.loading {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    .status.success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .status.error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1>{{ page_title }}</h1>
    
    <div id="status" class="status loading">Initializing map...</div>
    
    <div class="debug-info">
        <strong>Debug Information:</strong><br>
        Farms: {{ total_farms }}<br>
        Ponds: {{ total_ponds }}<br>
        Workers: {{ total_workers }}<br>
        Weather Stations: {{ weather_stations }}<br>
        Center: {{ center_lat }}, {{ center_lng }}<br>
        API Key: {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}
    </div>
    
    <div id="map"></div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Debug logging
    function updateStatus(message, type = 'loading') {
        const statusDiv = document.getElementById('status');
        statusDiv.textContent = message;
        statusDiv.className = 'status ' + type;
        console.log('[MAP] ' + message);
    }
    
    // Global variables
    let map;
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    const workersData = {{ workers_data|safe }};
    const weatherData = {{ weather_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    
    updateStatus('Loading data...');
    console.log('Farms data:', farmsData);
    console.log('Ponds data:', pondsData);
    console.log('Workers data:', workersData);
    console.log('Weather data:', weatherData);
    console.log('Center coordinates:', centerLat, centerLng);
    
    function initMap() {
        updateStatus('Google Maps API loaded, creating map...');
        
        try {
            const mapElement = document.getElementById('map');
            if (!mapElement) {
                throw new Error('Map container not found');
            }
            
            // Check coordinates
            if (isNaN(centerLat) || isNaN(centerLng)) {
                throw new Error('Invalid center coordinates');
            }
            
            // Create map
            map = new google.maps.Map(mapElement, {
                zoom: 12,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: 'roadmap'
            });
            
            updateStatus('Map created successfully!', 'success');
            
            // Add markers
            addMarkers();
            
        } catch (error) {
            updateStatus('Error: ' + error.message, 'error');
            console.error('Map error:', error);
        }
    }
    
    function addMarkers() {
        try {
            // Add farm markers
            farmsData.forEach(farm => {
                if (farm.latitude && farm.longitude) {
                    new google.maps.Marker({
                        position: { lat: farm.latitude, lng: farm.longitude },
                        map: map,
                        title: farm.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="25" height="25" viewBox="0 0 25 25">
                                    <circle cx="12.5" cy="12.5" r="10" fill="#3b82f6" stroke="white" stroke-width="2"/>
                                    <text x="12.5" y="17" text-anchor="middle" fill="white" font-size="10" font-weight="bold">F</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });
                    console.log('Added farm marker:', farm.name);
                }
            });
            
            // Add pond markers
            pondsData.forEach(pond => {
                if (pond.latitude && pond.longitude) {
                    new google.maps.Marker({
                        position: { lat: pond.latitude, lng: pond.longitude },
                        map: map,
                        title: pond.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="20" height="20" viewBox="0 0 20 20">
                                    <circle cx="10" cy="10" r="8" fill="#06b6d4" stroke="white" stroke-width="2"/>
                                    <text x="10" y="14" text-anchor="middle" fill="white" font-size="8" font-weight="bold">P</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(20, 20)
                        }
                    });
                    console.log('Added pond marker:', pond.name);
                }
            });
            
            // Add worker markers
            workersData.forEach(worker => {
                if (worker.latitude && worker.longitude) {
                    new google.maps.Marker({
                        position: { lat: worker.latitude, lng: worker.longitude },
                        map: map,
                        title: worker.name,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="18" height="18" viewBox="0 0 18 18">
                                    <circle cx="9" cy="9" r="7" fill="#10b981" stroke="white" stroke-width="2"/>
                                    <text x="9" y="13" text-anchor="middle" fill="white" font-size="7" font-weight="bold">W</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(18, 18)
                        }
                    });
                    console.log('Added worker marker:', worker.name);
                }
            });
            
            // Add weather markers
            weatherData.forEach(weather => {
                if (weather.latitude && weather.longitude) {
                    new google.maps.Marker({
                        position: { lat: weather.latitude, lng: weather.longitude },
                        map: map,
                        title: 'Weather: ' + weather.description,
                        icon: {
                            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                                <svg width="22" height="22" viewBox="0 0 22 22">
                                    <circle cx="11" cy="11" r="9" fill="#f59e0b" stroke="white" stroke-width="2"/>
                                    <text x="11" y="15" text-anchor="middle" fill="white" font-size="8" font-weight="bold">☀</text>
                                </svg>
                            `),
                            scaledSize: new google.maps.Size(22, 22)
                        }
                    });
                    console.log('Added weather marker:', weather.description);
                }
            });
            
            updateStatus('All markers added successfully!', 'success');
            
        } catch (error) {
            updateStatus('Error adding markers: ' + error.message, 'error');
            console.error('Marker error:', error);
        }
    }
    
    function handleMapError() {
        updateStatus('Failed to load Google Maps API', 'error');
        console.error('Google Maps API failed to load');
    }
    
    // Set a timeout to detect loading issues
    setTimeout(() => {
        if (typeof google === 'undefined' || !google.maps) {
            updateStatus('Google Maps API timeout - check internet connection', 'error');
        }
    }, 15000);
    
    updateStatus('Waiting for Google Maps API...');
</script>

<!-- Load Google Maps API -->
<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
    onerror="handleMapError()">
</script>
{% endblock %}
