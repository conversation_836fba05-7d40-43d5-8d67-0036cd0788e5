{% extends "base.html" %}
{% load static %}

{% block title %}Import Farms - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-1">
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_list' %}">Farms</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Import</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0">Import Farms</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Upload CSV File</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text">Upload a CSV file with farm data.</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                            <label class="form-check-label" for="has_header">
                                File has header row
                            </label>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back to Farms
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i> Upload and Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            {% if preview_data %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">Preview Data</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        {% for header in preview_headers %}
                                            <th>{{ header }}</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for row in preview_data %}
                                        <tr>
                                            {% for cell in row %}
                                                <td>{{ cell }}</td>
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <form method="post" action="{% url 'ponds:farm_import_confirm' %}">
                            {% csrf_token %}
                            <input type="hidden" name="file_path" value="{{ file_path }}">
                            <input type="hidden" name="has_header" value="{{ has_header }}">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Name Column</label>
                                    <select class="form-select" name="name_col">
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Location Column</label>
                                    <select class="form-select" name="location_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Size Column</label>
                                    <select class="form-select" name="size_col">
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Description Column</label>
                                    <select class="form-select" name="description_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Latitude Column</label>
                                    <select class="form-select" name="latitude_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Longitude Column</label>
                                    <select class="form-select" name="longitude_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'ponds:farm_import' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i> Confirm Import
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Instructions</h5>
                </div>
                <div class="card-body">
                    <h6>CSV Format</h6>
                    <p class="text-muted small">Your CSV file should contain the following columns:</p>
                    <ul class="small">
                        <li><strong>Name</strong> (required): The name of the farm</li>
                        <li><strong>Location</strong> (optional): City, state, or region</li>
                        <li><strong>Size</strong> (required): Size in hectares</li>
                        <li><strong>Description</strong> (optional): Description of the farm</li>
                        <li><strong>Latitude</strong> (optional): Geographical coordinate</li>
                        <li><strong>Longitude</strong> (optional): Geographical coordinate</li>
                    </ul>
                    
                    <h6>Example</h6>
                    <div class="bg-light p-2 rounded small mb-3">
                        <code>
                            Name,Location,Size,Description,Latitude,Longitude<br>
                            Farm A,City A,10.5,Description for Farm A,12.345678,98.765432<br>
                            Farm B,City B,5.2,Description for Farm B,23.456789,87.654321
                        </code>
                    </div>
                    
                    <h6>Notes</h6>
                    <ul class="small">
                        <li>The first row can be a header row (check the box if it is)</li>
                        <li>You'll be able to map columns after uploading</li>
                        <li>All farms will be assigned to your user account</li>
                    </ul>
                    
                    <a href="{% static 'templates/farm_import_template.csv' %}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-download me-2"></i> Download Template
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}{% extends "base.html" %}
{% load static %}

{% block title %}Import Farms - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-1">
                    <li class="breadcrumb-item"><a href="{% url 'ponds:farm_list' %}">Farms</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Import</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0">Import Farms</h1>
        </div>
    </div>
    
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Upload CSV File</h5>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="csv_file" class="form-label">CSV File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                            <div class="form-text">Upload a CSV file with farm data.</div>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="has_header" name="has_header" checked>
                            <label class="form-check-label" for="has_header">
                                File has header row
                            </label>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'ponds:farm_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i> Back to Farms
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload me-2"></i> Upload and Import
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            {% if preview_data %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">Preview Data</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        {% for header in preview_headers %}
                                            <th>{{ header }}</th>
                                        {% endfor %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for row in preview_data %}
                                        <tr>
                                            {% for cell in row %}
                                                <td>{{ cell }}</td>
                                            {% endfor %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer bg-light">
                        <form method="post" action="{% url 'ponds:farm_import_confirm' %}">
                            {% csrf_token %}
                            <input type="hidden" name="file_path" value="{{ file_path }}">
                            <input type="hidden" name="has_header" value="{{ has_header }}">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Name Column</label>
                                    <select class="form-select" name="name_col">
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Location Column</label>
                                    <select class="form-select" name="location_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Size Column</label>
                                    <select class="form-select" name="size_col">
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Description Column</label>
                                    <select class="form-select" name="description_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label class="form-label">Latitude Column</label>
                                    <select class="form-select" name="latitude_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Longitude Column</label>
                                    <select class="form-select" name="longitude_col">
                                        <option value="-1">-- Not included --</option>
                                        {% for header in preview_headers %}
                                            <option value="{{ forloop.counter0 }}">{{ header }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'ponds:farm_import' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i> Confirm Import
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            {% endif %}
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Instructions</h5>
                </div>
                <div class="card-body">
                    <h6>CSV Format</h6>
                    <p class="text-muted small">Your CSV file should contain the following columns:</p>
                    <ul class="small">
                        <li><strong>Name</strong> (required): The name of the farm</li>
                        <li><strong>Location</strong> (optional): City, state, or region</li>
                        <li><strong>Size</strong> (required): Size in hectares</li>
                        <li><strong>Description</strong> (optional): Description of the farm</li>
                        <li><strong>Latitude</strong> (optional): Geographical coordinate</li>
                        <li><strong>Longitude</strong> (optional): Geographical coordinate</li>
                    </ul>
                    
                    <h6>Example</h6>
                    <div class="bg-light p-2 rounded small mb-3">
                        <code>
                            Name,Location,Size,Description,Latitude,Longitude<br>
                            Farm A,City A,10.5,Description for Farm A,12.345678,98.765432<br>
                            Farm B,City B,5.2,Description for Farm B,23.456789,87.654321
                        </code>
                    </div>
                    
                    <h6>Notes</h6>
                    <ul class="small">
                        <li>The first row can be a header row (check the box if it is)</li>
                        <li>You'll be able to map columns after uploading</li>
                        <li>All farms will be assigned to your user account</li>
                    </ul>
                    
                    <a href="{% static 'templates/farm_import_template.csv' %}" class="btn btn-outline-primary btn-sm w-100">
                        <i class="fas fa-download me-2"></i> Download Template
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}