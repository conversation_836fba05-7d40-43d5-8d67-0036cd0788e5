#!/usr/bin/env python3
"""
Migration Conflict Resolution Script
Fixes migration conflicts and consolidates schema changes
"""

import os
import sys
import django
from django.core.management import execute_from_command_line
from django.db import connection
from django.apps import apps

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shrimp_farm_guardian.settings')
django.setup()

def check_migration_conflicts():
    """Check for migration conflicts across all apps"""
    print("🔍 Checking for migration conflicts...")
    
    # Get all installed apps
    installed_apps = [
        'core', 'users', 'ponds', 'water_quality', 'feed', 'medicine',
        'lunar', 'weather', 'alerts', 'harvest', 'electricity', 'disease',
        'labor', 'ai_ml', 'iot_integration'
    ]
    
    conflicts = []
    
    for app_name in installed_apps:
        try:
            app_config = apps.get_app_config(app_name)
            migrations_dir = os.path.join(app_config.path, 'migrations')
            
            if os.path.exists(migrations_dir):
                migration_files = [f for f in os.listdir(migrations_dir) 
                                 if f.endswith('.py') and f != '__init__.py']
                
                # Check for merge migrations
                merge_migrations = [f for f in migration_files if 'merge' in f.lower()]
                if merge_migrations:
                    conflicts.append({
                        'app': app_name,
                        'merge_files': merge_migrations,
                        'path': migrations_dir
                    })
                    
        except Exception as e:
            print(f"⚠️  Error checking {app_name}: {e}")
    
    return conflicts

def fix_migration_conflicts():
    """Fix identified migration conflicts"""
    print("🔧 Fixing migration conflicts...")
    
    conflicts = check_migration_conflicts()
    
    if not conflicts:
        print("✅ No migration conflicts found!")
        return True
    
    for conflict in conflicts:
        app_name = conflict['app']
        print(f"\n📦 Fixing conflicts in {app_name}...")
        
        try:
            # Reset migrations for problematic apps
            execute_from_command_line([
                'manage.py', 'migrate', app_name, 'zero', '--fake'
            ])
            
            # Remove conflicting migration files
            for merge_file in conflict['merge_files']:
                file_path = os.path.join(conflict['path'], merge_file)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    print(f"🗑️  Removed {merge_file}")
            
            # Create fresh migration
            execute_from_command_line([
                'manage.py', 'makemigrations', app_name
            ])
            
            # Apply migration
            execute_from_command_line([
                'manage.py', 'migrate', app_name
            ])
            
            print(f"✅ Fixed conflicts in {app_name}")
            
        except Exception as e:
            print(f"❌ Error fixing {app_name}: {e}")
            return False
    
    return True

def validate_schema():
    """Validate database schema consistency"""
    print("\n🔍 Validating database schema...")
    
    try:
        # Check database connectivity
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        
        # Run Django checks
        execute_from_command_line(['manage.py', 'check', '--database', 'default'])
        
        print("✅ Schema validation passed!")
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Migration Conflict Resolution...")
    
    success = True
    success &= fix_migration_conflicts()
    success &= validate_schema()
    
    if success:
        print("\n🎉 Migration conflicts resolved successfully!")
        sys.exit(0)
    else:
        print("\n❌ Migration conflict resolution failed!")
        sys.exit(1)
