{% extends 'base.html' %}
{% load static %}

{% block title %}{{ pond.name }} Weather Map - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .pond-weather-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .pond-weather-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: pond-sweep 6s infinite;
    }
    
    @keyframes pond-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .pond-map-container {
        height: 60vh;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        margin-bottom: 30px;
    }
    
    #pond-weather-map {
        height: 100%;
        width: 100%;
    }
    
    .weather-info-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .weather-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
    }
    
    .weather-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #667eea;
    }
    
    .weather-card-icon {
        font-size: 3em;
        margin-bottom: 15px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-align: center;
    }
    
    .weather-card-value {
        font-size: 2.5em;
        font-weight: bold;
        color: #2d3436;
        text-align: center;
        margin-bottom: 10px;
    }
    
    .weather-card-label {
        color: #636e72;
        text-align: center;
        font-size: 1.1em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .pond-info-panel {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        margin-bottom: 30px;
    }
    
    .pond-detail {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .pond-detail:last-child {
        border-bottom: none;
    }
    
    .detail-label {
        font-weight: 600;
        color: #636e72;
    }
    
    .detail-value {
        font-weight: bold;
        color: #2d3436;
    }
    
    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .status-active {
        background: linear-gradient(45deg, #00b894, #00cec9);
        color: white;
    }
    
    .status-maintenance {
        background: linear-gradient(45deg, #fdcb6e, #e17055);
        color: white;
    }
    
    .weather-alerts {
        background: linear-gradient(135deg, #ff7675, #fd79a8);
        color: white;
        border-radius: 20px;
        padding: 25px;
        margin: 20px 0;
        animation: pulse-alert 2s infinite;
    }
    
    @keyframes pulse-alert {
        0%, 100% { transform: scale(1); opacity: 0.9; }
        50% { transform: scale(1.02); opacity: 1; }
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
        margin: 30px 0;
    }
    
    .action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        padding: 15px 30px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        background: linear-gradient(45deg, #764ba2, #667eea);
        color: white;
        text-decoration: none;
    }
    
    .weather-history {
        background: white;
        border-radius: 20px;
        padding: 25px;
        margin: 30px 0;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .history-chart {
        height: 300px;
        margin: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Pond Weather Header -->
    <div class="pond-weather-header">
        <div class="position-relative">
            <h1><i class="fas fa-fish me-3"></i>{{ pond.name }} Weather Map</h1>
            <p class="lead mb-0">Real-time weather monitoring for pond location</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🐟 POND SPECIFIC</span>
                <span class="badge bg-light text-dark me-2">🌤️ REAL-TIME WEATHER</span>
                <span class="badge bg-light text-dark me-2">📍 LOCATION BASED</span>
                {% if pond.farm %}
                <span class="badge bg-info text-white ms-3">🏢 {{ pond.farm.name }}</span>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pond Information Panel -->
    <div class="pond-info-panel">
        <h3><i class="fas fa-info-circle me-2"></i>Pond Information</h3>
        <div class="row">
            <div class="col-md-6">
                <div class="pond-detail">
                    <span class="detail-label">Pond Name:</span>
                    <span class="detail-value">{{ pond.name }}</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Farm:</span>
                    <span class="detail-value">{{ pond.farm.name|default:"Not assigned" }}</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Size:</span>
                    <span class="detail-value">{{ pond.size }} m²</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Species:</span>
                    <span class="detail-value">{{ pond.species|default:"Not specified" }}</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="pond-detail">
                    <span class="detail-label">Status:</span>
                    <span class="status-badge status-{{ pond.status }}">{{ pond.status|title }}</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Location:</span>
                    <span class="detail-value">{{ pond.latitude|floatformat:4 }}°N, {{ pond.longitude|floatformat:4 }}°E</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Weather Station:</span>
                    <span class="detail-value">{{ weather_station.name|default:"No station assigned" }}</span>
                </div>
                <div class="pond-detail">
                    <span class="detail-label">Last Updated:</span>
                    <span class="detail-value">{{ weather_data.timestamp|timesince|default:"No data" }} ago</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Alerts -->
    {% if weather_alerts %}
    <div class="weather-alerts">
        <h4><i class="fas fa-exclamation-triangle me-2"></i>Weather Alerts</h4>
        {% for alert in weather_alerts %}
        <div class="alert-item">
            <strong>{{ alert.alert_type|title }}:</strong> {{ alert.message }}
            <small class="d-block mt-1">{{ alert.created_at|timesince }} ago</small>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Weather Information Cards -->
    {% if weather_data %}
    <div class="weather-info-cards">
        <div class="weather-card">
            <div class="weather-card-icon">
                {% if weather_data.condition == 'Clear' %}
                    <i class="fas fa-sun"></i>
                {% elif weather_data.condition == 'Partly Cloudy' %}
                    <i class="fas fa-cloud-sun"></i>
                {% elif 'Rain' in weather_data.condition %}
                    <i class="fas fa-cloud-rain"></i>
                {% else %}
                    <i class="fas fa-cloud"></i>
                {% endif %}
            </div>
            <div class="weather-card-value">{{ weather_data.temperature }}°C</div>
            <div class="weather-card-label">Temperature</div>
        </div>
        
        <div class="weather-card">
            <div class="weather-card-icon"><i class="fas fa-tint"></i></div>
            <div class="weather-card-value">{{ weather_data.humidity }}%</div>
            <div class="weather-card-label">Humidity</div>
        </div>
        
        <div class="weather-card">
            <div class="weather-card-icon"><i class="fas fa-wind"></i></div>
            <div class="weather-card-value">{{ weather_data.wind_speed }} km/h</div>
            <div class="weather-card-label">Wind Speed</div>
        </div>
        
        <div class="weather-card">
            <div class="weather-card-icon"><i class="fas fa-thermometer-half"></i></div>
            <div class="weather-card-value">{{ weather_data.pressure }} hPa</div>
            <div class="weather-card-label">Pressure</div>
        </div>
        
        <div class="weather-card">
            <div class="weather-card-icon"><i class="fas fa-cloud-rain"></i></div>
            <div class="weather-card-value">{{ weather_data.precipitation }} mm</div>
            <div class="weather-card-label">Precipitation</div>
        </div>
        
        <div class="weather-card">
            <div class="weather-card-icon"><i class="fas fa-eye"></i></div>
            <div class="weather-card-value">{{ weather_data.condition }}</div>
            <div class="weather-card-label">Condition</div>
        </div>
    </div>
    {% else %}
    <div class="text-center">
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
            <h4>No Weather Data Available</h4>
            <p>Weather information is not available for this pond location.</p>
        </div>
    </div>
    {% endif %}

    <!-- Pond Weather Map -->
    <div class="pond-map-container">
        <div id="pond-weather-map"></div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{% url 'ponds:pond_detail' pond.id %}" class="action-btn">
            <i class="fas fa-fish me-2"></i>Pond Details
        </a>
        <a href="{% url 'weather:unified_weather_map' %}" class="action-btn">
            <i class="fas fa-map me-2"></i>Unified Map
        </a>
        <a href="{% url 'weather:pond_weather_dashboard' %}" class="action-btn">
            <i class="fas fa-tachometer-alt me-2"></i>Weather Dashboard
        </a>
        {% if pond.farm %}
        <a href="{% url 'ponds:farm_detail' pond.farm.id %}" class="action-btn">
            <i class="fas fa-building me-2"></i>Farm Details
        </a>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Simple Google Maps implementation
let map;
let marker;
let infoWindow;

// Pond data
const pondData = {
    name: "{{ pond.name }}",
    lat: {{ pond.latitude }},
    lng: {{ pond.longitude }},
    {% if weather_data %}
    weather: {
        temperature: {{ weather_data.temperature }},
        humidity: {{ weather_data.humidity }},
        wind_speed: {{ weather_data.wind_speed }},
        condition: "{{ weather_data.condition }}"
    }
    {% else %}
    weather: null
    {% endif %}
};

function initMap() {
    console.log('🗺️ Initializing Google Maps...');

    // Create map
    map = new google.maps.Map(document.getElementById('pond-weather-map'), {
        center: { lat: pondData.lat, lng: pondData.lng },
        zoom: 15,
        mapTypeId: google.maps.MapTypeId.SATELLITE
    });

    // Create marker
    marker = new google.maps.Marker({
        position: { lat: pondData.lat, lng: pondData.lng },
        map: map,
        title: pondData.name
    });

    // Create info window
    let content = `<div style="padding: 10px;">
        <h3>🐟 ${pondData.name}</h3>
        <p><strong>Location:</strong> ${pondData.lat}, ${pondData.lng}</p>`;

    if (pondData.weather) {
        content += `<p><strong>Temperature:</strong> ${pondData.weather.temperature}°C</p>
                   <p><strong>Humidity:</strong> ${pondData.weather.humidity}%</p>
                   <p><strong>Condition:</strong> ${pondData.weather.condition}</p>`;
    }

    content += '</div>';

    infoWindow = new google.maps.InfoWindow({ content: content });

    // Show info window on marker click
    marker.addListener('click', () => {
        infoWindow.open(map, marker);
    });

    // Auto-open info window
    setTimeout(() => {
        infoWindow.open(map, marker);
    }, 1000);

    console.log('✅ Google Maps initialized successfully!');
}

// Direct Google Maps initialization
window.initGoogleMaps = function() {
    console.log('🗺️ Google Maps API loaded via callback');
    initMap();
};

// Check if Google Maps is already loaded
document.addEventListener('DOMContentLoaded', function() {
    if (typeof google !== 'undefined' && google.maps) {
        console.log('🗺️ Google Maps already available');
        initMap();
    } else {
        console.log('⏳ Waiting for Google Maps API...');
    }
});
</script>
<script async defer src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initGoogleMaps"></script>
{% endblock %}
