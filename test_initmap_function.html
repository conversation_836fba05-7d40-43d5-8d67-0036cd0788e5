<!DOCTYPE html>
<html>
<head>
    <title>Google Maps initMap Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        #map { height: 400px; width: 100%; border: 2px solid #ccc; margin: 20px 0; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #cce5ff; color: #004085; }
    </style>
</head>
<body>
    <h1>🧪 Google Maps initMap Function Test</h1>
    <div id="status" class="status info">Loading test...</div>
    
    <h2>Test Results:</h2>
    <ul id="results"></ul>
    
    <div id="map"></div>

    <script>
        const results = document.getElementById('results');
        const status = document.getElementById('status');
        
        function addResult(text, success = true) {
            const li = document.createElement('li');
            li.textContent = text;
            li.style.color = success ? 'green' : 'red';
            results.appendChild(li);
            console.log((success ? '✅' : '❌') + ' ' + text);
        }
        
        // Test 1: Check if we can define initMap
        addResult('Test 1: Defining initMap function');
        
        window.initMap = function() {
            addResult('Test 2: initMap function called successfully');
            status.innerHTML = '✅ Google Maps API loaded and initMap called!';
            status.className = 'status success';
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 13.0827, lng: 80.2707 }
                });
                
                new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Marker - Chennai, India'
                });
                
                addResult('Test 3: Google Maps created successfully');
                addResult('Test 4: Marker added successfully');
                
            } catch (error) {
                addResult('Test 3: Failed to create Google Maps - ' + error.message, false);
                console.error('Map creation error:', error);
            }
        };
        
        window.handleMapError = function() {
            addResult('Test ERROR: Google Maps API failed to load', false);
            status.innerHTML = '❌ Failed to load Google Maps API';
            status.className = 'status error';
        };
        
        // Test if initMap is properly defined
        if (typeof window.initMap === 'function') {
            addResult('Test 1.1: window.initMap is properly defined as function');
        } else {
            addResult('Test 1.1: window.initMap is NOT a function: ' + typeof window.initMap, false);
        }
        
        // Set timeout to check if Google Maps loads
        setTimeout(() => {
            if (status.textContent.includes('Loading test')) {
                addResult('Test TIMEOUT: Google Maps API did not load within 10 seconds', false);
                status.innerHTML = '⏰ Timeout: Google Maps API did not load';
                status.className = 'status error';
            }
        }, 10000);
        
        addResult('Test 0: Script setup complete, waiting for Google Maps API...');
    </script>

    <!-- Load Google Maps API with the same key and parameters -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
