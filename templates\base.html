{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="cache-bust" content="v20241227-22">
    <title>{% block title %}Shrimp Farm Guardian{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Unified Theme System -->
    <link href="{% static 'css/unified-theme.css' %}" rel="stylesheet">

    <!-- Legacy CSS for compatibility -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    <link href="{% static 'css/unified-template.css' %}" rel="stylesheet">
    <link href="{% static 'css/mobile-responsive.css' %}" rel="stylesheet">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1a202c">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Farm Guardian">
    <meta name="msapplication-TileColor" content="#1a202c">
    <meta name="color-scheme" content="dark light">

    <!-- PWA Manifest -->
    <link rel="manifest" href="{% static 'manifest.json' %}">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="{% static 'icons/favicon-32x32.png' %}">
    <link rel="icon" type="image/png" sizes="16x16" href="{% static 'icons/favicon-16x16.png' %}">

    <style>
        /* Additional custom styles can be added here */

        /* CRITICAL FIX: Remove all left spacing from sidebar */
        .sidebar {
            margin-left: 0 !important;
            padding-left: 0 !important;
            left: 0 !important;
            position: relative !important;
        }

        .layout-container {
            margin: 0 !important;
            padding: 0 !important;
        }

        .sidebar > div,
        .sidebar .p-3,
        .sidebar .px-3,
        .sidebar .ps-3 {
            padding-left: 0 !important;
            margin-left: 0 !important;
        }

        /* Ensure content inside sidebar has proper spacing */
        .sidebar .d-flex {
            padding-left: 1rem !important;
        }

        .sidebar .nav-link {
            padding-left: 1rem !important;
        }

        /* Force body and html to have no margins */
        html, body {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Enhanced main header styling */
        .top-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .top-header h1 {
            font-size: 2rem;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .top-header p {
            opacity: 0.9;
            font-size: 1.1rem;
            margin-top: 0.5rem;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* Adjust sidebar to not have top padding */
        .sidebar {
            padding-top: 0;
        }

        /* Sidebar Logo Styling */
        .sidebar-logo {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1.5rem 1rem;
            margin-bottom: 1rem;
            border-radius: 0 0 15px 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            font-size: 3rem;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .logo-text {
            flex: 1;
        }

        .logo-title {
            color: white;
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }

        .logo-subtitle {
            color: rgba(255,255,255,0.9);
            font-size: 0.85rem;
            margin: 0;
            opacity: 0.9;
            font-weight: 400;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="layout-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="p-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a href="{% url 'core:dashboard' %}" class="nav-link {% if 'dashboard' in request.path %}active{% endif %}" style="background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%); color: white; border-radius: 6px; margin: 0 4px;">
                            <i class="fas fa-satellite-dish"></i> Main Dashboard
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{% url 'core:advanced_dashboard' %}" class="nav-link {% if request.resolver_match.url_name == 'advanced_dashboard' %}active{% endif %}">
                            <i class="fas fa-analytics"></i> Advanced Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="/new-dashboard/" class="nav-link" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border-radius: 6px; margin: 0 4px;">
                            <i class="fas fa-tachometer-alt"></i> New Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'monitoring_control_center' %}" class="nav-link {% if 'monitoring' in request.path %}active{% endif %}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 6px; margin: 0 4px;">
                            <i class="fas fa-microchip"></i> IoT
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'enhancements:dashboard' %}" class="nav-link {% if 'enhancements' in request.path %}active{% endif %}">
                            <i class="fas fa-rocket"></i> Enhancements
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'ponds:unified_map_dashboard_with_cards' %}" class="nav-link {% if 'ponds' in request.path %}active{% endif %}" style="background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%); color: white; border-radius: 6px; margin: 0 4px;">
                            <i class="fas fa-water"></i> Ponds & Farms
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'water-quality' in request.path %}active{% endif %}" id="waterQualityDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-flask"></i> Water Quality
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="waterQualityDropdown">
                            <li><a class="dropdown-item" href="{% url 'water_quality:dashboard' %}"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'water_quality:add_reading' %}"><i class="fas fa-plus me-2"></i>Add Reading</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'water_quality:dashboard' %}"><i class="fas fa-chart-line me-2"></i>Analytics</a></li>
                            <li><a class="dropdown-item" href="#" onclick="alert('Water Quality Reports - Coming Soon!')"><i class="fas fa-file-alt me-2"></i>Reports</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="alert('IoT Sensors - Coming Soon!')"><i class="fas fa-microchip me-2"></i>IoT Sensors</a></li>
                            <li><a class="dropdown-item" href="#" onclick="alert('Automated Testing - Coming Soon!')"><i class="fas fa-robot me-2"></i>Automated Testing</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'feed' in request.path %}active{% endif %}" id="feedDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-drumstick-bite"></i> Feed Management
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="feedDropdown">
                            <li><a class="dropdown-item" href="{% url 'feed:dashboard' %}">Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'feed:inventory_management' %}">Inventory</a></li>
                            <li><a class="dropdown-item" href="{% url 'feed:feed_types' %}">Feed Types</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'feed:feeding_schedules' %}">Feeding Schedules</a></li>
                            <li><a class="dropdown-item" href="{% url 'feed:feeding_records' %}">Feeding Records</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'feed:fcr_calculator' %}">FCR Calculator</a></li>
                            <li><a class="dropdown-item" href="{% url 'feed:feed_list' %}">Feed Tasks</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'medicine' in request.path %}active{% endif %}" id="medicineDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-pills"></i> Medicine
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="medicineDropdown">
                            <li><a class="dropdown-item" href="{% url 'medicine:medicine_dashboard' %}">Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:analytics_dashboard' %}">Analytics</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:medicine_list' %}">Inventory</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:application_list' %}">Applications</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:treatment_plan_list' %}">Treatment Plans</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:category_list' %}">Categories</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:supplier_list' %}">Suppliers</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:order_list' %}">Orders</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:disease_list' %}">Diseases</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:outbreak_list' %}">Outbreaks</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:disease_diagnosis' %}">Diagnosis</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:compliance_list' %}">Compliance</a></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:benchmark_list' %}">Benchmarks</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'medicine:barcode_scanner' %}">Barcode Scanner</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'lunar' in request.path %}active{% endif %}" href="#" id="lunarDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-moon"></i> Lunar Calendar
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="lunarDropdown">
                            <li><a class="dropdown-item" href="{% url 'lunar:lunar_calendar' %}">
                                <i class="fas fa-calendar-day me-2"></i>Lunar Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'lunar:enhanced_lunar_dashboard' %}">
                                <i class="fas fa-rocket me-2"></i>Enhanced Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'lunar:full_lunar_calendar' %}">
                                <i class="fas fa-calendar-alt me-2"></i>Full Calendar View
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'lunar:integration_demo' %}">
                                <i class="fas fa-link me-2"></i>Integration Demo
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'lunar:lunar_api' %}">
                                <i class="fas fa-code me-2"></i>API Data
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'lunar:lunar_test' %}">
                                <i class="fas fa-flask me-2"></i>Test & Verify
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'weather' in request.path %}active{% endif %}" id="weatherDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-cloud-sun-rain"></i> Weather
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="weatherDropdown">
                            <li><a class="dropdown-item" href="{% url 'weather:weather_dashboard' %}">Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:pond_weather_dashboard' %}">Pond Weather Integration</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:unified_weather_map' %}">Unified Weather Map</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:maps_dashboard' %}">Maps</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'weather:forecast_list' %}">Forecasts</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:alert_list' %}">Weather Alerts</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:impact_list' %}">Weather Impacts</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'weather:station_list' %}">Weather Stations</a></li>
                            <li><a class="dropdown-item" href="{% url 'weather:api_config_list' %}">API Settings</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'alerts' in request.path %}active{% endif %}" id="alertsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i> Alerts & Notifications
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="alertsDropdown">
                            <li><a class="dropdown-item" href="{% url 'alerts:alert_list' %}">
                                <i class="fas fa-list me-1"></i> All Alerts
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:create_alert' %}">
                                <i class="fas fa-plus me-1"></i> Create Alert
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:ai_alert_dashboard' %}">
                                <i class="fas fa-robot me-1"></i> AI Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:pond_weather_map' %}">
                                <i class="fas fa-map me-1"></i> Pond Weather Map
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:alert_settings' %}">
                                <i class="fas fa-cog me-1"></i> Alert Settings
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'alerts:notification_settings' %}">
                                <i class="fas fa-bell-slash me-1"></i> Notification Settings
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'harvest' in request.path %}active{% endif %}" id="harvestDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-fish"></i> Harvest
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="harvestDropdown">
                            <li><a class="dropdown-item" href="{% url 'harvest:harvest_dashboard' %}">Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:harvest_planning' %}">Planning</a></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:harvest_calendar' %}">Calendar</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:plan_list' %}">Harvest Plans</a></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:reports' %}">Reports</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:harvest_yield_prediction' %}">Yield Prediction</a></li>
                            <li><a class="dropdown-item" href="{% url 'harvest:quality_assessment' %}">Quality Assessment</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'disease:disease_list' %}" class="nav-link {% if 'disease' in request.path %}active{% endif %}">
                            <i class="fas fa-virus"></i> Disease Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'digital_twin_api:dashboard' %}" class="nav-link {% if 'digital-twin' in request.path %}active{% endif %}">
                            <i class="fas fa-cube"></i> Digital Twin
                        </a>
                    </li>
                    <!-- Task Templates removed for labor module rebuild -->

                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'labor' in request.path %}active{% endif %}" id="laborDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-users-cog"></i> Labor Management
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="laborDropdown">
                            <li><a class="dropdown-item" href="{% url 'labor:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'labor:worker_list' %}">
                                <i class="fas fa-user me-1"></i> Workers
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'labor:team_list' %}">
                                <i class="fas fa-users me-1"></i> Teams
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'labor:task_list' %}">
                                <i class="fas fa-tasks me-1"></i> Tasks
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'labor:work_log_list' %}">
                                <i class="fas fa-clipboard-list me-1"></i> Work Logs
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'labor:schedule_list' %}">
                                <i class="fas fa-calendar-alt me-1"></i> Schedules
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'labor:calendar' %}">
                                <i class="fas fa-calendar-day me-1"></i> Calendar
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'electricity' in request.path %}active{% endif %}" id="electricityDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bolt"></i> Electricity & Aerators
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="electricityDropdown">
                            <li><h6 class="dropdown-header"><i class="fas fa-bolt me-1"></i> Electricity Management</h6></li>
                            <li><a class="dropdown-item" href="{% url 'electricity:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>Electricity Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'electricity:add_equipment_page' %}">
                                <i class="fas fa-plus me-2"></i>Add Equipment
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'electricity:equipment_list' %}">
                                <i class="fas fa-list me-2"></i>Equipment List
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'electricity:bulk_import_equipment' %}">
                                <i class="fas fa-upload me-2"></i>Bulk Import
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header"><i class="fas fa-fan me-1"></i> Aerator Control</h6></li>
                            <li><a class="dropdown-item" href="{% url 'ponds:all_aerators_management' %}" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; border-radius: 6px; margin: 2px;">
                                <i class="fas fa-cogs me-2"></i>All Aerators Management
                                <span class="badge bg-light text-dark ms-2">NEW</span>
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'iot_integration:dashboard' %}" style="background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%); color: white; border-radius: 6px; margin: 2px;">
                                <i class="fas fa-satellite-dish me-2"></i>IoT Control Center
                                <span class="badge bg-light text-dark ms-2">LIVE</span>
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'iot_integration:power_analytics' %}">
                                <i class="fas fa-chart-line me-2"></i>Power Analytics
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'iot_integration:automation_dashboard' %}">
                                <i class="fas fa-robot me-2"></i>Automation Control
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'iot_integration:device_list' %}">
                                <i class="fas fa-microchip me-2"></i>Device Management
                            </a></li>

                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header"><i class="fas fa-map me-1"></i> Aerator Mapping</h6></li>
                            <li><a class="dropdown-item" href="{% url 'ponds:multi_pond_aerator_map' %}" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 6px; margin: 2px;">
                                <i class="fas fa-map-marked-alt me-2"></i>Multi-Pond Aerator Map
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'ponds:enhanced_cumulative_map_dashboard' %}">
                                <i class="fas fa-layer-group me-2"></i>Enhanced Map Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'ponds:maps_diagnostic' %}">
                                <i class="fas fa-tools me-2"></i>Map Diagnostics
                            </a></li>
                        </ul>
                    </li>

                    <!-- Edge Computing & AI Optimization -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle {% if 'edge_computing' in request.path %}active{% endif %}" id="edgeAIDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-microchip"></i> Edge Computing
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="edgeAIDropdown">
                            <!-- Edge Computing Section -->
                            <li><h6 class="dropdown-header">
                                <i class="fas fa-network-wired me-1"></i> Edge Computing
                            </h6></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i> Edge Dashboard
                                <span class="badge bg-success ms-2">LIVE</span>
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:nodes_list' %}">
                                <i class="fas fa-server me-1"></i> Edge Nodes
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:models_list' %}">
                                <i class="fas fa-brain me-1"></i> AI Models
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:tasks_list' %}">
                                <i class="fas fa-tasks me-1"></i> Processing Tasks
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:real_time_status' %}">
                                <i class="fas fa-bolt me-1"></i> Real-Time Processing
                                <span class="badge bg-warning ms-2">< 100ms</span>
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:distributed_intelligence' %}">
                                <i class="fas fa-project-diagram me-1"></i> Distributed Intelligence
                                <span class="badge bg-info ms-2">FEDERATED</span>
                            </a></li>

                            <li><hr class="dropdown-divider"></li>

                            <li><hr class="dropdown-divider"></li>

                            <!-- Quick Actions -->
                            <li><h6 class="dropdown-header">
                                <i class="fas fa-rocket me-1"></i> Quick Actions
                            </h6></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:deploy_model' %}">
                                <i class="fas fa-upload me-1"></i> Deploy AI Model
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'edge_computing:create_task' %}">
                                <i class="fas fa-plus me-1"></i> Create Processing Task
                            </a></li>
                        </ul>
                    </li>

                    <!-- PHASE 10: ADVANCED SHRIMP FARMING ECOSYSTEM -->

                    <!-- Advanced Water Quality AI -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="waterAIDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-brain"></i> Advanced Water Quality AI
                            <span class="badge bg-gradient-info ms-2">AI</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'ai_ml:predictive_analytics' %}"><i class="fas fa-chart-line me-1"></i> Predictive Analytics</a></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:anomaly_detection_dashboard' %}"><i class="fas fa-exclamation-triangle me-1"></i> Anomaly Detection</a></li>
                            <li><a class="dropdown-item" href="{% url 'water_quality:dashboard' %}"><i class="fas fa-flask me-1"></i> Smart Dosing</a></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:optimization_dashboard' %}"><i class="fas fa-cogs me-1"></i> Multi-Objective Optimization</a></li>
                        </ul>
                    </li>

                    <!-- Shrimp Behavior Analytics -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="behaviorDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-eye"></i> Shrimp Behavior Analytics
                            <span class="badge bg-gradient-warning ms-2">CV</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'analytics:dashboard' %}"><i class="fas fa-video me-1"></i> Computer Vision Tracking</a></li>
                            <li><a class="dropdown-item" href="{% url 'feed:feed_analytics' %}"><i class="fas fa-utensils me-1"></i> Feeding Behavior Analysis</a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:predictive_analytics' %}"><i class="fas fa-heartbeat me-1"></i> Health Assessment</a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:dashboard' %}"><i class="fas fa-chart-area me-1"></i> Movement Patterns</a></li>
                        </ul>
                    </li>

                    <!-- Climate-Smart Technology -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="climateDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cloud-sun"></i> Climate-Smart Technology
                            <span class="badge bg-gradient-success ms-2">ECO</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-thermometer-half me-1"></i> Climate Adaptation</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cloud-rain me-1"></i> Weather Integration</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-leaf me-1"></i> Carbon Footprint</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-recycle me-1"></i> Sustainability Metrics</a></li>
                        </ul>
                    </li>

                    <!-- Mobile & IoT Expansion -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="mobileIoTDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-mobile-alt"></i> Mobile & IoT Expansion
                            <span class="badge bg-gradient-primary ms-2">5G</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-mobile me-1"></i> Mobile App</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-wifi me-1"></i> IoT Sensor Network</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-helicopter me-1"></i> Drone Integration</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-watch me-1"></i> Wearable Devices</a></li>
                        </ul>
                    </li>

                    <!-- Blockchain & Supply Chain -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="blockchainDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-link"></i> Blockchain & Supply Chain
                            <span class="badge bg-gradient-dark ms-2">WEB3</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-qrcode me-1"></i> Traceability</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-file-contract me-1"></i> Smart Contracts</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-certificate me-1"></i> Quality Certificates</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-truck me-1"></i> Supply Chain</a></li>
                        </ul>
                    </li>

                    <!-- AR/VR & Digital Twin -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="arvrDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-vr-cardboard"></i> AR/VR & Digital Twin
                            <span class="badge bg-gradient-purple ms-2">XR</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cube me-1"></i> 3D Farm Visualization</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-glasses me-1"></i> AR Maintenance</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-headset me-1"></i> VR Training</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sync me-1"></i> Digital Twin Sync</a></li>
                        </ul>
                    </li>

                    <!-- Advanced Automation -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="automationDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-robot"></i> Advanced Automation
                            <span class="badge bg-gradient-danger ms-2">AUTO</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-robot me-1"></i> Robotic Systems</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sitemap me-1"></i> Automation Workflows</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-wrench me-1"></i> Predictive Maintenance</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-1"></i> Autonomous Operations</a></li>
                        </ul>
                    </li>

                    <!-- COMPREHENSIVE IMPLEMENTATION - ALL FEATURES -->

                    <!-- Advanced Analytics Dashboard -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="analyticsDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-line"></i> Advanced Analytics
                            <span class="badge bg-gradient-primary ms-2">AI</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'analytics:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i> Analytics Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:predictive_analytics' %}">
                                <i class="fas fa-crystal-ball me-1"></i> Predictive Analytics
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:optimization_recommendations' %}">
                                <i class="fas fa-lightbulb me-1"></i> AI Recommendations
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:ai_dashboard' %}">
                                <i class="fas fa-brain me-1"></i> AI/ML Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:predictive_analytics' %}">
                                <i class="fas fa-chart-area me-1"></i> ML Predictions
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:anomaly_detection_dashboard' %}">
                                <i class="fas fa-exclamation-triangle me-1"></i> Anomaly Detection
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'ai_ml:optimization_dashboard' %}">
                                <i class="fas fa-cogs me-1"></i> Energy Optimization
                            </a></li>
                        </ul>
                    </li>

                    <!-- Comprehensive Reporting System -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="reportingDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-file-alt"></i> Reporting System
                            <span class="badge bg-gradient-success ms-2">AUTO</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'analytics:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i> Reporting Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:generate_report' %}">
                                <i class="fas fa-file-contract me-1"></i> Generate Report
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:predictive_analytics' %}">
                                <i class="fas fa-download me-1"></i> Predictive Reports
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:optimization_recommendations' %}">
                                <i class="fas fa-calendar-alt me-1"></i> Optimization Reports
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:enhanced_dashboard' %}">
                                <i class="fas fa-puzzle-piece me-1"></i> Enhanced Analytics
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:advanced_dashboard' %}">
                                <i class="fas fa-th-large me-1"></i> Advanced Dashboard
                            </a></li>
                        </ul>
                    </li>

                    <!-- Enhanced Security & User Management -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="securityDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-shield-alt"></i> Security & Users
                            <span class="badge bg-gradient-danger ms-2">2FA</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'security:dashboard' %}">
                                <i class="fas fa-shield-alt me-1"></i> Security Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'security:user_management' %}">
                                <i class="fas fa-users me-1"></i> User Management
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'security:role_management' %}">
                                <i class="fas fa-user-tag me-1"></i> Role Management
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'security:audit_logs' %}">
                                <i class="fas fa-clipboard-list me-1"></i> Audit Logs
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'security:security_events' %}">
                                <i class="fas fa-exclamation-circle me-1"></i> Security Events
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'security:api_key_management' %}">
                                <i class="fas fa-key me-1"></i> API Keys
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'security:session_management' %}">
                                <i class="fas fa-clock me-1"></i> Active Sessions
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'security:setup_2fa' %}">
                                <i class="fas fa-mobile-alt me-1"></i> Setup 2FA
                            </a></li>
                        </ul>
                    </li>

                    <!-- Business Intelligence -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="businessDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-pie"></i> Business Intelligence
                            <span class="badge bg-gradient-info ms-2">BI</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'enterprise:dashboard' %}">
                                <i class="fas fa-tachometer-alt me-1"></i> Enterprise Dashboard
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'enterprise:enterprise_analytics' %}">
                                <i class="fas fa-chart-line me-1"></i> Enterprise Analytics
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'enterprise:cross_farm_analytics' %}">
                                <i class="fas fa-chart-bar me-1"></i> Cross-Farm Analytics
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'enterprise:enterprise_summary' %}">
                                <i class="fas fa-file-alt me-1"></i> Business Summary
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'enterprise:farm_networks' %}">
                                <i class="fas fa-network-wired me-1"></i> Farm Networks
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'analytics:dashboard' %}">
                                <i class="fas fa-chart-pie me-1"></i> Analytics Dashboard
                            </a></li>
                        </ul>
                    </li>

                    <!-- Research & Development -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="researchDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-flask"></i> Research & Development
                            <span class="badge bg-gradient-secondary ms-2">R&D</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-project-diagram me-1"></i> Research Projects</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-vial me-1"></i> Experimental Design</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-code me-1"></i> Data Science Workbench</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-share-alt me-1"></i> Collaboration Platform</a></li>
                        </ul>
                    </li>



                    <!-- Multi-Farm Enterprise -->
                    <li class="nav-item dropdown">
                        <a href="#" class="nav-link dropdown-toggle" id="enterpriseDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-network-wired"></i> Multi-Farm Enterprise
                            <span class="badge bg-gradient-warning ms-2">ENTERPRISE</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-network-wired me-1"></i> Enterprise Network</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-balance-scale me-1"></i> Farm Comparison</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-lightbulb me-1"></i> Knowledge Sharing</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-users me-1"></i> Best Practices</a></li>
                        </ul>
                    </li>

                </ul>

                <hr>

                <ul class="nav flex-column">
                    {% if user.is_authenticated %}
                        {% if user.role == 'admin' or user.is_staff %}
                        <li class="nav-item">
                            <a href="{% url 'users:admin_dashboard' %}" class="nav-link {% if 'admin-dashboard' in request.path %}active{% endif %}">
                                <i class="fas fa-user-shield"></i> Admin Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'core:api_key_dashboard' %}" class="nav-link {% if 'api-key-dashboard' in request.path %}active{% endif %}">
                                <i class="fas fa-key"></i> API Key Management
                            </a>
                        </li>
                        {% endif %}
                        <li class="nav-item">
                            <a href="{% url 'users:logout' %}" class="nav-link">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a href="{% url 'users:login' %}" class="nav-link {% if 'login' in request.path %}active{% endif %}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{% url 'users:register' %}" class="nav-link {% if 'register' in request.path %}active{% endif %}">
                                <i class="fas fa-user-plus"></i> Register
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Main Header -->
            {% block show_default_header %}
            <div class="top-header fade-in">
                <div class="header-info">
                    <h1 class="mb-0 d-flex align-items-center">
                        <i class="fas fa-water me-2 text-primary"></i>
                        <span>{% block page_title %}Shrimp Farm Guardian{% endblock %}</span>
                    </h1>
                    <p class="mb-0">{% block page_description %}Professional shrimp farm management system{% endblock %}</p>
                </div>
                <div class="header-controls d-flex align-items-center gap-3">
                    {% if user.is_authenticated %}
                        <!-- Notification Center -->
                        {% include 'components/notification_center.html' %}
                    {% endif %}
                    <div class="theme-toggle-wrapper">
                        <span class="theme-label" id="lightLabel">☀️</span>
                        <div class="theme-toggle" id="themeToggle">
                            <div class="theme-toggle-slider">
                                <i class="fas fa-moon"></i>
                            </div>
                        </div>
                        <span class="theme-label active" id="darkLabel">🌙</span>
                    </div>
                </div>
            </div>
            {% endblock %}

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}

            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Flatpickr for date picking -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Custom JS -->
    <script src="{% static 'js/dashboard.js' %}"></script>
    
    <!-- Map Styles -->
    <script src="{% url 'core:map_style_js' %}"></script>
    
    <!-- Google Maps Loader - Temporarily disabled to prevent conflicts -->
    <!-- <script src="{% static 'js/google-maps-loader.js' %}"></script> -->

    <!-- Conditional loading of specialized JS files -->
    {% if 'weather/maps' in request.path %}
    <script src="{% static 'js/maps.js' %}"></script>
    <script>
        // Handle Google Maps API loading errors
        function gm_authFailure() {
            console.error("Google Maps authentication failed");
            const mapElements = document.querySelectorAll('.map-container');
            mapElements.forEach(el => {
                el.innerHTML = '<div class="alert alert-danger">Failed to load Google Maps. The API key may be invalid or restricted.</div>';
            });
        }
    </script>
    {% endif %}

    {% if 'medicine/detection' in request.path or 'medicine/prediction' in request.path %}
    <script src="{% static 'js/disease-detection.js' %}"></script>
    {% endif %}

    <!-- Theme Toggle Script -->
    <script>
        // Theme Toggle Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;
            const lightLabel = document.getElementById('lightLabel');
            const darkLabel = document.getElementById('darkLabel');

            if (themeToggle) {
                const slider = themeToggle.querySelector('.theme-toggle-slider');

                // Load saved theme
                const savedTheme = localStorage.getItem('theme') || 'dark';
                if (savedTheme === 'light') {
                    body.classList.add('light-mode');
                    if (slider) {
                        slider.style.transform = 'translateX(35px)';
                        slider.innerHTML = '<i class="fas fa-sun"></i>';
                    }
                    if (lightLabel) lightLabel.classList.add('active');
                    if (darkLabel) darkLabel.classList.remove('active');
                }

                themeToggle.addEventListener('click', () => {
                    body.classList.toggle('light-mode');

                    if (body.classList.contains('light-mode')) {
                        if (slider) {
                            slider.style.transform = 'translateX(35px)';
                            slider.innerHTML = '<i class="fas fa-sun"></i>';
                        }
                        if (lightLabel) lightLabel.classList.add('active');
                        if (darkLabel) darkLabel.classList.remove('active');
                        localStorage.setItem('theme', 'light');
                    } else {
                        if (slider) {
                            slider.style.transform = 'translateX(0)';
                            slider.innerHTML = '<i class="fas fa-moon"></i>';
                        }
                        if (lightLabel) lightLabel.classList.remove('active');
                        if (darkLabel) darkLabel.classList.add('active');
                        localStorage.setItem('theme', 'dark');
                    }
                });
            }
        });
    </script>

    <!-- Mobile & PWA Enhancement Scripts -->
    <script src="{% static 'js/mobile-enhancements.js' %}"></script>
    <!-- PWA installer temporarily disabled to fix service worker scope issues -->
    <!-- <script src="{% static 'js/pwa-installer.js' %}"></script> -->
    
    <!-- Browser Detection Script -->
    <script src="{% static 'js/browser-detect.js' %}"></script>

    <!-- Advanced Analytics Scripts -->
    <script src="{% static 'js/advanced-charts.js' %}"></script>
    <script src="{% static 'js/analytics-dashboard.js' %}"></script>

    <!-- AI/ML Engine Scripts -->
    <script src="{% static 'js/ai-ml-engine.js' %}"></script>
    <script src="{% static 'js/ai-dashboard.js' %}"></script>

    <!-- IoT & Real-time Monitoring Scripts -->
    <script src="{% static 'js/iot-sensor-manager.js' %}"></script>
    <script src="{% static 'js/realtime-dashboard.js' %}"></script>

    <!-- Computer Vision Scripts -->
    <script src="{% static 'js/computer-vision-engine.js' %}"></script>
    <script src="{% static 'js/vision-dashboard.js' %}"></script>

    <!-- Blockchain & Supply Chain Scripts -->
    <script src="{% static 'js/blockchain-engine.js' %}"></script>
    <script src="{% static 'js/supply-chain-dashboard.js' %}"></script>

    <!-- AR/VR & Immersive Technologies Scripts -->
    <script src="{% static 'js/arvr-engine.js' %}"></script>
    <script src="{% static 'js/immersive-dashboard.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <!-- Unified Theme System -->
    <script src="{% static 'js/unified-theme.js' %}"></script>

    <!-- Map Scripts (loaded conditionally) -->
    {% if show_farm_map %}
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script async defer src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&libraries=places&callback=initGoogleMaps&v=weekly" onerror="console.error('Failed to load Google Maps API')"></script>
    <script src="{% static 'js/enhanced-dashboard-map.js' %}"></script>
    <script src="{% static 'js/farm-equipment-map.js' %}"></script>
    {% endif %}
</body>
</html>
