"""
Cache Utilities
Comprehensive cache management and invalidation system
"""

from django.core.cache import cache
from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.conf import settings
import logging
import hashlib
import json
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class CacheManager:
    """
    Centralized cache management system
    """
    
    # Cache key prefixes for different data types
    PREFIXES = {
        'pond': 'pond',
        'water_quality': 'wq',
        'feed': 'feed',
        'medicine': 'med',
        'user': 'user',
        'analytics': 'analytics',
        'dashboard': 'dashboard',
        'api': 'api',
    }
    
    # Cache timeouts (in seconds)
    TIMEOUTS = {
        'short': 300,      # 5 minutes
        'medium': 1800,    # 30 minutes
        'long': 3600,      # 1 hour
        'daily': 86400,    # 24 hours
        'weekly': 604800,  # 7 days
    }
    
    @classmethod
    def generate_key(cls, prefix: str, identifier: str, **kwargs) -> str:
        """
        Generate a standardized cache key
        """
        key_parts = [cls.PREFIXES.get(prefix, prefix), str(identifier)]
        
        # Add additional parameters to key
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            params_str = json.dumps(sorted_kwargs, sort_keys=True)
            params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
            key_parts.append(params_hash)
        
        return ':'.join(key_parts)
    
    @classmethod
    def set(cls, prefix: str, identifier: str, value: Any, timeout: str = 'medium', **kwargs) -> bool:
        """
        Set cache value with standardized key and timeout
        """
        key = cls.generate_key(prefix, identifier, **kwargs)
        timeout_seconds = cls.TIMEOUTS.get(timeout, cls.TIMEOUTS['medium'])
        
        try:
            cache.set(key, value, timeout_seconds)
            logger.debug(f"Cache set: {key} (timeout: {timeout_seconds}s)")
            return True
        except Exception as e:
            logger.error(f"Cache set failed for {key}: {e}")
            return False
    
    @classmethod
    def get(cls, prefix: str, identifier: str, default=None, **kwargs) -> Any:
        """
        Get cache value with standardized key
        """
        key = cls.generate_key(prefix, identifier, **kwargs)
        
        try:
            value = cache.get(key, default)
            if value is not default:
                logger.debug(f"Cache hit: {key}")
            else:
                logger.debug(f"Cache miss: {key}")
            return value
        except Exception as e:
            logger.error(f"Cache get failed for {key}: {e}")
            return default
    
    @classmethod
    def delete(cls, prefix: str, identifier: str, **kwargs) -> bool:
        """
        Delete cache value with standardized key
        """
        key = cls.generate_key(prefix, identifier, **kwargs)
        
        try:
            cache.delete(key)
            logger.debug(f"Cache deleted: {key}")
            return True
        except Exception as e:
            logger.error(f"Cache delete failed for {key}: {e}")
            return False
    
    @classmethod
    def delete_pattern(cls, pattern: str) -> int:
        """
        Delete cache keys matching a pattern
        """
        try:
            if hasattr(cache, 'delete_pattern'):
                # Redis backend supports pattern deletion
                deleted_count = cache.delete_pattern(pattern)
                logger.debug(f"Cache pattern deleted: {pattern} ({deleted_count} keys)")
                return deleted_count
            else:
                # Fallback for other backends
                logger.warning(f"Pattern deletion not supported for cache backend")
                return 0
        except Exception as e:
            logger.error(f"Cache pattern delete failed for {pattern}: {e}")
            return 0
    
    @classmethod
    def invalidate_related(cls, prefix: str, identifier: str) -> None:
        """
        Invalidate all cache entries related to a specific object
        """
        patterns_to_delete = [
            f"{cls.PREFIXES.get(prefix, prefix)}:{identifier}:*",
            f"{cls.PREFIXES.get(prefix, prefix)}:*:{identifier}:*",
            f"analytics:*:{identifier}:*",
            f"dashboard:*:{identifier}:*",
        ]
        
        for pattern in patterns_to_delete:
            cls.delete_pattern(pattern)

# Cache invalidation decorators and signals

def cache_result(prefix: str, timeout: str = 'medium', key_func=None):
    """
    Decorator to cache function results
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # Default key generation
                args_str = '_'.join(str(arg) for arg in args)
                kwargs_str = '_'.join(f"{k}_{v}" for k, v in sorted(kwargs.items()))
                cache_key = f"{func.__name__}_{args_str}_{kwargs_str}"
            
            # Try to get from cache
            result = CacheManager.get(prefix, cache_key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            CacheManager.set(prefix, cache_key, result, timeout)
            return result
        
        return wrapper
    return decorator

# Model-specific cache invalidation

def invalidate_pond_cache(pond_id: int) -> None:
    """Invalidate all cache entries related to a pond"""
    CacheManager.invalidate_related('pond', pond_id)
    
    # Also invalidate dashboard and analytics caches
    CacheManager.delete_pattern(f"dashboard:*")
    CacheManager.delete_pattern(f"analytics:pond:*")

def invalidate_water_quality_cache(pond_id: int) -> None:
    """Invalidate water quality related caches"""
    CacheManager.invalidate_related('water_quality', pond_id)
    invalidate_pond_cache(pond_id)  # Pond data depends on water quality

def invalidate_user_cache(user_id: int) -> None:
    """Invalidate user-specific caches"""
    CacheManager.invalidate_related('user', user_id)
    CacheManager.delete_pattern(f"dashboard:user:{user_id}:*")

# Signal handlers for automatic cache invalidation

@receiver(post_save)
def handle_model_save(sender, instance, created, **kwargs):
    """
    Handle cache invalidation when models are saved
    """
    model_name = sender._meta.model_name
    app_label = sender._meta.app_label
    
    # Map models to cache invalidation functions
    invalidation_map = {
        'pond': lambda: invalidate_pond_cache(instance.id),
        'waterqualityreading': lambda: invalidate_water_quality_cache(instance.pond_id),
        'user': lambda: invalidate_user_cache(instance.id),
    }
    
    invalidation_func = invalidation_map.get(model_name)
    if invalidation_func:
        try:
            invalidation_func()
        except Exception as e:
            logger.error(f"Cache invalidation failed for {model_name}: {e}")

@receiver(post_delete)
def handle_model_delete(sender, instance, **kwargs):
    """
    Handle cache invalidation when models are deleted
    """
    # Use the same logic as save
    handle_model_save(sender, instance, created=False, **kwargs)
