{% extends 'website/base.html' %}

{% block content %}
<!-- Documentation Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Documentation
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Comprehensive guides, API references, and technical documentation
                </p>
                <div data-aos="fade-up" data-aos-delay="200">
                    <a href="#quick-start" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-rocket me-2"></i>Quick Start
                    </a>
                    <a href="#api-reference" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-code me-2"></i>API Reference
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Documentation Navigation -->
<section class="py-4 bg-light border-bottom">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <nav class="nav nav-pills nav-fill">
                    <a class="nav-link active" href="#overview">Overview</a>
                    <a class="nav-link" href="#quick-start">Quick Start</a>
                    <a class="nav-link" href="#api-reference">API Reference</a>
                    <a class="nav-link" href="#integrations">Integrations</a>
                    <a class="nav-link" href="#examples">Examples</a>
                </nav>
            </div>
        </div>
    </div>
</section>

<!-- Overview Section -->
<section id="overview" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-4">Platform Overview</h2>
                    <p class="lead text-muted mb-4">
                        The Shrimp Farm Guardian platform provides comprehensive IoT monitoring, 
                        AI-powered analytics, and automated management tools for modern aquaculture operations.
                    </p>
                    
                    <div class="feature-card mb-4">
                        <h4 class="fw-bold mb-3">Core Components</h4>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="feature-icon me-3" style="background: linear-gradient(135deg, #667eea, #764ba2); width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-satellite-dish"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">IoT Sensors</h6>
                                        <p class="text-muted small mb-0">Real-time monitoring of water quality, weather, and equipment</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="feature-icon me-3" style="background: linear-gradient(135deg, #10b981, #059669); width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">AI Analytics</h6>
                                        <p class="text-muted small mb-0">Predictive insights and automated recommendations</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="feature-icon me-3" style="background: linear-gradient(135deg, #f59e0b, #d97706); width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Mobile App</h6>
                                        <p class="text-muted small mb-0">Remote monitoring and control from anywhere</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="feature-icon me-3" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); width: 40px; height: 40px; font-size: 1rem;">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div>
                                        <h6 class="fw-bold">Dashboard</h6>
                                        <p class="text-muted small mb-0">Comprehensive data visualization and reporting</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Start Section -->
<section id="quick-start" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-4">Quick Start Guide</h2>
                    <p class="lead text-muted mb-5">
                        Get up and running with your aquaculture monitoring system in minutes
                    </p>
                    
                    <div class="row g-4">
                        {% for step in quickstart_steps %}
                        <div class="col-12">
                            <div class="feature-card">
                                <div class="d-flex align-items-start">
                                    <div class="step-number me-3" style="width: 50px; height: 50px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                                        {{ forloop.counter }}
                                    </div>
                                    <div class="flex-grow-1">
                                        <h5 class="fw-bold mb-2">{{ step.title }}</h5>
                                        <p class="text-muted mb-3">{{ step.description }}</p>
                                        {% if step.code %}
                                        <div class="code-block p-3 mb-3" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
                                            <code>{{ step.code }}</code>
                                        </div>
                                        {% endif %}
                                        {% if step.note %}
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>{{ step.note }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- API Reference Section -->
<section id="api-reference" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-4">API Reference</h2>
                    <p class="lead text-muted mb-5">
                        RESTful API for integrating with your existing systems and building custom applications
                    </p>
                    
                    <div class="feature-card mb-4">
                        <h4 class="fw-bold mb-3">Authentication</h4>
                        <p class="text-muted mb-3">All API requests require authentication using API keys.</p>
                        <div class="code-block p-3" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea;">
                            <code>curl -H "Authorization: Bearer YOUR_API_KEY" \<br>
                            &nbsp;&nbsp;&nbsp;&nbsp;https://api.shrimpguardian.com/v1/farms</code>
                        </div>
                    </div>
                    
                    {% for endpoint in api_endpoints %}
                    <div class="feature-card mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <span class="badge bg-primary me-2 px-3 py-2">{{ endpoint.method }}</span>
                            <code class="h6 mb-0">{{ endpoint.path }}</code>
                        </div>
                        <p class="text-muted mb-3">{{ endpoint.description }}</p>
                        
                        {% if endpoint.parameters %}
                        <h6 class="fw-bold mb-2">Parameters</h6>
                        <div class="table-responsive mb-3">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for param in endpoint.parameters %}
                                    <tr>
                                        <td><code>{{ param.name }}</code></td>
                                        <td><span class="badge bg-light text-dark">{{ param.type }}</span></td>
                                        <td>
                                            {% if param.required %}
                                            <span class="badge bg-danger">Required</span>
                                            {% else %}
                                            <span class="badge bg-secondary">Optional</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-muted">{{ param.description }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% endif %}
                        
                        {% if endpoint.example %}
                        <h6 class="fw-bold mb-2">Example Response</h6>
                        <div class="code-block p-3" style="background: #f8f9fa; border-radius: 8px; border-left: 4px solid #10b981;">
                            <pre><code>{{ endpoint.example }}</code></pre>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Integrations Section -->
<section id="integrations" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-4">Integrations</h2>
                    <p class="lead text-muted mb-5">
                        Connect with popular tools and services in the aquaculture industry
                    </p>
                    
                    <div class="row g-4">
                        {% for integration in integrations %}
                        <div class="col-md-6">
                            <div class="feature-card text-center h-100">
                                <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2); width: 60px; height: 60px; font-size: 1.5rem;">
                                    <i class="fas fa-{{ integration.icon }}"></i>
                                </div>
                                <h5 class="fw-bold mb-2">{{ integration.name }}</h5>
                                <p class="text-muted mb-3">{{ integration.description }}</p>
                                <div class="mt-auto">
                                    <a href="#" class="btn btn-outline-gradient">
                                        View Integration <i class="fas fa-arrow-right ms-1"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Examples Section -->
<section id="examples" class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div data-aos="fade-up">
                    <h2 class="display-5 fw-bold mb-4">Code Examples</h2>
                    <p class="lead text-muted mb-5">
                        Sample code and implementation examples for common use cases
                    </p>
                    
                    {% for example in code_examples %}
                    <div class="feature-card mb-4">
                        <h5 class="fw-bold mb-3">{{ example.title }}</h5>
                        <p class="text-muted mb-3">{{ example.description }}</p>
                        
                        <ul class="nav nav-tabs" id="example{{ forloop.counter }}Tabs" role="tablist">
                            {% for lang in example.languages %}
                            <li class="nav-item" role="presentation">
                                <button class="nav-link {% if forloop.first %}active{% endif %}" 
                                        id="example{{ forloop.parentloop.counter }}{{ lang.name }}Tab" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#example{{ forloop.parentloop.counter }}{{ lang.name }}" 
                                        type="button" role="tab">
                                    {{ lang.name }}
                                </button>
                            </li>
                            {% endfor %}
                        </ul>
                        
                        <div class="tab-content">
                            {% for lang in example.languages %}
                            <div class="tab-pane fade {% if forloop.first %}show active{% endif %}" 
                                 id="example{{ forloop.parentloop.counter }}{{ lang.name }}" 
                                 role="tabpanel">
                                <div class="code-block p-3" style="background: #f8f9fa; border-radius: 0 0 8px 8px;">
                                    <pre><code>{{ lang.code }}</code></pre>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Support CTA -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div data-aos="fade-right">
                    <h2 class="display-5 fw-bold mb-3">
                        Need Help?
                    </h2>
                    <p class="lead mb-0">
                        Our technical team is here to help you integrate and customize our platform
                    </p>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div data-aos="fade-left">
                    <a href="{% url 'website:support' %}" class="btn btn-light text-primary btn-lg me-3">
                        <i class="fas fa-headset me-2"></i>Get Support
                    </a>
                    <a href="{% url 'website:contact' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-envelope me-2"></i>Contact Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
