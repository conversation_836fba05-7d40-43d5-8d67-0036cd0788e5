{% extends 'base.html' %}
{% load static %}

{% block title %}{% block form_title %}Form{% endblock %} - Shrimp Farm Guardian{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Form Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            {% block form_header_icon %}<i class="fas fa-edit text-primary me-2"></i>{% endblock %}
            <h1 class="h3 mb-0">{% block form_header %}Form{% endblock %}</h1>
        </div>
        <div class="d-flex align-items-center">
            {% include 'components/header_controls.html' %}
            {% include 'components/theme_toggle.html' %}
            <div class="ms-3">
                {% block form_header_actions %}
                <!-- Form header actions will go here -->
                {% endblock %}
            </div>
        </div>
    </div>
    
    <!-- Form Content -->
    <div class="row">
        <div class="col-12">
            <div class="card form-card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">{% block form_card_title %}Enter Information{% endblock %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="{% block form_id %}main-form{% endblock %}">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        {% block form_fields %}
                        <!-- Form fields will go here -->
                        {% endblock %}
                        
                        <div class="mt-4">
                            {% block form_buttons %}
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save
                            </button>
                            <a href="{% block form_cancel_url %}#{% endblock %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i> Cancel
                            </a>
                            {% endblock %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block form_extra_js %}
<!-- Form-specific JavaScript will go here -->
{% endblock %}
{% endblock %}