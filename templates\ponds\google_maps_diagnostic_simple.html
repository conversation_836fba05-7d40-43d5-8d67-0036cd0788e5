<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Diagnostic - Simple Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #map {
            width: 100%;
            height: 500px;
            background-color: #e5e5e5;
            border: 2px solid #ccc;
            margin: 20px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }
        .debug-log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps Diagnostic Tool</h1>
        <p>This page will help diagnose why Google Maps is not loading in the cumulative dashboard.</p>
        
        <div class="step">
            <h3>Step 1: API Key Check</h3>
            <div id="api-key-status" class="status info">
                API Key: {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}
            </div>
        </div>

        <div class="step">
            <h3>Step 2: Data Check</h3>
            <div id="data-status" class="status info">
                Farms: {{ farms_count }}, Ponds: {{ ponds_count }}, Workers: {{ workers_count }}, Geofences: {{ geofences_count }}
            </div>
        </div>

        <div class="step">
            <h3>Step 3: Map Container</h3>
            <div id="map">Waiting for Google Maps to load...</div>
        </div>

        <div class="step">
            <h3>Step 4: Debug Log</h3>
            <div id="debug-log" class="debug-log">
                <div id="log-content">Initializing diagnostic...</div>
            </div>
        </div>
    </div>

    <script>
        // Debug logging function
        function log(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        // Check if Google Maps API is accessible
        function checkGoogleMapsAPI() {
            log('Checking if Google Maps API is accessible...');
            
            // Test 1: Check if google object exists
            if (typeof google !== 'undefined') {
                log('✅ Google object is defined', 'success');
                
                if (google.maps) {
                    log('✅ Google Maps API is loaded', 'success');
                    log(`📊 Google Maps version: ${google.maps.version}`, 'info');
                    initializeMap();
                } else {
                    log('❌ Google Maps API not loaded - google.maps is undefined', 'error');
                }
            } else {
                log('❌ Google object not defined - API not loaded', 'error');
            }
        }

        // Initialize the map
        function initializeMap() {
            log('Attempting to initialize map...');
            
            try {
                const mapContainer = document.getElementById('map');
                
                const map = new google.maps.Map(mapContainer, {
                    zoom: 10,
                    center: { lat: {{ center_lat }}, lng: {{ center_lng }} },
                    mapTypeId: 'roadmap'
                });
                
                log('✅ Map initialized successfully!', 'success');
                
                // Add a simple marker
                const marker = new google.maps.Marker({
                    position: { lat: {{ center_lat }}, lng: {{ center_lng }} },
                    map: map,
                    title: 'Test Location'
                });
                
                log('✅ Test marker added', 'success');
                
                // Update status
                document.getElementById('api-key-status').className = 'status success';
                document.getElementById('api-key-status').innerHTML = '✅ API Key is working correctly';
                
            } catch (error) {
                log(`❌ Error initializing map: ${error.message}`, 'error');
                document.getElementById('map').innerHTML = `
                    <div style="color: red; text-align: center;">
                        <h3>❌ Map Initialization Failed</h3>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Global callback function for Google Maps API
        function initMap() {
            log('🗺️ initMap() callback triggered by Google Maps API', 'success');
            checkGoogleMapsAPI();
        }

        // Check browser capabilities
        function checkBrowserCapabilities() {
            log('Checking browser capabilities...');
            
            // Check if fetch API is available
            if (typeof fetch !== 'undefined') {
                log('✅ Fetch API available', 'success');
            } else {
                log('❌ Fetch API not available', 'error');
            }
            
            // Check if Promise is available
            if (typeof Promise !== 'undefined') {
                log('✅ Promise support available', 'success');
            } else {
                log('❌ Promise support not available', 'error');
            }
            
            // Check console
            if (typeof console !== 'undefined') {
                log('✅ Console available', 'success');
            } else {
                log('❌ Console not available', 'error');
            }
        }

        // Initialize diagnostics when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page loaded, starting diagnostics...', 'info');
            checkBrowserCapabilities();
            
            // Set a timeout to check if Google Maps API loads
            setTimeout(function() {
                if (typeof google === 'undefined') {
                    log('⏰ Timeout: Google Maps API did not load after 5 seconds', 'error');
                    document.getElementById('map').innerHTML = `
                        <div style="color: red; text-align: center;">
                            <h3>❌ Google Maps API Loading Timeout</h3>
                            <p>The API did not load within 5 seconds</p>
                            <p>Check your internet connection and API key</p>
                        </div>
                    `;
                }
            }, 5000);
        });

        // Error handler for script loading
        window.addEventListener('error', function(e) {
            log(`❌ Script Error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
        });
    </script>

    <!-- Load Google Maps API -->
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onerror="log('❌ Failed to load Google Maps API script', 'error')">
    </script>
</body>
</html>
