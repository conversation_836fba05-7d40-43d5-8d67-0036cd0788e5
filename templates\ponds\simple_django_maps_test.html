<!DOCTYPE html>
<html>
<head>
    <title>Django Google Maps Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #map { height: 400px; width: 100%; border: 2px solid #ddd; margin: 20px 0; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🗺️ Django Google Maps Test</h1>
    
    <div id="status" class="status info">⏳ Initializing...</div>
    
    <div class="debug">
        <strong>Configuration:</strong><br>
        API Key: {{ google_maps_api_key|default:"NOT_SET" }}<br>
        Center: {{ center_lat }}, {{ center_lng }}<br>
        Farms: {{ farms_data|length }} farms<br>
        Ponds: {{ ponds_data|length }} ponds
    </div>
    
    <div class="debug" id="debug-log">
        <strong>Debug Log:</strong><br>
        <div id="debug-content">Starting test...<br></div>
    </div>
    
    <div id="map"></div>

    <script>
        // Debug logging
        function log(message, type = 'info') {
            console.log(message);
            const debugContent = document.getElementById('debug-content');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            debugContent.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            debugContent.scrollTop = debugContent.scrollHeight;
        }
        
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Data from Django
        const apiKey = '{{ google_maps_api_key|escapejs }}';
        const centerLat = {{ center_lat|default:13.0827 }};
        const centerLng = {{ center_lng|default:80.2707 }};
        
        log(`🔑 API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length-5)}`);
        log(`📍 Center: ${centerLat}, ${centerLng}`);

        // Global error handler
        window.gm_authFailure = function() {
            log('❌ Google Maps authentication failed!', 'error');
            updateStatus('❌ Authentication Failed', 'error');
        };

        function initMap() {
            log('🗺️ initMap() called by Google Maps API', 'success');
            updateStatus('🗺️ Google Maps API loaded', 'success');
            
            if (typeof google === 'undefined' || !google.maps) {
                log('❌ Google Maps API not available', 'error');
                updateStatus('❌ Google Maps API not available', 'error');
                return;
            }
            
            log('✅ Google Maps API available', 'success');
            
            try {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: centerLat, lng: centerLng },
                    mapTypeId: 'roadmap'
                });
                
                log('✅ Map created successfully', 'success');
                updateStatus('✅ Map working perfectly!', 'success');
                
                // Add test marker
                const marker = new google.maps.Marker({
                    position: { lat: centerLat, lng: centerLng },
                    map: map,
                    title: 'Test Location'
                });
                
                log('✅ Test marker added', 'success');
                
            } catch (error) {
                log(`❌ Error creating map: ${error.message}`, 'error');
                updateStatus('❌ Map creation failed', 'error');
            }
        }

        log('📡 Loading Google Maps API...');
    </script>

    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&libraries=geometry"
        onerror="log('❌ Script loading failed', 'error'); updateStatus('❌ Script loading failed', 'error');">
    </script>
</body>
</html>
