"""
Load Testing Framework
Comprehensive load testing for the Shrimp Farm Guardian application
"""

import asyncio
import aiohttp
import time
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass, asdict
from concurrent.futures import ThreadPoolExecutor
import argparse

logger = logging.getLogger(__name__)

@dataclass
class LoadTestConfig:
    """Configuration for load testing"""
    base_url: str = "http://localhost:8000"
    concurrent_users: int = 10
    test_duration: int = 60  # seconds
    ramp_up_time: int = 10   # seconds
    endpoints: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.endpoints is None:
            self.endpoints = [
                {"path": "/", "method": "GET", "weight": 30},
                {"path": "/api/v1/ponds/", "method": "GET", "weight": 25},
                {"path": "/api/v1/water-quality/", "method": "GET", "weight": 20},
                {"path": "/health/", "method": "GET", "weight": 15},
                {"path": "/app/dashboard/", "method": "GET", "weight": 10}
            ]

@dataclass
class RequestResult:
    """Result of a single request"""
    url: str
    method: str
    status_code: int
    response_time: float
    timestamp: datetime
    success: bool
    error: Optional[str] = None
    response_size: int = 0

class LoadTestRunner:
    """
    Main load testing runner with comprehensive metrics collection
    """
    
    def __init__(self, config: LoadTestConfig):
        self.config = config
        self.results: List[RequestResult] = []
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def run_load_test(self) -> Dict[str, Any]:
        """Run the complete load test"""
        logger.info(f"Starting load test with {self.config.concurrent_users} users for {self.config.test_duration}s")
        
        self.start_time = datetime.now()
        
        # Create HTTP session with connection pooling
        connector = aiohttp.TCPConnector(
            limit=self.config.concurrent_users * 2,
            limit_per_host=self.config.concurrent_users
        )
        
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
        
        try:
            # Run load test with gradual ramp-up
            await self._run_with_ramp_up()
            
        finally:
            await self.session.close()
            self.end_time = datetime.now()
        
        # Generate and return results
        return self._generate_report()
    
    async def _run_with_ramp_up(self):
        """Run load test with gradual user ramp-up"""
        tasks = []
        users_per_second = self.config.concurrent_users / self.config.ramp_up_time
        
        for i in range(self.config.concurrent_users):
            # Calculate when to start this user
            start_delay = i / users_per_second
            
            # Create user task
            task = asyncio.create_task(
                self._simulate_user(user_id=i, start_delay=start_delay)
            )
            tasks.append(task)
        
        # Wait for all users to complete
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _simulate_user(self, user_id: int, start_delay: float):
        """Simulate a single user's behavior"""
        # Wait for ramp-up delay
        await asyncio.sleep(start_delay)
        
        user_start_time = time.time()
        request_count = 0
        
        while (time.time() - user_start_time) < self.config.test_duration:
            try:
                # Select endpoint based on weight
                endpoint = self._select_weighted_endpoint()
                
                # Make request
                result = await self._make_request(
                    endpoint["path"],
                    endpoint["method"],
                    user_id
                )
                
                self.results.append(result)
                request_count += 1
                
                # Small delay between requests (simulate user think time)
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"User {user_id} error: {e}")
                break
        
        logger.debug(f"User {user_id} completed {request_count} requests")
    
    def _select_weighted_endpoint(self) -> Dict[str, Any]:
        """Select endpoint based on weight distribution"""
        import random
        
        total_weight = sum(ep["weight"] for ep in self.config.endpoints)
        random_weight = random.randint(1, total_weight)
        
        current_weight = 0
        for endpoint in self.config.endpoints:
            current_weight += endpoint["weight"]
            if random_weight <= current_weight:
                return endpoint
        
        return self.config.endpoints[0]  # Fallback
    
    async def _make_request(self, path: str, method: str, user_id: int) -> RequestResult:
        """Make a single HTTP request and record metrics"""
        url = f"{self.config.base_url}{path}"
        start_time = time.time()
        timestamp = datetime.now()
        
        try:
            headers = {
                "User-Agent": f"LoadTest-User-{user_id}",
                "Accept": "application/json,text/html,*/*"
            }
            
            async with self.session.request(method, url, headers=headers) as response:
                # Read response body to get size
                body = await response.read()
                response_time = time.time() - start_time
                
                return RequestResult(
                    url=url,
                    method=method,
                    status_code=response.status,
                    response_time=response_time,
                    timestamp=timestamp,
                    success=200 <= response.status < 400,
                    response_size=len(body)
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return RequestResult(
                url=url,
                method=method,
                status_code=0,
                response_time=response_time,
                timestamp=timestamp,
                success=False,
                error=str(e)
            )
    
    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive load test report"""
        if not self.results:
            return {"error": "No results to analyze"}
        
        # Basic metrics
        total_requests = len(self.results)
        successful_requests = sum(1 for r in self.results if r.success)
        failed_requests = total_requests - successful_requests
        success_rate = (successful_requests / total_requests) * 100
        
        # Response time metrics
        response_times = [r.response_time for r in self.results if r.success]
        if response_times:
            avg_response_time = statistics.mean(response_times)
            median_response_time = statistics.median(response_times)
            p95_response_time = self._percentile(response_times, 95)
            p99_response_time = self._percentile(response_times, 99)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
        else:
            avg_response_time = median_response_time = p95_response_time = p99_response_time = 0
            min_response_time = max_response_time = 0
        
        # Throughput metrics
        test_duration = (self.end_time - self.start_time).total_seconds()
        requests_per_second = total_requests / test_duration if test_duration > 0 else 0
        
        # Error analysis
        error_types = {}
        status_codes = {}
        
        for result in self.results:
            # Count status codes
            status_codes[result.status_code] = status_codes.get(result.status_code, 0) + 1
            
            # Count error types
            if not result.success and result.error:
                error_types[result.error] = error_types.get(result.error, 0) + 1
        
        # Endpoint performance
        endpoint_stats = {}
        for result in self.results:
            endpoint = result.url.replace(self.config.base_url, "")
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    "requests": 0,
                    "successes": 0,
                    "response_times": []
                }
            
            endpoint_stats[endpoint]["requests"] += 1
            if result.success:
                endpoint_stats[endpoint]["successes"] += 1
                endpoint_stats[endpoint]["response_times"].append(result.response_time)
        
        # Calculate endpoint averages
        for endpoint, stats in endpoint_stats.items():
            if stats["response_times"]:
                stats["avg_response_time"] = statistics.mean(stats["response_times"])
                stats["success_rate"] = (stats["successes"] / stats["requests"]) * 100
            else:
                stats["avg_response_time"] = 0
                stats["success_rate"] = 0
            del stats["response_times"]  # Remove raw data from report
        
        return {
            "test_config": asdict(self.config),
            "test_duration": test_duration,
            "summary": {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate": round(success_rate, 2),
                "requests_per_second": round(requests_per_second, 2)
            },
            "response_times": {
                "average": round(avg_response_time * 1000, 2),  # Convert to ms
                "median": round(median_response_time * 1000, 2),
                "p95": round(p95_response_time * 1000, 2),
                "p99": round(p99_response_time * 1000, 2),
                "min": round(min_response_time * 1000, 2),
                "max": round(max_response_time * 1000, 2)
            },
            "status_codes": status_codes,
            "error_types": error_types,
            "endpoint_performance": endpoint_stats,
            "timestamps": {
                "start": self.start_time.isoformat(),
                "end": self.end_time.isoformat()
            }
        }
    
    def _percentile(self, data: List[float], percentile: int) -> float:
        """Calculate percentile of a dataset"""
        if not data:
            return 0
        
        sorted_data = sorted(data)
        index = (percentile / 100) * (len(sorted_data) - 1)
        
        if index.is_integer():
            return sorted_data[int(index)]
        else:
            lower = sorted_data[int(index)]
            upper = sorted_data[int(index) + 1]
            return lower + (upper - lower) * (index - int(index))

async def run_load_test_suite():
    """Run a comprehensive load test suite"""
    test_scenarios = [
        LoadTestConfig(concurrent_users=5, test_duration=30, ramp_up_time=5),
        LoadTestConfig(concurrent_users=10, test_duration=60, ramp_up_time=10),
        LoadTestConfig(concurrent_users=25, test_duration=120, ramp_up_time=15),
        LoadTestConfig(concurrent_users=50, test_duration=180, ramp_up_time=20),
    ]
    
    results = {}
    
    for i, config in enumerate(test_scenarios, 1):
        print(f"\n🚀 Running Load Test Scenario {i}/{len(test_scenarios)}")
        print(f"   Users: {config.concurrent_users}, Duration: {config.test_duration}s")
        
        runner = LoadTestRunner(config)
        result = await runner.run_load_test()
        results[f"scenario_{i}"] = result
        
        # Brief pause between scenarios
        await asyncio.sleep(5)
    
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Load Testing Framework")
    parser.add_argument("--users", type=int, default=10, help="Number of concurrent users")
    parser.add_argument("--duration", type=int, default=60, help="Test duration in seconds")
    parser.add_argument("--url", default="http://localhost:8000", help="Base URL to test")
    parser.add_argument("--suite", action="store_true", help="Run full test suite")
    
    args = parser.parse_args()
    
    async def main():
        if args.suite:
            results = await run_load_test_suite()
        else:
            config = LoadTestConfig(
                base_url=args.url,
                concurrent_users=args.users,
                test_duration=args.duration
            )
            runner = LoadTestRunner(config)
            results = await runner.run_load_test()
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"load_test_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n📊 Load test completed! Results saved to: {filename}")
        
        # Print summary
        if "summary" in results:
            summary = results["summary"]
            print(f"\n📈 Summary:")
            print(f"   Total Requests: {summary['total_requests']}")
            print(f"   Success Rate: {summary['success_rate']}%")
            print(f"   Requests/sec: {summary['requests_per_second']}")
            print(f"   Avg Response Time: {results['response_times']['average']}ms")
    
    asyncio.run(main())
