{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
{% load map_tags %}
<style>
    #pond-map {
        width: 100%;
        height: 550px;
        border-radius: 0.5rem;
        margin-bottom: 0;
    }

    .map-controls {
        position: absolute;
        top: 70px;
        right: 10px;
        z-index: 999;
        background-color: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        width: 160px;
    }

    .map-info-panel {
        position: absolute;
        top: 70px;
        left: 10px;
        z-index: 999;
        width: 200px;
    }

    .map-info-panel .card {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .map-legend {
        position: absolute;
        bottom: 30px;
        left: 10px;
        z-index: 999;
        background-color: white;
        border-radius: 0.5rem;
        padding: 0.5rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    /* Tooltip styling */
    .tooltip {
        position: absolute;
        z-index: 1070;
        display: block;
        margin: 0;
        font-family: var(--bs-font-sans-serif);
        font-style: normal;
        font-weight: 400;
        line-height: 1.5;
        text-align: left;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        letter-spacing: normal;
        word-break: normal;
        word-spacing: normal;
        white-space: normal;
        line-break: auto;
        font-size: 0.875rem;
        word-wrap: break-word;
        opacity: 0;
    }

    .map-legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .map-legend-color {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }

    .pond-marker {
        background-color: #1976D2;
    }

    .pond-boundary {
        background-color: rgba(33, 150, 243, 0.3);
        border: 2px solid #2196F3;
    }

    .drawing-instructions {
        margin-top: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        border-left: 4px solid #1976D2;
    }

    .drawing-instructions h6 {
        margin-bottom: 0.5rem;
    }

    .drawing-instructions ol {
        margin-bottom: 0;
        padding-left: 1.5rem;
    }

    /* Google Maps custom styles */
    .custom-map-control-button {
        background-color: #fff;
        border: 0;
        border-radius: 2px;
        box-shadow: 0 1px 4px -1px rgba(0, 0, 0, 0.3);
        margin: 10px;
        padding: 0 0.5em;
        font: 400 18px Roboto, Arial, sans-serif;
        overflow: hidden;
        height: 40px;
        cursor: pointer;
    }

    .custom-map-control-button:hover {
        background: #ebebeb;
    }
</style>
{% map_provider_css %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-water text-primary me-2" style="font-size: 1.5rem;"></i>
            <h1 class="mb-0">{{ title }}</h1>
        </div>
        <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-1"></i> Back to Ponds
        </a>
    </div>

    <form method="post" id="pond-form">
        {% csrf_token %}
        {% if form.non_field_errors %}
        <div class="alert alert-danger">
            {% for error in form.non_field_errors %}
            {{ error }}
            {% endfor %}
        </div>
        {% endif %}

        <!-- Pond Location Map at the top -->
        <div class="card mb-4">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h5 class="mb-0">Pond Location</h5>
                    <div class="d-flex align-items-center">
                        <div class="btn-group btn-group-sm" role="group" aria-label="Map Type">
                            <button type="button" class="btn btn-outline-primary active" id="satellite-view-btn">Satellite</button>
                            <button type="button" class="btn btn-outline-primary" id="map-view-btn">Map</button>
                        </div>
                    </div>
                </div>
                <div class="row g-2">
                    <div class="col-md-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text bg-primary text-white">Latitude</span>
                            <input type="number" step="0.000001" name="{{ form.latitude.name }}" id="{{ form.latitude.id_for_label }}" class="form-control {% if form.latitude.errors %}is-invalid{% endif %}" value="{{ form.latitude.value|default:'' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text bg-primary text-white">Longitude</span>
                            <input type="number" step="0.000001" name="{{ form.longitude.name }}" id="{{ form.longitude.id_for_label }}" class="form-control {% if form.longitude.errors %}is-invalid{% endif %}" value="{{ form.longitude.value|default:'' }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text bg-primary text-white">Size</span>
                            <input type="number" name="{{ form.size.name }}" id="{{ form.size.id_for_label }}" class="form-control {% if form.size.errors %}is-invalid{% endif %}" value="{{ form.size.value|default:'' }}">
                            <span class="input-group-text">m²</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text bg-primary text-white"><i class="fas fa-search"></i></span>
                            <input type="text" id="location-search" class="form-control" placeholder="Search location...">
                            <button class="btn btn-primary" type="button" id="search-btn">Find</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div id="pond-map"></div>

                <div class="map-controls">
                    <button type="button" class="btn btn-sm btn-primary d-block w-100 mb-2" id="locate-me-btn" data-bs-toggle="tooltip" title="Find your current location">
                        <i class="fas fa-location-arrow me-1"></i> My Location
                    </button>
                    <button type="button" class="btn btn-sm btn-success d-block w-100 mb-2" id="draw-boundary-btn" data-bs-toggle="tooltip" title="Start drawing the pond boundary">
                        <i class="fas fa-draw-polygon me-1"></i> Draw Boundary
                    </button>
                    <button type="button" class="btn btn-sm btn-info d-block w-100 mb-2" id="edit-boundary-btn" data-bs-toggle="tooltip" title="Edit existing boundary">
                        <i class="fas fa-edit me-1"></i> Edit Boundary
                    </button>
                    <button type="button" class="btn btn-sm btn-danger d-block w-100" id="clear-boundary-btn" data-bs-toggle="tooltip" title="Remove the current boundary">
                        <i class="fas fa-trash-alt me-1"></i> Clear Boundary
                    </button>
                </div>

                <div class="map-info-panel">
                    <div class="card">
                        <div class="card-body p-2">
                            <h6 class="mb-2"><i class="fas fa-info-circle me-1"></i> Pond Details</h6>
                            <div class="d-flex justify-content-between mb-1">
                                <span>Area:</span>
                                <strong id="pond-area-display">0 m²</strong>
                            </div>
                            <div class="d-flex justify-content-between mb-1">
                                <span>Perimeter:</span>
                                <strong id="pond-perimeter-display">0 m</strong>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Points:</span>
                                <strong id="pond-points-display">0</strong>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="map-legend">
                    <div class="map-legend-item">
                        <div class="map-legend-color pond-marker"></div>
                        <span>Pond Location</span>
                    </div>
                    <div class="map-legend-item">
                        <div class="map-legend-color pond-boundary"></div>
                        <span>Pond Boundary</span>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="drawing-instructions">
                    <h6><i class="fas fa-info-circle me-1"></i> How to Mark Pond Boundaries</h6>
                    <ol>
                        <li>Click the "Draw Pond Boundary" button</li>
                        <li>Click on the map to place points around your pond</li>
                        <li>Complete the polygon by clicking on the first point</li>
                        <li>To edit, drag any point to adjust the boundary</li>
                        <li>Click "Clear Boundary" to start over</li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- Pond Information Form -->
        <div class="row">
            <div class="col-12">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Pond Information</h5>
                    </div>
                    <div class="card-body">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.name.id_for_label }}" class="form-label">Pond Name</label>
                                {{ form.name.errors }}
                                <input type="text" name="{{ form.name.name }}" id="{{ form.name.id_for_label }}" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
                                {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status.errors }}
                                <select name="{{ form.status.name }}" id="{{ form.status.id_for_label }}" class="form-select {% if form.status.errors %}is-invalid{% endif %}">
                                    {% for value, text in form.status.field.choices %}
                                    <option value="{{ value }}" {% if form.status.value == value %}selected{% endif %}>{{ text }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.status.help_text %}
                                <div class="form-text">{{ form.status.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.water_quality.id_for_label }}" class="form-label">Water Quality</label>
                                {{ form.water_quality.errors }}
                                <select name="{{ form.water_quality.name }}" id="{{ form.water_quality.id_for_label }}" class="form-select {% if form.water_quality.errors %}is-invalid{% endif %}">
                                    {% for value, text in form.water_quality.field.choices %}
                                    <option value="{{ value }}" {% if form.water_quality.value == value %}selected{% endif %}>{{ text }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.water_quality.help_text %}
                                <div class="form-text">{{ form.water_quality.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6">
                                <label for="{{ form.feeding_status.id_for_label }}" class="form-label">Feeding Status</label>
                                {{ form.feeding_status.errors }}
                                <select name="{{ form.feeding_status.name }}" id="{{ form.feeding_status.id_for_label }}" class="form-select {% if form.feeding_status.errors %}is-invalid{% endif %}">
                                    {% for value, text in form.feeding_status.field.choices %}
                                    <option value="{{ value }}" {% if form.feeding_status.value == value %}selected{% endif %}>{{ text }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.feeding_status.help_text %}
                                <div class="form-text">{{ form.feeding_status.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.occupancy.id_for_label }}" class="form-label">Occupancy (%)</label>
                                {{ form.occupancy.errors }}
                                <input type="number" name="{{ form.occupancy.name }}" id="{{ form.occupancy.id_for_label }}" class="form-control {% if form.occupancy.errors %}is-invalid{% endif %}" value="{{ form.occupancy.value|default:'' }}" min="0" max="100">
                                {% if form.occupancy.help_text %}
                                <div class="form-text">{{ form.occupancy.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6">
                                <label for="{{ form.species.id_for_label }}" class="form-label">Species</label>
                                {{ form.species.errors }}
                                <input type="text" name="{{ form.species.name }}" id="{{ form.species.id_for_label }}" class="form-control {% if form.species.errors %}is-invalid{% endif %}" value="{{ form.species.value|default:'' }}">
                                {% if form.species.help_text %}
                                <div class="form-text">{{ form.species.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.team.id_for_label }}" class="form-label">Assigned Labor Team</label>
                                {{ form.team.errors }}
                                <select name="{{ form.team.name }}" id="{{ form.team.id_for_label }}" class="form-select {% if form.team.errors %}is-invalid{% endif %}">
                                    <option value="">---------</option>
                                    {% for team in form.team.field.queryset %}
                                    <option value="{{ team.id }}" {% if form.team.value|stringformat:"i" == team.id|stringformat:"i" %}selected{% endif %}>
                                        {{ team.name }} ({{ team.member_count }} members)
                                    </option>
                                    {% endfor %}
                                </select>
                                {% if form.team.help_text %}
                                <div class="form-text">{{ form.team.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="{{ form.stocked_date.id_for_label }}" class="form-label">Stocked Date</label>
                                {{ form.stocked_date.errors }}
                                <input type="date" name="{{ form.stocked_date.name }}" id="{{ form.stocked_date.id_for_label }}" class="form-control {% if form.stocked_date.errors %}is-invalid{% endif %}" value="{{ form.stocked_date.value|date:'Y-m-d'|default:'' }}">
                                {% if form.stocked_date.help_text %}
                                <div class="form-text">{{ form.stocked_date.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="col-md-6">
                                <label for="{{ form.harvest_date.id_for_label }}" class="form-label">Harvest Date</label>
                                {{ form.harvest_date.errors }}
                                <input type="date" name="{{ form.harvest_date.name }}" id="{{ form.harvest_date.id_for_label }}" class="form-control {% if form.harvest_date.errors %}is-invalid{% endif %}" value="{{ form.harvest_date.value|date:'Y-m-d'|default:'' }}">
                                {% if form.harvest_date.help_text %}
                                <div class="form-text">{{ form.harvest_date.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.density.id_for_label }}" class="form-label">Density</label>
                            {{ form.density.errors }}
                            <input type="text" name="{{ form.density.name }}" id="{{ form.density.id_for_label }}" class="form-control {% if form.density.errors %}is-invalid{% endif %}" value="{{ form.density.value|default:'' }}" placeholder="e.g., 120 PL/m²">
                            {% if form.density.help_text %}
                            <div class="form-text">{{ form.density.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- Hidden field for pond boundary coordinates -->
                        <input type="hidden" name="{{ form.boundary.name }}" id="pond-boundary-input" value="{{ form.boundary.value|default:'' }}">

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'ponds:pond_list' %}" class="btn btn-outline-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">Save Pond</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
{% map_provider_js %}
<script>
    // Listen for the Google Maps loaded event
    document.addEventListener('google-maps-loaded', function() {
        console.log('Google Maps loaded event received in pond form');
        initializePondMap();
    });
    
    function initializePondMap() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize form fields
        const latitudeField = document.getElementById('id_latitude');
        const longitudeField = document.getElementById('id_longitude');
        const boundaryInput = document.getElementById('pond-boundary-input');
        const sizeField = document.getElementById('id_size');
        const locationSearch = document.getElementById('location-search');
        const searchBtn = document.getElementById('search-btn');
        const pondAreaDisplay = document.getElementById('pond-area-display');
        const pondPerimeterDisplay = document.getElementById('pond-perimeter-display');
        const pondPointsDisplay = document.getElementById('pond-points-display');

        // Default coordinates (Vietnam)
        let initialLat = 10.125;
        let initialLng = 106.458;
        let initialZoom = 15;

        // Set initial coordinates if available
        if (latitudeField.value && longitudeField.value) {
            initialLat = parseFloat(latitudeField.value);
            initialLng = parseFloat(longitudeField.value);
        }

        // Initialize map
        const mapOptions = {
            center: { lat: initialLat, lng: initialLng },
            zoom: initialZoom,
            mapTypeId: google.maps.MapTypeId.HYBRID,  // Satellite with labels
            mapTypeControl: false, // We'll use our own controls
            zoomControl: true,
            zoomControlOptions: {
                position: google.maps.ControlPosition.RIGHT_CENTER
            },
            scaleControl: true,
            streetViewControl: false,
            rotateControl: true,
            fullscreenControl: true,
            fullscreenControlOptions: {
                position: google.maps.ControlPosition.TOP_LEFT
            },
            // Custom styling for water features
            styles: [
                {
                    featureType: 'water',
                    elementType: 'geometry',
                    stylers: [{ color: '#b3d1ff' }]
                },
                {
                    featureType: 'landscape',
                    elementType: 'geometry',
                    stylers: [{ color: '#e6f2e6' }]
                }
            ]
        };

        const map = new google.maps.Map(document.getElementById('pond-map'), mapOptions);

        // Initialize geocoder for location search
        const geocoder = new google.maps.Geocoder();

        // Create drawing manager
        const drawingManager = new google.maps.drawing.DrawingManager({
            drawingMode: null,
            drawingControl: false, // We'll use our own controls

            markerOptions: {
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: '#1976D2',
                    fillOpacity: 1,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 2,
                    scale: 8
                },
                draggable: true
            },
            polygonOptions: {
                fillColor: '#2196F3',
                fillOpacity: 0.3,
                strokeWeight: 2,
                strokeColor: '#2196F3',
                clickable: true,
                editable: true,
                draggable: true,
                zIndex: 1
            }
        });

        drawingManager.setMap(map);

        // Add marker
        let marker = null;

        // Set initial marker if coordinates are available
        if (latitudeField.value && longitudeField.value) {
            marker = new google.maps.Marker({
                position: { lat: initialLat, lng: initialLng },
                map: map,
                draggable: true,
                icon: {
                    path: google.maps.SymbolPath.CIRCLE,
                    fillColor: '#1976D2',
                    fillOpacity: 1,
                    strokeColor: '#FFFFFF',
                    strokeWeight: 2,
                    scale: 8
                }
            });

            // Update form fields when marker is dragged
            marker.addListener('dragend', function() {
                const position = marker.getPosition();
                latitudeField.value = position.lat().toFixed(6);
                longitudeField.value = position.lng().toFixed(6);
            });
        }

        // Current polygon
        let currentPolygon = null;

        // Set initial boundary if available
        {% if pond.boundary %}
        try {
            const boundaryCoordinates = JSON.parse('{{ pond.boundary|escapejs }}');

            if (boundaryCoordinates && boundaryCoordinates.length > 0) {
                // Convert coordinates to Google Maps format
                const polygonCoords = boundaryCoordinates.map(coord => {
                    return { lat: coord[0], lng: coord[1] };
                });

                // Create polygon
                currentPolygon = new google.maps.Polygon({
                    paths: polygonCoords,
                    strokeColor: '#2196F3',
                    strokeOpacity: 0.8,
                    strokeWeight: 2,
                    fillColor: '#2196F3',
                    fillOpacity: 0.3,
                    map: map,
                    editable: true,
                    draggable: true
                });

                // Add listeners for polygon changes
                addPolygonListeners(currentPolygon);

                // Fit map to boundary
                const bounds = new google.maps.LatLngBounds();
                polygonCoords.forEach(coord => bounds.extend(coord));
                map.fitBounds(bounds);

                // Set boundary input value
                boundaryInput.value = JSON.stringify(boundaryCoordinates);
            }
        } catch (error) {
            console.error("Error parsing pond boundary:", error);
        }
        {% endif %}

        // Handle map click to set pond location
        map.addListener('click', function(e) {
            // Only set marker if not in drawing mode
            if (drawingManager.getDrawingMode() === null) {
                if (marker) {
                    marker.setMap(null);
                }

                marker = new google.maps.Marker({
                    position: e.latLng,
                    map: map,
                    draggable: true,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        fillColor: '#1976D2',
                        fillOpacity: 1,
                        strokeColor: '#FFFFFF',
                        strokeWeight: 2,
                        scale: 8
                    }
                });

                // Update form fields
                latitudeField.value = e.latLng.lat().toFixed(6);
                longitudeField.value = e.latLng.lng().toFixed(6);

                // Update form fields when marker is dragged
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    latitudeField.value = position.lat().toFixed(6);
                    longitudeField.value = position.lng().toFixed(6);
                });
            }
        });

        // Handle polygon complete event
        google.maps.event.addListener(drawingManager, 'polygoncomplete', function(polygon) {
            // Remove previous polygon if exists
            if (currentPolygon) {
                currentPolygon.setMap(null);
            }

            currentPolygon = polygon;

            // Add listeners for polygon changes
            addPolygonListeners(polygon);

            // Switch back to non-drawing mode
            drawingManager.setDrawingMode(null);
        });

        // Add listeners for polygon changes
        function addPolygonListeners(polygon) {
            // Update boundary when polygon is changed
            const updateBoundary = function() {
                const path = polygon.getPath();
                const coordinates = [];

                // Convert to [lat, lng] format for storage
                for (let i = 0; i < path.getLength(); i++) {
                    const point = path.getAt(i);
                    coordinates.push([point.lat(), point.lng()]);
                }

                // Store coordinates in hidden input
                boundaryInput.value = JSON.stringify(coordinates);

                // Calculate and update area
                const area = google.maps.geometry.spherical.computeArea(path);
                sizeField.value = Math.round(area);
                pondAreaDisplay.textContent = Math.round(area) + ' m²';

                // Calculate and update perimeter
                const perimeter = google.maps.geometry.spherical.computeLength(path);
                pondPerimeterDisplay.textContent = Math.round(perimeter) + ' m';

                // Update points count
                pondPointsDisplay.textContent = path.getLength();
            };

            // Add listeners for polygon changes
            google.maps.event.addListener(polygon.getPath(), 'insert_at', updateBoundary);
            google.maps.event.addListener(polygon.getPath(), 'remove_at', updateBoundary);
            google.maps.event.addListener(polygon.getPath(), 'set_at', updateBoundary);
            google.maps.event.addListener(polygon, 'dragend', updateBoundary);

            // Initial update
            updateBoundary();
        }

        // Handle locate me button
        document.getElementById('locate-me-btn').addEventListener('click', function() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        const lat = position.coords.latitude;
                        const lng = position.coords.longitude;
                        const pos = { lat, lng };

                        // Update map
                        map.setCenter(pos);
                        map.setZoom(16);

                        // Update marker
                        if (marker) {
                            marker.setMap(null);
                        }

                        marker = new google.maps.Marker({
                            position: pos,
                            map: map,
                            draggable: true,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                fillColor: '#1976D2',
                                fillOpacity: 1,
                                strokeColor: '#FFFFFF',
                                strokeWeight: 2,
                                scale: 8
                            }
                        });

                        // Update form fields
                        latitudeField.value = lat.toFixed(6);
                        longitudeField.value = lng.toFixed(6);

                        // Update form fields when marker is dragged
                        marker.addListener('dragend', function() {
                            const position = marker.getPosition();
                            latitudeField.value = position.lat().toFixed(6);
                            longitudeField.value = position.lng().toFixed(6);
                        });

                        // Show accuracy circle
                        const radius = position.coords.accuracy;

                        // Remove existing accuracy circle if any
                        if (window.accuracyCircle) {
                            window.accuracyCircle.setMap(null);
                        }

                        window.accuracyCircle = new google.maps.Circle({
                            strokeColor: '#1976D2',
                            strokeOpacity: 0.8,
                            strokeWeight: 1,
                            fillColor: '#1976D2',
                            fillOpacity: 0.1,
                            map: map,
                            center: pos,
                            radius: radius
                        });
                    },
                    function(error) {
                        alert("Could not find your location: " + error.message);
                    }
                );
            } else {
                alert("Geolocation is not supported by this browser.");
            }
        });

        // Handle draw boundary button
        document.getElementById('draw-boundary-btn').addEventListener('click', function() {
            // Clear existing polygon
            if (currentPolygon) {
                currentPolygon.setMap(null);
                currentPolygon = null;
            }

            // Clear hidden input
            boundaryInput.value = '';

            // Start polygon drawing mode
            drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
        });

        // Handle clear boundary button
        document.getElementById('clear-boundary-btn').addEventListener('click', function() {
            // Clear existing polygon
            if (currentPolygon) {
                currentPolygon.setMap(null);
                currentPolygon = null;
            }

            // Clear hidden input
            boundaryInput.value = '';

            // Reset size field if it was calculated from boundary
            if (sizeField.value > 0) {
                sizeField.value = '';
            }

            // Reset info panel
            pondAreaDisplay.textContent = '0 m²';
            pondPerimeterDisplay.textContent = '0 m';
            pondPointsDisplay.textContent = '0';
        });

        // Handle map type buttons
        document.getElementById('satellite-view-btn').addEventListener('click', function() {
            map.setMapTypeId(google.maps.MapTypeId.HYBRID);
            this.classList.add('active');
            document.getElementById('map-view-btn').classList.remove('active');
        });

        document.getElementById('map-view-btn').addEventListener('click', function() {
            map.setMapTypeId(google.maps.MapTypeId.ROADMAP);
            this.classList.add('active');
            document.getElementById('satellite-view-btn').classList.remove('active');
        });

        // Handle edit boundary button
        document.getElementById('edit-boundary-btn').addEventListener('click', function() {
            if (currentPolygon) {
                currentPolygon.setEditable(true);
                currentPolygon.setDraggable(true);
            } else {
                alert('No boundary to edit. Please draw a boundary first.');
            }
        });

        // Handle location search
        searchBtn.addEventListener('click', function() {
            const address = locationSearch.value;
            if (address) {
                geocoder.geocode({ 'address': address }, function(results, status) {
                    if (status === 'OK') {
                        map.setCenter(results[0].geometry.location);
                        map.setZoom(15);

                        // Update marker
                        if (marker) {
                            marker.setMap(null);
                        }

                        marker = new google.maps.Marker({
                            position: results[0].geometry.location,
                            map: map,
                            draggable: true,
                            icon: {
                                path: google.maps.SymbolPath.CIRCLE,
                                fillColor: '#1976D2',
                                fillOpacity: 1,
                                strokeColor: '#FFFFFF',
                                strokeWeight: 2,
                                scale: 8
                            }
                        });

                        // Update form fields
                        latitudeField.value = results[0].geometry.location.lat().toFixed(6);
                        longitudeField.value = results[0].geometry.location.lng().toFixed(6);

                        // Update form fields when marker is dragged
                        marker.addListener('dragend', function() {
                            const position = marker.getPosition();
                            latitudeField.value = position.lat().toFixed(6);
                            longitudeField.value = position.lng().toFixed(6);
                        });
                    } else {
                        alert('Geocode was not successful for the following reason: ' + status);
                    }
                });
            }
        });

        // Handle Enter key in search box
        locationSearch.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchBtn.click();
            }
        });
    }
    
    // Also initialize on DOM content loaded in case Google Maps is already loaded
    document.addEventListener('DOMContentLoaded', function() {
        if (window.google && window.google.maps) {
            console.log('Google Maps already loaded, initializing pond map');
            initializePondMap();
        }
    });
</script>
{% endblock %}