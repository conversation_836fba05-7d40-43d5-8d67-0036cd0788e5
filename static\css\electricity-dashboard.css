/**
 * Lunar Electricity Dashboard CSS - Responsive & Space-Inspired Interface
 * Comprehensive styling for Shrimp Farm Guardian electricity management
 */

/* ========================================
   LUNAR THEME CSS VARIABLES
   ======================================== */
:root {
    /* Lunar Color Palette */
    --lunar-primary: #0a0b1e;
    --lunar-secondary: #151632;
    --lunar-accent: #6366f1;
    --lunar-surface: #050510;
    --lunar-atmosphere: #1a1b3a;
    --lunar-crater: #1e1f42;
    --lunar-silver: #e2e8f0;
    --lunar-gold: #fbbf24;
    --lunar-energy: #10b981;
    --lunar-warning: #f59e0b;
    --lunar-danger: #ef4444;
    --lunar-nebula: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --lunar-aurora: linear-gradient(45deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    --lunar-cosmic: radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.15) 0%, transparent 70%);
    
    /* Status Colors */
    --status-online: #10b981;
    --status-offline: #ef4444;
    --status-warning: #f59e0b;
    --status-info: #6366f1;
    
    /* Alert Colors */
    --alert-critical: #ef4444;
    --alert-high: #fd7e14;
    --alert-medium: #f59e0b;
    --alert-low: #6366f1;
    
    /* Power Quality Colors */
    --power-excellent: #10b981;
    --power-good: #20c997;
    --power-fair: #f59e0b;
    --power-poor: #fd7e14;
    --power-critical: #ef4444;
    
    /* Equipment Colors */
    --equipment-running: #10b981;
    --equipment-standby: #6c757d;
    --equipment-fault: #ef4444;
    --equipment-maintenance: #f59e0b;
    
    /* Background Colors */
    --bg-primary: var(--lunar-surface);
    --bg-secondary: var(--lunar-atmosphere);
    --bg-tertiary: var(--lunar-crater);
    --bg-dark: var(--lunar-primary);
    
    /* Text Colors */
    --text-primary: var(--lunar-silver);
    --text-secondary: rgba(226, 232, 240, 0.8);
    --text-muted: rgba(226, 232, 240, 0.6);
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ========================================
   MAIN DASHBOARD LAYOUT
   ======================================== */
.electricity-dashboard {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--bg-tertiary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 1020;
}

.dashboard-title {
    color: var(--electricity-primary);
    font-weight: 600;
    margin: 0;
}

.dashboard-subtitle {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.connection-status {
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 0.875rem;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.connection-status.connected {
    color: var(--status-online);
    background: rgba(40, 167, 69, 0.1);
}

.connection-status.disconnected {
    color: var(--status-offline);
    background: rgba(220, 53, 69, 0.1);
}

.connection-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* ========================================
   MAIN CONTENT LAYOUT
   ======================================== */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--space-lg);
    padding: var(--space-lg);
    min-height: calc(100vh - 80px);
}

.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.dashboard-sidebar {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--space-lg);
    position: sticky;
    top: 100px;
    height: fit-content;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
}

/* ========================================
   FARM MAP SECTION
   ======================================== */
.farm-map-container {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.map-header {
    background: linear-gradient(135deg, var(--electricity-primary), var(--electricity-primary-dark));
    color: white;
    padding: var(--space-md) var(--space-lg);
    display: flex;
    justify-content: between;
    align-items: center;
}

.map-controls {
    display: flex;
    gap: var(--space-sm);
}

.map-control-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition-fast);
}

.map-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.farm-map {
    height: 400px;
    position: relative;
    background: #e8f4f8;
}

.equipment-marker {
    position: absolute;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow-sm);
    border: 3px solid white;
}

.equipment-marker:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.equipment-marker.running {
    background: var(--equipment-running);
    color: white;
}

.equipment-marker.standby {
    background: var(--equipment-standby);
    color: white;
}

.equipment-marker.fault {
    background: var(--equipment-fault);
    color: white;
    animation: alarm 1s infinite alternate;
}

.equipment-marker.maintenance {
    background: var(--equipment-maintenance);
    color: white;
}

@keyframes alarm {
    0% { opacity: 1; }
    100% { opacity: 0.6; }
}

.equipment-tooltip {
    position: absolute;
    background: var(--bg-dark);
    color: white;
    padding: var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: var(--transition-fast);
}

.equipment-marker:hover .equipment-tooltip {
    opacity: 1;
}

/* ========================================
   REAL-TIME MONITORING SECTION
   ======================================== */
.realtime-monitoring {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.monitoring-header {
    background: linear-gradient(135deg, var(--status-online), #20c997);
    color: white;
    padding: var(--space-md) var(--space-lg);
    display: flex;
    justify-content: between;
    align-items: center;
}

.monitoring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-md);
    padding: var(--space-lg);
}

.power-metric {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--space-md);
    text-align: center;
    transition: var(--transition-fast);
    border: 2px solid transparent;
}

.power-metric:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--electricity-primary-light);
}

.metric-icon {
    font-size: 2rem;
    margin-bottom: var(--space-sm);
}

.metric-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-sm);
}

.metric-trend {
    font-size: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-xs);
}

.trend-up {
    color: var(--status-online);
}

.trend-down {
    color: var(--status-offline);
}

.trend-stable {
    color: var(--text-secondary);
}

/* ========================================
   POWER QUALITY GAUGES
   ======================================== */
.power-quality-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.quality-header {
    background: linear-gradient(135deg, var(--status-info), #6610f2);
    color: white;
    padding: var(--space-md) var(--space-lg);
}

.quality-gauges {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-lg);
    padding: var(--space-lg);
}

.gauge-container {
    text-align: center;
}

.gauge-chart {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto var(--space-md);
}

.gauge-label {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
}

.gauge-status {
    display: inline-block;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.gauge-status.excellent {
    background: var(--power-excellent);
    color: white;
}

.gauge-status.good {
    background: var(--power-good);
    color: white;
}

.gauge-status.fair {
    background: var(--power-fair);
    color: var(--text-primary);
}

.gauge-status.poor {
    background: var(--power-poor);
    color: white;
}

.gauge-status.critical {
    background: var(--power-critical);
    color: white;
}

/* ========================================
   CHARTS AND GRAPHS
   ======================================== */
.chart-container {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.chart-header {
    background: var(--bg-secondary);
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--bg-tertiary);
    display: flex;
    justify-content: between;
    align-items: center;
}

.chart-title {
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--space-sm);
    align-items: center;
}

.time-range-selector {
    background: var(--bg-primary);
    border: 1px solid var(--bg-tertiary);
    border-radius: var(--radius-sm);
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.875rem;
    cursor: pointer;
}

.chart-content {
    padding: var(--space-lg);
    height: 300px;
    position: relative;
}

.chart-canvas {
    width: 100% !important;
    height: 100% !important;
}

/* ========================================
   ALERT PANELS
   ======================================== */
.alerts-panel {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    max-height: 400px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.alerts-header {
    background: linear-gradient(135deg, var(--alert-critical), var(--alert-high));
    color: white;
    padding: var(--space-md) var(--space-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-count {
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 600;
}

.alerts-list {
    flex: 1;
    overflow-y: auto;
    max-height: 300px;
}

.alert-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    border-bottom: 1px solid var(--bg-tertiary);
    cursor: pointer;
    transition: var(--transition-fast);
}

.alert-item:hover {
    background: var(--bg-secondary);
}

.alert-item:last-child {
    border-bottom: none;
}

.alert-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.alert-icon.critical {
    background: var(--alert-critical);
    animation: pulse 2s infinite;
}

.alert-icon.high {
    background: var(--alert-high);
}

.alert-icon.medium {
    background: var(--alert-medium);
}

.alert-icon.low {
    background: var(--alert-low);
}

.alert-content {
    flex: 1;
    min-width: 0;
}

.alert-message {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.alert-details {
    font-size: 0.875rem;
    color: var(--text-secondary);
    display: flex;
    gap: var(--space-md);
}

.alert-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    white-space: nowrap;
}

/* ========================================
   GENERATOR STATUS
   ======================================== */
.generator-panel {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.generator-header {
    background: linear-gradient(135deg, #6610f2, #e83e8c);
    color: white;
    padding: var(--space-md) var(--space-lg);
}

.generator-status {
    padding: var(--space-lg);
}

.generator-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.generator-metric {
    text-align: center;
}

.generator-metric-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.generator-metric-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.generator-controls {
    display: flex;
    gap: var(--space-sm);
    justify-content: center;
}

.generator-control {
    padding: var(--space-sm) var(--space-md);
    border: none;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.generator-control.start {
    background: var(--status-online);
    color: white;
}

.generator-control.stop {
    background: var(--status-offline);
    color: white;
}

.generator-control:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.generator-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ========================================
   COST ANALYSIS
   ======================================== */
.cost-panel {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.cost-header {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    color: white;
    padding: var(--space-md) var(--space-lg);
}

.cost-summary {
    padding: var(--space-lg);
}

.cost-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
}

.cost-metric {
    text-align: center;
    padding: var(--space-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.cost-amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
}

.cost-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.cost-trend {
    font-size: 0.75rem;
    margin-top: var(--space-xs);
}

/* ========================================
   SIDEBAR COMPONENTS
   ======================================== */
.sidebar-section {
    margin-bottom: var(--space-xl);
}

.sidebar-section:last-child {
    margin-bottom: 0;
}

.sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.quick-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

.quick-stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.quick-stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

/* ========================================
   BUTTONS AND CONTROLS
   ======================================== */
.btn-electricity {
    background: var(--electricity-primary);
    border: none;
    color: white;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-xs);
}

.btn-electricity:hover {
    background: var(--electricity-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-electricity:active {
    transform: translateY(0);
}

.btn-electricity.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.875rem;
}

.btn-electricity.btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: 1.1rem;
}

.btn-outline-electricity {
    background: transparent;
    border: 2px solid var(--electricity-primary);
    color: var(--electricity-primary);
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-outline-electricity:hover {
    background: var(--electricity-primary);
    color: white;
}

/* ========================================
   LOADING STATES
   ======================================== */
.dashboard-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    flex-direction: column;
    gap: var(--space-md);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--bg-tertiary);
    border-top: 4px solid var(--electricity-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: var(--text-secondary);
    font-weight: 600;
}

.metric-skeleton {
    background: linear-gradient(90deg, var(--bg-tertiary) 25%, var(--bg-secondary) 50%, var(--bg-tertiary) 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: var(--radius-sm);
    height: 20px;
    margin: var(--space-sm) 0;
}

@keyframes loading-shimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* ========================================
   COSMIC STATUS CARDS (Enhanced with glassmorphism)
   ======================================== */
.cosmic-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 30px 20px;
    animation: slide-up 0.6s ease-out;
}

.cosmic-status-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 2px solid rgba(255,255,255,0.2);
    padding: 24px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.cosmic-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.6s ease;
}

.cosmic-status-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 0 50px rgba(99, 102, 241, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255,255,255,0.4);
}

.cosmic-status-card:hover::before {
    left: 100%;
}

.cosmic-icon {
    font-size: 2.5rem;
    margin-bottom: 16px;
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: icon-glow 3s ease-in-out infinite;
}

@keyframes icon-glow {
    0%, 100% { 
        filter: drop-shadow(0 0 10px rgba(116, 185, 255, 0.5));
    }
    50% { 
        filter: drop-shadow(0 0 20px rgba(116, 185, 255, 0.8));
    }
}

.cosmic-value {
    font-size: 2.8rem;
    font-weight: 800;
    color: var(--lunar-silver);
    margin-bottom: 8px;
    font-family: 'Orbitron', monospace;
    text-shadow: 0 0 20px rgba(226, 232, 240, 0.3);
}

.cosmic-label {
    font-size: 1rem;
    color: rgba(226, 232, 240, 0.8);
    margin-bottom: 12px;
    font-weight: 500;
}

.cosmic-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

.trend-positive {
    background: rgba(16, 185, 129, 0.2);
    color: var(--lunar-energy);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.trend-negative {
    background: rgba(239, 68, 68, 0.2);
    color: var(--lunar-danger);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ========================================
   LUNAR PANELS (Enhanced with glassmorphism like /lunar/)
   ======================================== */
.lunar-panel {
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
    backdrop-filter: blur(15px);
    border-radius: 20px;
    border: 2px solid rgba(255,255,255,0.15);
    margin-bottom: 24px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.lunar-panel::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.08), transparent);
    animation: panel-sweep 6s infinite ease-in-out;
    pointer-events: none;
}

@keyframes panel-sweep {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.lunar-panel:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 15px 40px rgba(0, 0, 0, 0.25),
        0 0 60px rgba(99, 102, 241, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border-color: rgba(255,255,255,0.3);
}

.lunar-panel-header {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.2), rgba(139, 69, 19, 0.1));
    border-bottom: 2px solid rgba(255,255,255,0.1);
    padding: 20px 24px;
    border-radius: 18px 18px 0 0;
    position: relative;
}

.lunar-panel-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.5), transparent);
}

.lunar-panel-title {
    color: var(--lunar-silver);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 12px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.lunar-panel-title i {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.4rem;
    filter: drop-shadow(0 0 8px rgba(116, 185, 255, 0.5));
}

.lunar-panel-content {
    padding: 24px;
    position: relative;
    z-index: 1;
}

/* ========================================
   PULSING ANIMATIONS & IMPORTANCE LEVELS (Inspired by /lunar/)
   ======================================== */
.importance-high {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52) !important;
    animation: pulse-high 2s infinite;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4) !important;
}

.importance-medium {
    background: linear-gradient(45deg, #feca57, #ff9ff3) !important;
    animation: pulse-medium 3s infinite;
}

.importance-low {
    background: linear-gradient(45deg, #48dbfb, #0abde3) !important;
}

@keyframes pulse-high {
    0%, 100% { 
        opacity: 1; 
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.4);
    }
    50% { 
        opacity: 0.85; 
        transform: scale(1.02);
        box-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
    }
}

@keyframes pulse-medium {
    0%, 100% { 
        opacity: 1;
        box-shadow: 0 0 15px rgba(254, 202, 87, 0.3);
    }
    50% { 
        opacity: 0.9;
        box-shadow: 0 0 25px rgba(254, 202, 87, 0.5);
    }
}

/* Equipment Status with Importance Levels */
.equipment-status.critical {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52) !important;
    animation: pulse-high 2s infinite;
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.4) !important;
}

.equipment-status.warning {
    background: linear-gradient(45deg, #feca57, #ff9ff3) !important;
    animation: pulse-medium 3s infinite;
}

.equipment-status.normal {
    background: linear-gradient(45deg, #48dbfb, #0abde3) !important;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr 350px;
    }
    
    .monitoring-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 992px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .dashboard-sidebar {
        position: static;
        max-height: none;
    }
    
    .monitoring-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
    
    .quality-gauges {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: var(--space-md);
    }
    
    .dashboard-header {
        padding: var(--space-md);
    }
    
    .map-header,
    .monitoring-header,
    .quality-header,
    .chart-header {
        flex-direction: column;
        gap: var(--space-sm);
        text-align: center;
    }
    
    .map-controls,
    .chart-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .monitoring-grid {
        grid-template-columns: 1fr;
    }
    
    .quality-gauges {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
    
    .gauge-chart {
        width: 120px;
        height: 120px;
    }
    
    .cost-metrics {
        grid-template-columns: 1fr;
    }
    
    .generator-info {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .alert-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm);
    }
    
    .alert-details {
        flex-direction: column;
        gap: var(--space-xs);
    }
}

@media (max-width: 480px) {
    :root {
        --space-lg: 1rem;
        --space-xl: 2rem;
    }
    
    .main-content {
        padding: var(--space-sm);
        gap: var(--space-md);
    }
    
    .farm-map {
        height: 250px;
    }
    
    .chart-content {
        height: 200px;
        padding: var(--space-md);
    }
    
    .equipment-marker {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
    
    .dashboard-title {
        font-size: 1.25rem;
    }
    
    .metric-value {
        font-size: 1.25rem;
    }
    
    .cost-amount {
        font-size: 1.25rem;
    }
    
    .generator-metric-value {
        font-size: 1rem;
    }
}

/* ========================================
   ACCESSIBILITY IMPROVEMENTS
   ======================================== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --bg-tertiary: #404040;
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --text-muted: #808080;
    }
}

/* Focus styles for keyboard navigation */
.btn-electricity:focus,
.generator-control:focus,
.time-range-selector:focus,
.equipment-marker:focus {
    outline: 2px solid var(--electricity-primary);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .equipment-marker,
    .alert-icon,
    .gauge-status {
        border: 2px solid currentColor;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */
@media print {
    .electricity-dashboard {
        background: white;
    }
    
    .dashboard-header,
    .map-controls,
    .chart-controls,
    .generator-controls,
    .btn-electricity {
        display: none;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .chart-container,
    .alerts-panel,
    .generator-panel,
    .cost-panel {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
}

/* ========================================
   COSMIC STAR FIELD BACKGROUND (Inspired by space theme)
   ======================================== */
.lunar-dashboard::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255,255,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: stars-twinkle 8s infinite ease-in-out;
    pointer-events: none;
    z-index: 0;
}

@keyframes stars-twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* Ensure content is above stars */
.lunar-nav,
.quick-actions,
.lunar-header,
.cosmic-status-grid,
.lunar-panel {
    position: relative;
    z-index: 1;
}
