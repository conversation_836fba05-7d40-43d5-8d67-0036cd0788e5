<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply {{ treatment.get_treatment_type_display }} Treatment - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Quick Actions Styles */
    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="container-fluid">
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="/lunar/" class="quick-action">
                    <i class="fas fa-calendar"></i>
                    Lunar Calendar
                </a>
                <a href="/lunar/enhanced/" class="quick-action">
                    <i class="fas fa-star"></i>
                    Enhanced Dashboard
                </a>
                <a href="/lunar/full-calendar/" class="quick-action">
                    <i class="fas fa-calendar-alt"></i>
                    Full Calendar
                </a>
                <a href="/" class="quick-action">
                    <i class="fas fa-home"></i>
                    Back to Home
                </a>
            </div>
<div class="container-fluid px-4">
    <h1 class="mt-4">Apply Treatment</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{% url 'lunar:lunar_calendar' %}">Lunar Calendar</a></li>
        <li class="breadcrumb-item"><a href="{% url 'lunar:lunar_day_detail' pk=treatment.lunar_day.pk %}">{{ treatment.lunar_day.phase }} Details</a></li>
        <li class="breadcrumb-item active">Apply Treatment</li>
    </ol>
    
    <div class="row">
        <div class="col-lg-8 col-xl-6 mx-auto">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-flask me-1"></i>
                    Apply {{ treatment.get_treatment_type_display }} Treatment
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-4">
                        <h5 class="alert-heading">Treatment Information</h5>
                        <p><strong>Pond:</strong> {{ treatment.pond.name }}</p>
                        <p><strong>Lunar Day:</strong> {{ treatment.lunar_day.phase }} on {{ treatment.lunar_day.formatted_date }}</p>
                        <p><strong>Treatment Type:</strong> {{ treatment.get_treatment_type_display }}</p>
                        <p><strong>Recommended Quantity:</strong> {{ treatment.quantity }} {{ treatment.unit }}</p>
                        {% if treatment.notes %}
                        <p><strong>Notes:</strong> {{ treatment.notes|linebreaksbr }}</p>
                        {% endif %}
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="{{ form.quantity_applied.id_for_label }}" class="form-label">Actual Quantity Applied</label>
                            {{ form.quantity_applied }}
                            <div class="form-text">{{ form.quantity_applied.help_text }}</div>
                            {% if form.quantity_applied.errors %}
                            <div class="invalid-feedback d-block">{{ form.quantity_applied.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">Application Notes</label>
                            {{ form.notes }}
                            <div class="form-text">{{ form.notes.help_text }}</div>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'lunar:lunar_day_detail' pk=treatment.lunar_day.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Back
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-1"></i> Confirm Application                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

        </div>
    </div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>