<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Agent/Supervisor - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }
    
    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        margin: 20px;
        padding: 30px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }
    
    /* Enhanced Header */
    .agent-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .agent-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: agent-sweep 4s infinite;
    }
    
    @keyframes agent-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    .enhanced-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 25px;
        border: 1px solid rgba(102, 126, 234, 0.1);
        transition: all 0.3s ease;
    }

    .enhanced-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        border-color: rgba(102, 126, 234, 0.2);
    }

    .section-title {
        color: #667eea;
        font-weight: 700;
        margin-bottom: 25px;
        font-size: 1.3rem;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
    }

    .control-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        text-decoration: none;
    }

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .quick-actions {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-wrap: wrap;
        margin: 30px 0;
        position: relative;
        z-index: 10;
    }
    
    .quick-action {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        cursor: pointer;
        pointer-events: auto;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        color: white;
        text-decoration: none;
    }

    .form-control, .form-select {
        border: 2px solid rgba(102, 126, 234, 0.1);
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-label {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .btn-outline-secondary {
        border: 2px solid rgba(102, 126, 234, 0.3);
        color: #667eea;
        border-radius: 25px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-secondary:hover {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .photo-upload-area {
        border: 2px dashed rgba(102, 126, 234, 0.3);
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        background: rgba(102, 126, 234, 0.02);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .photo-upload-area:hover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.05);
    }

    .photo-upload-area.dragover {
        border-color: #667eea;
        background: rgba(102, 126, 234, 0.1);
    }

    .photo-preview {
        max-width: 150px;
        max-height: 150px;
        border-radius: 10px;
        margin: 10px auto;
        display: block;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .form-section {
        background: rgba(102, 126, 234, 0.02);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        border: 1px solid rgba(102, 126, 234, 0.1);
    }

    .required {
        color: #dc3545;
    }

    .file-info {
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid rgba(40, 167, 69, 0.2);
        border-radius: 8px;
        padding: 10px;
        margin-top: 10px;
        color: #155724;
    }

    .role-badge {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        margin: 3px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .role-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .role-badge.selected {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    }
    </style>
</head>

<body>
<div class="main-container">
    <!-- Enhanced Header -->
    <div class="agent-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-2">Create Agent/Supervisor</h1>
                <p class="lead mb-0">Add a new management team member</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex flex-wrap gap-2 justify-content-end">
                    <a href="/labor/agents/" class="control-btn">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <a href="/labor/agents/" class="quick-action">
            <i class="fas fa-user-tie"></i>
            All Agents
        </a>
        <a href="/labor/workers/" class="quick-action">
            <i class="fas fa-users"></i>
            Workers
        </a>
        <a href="/labor/dashboard/" class="quick-action">
            <i class="fas fa-tachometer-alt"></i>
            Dashboard
        </a>
    </div>

    <!-- Agent Creation Form -->
    <form method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        <!-- Personal Information Section -->
        <div class="enhanced-card">
            <h3 class="section-title">
                <i class="fas fa-user-tie me-2"></i>Personal Information
            </h3>
            
            <div class="row">
                <!-- Photo Upload Section -->
                <div class="col-md-4">
                    <div class="form-section">
                        <label class="form-label">Agent Photo <span class="required">*</span></label>
                        <div class="photo-upload-area" onclick="document.getElementById('photo-input').click()">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <p class="text-muted mb-0">Click to upload photo</p>
                            <p class="small text-muted">or drag and drop here</p>
                            <img id="photo-preview" class="photo-preview" style="display: none;" alt="Photo Preview">
                        </div>
                        <input type="file" id="photo-input" name="photo" accept="image/*" style="display: none;" required>
                        <div id="photo-info" class="file-info" style="display: none;"></div>
                    </div>
                </div>
                
                <!-- Basic Details -->
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">Full Name <span class="required">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="employee_id" class="form-label">Employee ID <span class="required">*</span></label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" required placeholder="AGT001">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email Address <span class="required">*</span></label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="mobile" class="form-label">Mobile Number <span class="required">*</span></label>
                            <input type="tel" class="form-control" id="mobile" name="mobile" required>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role & Authority Section -->
        <div class="enhanced-card">
            <h3 class="section-title">
                <i class="fas fa-user-shield me-2"></i>Role & Authority
            </h3>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="role_type" class="form-label">Role Type <span class="required">*</span></label>
                    <select class="form-select" id="role_type" name="role_type" required>
                        <option value="">Select Role Type</option>
                        <option value="field-agent">Field Agent</option>
                        <option value="team-supervisor">Team Supervisor</option>
                        <option value="labor-contractor">Labor Contractor</option>
                        <option value="senior-coordinator">Senior Coordinator</option>
                        <option value="shift-manager">Shift Manager</option>
                        <option value="area-manager">Area Manager</option>
                        <option value="operations-head">Operations Head</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="department" class="form-label">Department <span class="required">*</span></label>
                    <select class="form-select" id="department" name="department" required>
                        <option value="">Select Department</option>
                        <option value="pond-management">Pond Management</option>
                        <option value="feeding-operations">Feeding Operations</option>
                        <option value="water-quality">Water Quality Control</option>
                        <option value="harvesting">Harvesting</option>
                        <option value="security">Security</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="general-operations">General Operations</option>
                        <option value="administration">Administration</option>
                    </select>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 mb-3">
                    <label class="form-label">Authority Level <span class="required">*</span></label>
                    <div class="form-section">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="can_assign_tasks" name="authorities" value="assign_tasks">
                                    <label class="form-check-label" for="can_assign_tasks">
                                        Assign Tasks
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="can_approve_attendance" name="authorities" value="approve_attendance">
                                    <label class="form-check-label" for="can_approve_attendance">
                                        Approve Attendance
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="can_manage_workers" name="authorities" value="manage_workers">
                                    <label class="form-check-label" for="can_manage_workers">
                                        Manage Workers
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="can_approve_overtime" name="authorities" value="approve_overtime">
                                    <label class="form-check-label" for="can_approve_overtime">
                                        Approve Overtime
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employment Information Section -->
        <div class="enhanced-card">
            <h3 class="section-title">
                <i class="fas fa-briefcase me-2"></i>Employment Information
            </h3>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="reporting_to" class="form-label">Reports To</label>
                    <select class="form-select" id="reporting_to" name="reporting_to">
                        <option value="">Select Superior</option>
                        <option value="operations-head-1">Operations Head - Rajesh Gupta</option>
                        <option value="area-manager-1">Area Manager - Sunita Reddy</option>
                        <option value="senior-coordinator-1">Senior Coordinator - Prakash</option>
                        <option value="farm-manager">Farm Manager</option>
                        <option value="general-manager">General Manager</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="salary" class="form-label">Monthly Salary (₹)</label>
                    <input type="number" class="form-control" id="salary" name="salary" step="0.01" min="0">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="join_date" class="form-label">Joining Date <span class="required">*</span></label>
                    <input type="date" class="form-control" id="join_date" name="join_date" required>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="experience_years" class="form-label">Years of Experience</label>
                    <input type="number" class="form-control" id="experience_years" name="experience_years" min="0" max="50">
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            Active Employee
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information Section -->
        <div class="enhanced-card">
            <h3 class="section-title">
                <i class="fas fa-map-marker-alt me-2"></i>Contact Information
            </h3>
            
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label for="address" class="form-label">Complete Address <span class="required">*</span></label>
                    <textarea class="form-control" id="address" name="address" rows="3" required placeholder="Enter complete address with landmark"></textarea>
                </div>
                <div class="col-md-4">
                    <div class="form-section">
                        <label class="form-label">ID Proof <span class="required">*</span></label>
                        <div class="photo-upload-area" onclick="document.getElementById('id-proof-input').click()">
                            <i class="fas fa-id-card fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Upload ID Document</p>
                            <p class="small text-muted">Aadhar/PAN/License</p>
                        </div>
                        <input type="file" id="id-proof-input" name="id_proof" accept=".jpg,.jpeg,.png,.pdf" style="display: none;" required>
                        <div id="id-proof-info" class="file-info" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Contact Section -->
        <div class="enhanced-card">
            <h3 class="section-title">
                <i class="fas fa-phone-alt me-2"></i>Emergency Contact
            </h3>
            
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="emergency_contact_name" class="form-label">Emergency Contact Name</label>
                    <input type="text" class="form-control" id="emergency_contact_name" name="emergency_contact_name">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="emergency_contact_phone" class="form-label">Emergency Contact Phone</label>
                    <input type="tel" class="form-control" id="emergency_contact_phone" name="emergency_contact_phone">
                </div>
                <div class="col-md-12 mb-3">
                    <label for="emergency_contact_relation" class="form-label">Relationship</label>
                    <select class="form-select" id="emergency_contact_relation" name="emergency_contact_relation">
                        <option value="">Select Relationship</option>
                        <option value="father">Father</option>
                        <option value="mother">Mother</option>
                        <option value="spouse">Spouse</option>
                        <option value="sibling">Sibling</option>
                        <option value="friend">Friend</option>
                        <option value="relative">Relative</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="text-center">
            <button type="submit" class="control-btn me-3">
                <i class="fas fa-save me-2"></i>Create Agent/Supervisor
            </button>
            <a href="/labor/agents/" class="btn-outline-secondary">
                <i class="fas fa-times me-2"></i>Cancel
            </a>
        </div>
    </form>
</div>

<!-- JavaScript for file handling and form interactions -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default joining date
    document.getElementById('join_date').value = new Date().toISOString().split('T')[0];
    
    // Auto-generate employee ID based on role
    document.getElementById('role_type').addEventListener('change', function() {
        const roleType = this.value;
        const employeeIdField = document.getElementById('employee_id');
        
        if (roleType && !employeeIdField.value) {
            const prefixes = {
                'field-agent': 'AGT',
                'team-supervisor': 'SUP',
                'labor-contractor': 'CON',
                'senior-coordinator': 'CRD',
                'shift-manager': 'SHM',
                'area-manager': 'ARM',
                'operations-head': 'OPH'
            };
            
            const prefix = prefixes[roleType] || 'EMP';
            const randomNum = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
            employeeIdField.value = prefix + randomNum;
        }
    });
    
    // Photo upload handling
    const photoInput = document.getElementById('photo-input');
    const photoPreview = document.getElementById('photo-preview');
    const photoInfo = document.getElementById('photo-info');
    const photoUploadArea = document.querySelector('.photo-upload-area');
    
    photoInput.addEventListener('change', function(e) {
        handleFileUpload(e.target.files[0], photoPreview, photoInfo, 'image');
    });
    
    // ID proof upload handling
    const idProofInput = document.getElementById('id-proof-input');
    const idProofInfo = document.getElementById('id-proof-info');
    
    idProofInput.addEventListener('change', function(e) {
        handleFileUpload(e.target.files[0], null, idProofInfo, 'document');
    });
    
    // Drag and drop for photo
    photoUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });
    
    photoUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });
    
    photoUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            photoInput.files = files;
            handleFileUpload(files[0], photoPreview, photoInfo, 'image');
        }
    });
    
    function handleFileUpload(file, previewElement, infoElement, type) {
        if (!file) return;
        
        // Show file info
        if (infoElement) {
            infoElement.style.display = 'block';
            infoElement.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                <strong>${file.name}</strong><br>
                <small>Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</small>
            `;
        }
        
        // Show image preview for photos
        if (type === 'image' && previewElement) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewElement.src = e.target.result;
                previewElement.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    }
    
    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = ['name', 'employee_id', 'email', 'mobile', 'role_type', 'department', 'join_date', 'address'];
        const requiredFiles = ['photo', 'id_proof'];
        let isValid = true;
        
        // Check required text fields
        requiredFields.forEach(fieldName => {
            const field = document.getElementById(fieldName);
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        // Check required file fields
        requiredFiles.forEach(fieldName => {
            const field = document.getElementById(fieldName + '-input');
            if (!field.files.length) {
                field.parentElement.style.borderColor = '#dc3545';
                isValid = false;
            } else {
                field.parentElement.style.borderColor = '';
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill all required fields and upload required documents.');
        }
    });
    
    // Auto-format phone numbers
    const phoneFields = ['mobile', 'emergency_contact_phone'];
    phoneFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (field) {
            field.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 10) value = value.slice(0, 10);
                if (value.length > 5) {
                    value = value.slice(0, 5) + '-' + value.slice(5);
                }
                e.target.value = value;
            });
        }
    });
});
</script>

</body>
</html>
