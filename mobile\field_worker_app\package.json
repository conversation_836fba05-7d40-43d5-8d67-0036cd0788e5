{"name": "ShrimpFarmFieldWorker", "version": "1.0.0", "description": "Field Worker Task Management App for Shrimp Farm Operations", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace ShrimpFarmFieldWorker.xcworkspace -scheme ShrimpFarmFieldWorker archive", "postinstall": "cd ios && pod install"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-camera": "^4.2.1", "react-native-image-picker": "^7.0.3", "react-native-permissions": "^3.10.1", "react-native-geolocation-service": "^5.3.1", "react-native-maps": "^1.8.0", "react-native-push-notification": "^8.1.1", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1", "react-native-device-info": "^10.11.0", "react-native-network-info": "^5.2.1", "react-native-sqlite-storage": "^6.0.1", "react-native-fs": "^2.20.0", "react-native-background-job": "^0.2.9", "react-native-qrcode-scanner": "^1.5.5", "react-native-share": "^9.4.1", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-date-picker": "^4.3.3", "axios": "^1.6.0", "moment": "^2.29.4", "lodash": "^4.17.21", "react-hook-form": "^7.47.0", "yup": "^1.3.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@types/react": "^18.0.24", "@types/lodash": "^4.14.200", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "prettier": "^2.4.1", "typescript": "4.8.4"}}