<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ Live Dashboard - Real-time Farm Monitoring</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: '<PERSON><PERSON><PERSON> UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .live-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .live-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            position: relative;
            overflow: hidden;
        }
        
        .live-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255,255,255,0.1) 10px,
                rgba(255,255,255,0.1) 20px
            );
            animation: livePattern 20s linear infinite;
        }
        
        @keyframes livePattern {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }
        
        .live-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            position: relative;
            z-index: 2;
        }
        
        .live-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-connecting {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .live-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .live-metric-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }
        
        .live-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #28a745, transparent);
            animation: pulse-line 2s ease-in-out infinite;
        }
        
        @keyframes pulse-line {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }
        
        .live-metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }
        
        .metric-value.updating {
            color: #28a745;
            transform: scale(1.05);
        }
        
        .metric-label {
            color: #64748b;
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .metric-timestamp {
            color: #9ca3af;
            font-size: 0.8rem;
        }
        
        .live-charts {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .chart-panel {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .panel-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
        }
        
        .live-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #28a745;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .live-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #28a745;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.5; transform: scale(1.2); }
            100% { opacity: 1; transform: scale(1); }
        }
        
        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            transition: all 0.3s ease;
        }
        
        .activity-item:hover {
            background: #f8f9fa;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            color: white;
            flex-shrink: 0;
        }
        
        .activity-icon.reading {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }
        
        .activity-icon.prediction {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .activity-icon.alert {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }
        
        .activity-description {
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        
        .activity-time {
            color: #9ca3af;
            font-size: 0.8rem;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
            color: white;
        }
        
        @media (max-width: 768px) {
            .live-container {
                margin: 10px;
                padding: 20px;
            }
            
            .live-charts {
                grid-template-columns: 1fr;
            }
            
            .live-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>

<div class="live-container">
    <!-- Connection Status -->
    <div id="connection-status" class="connection-status status-connecting">
        <i class="fas fa-wifi me-2"></i>
        <span id="status-text">Connecting...</span>
    </div>

    <!-- Live Header -->
    <div class="live-header">
        <h1 class="live-title">⚡ Live Dashboard</h1>
        <p class="live-subtitle">Real-time farm monitoring with WebSocket streaming</p>
        
        <!-- Quick Actions -->
        <div class="quick-actions mt-4">
            <a href="#" class="quick-action" onclick="requestUpdate()">
                <i class="fas fa-sync"></i>
                <span>Refresh Data</span>
            </a>
            <a href="#" class="quick-action" onclick="toggleAutoUpdate()">
                <i class="fas fa-play" id="auto-update-icon"></i>
                <span id="auto-update-text">Auto Update</span>
            </a>
            <a href="/" class="quick-action">
                <i class="fas fa-home"></i>
                <span>Main Dashboard</span>
            </a>
        </div>
    </div>

    <!-- Live Metrics -->
    <div class="live-metrics">
        <div class="live-metric-card">
            <div class="metric-icon">
                <i class="fas fa-industry"></i>
            </div>
            <div class="metric-value" id="total-ponds">--</div>
            <div class="metric-label">Total Ponds</div>
            <div class="metric-timestamp" id="ponds-timestamp">--</div>
        </div>
        
        <div class="live-metric-card">
            <div class="metric-icon">
                <i class="fas fa-wifi"></i>
            </div>
            <div class="metric-value" id="online-devices">--</div>
            <div class="metric-label">Online Devices</div>
            <div class="metric-timestamp" id="devices-timestamp">--</div>
        </div>
        
        <div class="live-metric-card">
            <div class="metric-icon">
                <i class="fas fa-thermometer-half"></i>
            </div>
            <div class="metric-value" id="avg-temperature">--°C</div>
            <div class="metric-label">Avg Temperature</div>
            <div class="metric-timestamp" id="temp-timestamp">--</div>
        </div>
        
        <div class="live-metric-card">
            <div class="metric-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="metric-value" id="active-alerts">--</div>
            <div class="metric-label">Active Alerts</div>
            <div class="metric-timestamp" id="alerts-timestamp">--</div>
        </div>
    </div>

    <!-- Live Charts -->
    <div class="live-charts">
        <!-- Real-time Chart -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">Real-time Water Quality</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            <canvas id="realtime-chart" width="400" height="200"></canvas>
        </div>

        <!-- Activity Feed -->
        <div class="chart-panel">
            <div class="panel-header">
                <div class="panel-title">Live Activity Feed</div>
                <div class="live-indicator">
                    <div class="live-dot"></div>
                    <span>LIVE</span>
                </div>
            </div>
            
            <div class="activity-feed" id="activity-feed">
                <!-- Activity items will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
// WebSocket connection management
let socket = null;
let autoUpdate = true;
let realtimeChart = null;

// Initialize live dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('⚡ Live Dashboard initializing...');
    
    initializeWebSocket();
    initializeChart();
    
    // Auto-reconnect on page visibility change
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden && (!socket || socket.readyState === WebSocket.CLOSED)) {
            console.log('Page visible, reconnecting WebSocket...');
            initializeWebSocket();
        }
    });
});

function initializeWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/farm/123e4567-e89b-12d3-a456-426614174000/dashboard/`;
    
    updateConnectionStatus('connecting');
    
    socket = new WebSocket(wsUrl);
    
    socket.onopen = function(e) {
        console.log('✅ WebSocket connected');
        updateConnectionStatus('connected');
    };
    
    socket.onmessage = function(e) {
        const data = JSON.parse(e.data);
        console.log('📨 Received:', data.type);
        
        handleWebSocketMessage(data);
    };
    
    socket.onclose = function(e) {
        console.log('❌ WebSocket closed');
        updateConnectionStatus('disconnected');
        
        // Auto-reconnect after 5 seconds
        setTimeout(() => {
            if (autoUpdate) {
                console.log('🔄 Attempting to reconnect...');
                initializeWebSocket();
            }
        }, 5000);
    };
    
    socket.onerror = function(e) {
        console.error('❌ WebSocket error:', e);
        updateConnectionStatus('disconnected');
    };
}

function handleWebSocketMessage(data) {
    switch(data.type) {
        case 'initial_data':
        case 'farm_update':
            updateDashboardData(data.data);
            break;
        case 'pond_update':
            updatePondData(data.data);
            break;
        case 'device_alert':
            addActivityItem('alert', data.data);
            break;
        case 'pong':
            console.log('🏓 Pong received');
            break;
        case 'error':
            console.error('❌ WebSocket error:', data.message);
            break;
    }
}

function updateDashboardData(data) {
    if (data.error) {
        console.error('❌ Data error:', data.error);
        return;
    }
    
    // Update metrics with animation
    updateMetricValue('total-ponds', data.farm?.total_ponds || 0);
    updateMetricValue('online-devices', data.devices?.online || 0);
    updateMetricValue('active-alerts', data.alerts?.active || 0);
    
    // Calculate average temperature
    if (data.recent_readings && data.recent_readings.length > 0) {
        const avgTemp = data.recent_readings.reduce((sum, reading) => sum + reading.temperature, 0) / data.recent_readings.length;
        updateMetricValue('avg-temperature', avgTemp.toFixed(1) + '°C');
    }
    
    // Update timestamps
    const now = new Date().toLocaleTimeString();
    document.getElementById('ponds-timestamp').textContent = now;
    document.getElementById('devices-timestamp').textContent = now;
    document.getElementById('temp-timestamp').textContent = now;
    document.getElementById('alerts-timestamp').textContent = now;
    
    // Update chart
    if (data.recent_readings) {
        updateRealtimeChart(data.recent_readings);
    }
    
    // Update activity feed
    if (data.recent_readings) {
        data.recent_readings.slice(0, 3).forEach(reading => {
            addActivityItem('reading', {
                pond_name: reading.pond_name,
                temperature: reading.temperature,
                ph: reading.ph,
                oxygen: reading.oxygen,
                timestamp: reading.timestamp
            });
        });
    }
    
    if (data.recent_predictions) {
        data.recent_predictions.slice(0, 2).forEach(prediction => {
            addActivityItem('prediction', {
                pond_name: prediction.pond_name,
                type: prediction.type,
                value: prediction.value,
                confidence: prediction.confidence,
                timestamp: prediction.timestamp
            });
        });
    }
}

function updateMetricValue(elementId, newValue) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add('updating');
        element.textContent = newValue;
        
        setTimeout(() => {
            element.classList.remove('updating');
        }, 500);
    }
}

function updateConnectionStatus(status) {
    const statusElement = document.getElementById('connection-status');
    const statusText = document.getElementById('status-text');
    
    statusElement.className = `connection-status status-${status}`;
    
    switch(status) {
        case 'connected':
            statusText.innerHTML = '<i class="fas fa-wifi me-2"></i>Connected';
            break;
        case 'connecting':
            statusText.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connecting...';
            break;
        case 'disconnected':
            statusText.innerHTML = '<i class="fas fa-wifi-slash me-2"></i>Disconnected';
            break;
    }
}

function initializeChart() {
    const ctx = document.getElementById('realtime-chart').getContext('2d');
    
    realtimeChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: 'Temperature (°C)',
                data: [],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'pH',
                data: [],
                borderColor: '#17a2b8',
                backgroundColor: 'rgba(23, 162, 184, 0.1)',
                tension: 0.4,
                fill: false
            }, {
                label: 'Oxygen (mg/L)',
                data: [],
                borderColor: '#ffc107',
                backgroundColor: 'rgba(255, 193, 7, 0.1)',
                tension: 0.4,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
                duration: 750,
                easing: 'easeInOutQuart'
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Time'
                    }
                },
                y: {
                    display: true,
                    title: {
                        display: true,
                        text: 'Value'
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            }
        }
    });
}

function updateRealtimeChart(readings) {
    if (!realtimeChart || !readings.length) return;
    
    // Keep only last 20 data points
    const maxPoints = 20;
    
    readings.slice(-maxPoints).forEach(reading => {
        const time = new Date(reading.timestamp).toLocaleTimeString();
        
        realtimeChart.data.labels.push(time);
        realtimeChart.data.datasets[0].data.push(reading.temperature);
        realtimeChart.data.datasets[1].data.push(reading.ph);
        realtimeChart.data.datasets[2].data.push(reading.oxygen);
    });
    
    // Remove old data points
    if (realtimeChart.data.labels.length > maxPoints) {
        const excess = realtimeChart.data.labels.length - maxPoints;
        realtimeChart.data.labels.splice(0, excess);
        realtimeChart.data.datasets.forEach(dataset => {
            dataset.data.splice(0, excess);
        });
    }
    
    realtimeChart.update('none'); // No animation for real-time updates
}

function addActivityItem(type, data) {
    const feed = document.getElementById('activity-feed');
    const item = document.createElement('div');
    item.className = 'activity-item';
    
    let iconClass, title, description;
    
    switch(type) {
        case 'reading':
            iconClass = 'reading';
            title = `Water Quality - ${data.pond_name}`;
            description = `Temp: ${data.temperature}°C, pH: ${data.ph}, O2: ${data.oxygen}mg/L`;
            break;
        case 'prediction':
            iconClass = 'prediction';
            title = `AI Prediction - ${data.pond_name}`;
            description = `${data.type}: ${data.value} (${(data.confidence * 100).toFixed(1)}% confidence)`;
            break;
        case 'alert':
            iconClass = 'alert';
            title = data.title || 'System Alert';
            description = data.message || 'Alert triggered';
            break;
    }
    
    const time = new Date(data.timestamp).toLocaleTimeString();
    
    item.innerHTML = `
        <div class="activity-icon ${iconClass}">
            <i class="fas fa-${type === 'reading' ? 'thermometer-half' : type === 'prediction' ? 'brain' : 'exclamation-triangle'}"></i>
        </div>
        <div class="activity-content">
            <div class="activity-title">${title}</div>
            <div class="activity-description">${description}</div>
            <div class="activity-time">${time}</div>
        </div>
    `;
    
    // Add to top of feed
    feed.insertBefore(item, feed.firstChild);
    
    // Remove old items (keep last 10)
    while (feed.children.length > 10) {
        feed.removeChild(feed.lastChild);
    }
    
    // Animate new item
    item.style.opacity = '0';
    item.style.transform = 'translateX(-20px)';
    setTimeout(() => {
        item.style.transition = 'all 0.3s ease';
        item.style.opacity = '1';
        item.style.transform = 'translateX(0)';
    }, 100);
}

function requestUpdate() {
    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
            type: 'request_update'
        }));
        console.log('📤 Update requested');
    }
}

function toggleAutoUpdate() {
    autoUpdate = !autoUpdate;
    const icon = document.getElementById('auto-update-icon');
    const text = document.getElementById('auto-update-text');
    
    if (autoUpdate) {
        icon.className = 'fas fa-pause';
        text.textContent = 'Pause Updates';
        if (!socket || socket.readyState === WebSocket.CLOSED) {
            initializeWebSocket();
        }
    } else {
        icon.className = 'fas fa-play';
        text.textContent = 'Resume Updates';
        if (socket) {
            socket.close();
        }
    }
}

// Send periodic ping to keep connection alive
setInterval(() => {
    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
            type: 'ping'
        }));
    }
}, 30000); // Every 30 seconds
</script>

</body>
</html>
