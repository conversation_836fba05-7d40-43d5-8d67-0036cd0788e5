<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #333;
        }
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    
    <div class="info">
        <strong>API Key Status:</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}<br>
        <strong>Map should load below:</strong>
    </div>
    
    <div id="map"></div>
    
    <div class="info">
        <strong>Debug:</strong>
        <div id="debug-info">Loading map...</div>
    </div>

    <script>
        let map;
        
        function initMap() {
            console.log('initMap called');
            document.getElementById('debug-info').innerHTML = 'initMap function called';
            
            try {
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 13,
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai
                    mapTypeId: google.maps.MapTypeId.ROADMAP
                });
                
                // Add a simple marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location'
                });
                
                document.getElementById('debug-info').innerHTML = 'Map loaded successfully!';
                console.log('Map created successfully');
            } catch (error) {
                console.error('Error creating map:', error);
                document.getElementById('debug-info').innerHTML = 'Error: ' + error.message;
            }
        }
        
        function handleMapError() {
            console.error('Google Maps API failed to load');
            document.getElementById('debug-info').innerHTML = 'Google Maps API failed to load. Check API key.';
        }
        
        // Check if Google Maps API is available
        window.addEventListener('load', function() {
            setTimeout(function() {
                if (typeof google === 'undefined') {
                    handleMapError();
                }
            }, 5000);
        });
    </script>
    
    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap&onerror=handleMapError">
    </script>
</body>
</html>
