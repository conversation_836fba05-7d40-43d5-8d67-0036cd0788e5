{% extends 'base.html' %}
{% load static %}

{% block title %}Aerator Management - {{ pond.name }}{% endblock %}

{% block extra_css %}
<style>
    .aerator-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        border-radius: 20px;
        padding: 40px;
        color: white;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    }

    .aerator-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border-left: 5px solid #667eea;
        transition: all 0.3s ease;
    }

    .aerator-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    .status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9em;
    }

    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #f8d7da; color: #721c24; }
    .status-maintenance { background: #fff3cd; color: #856404; }
    .status-faulty { background: #f5c6cb; color: #721c24; }

    .power-meter {
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        border-radius: 10px;
        padding: 15px;
        text-align: center;
        margin: 10px 0;
    }

    .control-panel {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }

    .aerator-type-icon {
        font-size: 2em;
        margin-bottom: 10px;
        display: block;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
    }

    .stat-card {
        background: linear-gradient(135deg, #74b9ff, #0984e3);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }

    .efficiency-bar {
        background: #e9ecef;
        border-radius: 10px;
        height: 20px;
        overflow: hidden;
        margin: 10px 0;
    }

    .efficiency-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745, #20c997);
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="aerator-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1><i class="fas fa-fan"></i> Aerator Management</h1>
                <p class="mb-2">{{ pond.name }} - Real-time Aerator Control & Monitoring</p>
                <div class="d-flex gap-3">
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-water"></i> {{ pond.size }} m²
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-fan"></i> {{ aerators.count }} Aerators
                    </span>
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-bolt"></i> {{ total_power }}kW Total Power
                    </span>
                </div>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'ponds:aerator_map' pond.pk %}" class="btn btn-info me-2">
                    <i class="fas fa-map-marked-alt"></i> Map View
                </a>
                <a href="{% url 'ponds:add_aerator' pond.pk %}" class="btn btn-success me-2">
                    <i class="fas fa-plus"></i> Add Aerator
                </a>
                <a href="{% url 'ponds:pond_detail' pond.pk %}" class="btn btn-light me-2">
                    <i class="fas fa-arrow-left"></i> Back to Pond
                </a>
                <a href="#" class="btn btn-warning" onclick="emergencyStopAll()">
                    <i class="fas fa-stop"></i> Emergency Stop
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Overview -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>{{ active_aerators }}</h3>
            <p>Active Aerators</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_power }}kW</h3>
            <p>Total Power</p>
        </div>
        <div class="stat-card">
            <h3>{{ avg_efficiency }}%</h3>
            <p>Avg Efficiency</p>
        </div>
        <div class="stat-card">
            <h3>{{ total_hours }}h</h3>
            <p>Total Runtime</p>
        </div>
    </div>

    <!-- Aerator List -->
    <div class="row">
        {% for aerator in aerators %}
        <div class="col-lg-6 col-xl-4">
            <div class="aerator-card">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h5>
                            {% if aerator.aerator_type == 'paddle_wheel' %}
                                <i class="fas fa-fan aerator-type-icon"></i>
                            {% elif aerator.aerator_type == 'fountain' %}
                                <i class="fas fa-fountain aerator-type-icon"></i>
                            {% elif aerator.aerator_type == 'diffuser' %}
                                <i class="fas fa-circle-dot aerator-type-icon"></i>
                            {% else %}
                                <i class="fas fa-cog aerator-type-icon"></i>
                            {% endif %}
                            {{ aerator.name }}
                        </h5>
                        <p class="text-muted mb-2">{{ aerator.get_aerator_type_display }}</p>
                    </div>
                    <span class="status-badge status-{{ aerator.status }}">
                        {{ aerator.get_status_display }}
                    </span>
                </div>

                <!-- Power Information -->
                <div class="power-meter">
                    <div class="row text-center">
                        <div class="col-4">
                            <div style="font-size: 1.2em; font-weight: bold;">
                                {{ aerator.power_rating|default:"N/A" }}
                            </div>
                            <small>Power (HP)</small>
                        </div>
                        <div class="col-4">
                            <div style="font-size: 1.2em; font-weight: bold;">
                                {{ aerator.power_consumption|default:"0" }}
                            </div>
                            <small>Current (kW)</small>
                        </div>
                        <div class="col-4">
                            <div style="font-size: 1.2em; font-weight: bold;">
                                {{ aerator.operating_hours|floatformat:1 }}
                            </div>
                            <small>Hours</small>
                        </div>
                    </div>
                </div>

                <!-- Efficiency Bar -->
                {% if aerator.efficiency %}
                <div class="efficiency-bar">
                    <div class="efficiency-fill" style="width: {{ aerator.efficiency }}%"></div>
                </div>
                <small class="text-muted">Efficiency: {{ aerator.efficiency }}%</small>
                {% endif %}

                <!-- Device Information -->
                <div class="mt-3">
                    <div class="row">
                        <div class="col-6">
                            <small class="text-muted">Power Source:</small><br>
                            <strong>{{ aerator.get_power_source_display }}</strong>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Location:</small><br>
                            <strong>{{ aerator.latitude|floatformat:4 }}, {{ aerator.longitude|floatformat:4 }}</strong>
                        </div>
                    </div>
                </div>

                <!-- Control Panel -->
                <div class="control-panel">
                    <div class="d-flex justify-content-between align-items-center">
                        <span><strong>Controls:</strong></span>
                        <div>
                            {% if aerator.is_automated %}
                                <span class="badge bg-info me-2">
                                    <i class="fas fa-robot"></i> Automated
                                </span>
                            {% endif %}
                            <a href="{% url 'ponds:edit_aerator' pond.pk aerator.pk %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                    <div class="btn-group w-100 mt-2" role="group">
                        <button type="button" class="btn btn-success btn-sm"
                                onclick="controlAerator('{{ aerator.device_id }}', 'on')"
                                {% if aerator.status == 'active' %}disabled{% endif %}>
                            <i class="fas fa-play"></i> Start
                        </button>
                        <button type="button" class="btn btn-danger btn-sm"
                                onclick="controlAerator('{{ aerator.device_id }}', 'off')"
                                {% if aerator.status == 'inactive' %}disabled{% endif %}>
                            <i class="fas fa-stop"></i> Stop
                        </button>
                        <button type="button" class="btn btn-warning btn-sm"
                                onclick="controlAerator('{{ aerator.device_id }}', 'trip')">
                            <i class="fas fa-exclamation-triangle"></i> Trip
                        </button>
                    </div>
                </div>

                <!-- Maintenance Info -->
                {% if aerator.needs_maintenance %}
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-wrench"></i> Maintenance Required
                    <br><small>{{ aerator.operating_hours }} hours runtime</small>
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert alert-info text-center">
                <h4><i class="fas fa-info-circle"></i> No Aerators Found</h4>
                <p>This pond doesn't have any aerators configured yet.</p>
                <a href="{% url 'ponds:add_aerator' pond.pk %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add Aerator
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-success" onclick="startAllAerators()">
                            <i class="fas fa-play"></i> Start All
                        </button>
                        <button type="button" class="btn btn-danger" onclick="stopAllAerators()">
                            <i class="fas fa-stop"></i> Stop All
                        </button>
                    </div>
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-info" onclick="refreshData()">
                            <i class="fas fa-sync"></i> Refresh Data
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="exportReport()">
                            <i class="fas fa-download"></i> Export Report
                        </button>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="{% url 'iot_integration:realtime_dashboard' %}" class="btn btn-primary">
                            <i class="fas fa-tachometer-alt"></i> IoT Dashboard
                        </a>
                        <a href="{% url 'iot_integration:power_analytics' %}" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> Analytics
                        </a>
                        <a href="/monitoring/realtime/" class="btn btn-success">
                            <i class="fas fa-desktop"></i> Monitoring
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Control functions
function controlAerator(aeratorId, action) {
    if (!aeratorId) {
        alert('Aerator ID not found');
        return;
    }

    console.log(`Controlling aerator ${aeratorId}: ${action}`);

    fetch(`/iot/api/aerator/${aeratorId}/${action}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', `Aerator ${aeratorId} ${action} command sent successfully`);
            setTimeout(() => location.reload(), 1000);
        } else {
            showAlert('error', `Failed to ${action} aerator ${aeratorId}`);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'Failed to communicate with device');
    });
}

function emergencyStopAll() {
    if (confirm('Are you sure you want to emergency stop ALL aerators?')) {
        fetch('/iot/api/emergency-stop/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('warning', 'Emergency stop activated for all aerators');
                setTimeout(() => location.reload(), 1000);
            } else {
                showAlert('error', 'Failed to execute emergency stop');
            }
        });
    }
}

function startAllAerators() {
    if (confirm('Start all aerators in this pond?')) {
        // Implementation for starting all aerators
        showAlert('info', 'Starting all aerators...');
    }
}

function stopAllAerators() {
    if (confirm('Stop all aerators in this pond?')) {
        // Implementation for stopping all aerators
        showAlert('info', 'Stopping all aerators...');
    }
}

function refreshData() {
    location.reload();
}

function exportReport() {
    showAlert('info', 'Generating aerator report...');
    // Implementation for exporting report
}

function showAlert(type, message) {
    // Simple alert implementation
    const alertClass = type === 'success' ? 'alert-success' : 
                      type === 'error' ? 'alert-danger' : 
                      type === 'warning' ? 'alert-warning' : 'alert-info';
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', alertHtml);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) alert.remove();
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
