from django.urls import path
from . import views
from .direct_access import direct_labor_dashboard
from .standalone import standalone_dashboard
from .views_simple import simple_dashboard
from .views_basic import basic_dashboard
from .enhanced_urls import enhanced_urlpatterns

app_name = 'labor'

urlpatterns = [
    # Root URL - main labor landing page
    path('', views.labor_main, name='labor_root'),
    # Test URLs
    path('test/', views.test_view, name='test_view'),
    path('test-maps/', views.test_maps_view, name='test_maps_view'),
    path('basic-dashboard/', basic_dashboard, name='basic_dashboard'),
    # Dashboard
    path('dashboard/', views.dashboard, name='dashboard'),
    path('overview/', views.overview, name='overview'),
    path('attendance/', views.attendance_tracking, name='attendance_tracking'),
    path('agents/', views.agent_list, name='agent_list'),
    path('agents/create/', views.agent_create, name='agent_create'),
    path('teams/management/', views.team_management, name='team_management'),
    path('direct-dashboard/', direct_labor_dashboard, name='direct_dashboard'),
    path('standalone-dashboard/', standalone_dashboard, name='standalone_dashboard'),
    path('simple-dashboard/', simple_dashboard, name='simple_dashboard'),

    # Team URLs
    path('teams/', views.TeamListView.as_view(), name='team_list'),
    path('teams/<int:pk>/', views.TeamDetailView.as_view(), name='team_detail'),
    path('teams/create/', views.TeamCreateView.as_view(), name='team_create'),
    path('teams/<int:pk>/update/', views.TeamUpdateView.as_view(), name='team_update'),
    path('teams/<int:pk>/delete/', views.TeamDeleteView.as_view(), name='team_delete'),

    # Worker URLs
    path('workers/', views.WorkerListView.as_view(), name='worker_list'),
    path('workers/<int:pk>/', views.WorkerDetailView.as_view(), name='worker_detail'),
    path('workers/create/', views.WorkerCreateView.as_view(), name='worker_create'),
    path('workers/<int:pk>/update/', views.WorkerUpdateView.as_view(), name='worker_update'),
    path('workers/<int:pk>/delete/', views.WorkerDeleteView.as_view(), name='worker_delete'),

    # Task URLs
    path('tasks/', views.TaskListView.as_view(), name='task_list'),
    path('tasks/<int:pk>/', views.TaskDetailView.as_view(), name='task_detail'),
    path('tasks/create/', views.TaskCreateView.as_view(), name='task_create'),
    path('tasks/<int:pk>/update/', views.TaskUpdateView.as_view(), name='task_update'),
    path('tasks/<int:pk>/delete/', views.TaskDeleteView.as_view(), name='task_delete'),
    path('tasks/<int:pk>/start/', views.start_task, name='start_task'),
    path('tasks/<int:pk>/complete/', views.complete_task, name='complete_task'),

    # WorkLog URLs
    path('work-logs/', views.WorkLogListView.as_view(), name='work_log_list'),
    path('work-logs/create/', views.WorkLogCreateView.as_view(), name='work_log_create'),
    path('work-logs/<int:pk>/update/', views.WorkLogUpdateView.as_view(), name='work_log_update'),
    path('work-logs/<int:pk>/delete/', views.WorkLogDeleteView.as_view(), name='work_log_delete'),

    # Schedule URLs
    path('schedules/', views.ScheduleListView.as_view(), name='schedule_list'),
    path('schedules/<int:pk>/', views.ScheduleDetailView.as_view(), name='schedule_detail'),
    path('schedules/create/', views.ScheduleCreateView.as_view(), name='schedule_create'),
    path('schedules/<int:pk>/update/', views.ScheduleUpdateView.as_view(), name='schedule_update'),
    path('schedules/<int:pk>/delete/', views.ScheduleDeleteView.as_view(), name='schedule_delete'),

    # Shift URLs
    path('shifts/create/', views.ShiftCreateView.as_view(), name='shift_create'),
    path('shifts/create/<int:schedule_id>/', views.ShiftCreateView.as_view(), name='shift_create_for_schedule'),
    path('shifts/<int:pk>/update/', views.ShiftUpdateView.as_view(), name='shift_update'),
    path('shifts/<int:pk>/delete/', views.ShiftDeleteView.as_view(), name='shift_delete'),

    # Calendar view
    path('calendar/', views.calendar_view, name='calendar'),

    # Geofence URLs
    path('geofences/', views.GeofenceListView.as_view(), name='geofence_list'),
    path('geofences/<int:pk>/', views.GeofenceDetailView.as_view(), name='geofence_detail'),
    path('geofences/create/', views.GeofenceCreateView.as_view(), name='geofence_create'),
    path('geofences/<int:pk>/update/', views.GeofenceUpdateView.as_view(), name='geofence_update'),
    path('geofences/<int:pk>/delete/', views.GeofenceDeleteView.as_view(), name='geofence_delete'),
    path('geofences/create-from-pond/<int:pond_id>/', views.create_geofence_from_pond, name='create_geofence_from_pond'),

    # Location Tracking URLs
    path('location-tracking/', views.location_tracking_dashboard, name='location_tracking_dashboard'),
    path('workers/<int:worker_id>/update-location/', views.update_worker_location, name='update_worker_location'),
    path('workers/<int:worker_id>/location-history/', views.worker_location_history, name='worker_location_history'),
    path('workers/<int:worker_id>/export-location-history/', views.export_location_history, name='export_location_history'),

    # Heat Map URLs
    path('heat-map/', views.heat_map_view, name='heat_map'),
    path('heat-map/advanced/', views.heat_map_view_advanced, name='heat_map_advanced'),
    path('heat-map/generate/', views.generate_heat_map_data, name='generate_heat_map_data'),
    path('heat-map/generate/advanced/', views.generate_heat_map_data_advanced, name='generate_heat_map_data_advanced'),
    path('google-maps-test/', views.google_maps_test_view, name='google_maps_test'),
    path('google-maps-standalone-test/', views.google_maps_standalone_test_view, name='google_maps_standalone_test'),
    path('heat-map/standalone/', views.heat_map_standalone, name='heat_map_standalone'),

    # Weather Forecast URLs
    path('workers/<int:worker_id>/weather-forecast/', views.worker_weather_forecast, name='worker_weather_forecast'),

    # Safety URLs
    path('safety/dashboard/', views.safety_dashboard, name='safety_dashboard'),
    path('safety/alerts/', views.SafetyAlertListView.as_view(), name='safety_alerts'),
    path('safety/alerts/<int:pk>/', views.SafetyAlertDetailView.as_view(), name='safety_alert_detail'),
    path('safety/alerts/<int:pk>/acknowledge/', views.acknowledge_safety_alert, name='safety_alert_acknowledge'),
    path('safety/alerts/<int:pk>/in-progress/', views.mark_safety_alert_in_progress, name='safety_alert_in_progress'),
    path('safety/alerts/<int:pk>/resolve/', views.resolve_safety_alert, name='safety_alert_resolve'),
    path('safety/alerts/<int:pk>/false-alarm/', views.mark_safety_alert_false_alarm, name='safety_alert_false_alarm'),
    path('workers/<int:worker_id>/panic/', views.trigger_panic_alert, name='trigger_panic_alert'),
    path('workers/<int:worker_id>/man-down/', views.trigger_man_down_alert, name='trigger_man_down_alert'),
    path('safety/run-checks/', views.run_safety_checks, name='run_safety_checks'),
    path('safety/calculate-score/', views.calculate_safety_score, name='calculate_safety_score'),

    # Mobile API URLs
    path('api/workers/<int:worker_id>/update-location/', views.update_worker_location_api, name='update_worker_location_api'),
    path('api/workers/<int:worker_id>/panic/', views.trigger_panic_alert_api, name='trigger_panic_alert_api'),
    path('api/workers/<int:worker_id>/update-weather/', views.update_weather_data_api, name='update_weather_data_api'),
    path('api/workers/<int:worker_id>/tasks/', views.get_worker_tasks_api, name='get_worker_tasks_api'),
    path('api/workers/<int:worker_id>/safety-alerts/', views.get_worker_safety_alerts_api, name='get_worker_safety_alerts_api'),
    path('api/tasks/update-status/', views.update_task_status_api, name='update_task_status_api'),

    # Enhanced Dashboard URLs
    path('productivity-analytics/', views.productivity_analytics_dashboard, name='productivity_analytics'),
    path('geofence-alerts/', views.geofence_alerts_dashboard, name='geofence_alerts_dashboard'),
    path('api/geofence-alert-webhook/', views.geofence_alert_webhook, name='geofence_alert_webhook'),
    path('api/geofences/', views.get_geofences_api, name='get_geofences_api'),

    # Equipment URLs
    path('equipment/', views.equipment_dashboard, name='equipment_dashboard'),
    path('equipment/list/', views.EquipmentListView.as_view(), name='equipment_list'),
    path('equipment/<int:pk>/', views.EquipmentDetailView.as_view(), name='equipment_detail'),
    path('equipment/create/', views.EquipmentCreateView.as_view(), name='equipment_create'),
    path('equipment/<int:pk>/update/', views.EquipmentUpdateView.as_view(), name='equipment_update'),
    path('equipment/<int:pk>/delete/', views.EquipmentDeleteView.as_view(), name='equipment_delete'),
    path('equipment/<int:pk>/assign/', views.assign_equipment, name='equipment_assign'),
    path('equipment/<int:pk>/return/', views.return_equipment, name='equipment_return'),
    path('equipment/maintenance/', views.equipment_maintenance, name='equipment_maintenance'),
    path('equipment/maintenance/<int:pk>/complete/', views.complete_maintenance, name='complete_maintenance'),
    path('equipment/maintenance/schedule/', views.schedule_maintenance, name='schedule_maintenance'),
    path('equipment/issues/', views.equipment_issues, name='equipment_issues'),
    path('equipment/issues/<int:pk>/', views.equipment_issue_detail, name='equipment_issue_detail'),
    path('equipment/<int:pk>/report-issue/', views.report_equipment_issue, name='equipment_issue_report'),
    path('equipment/issues/<int:pk>/resolve/', views.resolve_equipment_issue, name='resolve_equipment_issue'),
    path('api/equipment/<int:equipment_id>/update-location/', views.update_equipment_location_api, name='update_equipment_location_api'),

    # Include enhanced URLs
] + enhanced_urlpatterns