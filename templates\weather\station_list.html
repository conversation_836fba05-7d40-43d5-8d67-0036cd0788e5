{% extends 'layouts/list_base.html' %}
{% load static %}

{% block list_title %}Weather Stations{% endblock %}

{% block list_header %}Weather Stations{% endblock %}
{% block list_header_icon %}<i class="fas fa-broadcast-tower text-primary me-2"></i>{% endblock %}

{% block list_header_actions %}
<a href="{% url 'weather:station_create' %}" class="btn btn-primary me-2">
    <i class="fas fa-plus me-1"></i> Add Station
</a>
<a href="{% url 'weather:weather_dashboard' %}" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
</a>
{% endblock %}

{% block list_card_title %}All Weather Stations{% endblock %}

{% block list_content %}
{% if stations %}
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th>Name</th>
                <th>Location</th>
                <th>Coordinates</th>
                <th>Elevation</th>
                <th>Status</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for station in stations %}
            <tr>
                <td>{{ station.name }}</td>
                <td>{{ station.location }}</td>
                <td>{{ station.latitude|floatformat:4 }}, {{ station.longitude|floatformat:4 }}</td>
                <td>{{ station.elevation|floatformat:0 }} m</td>
                <td>
                    <span class="badge {% if station.is_active %}bg-success{% else %}bg-secondary{% endif %}">
                        {{ station.is_active|yesno:"Active,Inactive" }}
                    </span>
                </td>
                <td>
                    <a href="{% url 'weather:station_detail' station.id %}" class="btn btn-sm btn-outline-primary me-1">
                        <i class="fas fa-eye"></i>
                    </a>
                    <a href="{% url 'weather:station_update' station.id %}" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="fas fa-edit"></i>
                    </a>
                    <a href="{% url 'weather:data_create' %}?station_id={{ station.id }}" class="btn btn-sm btn-outline-success">
                        <i class="fas fa-plus"></i> Add Data
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="text-center py-4">
    <i class="fas fa-broadcast-tower text-muted mb-3" style="font-size: 3rem;"></i>
    <h5>No Weather Stations Found</h5>
    <p class="text-muted">You haven't added any weather stations yet.</p>
    <a href="{% url 'weather:station_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i> Add First Station
    </a>
</div>
{% endif %}
{% endblock %}
