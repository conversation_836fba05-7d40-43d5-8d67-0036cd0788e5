{% extends 'website/base.html' %}

{% block content %}
<!-- Demo Hero Section -->
<section class="py-5 bg-gradient-hero text-white">
    <div class="container">
        <div class="row align-items-center" style="min-height: 50vh;">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4" data-aos="fade-up">
                    Live Demo
                </h1>
                <p class="lead mb-4" data-aos="fade-up" data-aos-delay="100">
                    Experience the power of our platform with an interactive demonstration
                </p>
                <a href="/app/" class="btn btn-light btn-lg" data-aos="fade-up" data-aos-delay="200">
                    <i class="fas fa-rocket me-2"></i>Launch Dashboard
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Demo Features Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-4 fw-bold" data-aos="fade-up">
                    What You'll See in the Demo
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Explore all the key features of our aquaculture management platform
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <h5>Real-time Dashboard</h5>
                    <p class="text-muted">
                        Live monitoring of water quality, equipment status, and farm operations with interactive charts and alerts.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h5>Interactive Maps</h5>
                    <p class="text-muted">
                        GPS-enabled pond mapping with sensor locations, aerator controls, and worker tracking visualization.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h5>AI Disease Detection</h5>
                    <p class="text-muted">
                        Computer vision analysis of shrimp health with automated disease identification and treatment recommendations.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-utensils"></i>
                    </div>
                    <h5>Feed Management</h5>
                    <p class="text-muted">
                        Automated feeding schedules, inventory tracking, and cost optimization with growth performance analytics.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i class="fas fa-bell"></i>
                    </div>
                    <h5>Smart Alerts</h5>
                    <p class="text-muted">
                        Intelligent notification system with customizable thresholds and multi-channel alert delivery.
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6">
                <div class="feature-card text-center" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-icon mx-auto mb-3" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h5>Analytics & Reports</h5>
                    <p class="text-muted">
                        Comprehensive reporting with trend analysis, predictive insights, and performance benchmarking.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo Video Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="display-4 fw-bold mb-4" data-aos="fade-up">
                    Watch Our Platform in Action
                </h2>
                <p class="lead text-muted mb-5" data-aos="fade-up" data-aos-delay="100">
                    See how our system transforms traditional aquaculture operations
                </p>
                
                <div class="feature-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="video-placeholder" style="height: 400px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 15px; display: flex; align-items: center; justify-content: center; color: white; position: relative; overflow: hidden;">
                        <div class="text-center">
                            <div class="play-button" style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; cursor: pointer; transition: all 0.3s ease;">
                                <i class="fas fa-play" style="font-size: 2rem; margin-left: 5px;"></i>
                            </div>
                            <h4>Product Demo Video</h4>
                            <p class="mb-0">5-minute overview of key features</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo Steps Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-4 fw-bold" data-aos="fade-up">
                    How the Demo Works
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    Follow these simple steps to explore our platform
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="text-center" data-aos="fade-up">
                    <div class="demo-step mb-3">
                        <div class="step-number mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; font-weight: bold;">
                            1
                        </div>
                        <h5>Access Dashboard</h5>
                        <p class="text-muted">
                            Click the launch button to access our live demo dashboard with sample data.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center" data-aos="fade-up" data-aos-delay="100">
                    <div class="demo-step mb-3">
                        <div class="step-number mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #10b981, #059669); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; font-weight: bold;">
                            2
                        </div>
                        <h5>Explore Features</h5>
                        <p class="text-muted">
                            Navigate through different modules and experience the full functionality.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center" data-aos="fade-up" data-aos-delay="200">
                    <div class="demo-step mb-3">
                        <div class="step-number mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; font-weight: bold;">
                            3
                        </div>
                        <h5>Test Interactions</h5>
                        <p class="text-muted">
                            Try out controls, view reports, and see how the system responds to your actions.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="text-center" data-aos="fade-up" data-aos-delay="300">
                    <div class="demo-step mb-3">
                        <div class="step-number mx-auto mb-3" style="width: 60px; height: 60px; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; font-weight: bold;">
                            4
                        </div>
                        <h5>Schedule Meeting</h5>
                        <p class="text-muted">
                            Ready to implement? Contact our team to discuss your specific requirements.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo CTA Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div data-aos="fade-right">
                    <h2 class="display-5 fw-bold mb-3">
                        Ready to See It Live?
                    </h2>
                    <p class="lead mb-0">
                        Experience the future of aquaculture management with our interactive demo
                    </p>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div data-aos="fade-left">
                    <a href="/app/" class="btn btn-light btn-lg me-3">
                        <i class="fas fa-rocket me-2"></i>Launch Demo
                    </a>
                    <a href="{% url 'website:contact' %}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-calendar me-2"></i>Schedule Meeting
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-12 text-center mb-5">
                <h2 class="display-4 fw-bold" data-aos="fade-up">
                    Why Try Our Demo?
                </h2>
                <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                    See the tangible benefits of our platform
                </p>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="d-flex align-items-start" data-aos="fade-up">
                    <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #667eea, #764ba2);">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div>
                        <h5>Visual Learning</h5>
                        <p class="text-muted">
                            See exactly how our features work in real aquaculture scenarios with sample data that mirrors your operations.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="d-flex align-items-start" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h5>No Time Commitment</h5>
                        <p class="text-muted">
                            Explore at your own pace without pressure. The demo is available 24/7 and requires no registration.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="d-flex align-items-start" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-hands-helping"></i>
                    </div>
                    <div>
                        <h5>Hands-on Experience</h5>
                        <p class="text-muted">
                            Interactive demo allows you to click, navigate, and test features just like in the real application.
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="d-flex align-items-start" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-icon me-3" style="width: 50px; height: 50px; font-size: 1.2rem; background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <div>
                        <h5>Expert Support</h5>
                        <p class="text-muted">
                            Have questions during the demo? Our team is ready to provide personalized guidance and answer your queries.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Demo video interaction
document.querySelector('.video-placeholder').addEventListener('click', function() {
    // In a real implementation, this would launch a video modal or redirect to video
    alert('Demo video would play here. In the actual implementation, this would show a product demonstration video.');
});

// Play button hover effect
document.querySelector('.play-button').addEventListener('mouseenter', function() {
    this.style.background = 'rgba(255,255,255,0.3)';
    this.style.transform = 'scale(1.1)';
});

document.querySelector('.play-button').addEventListener('mouseleave', function() {
    this.style.background = 'rgba(255,255,255,0.2)';
    this.style.transform = 'scale(1)';
});
</script>
{% endblock %}
