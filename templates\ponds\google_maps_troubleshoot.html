{% extends "base.html" %}
{% load static %}

{% block title %}Google Maps Troubleshooting{% endblock %}

{% block extra_css %}
<style>
    .troubleshoot-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .test-section {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        margin: 20px 0;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .test-header {
        background: #f8f9fa;
        padding: 10px 15px;
        margin: -20px -20px 15px -20px;
        border-bottom: 1px solid #ddd;
        border-radius: 7px 7px 0 0;
        font-weight: bold;
    }
    
    .map-container {
        height: 300px;
        width: 100%;
        border: 2px solid #ddd;
        border-radius: 5px;
        margin: 10px 0;
        background: #f5f5f5;
    }
    
    .status-indicator {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
    }
    
    .status-pending { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    .status-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .status-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .status-warning { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    
    .console-output {
        background: #263238;
        color: #eeffff;
        padding: 15px;
        border-radius: 5px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        margin: 10px 0;
    }
    
    .api-details {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
    }
    
    .test-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
    }
    
    .test-button:hover {
        background: #0056b3;
    }
    
    .error { color: #dc3545; }
    .success { color: #28a745; }
    .warning { color: #ffc107; }
    .info { color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="troubleshoot-container">
    <h1>🔧 Google Maps Troubleshooting Dashboard</h1>
    <p class="lead">Comprehensive testing tool to diagnose Google Maps loading issues</p>
    
    <!-- API Key Information -->
    <div class="test-section">
        <div class="test-header">🔑 API Key Information</div>
        <div class="api-details">
            <div><strong>API Key (masked):</strong> {{ google_maps_api_key|slice:":10" }}...{{ google_maps_api_key|slice:"-5:" }}</div>
            <div><strong>Key Length:</strong> {{ google_maps_api_key|length }} characters</div>
            <div><strong>API URL:</strong> https://maps.googleapis.com/maps/api/js</div>
            <div><strong>Libraries:</strong> geometry, places</div>
        </div>
        <button class="test-button" onclick="testApiKey()">Test API Key</button>
        <div id="api-key-status" class="status-indicator status-pending">⏳ Ready to test API key...</div>
    </div>
    
    <!-- Script Loading Test -->
    <div class="test-section">
        <div class="test-header">📡 Script Loading Test</div>
        <div id="script-status" class="status-indicator status-pending">⏳ Ready to test script loading...</div>
        <button class="test-button" onclick="testScriptLoading()">Test Script Loading</button>
        <div class="console-output" id="script-console">Waiting for script loading test...</div>
    </div>
    
    <!-- Basic Map Test -->
    <div class="test-section">
        <div class="test-header">🗺️ Basic Map Test</div>
        <div id="basic-map-status" class="status-indicator status-pending">⏳ Ready to test basic map...</div>
        <button class="test-button" onclick="testBasicMap()">Test Basic Map</button>
        <div id="basic-map" class="map-container"></div>
        <div class="console-output" id="basic-console">Waiting for basic map test...</div>
    </div>
    
    <!-- Advanced Map Test -->
    <div class="test-section">
        <div class="test-header">🎯 Advanced Map Test</div>
        <div id="advanced-map-status" class="status-indicator status-pending">⏳ Ready to test advanced map...</div>
        <button class="test-button" onclick="testAdvancedMap()">Test Advanced Map</button>
        <div id="advanced-map" class="map-container"></div>
        <div class="console-output" id="advanced-console">Waiting for advanced map test...</div>
    </div>
    
    <!-- Network Test -->
    <div class="test-section">
        <div class="test-header">🌐 Network Test</div>
        <div id="network-status" class="status-indicator status-pending">⏳ Ready to test network...</div>
        <button class="test-button" onclick="testNetwork()">Test Network</button>
        <div class="console-output" id="network-console">Waiting for network test...</div>
    </div>
    
    <!-- Console Monitor -->
    <div class="test-section">
        <div class="test-header">📊 Live Console Monitor</div>
        <div class="console-output" id="live-console">Console monitor active...</div>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global variables for testing
    let testMap;
    let apiKey = '{{ google_maps_api_key|escapejs }}';
    let testResults = {};
    
    // Console logging functions
    function logToConsole(containerId, message, type = 'info') {
        const container = document.getElementById(containerId);
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            error: '#ff5252',
            success: '#4caf50',
            warning: '#ff9800',
            info: '#2196f3'
        };
        
        const entry = `<div style="color: ${colors[type]}; margin: 2px 0;">[${timestamp}] ${message}</div>`;
        container.innerHTML += entry;
        container.scrollTop = container.scrollHeight;
        
        // Also log to browser console
        console[type](message);
        
        // Log to live monitor
        logToLiveConsole(message, type);
    }
    
    function logToLiveConsole(message, type = 'info') {
        const liveConsole = document.getElementById('live-console');
        const timestamp = new Date().toLocaleTimeString();
        const colors = {
            error: '#ff5252',
            success: '#4caf50',
            warning: '#ff9800',
            info: '#2196f3'
        };
        
        const entry = `<div style="color: ${colors[type]}; margin: 2px 0;">[${timestamp}] ${message}</div>`;
        liveConsole.innerHTML += entry;
        liveConsole.scrollTop = liveConsole.scrollHeight;
    }
    
    function updateStatus(statusId, message, type) {
        const statusElement = document.getElementById(statusId);
        statusElement.textContent = message;
        statusElement.className = `status-indicator status-${type}`;
    }
    
    function clearConsole() {
        document.getElementById('live-console').innerHTML = 'Console cleared...<br>';
    }
    
    // Test functions
    function testApiKey() {
        logToConsole('script-console', '🔑 Testing API key validity...', 'info');
        updateStatus('api-key-status', '🔍 Testing API key...', 'warning');
        
        // Test by making a request to Google Maps API
        const testUrl = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry&callback=handleApiKeyTest`;
        
        // Create script element to test loading
        const script = document.createElement('script');
        script.src = testUrl;
        script.async = true;
        script.defer = true;
        
        script.onload = function() {
            logToConsole('script-console', '✅ API key appears valid - script loaded', 'success');
            updateStatus('api-key-status', '✅ API key is valid', 'success');
            testResults.apiKey = true;
        };
        
        script.onerror = function() {
            logToConsole('script-console', '❌ API key test failed - script failed to load', 'error');
            updateStatus('api-key-status', '❌ API key test failed', 'error');
            testResults.apiKey = false;
        };
        
        document.head.appendChild(script);
        
        // Timeout test
        setTimeout(() => {
            if (typeof google === 'undefined') {
                logToConsole('script-console', '⏰ API key test timeout - Google Maps API not loaded', 'error');
                updateStatus('api-key-status', '⏰ API key test timeout', 'error');
                testResults.apiKey = false;
            }
        }, 10000);
    }
    
    function handleApiKeyTest() {
        logToConsole('script-console', '🎯 API key test callback triggered', 'success');
    }
    
    function testScriptLoading() {
        logToConsole('script-console', '📡 Testing Google Maps script loading...', 'info');
        updateStatus('script-status', '📡 Testing script loading...', 'warning');
        
        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            logToConsole('script-console', '✅ Google Maps API already loaded', 'success');
            logToConsole('script-console', `📦 Google Maps version: ${google.maps.version}`, 'info');
            updateStatus('script-status', '✅ Google Maps API loaded', 'success');
            testResults.scriptLoading = true;
            return;
        }
        
        // Load fresh script
        const script = document.createElement('script');
        const scriptUrl = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=geometry,places&callback=handleScriptLoadTest`;
        
        logToConsole('script-console', `🔗 Loading: ${scriptUrl}`, 'info');
        
        script.src = scriptUrl;
        script.async = true;
        script.defer = true;
        
        script.onload = function() {
            logToConsole('script-console', '✅ Script element loaded successfully', 'success');
        };
        
        script.onerror = function() {
            logToConsole('script-console', '❌ Script element failed to load', 'error');
            updateStatus('script-status', '❌ Script loading failed', 'error');
            testResults.scriptLoading = false;
        };
        
        document.head.appendChild(script);
    }
    
    function handleScriptLoadTest() {
        if (typeof google !== 'undefined' && google.maps) {
            logToConsole('script-console', '✅ Google Maps API callback triggered', 'success');
            logToConsole('script-console', `📦 Version: ${google.maps.version}`, 'info');
            logToConsole('script-console', `🧩 Available objects: ${Object.keys(google.maps).length}`, 'info');
            updateStatus('script-status', '✅ Script loaded successfully', 'success');
            testResults.scriptLoading = true;
        } else {
            logToConsole('script-console', '❌ Callback triggered but Google Maps not available', 'error');
            updateStatus('script-status', '❌ Script loading incomplete', 'error');
            testResults.scriptLoading = false;
        }
    }
    
    function testBasicMap() {
        logToConsole('basic-console', '🗺️ Testing basic map creation...', 'info');
        updateStatus('basic-map-status', '🗺️ Creating basic map...', 'warning');
        
        try {
            // Check Google Maps availability
            if (typeof google === 'undefined') {
                throw new Error('Google object not available');
            }
            
            if (!google.maps) {
                throw new Error('google.maps not available');
            }
            
            logToConsole('basic-console', '✅ Google Maps API available', 'success');
            
            // Get map container
            const mapContainer = document.getElementById('basic-map');
            if (!mapContainer) {
                throw new Error('Map container not found');
            }
            
            logToConsole('basic-console', '✅ Map container found', 'success');
            
            // Create basic map
            const mapOptions = {
                zoom: 10,
                center: { lat: 13.0827, lng: 80.2707 },
                mapTypeId: google.maps.MapTypeId.ROADMAP
            };
            
            logToConsole('basic-console', `📍 Map center: ${mapOptions.center.lat}, ${mapOptions.center.lng}`, 'info');
            
            testMap = new google.maps.Map(mapContainer, mapOptions);
            
            logToConsole('basic-console', '✅ Basic map created successfully', 'success');
            updateStatus('basic-map-status', '✅ Basic map created', 'success');
            testResults.basicMap = true;
            
            // Add map event listeners
            testMap.addListener('idle', function() {
                logToConsole('basic-console', '🎯 Map idle event - fully loaded', 'success');
            });
            
            testMap.addListener('click', function(e) {
                logToConsole('basic-console', `🖱️ Map click: ${e.latLng.lat()}, ${e.latLng.lng()}`, 'info');
            });
            
        } catch (error) {
            logToConsole('basic-console', `❌ Basic map test failed: ${error.message}`, 'error');
            updateStatus('basic-map-status', '❌ Basic map test failed', 'error');
            testResults.basicMap = false;
        }
    }
    
    function testAdvancedMap() {
        logToConsole('advanced-console', '🎯 Testing advanced map features...', 'info');
        updateStatus('advanced-map-status', '🎯 Creating advanced map...', 'warning');
        
        try {
            if (typeof google === 'undefined' || !google.maps) {
                throw new Error('Google Maps not available');
            }
            
            const mapContainer = document.getElementById('advanced-map');
            if (!mapContainer) {
                throw new Error('Advanced map container not found');
            }
            
            // Advanced map with custom styling
            const advancedMap = new google.maps.Map(mapContainer, {
                zoom: 12,
                center: { lat: 13.0827, lng: 80.2707 },
                mapTypeId: google.maps.MapTypeId.ROADMAP,
                zoomControl: true,
                mapTypeControl: true,
                scaleControl: true,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: true,
                styles: [
                    {
                        featureType: 'water',
                        elementType: 'geometry',
                        stylers: [{ color: '#193047' }]
                    },
                    {
                        featureType: 'landscape',
                        elementType: 'geometry',
                        stylers: [{ color: '#2c5234' }]
                    }
                ]
            });
            
            logToConsole('advanced-console', '✅ Advanced map created with custom styling', 'success');
            
            // Add multiple markers
            const locations = [
                { lat: 13.0827, lng: 80.2707, title: 'Chennai' },
                { lat: 13.0900, lng: 80.2800, title: 'Test Location 1' },
                { lat: 13.0750, lng: 80.2600, title: 'Test Location 2' }
            ];
            
            locations.forEach((location, index) => {
                const marker = new google.maps.Marker({
                    position: { lat: location.lat, lng: location.lng },
                    map: advancedMap,
                    title: location.title,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: index === 0 ? '#FF0000' : '#0066FF',
                        fillOpacity: 1,
                        strokeColor: '#FFFFFF',
                        strokeWeight: 2
                    }
                });
                
                // Add info window
                const infoWindow = new google.maps.InfoWindow({
                    content: `<div><strong>${location.title}</strong><br>Lat: ${location.lat}<br>Lng: ${location.lng}</div>`
                });
                
                marker.addListener('click', function() {
                    infoWindow.open(advancedMap, marker);
                    logToConsole('advanced-console', `📍 Clicked marker: ${location.title}`, 'info');
                });
                
                logToConsole('advanced-console', `✅ Added marker: ${location.title}`, 'success');
            });
            
            // Add polygon
            const triangleCoords = [
                { lat: 13.0800, lng: 80.2650 },
                { lat: 13.0850, lng: 80.2750 },
                { lat: 13.0750, lng: 80.2750 },
            ];
            
            const polygon = new google.maps.Polygon({
                paths: triangleCoords,
                strokeColor: '#FF0000',
                strokeOpacity: 0.8,
                strokeWeight: 2,
                fillColor: '#FF0000',
                fillOpacity: 0.35,
            });
            
            polygon.setMap(advancedMap);
            logToConsole('advanced-console', '✅ Added test polygon', 'success');
            
            updateStatus('advanced-map-status', '✅ Advanced map created successfully', 'success');
            testResults.advancedMap = true;
            
        } catch (error) {
            logToConsole('advanced-console', `❌ Advanced map test failed: ${error.message}`, 'error');
            updateStatus('advanced-map-status', '❌ Advanced map test failed', 'error');
            testResults.advancedMap = false;
        }
    }
    
    function testNetwork() {
        logToConsole('network-console', '🌐 Testing network connectivity...', 'info');
        updateStatus('network-status', '🌐 Testing network...', 'warning');
        
        // Test Google Maps API endpoint
        fetch(`https://maps.googleapis.com/maps/api/js?key=${apiKey}`)
            .then(response => {
                if (response.ok) {
                    logToConsole('network-console', '✅ Google Maps API endpoint reachable', 'success');
                    logToConsole('network-console', `📊 Response status: ${response.status}`, 'info');
                    updateStatus('network-status', '✅ Network connectivity OK', 'success');
                    testResults.network = true;
                } else {
                    logToConsole('network-console', `❌ API endpoint error: ${response.status}`, 'error');
                    updateStatus('network-status', '❌ Network connectivity issues', 'error');
                    testResults.network = false;
                }
            })
            .catch(error => {
                logToConsole('network-console', `❌ Network test failed: ${error.message}`, 'error');
                updateStatus('network-status', '❌ Network test failed', 'error');
                testResults.network = false;
            });
        
        // Test general internet connectivity
        fetch('https://www.google.com/favicon.ico')
            .then(response => {
                if (response.ok) {
                    logToConsole('network-console', '✅ General internet connectivity OK', 'success');
                } else {
                    logToConsole('network-console', '❌ Internet connectivity issues', 'warning');
                }
            })
            .catch(error => {
                logToConsole('network-console', `❌ Internet connectivity test failed: ${error.message}`, 'error');
            });
    }
    
    // Global error handlers
    window.addEventListener('error', function(e) {
        logToLiveConsole(`🚨 Global error: ${e.message} at ${e.filename}:${e.lineno}`, 'error');
    });
    
    window.gm_authFailure = function() {
        logToLiveConsole('🔑 Google Maps authentication failure!', 'error');
        updateStatus('api-key-status', '❌ API key authentication failed', 'error');
    };
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        logToLiveConsole('🎬 Troubleshooting dashboard initialized', 'info');
        logToLiveConsole(`🔑 Using API key: ${apiKey.substring(0, 10)}...`, 'info');
    });
</script>
{% endblock %}
