{% extends 'base.html' %}
{% load static %}

{% block title %}Pond Weather Integration Dashboard - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    .weather-header {
        background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .weather-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        animation: weather-sweep 6s infinite;
    }
    
    @keyframes weather-sweep {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }
    
    .pond-weather-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        transition: all 0.4s ease;
        border: 2px solid transparent;
    }
    
    .pond-weather-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.2);
        border-color: #74b9ff;
    }
    
    .weather-icon {
        font-size: 4em;
        margin-bottom: 20px;
        background: linear-gradient(45deg, #74b9ff, #6c5ce7);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .weather-metric {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 15px;
        padding: 20px;
        margin: 15px 0;
        border-left: 5px solid #74b9ff;
        transition: all 0.3s ease;
    }
    
    .weather-metric:hover {
        background: linear-gradient(135deg, #e9ecef, #dee2e6);
        transform: translateX(10px);
    }
    
    .weather-value {
        font-size: 2em;
        font-weight: bold;
        color: #2d3436;
        margin-bottom: 5px;
    }
    
    .weather-label {
        color: #636e72;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .pond-status-badge {
        padding: 8px 16px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 0.9em;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .status-active {
        background: linear-gradient(45deg, #00b894, #00cec9);
        color: white;
    }
    
    .status-maintenance {
        background: linear-gradient(45deg, #fdcb6e, #e17055);
        color: white;
    }
    
    .weather-alert {
        background: linear-gradient(135deg, #ff7675, #fd79a8);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        animation: pulse-alert 2s infinite;
    }
    
    @keyframes pulse-alert {
        0%, 100% { transform: scale(1); opacity: 0.9; }
        50% { transform: scale(1.02); opacity: 1; }
    }
    
    .integration-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }
    
    .stat-card {
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        color: white;
        padding: 25px;
        border-radius: 15px;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px) scale(1.05);
        box-shadow: 0 15px 30px rgba(116, 185, 255, 0.4);
    }
    
    .stat-number {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .stat-label {
        font-size: 0.9em;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .weather-chart {
        background: white;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .no-location-warning {
        background: linear-gradient(135deg, #ffeaa7, #fab1a0);
        color: #2d3436;
        border-radius: 15px;
        padding: 25px;
        margin: 20px 0;
        border-left: 5px solid #e17055;
    }
    
    .action-button {
        background: linear-gradient(45deg, #74b9ff, #0984e3);
        border: none;
        border-radius: 25px;
        padding: 12px 25px;
        color: white;
        font-weight: bold;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin: 10px 5px;
    }
    
    .action-button:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(116, 185, 255, 0.4);
        background: linear-gradient(45deg, #0984e3, #74b9ff);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Weather Integration Header -->
    <div class="weather-header">
        <div class="position-relative">
            <h1><i class="fas fa-cloud-sun me-3"></i>Pond Weather Integration Dashboard</h1>
            <p class="lead mb-0">Real-time weather monitoring for optimal aquaculture management</p>
            <div class="mt-3">
                <span class="badge bg-light text-dark me-2">🌤️ WEATHER TRACKING</span>
                <span class="badge bg-light text-dark me-2">📍 LOCATION-BASED</span>
                <span class="badge bg-light text-dark me-2">🚨 SMART ALERTS</span>
                <span class="badge bg-success text-white ms-3">✅ FULLY INTEGRATED</span>
            </div>
        </div>
    </div>

    <!-- Integration Statistics -->
    <div class="integration-stats">
        <div class="stat-card">
            <div class="stat-number">{{ total_ponds }}</div>
            <div class="stat-label">Total Ponds</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ ponds_with_location }}</div>
            <div class="stat-label">Ponds with Location</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ weather_stations }}</div>
            <div class="stat-label">Weather Stations</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ recent_weather_records }}</div>
            <div class="stat-label">Recent Weather Records</div>
        </div>
    </div>

    <!-- Pond Weather Cards -->
    <div class="row">
        {% for pond_data in ponds_with_weather %}
        <div class="col-lg-6 col-xl-4">
            <div class="pond-weather-card">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <h3>{{ pond_data.pond.name }}</h3>
                    <span class="pond-status-badge status-{{ pond_data.pond.status }}">
                        {{ pond_data.pond.status|title }}
                    </span>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="text-center">
                            <div class="weather-icon">
                                {% if pond_data.weather.condition == 'Clear' %}
                                    <i class="fas fa-sun"></i>
                                {% elif pond_data.weather.condition == 'Partly Cloudy' %}
                                    <i class="fas fa-cloud-sun"></i>
                                {% elif 'Rain' in pond_data.weather.condition %}
                                    <i class="fas fa-cloud-rain"></i>
                                {% else %}
                                    <i class="fas fa-cloud"></i>
                                {% endif %}
                            </div>
                            <h4>{{ pond_data.weather.condition }}</h4>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="weather-metric">
                            <div class="weather-value">{{ pond_data.weather.temperature }}°C</div>
                            <div class="weather-label">Temperature</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-6">
                        <div class="weather-metric">
                            <div class="weather-value">{{ pond_data.weather.humidity }}%</div>
                            <div class="weather-label">Humidity</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="weather-metric">
                            <div class="weather-value">{{ pond_data.weather.wind_speed }} km/h</div>
                            <div class="weather-label">Wind Speed</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-6">
                        <div class="weather-metric">
                            <div class="weather-value">{{ pond_data.weather.precipitation }} mm</div>
                            <div class="weather-label">Precipitation</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="weather-metric">
                            <div class="weather-value">{{ pond_data.weather.pressure }} hPa</div>
                            <div class="weather-label">Pressure</div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        Station: {{ pond_data.station.name }}
                        {% if pond_data.distance_km %}
                            ({{ pond_data.distance_km|floatformat:1 }} km away)
                        {% endif %}
                    </small>
                    <br>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Updated: {{ pond_data.weather.timestamp|timesince }} ago
                    </small>
                </div>
                
                {% if pond_data.alerts %}
                <div class="weather-alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Weather Alert:</strong> {{ pond_data.alerts.0.message }}
                </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="no-location-warning">
                <h4><i class="fas fa-exclamation-triangle me-2"></i>No Weather Data Available</h4>
                <p>No ponds with location data and weather information found. To enable weather monitoring:</p>
                <ul>
                    <li>Add latitude and longitude coordinates to your ponds</li>
                    <li>Ensure weather stations are configured</li>
                    <li>Verify weather API configuration</li>
                </ul>
                <a href="{% url 'ponds:pond_list' %}" class="action-button">
                    <i class="fas fa-edit me-2"></i>Edit Pond Locations
                </a>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Ponds Without Location -->
    {% if ponds_without_location %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="no-location-warning">
                <h4><i class="fas fa-map-marker-alt me-2"></i>Ponds Missing Location Data</h4>
                <p>The following ponds don't have location coordinates and cannot receive weather data:</p>
                <div class="row">
                    {% for pond in ponds_without_location %}
                    <div class="col-md-3 mb-2">
                        <span class="badge bg-warning text-dark">{{ pond.name }}</span>
                    </div>
                    {% endfor %}
                </div>
                <div class="mt-3">
                    <a href="{% url 'ponds:pond_list' %}" class="action-button">
                        <i class="fas fa-plus me-2"></i>Add Pond Locations
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Integration Actions -->
    <div class="text-center mt-5">
        <h3>Weather Integration Actions</h3>
        <div class="mt-4">
            <a href="{% url 'weather:weather_dashboard' %}" class="action-button">
                <i class="fas fa-tachometer-alt me-2"></i>Weather Dashboard
            </a>
            <a href="{% url 'ponds:pond_list' %}" class="action-button">
                <i class="fas fa-fish me-2"></i>Manage Ponds
            </a>
            <a href="{% url 'weather:alert_list' %}" class="action-button">
                <i class="fas fa-bell me-2"></i>Weather Alerts
            </a>
        </div>
        
        <div class="mt-4">
            <h5>Management Commands</h5>
            <div class="bg-dark text-light p-3 rounded">
                <code>
                    python manage.py verify_pond_weather --verbose<br>
                    python manage.py setup_weather_api --create-config --generate-data<br>
                    python manage.py verify_pond_weather --create-stations --update-weather
                </code>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('.pond-weather-card, .stat-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(50px)';
        card.style.transition = 'all 0.6s ease';
        observer.observe(card);
    });
    
    // Auto-refresh weather data every 5 minutes
    setInterval(() => {
        console.log('Auto-refreshing weather data...');
        // In a real implementation, this would make an AJAX call to update weather data
    }, 300000); // 5 minutes
});
</script>
{% endblock %}
