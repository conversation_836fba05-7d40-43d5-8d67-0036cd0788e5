{% extends 'base.html' %}
{% load static %}

{% block title %}Weather Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/weather_dashboard.css' %}">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.weather-dashboard {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    padding: 20px;
}

.weather-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.weather-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.current-weather {
    text-align: center;
    color: white;
}

.temperature-display {
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 8px;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.weather-condition {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 16px;
}

.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.weather-detail-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
}

.weather-detail-value {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
}

.weather-detail-label {
    font-size: 0.8rem;
    opacity: 0.8;
    color: white;
}

.forecast-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    color: white;
}

.forecast-date {
    font-weight: 600;
    margin-bottom: 8px;
}

.forecast-temps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    border-left: 4px solid;
    color: white;
}

.alert-critical { border-left-color: #e74c3c; }
.alert-high { border-left-color: #f39c12; }
.alert-medium { border-left-color: #f1c40f; }
.alert-low { border-left-color: #27ae60; }

.automation-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
}

.automation-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 16px;
    border-radius: 12px;
    color: white;
}

.automation-stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.automation-stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.weather-button {
    background: linear-gradient(45deg, #74b9ff, #0984e3);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.weather-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(116, 185, 255, 0.3);
    color: white;
    text-decoration: none;
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

.weather-icon {
    font-size: 3rem;
    margin-bottom: 16px;
}

.station-info {
    background: rgba(255, 255, 255, 0.1);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 16px;
    color: white;
}

.data-age {
    font-size: 0.8rem;
    opacity: 0.7;
    color: white;
}

.refresh-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-button:hover {
    background: rgba(255, 255, 255, 0.3);
}
</style>
{% endblock %}

{% block content %}
<div class="weather-dashboard">
    <!-- Dashboard Header -->
    <div class="weather-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="text-white mb-2">🌤️ Weather Dashboard</h1>
                <p class="text-white-50 mb-0">Real-time weather monitoring and intelligent automation for your shrimp farms</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 justify-content-end">
                    <a href="{% url 'weather_integration:weather_automation' %}" class="weather-button">
                        🤖 Automation
                    </a>
                    <a href="{% url 'weather_integration:weather_alerts' %}" class="weather-button">
                        🚨 Alerts
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Weather -->
    <div class="row mb-4">
        {% for weather in current_weather %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="weather-card current-weather">
                <div class="station-info">
                    <strong>{{ weather.station.name }}</strong>
                    <div class="data-age">Updated {{ weather.age_minutes|floatformat:0 }} minutes ago</div>
                    <button class="refresh-button float-end" onclick="updateWeatherData({{ weather.station.id }})">
                        🔄 Refresh
                    </button>
                </div>
                
                <div class="weather-icon">
                    {% if 'rain' in weather.data.weather_condition|lower %}
                        🌧️
                    {% elif 'cloud' in weather.data.weather_condition|lower %}
                        ☁️
                    {% elif 'sun' in weather.data.weather_condition|lower or 'clear' in weather.data.weather_condition|lower %}
                        ☀️
                    {% else %}
                        🌤️
                    {% endif %}
                </div>
                
                <div class="temperature-display">{{ weather.data.temperature|floatformat:1 }}°C</div>
                <div class="weather-condition">{{ weather.data.weather_condition|title }}</div>
                
                <div class="weather-details">
                    <div class="weather-detail-item">
                        <div class="weather-detail-value">{{ weather.data.humidity|floatformat:0 }}%</div>
                        <div class="weather-detail-label">Humidity</div>
                    </div>
                    <div class="weather-detail-item">
                        <div class="weather-detail-value">{{ weather.data.wind_speed|floatformat:1 }}</div>
                        <div class="weather-detail-label">Wind (m/s)</div>
                    </div>
                    <div class="weather-detail-item">
                        <div class="weather-detail-value">{{ weather.data.pressure|floatformat:0 }}</div>
                        <div class="weather-detail-label">Pressure (hPa)</div>
                    </div>
                    <div class="weather-detail-item">
                        <div class="weather-detail-value">{{ weather.data.rainfall|floatformat:1 }}</div>
                        <div class="weather-detail-label">Rain (mm)</div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="weather-card">
                <p class="text-white text-center">No weather stations found. Add weather stations for your farms to see current weather data.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Weather Forecast and Alerts -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="weather-card">
                <h3 class="text-white mb-3">📈 7-Day Weather Forecast</h3>
                
                {% for forecast_group in weather_forecast %}
                <div class="mb-4">
                    <h5 class="text-white-50">{{ forecast_group.station.name }}</h5>
                    {% for forecast in forecast_group.forecasts %}
                    <div class="forecast-item">
                        <div class="forecast-date">
                            {{ forecast.forecast_time|date:"l, M d" }}
                        </div>
                        <div class="forecast-temps">
                            <div>
                                <span class="text-white-50">{{ forecast.weather_condition }}</span>
                            </div>
                            <div>
                                <span class="fw-bold">{{ forecast.temperature_max|floatformat:0 }}°</span>
                                <span class="text-white-50">/ {{ forecast.temperature_min|floatformat:0 }}°</span>
                            </div>
                            <div>
                                <span class="text-info">💧 {{ forecast.precipitation_probability|floatformat:0 }}%</span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% empty %}
                <p class="text-white-50">No forecast data available.</p>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="weather-card">
                <h3 class="text-white mb-3">🚨 Active Weather Alerts</h3>
                
                {% for alert in active_alerts %}
                <div class="alert-item alert-{{ alert.severity }}">
                    <div class="fw-bold">{{ alert.get_alert_type_display }}</div>
                    <div class="small text-white-50 mb-2">{{ alert.station.name }}</div>
                    <div class="small">{{ alert.description|truncatewords:20 }}</div>
                    <div class="small text-white-50 mt-2">
                        {{ alert.issued_at|timesince }} ago
                    </div>
                    <button class="btn btn-sm btn-outline-light mt-2" onclick="acknowledgeAlert({{ alert.id }})">
                        Acknowledge
                    </button>
                </div>
                {% empty %}
                <p class="text-white-50">No active weather alerts.</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Weather Automation Summary -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="weather-card">
                <h3 class="text-white mb-3">🤖 Weather Automation Summary</h3>
                
                {% if automation_summary.success %}
                <div class="automation-summary">
                    <div class="automation-stat">
                        <div class="automation-stat-value">{{ automation_summary.active_rules }}</div>
                        <div class="automation-stat-label">Active Rules</div>
                    </div>
                    <div class="automation-stat">
                        <div class="automation-stat-value">{{ automation_summary.total_executions }}</div>
                        <div class="automation-stat-label">Total Executions</div>
                    </div>
                    <div class="automation-stat">
                        <div class="automation-stat-value">{{ automation_summary.success_rate|floatformat:1 }}%</div>
                        <div class="automation-stat-label">Success Rate</div>
                    </div>
                    <div class="automation-stat">
                        <div class="automation-stat-value">{{ automation_summary.successful_executions }}</div>
                        <div class="automation-stat-label">Successful</div>
                    </div>
                </div>
                {% else %}
                <p class="text-white-50">Weather automation data not available.</p>
                {% endif %}
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="weather-card">
                <h3 class="text-white mb-3">📊 Weather Trends</h3>
                <div class="chart-container">
                    <canvas id="weatherTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Statistics -->
    <div class="row">
        <div class="col-12">
            <div class="weather-card">
                <h3 class="text-white mb-3">📈 Weather Statistics (Last 30 Days)</h3>
                
                {% if weather_stats %}
                <div class="row">
                    {% for station_id, stats in weather_stats.items %}
                    <div class="col-md-4 mb-3">
                        <div class="station-info">
                            <h5 class="text-white">{{ stats.station_name }}</h5>
                            <div class="row">
                                <div class="col-6">
                                    <div class="text-white-50 small">Avg Temperature</div>
                                    <div class="text-white fw-bold">{{ stats.avg_temperature|floatformat:1 }}°C</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-white-50 small">Max Temperature</div>
                                    <div class="text-white fw-bold">{{ stats.max_temperature|floatformat:1 }}°C</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-white-50 small">Avg Humidity</div>
                                    <div class="text-white fw-bold">{{ stats.avg_humidity|floatformat:1 }}%</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-white-50 small">Total Rainfall</div>
                                    <div class="text-white fw-bold">{{ stats.total_rainfall|floatformat:1 }}mm</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-white-50">No weather statistics available.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
// Weather Trends Chart
const ctx = document.getElementById('weatherTrendsChart').getContext('2d');
const weatherTrendsChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['6 days ago', '5 days ago', '4 days ago', '3 days ago', '2 days ago', 'Yesterday', 'Today'],
        datasets: [{
            label: 'Temperature (°C)',
            data: [26, 28, 27, 29, 28, 27, 26],
            borderColor: '#e74c3c',
            backgroundColor: 'rgba(231, 76, 60, 0.1)',
            tension: 0.4,
            yAxisID: 'y'
        }, {
            label: 'Humidity (%)',
            data: [75, 78, 72, 80, 76, 74, 77],
            borderColor: '#3498db',
            backgroundColor: 'rgba(52, 152, 219, 0.1)',
            tension: 0.4,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                labels: {
                    color: 'white'
                }
            }
        },
        scales: {
            x: {
                ticks: {
                    color: 'white'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                ticks: {
                    color: 'white'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                ticks: {
                    color: 'white'
                },
                grid: {
                    drawOnChartArea: false,
                }
            }
        }
    }
});

// Update weather data function
function updateWeatherData(stationId) {
    fetch(`/weather/update/${stationId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error updating weather data: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating weather data');
    });
}

// Acknowledge alert function
function acknowledgeAlert(alertId) {
    fetch(`/weather/alerts/${alertId}/acknowledge/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error acknowledging alert: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error acknowledging alert');
    });
}

// Auto-refresh weather data every 5 minutes
setInterval(() => {
    location.reload();
}, 300000);
</script>
{% endblock %}
