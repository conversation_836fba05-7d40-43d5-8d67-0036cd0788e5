"""
Database Query Profiler
Monitors and optimizes database query performance
"""

import time
import logging
from django.db import connection
from django.conf import settings
from django.core.management.base import BaseCommand
from collections import defaultdict, Counter
import json
from typing import Dict, List, Any
import threading
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class QueryProfiler:
    """
    Database query profiler for performance monitoring and optimization
    """
    
    def __init__(self):
        self.queries = []
        self.slow_queries = []
        self.duplicate_queries = defaultdict(list)
        self.query_stats = defaultdict(lambda: {'count': 0, 'total_time': 0, 'avg_time': 0})
        self.lock = threading.Lock()
        self.enabled = getattr(settings, 'QUERY_PROFILER_ENABLED', settings.DEBUG)
        self.slow_query_threshold = getattr(settings, 'SLOW_QUERY_THRESHOLD', 0.1)  # 100ms
    
    def start_profiling(self):
        """Start query profiling"""
        if not self.enabled:
            return
        
        # Hook into Django's database connection
        self._original_execute = connection.ops.execute
        connection.ops.execute = self._profiled_execute
        logger.info("Query profiling started")
    
    def stop_profiling(self):
        """Stop query profiling"""
        if not self.enabled:
            return
        
        if hasattr(self, '_original_execute'):
            connection.ops.execute = self._original_execute
        logger.info("Query profiling stopped")
    
    def _profiled_execute(self, cursor, sql, params=None):
        """Execute query with profiling"""
        start_time = time.time()
        
        try:
            result = self._original_execute(cursor, sql, params)
            execution_time = time.time() - start_time
            
            # Record query
            self._record_query(sql, params, execution_time)
            
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            self._record_query(sql, params, execution_time, error=str(e))
            raise
    
    def _record_query(self, sql, params, execution_time, error=None):
        """Record query execution details"""
        with self.lock:
            query_info = {
                'sql': sql,
                'params': params,
                'execution_time': execution_time,
                'timestamp': time.time(),
                'error': error
            }
            
            self.queries.append(query_info)
            
            # Track slow queries
            if execution_time > self.slow_query_threshold:
                self.slow_queries.append(query_info)
                logger.warning(f"Slow query detected ({execution_time:.3f}s): {sql[:100]}...")
            
            # Track duplicate queries
            query_signature = self._get_query_signature(sql)
            self.duplicate_queries[query_signature].append(query_info)
            
            # Update statistics
            self.query_stats[query_signature]['count'] += 1
            self.query_stats[query_signature]['total_time'] += execution_time
            self.query_stats[query_signature]['avg_time'] = (
                self.query_stats[query_signature]['total_time'] / 
                self.query_stats[query_signature]['count']
            )
    
    def _get_query_signature(self, sql):
        """Get normalized query signature for duplicate detection"""
        # Remove parameters and normalize whitespace
        import re
        normalized = re.sub(r'\s+', ' ', sql.strip())
        normalized = re.sub(r'%s|\?', 'PARAM', normalized)
        return normalized
    
    def get_performance_report(self):
        """Generate comprehensive performance report"""
        with self.lock:
            total_queries = len(self.queries)
            total_time = sum(q['execution_time'] for q in self.queries)
            avg_time = total_time / total_queries if total_queries > 0 else 0
            
            # Find most frequent queries
            most_frequent = sorted(
                self.query_stats.items(),
                key=lambda x: x[1]['count'],
                reverse=True
            )[:10]
            
            # Find slowest queries
            slowest_queries = sorted(
                self.queries,
                key=lambda x: x['execution_time'],
                reverse=True
            )[:10]
            
            # Find duplicate queries
            duplicates = {
                sig: queries for sig, queries in self.duplicate_queries.items()
                if len(queries) > 1
            }
            
            report = {
                'summary': {
                    'total_queries': total_queries,
                    'total_time': round(total_time, 3),
                    'average_time': round(avg_time, 3),
                    'slow_queries_count': len(self.slow_queries),
                    'duplicate_query_groups': len(duplicates)
                },
                'most_frequent_queries': [
                    {
                        'query': sig[:100] + '...' if len(sig) > 100 else sig,
                        'count': stats['count'],
                        'avg_time': round(stats['avg_time'], 3),
                        'total_time': round(stats['total_time'], 3)
                    }
                    for sig, stats in most_frequent
                ],
                'slowest_queries': [
                    {
                        'query': q['sql'][:100] + '...' if len(q['sql']) > 100 else q['sql'],
                        'execution_time': round(q['execution_time'], 3),
                        'params': str(q['params'])[:50] if q['params'] else None
                    }
                    for q in slowest_queries
                ],
                'optimization_suggestions': self._get_optimization_suggestions()
            }
            
            return report
    
    def _get_optimization_suggestions(self):
        """Generate optimization suggestions based on profiling data"""
        suggestions = []
        
        # Check for N+1 queries
        for sig, queries in self.duplicate_queries.items():
            if len(queries) > 5:  # More than 5 identical queries
                suggestions.append({
                    'type': 'n_plus_one',
                    'message': f'Potential N+1 query detected: {len(queries)} identical queries',
                    'query': sig[:100] + '...',
                    'suggestion': 'Consider using select_related() or prefetch_related()'
                })
        
        # Check for missing indexes
        for query in self.slow_queries:
            if 'WHERE' in query['sql'].upper() and 'INDEX' not in query['sql'].upper():
                suggestions.append({
                    'type': 'missing_index',
                    'message': 'Slow query with WHERE clause may need index',
                    'query': query['sql'][:100] + '...',
                    'suggestion': 'Consider adding database index for filtered columns'
                })
        
        # Check for large result sets
        for sig, stats in self.query_stats.items():
            if stats['avg_time'] > 0.5:  # Queries taking more than 500ms
                suggestions.append({
                    'type': 'large_result_set',
                    'message': f'Very slow query (avg: {stats["avg_time"]:.3f}s)',
                    'query': sig[:100] + '...',
                    'suggestion': 'Consider adding pagination or limiting result set'
                })
        
        return suggestions
    
    def clear_data(self):
        """Clear all profiling data"""
        with self.lock:
            self.queries.clear()
            self.slow_queries.clear()
            self.duplicate_queries.clear()
            self.query_stats.clear()
            logger.info("Query profiler data cleared")

# Global profiler instance
query_profiler = QueryProfiler()

@contextmanager
def profile_queries():
    """Context manager for query profiling"""
    query_profiler.start_profiling()
    try:
        yield query_profiler
    finally:
        query_profiler.stop_profiling()

class QueryOptimizer:
    """
    Query optimization utilities
    """
    
    @staticmethod
    def optimize_pond_queries():
        """Optimize pond-related queries"""
        from ponds.models import Pond
        
        # Example optimizations
        optimizations = []
        
        # Check for missing select_related
        try:
            # This would cause N+1 queries
            ponds = list(Pond.objects.all())
            for pond in ponds:
                _ = pond.farm.name  # This accesses related farm
            
            optimizations.append({
                'model': 'Pond',
                'issue': 'Missing select_related for farm',
                'solution': 'Use Pond.objects.select_related("farm")'
            })
        except:
            pass
        
        return optimizations
    
    @staticmethod
    def optimize_water_quality_queries():
        """Optimize water quality queries"""
        from water_quality.models import WaterQualityReading
        
        optimizations = []
        
        try:
            # Check for inefficient date filtering
            readings = WaterQualityReading.objects.filter(
                timestamp__gte='2024-01-01'
            ).order_by('-timestamp')[:100]
            
            if not readings:
                optimizations.append({
                    'model': 'WaterQualityReading',
                    'issue': 'Date filtering without index',
                    'solution': 'Add database index on timestamp field'
                })
        except:
            pass
        
        return optimizations

def generate_optimization_report():
    """Generate comprehensive optimization report"""
    optimizer = QueryOptimizer()
    
    report = {
        'timestamp': time.time(),
        'optimizations': {
            'pond_queries': optimizer.optimize_pond_queries(),
            'water_quality_queries': optimizer.optimize_water_quality_queries(),
        },
        'profiler_data': query_profiler.get_performance_report() if query_profiler.enabled else None
    }
    
    return report
