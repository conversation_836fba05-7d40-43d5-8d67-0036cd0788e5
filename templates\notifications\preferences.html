<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notification Preferences - Shrimp Farm Guardian</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .dashboard-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .dashboard-header h1 {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .quick-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }
        
        .quick-action {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .preference-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .preference-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .preference-item:last-child {
            border-bottom: none;
        }
        
        .preference-label {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .preference-title {
            font-weight: 600;
            color: #2d3748;
        }
        
        .preference-description {
            font-size: 0.9rem;
            color: #64748b;
        }
        
        .form-switch {
            transform: scale(1.2);
        }
        
        .time-input-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }
        
        .time-input {
            padding: 8px 12px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        
        .save-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .alert-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1>⚙️ Notification Preferences</h1>
        <p>Customize how and when you receive notifications</p>
        
        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="{% url 'notifications:center' %}" class="quick-action">
                <i class="fas fa-bell"></i>
                <span>Notification Center</span>
            </a>
            <a href="/" class="quick-action">
                <i class="fas fa-home"></i>
                <span>Main Dashboard</span>
            </a>
        </div>
    </div>

    <form id="preferences-form" method="post">
        {% csrf_token %}
        
        <!-- Channel Preferences -->
        <div class="preference-section">
            <div class="section-title">
                <div class="section-icon">
                    <i class="fas fa-broadcast-tower"></i>
                </div>
                <span>Notification Channels</span>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Email Notifications</div>
                    <div class="preference-description">Receive notifications via email</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="email_enabled" 
                           {% if preferences.email_enabled %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Push Notifications</div>
                    <div class="preference-description">Browser and mobile push notifications</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="push_enabled" 
                           {% if preferences.push_enabled %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">SMS Notifications</div>
                    <div class="preference-description">Text message notifications for critical alerts</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="sms_enabled" 
                           {% if preferences.sms_enabled %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Web Notifications</div>
                    <div class="preference-description">In-app notifications in the notification center</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="web_enabled" 
                           {% if preferences.web_enabled %}checked{% endif %}>
                </div>
            </div>
        </div>

        <!-- Notification Types -->
        <div class="preference-section">
            <div class="section-title">
                <div class="section-icon">
                    <i class="fas fa-list-check"></i>
                </div>
                <span>Notification Types</span>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Water Quality Alerts</div>
                    <div class="preference-description">Critical water parameter notifications</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="water_quality_alerts" 
                           {% if preferences.water_quality_alerts %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Feed Alerts</div>
                    <div class="preference-description">Feed stock and batch expiry notifications</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="feed_alerts" 
                           {% if preferences.feed_alerts %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Equipment Alerts</div>
                    <div class="preference-description">Equipment maintenance and malfunction alerts</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="equipment_alerts" 
                           {% if preferences.equipment_alerts %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Harvest Reminders</div>
                    <div class="preference-description">Harvest timing and preparation notifications</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="harvest_reminders" 
                           {% if preferences.harvest_reminders %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Performance Reports</div>
                    <div class="preference-description">Weekly and monthly performance summaries</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="performance_reports" 
                           {% if preferences.performance_reports %}checked{% endif %}>
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">AI Recommendations</div>
                    <div class="preference-description">AI-generated insights and suggestions</div>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" name="ai_recommendations" 
                           {% if preferences.ai_recommendations %}checked{% endif %}>
                </div>
            </div>
        </div>

        <!-- Timing Preferences -->
        <div class="preference-section">
            <div class="section-title">
                <div class="section-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <span>Timing & Schedule</span>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Quiet Hours</div>
                    <div class="preference-description">No notifications during these hours (except critical alerts)</div>
                </div>
                <div class="time-input-group">
                    <input type="time" name="quiet_hours_start" class="time-input" 
                           value="{{ preferences.quiet_hours_start|time:'H:i' }}">
                    <span>to</span>
                    <input type="time" name="quiet_hours_end" class="time-input" 
                           value="{{ preferences.quiet_hours_end|time:'H:i' }}">
                </div>
            </div>
            
            <div class="preference-item">
                <div class="preference-label">
                    <div class="preference-title">Digest Frequency</div>
                    <div class="preference-description">How often to receive summary notifications</div>
                </div>
                <select name="digest_frequency" class="form-select" style="width: auto;">
                    <option value="none" {% if preferences.digest_frequency == 'none' %}selected{% endif %}>No Digest</option>
                    <option value="daily" {% if preferences.digest_frequency == 'daily' %}selected{% endif %}>Daily</option>
                    <option value="weekly" {% if preferences.digest_frequency == 'weekly' %}selected{% endif %}>Weekly</option>
                    <option value="monthly" {% if preferences.digest_frequency == 'monthly' %}selected{% endif %}>Monthly</option>
                </select>
            </div>
        </div>

        <!-- Save Button -->
        <div class="text-center">
            <button type="submit" class="save-button">
                <i class="fas fa-save me-2"></i>Save Preferences
            </button>
        </div>
    </form>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
document.getElementById('preferences-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const saveButton = document.querySelector('.save-button');
    const originalText = saveButton.innerHTML;
    
    // Show loading state
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    saveButton.disabled = true;
    
    fetch('{% url "notifications:preferences" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success mt-3';
            alertDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.message;
            
            const form = document.getElementById('preferences-form');
            form.parentNode.insertBefore(alertDiv, form);
            
            // Remove alert after 3 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        } else {
            alert('Error saving preferences: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving preferences');
    })
    .finally(() => {
        // Restore button state
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
});
</script>

</body>
</html>
