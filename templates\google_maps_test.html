<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 5px solid;
        }
        .status.success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        #map {
            width: 100%;
            height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            color: #6c757d;
            font-size: 18px;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #e9ecef;
            margin: 10px 0;
        }
        .logs {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .test-buttons {
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.success {
            background: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ Google Maps Loading Test</h1>
        
        <div class="info">
            <strong>📍 Testing Google Maps Integration</strong><br>
            This page tests if Google Maps API is loading correctly and helps diagnose any issues.
        </div>

        <!-- Status Indicators -->
        <div id="apiKeyStatus" class="status warning">
            ⏳ Checking API Key...
        </div>
        <div id="scriptLoadStatus" class="status warning">
            ⏳ Loading Google Maps Script...
        </div>
        <div id="mapInitStatus" class="status warning">
            ⏳ Initializing Map...
        </div>

        <!-- API Key Info -->
        <div class="info">
            <strong>🔑 API Key Info:</strong><br>
            <div class="code">
                API Key: AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw<br>
                Status: <span id="keyValidation">Checking...</span>
            </div>
        </div>

        <!-- Test Buttons -->
        <div class="test-buttons">
            <button class="btn" onclick="testDirectApiCall()">🧪 Test Direct API Call</button>
            <button class="btn secondary" onclick="reloadMap()">🔄 Reload Map</button>
            <button class="btn success" onclick="testGeocoding()">📍 Test Geocoding</button>
        </div>

        <!-- Map Container -->
        <div id="map" class="loading">
            🔄 Loading Google Maps...
        </div>

        <!-- Console Logs -->
        <h3>📝 Console Logs</h3>
        <div id="consoleLogs" class="logs">
            Initializing diagnostics...<br>
        </div>

        <!-- Error Details -->
        <div id="errorDetails" style="display: none;">
            <h3>❌ Error Details</h3>
            <div id="errorContent" class="status error"></div>
        </div>
    </div>

    <script>
        let map;
        let logContainer = document.getElementById('consoleLogs');
        
        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}<br>`;
            logContainer.innerHTML += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
        }

        // Update status indicators
        function updateStatus(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.className = `status ${type}`;
            element.innerHTML = type === 'success' ? `✅ ${message}` : 
                               type === 'error' ? `❌ ${message}` : 
                               `⏳ ${message}`;
        }

        // Error handling
        function showError(title, details) {
            document.getElementById('errorDetails').style.display = 'block';
            document.getElementById('errorContent').innerHTML = `<strong>${title}</strong><br>${details}`;
            log(`ERROR: ${title} - ${details}`, 'error');
        }

        // Test direct API call
        function testDirectApiCall() {
            log('Testing direct API call...', 'info');
            const testUrl = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&libraries=places';
            
            fetch(testUrl, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        log('Direct API call successful', 'success');
                        updateStatus('apiKeyStatus', 'API Key Valid', 'success');
                    } else {
                        log(`Direct API call failed: ${response.status}`, 'error');
                        updateStatus('apiKeyStatus', `API Key Error: ${response.status}`, 'error');
                    }
                })
                .catch(error => {
                    log(`Network error: ${error.message}`, 'error');
                    updateStatus('apiKeyStatus', 'Network Error', 'error');
                });
        }

        // Test geocoding
        function testGeocoding() {
            if (typeof google !== 'undefined' && google.maps) {
                log('Testing Geocoding service...', 'info');
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode({ address: 'Chennai, India' }, (results, status) => {
                    if (status === 'OK') {
                        log('Geocoding test successful', 'success');
                    } else {
                        log(`Geocoding failed: ${status}`, 'error');
                    }
                });
            } else {
                log('Google Maps not loaded yet', 'warning');
            }
        }

        // Reload map
        function reloadMap() {
            log('Reloading map...', 'info');
            initMap();
        }

        // Initialize map
        function initMap() {
            try {
                log('Initializing Google Maps...', 'info');
                
                if (typeof google === 'undefined') {
                    throw new Error('Google Maps API not loaded');
                }

                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 10,
                    center: { lat: 13.0827, lng: 80.2707 }, // Chennai, India
                    mapTypeId: 'roadmap'
                });

                // Add a test marker
                const marker = new google.maps.Marker({
                    position: { lat: 13.0827, lng: 80.2707 },
                    map: map,
                    title: 'Test Location - Chennai',
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="15" cy="15" r="12" fill="#ff0000" stroke="white" stroke-width="2"/>
                                <text x="15" y="20" text-anchor="middle" fill="white" font-size="12" font-family="Arial">T</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(30, 30)
                    }
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: '<div><h4>✅ Google Maps Test</h4><p>Map loaded successfully!</p></div>'
                });

                marker.addListener('click', () => {
                    infoWindow.open(map, marker);
                });

                log('Map initialized successfully', 'success');
                updateStatus('mapInitStatus', 'Map Initialized Successfully', 'success');
                updateStatus('scriptLoadStatus', 'Script Loaded Successfully', 'success');

            } catch (error) {
                log(`Map initialization failed: ${error.message}`, 'error');
                updateStatus('mapInitStatus', `Initialization Failed: ${error.message}`, 'error');
                showError('Map Initialization Error', error.message);
            }
        }

        // Global error handler for Google Maps
        window.gm_authFailure = function() {
            const errorMsg = 'Google Maps authentication failed. Please check your API key.';
            log(errorMsg, 'error');
            updateStatus('apiKeyStatus', 'Authentication Failed', 'error');
            showError('Authentication Error', 'API key is invalid or has restrictions that prevent it from loading.');
            document.getElementById('map').innerHTML = '<div class="loading" style="color: #dc3545;">❌ Authentication Failed</div>';
        };

        // Handle script load errors
        window.addEventListener('error', function(event) {
            if (event.target.src && event.target.src.includes('maps.googleapis.com')) {
                const errorMsg = 'Failed to load Google Maps script';
                log(errorMsg, 'error');
                updateStatus('scriptLoadStatus', 'Script Load Failed', 'error');
                showError('Script Load Error', 'Could not load Google Maps JavaScript API. Check your internet connection.');
            }
        });

        // Check if Google Maps is already loaded
        if (typeof google !== 'undefined' && google.maps) {
            log('Google Maps already loaded', 'info');
            updateStatus('scriptLoadStatus', 'Already Loaded', 'success');
            initMap();
        } else {
            log('Loading Google Maps script...', 'info');
        }

        // Monitor script loading
        const checkGoogleMaps = setInterval(() => {
            if (typeof google !== 'undefined' && google.maps) {
                clearInterval(checkGoogleMaps);
                log('Google Maps API detected', 'success');
                updateStatus('scriptLoadStatus', 'Script Loaded', 'success');
                initMap();
            }
        }, 100);

        // Timeout for script loading
        setTimeout(() => {
            if (typeof google === 'undefined') {
                clearInterval(checkGoogleMaps);
                const errorMsg = 'Timeout: Google Maps script failed to load within 10 seconds';
                log(errorMsg, 'error');
                updateStatus('scriptLoadStatus', 'Load Timeout', 'error');
                showError('Load Timeout', 'Google Maps script took too long to load. Check your internet connection and API key.');
                document.getElementById('map').innerHTML = '<div class="loading" style="color: #dc3545;">❌ Load Timeout</div>';
            }
        }, 10000);

        // Initial API key validation
        log('Starting Google Maps diagnostics...', 'info');
        testDirectApiCall();
    </script>

    <!-- Load Google Maps API -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap"
        onerror="updateStatus('scriptLoadStatus', 'Script Load Error', 'error')">
    </script>
</body>
</html>
