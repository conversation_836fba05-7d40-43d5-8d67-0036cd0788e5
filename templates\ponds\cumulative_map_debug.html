{% extends 'base.html' %}
{% load static %}

{% block title %}Map Debug - Shrimp Farm Guardian{% endblock %}

{% block extra_css %}
<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    #map { height: 500px; width: 100%; border: 2px solid #ccc; margin: 20px 0; }
    .debug-info { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
    .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1>Cumulative Map Debug</h1>
            
            <div class="debug-info">
                <h3>Debug Information</h3>
                <p><strong>API Key:</strong> {{ google_maps_api_key|slice:":20" }}...</p>
                <p><strong>Farms Count:</strong> {{ farms_data|length }}</p>
                <p><strong>Ponds Count:</strong> {{ ponds_data|length }}</p>
                <p><strong>Center:</strong> {{ center_lat }}, {{ center_lng }}</p>
            </div>
            
            <div id="status-log"></div>
            <div id="map"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const statusLog = document.getElementById('status-log');
    
    function logStatus(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const statusDiv = document.createElement('div');
        statusDiv.className = `status ${type}`;
        statusDiv.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
        statusLog.appendChild(statusDiv);
        console.log(`[${timestamp}] ${message}`);
    }

    // Global variables
    let map;
    let infoWindow;
    
    // Data from Django (simplified)
    const farmsData = {{ farms_data|safe }};
    const pondsData = {{ ponds_data|safe }};
    const centerLat = {{ center_lat }};
    const centerLng = {{ center_lng }};
    
    logStatus('Page loaded, starting Google Maps initialization...', 'info');
    logStatus(`Center coordinates: ${centerLat}, ${centerLng}`, 'info');
    logStatus(`Farms data length: ${farmsData ? farmsData.length : 'undefined'}`, 'info');
    logStatus(`Ponds data length: ${pondsData ? pondsData.length : 'undefined'}`, 'info');

    // Global callback function
    window.initMap = function() {
        logStatus('✅ initMap callback called!', 'success');
        
        try {
            logStatus('Creating map instance...', 'info');
            
            map = new google.maps.Map(document.getElementById('map'), {
                zoom: 10,
                center: { lat: centerLat, lng: centerLng },
                mapTypeId: 'roadmap'
            });
            
            logStatus('✅ Map instance created successfully!', 'success');
            
            infoWindow = new google.maps.InfoWindow();
            
            // Add a simple test marker
            const testMarker = new google.maps.Marker({
                position: { lat: centerLat, lng: centerLng },
                map: map,
                title: 'Test Location'
            });
            
            logStatus('✅ Test marker added successfully!', 'success');
            logStatus('🎉 Map initialization complete!', 'success');
            
        } catch (error) {
            logStatus(`❌ Error in initMap: ${error.message}`, 'error');
            console.error('Map initialization error:', error);
        }
    };

    // Error handlers
    window.gm_authFailure = function() {
        logStatus('❌ Google Maps authentication failed!', 'error');
        document.getElementById('map').innerHTML = 
            '<div class="alert alert-danger m-3">Google Maps authentication failed! Check API key.</div>';
    };

    // Script load error handler
    window.addEventListener('error', function(e) {
        if (e.target.src && e.target.src.includes('maps.googleapis.com')) {
            logStatus('❌ Failed to load Google Maps script', 'error');
        }
    });

    // Timeout check
    setTimeout(() => {
        if (typeof google === 'undefined') {
            logStatus('❌ Google Maps failed to load within 10 seconds', 'error');
        }
    }, 10000);
</script>

<script async defer 
    src="https://maps.googleapis.com/maps/api/js?key={{ google_maps_api_key }}&callback=initMap"
    onload="console.log('Google Maps script loaded successfully')"
    onerror="console.error('Failed to load Google Maps script')">
</script>
{% endblock %}
