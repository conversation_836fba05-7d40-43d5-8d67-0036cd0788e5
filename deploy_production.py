#!/usr/bin/env python3
"""
Production Deployment Script for Shrimp Farm Guardian
Automates the deployment process with safety checks
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

class ProductionDeployer:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.env_file = self.project_root / '.env'
        self.backup_dir = self.project_root / 'backups'
        self.deployment_log = self.project_root / 'deployment.log'
        
    def log(self, message, level='INFO'):
        """Log deployment messages"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] {level}: {message}"
        print(log_message)
        
        with open(self.deployment_log, 'a') as f:
            f.write(log_message + '\n')
    
    def run_command(self, command, check=True):
        """Run shell command with logging"""
        self.log(f"Running: {command}")
        try:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
            if result.stdout:
                self.log(f"Output: {result.stdout.strip()}")
            return result
        except subprocess.CalledProcessError as e:
            self.log(f"Command failed: {e}", 'ERROR')
            if e.stderr:
                self.log(f"Error: {e.stderr.strip()}", 'ERROR')
            raise
    
    def check_prerequisites(self):
        """Check deployment prerequisites"""
        self.log("Checking deployment prerequisites...")
        
        # Check if .env file exists
        if not self.env_file.exists():
            self.log("ERROR: .env file not found. Copy .env.production to .env and configure it.", 'ERROR')
            return False
        
        # Check if Docker is running
        try:
            self.run_command('docker --version')
            self.run_command('docker-compose --version')
        except subprocess.CalledProcessError:
            self.log("ERROR: Docker or Docker Compose not available", 'ERROR')
            return False
        
        # Check if required environment variables are set
        required_vars = ['SECRET_KEY', 'DATABASE_URL', 'ALLOWED_HOSTS']
        missing_vars = []
        
        with open(self.env_file) as f:
            env_content = f.read()
            for var in required_vars:
                if f"{var}=" not in env_content or f"{var}=your-" in env_content:
                    missing_vars.append(var)
        
        if missing_vars:
            self.log(f"ERROR: Missing or unconfigured environment variables: {missing_vars}", 'ERROR')
            return False
        
        self.log("Prerequisites check passed!")
        return True
    
    def create_backup(self):
        """Create backup before deployment"""
        self.log("Creating backup...")
        
        # Create backup directory
        self.backup_dir.mkdir(exist_ok=True)
        
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        backup_name = f"backup_{timestamp}"
        
        # Backup database if running
        try:
            self.run_command(f'docker-compose exec -T db pg_dump -U postgres shrimp_farm_guardian > {self.backup_dir}/{backup_name}_db.sql')
            self.log("Database backup created")
        except:
            self.log("Database backup failed (container may not be running)", 'WARNING')
        
        # Backup media files
        media_dir = self.project_root / 'media'
        if media_dir.exists():
            self.run_command(f'tar -czf {self.backup_dir}/{backup_name}_media.tar.gz media/')
            self.log("Media files backup created")
        
        return backup_name
    
    def run_tests(self):
        """Run tests before deployment"""
        self.log("Running tests...")
        
        try:
            # Run migration checks
            self.run_command('python create_migration_fix.py')
            
            # Run schema audit
            self.run_command('python schema_audit.py')
            
            # Run Django checks
            self.run_command('python manage.py check --deploy')
            
            self.log("All tests passed!")
            return True
        except subprocess.CalledProcessError:
            self.log("Tests failed!", 'ERROR')
            return False
    
    def deploy(self):
        """Main deployment process"""
        self.log("Starting production deployment...")
        
        try:
            # Stop existing containers
            self.log("Stopping existing containers...")
            self.run_command('docker-compose down', check=False)
            
            # Pull latest images
            self.log("Pulling latest images...")
            self.run_command('docker-compose pull')
            
            # Build application
            self.log("Building application...")
            self.run_command('docker-compose build --no-cache')
            
            # Start database first
            self.log("Starting database...")
            self.run_command('docker-compose up -d db redis')
            
            # Wait for database to be ready
            self.log("Waiting for database to be ready...")
            time.sleep(30)
            
            # Run migrations
            self.log("Running database migrations...")
            self.run_command('docker-compose run --rm web python manage.py migrate')
            
            # Collect static files
            self.log("Collecting static files...")
            self.run_command('docker-compose run --rm web python manage.py collectstatic --noinput')
            
            # Start all services
            self.log("Starting all services...")
            self.run_command('docker-compose up -d')
            
            # Wait for services to start
            self.log("Waiting for services to start...")
            time.sleep(60)
            
            # Health check
            if self.health_check():
                self.log("Deployment completed successfully!")
                return True
            else:
                self.log("Deployment failed health check!", 'ERROR')
                return False
                
        except Exception as e:
            self.log(f"Deployment failed: {e}", 'ERROR')
            return False
    
    def health_check(self):
        """Perform post-deployment health check"""
        self.log("Performing health check...")
        
        max_retries = 10
        for i in range(max_retries):
            try:
                response = requests.get('http://localhost:8000/health/', timeout=10)
                if response.status_code == 200:
                    health_data = response.json()
                    if health_data.get('status') == 'ok':
                        self.log("Health check passed!")
                        return True
                    else:
                        self.log(f"Health check failed: {health_data}", 'WARNING')
                else:
                    self.log(f"Health check returned status {response.status_code}", 'WARNING')
            except requests.RequestException as e:
                self.log(f"Health check attempt {i+1} failed: {e}", 'WARNING')
            
            if i < max_retries - 1:
                self.log(f"Retrying health check in 10 seconds... ({i+1}/{max_retries})")
                time.sleep(10)
        
        return False
    
    def rollback(self, backup_name):
        """Rollback deployment"""
        self.log(f"Rolling back to backup: {backup_name}")
        
        try:
            # Stop current containers
            self.run_command('docker-compose down')
            
            # Restore database
            backup_file = self.backup_dir / f"{backup_name}_db.sql"
            if backup_file.exists():
                self.run_command('docker-compose up -d db')
                time.sleep(30)
                self.run_command(f'docker-compose exec -T db psql -U postgres -d shrimp_farm_guardian < {backup_file}')
                self.log("Database restored")
            
            # Restore media files
            media_backup = self.backup_dir / f"{backup_name}_media.tar.gz"
            if media_backup.exists():
                self.run_command(f'tar -xzf {media_backup}')
                self.log("Media files restored")
            
            # Start services
            self.run_command('docker-compose up -d')
            
            self.log("Rollback completed")
            return True
            
        except Exception as e:
            self.log(f"Rollback failed: {e}", 'ERROR')
            return False

def main():
    deployer = ProductionDeployer()
    
    print("🚀 Shrimp Farm Guardian - Production Deployment")
    print("=" * 50)
    
    # Check prerequisites
    if not deployer.check_prerequisites():
        sys.exit(1)
    
    # Create backup
    backup_name = deployer.create_backup()
    
    # Run tests
    if not deployer.run_tests():
        print("\n❌ Tests failed. Deployment aborted.")
        sys.exit(1)
    
    # Deploy
    if deployer.deploy():
        print("\n🎉 Deployment completed successfully!")
        print("Your application is now running at: http://localhost:8000")
        print("Health check: http://localhost:8000/health/")
    else:
        print("\n❌ Deployment failed!")
        rollback = input("Do you want to rollback? (y/N): ")
        if rollback.lower() == 'y':
            deployer.rollback(backup_name)

if __name__ == "__main__":
    main()
