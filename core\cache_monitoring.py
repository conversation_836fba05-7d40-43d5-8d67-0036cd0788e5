"""
Cache Monitoring System
Monitor cache performance and provide metrics
"""

from django.core.cache import cache
from django.core.management.base import BaseCommand
from django.conf import settings
import time
import json
import logging
from typing import Dict, Any, List
from collections import defaultdict, deque
import threading

logger = logging.getLogger(__name__)

class CacheMonitor:
    """
    Monitor cache performance and collect metrics
    """
    
    def __init__(self):
        self.metrics = defaultdict(int)
        self.hit_rates = defaultdict(lambda: deque(maxlen=100))
        self.response_times = defaultdict(lambda: deque(maxlen=100))
        self.lock = threading.Lock()
    
    def record_hit(self, key: str, response_time: float = 0.0):
        """Record a cache hit"""
        with self.lock:
            self.metrics[f'{key}_hits'] += 1
            self.metrics['total_hits'] += 1
            self.hit_rates[key].append(1)
            if response_time > 0:
                self.response_times[key].append(response_time)
    
    def record_miss(self, key: str, response_time: float = 0.0):
        """Record a cache miss"""
        with self.lock:
            self.metrics[f'{key}_misses'] += 1
            self.metrics['total_misses'] += 1
            self.hit_rates[key].append(0)
            if response_time > 0:
                self.response_times[key].append(response_time)
    
    def get_hit_rate(self, key: str = None) -> float:
        """Get hit rate for a specific key or overall"""
        with self.lock:
            if key:
                hits = self.metrics.get(f'{key}_hits', 0)
                misses = self.metrics.get(f'{key}_misses', 0)
            else:
                hits = self.metrics.get('total_hits', 0)
                misses = self.metrics.get('total_misses', 0)
            
            total = hits + misses
            return (hits / total * 100) if total > 0 else 0.0
    
    def get_average_response_time(self, key: str) -> float:
        """Get average response time for a key"""
        with self.lock:
            times = list(self.response_times[key])
            return sum(times) / len(times) if times else 0.0
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary"""
        with self.lock:
            summary = {
                'overall': {
                    'total_hits': self.metrics.get('total_hits', 0),
                    'total_misses': self.metrics.get('total_misses', 0),
                    'hit_rate': self.get_hit_rate(),
                    'total_requests': self.metrics.get('total_hits', 0) + self.metrics.get('total_misses', 0)
                },
                'by_key': {},
                'performance': {}
            }
            
            # Collect per-key metrics
            keys = set()
            for metric_key in self.metrics.keys():
                if metric_key.endswith('_hits') or metric_key.endswith('_misses'):
                    key = metric_key.rsplit('_', 1)[0]
                    keys.add(key)
            
            for key in keys:
                if key not in ['total']:
                    summary['by_key'][key] = {
                        'hits': self.metrics.get(f'{key}_hits', 0),
                        'misses': self.metrics.get(f'{key}_misses', 0),
                        'hit_rate': self.get_hit_rate(key),
                        'avg_response_time': self.get_average_response_time(key)
                    }
            
            return summary

# Global cache monitor instance
cache_monitor = CacheMonitor()

class CacheMetricsMiddleware:
    """
    Middleware to collect cache metrics
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # Record request start time
        start_time = time.time()
        
        # Process request
        response = self.get_response(request)
        
        # Record response time
        response_time = time.time() - start_time
        
        # Check if this was a cached response
        if hasattr(response, 'cache_hit'):
            if response.cache_hit:
                cache_monitor.record_hit(request.path, response_time)
            else:
                cache_monitor.record_miss(request.path, response_time)
        
        return response

def get_cache_info() -> Dict[str, Any]:
    """
    Get comprehensive cache information
    """
    info = {
        'backend': settings.CACHES['default']['BACKEND'],
        'location': settings.CACHES['default'].get('LOCATION', 'N/A'),
        'metrics': cache_monitor.get_metrics_summary(),
        'status': 'healthy'
    }
    
    # Test cache connectivity
    try:
        test_key = 'cache_health_check'
        test_value = 'ok'
        cache.set(test_key, test_value, 60)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            info['connectivity'] = 'ok'
            cache.delete(test_key)
        else:
            info['connectivity'] = 'error'
            info['status'] = 'unhealthy'
    
    except Exception as e:
        info['connectivity'] = f'error: {str(e)}'
        info['status'] = 'unhealthy'
    
    # Get Redis-specific info if using Redis
    if 'redis' in info['backend'].lower():
        try:
            from django_redis import get_redis_connection
            redis_conn = get_redis_connection("default")
            redis_info = redis_conn.info()
            
            info['redis'] = {
                'version': redis_info.get('redis_version'),
                'memory_used': redis_info.get('used_memory_human'),
                'memory_peak': redis_info.get('used_memory_peak_human'),
                'connected_clients': redis_info.get('connected_clients'),
                'total_commands_processed': redis_info.get('total_commands_processed'),
                'keyspace_hits': redis_info.get('keyspace_hits'),
                'keyspace_misses': redis_info.get('keyspace_misses'),
            }
            
            # Calculate Redis hit rate
            hits = redis_info.get('keyspace_hits', 0)
            misses = redis_info.get('keyspace_misses', 0)
            total = hits + misses
            info['redis']['hit_rate'] = (hits / total * 100) if total > 0 else 0.0
            
        except Exception as e:
            info['redis_error'] = str(e)
    
    return info

def clear_cache_metrics():
    """Clear all collected cache metrics"""
    global cache_monitor
    cache_monitor = CacheMonitor()

class CacheHealthCheck:
    """
    Health check for cache system
    """
    
    @staticmethod
    def check() -> Dict[str, Any]:
        """
        Perform comprehensive cache health check
        """
        result = {
            'status': 'healthy',
            'checks': {},
            'timestamp': time.time()
        }
        
        # Test basic connectivity
        try:
            cache.set('health_check', 'ok', 30)
            value = cache.get('health_check')
            cache.delete('health_check')
            
            result['checks']['connectivity'] = {
                'status': 'pass' if value == 'ok' else 'fail',
                'message': 'Cache connectivity test'
            }
        except Exception as e:
            result['checks']['connectivity'] = {
                'status': 'fail',
                'message': f'Cache connectivity failed: {str(e)}'
            }
            result['status'] = 'unhealthy'
        
        # Test performance
        try:
            start_time = time.time()
            cache.set('perf_test', 'data' * 1000, 30)
            cache.get('perf_test')
            cache.delete('perf_test')
            response_time = time.time() - start_time
            
            result['checks']['performance'] = {
                'status': 'pass' if response_time < 0.1 else 'warn',
                'message': f'Cache response time: {response_time:.3f}s',
                'response_time': response_time
            }
        except Exception as e:
            result['checks']['performance'] = {
                'status': 'fail',
                'message': f'Cache performance test failed: {str(e)}'
            }
        
        return result
