<!DOCTYPE html>
<html>
<head>
    <title>Google Maps API Test</title>
    <style>
        #map {
            height: 400px;
            width: 100%;
            border: 2px solid #ccc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Google Maps API Test</h1>
    <div id="status" class="status">Testing Google Maps API...</div>
    <div id="map"></div>

    <script>
        console.log('🧪 Starting Google Maps API test...');
        
        // Test the initMap function
        window.initMap = function() {
            console.log('✅ Google Maps API loaded successfully!');
            document.getElementById('status').innerHTML = '✅ Google Maps API loaded successfully!';
            document.getElementById('status').className = 'status success';
            
            const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 12,
                center: { lat: 13.0827, lng: 80.2707 } // Chennai coordinates
            });
            
            // Add a test marker
            new google.maps.Marker({
                position: { lat: 13.0827, lng: 80.2707 },
                map: map,
                title: 'Test Marker'
            });
            
            console.log('✅ Test map and marker created successfully!');
        };
        
        window.handleMapError = function() {
            console.error('❌ Failed to load Google Maps API');
            document.getElementById('status').innerHTML = '❌ Failed to load Google Maps API';
            document.getElementById('status').className = 'status error';
        };
    </script>

    <!-- Load Google Maps API with the same key as your Django app -->
    <script async defer 
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAvguEZqg2nL4bU0Q9jcjgNq3R0p8-seHw&callback=initMap&libraries=geometry"
        onerror="handleMapError()">
    </script>
</body>
</html>
