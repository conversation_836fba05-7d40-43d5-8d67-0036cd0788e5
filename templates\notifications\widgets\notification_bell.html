{% load notification_tags %}

<div class="notification-bell-widget">
    <div class="dropdown">
        <button class="btn btn-link notification-bell-btn" type="button" id="notificationDropdown" 
                data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-bell"></i>
            {% if unread_count > 0 %}
            <span class="notification-badge">{{ unread_count }}</span>
            {% endif %}
        </button>
        
        <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationDropdown">
            <div class="notification-header">
                <h6 class="mb-0">Notifications</h6>
                {% if unread_count > 0 %}
                <small class="text-muted">{{ unread_count }} unread</small>
                {% endif %}
            </div>
            
            <div class="notification-list">
                {% for notification in recent_notifications %}
                <div class="notification-item {% if notification.status in 'sent,delivered' %}unread{% endif %}" 
                     data-notification-id="{{ notification.id }}">
                    <div class="notification-content">
                        <div class="notification-title">{{ notification.subject|truncatechars:40 }}</div>
                        <div class="notification-message">{{ notification.message|truncatechars:60 }}</div>
                        <div class="notification-meta">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> {{ notification.created_at|timesince }} ago
                            </small>
                            <span class="priority-badge priority-{{ notification.priority }}">
                                {{ notification.priority|upper }}
                            </span>
                        </div>
                    </div>
                    {% if notification.status in 'sent,delivered' %}
                    <button class="btn btn-sm btn-outline-primary mark-read-btn" 
                            onclick="markAsRead('{{ notification.id }}')">
                        <i class="fas fa-check"></i>
                    </button>
                    {% endif %}
                </div>
                {% empty %}
                <div class="no-notifications">
                    <i class="fas fa-bell-slash text-muted"></i>
                    <p class="text-muted mb-0">No notifications</p>
                </div>
                {% endfor %}
            </div>
            
            <div class="notification-footer">
                <a href="{% url 'notifications:center' %}" class="btn btn-sm btn-primary w-100">
                    View All Notifications
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.notification-bell-widget {
    position: relative;
}

.notification-bell-btn {
    position: relative;
    color: #64748b;
    font-size: 1.2rem;
    padding: 8px;
    border: none;
    background: none;
    transition: color 0.3s ease;
}

.notification-bell-btn:hover {
    color: #667eea;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

.notification-dropdown {
    width: 350px;
    max-height: 500px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-radius: 15px;
    overflow: hidden;
}

.notification-header {
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    transition: background-color 0.3s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    border-left: 3px solid #667eea;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.notification-message {
    color: #64748b;
    font-size: 0.8rem;
    margin-bottom: 8px;
    line-height: 1.4;
}

.notification-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.priority-badge {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 600;
}

.priority-critical {
    background: #dc3545;
    color: white;
}

.priority-high {
    background: #fd7e14;
    color: white;
}

.priority-medium {
    background: #ffc107;
    color: #212529;
}

.priority-low {
    background: #28a745;
    color: white;
}

.mark-read-btn {
    padding: 4px 8px;
    font-size: 0.7rem;
}

.no-notifications {
    text-align: center;
    padding: 40px 20px;
}

.no-notifications i {
    font-size: 2rem;
    margin-bottom: 10px;
}

.notification-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* Scrollbar styling */
.notification-list::-webkit-scrollbar {
    width: 4px;
}

.notification-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.notification-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>

<script>
function markAsRead(notificationId) {
    fetch(`/notifications/mark-read/${notificationId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove unread styling
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            notificationElement.classList.remove('unread');
            
            // Hide mark as read button
            const markReadBtn = notificationElement.querySelector('.mark-read-btn');
            if (markReadBtn) {
                markReadBtn.style.display = 'none';
            }
            
            // Update badge count
            updateNotificationBadge();
        }
    })
    .catch(error => console.error('Error marking notification as read:', error));
}

function updateNotificationBadge() {
    // Get current unread count
    const unreadItems = document.querySelectorAll('.notification-item.unread').length;
    const badge = document.querySelector('.notification-badge');
    
    if (unreadItems === 0) {
        if (badge) {
            badge.remove();
        }
    } else {
        if (badge) {
            badge.textContent = unreadItems;
        }
    }
    
    // Update header count
    const headerCount = document.querySelector('.notification-header small');
    if (headerCount) {
        if (unreadItems === 0) {
            headerCount.textContent = '';
        } else {
            headerCount.textContent = `${unreadItems} unread`;
        }
    }
}

// Auto-refresh notifications every 30 seconds
setInterval(function() {
    // In a real implementation, you would fetch new notifications via AJAX
    // and update the dropdown content
}, 30000);
</script>
