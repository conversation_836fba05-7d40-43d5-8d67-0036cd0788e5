"""
API v2 URLs
Future version of the API with enhanced features
"""

from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from rest_framework.response import Response
from rest_framework.decorators import api_view
from rest_framework import status

# Import v2 viewsets (to be created)
# from .views_v2 import (
#     PondViewSetV2, WaterQualityViewSetV2, FeedViewSetV2
# )

@api_view(['GET'])
def api_v2_info(request):
    """
    API v2 information endpoint
    """
    return Response({
        'version': '2.0',
        'status': 'development',
        'message': 'API v2 is under development',
        'features': [
            'Enhanced authentication',
            'Improved error handling',
            'Better pagination',
            'GraphQL support',
            'Real-time subscriptions'
        ],
        'migration_guide': '/api/v2/docs/migration/',
        'documentation': '/api/v2/docs/'
    })

# Create router for v2 API
router = DefaultRouter()

# Register v2 viewsets (commented out until implemented)
# router.register(r'ponds', PondViewSetV2)
# router.register(r'water-quality', WaterQualityViewSetV2)
# router.register(r'feed', FeedViewSetV2)

urlpatterns = [
    # API v2 info
    path('', api_v2_info, name='api_v2_info'),
    
    # Include router URLs
    path('', include(router.urls)),
    
    # Authentication endpoints
    path('auth/', include('api.auth_urls_v2')),
    
    # Future endpoints
    path('graphql/', api_v2_info, name='graphql_endpoint'),  # Placeholder
    path('websocket/', api_v2_info, name='websocket_endpoint'),  # Placeholder
]
